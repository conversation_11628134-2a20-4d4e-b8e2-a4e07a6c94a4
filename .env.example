# 数据库配置 (本地MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sitemanager
DB_USER=root
DB_PASSWORD=root123

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# 缓存配置 (使用内存缓存，无需Redis)
# REDIS_HOST=localhost
# REDIS_PORT=6379

# 外部API配置
BAIDU_API_KEY=your-baidu-api-key

# 应用配置
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# 邮件配置（用于通知）
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# SSL检测API
SSL_LABS_API=https://api.ssllabs.com/api/v3/

# 监控配置
MONITOR_INTERVAL=300000  # 5分钟检查一次
ALERT_EMAIL=<EMAIL>
