const express = require('./backend/node_modules/express');
const mysql = require('./backend/node_modules/mysql2/promise');
const cors = require('./backend/node_modules/cors');

const app = express();
app.use(cors());
app.use(express.json());

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'root123',
  database: 'sitemanager'
};

// 创建网站API
app.post('/api/v1/websites', async (req, res) => {
  const connection = await mysql.createConnection(dbConfig);

  try {
    const {
      platformId,
      serverId,
      siteName,
      siteUrl,
      domain,
      onlineDate,
      expireDate,
      projectAmount,
      renewalFee,
      sslExpireDate,
      domainExpireDate,
      hasOnboard,
      siteId,
      status = 'active',
      notes
    } = req.body;

    console.log('接收到的数据:', req.body);

    const query = `
      INSERT INTO websites (
        platform_id, server_id, site_name, site_url, domain,
        online_date, expire_date, project_amount, renewal_fee, ssl_expire_date,
        domain_expire_date, has_onboard, site_id, status, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      platformId,
      serverId || null,
      siteName,
      siteUrl,
      domain,
      onlineDate || null,
      expireDate || null,
      projectAmount || null,
      renewalFee || null,
      sslExpireDate || null,
      domainExpireDate || null,
      hasOnboard ? 1 : 0,
      siteId || null,
      status,
      notes || null
    ];

    console.log('SQL查询:', query);
    console.log('参数值:', values);

    const [result] = await connection.execute(query, values);

    res.json({
      success: true,
      message: '网站创建成功',
      data: { id: result.insertId }
    });

  } catch (error) {
    console.error('创建网站失败:', error);
    res.status(500).json({
      success: false,
      message: '创建网站失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

// 获取网站列表API
app.get('/api/v1/websites', async (req, res) => {
  const connection = await mysql.createConnection(dbConfig);

  try {
    const [rows] = await connection.execute(`
      SELECT
        w.*,
        p.name as platform,
        s.name as server
      FROM websites w
      LEFT JOIN platforms p ON w.platform_id = p.platform_id
      LEFT JOIN servers s ON w.server_id = s.id
      ORDER BY w.created_at DESC
    `);

    res.json({
      success: true,
      message: '获取网站列表成功',
      data: {
        websites: rows,
        total: rows.length
      }
    });

  } catch (error) {
    console.error('获取网站列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取网站列表失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

// 获取行业选项API
app.get('/api/v1/settings/industries', async (req, res) => {
  res.json({
    success: true,
    message: '获取行业选项成功',
    data: [
      { id: 1, name: '电商', description: '电子商务' },
      { id: 2, name: '企业官网', description: '企业展示网站' },
      { id: 3, name: '教育', description: '教育培训' },
      { id: 4, name: '医疗', description: '医疗健康' },
      { id: 5, name: '金融', description: '金融服务' }
    ]
  });
});

// 获取平台选项API
app.get('/api/v1/settings/platforms', async (req, res) => {
  const connection = await mysql.createConnection(dbConfig);

  try {
    const [rows] = await connection.execute(`
      SELECT id, name, description, is_active
      FROM platforms
      WHERE is_active = 1
      ORDER BY name
    `);

    res.json({
      success: true,
      message: '获取平台选项成功',
      data: rows
    });

  } catch (error) {
    console.error('获取平台选项失败:', error);
    res.status(500).json({
      success: false,
      message: '获取平台选项失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

// 获取服务器选项API
app.get('/api/v1/settings/servers', async (req, res) => {
  const connection = await mysql.createConnection(dbConfig);

  try {
    const [rows] = await connection.execute(`
      SELECT id, name, ip_address, location
      FROM servers
      ORDER BY name
    `);

    res.json({
      success: true,
      message: '获取服务器选项成功',
      data: rows
    });

  } catch (error) {
    console.error('获取服务器选项失败:', error);
    res.status(500).json({
      success: false,
      message: '获取服务器选项失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

// 获取行业选项API
app.get('/api/v1/settings/industries', async (req, res) => {
  res.json({
    success: true,
    message: '获取行业选项成功',
    data: [
      { id: 1, name: '电商', description: '电子商务' },
      { id: 2, name: '企业官网', description: '企业展示网站' },
      { id: 3, name: '教育', description: '教育培训' },
      { id: 4, name: '医疗', description: '医疗健康' },
      { id: 5, name: '金融', description: '金融服务' }
    ]
  });
});

// 获取平台选项API
app.get('/api/v1/settings/platforms', async (req, res) => {
  const connection = await mysql.createConnection(dbConfig);

  try {
    const [rows] = await connection.execute(`
      SELECT id, name, description, is_active
      FROM platforms
      WHERE is_active = 1
      ORDER BY name
    `);

    res.json({
      success: true,
      message: '获取平台选项成功',
      data: rows
    });

  } catch (error) {
    console.error('获取平台选项失败:', error);
    res.status(500).json({
      success: false,
      message: '获取平台选项失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

// 获取服务器选项API
app.get('/api/v1/settings/servers', async (req, res) => {
  const connection = await mysql.createConnection(dbConfig);

  try {
    const [rows] = await connection.execute(`
      SELECT id, name, ip_address, location
      FROM servers
      ORDER BY name
    `);

    res.json({
      success: true,
      message: '获取服务器选项成功',
      data: rows
    });

  } catch (error) {
    console.error('获取服务器选项失败:', error);
    res.status(500).json({
      success: false,
      message: '获取服务器选项失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

const PORT = 3001;
app.listen(PORT, () => {
  console.log(`简单服务器运行在端口 ${PORT}`);
});
