#!/bin/bash

# 网站管理系统全栈启动脚本
# 功能：启动前端和后端服务

set -e

# 设置工作目录
cd /opt/sitemanager

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "错误：Node.js 未安装"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "错误：npm 未安装"
    exit 1
fi

echo "正在启动网站管理系统..."

# 启动后端服务
echo "启动后端服务..."
cd /opt/sitemanager/backend
node app.js &
BACKEND_PID=$!

# 等待后端服务启动
sleep 3

# 检查后端服务是否正常启动
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    echo "错误：后端服务启动失败"
    exit 1
fi

echo "后端服务已启动 (PID: $BACKEND_PID)"

# 启动前端服务
echo "启动前端服务..."
cd /opt/sitemanager/frontend
npm run dev &
FRONTEND_PID=$!

# 等待前端服务启动
sleep 5

# 检查前端服务是否正常启动
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    echo "错误：前端服务启动失败"
    # 清理后端进程
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

echo "前端服务已启动 (PID: $FRONTEND_PID)"

# 创建PID文件
echo $BACKEND_PID > /var/run/sitemanager-backend.pid
echo $FRONTEND_PID > /var/run/sitemanager-frontend.pid

echo "网站管理系统启动完成"
echo "前端地址: http://localhost:3000"
echo "后端地址: http://localhost:3001"

# 信号处理函数
cleanup() {
    echo "🛑 接收到停止信号，正在清理进程..."

    # 停止后端进程
    if [ -n "$BACKEND_PID" ] && kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "✅ 后端进程已停止"
    fi

    # 停止前端进程
    if [ -n "$FRONTEND_PID" ] && kill -0 $FRONTEND_PID 2>/dev/null; then
        kill $FRONTEND_PID
        echo "✅ 前端进程已停止"
    fi

    # 清理PID文件
    rm -f /var/run/sitemanager-backend.pid
    rm -f /var/run/sitemanager-frontend.pid

    echo "🎉 清理完成"
    exit 0
}

# 注册信号处理
trap cleanup SIGTERM SIGINT

# 等待子进程，但允许信号中断
wait