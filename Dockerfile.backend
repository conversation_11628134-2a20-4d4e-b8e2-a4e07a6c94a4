# SiteManager 后端 Dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    curl \
    bash \
    openssh-client \
    mysql-client \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S sitemanager && \
    adduser -S sitemanager -u 1001 -G sitemanager

# 复制package.json文件
COPY backend/package*.json ./backend/
COPY package*.json ./

# 安装依赖
RUN cd backend && npm ci --only=production && npm cache clean --force

# 复制应用代码
COPY backend/ ./backend/
COPY ssl-checker/ ./ssl-checker/
COPY database/ ./database/

# 创建必要的目录
RUN mkdir -p ./backend/logs ./backend/uploads ./backend/database && \
    chown -R sitemanager:sitemanager /app

# 设置权限
RUN chmod +x ./backend/simple-server.js

# 切换到非root用户
USER sitemanager

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# 启动命令
CMD ["node", "backend/simple-server.js"]
