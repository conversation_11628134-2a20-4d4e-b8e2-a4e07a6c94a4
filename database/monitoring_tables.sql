-- 站点监控相关数据库表结构

-- 1. 检查websites表监控字段（字段已存在，跳过添加）
-- ALTER TABLE websites ADD COLUMN consecutive_failures INT DEFAULT 0 COMMENT '连续失败次数';
-- ALTER TABLE websites ADD COLUMN last_failure_time DATETIME NULL COMMENT '最后失败时间';
-- ALTER TABLE websites ADD COLUMN notification_sent BOOLEAN DEFAULT FALSE COMMENT '是否已发送通知';
-- ALTER TABLE websites ADD COLUMN last_notification_time DATETIME NULL COMMENT '最后通知时间';

-- 2. 创建站点检测历史表
CREATE TABLE IF NOT EXISTS site_check_history (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  website_id INT NOT NULL,
  check_time DATETIME NOT NULL,
  status_code INT DEFAULT 0,
  response_time INT DEFAULT 0,
  success BOOLEAN DEFAULT FALSE,
  error_message TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_website_id (website_id),
  INDEX idx_check_time (check_time),
  INDEX idx_success (success),
  
  FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='站点检测历史记录';

-- 3. 创建监控配置表
CREATE TABLE IF NOT EXISTS monitoring_config (
  id INT AUTO_INCREMENT PRIMARY KEY,
  config_key VARCHAR(100) NOT NULL UNIQUE,
  config_value TEXT NOT NULL,
  description TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控系统配置';

-- 4. 创建通知配置表
CREATE TABLE IF NOT EXISTS notification_config (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  type ENUM('email', 'sms', 'wechat', 'dingtalk', 'webhook') NOT NULL,
  enabled BOOLEAN DEFAULT TRUE,
  config JSON NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_type (type),
  INDEX idx_enabled (enabled)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知配置';

-- 5. 创建通知历史表
CREATE TABLE IF NOT EXISTS notification_history (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  website_id INT NOT NULL,
  notification_type ENUM('site_down', 'site_up') NOT NULL,
  channels JSON NOT NULL COMMENT '发送渠道列表',
  message JSON NOT NULL COMMENT '通知消息内容',
  success_count INT DEFAULT 0 COMMENT '成功发送数量',
  failure_count INT DEFAULT 0 COMMENT '失败发送数量',
  sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_website_id (website_id),
  INDEX idx_notification_type (notification_type),
  INDEX idx_sent_at (sent_at),
  
  FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知发送历史';

-- 6. 创建监控统计表
CREATE TABLE IF NOT EXISTS monitoring_stats (
  id INT AUTO_INCREMENT PRIMARY KEY,
  stat_date DATE NOT NULL,
  total_sites INT DEFAULT 0,
  total_checks INT DEFAULT 0,
  successful_checks INT DEFAULT 0,
  failed_checks INT DEFAULT 0,
  average_response_time DECIMAL(10,2) DEFAULT 0,
  notifications_sent INT DEFAULT 0,
  uptime_percentage DECIMAL(5,2) DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY uk_stat_date (stat_date),
  INDEX idx_stat_date (stat_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控统计数据';

-- 7. 插入默认监控配置
INSERT IGNORE INTO monitoring_config (config_key, config_value, description) VALUES
('check_interval', '300000', '检测间隔(毫秒)，默认5分钟'),
('batch_size', '50', '每批检测站点数'),
('concurrency', '10', '并发检测数'),
('timeout', '15000', '请求超时时间(毫秒)'),
('failure_threshold', '3', '连续失败次数阈值'),
('recovery_threshold', '2', '连续成功次数阈值'),
('retry_attempts', '2', '失败重试次数'),
('retry_delay', '3000', '重试延迟(毫秒)');

-- 8. 插入默认通知配置示例
INSERT IGNORE INTO notification_config (name, type, enabled, config) VALUES
('默认邮件通知', 'email', FALSE, JSON_OBJECT(
  'host', 'smtp.qq.com',
  'port', 587,
  'secure', false,
  'user', '',
  'pass', '',
  'from', '',
  'to', JSON_ARRAY()
)),
('企业微信通知', 'wechat', FALSE, JSON_OBJECT(
  'webhookUrl', '',
  'mentionedList', JSON_ARRAY()
)),
('钉钉通知', 'dingtalk', FALSE, JSON_OBJECT(
  'webhookUrl', '',
  'secret', '',
  'atMobiles', JSON_ARRAY(),
  'isAtAll', false
)),
('Webhook通知', 'webhook', FALSE, JSON_OBJECT(
  'urls', JSON_ARRAY(),
  'headers', JSON_OBJECT()
));

-- 9. 创建视图：站点监控概览
CREATE OR REPLACE VIEW v_site_monitoring_overview AS
SELECT 
  w.id,
  w.site_name,
  w.domain,
  w.site_url,
  w.access_status,
  w.access_status_code,
  w.response_time,
  w.last_check_time,
  w.consecutive_failures,
  w.notification_sent,
  w.last_notification_time,
  CASE 
    WHEN w.access_status = 'online' THEN '正常'
    WHEN w.consecutive_failures >= 3 THEN '故障'
    WHEN w.consecutive_failures > 0 THEN '异常'
    ELSE '未知'
  END as status_text,
  CASE 
    WHEN w.last_check_time IS NULL THEN '从未检测'
    WHEN w.last_check_time < DATE_SUB(NOW(), INTERVAL 10 MINUTE) THEN '检测延迟'
    ELSE '正常'
  END as check_status
FROM websites w
WHERE w.status = 'active'
ORDER BY w.consecutive_failures DESC, w.last_check_time ASC;

-- 10. 创建视图：今日监控统计
CREATE OR REPLACE VIEW v_today_monitoring_stats AS
SELECT 
  COUNT(*) as total_sites,
  SUM(CASE WHEN h.success = 1 THEN 1 ELSE 0 END) as successful_checks,
  SUM(CASE WHEN h.success = 0 THEN 1 ELSE 0 END) as failed_checks,
  COUNT(h.id) as total_checks,
  ROUND(AVG(CASE WHEN h.success = 1 THEN h.response_time END), 2) as avg_response_time,
  ROUND(
    SUM(CASE WHEN h.success = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(h.id), 
    2
  ) as success_rate
FROM site_check_history h
WHERE DATE(h.check_time) = CURDATE();

-- 11. 创建存储过程：清理历史数据
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanupMonitoringData(IN days_to_keep INT)
BEGIN
  DECLARE done INT DEFAULT FALSE;
  DECLARE cleanup_date DATETIME;
  
  SET cleanup_date = DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
  
  -- 清理检测历史数据
  DELETE FROM site_check_history 
  WHERE check_time < cleanup_date;
  
  -- 清理通知历史数据
  DELETE FROM notification_history 
  WHERE sent_at < cleanup_date;
  
  -- 清理统计数据（保留更长时间）
  DELETE FROM monitoring_stats 
  WHERE stat_date < DATE_SUB(CURDATE(), INTERVAL (days_to_keep * 2) DAY);
  
  SELECT 
    ROW_COUNT() as deleted_rows,
    cleanup_date as cleanup_before_date;
END //
DELIMITER ;

-- 12. 创建事件：自动清理数据（每天凌晨2点执行）
SET GLOBAL event_scheduler = ON;

CREATE EVENT IF NOT EXISTS evt_cleanup_monitoring_data
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '02:00:00')
DO
  CALL CleanupMonitoringData(30); -- 保留30天数据

-- 13. 创建触发器：自动更新统计数据
DELIMITER //
CREATE TRIGGER IF NOT EXISTS tr_update_monitoring_stats
AFTER INSERT ON site_check_history
FOR EACH ROW
BEGIN
  INSERT INTO monitoring_stats (
    stat_date, 
    total_checks, 
    successful_checks, 
    failed_checks,
    average_response_time
  )
  VALUES (
    DATE(NEW.check_time),
    1,
    CASE WHEN NEW.success = 1 THEN 1 ELSE 0 END,
    CASE WHEN NEW.success = 0 THEN 1 ELSE 0 END,
    CASE WHEN NEW.success = 1 THEN NEW.response_time ELSE 0 END
  )
  ON DUPLICATE KEY UPDATE
    total_checks = total_checks + 1,
    successful_checks = successful_checks + CASE WHEN NEW.success = 1 THEN 1 ELSE 0 END,
    failed_checks = failed_checks + CASE WHEN NEW.success = 0 THEN 1 ELSE 0 END,
    average_response_time = CASE 
      WHEN NEW.success = 1 THEN 
        (average_response_time * (successful_checks - CASE WHEN NEW.success = 1 THEN 1 ELSE 0 END) + NEW.response_time) / successful_checks
      ELSE average_response_time
    END,
    updated_at = CURRENT_TIMESTAMP;
END //
DELIMITER ;

-- 14. 创建索引优化查询性能
CREATE INDEX idx_websites_monitoring ON websites(status, last_check_time, consecutive_failures);
CREATE INDEX idx_check_history_daily ON site_check_history(website_id, check_time, success);

-- 15. 显示创建结果
SELECT 'Monitoring tables and procedures created successfully!' as result;
