-- WordPress站点管理系统数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE COMMENT '用户名',
  `email` varchar(100) NOT NULL UNIQUE COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码哈希',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `role` enum('super_admin','admin','user') NOT NULL DEFAULT 'user' COMMENT '角色',
  `status` enum('active','inactive','suspended') NOT NULL DEFAULT 'active' COMMENT '状态',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `department` varchar(50) DEFAULT NULL COMMENT '部门',
  `last_login` datetime DEFAULT NULL COMMENT '最后登录时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 客户表
CREATE TABLE `customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '客户名称',
  `company` varchar(100) DEFAULT NULL COMMENT '公司名称',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `phone` varchar(20) DEFAULT NULL COMMENT '电话',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `address` text DEFAULT NULL COMMENT '地址',
  `industry` varchar(50) DEFAULT NULL COMMENT '行业',
  `source` varchar(50) DEFAULT NULL COMMENT '客户来源',
  `status` enum('potential','confirmed','lost') NOT NULL DEFAULT 'potential' COMMENT '客户状态',
  `sales_person_id` int(11) DEFAULT NULL COMMENT '销售负责人ID',
  `notes` text DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_sales_person` (`sales_person_id`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`sales_person_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='客户表';

-- 项目表（建站管理）
CREATE TABLE `projects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL COMMENT '客户ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `project_type` varchar(50) NOT NULL COMMENT '项目类型',
  `service_fee` decimal(10,2) DEFAULT NULL COMMENT '网站服务费用',
  `project_manager_id` int(11) DEFAULT NULL COMMENT '项目负责人ID',
  `sales_person_id` int(11) DEFAULT NULL COMMENT '销售ID',
  `contract_number` varchar(50) DEFAULT NULL COMMENT '合同编号',
  `contract_signed_date` date DEFAULT NULL COMMENT '签订时间',
  `online_status` enum('planning','development','testing','online','suspended') NOT NULL DEFAULT 'planning' COMMENT '上线状态',
  `online_date` date DEFAULT NULL COMMENT '上线时间',
  `planned_online_date` date DEFAULT NULL COMMENT '计划上线时间',
  `info_collection_form` varchar(255) DEFAULT NULL COMMENT '信息采集表URL',
  `preview_link` varchar(255) DEFAULT NULL COMMENT '预览链接',
  `progress_sheet` varchar(255) DEFAULT NULL COMMENT '进度表URL',
  `notes` text DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_customer` (`customer_id`),
  KEY `idx_project_manager` (`project_manager_id`),
  KEY `idx_sales_person` (`sales_person_id`),
  KEY `idx_online_status` (`online_status`),
  FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`project_manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`sales_person_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目表';

-- 平台类型表
CREATE TABLE `platforms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '平台名称',
  `description` text DEFAULT NULL COMMENT '平台描述',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台类型表';

-- 服务器表
CREATE TABLE `servers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '服务器名称',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `location` varchar(100) DEFAULT NULL COMMENT '服务器位置',
  `provider` varchar(50) DEFAULT NULL COMMENT '服务商',
  `cpu_cores` int(11) DEFAULT NULL COMMENT 'CPU核数',
  `memory_gb` int(11) DEFAULT NULL COMMENT '内存GB',
  `storage_gb` int(11) DEFAULT NULL COMMENT '存储GB',
  `bandwidth_mbps` int(11) DEFAULT NULL COMMENT '带宽Mbps',
  `os` varchar(50) DEFAULT NULL COMMENT '操作系统',
  `expire_date` date DEFAULT NULL COMMENT '到期日期',
  `renewal_fee` decimal(10,2) DEFAULT NULL COMMENT '续费金额',
  `status` enum('active','inactive','maintenance') NOT NULL DEFAULT 'active' COMMENT '状态',
  `ssh_port` int(11) DEFAULT 22 COMMENT 'SSH端口',
  `ssh_username` varchar(50) DEFAULT NULL COMMENT 'SSH用户名',
  `notes` text DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_ip` (`ip_address`),
  KEY `idx_expire_date` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务器表';

-- 网站表
CREATE TABLE `websites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `project_id` int(11) DEFAULT NULL COMMENT '关联项目ID',
  `platform_id` int(11) NOT NULL COMMENT '平台ID',
  `server_id` int(11) DEFAULT NULL COMMENT '服务器ID',
  `site_url` varchar(255) NOT NULL COMMENT '站点URL',
  `domain` varchar(100) NOT NULL COMMENT '域名',
  `online_date` date DEFAULT NULL COMMENT '上线日期',
  `expire_date` date DEFAULT NULL COMMENT '到期日期',
  `project_amount` decimal(10,2) DEFAULT NULL COMMENT '项目金额',
  `renewal_fee` decimal(10,2) DEFAULT NULL COMMENT '续费金额',
  `access_status_code` int(11) DEFAULT NULL COMMENT '访问状态码',
  `ssl_expire_date` date DEFAULT NULL COMMENT 'SSL到期时间',
  `domain_expire_date` date DEFAULT NULL COMMENT '域名到期时间',
  `last_check_time` datetime DEFAULT NULL COMMENT '最后检查时间',
  `status` enum('active','inactive','suspended','expired') NOT NULL DEFAULT 'active' COMMENT '状态',
  `notes` text DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_project` (`project_id`),
  KEY `idx_platform` (`platform_id`),
  KEY `idx_server` (`server_id`),
  KEY `idx_domain` (`domain`),
  KEY `idx_expire_date` (`expire_date`),
  FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE SET NULL,
  FOREIGN KEY (`platform_id`) REFERENCES `platforms` (`id`) ON DELETE RESTRICT,
  FOREIGN KEY (`server_id`) REFERENCES `servers` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站表';

-- 网站账号表
CREATE TABLE `website_accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) NOT NULL COMMENT '网站ID',
  `account_type` varchar(50) NOT NULL COMMENT '账户类型',
  `username` varchar(100) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码（加密）',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `role` varchar(50) DEFAULT NULL COMMENT '角色',
  `login_url` varchar(255) DEFAULT NULL COMMENT '登录URL',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `notes` text DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_website` (`website_id`),
  KEY `idx_account_type` (`account_type`),
  FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站账号表';

-- 网站附件表
CREATE TABLE `website_attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) NOT NULL COMMENT '网站ID',
  `file_name` varchar(255) NOT NULL COMMENT '文件名',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(100) NOT NULL COMMENT '文件类型',
  `mime_type` varchar(100) NOT NULL COMMENT 'MIME类型',
  `category` enum('image','pdf','excel','word','other') NOT NULL DEFAULT 'other' COMMENT '文件分类',
  `description` text DEFAULT NULL COMMENT '文件描述',
  `uploaded_by` int(11) DEFAULT NULL COMMENT '上传者ID',
  `is_preview_available` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否支持预览',
  `thumbnail_path` varchar(500) DEFAULT NULL COMMENT '缩略图路径',
  `download_count` int(11) NOT NULL DEFAULT 0 COMMENT '下载次数',
  `last_accessed` datetime DEFAULT NULL COMMENT '最后访问时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_website` (`website_id`),
  KEY `idx_category` (`category`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`uploaded_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站附件表';

-- 域名表
CREATE TABLE `domains` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `domain_name` varchar(100) NOT NULL COMMENT '域名',
  `registrar` varchar(50) DEFAULT NULL COMMENT '注册商',
  `register_date` date DEFAULT NULL COMMENT '注册日期',
  `expire_date` date DEFAULT NULL COMMENT '到期日期',
  `renewal_fee` decimal(10,2) DEFAULT NULL COMMENT '续费金额',
  `auto_renewal` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自动续费',
  `dns_provider` varchar(50) DEFAULT NULL COMMENT 'DNS服务商',
  `status` enum('active','expired','suspended','transferred') NOT NULL DEFAULT 'active' COMMENT '状态',
  `whois_info` json DEFAULT NULL COMMENT 'WHOIS信息',
  `last_check_time` datetime DEFAULT NULL COMMENT '最后检查时间',
  `notes` text DEFAULT NULL COMMENT '备注',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_domain` (`domain_name`),
  KEY `idx_expire_date` (`expire_date`),
  KEY `idx_registrar` (`registrar`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='域名表';

-- 用户权限表
CREATE TABLE `user_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `resource_type` enum('website','server','domain','project','customer') NOT NULL COMMENT '资源类型',
  `resource_id` int(11) NOT NULL COMMENT '资源ID',
  `permission` enum('read','write','admin') NOT NULL DEFAULT 'read' COMMENT '权限级别',
  `granted_by` int(11) DEFAULT NULL COMMENT '授权人ID',
  `granted_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_resource` (`user_id`, `resource_type`, `resource_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_resource` (`resource_type`, `resource_id`),
  KEY `idx_granted_by` (`granted_by`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`granted_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户权限表';

-- 监控日志表
CREATE TABLE `monitor_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `resource_type` enum('website','server','domain','ssl') NOT NULL COMMENT '监控资源类型',
  `resource_id` int(11) NOT NULL COMMENT '资源ID',
  `check_type` varchar(50) NOT NULL COMMENT '检查类型',
  `status` enum('success','warning','error') NOT NULL COMMENT '检查状态',
  `response_time` int(11) DEFAULT NULL COMMENT '响应时间（毫秒）',
  `status_code` int(11) DEFAULT NULL COMMENT 'HTTP状态码',
  `error_message` text DEFAULT NULL COMMENT '错误信息',
  `check_data` json DEFAULT NULL COMMENT '检查数据',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_resource` (`resource_type`, `resource_id`),
  KEY `idx_check_type` (`check_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控日志表';

-- 通知表
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '用户ID，NULL表示系统通知',
  `type` enum('info','warning','error','success') NOT NULL DEFAULT 'info' COMMENT '通知类型',
  `title` varchar(200) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读',
  `resource_type` varchar(50) DEFAULT NULL COMMENT '关联资源类型',
  `resource_id` int(11) DEFAULT NULL COMMENT '关联资源ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_resource` (`resource_type`, `resource_id`),
  FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知表';

-- 插入初始数据

-- 插入默认平台
INSERT INTO `platforms` (`name`, `description`) VALUES
('WordPress', 'WordPress内容管理系统'),
('Shopify', 'Shopify电商平台'),
('WooCommerce', 'WooCommerce电商插件'),
('Magento', 'Magento电商平台'),
('Drupal', 'Drupal内容管理系统'),
('Joomla', 'Joomla内容管理系统'),
('Custom', '自定义开发平台'),
('Static', '静态网站'),
('Laravel', 'Laravel PHP框架'),
('React', 'React前端应用');

-- 插入默认超级管理员用户
INSERT INTO `users` (`username`, `email`, `password`, `real_name`, `role`, `status`) VALUES
('admin', '<EMAIL>', '$2a$10$rnKrTghDoQGde/b5Kc1HVO00vt8vXMxUaKvlXrQMqGKzKXVafJpra', '系统管理员', 'super_admin', 'active');

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 1;
