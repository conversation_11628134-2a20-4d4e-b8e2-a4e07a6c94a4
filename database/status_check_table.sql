-- 创建状态检测表
CREATE TABLE IF NOT EXISTS status_check (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  platform_id INT NOT NULL COMMENT '平台ID，关联websites表',
  status_code INT DEFAULT 0 COMMENT 'HTTP状态码',
  error_count INT DEFAULT 0 COMMENT '错误次数',
  last_check_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '最后检测时间',
  last_success_time TIMESTAMP NULL COMMENT '最后成功时间',
  response_time INT DEFAULT 0 COMMENT '响应时间(毫秒)',
  error_message TEXT NULL COMMENT '错误信息',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  UNIQUE KEY uk_platform_id (platform_id),
  INDEX idx_error_count (error_count),
  INDEX idx_last_check_time (last_check_time),
  INDEX idx_status_code (status_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='站点状态检测表';

-- 初始化现有站点的状态检测记录
INSERT IGNORE INTO status_check (platform_id, status_code, error_count, last_check_time)
SELECT 
  id as platform_id,
  COALESCE(access_status_code, 0) as status_code,
  COALESCE(consecutive_failures, 0) as error_count,
  COALESCE(last_check_time, NOW()) as last_check_time
FROM websites 
WHERE status = 'active';

-- 创建视图：故障站点详情
CREATE OR REPLACE VIEW v_failed_sites AS
SELECT 
  sc.platform_id,
  w.site_name,
  w.domain,
  w.site_url,
  sc.status_code,
  sc.error_count,
  sc.last_check_time,
  sc.last_success_time,
  sc.error_message,
  s.name as server_name,
  s.location as server_location,
  TIMESTAMPDIFF(MINUTE, sc.last_success_time, NOW()) as failure_duration_minutes,
  CASE 
    WHEN sc.last_success_time IS NULL THEN '未知'
    WHEN TIMESTAMPDIFF(HOUR, sc.last_success_time, NOW()) >= 24 THEN 
      CONCAT(TIMESTAMPDIFF(DAY, sc.last_success_time, NOW()), '天')
    WHEN TIMESTAMPDIFF(MINUTE, sc.last_success_time, NOW()) >= 60 THEN 
      CONCAT(TIMESTAMPDIFF(HOUR, sc.last_success_time, NOW()), '小时')
    ELSE 
      CONCAT(TIMESTAMPDIFF(MINUTE, sc.last_success_time, NOW()), '分钟')
  END as failure_duration_text
FROM status_check sc
JOIN websites w ON sc.platform_id = w.id
LEFT JOIN servers s ON w.server_id = s.id
WHERE sc.error_count >= 3
ORDER BY sc.error_count DESC, sc.last_check_time ASC;

-- 创建存储过程：获取需要通知的故障站点
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS GetFailedSitesForNotification()
BEGIN
  SELECT 
    platform_id,
    site_name,
    domain,
    site_url,
    status_code,
    error_count,
    server_name,
    server_location,
    failure_duration_text,
    last_check_time,
    last_success_time
  FROM v_failed_sites
  WHERE error_count >= 3;
END //
DELIMITER ;

-- 创建存储过程：重置站点错误计数
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS ResetSiteErrorCount(IN site_platform_id INT)
BEGIN
  UPDATE status_check 
  SET 
    error_count = 0,
    last_success_time = NOW(),
    updated_at = NOW()
  WHERE platform_id = site_platform_id;
END //
DELIMITER ;

-- 创建存储过程：增加站点错误计数
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS IncrementSiteErrorCount(
  IN site_platform_id INT,
  IN site_status_code INT,
  IN site_response_time INT,
  IN site_error_message TEXT
)
BEGIN
  INSERT INTO status_check (
    platform_id, 
    status_code, 
    error_count, 
    response_time, 
    error_message,
    last_check_time
  )
  VALUES (
    site_platform_id, 
    site_status_code, 
    1, 
    site_response_time, 
    site_error_message,
    NOW()
  )
  ON DUPLICATE KEY UPDATE
    status_code = site_status_code,
    error_count = error_count + 1,
    response_time = site_response_time,
    error_message = site_error_message,
    last_check_time = NOW(),
    updated_at = NOW();
END //
DELIMITER ;

-- 创建存储过程：更新站点成功状态
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS UpdateSiteSuccessStatus(
  IN site_platform_id INT,
  IN site_status_code INT,
  IN site_response_time INT
)
BEGIN
  INSERT INTO status_check (
    platform_id, 
    status_code, 
    error_count, 
    response_time, 
    last_check_time,
    last_success_time
  )
  VALUES (
    site_platform_id, 
    site_status_code, 
    0, 
    site_response_time, 
    NOW(),
    NOW()
  )
  ON DUPLICATE KEY UPDATE
    status_code = site_status_code,
    error_count = 0,
    response_time = site_response_time,
    last_check_time = NOW(),
    last_success_time = NOW(),
    error_message = NULL,
    updated_at = NOW();
END //
DELIMITER ;

SELECT 'Status check table and procedures created successfully!' as result;
