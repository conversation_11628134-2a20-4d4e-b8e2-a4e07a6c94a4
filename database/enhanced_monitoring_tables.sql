-- 增强版监控系统数据库表结构
-- 基于coolmonitor的设计，适配我们的网站管理系统

-- 网站监控配置表
CREATE TABLE IF NOT EXISTS `website_monitor_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) NOT NULL,
  `http_method` varchar(10) DEFAULT 'GET' COMMENT 'HTTP请求方法',
  `status_codes` varchar(100) DEFAULT '200-299' COMMENT '期望的状态码范围',
  `connect_timeout` int(11) DEFAULT 10 COMMENT '连接超时时间(秒)',
  `retries` int(11) DEFAULT 1 COMMENT '重试次数',
  `retry_interval` int(11) DEFAULT 30 COMMENT '重试间隔(秒)',
  `enable_http_check` tinyint(1) DEFAULT 1 COMMENT '启用HTTP检查',
  `enable_ssl_check` tinyint(1) DEFAULT 1 COMMENT '启用SSL检查',
  `enable_keyword_check` tinyint(1) DEFAULT 0 COMMENT '启用关键词检查',
  `notify_cert_expiry` tinyint(1) DEFAULT 1 COMMENT '启用证书到期通知',
  `ignore_tls` tinyint(1) DEFAULT 0 COMMENT '忽略TLS证书错误',
  `keyword` text COMMENT '关键词(多个用逗号分隔)',
  `request_headers` text COMMENT '自定义请求头(JSON格式)',
  `request_body` text COMMENT '请求体内容',
  `max_redirects` int(11) DEFAULT 10 COMMENT '最大重定向次数',
  `check_interval` int(11) DEFAULT 300 COMMENT '检查间隔(秒)',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_website_id` (`website_id`),
  KEY `idx_website_id` (`website_id`),
  CONSTRAINT `fk_monitor_config_website` FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站监控配置表';

-- 网站监控历史记录表
CREATE TABLE IF NOT EXISTS `website_monitor_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL COMMENT '监控状态: 0=异常, 1=正常, 2=等待',
  `message` text COMMENT '检查结果消息',
  `response_time` int(11) DEFAULT 0 COMMENT '响应时间(毫秒)',
  `status_code` int(11) DEFAULT NULL COMMENT 'HTTP状态码',
  `check_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '检查时间',
  `details` json DEFAULT NULL COMMENT '详细检查结果(JSON格式)',
  `error_message` text COMMENT '错误信息',
  PRIMARY KEY (`id`),
  KEY `idx_website_id` (`website_id`),
  KEY `idx_check_time` (`check_time`),
  KEY `idx_status` (`status`),
  KEY `idx_website_status_time` (`website_id`, `status`, `check_time`),
  CONSTRAINT `fk_monitor_history_website` FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站监控历史记录表';

-- 监控通知配置表
CREATE TABLE IF NOT EXISTS `monitor_notification_configs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '通知配置名称',
  `type` varchar(50) NOT NULL COMMENT '通知类型: email, webhook, wechat, dingtalk, feishu',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `config` json NOT NULL COMMENT '通知配置(JSON格式)',
  `default_for_new_monitors` tinyint(1) DEFAULT 0 COMMENT '新监控项默认启用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控通知配置表';

-- 网站监控通知绑定表
CREATE TABLE IF NOT EXISTS `website_monitor_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) NOT NULL,
  `notification_config_id` int(11) NOT NULL,
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用此通知',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_website_notification` (`website_id`, `notification_config_id`),
  KEY `idx_website_id` (`website_id`),
  KEY `idx_notification_config_id` (`notification_config_id`),
  CONSTRAINT `fk_website_notification_website` FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_website_notification_config` FOREIGN KEY (`notification_config_id`) REFERENCES `monitor_notification_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站监控通知绑定表';

-- 监控通知历史记录表
CREATE TABLE IF NOT EXISTS `monitor_notification_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `website_id` int(11) NOT NULL,
  `notification_config_id` int(11) NOT NULL,
  `notification_type` varchar(50) NOT NULL COMMENT '通知类型',
  `status` varchar(20) NOT NULL COMMENT '发送状态: success, failed, pending',
  `message` text COMMENT '通知内容',
  `response` text COMMENT '通知响应',
  `error_message` text COMMENT '错误信息',
  `sent_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
  PRIMARY KEY (`id`),
  KEY `idx_website_id` (`website_id`),
  KEY `idx_notification_config_id` (`notification_config_id`),
  KEY `idx_sent_at` (`sent_at`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_notification_history_website` FOREIGN KEY (`website_id`) REFERENCES `websites` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_notification_history_config` FOREIGN KEY (`notification_config_id`) REFERENCES `monitor_notification_configs` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='监控通知历史记录表';

-- 为websites表添加增强监控相关字段（使用存储过程来避免重复添加）
DELIMITER $$

CREATE PROCEDURE AddColumnIfNotExists()
BEGIN
  -- 检查并添加ssl_status字段
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'websites' AND COLUMN_NAME = 'ssl_status') THEN
    ALTER TABLE `websites` ADD COLUMN `ssl_status` varchar(20) DEFAULT NULL COMMENT 'SSL证书状态: valid, invalid, expired, unknown';
  END IF;

  -- 检查并添加ssl_expire_date字段
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'websites' AND COLUMN_NAME = 'ssl_expire_date') THEN
    ALTER TABLE `websites` ADD COLUMN `ssl_expire_date` date DEFAULT NULL COMMENT 'SSL证书到期日期';
  END IF;

  -- 检查并添加ssl_days_remaining字段
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'websites' AND COLUMN_NAME = 'ssl_days_remaining') THEN
    ALTER TABLE `websites` ADD COLUMN `ssl_days_remaining` int(11) DEFAULT NULL COMMENT 'SSL证书剩余天数';
  END IF;

  -- 检查并添加ssl_issuer字段
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'websites' AND COLUMN_NAME = 'ssl_issuer') THEN
    ALTER TABLE `websites` ADD COLUMN `ssl_issuer` varchar(255) DEFAULT NULL COMMENT 'SSL证书颁发者';
  END IF;

  -- 检查并添加last_ssl_check字段
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'websites' AND COLUMN_NAME = 'last_ssl_check') THEN
    ALTER TABLE `websites` ADD COLUMN `last_ssl_check` timestamp NULL DEFAULT NULL COMMENT '最后SSL检查时间';
  END IF;

  -- 检查并添加monitor_enabled字段
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'websites' AND COLUMN_NAME = 'monitor_enabled') THEN
    ALTER TABLE `websites` ADD COLUMN `monitor_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用监控';
  END IF;

  -- 检查并添加monitor_interval字段
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'websites' AND COLUMN_NAME = 'monitor_interval') THEN
    ALTER TABLE `websites` ADD COLUMN `monitor_interval` int(11) DEFAULT 300 COMMENT '监控间隔(秒)';
  END IF;
END$$

DELIMITER ;

-- 执行存储过程
CALL AddColumnIfNotExists();

-- 删除存储过程
DROP PROCEDURE AddColumnIfNotExists;

-- 添加索引优化查询性能（使用存储过程）
DELIMITER $$

CREATE PROCEDURE AddIndexIfNotExists()
BEGIN
  -- 检查并添加ssl_status索引
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'websites' AND INDEX_NAME = 'idx_ssl_status') THEN
    ALTER TABLE `websites` ADD INDEX `idx_ssl_status` (`ssl_status`);
  END IF;

  -- 检查并添加ssl_expire_date索引
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'websites' AND INDEX_NAME = 'idx_ssl_expire_date') THEN
    ALTER TABLE `websites` ADD INDEX `idx_ssl_expire_date` (`ssl_expire_date`);
  END IF;

  -- 检查并添加monitor_enabled索引
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'websites' AND INDEX_NAME = 'idx_monitor_enabled') THEN
    ALTER TABLE `websites` ADD INDEX `idx_monitor_enabled` (`monitor_enabled`);
  END IF;

  -- 检查并添加last_ssl_check索引
  IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.STATISTICS
                 WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'websites' AND INDEX_NAME = 'idx_last_ssl_check') THEN
    ALTER TABLE `websites` ADD INDEX `idx_last_ssl_check` (`last_ssl_check`);
  END IF;
END$$

DELIMITER ;

-- 执行存储过程
CALL AddIndexIfNotExists();

-- 删除存储过程
DROP PROCEDURE AddIndexIfNotExists;

-- 创建视图：监控状态概览
CREATE OR REPLACE VIEW `v_website_monitor_status` AS
SELECT
  w.id,
  w.site_name,
  w.domain,
  w.site_url,
  w.access_status,
  w.ssl_status,
  w.ssl_expire_date,
  w.ssl_days_remaining,
  w.last_check_time,
  w.last_ssl_check,
  w.monitor_enabled,
  w.monitor_interval,
  CASE
    WHEN w.ssl_expire_date IS NOT NULL AND w.ssl_expire_date <= CURDATE() THEN 'expired'
    WHEN w.ssl_expire_date IS NOT NULL AND w.ssl_expire_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 'expiring_soon'
    WHEN w.ssl_expire_date IS NOT NULL AND w.ssl_expire_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 'expiring_month'
    ELSE 'normal'
  END as ssl_alert_level,
  (SELECT COUNT(*) FROM website_monitor_history h
   WHERE h.website_id = w.id AND h.status = 0
   AND h.check_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as error_count_24h,
  (SELECT AVG(response_time) FROM website_monitor_history h
   WHERE h.website_id = w.id AND h.status = 1
   AND h.check_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as avg_response_time_24h
FROM websites w
WHERE w.status = 'active';

-- 创建视图：SSL证书到期预警
CREATE OR REPLACE VIEW `v_ssl_expiry_alerts` AS
SELECT
  w.id,
  w.site_name,
  w.domain,
  w.site_url,
  w.ssl_expire_date,
  w.ssl_days_remaining,
  w.ssl_issuer,
  CASE
    WHEN w.ssl_expire_date <= CURDATE() THEN 'expired'
    WHEN w.ssl_expire_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 'critical'
    WHEN w.ssl_expire_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) THEN 'warning'
    ELSE 'normal'
  END as alert_level
FROM websites w
WHERE w.status = 'active'
  AND w.ssl_expire_date IS NOT NULL
  AND w.ssl_expire_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)
ORDER BY w.ssl_expire_date ASC;

-- 插入默认的监控通知配置示例
INSERT IGNORE INTO `monitor_notification_configs` (`name`, `type`, `enabled`, `config`, `default_for_new_monitors`) VALUES
('默认邮件通知', 'email', 0, '{"smtp_server": "smtp.example.com", "smtp_port": 587, "username": "", "password": "", "from": "", "to": "", "subject_template": "网站监控告警 - {siteName}", "body_template": "网站 {siteName} ({url}) 出现异常：{message}\\n检查时间：{checkTime}"}', 0),
('默认Webhook通知', 'webhook', 0, '{"url": "", "method": "POST", "headers": {"Content-Type": "application/json"}, "body_template": "{\\"event\\": \\"monitor_alert\\", \\"site\\": \\"{siteName}\\", \\"url\\": \\"{url}\\", \\"message\\": \\"{message}\\", \\"time\\": \\"{checkTime}\\"}"}', 0),
('默认飞书通知', 'feishu', 0, '{"webhook_url": "", "title_template": "网站监控告警", "content_template": "**网站名称**: {siteName}\\n**网站URL**: {url}\\n**异常信息**: {message}\\n**检查时间**: {checkTime}"}', 1);
