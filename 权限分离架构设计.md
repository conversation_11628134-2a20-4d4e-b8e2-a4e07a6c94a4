# 权限分离架构设计方案

## 概述

将权限系统分为两个独立的层次：
1. **前端显示权限**：控制UI元素的显示/隐藏
2. **后端API权限**：控制实际的数据访问权限

## 数据库设计

### 前端显示权限表
```sql
CREATE TABLE frontend_permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  permission_code VARCHAR(100) NOT NULL,
  granted BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_permission (user_id, permission_code)
);
```

### 后端API权限表
```sql
CREATE TABLE api_permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  permission_code VARCHAR(100) NOT NULL,
  granted BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_permission (user_id, permission_code)
);
```

## 权限类型定义

### 前端显示权限
- `ui.user.list.view` - 显示用户列表页面
- `ui.user.create.button` - 显示新建用户按钮
- `ui.user.edit.button` - 显示编辑用户按钮
- `ui.user.delete.button` - 显示删除用户按钮

### 后端API权限
- `api.user.list` - 获取用户列表API
- `api.user.create` - 创建用户API
- `api.user.update` - 更新用户API
- `api.user.delete` - 删除用户API

## 实现方案

### 前端权限检查
```typescript
// 前端权限Hook
export const useFrontendPermission = (permission: string) => {
  const { user } = useAuth();
  const [hasPermission, setHasPermission] = useState(false);

  useEffect(() => {
    const checkPermission = async () => {
      const result = await permissionAPI.checkFrontendPermission(permission);
      setHasPermission(result);
    };
    checkPermission();
  }, [permission]);

  return hasPermission;
};

// 使用示例
const UserList = () => {
  const canViewList = useFrontendPermission('ui.user.list.view');
  const canCreateUser = useFrontendPermission('ui.user.create.button');

  if (!canViewList) {
    return <div>您没有权限查看此页面</div>;
  }

  return (
    <div>
      <h1>用户列表</h1>
      {canCreateUser && (
        <Button onClick={handleCreate}>新建用户</Button>
      )}
    </div>
  );
};
```

### 后端权限检查
```javascript
// 后端API权限中间件
const requireApiPermission = (permission) => {
  return async (req, res, next) => {
    try {
      const userId = req.user.id;
      const hasPermission = await apiPermissionService.checkPermission(userId, permission);
      
      if (hasPermission) {
        next();
      } else {
        res.status(403).json({
          success: false,
          message: '您没有权限访问此API',
          requiredPermission: permission
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        message: '权限验证失败'
      });
    }
  };
};

// 使用示例
router.get('/users', 
  requireApiPermission('api.user.list'),
  userController.getUsers
);

router.post('/users', 
  requireApiPermission('api.user.create'),
  userController.createUser
);
```

## 优势分析

### 1. 更细粒度的控制
- 可以单独控制某个按钮的显示
- 可以单独控制某个API的访问
- 前后端权限可以独立配置

### 2. 更好的用户体验
- 用户看不到无权限的功能按钮
- 避免点击后被拒绝的情况
- 界面更加简洁

### 3. 更强的安全性
- 前端权限控制UI显示
- 后端权限控制数据访问
- 双重保护机制

## 潜在问题

### 1. 复杂性增加
- 需要维护两套权限系统
- 权限配置工作量增加
- 系统复杂度提升

### 2. 一致性问题
- 前后端权限可能不一致
- 需要额外的同步机制
- 权限管理更加复杂

### 3. 性能影响
- 需要更多的权限检查
- 数据库查询增加
- 缓存策略更复杂

## 建议

### 混合方案
1. 保持当前的统一权限系统作为基础
2. 在需要细粒度控制的地方添加前端显示权限
3. 提供权限映射机制，自动同步相关权限

### 实施步骤
1. 先修正当前的"权限冲突"概念
2. 评估是否真的需要权限分离
3. 在特定场景下试点权限分离
4. 根据效果决定是否全面推广
