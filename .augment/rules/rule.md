---
type: "always_apply"
---

作为WordPress站点管理系统开发的AI助手，请严格遵循以下开发规范和工作流程：

## 项目基础信息
用Playwright进行视觉调试

### 🏗️ 项目架构
- **项目类型**: 全栈Web应用
- **架构模式**: 前后端分离
- **部署方式**: systemd服务管理

### 🔧 技术栈

#### 后端 (Backend)
- **主要入口**: `backend/app.js` (真正的服务启动文件)
- **备用文件**: `backend/simple-server.js` (功能更完整，但未被启动脚本使用)
- **运行时**: Node.js + Express.js
- **端口**: 3001
- **API前缀**: `/api/v1`
- **特性**:
  - RESTful API设计
  - JWT认证授权
  - 权限管理系统
  - 定时任务调度
  - 文件上传处理
  - SSL证书检测
  - 服务器监控

#### 前端 (Frontend)
- **主要入口**: `frontend/src/main.tsx`
- **开发服务器**: Vite
- **技术栈**: React 18 + TypeScript + Ant Design
- **端口**: 3000
- **代理配置**: `/api` → `http://localhost:3001`
- **构建工具**: Vite + Rollup
- **特性**:
  - 响应式设计
  - 组件化开发
  - 路由管理
  - 状态管理
  - 图表可视化

### 🗄️ 数据存储

#### MySQL数据库
- **主机**: localhost:3306
- **数据库名**: sitemanager
- **用户名**: sitemanager
- **密码**: sitemanager123
- **字符集**: utf8mb4
- **时区**: +08:00


#### Redis缓存
- **主机**: localhost:6379
- **数据库**: 0
- **用途**:
  - 会话存储
  - API缓存
  - 任务队列
  - 实时数据缓存

### 🚀 服务启动

#### 全栈启动 (推荐)

# systemd服务方式
sudo systemctl start sitemanager
sudo systemctl status sitemanager

# 手动启动方式
./start.sh


#### 单独启动

# 仅启动后端
cd backend && node simple-server.js

# 仅启动前端
cd frontend && npm run dev


### 🌐 访问地址
- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:3001
- **SSL检查器**: http://localhost:3001/ssl-checker/web/
- **API文档**: http://localhost:3001/api/v1 (需认证)

### 📁 核心目录结构

/opt/sitemanager/
├── backend/
│   ├── app.js              # 🎯 主要后端入口文件
│   ├── simple-server.js     # 📄 备用文件(功能更完整)
│   ├── models/             # 数据模型
│   ├── services/           # 业务服务
│   ├── utils/              # 工具函数
│   └── config/             # 配置文件
├── frontend/
│   ├── src/
│   │   ├── main.tsx        # 🎯 前端入口文件
│   │   ├── components/     # React组件
│   │   ├── pages/          # 页面组件
│   │   └── utils/          # 工具函数
│   ├── vite.config.ts      # Vite配置
│   └── package.json        # 依赖管理
├── start.sh                # 🚀 全栈启动脚本
├── stop.sh                 # 🛑 停止脚本
├── .env                    # 环境变量配置
└── README.md               # 项目文档

### 🔐 环境变量配置
主要配置文件：`.env`
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sitemanager
DB_USER=sitemanager
DB_PASSWORD=sitemanager123

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# 应用配置
NODE_ENV=production
PORT=3001
FRONTEND_URL=http://localhost:3000


## 语言和注释规范
1. **中文优先原则**：所有代码、注释、文档、变量名、函数名都必须使用中文。发现任何英文注释时，必须立即修改为中文注释。

## 安全和质量标准
2. **安全优先原则**：在处理任何问题时，必须运用批判性思维，将安全性作为首要考虑因素。包括但不限于：数据验证、SQL注入防护、XSS防护、权限检查、文件上传安全等。

3. **完整实现原则**：严禁创建演示性质、占位符性质或不完整的功能。每个设计的功能都必须完整实现，除非明确得到用户许可可以简化或跳过。

## 文档和记录规范
4. **变更记录原则**：每次代码修改、功能添加、问题修复都必须在`change.log`文件中详细记录，包括：修改时间、修改内容、修改原因、影响范围。

5. **结构文档原则**：所有功能结构、代码架构、模块关系都必须记录在`README.md`中。在进行任何修改前，必须先查阅README.md了解当前项目结构。

## 开发和调试流程
6. **日志优先调试**：在修复问题和排错前，必须先查看对应模块的日志文件，通过日志分析问题根源，然后再进行修复。禁止盲目修改代码。

7. **任务状态管理**：完成的任务必须及时标记，可以在README.md的任务列表中添加完成标记（✅），或在change.log中记录完成状态。

## 代码质量和结构规范
8. **代码清晰原则**：所有功能实现和代码结构都必须保持清晰易懂，包括：
   - 函数职责单一
   - 变量命名有意义
   - 代码逻辑层次分明
   - 适当的代码注释

## 文件管理规范
9. **测试文件管理**：
   - 禁止在项目根目录或各模块目录下创建单独的测试文件
   - 所有测试文件必须统一放在专门的测试目录中（如`/tests/`）
   - 临时调试文件使用完毕后必须立即删除
   - 优先使用日志系统进行调试，而非创建测试文件

10. **目录结构原则**：
    - 保持清晰简洁的目录结构
    - 及时清理无用文件和冗余代码
    - 每个文件都应有明确的用途和位置
    - 避免重复功能的文件

## 工作流程要求
在执行任何开发任务时，请按以下顺序进行：
1. 查阅README.md了解项目当前状态
2. 检查相关模块的日志文件
3. 分析问题或需求
4. 设计安全可靠的解决方案
5. 完整实现功能（不允许半成品）
6. 更新change.log记录变更
7. 更新README.md（如有结构变化）
8. 清理临时文件
9. 标记任务完成状态
10.用Playwright结合控制台日志、后端日志来进行错误的排查，不能只看一个方面的错误
11.禁止为了节省时间走捷径，一步步来
12.排错前先看后端日志有没有报错
13.扫描项目一定要扫描全

请确保每个步骤都严格执行，不得跳过或简化任何环节。