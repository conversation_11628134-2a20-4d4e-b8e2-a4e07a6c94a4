#!/bin/bash

echo "🔧 为网站管理API添加认证保护..."

# 网站基础操作
sed -i "1141s/app\.get('\\/api\\/v1\\/websites', async/app.get('\\/api\\/v1\\/websites', authenticateToken, async/" backend/simple-server.js
sed -i "1184s/app\.get('\\/api\\/v1\\/websites\\/stats', async/app.get('\\/api\\/v1\\/websites\\/stats', authenticateToken, async/" backend/simple-server.js
sed -i "1412s/app\.post('\\/api\\/v1\\/websites', async/app.post('\\/api\\/v1\\/websites', authenticateToken, async/" backend/simple-server.js
sed -i "1485s/app\.put('\\/api\\/v1\\/websites\\/:id', async/app.put('\\/api\\/v1\\/websites\\/:id', authenticateToken, async/" backend/simple-server.js
sed -i "1901s/app\.delete('\\/api\\/v1\\/websites\\/:id', async/app.delete('\\/api\\/v1\\/websites\\/:id', authenticateToken, async/" backend/simple-server.js

# 网站访问检查和凭据管理
sed -i "1555s/app\.post('\\/api\\/v1\\/websites\\/:id\\/access-check', async/app.post('\\/api\\/v1\\/websites\\/:id\\/access-check', authenticateToken, async/" backend/simple-server.js
sed -i "1675s/app\.get('\\/api\\/v1\\/websites\\/:id\\/credentials', async/app.get('\\/api\\/v1\\/websites\\/:id\\/credentials', authenticateToken, async/" backend/simple-server.js
sed -i "1729s/app\.post('\\/api\\/v1\\/websites\\/:id\\/credentials', async/app.post('\\/api\\/v1\\/websites\\/:id\\/credentials', authenticateToken, async/" backend/simple-server.js
sed -i "1796s/app\.put('\\/api\\/v1\\/websites\\/:id\\/credentials\\/:credentialId', async/app.put('\\/api\\/v1\\/websites\\/:id\\/credentials\\/:credentialId', authenticateToken, async/" backend/simple-server.js
sed -i "1860s/app\.delete('\\/api\\/v1\\/websites\\/:id\\/credentials\\/:credentialId', async/app.delete('\\/api\\/v1\\/websites\\/:id\\/credentials\\/:credentialId', authenticateToken, async/" backend/simple-server.js

echo "✅ 网站API认证保护完成"
