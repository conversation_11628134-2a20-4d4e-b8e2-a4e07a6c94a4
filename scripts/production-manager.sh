#!/bin/bash

# SiteManager 生产环境管理脚本
# 版本: 1.0.0
# 描述: 生产环境服务管理、监控、备份等功能

set -e

# 脚本配置
SCRIPT_VERSION="1.0.0"
PROJECT_NAME="sitemanager"
PROJECT_DIR="/opt/$PROJECT_NAME"
LOG_DIR="/var/log/$PROJECT_NAME"
BACKUP_DIR="/var/backups/$PROJECT_NAME"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示帮助信息
show_help() {
    echo "SiteManager 生产环境管理工具 v$SCRIPT_VERSION"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  status          查看服务状态"
    echo "  start           启动服务"
    echo "  stop            停止服务"
    echo "  restart         重启服务"
    echo "  logs            查看日志"
    echo "  backup          备份数据"
    echo "  restore         恢复数据"
    echo "  update          更新应用"
    echo "  monitor         系统监控"
    echo "  health          健康检查"
    echo "  cleanup         清理日志和临时文件"
    echo ""
    echo "选项:"
    echo "  -f, --follow    跟踪日志输出"
    echo "  -n, --lines     显示日志行数 (默认: 50)"
    echo "  -d, --date      指定备份/恢复日期"
    echo "  -h, --help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 status                    # 查看服务状态"
    echo "  $0 logs -f                   # 跟踪日志"
    echo "  $0 backup                    # 创建备份"
    echo "  $0 restore -d 20231201       # 恢复指定日期备份"
}

# 检查服务状态
check_status() {
    log_info "检查 SiteManager 服务状态..."
    
    # 检查 systemd 服务
    if systemctl is-active --quiet $PROJECT_NAME; then
        log_success "SiteManager 服务运行正常"
    else
        log_error "SiteManager 服务未运行"
        return 1
    fi
    
    # 检查端口
    if netstat -tlnp | grep -q ":3001"; then
        log_success "后端服务端口 3001 监听正常"
    else
        log_warning "后端服务端口 3001 未监听"
    fi
    
    # 检查 Nginx
    if systemctl is-active --quiet nginx; then
        log_success "Nginx 服务运行正常"
    else
        log_warning "Nginx 服务未运行"
    fi
    
    # 检查 MySQL
    if systemctl is-active --quiet mysql || systemctl is-active --quiet mysqld; then
        log_success "MySQL 服务运行正常"
    else
        log_error "MySQL 服务未运行"
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 80 ]; then
        log_warning "磁盘使用率过高: ${disk_usage}%"
    else
        log_success "磁盘使用率正常: ${disk_usage}%"
    fi
    
    # 检查内存使用
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$mem_usage" -gt 80 ]; then
        log_warning "内存使用率过高: ${mem_usage}%"
    else
        log_success "内存使用率正常: ${mem_usage}%"
    fi
}

# 启动服务
start_service() {
    log_info "启动 SiteManager 服务..."
    
    # 启动 MySQL
    smart_sudo systemctl start mysql || smart_sudo systemctl start mysqld
    
    # 启动 SiteManager
    smart_sudo systemctl start $PROJECT_NAME
    
    # 启动 Nginx
    smart_sudo systemctl start nginx
    
    sleep 3
    check_status
}

# 停止服务
stop_service() {
    log_info "停止 SiteManager 服务..."
    
    smart_sudo systemctl stop $PROJECT_NAME
    log_success "SiteManager 服务已停止"
}

# 重启服务
restart_service() {
    log_info "重启 SiteManager 服务..."
    
    smart_sudo systemctl restart $PROJECT_NAME
    smart_sudo systemctl reload nginx
    
    sleep 3
    check_status
}

# 查看日志
view_logs() {
    local follow=false
    local lines=50
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -f|--follow)
                follow=true
                shift
                ;;
            -n|--lines)
                lines="$2"
                shift 2
                ;;
            *)
                shift
                ;;
        esac
    done
    
    if [ "$follow" = true ]; then
        log_info "跟踪 SiteManager 日志 (Ctrl+C 退出)..."
        smart_sudo journalctl -u $PROJECT_NAME -f
    else
        log_info "显示最近 $lines 行日志..."
        smart_sudo journalctl -u $PROJECT_NAME -n "$lines" --no-pager
    fi
}

# 备份数据
backup_data() {
    local backup_date=$(date +%Y%m%d_%H%M%S)
    local backup_path="$BACKUP_DIR/$backup_date"
    
    log_info "创建数据备份到: $backup_path"
    
    # 创建备份目录
    smart_sudo mkdir -p "$backup_path"
    
    # 备份数据库
    if [ -f "$PROJECT_DIR/.env.production" ]; then
        source "$PROJECT_DIR/.env.production"
        log_info "备份数据库..."
        mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" | \
            smart_sudo tee "$backup_path/database.sql" > /dev/null
    fi
    
    # 备份配置文件
    log_info "备份配置文件..."
    smart_sudo cp -r "$PROJECT_DIR/.env.production" "$backup_path/"
    smart_sudo cp -r "/etc/nginx/sites-available/$PROJECT_NAME" "$backup_path/nginx.conf" 2>/dev/null || true
    
    # 备份上传文件
    if [ -d "$PROJECT_DIR/backend/uploads" ]; then
        log_info "备份上传文件..."
        smart_sudo tar -czf "$backup_path/uploads.tar.gz" -C "$PROJECT_DIR/backend" uploads
    fi
    
    # 备份日志
    log_info "备份日志文件..."
    smart_sudo tar -czf "$backup_path/logs.tar.gz" -C "$LOG_DIR" . 2>/dev/null || true
    
    # 设置权限
    smart_sudo chown -R sitemanager:sitemanager "$backup_path"
    
    log_success "备份完成: $backup_path"
    
    # 清理旧备份 (保留30天)
    find "$BACKUP_DIR" -type d -mtime +30 -exec smart_sudo rm -rf {} + 2>/dev/null || true
}

# 恢复数据
restore_data() {
    local restore_date=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--date)
                restore_date="$2"
                shift 2
                ;;
            *)
                shift
                ;;
        esac
    done
    
    if [ -z "$restore_date" ]; then
        log_error "请指定恢复日期，格式: YYYYMMDD"
        return 1
    fi
    
    local backup_path=$(find "$BACKUP_DIR" -name "${restore_date}*" -type d | head -1)
    
    if [ -z "$backup_path" ]; then
        log_error "未找到日期为 $restore_date 的备份"
        return 1
    fi
    
    log_info "从备份恢复数据: $backup_path"
    
    # 停止服务
    smart_sudo systemctl stop $PROJECT_NAME
    
    # 恢复数据库
    if [ -f "$backup_path/database.sql" ] && [ -f "$PROJECT_DIR/.env.production" ]; then
        source "$PROJECT_DIR/.env.production"
        log_info "恢复数据库..."
        mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$backup_path/database.sql"
    fi
    
    # 恢复上传文件
    if [ -f "$backup_path/uploads.tar.gz" ]; then
        log_info "恢复上传文件..."
        smart_sudo rm -rf "$PROJECT_DIR/backend/uploads"
        smart_sudo tar -xzf "$backup_path/uploads.tar.gz" -C "$PROJECT_DIR/backend/"
        smart_sudo chown -R sitemanager:sitemanager "$PROJECT_DIR/backend/uploads"
    fi
    
    # 启动服务
    smart_sudo systemctl start $PROJECT_NAME
    
    log_success "数据恢复完成"
}

# 更新应用
update_application() {
    log_info "更新 SiteManager 应用..."
    
    # 创建备份
    backup_data
    
    # 停止服务
    smart_sudo systemctl stop $PROJECT_NAME
    
    # 更新代码 (如果是 Git 仓库)
    if [ -d "$PROJECT_DIR/.git" ]; then
        log_info "更新代码..."
        cd "$PROJECT_DIR"
        smart_sudo -u sitemanager git pull origin main
        
        # 安装依赖
        if [ -f "backend/package.json" ]; then
            cd backend
            smart_sudo -u sitemanager npm ci --production
            cd ..
        fi
        
        if [ -f "frontend/package.json" ]; then
            cd frontend
            smart_sudo -u sitemanager npm ci
            smart_sudo -u sitemanager npm run build
            cd ..
        fi
    else
        log_warning "非 Git 仓库，请手动更新代码"
    fi
    
    # 启动服务
    smart_sudo systemctl start $PROJECT_NAME
    
    log_success "应用更新完成"
}

# 系统监控
system_monitor() {
    log_info "SiteManager 系统监控信息"
    echo ""
    
    # 服务状态
    echo -e "${CYAN}=== 服务状态 ===${NC}"
    systemctl status $PROJECT_NAME --no-pager -l
    echo ""
    
    # 资源使用
    echo -e "${CYAN}=== 资源使用 ===${NC}"
    echo "CPU 使用率:"
    top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//'
    echo ""
    echo "内存使用:"
    free -h
    echo ""
    echo "磁盘使用:"
    df -h /
    echo ""
    
    # 网络连接
    echo -e "${CYAN}=== 网络连接 ===${NC}"
    netstat -tlnp | grep -E ":(80|443|3001|3306)"
    echo ""
    
    # 最近日志
    echo -e "${CYAN}=== 最近日志 ===${NC}"
    smart_sudo journalctl -u $PROJECT_NAME -n 10 --no-pager
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local health_score=0
    local total_checks=6
    
    # 检查服务状态
    if systemctl is-active --quiet $PROJECT_NAME; then
        ((health_score++))
    fi
    
    # 检查端口监听
    if netstat -tlnp | grep -q ":3001"; then
        ((health_score++))
    fi
    
    # 检查数据库连接
    if [ -f "$PROJECT_DIR/.env.production" ]; then
        source "$PROJECT_DIR/.env.production"
        if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &>/dev/null; then
            ((health_score++))
        fi
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 90 ]; then
        ((health_score++))
    fi
    
    # 检查内存使用
    local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    if [ "$mem_usage" -lt 90 ]; then
        ((health_score++))
    fi
    
    # 检查 Nginx
    if systemctl is-active --quiet nginx; then
        ((health_score++))
    fi
    
    # 计算健康分数
    local health_percentage=$((health_score * 100 / total_checks))
    
    echo ""
    echo -e "${CYAN}=== 健康检查结果 ===${NC}"
    echo "健康分数: $health_score/$total_checks ($health_percentage%)"
    
    if [ "$health_percentage" -ge 80 ]; then
        log_success "系统健康状态良好"
    elif [ "$health_percentage" -ge 60 ]; then
        log_warning "系统健康状态一般，建议检查"
    else
        log_error "系统健康状态较差，需要立即处理"
    fi
}

# 清理系统
cleanup_system() {
    log_info "清理系统文件..."
    
    # 清理旧日志
    smart_sudo find "$LOG_DIR" -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    # 清理临时文件
    smart_sudo find /tmp -name "*$PROJECT_NAME*" -mtime +1 -delete 2>/dev/null || true
    
    # 清理 npm 缓存
    smart_sudo -u sitemanager npm cache clean --force 2>/dev/null || true
    
    # 清理系统日志
    smart_sudo journalctl --vacuum-time=7d
    
    log_success "系统清理完成"
}

# 主函数
main() {
    local command="$1"
    shift
    
    case "$command" in
        status)
            check_status
            ;;
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        logs)
            view_logs "$@"
            ;;
        backup)
            backup_data
            ;;
        restore)
            restore_data "$@"
            ;;
        update)
            update_application
            ;;
        monitor)
            system_monitor
            ;;
        health)
            health_check
            ;;
        cleanup)
            cleanup_system
            ;;
        -h|--help|help)
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 智能sudo函数
smart_sudo() {
    if [ "$EUID" -eq 0 ]; then
        "$@"
    else
        smart_sudo "$@"
    fi
}

# 检查权限（帮助命令除外）
if [[ "$1" != "-h" && "$1" != "--help" && "$1" != "help" ]]; then
    if [ "$EUID" -eq 0 ]; then
        log_info "检测到以 root 用户运行，将直接执行系统命令"
    else
        log_info "将使用 smart_sudo 执行需要管理员权限的命令"
    fi
fi

# 运行主函数
main "$@"
