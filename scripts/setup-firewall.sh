#!/bin/bash

echo "🔥 配置防火墙保护..."

# 重置防火墙规则
ufw --force reset

# 设置默认策略
ufw default deny incoming
ufw default allow outgoing

# 允许SSH（重要！）
ufw allow ssh
ufw allow 22

# 只允许本地访问SiteManager端口
ufw allow from 127.0.0.1 to any port 3000
ufw allow from 127.0.0.1 to any port 3001
ufw allow from ::1 to any port 3000
ufw allow from ::1 to any port 3001

# 允许局域网访问（可选，根据需要调整）
# ufw allow from 10.0.0.0/8 to any port 3000
# ufw allow from 10.0.0.0/8 to any port 3001
# ufw allow from ***********/16 to any port 3000
# ufw allow from ***********/16 to any port 3001

# 启用防火墙
ufw --force enable

echo "✅ 防火墙配置完成"
echo "📋 当前防火墙状态:"
ufw status numbered

echo ""
echo "🚨 重要提醒:"
echo "- SiteManager现在只能从本地访问 (localhost:3000)"
echo "- 如需远程访问，请先修复认证系统"
echo "- SSH访问仍然开放，可以远程管理"
