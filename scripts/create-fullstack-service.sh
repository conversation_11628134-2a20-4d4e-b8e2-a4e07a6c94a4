#!/bin/bash

echo "🚀 创建SiteManager全栈服务..."

# 1. 创建前端服务脚本
echo "🌐 创建前端服务脚本..."
cat > /opt/sitemanager/start-frontend.sh << 'FRONTEND_EOF'
#!/bin/bash

cd /opt/sitemanager/frontend

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 使用开发服务器（避免构建错误）
echo "🌐 启动前端开发服务器 (端口 3000)..."
npm run dev -- --host 0.0.0.0 --port 3000
FRONTEND_EOF

chmod +x /opt/sitemanager/start-frontend.sh

# 2. 创建全栈启动脚本
echo "🔗 创建全栈启动脚本..."
cat > /opt/sitemanager/start-fullstack.sh << 'FULLSTACK_EOF'
#!/bin/bash

echo "🚀 启动SiteManager全栈服务..."

# 启动后端服务
echo "🔧 启动后端服务..."
cd /opt/sitemanager
node backend/simple-server.js &
BACKEND_PID=$!
echo "✅ 后端服务已启动 (PID: $BACKEND_PID, 端口: 3001)"

# 等待后端启动
sleep 3

# 启动前端服务
echo "🌐 启动前端服务..."
/opt/sitemanager/start-frontend.sh &
FRONTEND_PID=$!
echo "✅ 前端服务已启动 (PID: $FRONTEND_PID, 端口: 3000)"

# 创建PID文件
echo $BACKEND_PID > /var/run/sitemanager-backend.pid
echo $FRONTEND_PID > /var/run/sitemanager-frontend.pid

echo "🎉 SiteManager全栈服务启动完成！"
echo "📱 前端访问地址: http://localhost:3000"
echo "🔧 后端API地址: http://localhost:3001"

# 等待子进程
wait $BACKEND_PID $FRONTEND_PID
FULLSTACK_EOF

chmod +x /opt/sitemanager/start-fullstack.sh

# 3. 创建全栈停止脚本
echo "🛑 创建全栈停止脚本..."
cat > /opt/sitemanager/stop-fullstack.sh << 'STOP_EOF'
#!/bin/bash

echo "🛑 停止SiteManager全栈服务..."

# 停止后端
if [ -f /var/run/sitemanager-backend.pid ]; then
    BACKEND_PID=$(cat /var/run/sitemanager-backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "✅ 后端服务已停止"
    fi
    rm -f /var/run/sitemanager-backend.pid
fi

# 停止前端
if [ -f /var/run/sitemanager-frontend.pid ]; then
    FRONTEND_PID=$(cat /var/run/sitemanager-frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        kill $FRONTEND_PID
        echo "✅ 前端服务已停止"
    fi
    rm -f /var/run/sitemanager-frontend.pid
fi

# 清理可能残留的进程
pkill -f "node backend/simple-server.js" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true

echo "🎉 全栈服务已完全停止"
STOP_EOF

chmod +x /opt/sitemanager/stop-fullstack.sh

echo "✅ 全栈服务脚本创建完成！"
