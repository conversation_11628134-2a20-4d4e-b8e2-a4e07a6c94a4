#!/bin/bash

echo "🧹 清理重复的认证定义..."

# 备份文件
cp backend/simple-server.js backend/simple-server.js.before_clean

# 删除所有重复的JWT配置和认证中间件定义
# 但保留API路由中的authenticateToken使用

# 删除JWT配置块
sed -i '/^\/\/ JWT配置$/,/^const JWT_REFRESH_EXPIRES_IN/d' backend/simple-server.js

# 删除认证中间件定义块
sed -i '/^\/\/ 认证中间件$/,/^};$/d' backend/simple-server.js

# 删除可选认证中间件定义块  
sed -i '/^\/\/ 可选认证中间件/,/^};$/d' backend/simple-server.js

echo "✅ 清理完成"
