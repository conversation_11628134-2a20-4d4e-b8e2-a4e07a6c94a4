#!/bin/bash

# 站点监控服务启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查Node.js环境
check_nodejs() {
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    NODE_VERSION=$(node --version)
    log_success "Node.js 版本: $NODE_VERSION"
}

# 检查数据库连接
check_database() {
    log_info "检查数据库连接..."
    
    # 从配置文件读取数据库信息
    if [ -f ".env.monitoring" ]; then
        source .env.monitoring
    else
        log_error "配置文件 .env.monitoring 不存在"
        exit 1
    fi
    
    # 测试MySQL连接
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME;" 2>/dev/null; then
        log_success "数据库连接正常"
    else
        log_error "数据库连接失败，请检查配置"
        exit 1
    fi
}

# 初始化数据库表
init_database() {
    log_info "初始化监控数据库表..."
    
    if [ -f "database/monitoring_tables.sql" ]; then
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < database/monitoring_tables.sql
        log_success "数据库表初始化完成"
    else
        log_warning "数据库表文件不存在，跳过初始化"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "检查并安装依赖..."
    
    cd backend
    
    # 检查package.json是否存在
    if [ ! -f "package.json" ]; then
        log_info "初始化 package.json..."
        npm init -y
    fi
    
    # 安装必要的依赖
    REQUIRED_PACKAGES="mysql2 axios nodemailer express cors"
    
    for package in $REQUIRED_PACKAGES; do
        if ! npm list "$package" &> /dev/null; then
            log_info "安装 $package..."
            npm install "$package"
        fi
    done
    
    cd ..
    log_success "依赖安装完成"
}

# 创建日志目录
create_log_dir() {
    if [ ! -d "logs" ]; then
        mkdir -p logs
        log_success "创建日志目录"
    fi
}

# 检查端口占用
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口 $port 已被占用"
        return 1
    fi
    return 0
}

# 启动监控服务
start_monitoring() {
    log_info "启动站点监控服务..."
    
    # 检查端口
    MONITOR_PORT=${MONITOR_PORT:-3002}
    if ! check_port $MONITOR_PORT; then
        log_error "监控服务端口 $MONITOR_PORT 被占用，请修改配置或停止占用进程"
        exit 1
    fi
    
    # 设置环境变量
    export NODE_ENV=production
    
    # 启动监控服务
    cd backend
    
    if [ "$1" = "-d" ] || [ "$1" = "--daemon" ]; then
        # 后台运行
        nohup node monitoring-server.js > ../logs/monitoring.log 2>&1 &
        MONITOR_PID=$!
        echo $MONITOR_PID > ../logs/monitoring.pid
        log_success "监控服务已在后台启动 (PID: $MONITOR_PID)"
        log_info "日志文件: logs/monitoring.log"
        log_info "PID文件: logs/monitoring.pid"
    else
        # 前台运行
        node monitoring-server.js
    fi
}

# 停止监控服务
stop_monitoring() {
    log_info "停止监控服务..."
    
    if [ -f "logs/monitoring.pid" ]; then
        PID=$(cat logs/monitoring.pid)
        if kill -0 $PID 2>/dev/null; then
            kill $PID
            rm -f logs/monitoring.pid
            log_success "监控服务已停止"
        else
            log_warning "监控服务进程不存在"
            rm -f logs/monitoring.pid
        fi
    else
        log_warning "PID文件不存在，尝试查找进程..."
        pkill -f "monitoring-server.js" && log_success "监控服务已停止" || log_warning "未找到监控服务进程"
    fi
}

# 查看服务状态
status_monitoring() {
    log_info "检查监控服务状态..."
    
    if [ -f "logs/monitoring.pid" ]; then
        PID=$(cat logs/monitoring.pid)
        if kill -0 $PID 2>/dev/null; then
            log_success "监控服务正在运行 (PID: $PID)"
            
            # 检查API是否响应
            MONITOR_PORT=${MONITOR_PORT:-3002}
            if curl -s "http://localhost:$MONITOR_PORT/status" > /dev/null; then
                log_success "监控API服务正常"
            else
                log_warning "监控API服务无响应"
            fi
        else
            log_error "监控服务进程不存在"
            rm -f logs/monitoring.pid
        fi
    else
        log_warning "监控服务未运行"
    fi
}

# 查看日志
view_logs() {
    if [ -f "logs/monitoring.log" ]; then
        tail -f logs/monitoring.log
    else
        log_warning "日志文件不存在"
    fi
}

# 测试通知
test_notification() {
    log_info "测试通知功能..."
    
    MONITOR_PORT=${MONITOR_PORT:-3002}
    
    if curl -s -X POST "http://localhost:$MONITOR_PORT/test-notification" \
       -H "Content-Type: application/json" \
       -d '{"type":"all"}' > /dev/null; then
        log_success "测试通知已发送"
    else
        log_error "测试通知发送失败"
    fi
}

# 显示帮助信息
show_help() {
    echo "站点监控服务管理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start, -s          启动监控服务（前台运行）"
    echo "  start -d           启动监控服务（后台运行）"
    echo "  stop               停止监控服务"
    echo "  restart            重启监控服务"
    echo "  status             查看服务状态"
    echo "  logs               查看实时日志"
    echo "  test               测试通知功能"
    echo "  init               初始化数据库表"
    echo "  help, -h           显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start           # 前台启动"
    echo "  $0 start -d        # 后台启动"
    echo "  $0 stop            # 停止服务"
    echo "  $0 logs            # 查看日志"
}

# 主函数
main() {
    echo "🚀 站点监控服务管理脚本"
    echo "=================================="
    
    case "$1" in
        "start"|"-s")
            check_nodejs
            check_database
            install_dependencies
            create_log_dir
            start_monitoring "$2"
            ;;
        "stop")
            stop_monitoring
            ;;
        "restart")
            stop_monitoring
            sleep 2
            check_nodejs
            check_database
            start_monitoring "-d"
            ;;
        "status")
            status_monitoring
            ;;
        "logs")
            view_logs
            ;;
        "test")
            test_notification
            ;;
        "init")
            check_database
            init_database
            ;;
        "help"|"-h"|"")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
