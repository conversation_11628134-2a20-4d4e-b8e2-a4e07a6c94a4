#!/bin/bash

# WordPress站点管理系统本地开发环境停止脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo -e "${CYAN}"
echo "🛑 WordPress站点管理系统停止脚本"
echo "=================================="
echo -e "${NC}"

LOG_DIR="./logs"

# 停止进程函数
stop_processes() {
    log_info "停止应用服务..."

    # 从PID文件读取进程ID
    if [ -f $LOG_DIR/pids.txt ]; then
        PIDS=$(cat $LOG_DIR/pids.txt)
        for pid in $PIDS; do
            if kill -0 $pid 2>/dev/null; then
                log_info "停止进程 $pid"
                kill $pid 2>/dev/null
                sleep 1

                # 如果进程仍在运行，强制杀死
                if kill -0 $pid 2>/dev/null; then
                    log_warning "强制停止进程 $pid"
                    kill -9 $pid 2>/dev/null
                fi
            fi
        done
        rm -f $LOG_DIR/pids.txt
        log_success "应用服务已停止"
    else
        log_warning "未找到PID文件，尝试按端口和进程名停止服务"

        # 停止npm进程
        pkill -f "npm run dev" 2>/dev/null && log_info "停止npm进程"
        pkill -f "vite" 2>/dev/null && log_info "停止vite进程"
        pkill -f "nodemon" 2>/dev/null && log_info "停止nodemon进程"
        pkill -f "ts-node" 2>/dev/null && log_info "停止ts-node进程"
        pkill -f "simple-server.js" 2>/dev/null && log_info "停止simple-server进程"

        # 按端口停止服务
        stop_by_port() {
            local port=$1
            local service=$2
            local pid=$(lsof -ti :$port 2>/dev/null)
            if [ ! -z "$pid" ]; then
                log_info "停止 $service (端口 $port, PID: $pid)"
                kill $pid 2>/dev/null
                sleep 1
                if kill -0 $pid 2>/dev/null; then
                    kill -9 $pid 2>/dev/null
                fi
                log_success "$service 已停止"
            fi
        }

        stop_by_port 3000 "前端服务"
        stop_by_port 3001 "后端API"
    fi
}

# 停止数据库服务
stop_database() {
    log_info "本地MySQL数据库保持运行（无需停止）"
    # 本地MySQL服务通常作为系统服务运行，不需要停止
}

# 清理临时文件
cleanup_files() {
    log_info "清理临时文件..."

    # 清理日志文件（可选）
    if [ "$1" = "--clean-logs" ]; then
        rm -f $LOG_DIR/*.log
        log_success "日志文件已清理"
    fi

    # 清理其他临时文件
    rm -f $LOG_DIR/pids.txt

    log_success "临时文件清理完成"
}

# 显示状态
show_status() {
    echo ""
    log_info "检查服务状态..."

    # 检查端口占用
    check_port() {
        local port=$1
        local service=$2
        if lsof -i :$port &> /dev/null; then
            log_warning "$service (端口 $port) 仍在运行"
            return 1
        else
            log_success "$service (端口 $port) 已停止"
            return 0
        fi
    }

    check_port 3000 "前端服务"
    check_port 3001 "后端API"

    # 检查MySQL服务状态
    if systemctl is-active --quiet mysql 2>/dev/null; then
        log_success "MySQL数据库服务运行正常"
    else
        log_warning "MySQL数据库服务未运行"
    fi

    echo ""
}

# 解析命令行参数
CLEAN_LOGS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --clean-logs)
            CLEAN_LOGS=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --clean-logs   清理日志文件"
            echo "  -h, --help     显示帮助信息"
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            exit 1
            ;;
    esac
done

# 执行停止操作
stop_processes
stop_database

if [ "$CLEAN_LOGS" = true ]; then
    cleanup_files --clean-logs
else
    cleanup_files
fi

show_status

echo -e "${GREEN}🎉 所有服务已停止！${NC}"
echo ""
echo -e "${YELLOW}💡 提示:${NC}"
echo -e "${BLUE}   重新启动: ${CYAN}./dev-local.sh${NC}"
echo -e "${BLUE}   快速启动: ${CYAN}./dev-local.sh -q${NC}"
echo -e "${BLUE}   调试模式: ${CYAN}./dev-local.sh -d -l${NC}"
echo ""
