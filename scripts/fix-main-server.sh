#!/bin/bash

# SiteManager 主服务器文件修复脚本
set -e

echo "🔧 开始修复主服务器文件的认证问题..."

# 备份当前文件
echo "📁 备份当前文件..."
cp backend/simple-server.js backend/simple-server.js.broken-$(date +%Y%m%d_%H%M%S)

# 检查主服务器文件中是否有认证模块导入
if ! grep -q "require('./auth')" backend/simple-server.js; then
    echo "🔧 添加认证模块导入..."
    # 在NotificationService导入后添加认证模块导入
    sed -i '/const NotificationService = require/a const { authenticateToken, optionalAuth, handleLogin, handleVerify, handleLogout } = require("./auth");' backend/simple-server.js
    echo "✅ 认证模块导入已添加"
else
    echo "✅ 认证模块导入已存在"
fi

# 检查是否有认证API路由
if ! grep -q "/api/auth/login" backend/simple-server.js; then
    echo "🔧 添加认证API路由..."
    # 在服务器管理API之前添加认证API
    sed -i '/\/\/ 服务器管理API/i \
// ================================\
// 认证相关API路由\
// ================================\
\
// 用户登录\
app.post("/api/auth/login", handleLogin);\
\
// 验证token有效性\
app.get("/api/auth/verify", authenticateToken, handleVerify);\
\
// 用户登出\
app.post("/api/auth/logout", handleLogout);\
\
' backend/simple-server.js
    echo "✅ 认证API路由已添加"
else
    echo "✅ 认证API路由已存在"
fi

echo "🧪 测试服务器文件语法..."
if node -c backend/simple-server.js; then
    echo "✅ 语法检查通过"
    echo "🎉 主服务器文件修复完成！"
    echo "现在可以使用 systemctl start sitemanager.service 启动服务"
else
    echo "❌ 语法检查失败，请检查文件"
    exit 1
fi
