#!/bin/bash

# 批量为API路由添加认证保护
echo "🔧 开始批量修复API认证..."

# 备份原文件
cp backend/simple-server.js backend/simple-server.js.backup

# 使用sed批量替换，为关键API添加认证
sed -i "s|app\.get('/api/v1/websites'|app.get('/api/v1/websites', authenticateToken|g" backend/simple-server.js
sed -i "s|app\.post('/api/v1/websites'|app.post('/api/v1/websites', authenticateToken|g" backend/simple-server.js
sed -i "s|app\.put('/api/v1/websites'|app.put('/api/v1/websites', authenticateToken|g" backend/simple-server.js
sed -i "s|app\.delete('/api/v1/websites'|app.delete('/api/v1/websites', authenticateToken|g" backend/simple-server.js

sed -i "s|app\.get('/api/v1/dashboard'|app.get('/api/v1/dashboard', authenticateToken|g" backend/simple-server.js
sed -i "s|app\.get('/api/v1/monitor'|app.get('/api/v1/monitor', authenticateToken|g" backend/simple-server.js
sed -i "s|app\.get('/api/v1/settings'|app.get('/api/v1/settings', authenticateToken|g" backend/simple-server.js
sed -i "s|app\.put('/api/v1/settings'|app.put('/api/v1/settings', authenticateToken|g" backend/simple-server.js
sed -i "s|app\.post('/api/v1/settings'|app.post('/api/v1/settings', authenticateToken|g" backend/simple-server.js

sed -i "s|app\.delete('/api/v1/servers'|app.delete('/api/v1/servers', authenticateToken|g" backend/simple-server.js
sed -i "s|app\.put('/api/v1/servers'|app.put('/api/v1/servers', authenticateToken|g" backend/simple-server.js
sed -i "s|app\.post('/api/v1/servers'|app.post('/api/v1/servers', authenticateToken|g" backend/simple-server.js

echo "✅ 批量修复完成"
echo "📋 检查修复结果..."
grep -c "authenticateToken" backend/simple-server.js
