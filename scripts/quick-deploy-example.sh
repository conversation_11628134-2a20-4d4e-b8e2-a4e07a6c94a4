#!/bin/bash

# SiteManager 快速部署示例脚本
# 演示如何使用部署脚本进行不同场景的部署

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}"
echo "=================================================================="
echo "    🚀 SiteManager 快速部署示例"
echo "    📋 演示不同部署场景的使用方法"
echo "=================================================================="
echo -e "${NC}"

echo -e "${YELLOW}选择部署场景:${NC}"
echo "1. 基础部署（单机，无SSL）"
echo "2. 完整部署（Nginx + SSL + PM2）"
echo "3. 集群部署（systemd + Nginx + SSL）"
echo "4. Docker 容器化部署"
echo "5. 仅检查环境"
echo "6. 查看帮助信息"
echo ""

read -p "请输入选择 (1-6): " choice

case $choice in
    1)
        echo -e "${GREEN}执行基础部署...${NC}"
        echo "命令: ./deploy-production.sh"
        echo ""
        echo "这将执行："
        echo "- 安装系统依赖"
        echo "- 安装 Node.js 和 MySQL"
        echo "- 部署项目代码"
        echo "- 配置数据库"
        echo "- 使用 systemd 管理服务"
        echo ""
        read -p "是否继续? (y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            ./deploy-production.sh
        fi
        ;;
    2)
        echo -e "${GREEN}执行完整部署...${NC}"
        read -p "请输入域名 (例: example.com): " domain
        if [ -z "$domain" ]; then
            domain="localhost"
        fi
        echo "命令: ./deploy-production.sh --domain=$domain --nginx --ssl --pm2"
        echo ""
        echo "这将执行："
        echo "- 基础部署的所有功能"
        echo "- 安装和配置 Nginx 反向代理"
        echo "- 生成 SSL 证书"
        echo "- 使用 PM2 管理进程"
        echo "- 配置防火墙"
        echo ""
        read -p "是否继续? (y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            ./deploy-production.sh --domain="$domain" --nginx --ssl --pm2
        fi
        ;;
    3)
        echo -e "${GREEN}执行集群部署...${NC}"
        read -p "请输入域名 (例: example.com): " domain
        if [ -z "$domain" ]; then
            domain="localhost"
        fi
        echo "命令: ./deploy-production.sh --mode=cluster --domain=$domain --nginx --ssl --systemd"
        echo ""
        echo "这将执行："
        echo "- 集群模式部署"
        echo "- systemd 服务管理"
        echo "- Nginx 负载均衡配置"
        echo "- SSL 证书配置"
        echo ""
        read -p "是否继续? (y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            ./deploy-production.sh --mode=cluster --domain="$domain" --nginx --ssl --systemd
        fi
        ;;
    4)
        echo -e "${GREEN}执行 Docker 部署...${NC}"
        echo "命令: docker-compose -f docker-compose.production.yml up -d"
        echo ""
        echo "这将执行："
        echo "- 构建 Docker 镜像"
        echo "- 启动所有服务容器"
        echo "- 配置服务间网络"
        echo "- 设置数据持久化"
        echo ""
        echo "请确保已安装 Docker 和 Docker Compose"
        echo "请先配置 .env.production 文件"
        echo ""
        read -p "是否继续? (y/N): " confirm
        if [[ $confirm == [yY] ]]; then
            if [ ! -f ".env.production" ]; then
                echo "复制环境变量模板..."
                cp .env.production.template .env.production
                echo "请编辑 .env.production 文件后重新运行"
                exit 1
            fi
            docker-compose -f docker-compose.production.yml up -d
        fi
        ;;
    5)
        echo -e "${GREEN}检查环境...${NC}"
        echo "命令: ./deploy-production.sh --check-only"
        echo ""
        ./deploy-production.sh --check-only
        ;;
    6)
        echo -e "${GREEN}显示帮助信息...${NC}"
        echo ""
        ./deploy-production.sh --help
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac

echo ""
echo -e "${GREEN}✅ 操作完成！${NC}"
echo ""
echo -e "${BLUE}后续操作建议:${NC}"
echo "1. 检查服务状态: ./production-manager.sh status"
echo "2. 查看日志: ./production-manager.sh logs"
echo "3. 创建备份: ./production-manager.sh backup"
echo "4. 系统监控: ./production-manager.sh monitor"
echo ""
echo -e "${BLUE}访问地址:${NC}"
if [ "$choice" == "2" ] || [ "$choice" == "3" ]; then
    echo "前端: https://$domain"
    echo "后端API: https://$domain/api"
elif [ "$choice" == "4" ]; then
    echo "前端: http://localhost"
    echo "后端API: http://localhost/api"
    echo "监控: http://localhost/monitoring"
else
    echo "前端: http://localhost:3000"
    echo "后端API: http://localhost:3001"
fi
echo ""
