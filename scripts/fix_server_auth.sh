#!/bin/bash

echo "🔧 为服务器管理API添加认证保护..."

# 服务器CRUD操作
sed -i "719s/app\.post('\\/api\\/v1\\/servers', async/app.post('\\/api\\/v1\\/servers', authenticateToken, async/" backend/simple-server.js
sed -i "754s/app\.put('\\/api\\/v1\\/servers\\/:id', async/app.put('\\/api\\/v1\\/servers\\/:id', authenticateToken, async/" backend/simple-server.js
sed -i "789s/app\.delete('\\/api\\/v1\\/servers\\/batch', async/app.delete('\\/api\\/v1\\/servers\\/batch', authenticateToken, async/" backend/simple-server.js
sed -i "853s/app\.delete('\\/api\\/v1\\/servers\\/:id', async/app.delete('\\/api\\/v1\\/servers\\/:id', authenticateToken, async/" backend/simple-server.js
sed -i "886s/app\.put('\\/api\\/v1\\/servers\\/batch\\/expire-date', async/app.put('\\/api\\/v1\\/servers\\/batch\\/expire-date', authenticateToken, async/" backend/simple-server.js

# 服务器配置和管理操作
sed -i "3384s/app\.post('\\/api\\/v1\\/servers\\/detect-config', async/app.post('\\/api\\/v1\\/servers\\/detect-config', authenticateToken, async/" backend/simple-server.js
sed -i "3450s/app\.post('\\/api\\/v1\\/servers\\/trigger-config-update', async/app.post('\\/api\\/v1\\/servers\\/trigger-config-update', authenticateToken, async/" backend/simple-server.js
sed -i "3745s/app\.post('\\/api\\/v1\\/servers\\/import', async/app.post('\\/api\\/v1\\/servers\\/import', authenticateToken, async/" backend/simple-server.js
sed -i "3788s/app\.post('\\/api\\/v1\\/servers\\/:id\\/performance-test', async/app.post('\\/api\\/v1\\/servers\\/:id\\/performance-test', authenticateToken, async/" backend/simple-server.js
sed -i "3846s/app\.post('\\/api\\/v1\\/servers\\/:id\\/connection-test', async/app.post('\\/api\\/v1\\/servers\\/:id\\/connection-test', authenticateToken, async/" backend/simple-server.js

# 批量操作
sed -i "4388s/app\.post('\\/api\\/v1\\/servers\\/batch', async/app.post('\\/api\\/v1\\/servers\\/batch', authenticateToken, async/" backend/simple-server.js
sed -i "4514s/app\.post('\\/api\\/v1\\/servers\\/batch-ssh-config', async/app.post('\\/api\\/v1\\/servers\\/batch-ssh-config', authenticateToken, async/" backend/simple-server.js
sed -i "4688s/app\.post('\\/api\\/v1\\/servers\\/batch-refresh-config', async/app.post('\\/api\\/v1\\/servers\\/batch-refresh-config', authenticateToken, async/" backend/simple-server.js

echo "✅ 服务器API认证保护完成"
