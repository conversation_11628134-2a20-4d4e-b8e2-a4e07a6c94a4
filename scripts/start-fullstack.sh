#!/bin/bash

echo "🚀 启动SiteManager全栈服务..."

# 启动后端服务
echo "🔧 启动后端服务..."
cd /opt/sitemanager
node backend/simple-server.js &
BACKEND_PID=$!
echo "✅ 后端服务已启动 (PID: $BACKEND_PID, 端口: 3001)"

# 等待后端启动
sleep 3

# 启动前端服务
echo "🌐 启动前端服务..."
/opt/sitemanager/start-frontend.sh &
FRONTEND_PID=$!
echo "✅ 前端服务已启动 (PID: $FRONTEND_PID, 端口: 3000)"

# 创建PID文件
echo $BACKEND_PID > /var/run/sitemanager-backend.pid
echo $FRONTEND_PID > /var/run/sitemanager-frontend.pid

echo "🎉 SiteManager全栈服务启动完成！"
echo "📱 前端访问地址: http://localhost:3000"
echo "🔧 后端API地址: http://localhost:3001"


# 保持脚本运行，监控子进程
while true; do
    # 检查后端进程是否还在运行
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        echo "❌ 后端进程已停止，重新启动..."
        cd /opt/sitemanager
        node backend/simple-server.js &
        BACKEND_PID=$!
        echo $BACKEND_PID > /var/run/sitemanager-backend.pid
    fi
    
    # 检查前端进程是否还在运行
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        echo "❌ 前端进程已停止，重新启动..."
        /opt/sitemanager/start-frontend.sh &
        FRONTEND_PID=$!
        echo $FRONTEND_PID > /var/run/sitemanager-frontend.pid
    fi
    
    sleep 30
done
