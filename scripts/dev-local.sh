#!/bin/bash

# WordPress站点管理系统本地开发环境启动脚本
# 设置错误时退出
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_debug() {
    echo -e "${PURPLE}🔍 $1${NC}"
}

# 创建日志目录
LOG_DIR="./logs"
mkdir -p $LOG_DIR

# 日志文件
BACKEND_LOG="$LOG_DIR/backend.log"
FRONTEND_LOG="$LOG_DIR/frontend.log"
API_LOG="$LOG_DIR/api.log"
SYSTEM_LOG="$LOG_DIR/system.log"

# 清理旧日志
> $BACKEND_LOG
> $FRONTEND_LOG
> $API_LOG
> $SYSTEM_LOG

echo -e "${CYAN}"
echo "🚀 WordPress站点管理系统本地开发环境启动脚本"
echo "=================================================="
echo -e "${NC}"

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -d, --debug    启用调试模式"
    echo "  -l, --logs     实时显示日志"
    echo "  -q, --quick    快速启动（跳过依赖检查）"
    echo "  -c, --clean    清理并重新安装依赖"
    echo "  --api-only     仅启动API服务器"
    echo "  --frontend-only 仅启动前端服务"
    echo ""
    echo "示例:"
    echo "  $0              # 正常启动"
    echo "  $0 -d -l        # 调试模式并显示日志"
    echo "  $0 --quick      # 快速启动"
    echo "  $0 --clean      # 清理重装"
}

# 解析命令行参数
DEBUG_MODE=false
SHOW_LOGS=false
QUICK_START=false
CLEAN_INSTALL=false
API_ONLY=false
FRONTEND_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -d|--debug)
            DEBUG_MODE=true
            shift
            ;;
        -l|--logs)
            SHOW_LOGS=true
            shift
            ;;
        -q|--quick)
            QUICK_START=true
            shift
            ;;
        -c|--clean)
            CLEAN_INSTALL=true
            shift
            ;;
        --api-only)
            API_ONLY=true
            shift
            ;;
        --frontend-only)
            FRONTEND_ONLY=true
            shift
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 调试模式设置
if [ "$DEBUG_MODE" = true ]; then
    set -x
    log_debug "调试模式已启用"
fi

log_info "启动WordPress站点管理系统本地开发环境..."

# 环境检查函数
check_environment() {
    if [ "$QUICK_START" = true ]; then
        log_info "快速启动模式，跳过环境检查"
        return 0
    fi

    log_info "检查开发环境..."

    # 检查Node.js是否安装
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js 18或更高版本"
        exit 1
    fi

    # 检查Node.js版本
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js版本过低，需要18或更高版本，当前版本: $(node -v)"
        exit 1
    fi

    log_success "Node.js版本检查通过: $(node -v)"

    # 检查npm是否安装
    if ! command -v npm &> /dev/null; then
        log_error "npm未安装，请先安装npm"
        exit 1
    fi

    log_success "npm版本: $(npm -v)"

    # 检查端口占用
    check_port() {
        local port=$1
        local service=$2
        if lsof -i :$port &> /dev/null; then
            log_warning "$service 端口 $port 已被占用"
            if [ "$DEBUG_MODE" = true ]; then
                log_debug "端口 $port 占用情况:"
                lsof -i :$port
            fi
            return 1
        fi
        return 0
    }

    # 检查关键端口
    if [ "$FRONTEND_ONLY" != true ]; then
        check_port 3001 "后端API"
    fi

    if [ "$API_ONLY" != true ]; then
        check_port 3000 "前端服务"
    fi
}

check_environment

# 数据库检查函数
check_database() {
    if [ "$QUICK_START" = true ]; then
        log_info "快速启动模式，跳过数据库检查"
        return 0
    fi

    log_info "检查本地数据库环境..."

    # 检查MySQL是否安装
    if ! command -v mysql &> /dev/null; then
        log_warning "MySQL未安装，请先安装MySQL服务器"
        log_info "Ubuntu/Debian: sudo apt install mysql-server"
        log_info "CentOS/RHEL: sudo yum install mysql-server"
        log_info "macOS: brew install mysql"
        return 1
    fi

    # 检查MySQL服务是否运行
    if ! systemctl is-active --quiet mysql 2>/dev/null && ! service mysql status &>/dev/null; then
        log_info "启动MySQL服务..."
        if command -v systemctl &> /dev/null; then
            sudo systemctl start mysql
        else
            sudo service mysql start
        fi

        # 等待MySQL启动
        sleep 3

        if systemctl is-active --quiet mysql 2>/dev/null || service mysql status &>/dev/null; then
            log_success "MySQL服务启动成功"
        else
            log_error "MySQL服务启动失败"
            return 1
        fi
    else
        log_success "MySQL服务运行正常"
    fi

    # 检查数据库连接
    if mysql -u root -proot123 -e "SELECT 1;" &>/dev/null; then
        log_success "MySQL数据库连接正常"
    else
        log_warning "MySQL数据库连接失败，请检查用户名密码配置"
        log_info "默认配置: 用户名=root, 密码=root123"
        log_info "如需修改，请编辑 backend/simple-server.js 中的数据库配置"
    fi

    # 检查数据库是否存在
    if mysql -u root -proot123 -e "USE sitemanager;" &>/dev/null; then
        log_success "数据库 sitemanager 存在"
    else
        log_info "创建数据库 sitemanager..."
        mysql -u root -proot123 -e "CREATE DATABASE IF NOT EXISTS sitemanager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" &>/dev/null
        if [ $? -eq 0 ]; then
            log_success "数据库创建成功"
        else
            log_warning "数据库创建失败，将使用内存数据"
        fi
    fi

    # 创建必要的目录
    log_info "创建必要的目录..."
    mkdir -p backend/logs
    mkdir -p backend/uploads
    mkdir -p backend/database
    chmod 755 backend/logs backend/uploads backend/database
}

check_database

# 依赖安装函数
install_dependencies() {
    if [ "$QUICK_START" = true ] && [ "$CLEAN_INSTALL" != true ]; then
        log_info "快速启动模式，跳过依赖检查"
        return 0
    fi

    # 清理依赖
    if [ "$CLEAN_INSTALL" = true ]; then
        log_info "清理旧依赖..."
        rm -rf backend/node_modules frontend/node_modules
        rm -f backend/package-lock.json frontend/package-lock.json
        log_success "依赖清理完成"
    fi

    # 安装后端依赖
    if [ "$FRONTEND_ONLY" != true ]; then
        log_info "安装后端依赖..."
        cd backend
        if [ ! -d "node_modules" ] || [ "$CLEAN_INSTALL" = true ]; then
            log_info "正在安装后端npm依赖..."
            npm install >> $BACKEND_LOG 2>&1
            if [ $? -eq 0 ]; then
                log_success "后端依赖安装完成"
            else
                log_error "后端依赖安装失败，查看日志: $BACKEND_LOG"
                exit 1
            fi
        else
            log_success "后端依赖已存在，跳过安装"
        fi
        cd ..
    fi

    # 安装前端依赖
    if [ "$API_ONLY" != true ]; then
        log_info "安装前端依赖..."
        cd frontend
        if [ ! -d "node_modules" ] || [ "$CLEAN_INSTALL" = true ]; then
            log_info "正在安装前端npm依赖..."
            npm install >> $FRONTEND_LOG 2>&1
            if [ $? -eq 0 ]; then
                log_success "前端依赖安装完成"
            else
                log_error "前端依赖安装失败，查看日志: $FRONTEND_LOG"
                exit 1
            fi
        else
            log_success "前端依赖已存在，跳过安装"
        fi
        cd ..
    fi
}

install_dependencies

# 服务启动函数
start_services() {
    local pids=()

    # 确保日志目录存在
    mkdir -p $LOG_DIR

    # 启动后端服务
    if [ "$FRONTEND_ONLY" != true ]; then
        log_info "启动后端服务..."
        cd backend

        # 检查是否有简化API服务器
        if [ -f "simple-server.js" ]; then
            log_info "使用简化API服务器..."
            node simple-server.js >> $API_LOG 2>&1 &
            BACKEND_PID=$!
            log_debug "后端API PID: $BACKEND_PID"
        elif [ -f "package.json" ] && grep -q "dev" package.json; then
            log_info "使用完整后端服务..."
            npm run dev >> ../$BACKEND_LOG 2>&1 &
            BACKEND_PID=$!
            log_debug "后端服务 PID: $BACKEND_PID"
        else
            log_error "未找到后端启动脚本"
            exit 1
        fi

        pids+=($BACKEND_PID)
        cd ..

        # 等待后端启动
        log_info "等待后端服务启动..."
        sleep 3

        # 检查后端是否启动成功
        if kill -0 $BACKEND_PID 2>/dev/null; then
            log_success "后端服务启动成功 (PID: $BACKEND_PID)"
        else
            log_error "后端服务启动失败"
            if [ "$DEBUG_MODE" = true ]; then
                tail -20 $BACKEND_LOG $API_LOG
            fi
            exit 1
        fi
    fi

    # 启动前端服务
    if [ "$API_ONLY" != true ]; then
        log_info "启动前端服务..."
        cd frontend
        npm run dev >> ../$FRONTEND_LOG 2>&1 &
        FRONTEND_PID=$!
        log_debug "前端服务 PID: $FRONTEND_PID"
        pids+=($FRONTEND_PID)
        cd ..

        # 等待前端启动
        log_info "等待前端服务启动..."
        sleep 3

        # 检查前端是否启动成功
        if kill -0 $FRONTEND_PID 2>/dev/null; then
            log_success "前端服务启动成功 (PID: $FRONTEND_PID)"
        else
            log_error "前端服务启动失败"
            if [ "$DEBUG_MODE" = true ]; then
                tail -20 $FRONTEND_LOG
            fi
            exit 1
        fi
    fi

    # 保存PID到文件
    echo "${pids[@]}" > $LOG_DIR/pids.txt
}

start_services

# 显示启动信息
show_startup_info() {
    echo ""
    echo -e "${GREEN}🎉 本地开发环境启动完成！${NC}"
    echo -e "${CYAN}=================================================${NC}"
    echo ""

    if [ "$API_ONLY" != true ]; then
        echo -e "${BLUE}📱 前端地址: ${CYAN}http://localhost:3000${NC}"
        echo -e "${BLUE}   ├─ 主应用: ${CYAN}http://localhost:3000${NC}"
        echo -e "${BLUE}   ├─ 系统状态: ${CYAN}http://localhost:3000/system-status.html${NC}"
        echo -e "${BLUE}   ├─ 功能演示: ${CYAN}http://localhost:3000/website-management-demo.html${NC}"
        echo -e "${BLUE}   └─ 修复报告: ${CYAN}http://localhost:3000/success-report.html${NC}"
    fi

    if [ "$FRONTEND_ONLY" != true ]; then
        echo -e "${BLUE}🔧 后端API: ${CYAN}http://localhost:3001${NC}"
        echo -e "${BLUE}   ├─ 健康检查: ${CYAN}http://localhost:3001/health${NC}"
        echo -e "${BLUE}   ├─ 仪表盘API: ${CYAN}http://localhost:3001/api/v1/dashboard/stats${NC}"
        echo -e "${BLUE}   ├─ 网站API: ${CYAN}http://localhost:3001/api/v1/websites${NC}"
        echo -e "${BLUE}   └─ 服务器API: ${CYAN}http://localhost:3001/api/v1/servers${NC}"
        echo -e "${BLUE}🗄️  数据库: ${CYAN}MySQL (localhost:3306)${NC}"
        echo -e "${BLUE}   ├─ 数据库名: ${CYAN}sitemanager${NC}"
        echo -e "${BLUE}   ├─ 用户名: ${CYAN}root${NC}"
        echo -e "${BLUE}   └─ 密码: ${CYAN}root123${NC}"
    fi

    echo ""
    echo -e "${YELLOW}📋 开发常用命令:${NC}"
    echo -e "${BLUE}   停止服务: ${CYAN}./stop-local.sh${NC}"
    echo -e "${BLUE}   查看日志: ${CYAN}tail -f $LOG_DIR/*.log${NC}"
    echo -e "${BLUE}   实时日志: ${CYAN}$0 -l${NC}"
    echo -e "${BLUE}   调试模式: ${CYAN}$0 -d${NC}"
    echo -e "${BLUE}   快速重启: ${CYAN}$0 -q${NC}"
    echo ""

    echo -e "${YELLOW}📁 日志文件:${NC}"
    echo -e "${BLUE}   前端日志: ${CYAN}$FRONTEND_LOG${NC}"
    echo -e "${BLUE}   后端日志: ${CYAN}$BACKEND_LOG${NC}"
    echo -e "${BLUE}   API日志: ${CYAN}$API_LOG${NC}"
    echo -e "${BLUE}   系统日志: ${CYAN}$SYSTEM_LOG${NC}"
    echo ""

    echo -e "${YELLOW}🔧 服务说明:${NC}"
    echo -e "${BLUE}   - 前端支持热重载${NC}"
    echo -e "${BLUE}   - 后端支持自动重启${NC}"
    echo -e "${BLUE}   - 本地MySQL数据库${NC}"
    echo -e "${BLUE}   - 真实SSH服务器监控${NC}"
    echo -e "${BLUE}   - Excel导入导出功能${NC}"
    echo -e "${BLUE}   - 日志实时记录${NC}"
    echo -e "${BLUE}   - 支持调试模式${NC}"
    echo ""

    if [ "$DEBUG_MODE" = true ]; then
        echo -e "${PURPLE}🔍 调试信息:${NC}"
        echo -e "${BLUE}   前端PID: ${CYAN}${FRONTEND_PID:-N/A}${NC}"
        echo -e "${BLUE}   后端PID: ${CYAN}${BACKEND_PID:-N/A}${NC}"
        echo -e "${BLUE}   日志目录: ${CYAN}$LOG_DIR${NC}"
        echo ""
    fi

    echo -e "${GREEN}💡 提示: 按 Ctrl+C 停止所有服务${NC}"
    echo ""
}

# 日志监控函数
monitor_logs() {
    if [ "$SHOW_LOGS" = true ]; then
        log_info "启动日志监控..."
        echo -e "${CYAN}=================================================${NC}"
        echo -e "${YELLOW}📊 实时日志监控 (按 Ctrl+C 停止)${NC}"
        echo -e "${CYAN}=================================================${NC}"

        # 使用tail -f监控所有日志文件
        tail -f $FRONTEND_LOG $BACKEND_LOG $API_LOG $SYSTEM_LOG 2>/dev/null &
        TAIL_PID=$!

        # 添加到清理列表
        echo "$TAIL_PID" >> $LOG_DIR/pids.txt
    fi
}

# 清理函数
cleanup() {
    echo ""
    log_info "正在停止服务..."

    # 读取PID文件
    if [ -f $LOG_DIR/pids.txt ]; then
        PIDS=$(cat $LOG_DIR/pids.txt)
        for pid in $PIDS; do
            if kill -0 $pid 2>/dev/null; then
                log_debug "停止进程 $pid"
                kill $pid 2>/dev/null
            fi
        done
        rm -f $LOG_DIR/pids.txt
    fi

    # 本地环境无需停止数据库服务（MySQL服务保持运行）

    log_success "服务已停止"
    exit 0
}

# 设置信号处理
trap cleanup INT TERM

# 显示启动信息
show_startup_info

# 启动日志监控
monitor_logs

# 保持脚本运行
log_info "服务运行中... (按 Ctrl+C 停止)"
wait
