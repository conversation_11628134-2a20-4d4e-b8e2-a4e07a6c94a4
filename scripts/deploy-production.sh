#!/bin/bash

# SiteManager 生产环境部署脚本
# 版本: 2.4.0
# 作者: SiteManager Team
# 描述: 自动化生产环境部署，包含环境检测、依赖安装、服务配置等功能

set -e

# 脚本版本和配置
SCRIPT_VERSION="2.4.0"
PROJECT_NAME="sitemanager"
SCRIPT_URL="https://raw.githubusercontent.com/your-repo/sitemanager/main/deploy-production.sh"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "$DEBUG_MODE" = true ]; then
        echo -e "${PURPLE}[DEBUG]${NC} $1"
    fi
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# 显示横幅
show_banner() {
    echo -e "${CYAN}"
    echo "=================================================================="
    echo "    🚀 SiteManager 生产环境部署脚本 v${SCRIPT_VERSION}"
    echo "    📋 企业级网站管理系统自动化部署工具"
    echo "=================================================================="
    echo -e "${NC}"
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -v, --version           显示脚本版本"
    echo "  -d, --debug             启用调试模式"
    echo "  -f, --force             强制重新安装"
    echo "  -u, --update            检查并更新脚本到最新版本"
    echo "  --check-only            仅检查环境，不执行安装"
    echo "  --skip-deps             跳过依赖安装"
    echo "  --skip-db               跳过数据库初始化"
    echo "  --mode=MODE             部署模式: standalone|cluster|docker"
    echo "  --domain=DOMAIN         设置域名"
    echo "  --ssl                   启用SSL证书"
    echo "  --nginx                 配置Nginx反向代理"
    echo "  --pm2                   使用PM2管理进程"
    echo "  --systemd               使用systemd管理服务"
    echo ""
    echo "部署模式:"
    echo "  standalone              单机部署（默认）"
    echo "  cluster                 集群部署"
    echo "  docker                  Docker容器部署"
    echo ""
    echo "示例:"
    echo "  $0                                    # 默认部署"
    echo "  $0 --mode=cluster --nginx --ssl      # 集群部署+Nginx+SSL"
    echo "  $0 --domain=example.com --pm2        # 指定域名+PM2管理"
    echo "  $0 --check-only                      # 仅检查环境"
    echo "  $0 --update                          # 检查并更新脚本"
    echo ""
    echo "注意:"
    echo "  - 脚本默认不检查更新，使用 --update 参数主动更新"
    echo "  - 如需启用自动更新，请在脚本中配置正确的 SCRIPT_URL"
}

# 解析命令行参数
parse_arguments() {
    # 默认配置
    DEBUG_MODE=false
    FORCE_INSTALL=false
    CHECK_ONLY=false
    SKIP_DEPS=false
    SKIP_DB=false
    DEPLOY_MODE="standalone"
    DOMAIN=""
    ENABLE_SSL=false
    ENABLE_NGINX=false
    USE_PM2=false
    USE_SYSTEMD=false
    UPDATE_SCRIPT=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--version)
                echo "SiteManager 部署脚本版本: $SCRIPT_VERSION"
                exit 0
                ;;
            -d|--debug)
                DEBUG_MODE=true
                shift
                ;;
            -f|--force)
                FORCE_INSTALL=true
                shift
                ;;
            -u|--update)
                UPDATE_SCRIPT=true
                shift
                ;;
            --check-only)
                CHECK_ONLY=true
                shift
                ;;
            --skip-deps)
                SKIP_DEPS=true
                shift
                ;;
            --skip-db)
                SKIP_DB=true
                shift
                ;;
            --mode=*)
                DEPLOY_MODE="${1#*=}"
                shift
                ;;
            --domain=*)
                DOMAIN="${1#*=}"
                shift
                ;;
            --ssl)
                ENABLE_SSL=true
                shift
                ;;
            --nginx)
                ENABLE_NGINX=true
                shift
                ;;
            --pm2)
                USE_PM2=true
                shift
                ;;
            --systemd)
                USE_SYSTEMD=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 验证部署模式
    if [[ ! "$DEPLOY_MODE" =~ ^(standalone|cluster|docker)$ ]]; then
        log_error "无效的部署模式: $DEPLOY_MODE"
        exit 1
    fi

    # 调试模式设置
    if [ "$DEBUG_MODE" = true ]; then
        set -x
        log_debug "调试模式已启用"
    fi
}

# 脚本自动更新功能
update_script() {
    # 如果明确要求更新，才执行更新检查
    if [ "$UPDATE_SCRIPT" != true ]; then
        log_info "跳过脚本更新检查（使用 --update 参数启用）"
        return 0
    fi

    log_step "检查脚本更新..."

    if ! command -v curl &> /dev/null && ! command -v wget &> /dev/null; then
        log_warning "curl 或 wget 未安装，无法检查更新"
        return 0
    fi

    # 检查是否配置了更新URL
    if [ -z "$SCRIPT_URL" ] || [ "$SCRIPT_URL" = "https://raw.githubusercontent.com/your-repo/sitemanager/main/deploy-production.sh" ]; then
        log_info "未配置脚本更新URL，跳过更新检查"
        log_info "如需启用自动更新，请在脚本中设置正确的 SCRIPT_URL"
        return 0
    fi

    # 下载最新版本
    local temp_script="/tmp/deploy-production-new.sh"

    log_info "从 $SCRIPT_URL 下载最新版本..."

    if command -v curl &> /dev/null; then
        curl -fsSL "$SCRIPT_URL" -o "$temp_script" 2>/dev/null || {
            log_warning "无法下载最新脚本，继续使用当前版本"
            return 0
        }
    elif command -v wget &> /dev/null; then
        wget -q "$SCRIPT_URL" -O "$temp_script" 2>/dev/null || {
            log_warning "无法下载最新脚本，继续使用当前版本"
            return 0
        }
    fi

    # 检查下载的脚本是否有效
    if [ ! -f "$temp_script" ] || [ ! -s "$temp_script" ]; then
        log_warning "下载的脚本无效，继续使用当前版本"
        return 0
    fi

    # 提取新版本号
    local new_version=$(grep '^SCRIPT_VERSION=' "$temp_script" | cut -d'"' -f2)

    if [ -z "$new_version" ]; then
        log_warning "无法获取新版本号，继续使用当前版本"
        rm -f "$temp_script"
        return 0
    fi

    # 比较版本
    if [ "$new_version" != "$SCRIPT_VERSION" ]; then
        log_info "发现新版本: $new_version (当前: $SCRIPT_VERSION)"
        log_step "更新脚本到版本 $new_version..."
        chmod +x "$temp_script"
        cp "$temp_script" "$0"
        rm -f "$temp_script"
        log_success "脚本已更新到版本 $new_version"
        log_info "请重新运行脚本以使用新版本"
        exit 0
    else
        log_success "脚本已是最新版本"
    fi

    rm -f "$temp_script"
}

# 检测操作系统
detect_os() {
    log_step "检测操作系统..."
    
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        OS_VERSION=$VERSION_ID
        OS_CODENAME=${VERSION_CODENAME:-}
    elif type lsb_release >/dev/null 2>&1; then
        OS=$(lsb_release -si)
        OS_VERSION=$(lsb_release -sr)
        OS_CODENAME=$(lsb_release -sc)
    elif [ -f /etc/redhat-release ]; then
        OS="Red Hat Enterprise Linux"
        OS_VERSION=$(cat /etc/redhat-release | grep -oE '[0-9]+\.[0-9]+')
    else
        OS=$(uname -s)
        OS_VERSION=$(uname -r)
    fi

    log_info "操作系统: $OS $OS_VERSION"
    
    # 设置包管理器
    if command -v apt-get &> /dev/null; then
        PKG_MANAGER="apt"
        PKG_UPDATE="apt-get update"
        PKG_INSTALL="apt-get install -y"
    elif command -v yum &> /dev/null; then
        PKG_MANAGER="yum"
        PKG_UPDATE="yum update -y"
        PKG_INSTALL="yum install -y"
    elif command -v dnf &> /dev/null; then
        PKG_MANAGER="dnf"
        PKG_UPDATE="dnf update -y"
        PKG_INSTALL="dnf install -y"
    elif command -v pacman &> /dev/null; then
        PKG_MANAGER="pacman"
        PKG_UPDATE="pacman -Sy"
        PKG_INSTALL="pacman -S --noconfirm"
    else
        log_error "不支持的包管理器，请手动安装依赖"
        exit 1
    fi
    
    log_success "包管理器: $PKG_MANAGER"
}

# 检查系统要求
check_system_requirements() {
    log_step "检查系统要求..."
    
    # 检查内存
    local total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    if [ "$total_mem" -lt 1024 ]; then
        log_warning "系统内存不足 1GB，建议至少 2GB"
    else
        log_success "内存检查通过: ${total_mem}MB"
    fi
    
    # 检查磁盘空间
    local available_space=$(df / | awk 'NR==2 {print $4}')
    local available_gb=$((available_space / 1024 / 1024))
    if [ "$available_gb" -lt 5 ]; then
        log_warning "可用磁盘空间不足 5GB，建议至少 10GB"
    else
        log_success "磁盘空间检查通过: ${available_gb}GB 可用"
    fi
    
    # 检查网络连接
    if ping -c 1 8.8.8.8 &> /dev/null; then
        log_success "网络连接正常"
    else
        log_warning "网络连接异常，可能影响依赖下载"
    fi
}

# 智能sudo函数 - 根据当前用户决定是否使用sudo
smart_sudo() {
    if [ "$EUID" -eq 0 ]; then
        # 如果是root用户，直接执行命令
        "$@"
    else
        # 如果不是root用户，使用sudo
        smart_sudo "$@"
    fi
}

# 智能sudo用户函数 - 处理 -u 参数的sudo命令
smart_sudo_user() {
    local target_user="$1"
    shift

    if [ "$EUID" -eq 0 ]; then
        # 如果是root用户，直接执行命令然后修改所有者
        "$@"
        # 如果是文件操作，修改所有者
        if [[ "$1" =~ ^(cp|mv|mkdir|touch|tee) ]]; then
            # 获取最后一个参数作为目标路径
            local target_path="${@: -1}"
            if [ -e "$target_path" ]; then
                chown -R "$target_user:$target_user" "$target_path" 2>/dev/null || true
            fi
        fi
    else
        # 如果不是root用户，使用sudo -u
        sudo -u "$target_user" "$@"
    fi
}

# 安装系统依赖
install_system_dependencies() {
    if [ "$SKIP_DEPS" = true ]; then
        log_info "跳过系统依赖安装"
        return 0
    fi

    log_step "安装系统依赖..."

    # 更新包管理器
    log_info "更新包管理器..."
    smart_sudo $PKG_UPDATE

    # 基础依赖包
    local base_packages="curl wget git unzip build-essential"

    # 根据包管理器调整包名
    case $PKG_MANAGER in
        "apt")
            base_packages="$base_packages software-properties-common apt-transport-https ca-certificates gnupg lsb-release"
            ;;
        "yum"|"dnf")
            base_packages="$base_packages epel-release gcc gcc-c++ make"
            ;;
        "pacman")
            base_packages="$base_packages base-devel"
            ;;
    esac

    log_info "安装基础依赖: $base_packages"
    smart_sudo $PKG_INSTALL $base_packages

    log_success "系统依赖安装完成"
}

# 安装 Node.js
install_nodejs() {
    log_step "检查和安装 Node.js..."

    local required_version=18
    local current_version=""

    if command -v node &> /dev/null; then
        current_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$current_version" -ge "$required_version" ]; then
            log_success "Node.js 已安装: $(node -v)"
            return 0
        else
            log_warning "Node.js 版本过低: v$current_version，需要 v$required_version+"
        fi
    fi

    log_info "安装 Node.js $required_version..."

    # 使用 NodeSource 仓库安装
    if [ "$PKG_MANAGER" = "apt" ]; then
        curl -fsSL https://deb.nodesource.com/setup_18.x | smart_sudo bash -
        smart_sudo apt-get install -y nodejs
    elif [ "$PKG_MANAGER" = "yum" ] || [ "$PKG_MANAGER" = "dnf" ]; then
        curl -fsSL https://rpm.nodesource.com/setup_18.x | smart_sudo bash -
        smart_sudo $PKG_INSTALL nodejs npm
    else
        # 使用 NVM 安装
        log_info "使用 NVM 安装 Node.js..."
        curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
        export NVM_DIR="$HOME/.nvm"
        [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
        nvm install 18
        nvm use 18
        nvm alias default 18
    fi

    # 验证安装
    if command -v node &> /dev/null && command -v npm &> /dev/null; then
        log_success "Node.js 安装成功: $(node -v)"
        log_success "npm 版本: $(npm -v)"
    else
        log_error "Node.js 安装失败"
        exit 1
    fi
}

# 安装 MySQL
install_mysql() {
    log_step "检查和安装 MySQL..."

    if command -v mysql &> /dev/null; then
        log_success "MySQL 已安装: $(mysql --version)"
        return 0
    fi

    log_info "安装 MySQL 服务器..."

    case $PKG_MANAGER in
        "apt")
            sudo $PKG_INSTALL mysql-server mysql-client
            ;;
        "yum"|"dnf")
            sudo $PKG_INSTALL mysql-server mysql
            ;;
        "pacman")
            sudo $PKG_INSTALL mysql
            ;;
    esac

    # 启动 MySQL 服务
    if command -v systemctl &> /dev/null; then
        smart_sudo systemctl start mysql || smart_sudo systemctl start mysqld
        smart_sudo systemctl enable mysql || smart_sudo systemctl enable mysqld
    else
        smart_sudo service mysql start || smart_sudo service mysqld start
    fi

    # 等待 MySQL 启动
    sleep 5

    log_success "MySQL 安装完成"
}

# 安装 PM2 (如果需要)
install_pm2() {
    if [ "$USE_PM2" != true ]; then
        return 0
    fi

    log_step "安装 PM2 进程管理器..."

    if command -v pm2 &> /dev/null; then
        log_success "PM2 已安装: $(pm2 --version)"
        return 0
    fi

    npm install -g pm2

    # 设置 PM2 开机启动
    pm2 startup

    log_success "PM2 安装完成"
}

# 安装 Nginx (如果需要)
install_nginx() {
    if [ "$ENABLE_NGINX" != true ]; then
        return 0
    fi

    log_step "安装 Nginx..."

    if command -v nginx &> /dev/null; then
        log_success "Nginx 已安装: $(nginx -v 2>&1)"
        return 0
    fi

    sudo $PKG_INSTALL nginx

    # 启动 Nginx 服务
    if command -v systemctl &> /dev/null; then
        smart_sudo systemctl start nginx
        smart_sudo systemctl enable nginx
    else
        smart_sudo service nginx start
    fi

    log_success "Nginx 安装完成"
}

# 创建项目目录和用户
setup_project_environment() {
    log_step "设置项目环境..."

    # 创建项目用户
    local project_user="sitemanager"
    if ! id "$project_user" &>/dev/null; then
        log_info "创建项目用户: $project_user"
        smart_sudo useradd -r -s /bin/bash -d /opt/$PROJECT_NAME $project_user
    fi

    # 创建项目目录
    local project_dir="/opt/$PROJECT_NAME"
    if [ ! -d "$project_dir" ]; then
        log_info "创建项目目录: $project_dir"
        smart_sudo mkdir -p "$project_dir"
        smart_sudo chown $project_user:$project_user "$project_dir"
    fi

    # 创建日志目录
    local log_dir="/var/log/$PROJECT_NAME"
    if [ ! -d "$log_dir" ]; then
        log_info "创建日志目录: $log_dir"
        smart_sudo mkdir -p "$log_dir"
        smart_sudo chown $project_user:$project_user "$log_dir"
    fi

    # 创建数据目录
    local data_dir="/var/lib/$PROJECT_NAME"
    if [ ! -d "$data_dir" ]; then
        log_info "创建数据目录: $data_dir"
        smart_sudo mkdir -p "$data_dir"
        smart_sudo chown $project_user:$project_user "$data_dir"
    fi

    log_success "项目环境设置完成"
}

# 下载和部署项目代码
deploy_project_code() {
    log_step "部署项目代码..."

    local project_dir="/opt/$PROJECT_NAME"
    local backup_dir="/opt/$PROJECT_NAME-backup-$(date +%Y%m%d-%H%M%S)"

    # 如果项目已存在，创建备份
    if [ -d "$project_dir" ] && [ "$(ls -A $project_dir)" ]; then
        if [ "$FORCE_INSTALL" = true ]; then
            log_info "强制安装模式，备份现有项目到: $backup_dir"
            smart_sudo mv "$project_dir" "$backup_dir"
            smart_sudo mkdir -p "$project_dir"
            smart_sudo chown sitemanager:sitemanager "$project_dir"
        else
            log_info "项目目录已存在，跳过代码部署"
            return 0
        fi
    fi

    # 切换到项目目录
    cd "$project_dir"

    # 如果是 Git 仓库，拉取最新代码
    if [ -d ".git" ]; then
        log_info "更新 Git 仓库..."
        smart_sudo_user sitemanager git pull origin main
    else
        # 这里可以添加从 Git 仓库克隆代码的逻辑
        # 或者从当前目录复制代码
        if [ -f "$OLDPWD/package.json" ]; then
            log_info "从当前目录复制项目文件..."
            smart_sudo_user sitemanager cp -r "$OLDPWD"/* .
            smart_sudo_user sitemanager cp -r "$OLDPWD"/.* . 2>/dev/null || true
        else
            log_warning "未找到项目源码，请手动部署代码到 $project_dir"
        fi
    fi

    log_success "项目代码部署完成"
}

# 安装项目依赖
install_project_dependencies() {
    if [ "$SKIP_DEPS" = true ]; then
        log_info "跳过项目依赖安装"
        return 0
    fi

    log_step "安装项目依赖..."

    local project_dir="/opt/$PROJECT_NAME"
    cd "$project_dir"

    # 安装后端依赖
    if [ -f "backend/package.json" ]; then
        log_info "安装后端依赖..."
        cd backend
        smart_sudo_user sitemanager npm ci --production
        cd ..
    fi

    # 安装前端依赖并构建
    if [ -f "frontend/package.json" ]; then
        log_info "安装前端依赖..."
        cd frontend
        smart_sudo_user sitemanager npm ci
        log_info "构建前端应用..."
        smart_sudo_user sitemanager npm run build
        cd ..
    fi

    # 安装根目录依赖
    if [ -f "package.json" ]; then
        log_info "安装根目录依赖..."
        smart_sudo_user sitemanager npm ci --production
    fi

    log_success "项目依赖安装完成"
}

# 配置数据库
setup_database() {
    if [ "$SKIP_DB" = true ]; then
        log_info "跳过数据库配置"
        return 0
    fi

    log_step "配置数据库..."

    local db_name="sitemanager"
    local db_user="sitemanager"
    local db_password=$(openssl rand -base64 32)
    local root_password="root123"

    # 检查 MySQL 是否运行
    if ! systemctl is-active --quiet mysql 2>/dev/null && ! systemctl is-active --quiet mysqld 2>/dev/null; then
        log_error "MySQL 服务未运行"
        exit 1
    fi

    # 设置 root 密码（如果需要）
    if ! mysql -u root -p"$root_password" -e "SELECT 1;" &>/dev/null; then
        log_info "设置 MySQL root 密码..."
        mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$root_password';" 2>/dev/null || {
            # 如果上面的命令失败，尝试其他方法
            mysqladmin -u root password "$root_password" 2>/dev/null || true
        }
    fi

    # 创建数据库和用户
    log_info "创建数据库和用户..."
    mysql -u root -p"$root_password" <<EOF
CREATE DATABASE IF NOT EXISTS $db_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS '$db_user'@'localhost' IDENTIFIED BY '$db_password';
GRANT ALL PRIVILEGES ON $db_name.* TO '$db_user'@'localhost';
FLUSH PRIVILEGES;
EOF

    # 导入数据库结构
    local project_dir="/opt/$PROJECT_NAME"
    if [ -f "$project_dir/database/init.sql" ]; then
        log_info "导入数据库结构..."
        mysql -u root -p"$root_password" "$db_name" < "$project_dir/database/init.sql"
    fi

    # 保存数据库配置
    local config_file="/opt/$PROJECT_NAME/.env.production"
    cat > "$config_file" <<EOF
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=$db_name
DB_USER=$db_user
DB_PASSWORD=$db_password

# 应用配置
NODE_ENV=production
PORT=3001
FRONTEND_PORT=3000

# 安全配置
JWT_SECRET=$(openssl rand -base64 64)
ENCRYPTION_KEY=$(openssl rand -base64 32)
EOF

    smart_sudo chown sitemanager:sitemanager "$config_file"
    smart_sudo chmod 600 "$config_file"

    log_success "数据库配置完成"
    log_info "数据库配置已保存到: $config_file"
}

# 配置 Nginx 反向代理
setup_nginx_config() {
    if [ "$ENABLE_NGINX" != true ]; then
        return 0
    fi

    log_step "配置 Nginx 反向代理..."

    local domain=${DOMAIN:-"localhost"}
    local config_file="/etc/nginx/sites-available/$PROJECT_NAME"
    local ssl_config=""

    # SSL 配置
    if [ "$ENABLE_SSL" = true ]; then
        ssl_config="
    listen 443 ssl http2;
    ssl_certificate /etc/ssl/certs/$domain.crt;
    ssl_certificate_key /etc/ssl/private/$domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # HTTP 重定向到 HTTPS
    if (\$scheme != \"https\") {
        return 301 https://\$server_name\$request_uri;
    }"
    fi

    # 创建 Nginx 配置文件
    smart_sudo tee "$config_file" > /dev/null <<EOF
server {
    listen 80;
    server_name $domain;
    $ssl_config

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 前端静态文件
    location / {
        root /opt/$PROJECT_NAME/frontend/dist;
        try_files \$uri \$uri/ /index.html;

        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API 代理
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 上传文件代理
    location /uploads/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # SSL 检查器
    location /ssl-checker/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

    # 启用站点
    smart_sudo ln -sf "$config_file" "/etc/nginx/sites-enabled/"

    # 删除默认站点
    smart_sudo rm -f /etc/nginx/sites-enabled/default

    # 测试配置
    if smart_sudo nginx -t; then
        smart_sudo systemctl reload nginx
        log_success "Nginx 配置完成"
    else
        log_error "Nginx 配置错误"
        exit 1
    fi
}

# 配置 SSL 证书
setup_ssl_certificate() {
    if [ "$ENABLE_SSL" != true ]; then
        return 0
    fi

    log_step "配置 SSL 证书..."

    local domain=${DOMAIN:-"localhost"}
    local cert_dir="/etc/ssl/certs"
    local key_dir="/etc/ssl/private"

    # 创建自签名证书（生产环境建议使用 Let's Encrypt）
    if [ ! -f "$cert_dir/$domain.crt" ]; then
        log_info "生成自签名 SSL 证书..."
        smart_sudo mkdir -p "$cert_dir" "$key_dir"

        smart_sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$key_dir/$domain.key" \
            -out "$cert_dir/$domain.crt" \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=$domain"

        smart_sudo chmod 600 "$key_dir/$domain.key"
        smart_sudo chmod 644 "$cert_dir/$domain.crt"

        log_success "SSL 证书生成完成"
        log_warning "生产环境建议使用 Let's Encrypt 证书"
    else
        log_success "SSL 证书已存在"
    fi
}

# 配置服务管理
setup_service_management() {
    log_step "配置服务管理..."

    local project_dir="/opt/$PROJECT_NAME"

    if [ "$USE_PM2" = true ]; then
        setup_pm2_service
    elif [ "$USE_SYSTEMD" = true ]; then
        setup_systemd_service
    else
        # 默认使用 systemd
        setup_systemd_service
    fi
}

# 配置 PM2 服务
setup_pm2_service() {
    log_info "配置 PM2 服务..."

    local project_dir="/opt/$PROJECT_NAME"
    local ecosystem_file="$project_dir/ecosystem.config.js"

    # 创建 PM2 配置文件
    smart_sudo_user sitemanager tee "$ecosystem_file" > /dev/null <<EOF
module.exports = {
  apps: [
    {
      name: 'sitemanager-backend',
      script: './backend/simple-server.js',
      cwd: '$project_dir',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      instances: 1,
      exec_mode: 'fork',
      watch: false,
      max_memory_restart: '1G',
      error_file: '/var/log/$PROJECT_NAME/backend-error.log',
      out_file: '/var/log/$PROJECT_NAME/backend-out.log',
      log_file: '/var/log/$PROJECT_NAME/backend.log',
      time: true
    }
  ]
};
EOF

    # 启动应用
    smart_sudo_user sitemanager pm2 start "$ecosystem_file"
    smart_sudo_user sitemanager pm2 save

    log_success "PM2 服务配置完成"
}

# 配置 systemd 服务
setup_systemd_service() {
    log_info "配置 systemd 服务..."

    local service_file="/etc/systemd/system/$PROJECT_NAME.service"

    # 创建 systemd 服务文件
    smart_sudo tee "$service_file" > /dev/null <<EOF
[Unit]
Description=SiteManager Backend Service
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=sitemanager
Group=sitemanager
WorkingDirectory=/opt/$PROJECT_NAME
Environment=NODE_ENV=production
Environment=PORT=3001
EnvironmentFile=/opt/$PROJECT_NAME/.env.production
ExecStart=/usr/bin/node backend/simple-server.js
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$PROJECT_NAME

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/$PROJECT_NAME /var/log/$PROJECT_NAME /var/lib/$PROJECT_NAME

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载 systemd 并启动服务
    smart_sudo systemctl daemon-reload
    smart_sudo systemctl enable "$PROJECT_NAME"
    smart_sudo systemctl start "$PROJECT_NAME"

    log_success "systemd 服务配置完成"
}

# 配置日志轮转
setup_log_rotation() {
    log_step "配置日志轮转..."

    local logrotate_file="/etc/logrotate.d/$PROJECT_NAME"

    smart_sudo tee "$logrotate_file" > /dev/null <<EOF
/var/log/$PROJECT_NAME/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 sitemanager sitemanager
    postrotate
        systemctl reload $PROJECT_NAME || true
    endscript
}
EOF

    log_success "日志轮转配置完成"
}

# 配置防火墙
setup_firewall() {
    log_step "配置防火墙..."

    # 检查防火墙类型
    if command -v ufw &> /dev/null; then
        # Ubuntu/Debian UFW
        smart_sudo ufw allow ssh
        smart_sudo ufw allow 80/tcp
        if [ "$ENABLE_SSL" = true ]; then
            smart_sudo ufw allow 443/tcp
        fi
        smart_sudo ufw --force enable
        log_success "UFW 防火墙配置完成"
    elif command -v firewall-cmd &> /dev/null; then
        # CentOS/RHEL firewalld
        smart_sudo firewall-cmd --permanent --add-service=ssh
        smart_sudo firewall-cmd --permanent --add-service=http
        if [ "$ENABLE_SSL" = true ]; then
            smart_sudo firewall-cmd --permanent --add-service=https
        fi
        smart_sudo firewall-cmd --reload
        log_success "firewalld 防火墙配置完成"
    else
        log_warning "未检测到防火墙，请手动配置端口访问"
    fi
}

# 运行部署后检查
post_deployment_check() {
    log_step "运行部署后检查..."

    # 检查服务状态
    if [ "$USE_PM2" = true ]; then
        if smart_sudo_user sitemanager pm2 list | grep -q "online"; then
            log_success "PM2 服务运行正常"
        else
            log_error "PM2 服务异常"
        fi
    else
        if systemctl is-active --quiet "$PROJECT_NAME"; then
            log_success "systemd 服务运行正常"
        else
            log_error "systemd 服务异常"
        fi
    fi

    # 检查端口监听
    if netstat -tlnp | grep -q ":3001"; then
        log_success "后端服务端口 3001 监听正常"
    else
        log_warning "后端服务端口 3001 未监听"
    fi

    # 检查 Nginx
    if [ "$ENABLE_NGINX" = true ]; then
        if systemctl is-active --quiet nginx; then
            log_success "Nginx 服务运行正常"
        else
            log_error "Nginx 服务异常"
        fi
    fi

    # 检查数据库连接
    local project_dir="/opt/$PROJECT_NAME"
    if [ -f "$project_dir/.env.production" ]; then
        source "$project_dir/.env.production"
        if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &>/dev/null; then
            log_success "数据库连接正常"
        else
            log_error "数据库连接失败"
        fi
    fi
}

# 显示部署结果
show_deployment_result() {
    echo ""
    echo -e "${GREEN}🎉 SiteManager 生产环境部署完成！${NC}"
    echo -e "${CYAN}=================================================================${NC}"
    echo ""

    local domain=${DOMAIN:-"localhost"}
    local protocol="http"
    if [ "$ENABLE_SSL" = true ]; then
        protocol="https"
    fi

    echo -e "${BLUE}📱 访问地址:${NC}"
    if [ "$ENABLE_NGINX" = true ]; then
        echo -e "${BLUE}   前端应用: ${CYAN}$protocol://$domain${NC}"
        echo -e "${BLUE}   后端API: ${CYAN}$protocol://$domain/api${NC}"
        echo -e "${BLUE}   SSL检查器: ${CYAN}$protocol://$domain/ssl-checker/web/${NC}"
    else
        echo -e "${BLUE}   前端应用: ${CYAN}http://$domain:3000${NC}"
        echo -e "${BLUE}   后端API: ${CYAN}http://$domain:3001${NC}"
        echo -e "${BLUE}   SSL检查器: ${CYAN}http://$domain:3001/ssl-checker/web/${NC}"
    fi

    echo ""
    echo -e "${BLUE}🔧 服务管理:${NC}"
    if [ "$USE_PM2" = true ]; then
        echo -e "${BLUE}   查看状态: ${CYAN}smart_sudo_user sitemanager pm2 status${NC}"
        echo -e "${BLUE}   查看日志: ${CYAN}smart_sudo_user sitemanager pm2 logs${NC}"
        echo -e "${BLUE}   重启服务: ${CYAN}smart_sudo_user sitemanager pm2 restart all${NC}"
    else
        echo -e "${BLUE}   查看状态: ${CYAN}smart_sudo systemctl status $PROJECT_NAME${NC}"
        echo -e "${BLUE}   查看日志: ${CYAN}smart_sudo journalctl -u $PROJECT_NAME -f${NC}"
        echo -e "${BLUE}   重启服务: ${CYAN}smart_sudo systemctl restart $PROJECT_NAME${NC}"
    fi

    echo ""
    echo -e "${BLUE}📁 重要路径:${NC}"
    echo -e "${BLUE}   项目目录: ${CYAN}/opt/$PROJECT_NAME${NC}"
    echo -e "${BLUE}   日志目录: ${CYAN}/var/log/$PROJECT_NAME${NC}"
    echo -e "${BLUE}   配置文件: ${CYAN}/opt/$PROJECT_NAME/.env.production${NC}"

    if [ "$ENABLE_NGINX" = true ]; then
        echo -e "${BLUE}   Nginx配置: ${CYAN}/etc/nginx/sites-available/$PROJECT_NAME${NC}"
    fi

    echo ""
    echo -e "${BLUE}🗄️ 数据库信息:${NC}"
    echo -e "${BLUE}   数据库名: ${CYAN}sitemanager${NC}"
    echo -e "${BLUE}   用户名: ${CYAN}sitemanager${NC}"
    echo -e "${BLUE}   配置文件: ${CYAN}/opt/$PROJECT_NAME/.env.production${NC}"

    echo ""
    echo -e "${YELLOW}📋 后续操作建议:${NC}"
    echo -e "${BLUE}   1. 配置域名DNS解析指向服务器IP${NC}"
    if [ "$ENABLE_SSL" = true ]; then
        echo -e "${BLUE}   2. 申请正式SSL证书（推荐Let's Encrypt）${NC}"
    fi
    echo -e "${BLUE}   3. 配置数据库备份策略${NC}"
    echo -e "${BLUE}   4. 设置监控告警${NC}"
    echo -e "${BLUE}   5. 定期更新系统和依赖${NC}"

    echo ""
    echo -e "${GREEN}✅ 部署完成，系统已就绪！${NC}"
    echo ""
}

# 主函数
main() {
    # 显示横幅
    show_banner

    # 解析命令行参数
    parse_arguments "$@"

    # 检查脚本更新（仅在明确要求时执行）
    update_script

    # 检测操作系统
    detect_os

    # 检查系统要求
    check_system_requirements

    # 如果只是检查环境，到此结束
    if [ "$CHECK_ONLY" = true ]; then
        log_success "环境检查完成"
        exit 0
    fi

    # 安装系统依赖
    install_system_dependencies

    # 安装 Node.js
    install_nodejs

    # 安装 MySQL
    install_mysql

    # 安装 PM2 (如果需要)
    install_pm2

    # 安装 Nginx (如果需要)
    install_nginx

    # 设置项目环境
    setup_project_environment

    # 部署项目代码
    deploy_project_code

    # 安装项目依赖
    install_project_dependencies

    # 配置数据库
    setup_database

    # 配置 SSL 证书
    setup_ssl_certificate

    # 配置 Nginx
    setup_nginx_config

    # 配置服务管理
    setup_service_management

    # 配置日志轮转
    setup_log_rotation

    # 配置防火墙
    setup_firewall

    # 运行部署后检查
    post_deployment_check

    # 显示部署结果
    show_deployment_result
}

# 错误处理
handle_error() {
    local exit_code=$?
    local line_number=$1

    echo ""
    log_error "脚本在第 $line_number 行执行失败，退出码: $exit_code"
    log_error "请检查错误信息并重新运行脚本"

    # 如果是部分部署失败，提供恢复建议
    if [ -d "/opt/$PROJECT_NAME" ]; then
        echo ""
        log_info "部分部署可能已完成，可以尝试："
        log_info "1. 检查具体错误原因"
        log_info "2. 使用 --force 参数重新部署"
        log_info "3. 手动完成剩余配置"
    fi

    exit $exit_code
}

# 清理函数
cleanup() {
    log_info "正在清理临时文件..."
    # 这里可以添加清理逻辑
}

# 设置信号处理
trap 'handle_error $LINENO' ERR
trap cleanup EXIT

# 检查运行权限
check_root() {
    if [ "$EUID" -eq 0 ]; then
        log_info "检测到以 root 用户运行，将直接执行系统命令"
    else
        # 检查 smart_sudo 权限
        if ! smart_sudo -n true 2>/dev/null; then
            log_info "此脚本需要 smart_sudo 权限来安装系统依赖"
            log_info "请输入密码以继续..."
            smart_sudo -v
        fi
        log_info "将使用 smart_sudo 执行需要管理员权限的命令"
    fi
}

# 脚本入口点
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    # 如果是帮助或版本命令，直接执行不检查权限
    if [[ "$1" == "-h" || "$1" == "--help" || "$1" == "-v" || "$1" == "--version" ]]; then
        main "$@"
    else
        # 检查 root 权限
        check_root

        # 运行主函数
        main "$@"
    fi
fi
