#!/bin/bash

# WordPress站点管理系统日志查看器

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

LOG_DIR="./logs"

# 显示帮助信息
show_help() {
    echo -e "${CYAN}WordPress站点管理系统日志查看器${NC}"
    echo "=================================="
    echo ""
    echo "用法: $0 [选项] [日志类型]"
    echo ""
    echo "日志类型:"
    echo "  frontend    前端日志"
    echo "  backend     后端日志"
    echo "  api         API日志"
    echo "  system      系统日志"
    echo "  all         所有日志"
    echo ""
    echo "选项:"
    echo "  -f, --follow    实时跟踪日志"
    echo "  -n, --lines N   显示最后N行 (默认50)"
    echo "  -c, --clear     清空日志文件"
    echo "  -s, --status    显示服务状态"
    echo "  -h, --help      显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 frontend           # 查看前端日志"
    echo "  $0 -f api            # 实时跟踪API日志"
    echo "  $0 -n 100 backend    # 查看后端最后100行日志"
    echo "  $0 -c all            # 清空所有日志"
    echo "  $0 -s                # 显示服务状态"
}

# 检查日志目录
check_log_dir() {
    if [ ! -d "$LOG_DIR" ]; then
        echo -e "${YELLOW}⚠️  日志目录不存在，创建中...${NC}"
        mkdir -p "$LOG_DIR"
    fi
}

# 显示服务状态
show_status() {
    echo -e "${CYAN}🔍 服务状态检查${NC}"
    echo "===================="
    
    # 检查端口占用
    check_port() {
        local port=$1
        local service=$2
        if lsof -i :$port &> /dev/null; then
            local pid=$(lsof -ti :$port)
            echo -e "${GREEN}✅ $service${NC} (端口 $port, PID: $pid)"
        else
            echo -e "${RED}❌ $service${NC} (端口 $port 未使用)"
        fi
    }
    
    check_port 3000 "前端服务"
    check_port 3001 "后端API"
    
    # 检查PID文件
    if [ -f "$LOG_DIR/pids.txt" ]; then
        echo ""
        echo -e "${BLUE}📋 运行中的进程:${NC}"
        PIDS=$(cat $LOG_DIR/pids.txt)
        for pid in $PIDS; do
            if kill -0 $pid 2>/dev/null; then
                local cmd=$(ps -p $pid -o comm= 2>/dev/null)
                echo -e "${GREEN}  ✅ PID $pid${NC} ($cmd)"
            else
                echo -e "${RED}  ❌ PID $pid${NC} (已停止)"
            fi
        done
    fi
    
    # 检查日志文件
    echo ""
    echo -e "${BLUE}📁 日志文件状态:${NC}"
    for log_file in frontend.log backend.log api.log system.log; do
        local file_path="$LOG_DIR/$log_file"
        if [ -f "$file_path" ]; then
            local size=$(du -h "$file_path" | cut -f1)
            local lines=$(wc -l < "$file_path")
            echo -e "${GREEN}  ✅ $log_file${NC} (大小: $size, 行数: $lines)"
        else
            echo -e "${YELLOW}  ⚠️  $log_file${NC} (不存在)"
        fi
    done
    
    echo ""
}

# 清空日志文件
clear_logs() {
    local log_type=$1
    
    case $log_type in
        frontend)
            > "$LOG_DIR/frontend.log"
            echo -e "${GREEN}✅ 前端日志已清空${NC}"
            ;;
        backend)
            > "$LOG_DIR/backend.log"
            echo -e "${GREEN}✅ 后端日志已清空${NC}"
            ;;
        api)
            > "$LOG_DIR/api.log"
            echo -e "${GREEN}✅ API日志已清空${NC}"
            ;;
        system)
            > "$LOG_DIR/system.log"
            echo -e "${GREEN}✅ 系统日志已清空${NC}"
            ;;
        all)
            > "$LOG_DIR/frontend.log"
            > "$LOG_DIR/backend.log"
            > "$LOG_DIR/api.log"
            > "$LOG_DIR/system.log"
            echo -e "${GREEN}✅ 所有日志已清空${NC}"
            ;;
        *)
            echo -e "${RED}❌ 未知的日志类型: $log_type${NC}"
            exit 1
            ;;
    esac
}

# 查看日志
view_logs() {
    local log_type=$1
    local follow=$2
    local lines=$3
    
    local log_files=()
    
    case $log_type in
        frontend)
            log_files=("$LOG_DIR/frontend.log")
            echo -e "${BLUE}📱 前端日志${NC}"
            ;;
        backend)
            log_files=("$LOG_DIR/backend.log")
            echo -e "${BLUE}🔧 后端日志${NC}"
            ;;
        api)
            log_files=("$LOG_DIR/api.log")
            echo -e "${BLUE}🌐 API日志${NC}"
            ;;
        system)
            log_files=("$LOG_DIR/system.log")
            echo -e "${BLUE}⚙️  系统日志${NC}"
            ;;
        all)
            log_files=("$LOG_DIR/frontend.log" "$LOG_DIR/backend.log" "$LOG_DIR/api.log" "$LOG_DIR/system.log")
            echo -e "${BLUE}📊 所有日志${NC}"
            ;;
        *)
            echo -e "${RED}❌ 未知的日志类型: $log_type${NC}"
            exit 1
            ;;
    esac
    
    echo "===================="
    
    # 检查日志文件是否存在
    local existing_files=()
    for file in "${log_files[@]}"; do
        if [ -f "$file" ]; then
            existing_files+=("$file")
        fi
    done
    
    if [ ${#existing_files[@]} -eq 0 ]; then
        echo -e "${YELLOW}⚠️  没有找到日志文件${NC}"
        return
    fi
    
    # 显示日志
    if [ "$follow" = true ]; then
        echo -e "${CYAN}🔄 实时跟踪日志 (按 Ctrl+C 停止)${NC}"
        echo ""
        tail -f "${existing_files[@]}" 2>/dev/null
    else
        if [ "$log_type" = "all" ]; then
            for file in "${existing_files[@]}"; do
                local basename=$(basename "$file" .log)
                echo -e "${PURPLE}=== $basename ===${NC}"
                tail -n "$lines" "$file" 2>/dev/null | while IFS= read -r line; do
                    # 简单的日志着色
                    if [[ $line == *"ERROR"* ]] || [[ $line == *"error"* ]]; then
                        echo -e "${RED}$line${NC}"
                    elif [[ $line == *"WARN"* ]] || [[ $line == *"warn"* ]]; then
                        echo -e "${YELLOW}$line${NC}"
                    elif [[ $line == *"INFO"* ]] || [[ $line == *"info"* ]]; then
                        echo -e "${BLUE}$line${NC}"
                    elif [[ $line == *"SUCCESS"* ]] || [[ $line == *"success"* ]]; then
                        echo -e "${GREEN}$line${NC}"
                    else
                        echo "$line"
                    fi
                done
                echo ""
            done
        else
            tail -n "$lines" "${existing_files[0]}" 2>/dev/null | while IFS= read -r line; do
                # 简单的日志着色
                if [[ $line == *"ERROR"* ]] || [[ $line == *"error"* ]]; then
                    echo -e "${RED}$line${NC}"
                elif [[ $line == *"WARN"* ]] || [[ $line == *"warn"* ]]; then
                    echo -e "${YELLOW}$line${NC}"
                elif [[ $line == *"INFO"* ]] || [[ $line == *"info"* ]]; then
                    echo -e "${BLUE}$line${NC}"
                elif [[ $line == *"SUCCESS"* ]] || [[ $line == *"success"* ]]; then
                    echo -e "${GREEN}$line${NC}"
                else
                    echo "$line"
                fi
            done
        fi
    fi
}

# 解析命令行参数
FOLLOW=false
LINES=50
CLEAR=false
STATUS=false
LOG_TYPE=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--follow)
            FOLLOW=true
            shift
            ;;
        -n|--lines)
            LINES="$2"
            shift 2
            ;;
        -c|--clear)
            CLEAR=true
            shift
            ;;
        -s|--status)
            STATUS=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        frontend|backend|api|system|all)
            LOG_TYPE="$1"
            shift
            ;;
        *)
            echo -e "${RED}❌ 未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
done

# 检查日志目录
check_log_dir

# 执行操作
if [ "$STATUS" = true ]; then
    show_status
elif [ "$CLEAR" = true ]; then
    if [ -z "$LOG_TYPE" ]; then
        echo -e "${RED}❌ 清空日志需要指定日志类型${NC}"
        show_help
        exit 1
    fi
    clear_logs "$LOG_TYPE"
else
    if [ -z "$LOG_TYPE" ]; then
        echo -e "${RED}❌ 请指定要查看的日志类型${NC}"
        show_help
        exit 1
    fi
    view_logs "$LOG_TYPE" "$FOLLOW" "$LINES"
fi
