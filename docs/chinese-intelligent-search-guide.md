# 中文精准搜索功能指南

## 功能概述

网站监控系统现已集成中文精准搜索功能，优先精确匹配完整搜索词，然后使用AND逻辑匹配所有关键词，如"深圳工业"会精确匹配"深圳市工业设计行业协会"，提供更精准的搜索体验。

## 核心特性

### 🎯 精准搜索策略
- **优先精确匹配**: 首先尝试匹配完整的搜索词
- **AND逻辑降级**: 如果没有精确匹配，使用AND逻辑匹配所有关键词
- **避免过度扩展**: 仅对地区词汇进行有限扩展，保持搜索精准性

### 🗺️ 地区词汇有限映射
- **主要城市**: 深圳 ↔ 深圳市 ↔ 深
- **直辖市**: 上海 ↔ 上海市 ↔ 沪
- **省会城市**: 广州 ↔ 广州市 ↔ 穗
- **精准控制**: 仅扩展地区词汇，不扩展行业词汇

## 搜索示例

### 精准搜索示例
```
搜索: "深圳工业"
策略1: 优先精确匹配 "深圳工业"
匹配结果:
- ✅ "深圳市工业设计行业协会" (精确匹配)

策略2: 如果没有精确匹配，使用AND逻辑
分词: ["深圳", "工业"]
地区扩展: "深圳" → ["深圳", "深圳市", "深"]
匹配条件: 必须同时包含"深圳"(或其变体)和"工业"
```

### 无结果搜索示例
```
搜索: "深圳银行"
策略1: 尝试精确匹配 "深圳银行" → 无结果
策略2: AND逻辑匹配 "深圳" AND "银行" → 无结果
结果: 空列表 (因为没有同时包含这两个词的网站)
```

### 地区搜索优化
```
搜索: "深圳"
地区扩展: ["深圳", "深圳市", "深"]
匹配结果:
- ✅ "深圳市启崴源电子有限公司"
- ✅ "深圳益世通科技有限公司"
- ✅ "方图智能（深圳）科技集团股份有限公司"
```

## 技术原理

### 分词算法
1. **文本预处理**: 移除标点符号，保留中文、英文、数字
2. **字符分类**: 区分中文字符和英文数字
3. **词汇识别**: 基于词典和规则识别完整词汇
4. **智能切分**: 对长词进行合理切分

### 词汇扩展策略
1. **地区映射**: 基于行政区划的标准映射
2. **行业同义**: 基于行业特点的同义词扩展
3. **停用词过滤**: 过滤无意义的常用词汇
4. **权重计算**: 原始词汇权重高于扩展词汇

### 匹配策略
- **中文搜索**: 使用OR逻辑，匹配任一扩展词汇
- **英文搜索**: 使用AND逻辑，所有关键词都需匹配
- **部分匹配**: 支持字符级别的包含匹配
- **相关性排序**: 基于匹配度和权重排序

## 支持的地区词汇

### 直辖市
- 北京 ↔ 北京市 ↔ 京
- 上海 ↔ 上海市 ↔ 沪
- 天津 ↔ 天津市 ↔ 津
- 重庆 ↔ 重庆市 ↔ 渝

### 主要省份
- 广东 ↔ 广东省 ↔ 粤
- 江苏 ↔ 江苏省 ↔ 苏
- 浙江 ↔ 浙江省 ↔ 浙
- 山东 ↔ 山东省 ↔ 鲁
- 河南 ↔ 河南省 ↔ 豫

### 重点城市
- 深圳 ↔ 深圳市 ↔ 深
- 广州 ↔ 广州市 ↔ 穗
- 杭州 ↔ 杭州市
- 南京 ↔ 南京市 ↔ 宁
- 武汉 ↔ 武汉市
- 成都 ↔ 成都市 ↔ 蓉

## 支持的行业词汇

### 制造业
- **核心词**: 工业、制造业、生产、制造、加工
- **应用场景**: 搜索制造类企业

### 科技业
- **核心词**: 科技、技术、高新、信息、IT、软件、互联网
- **应用场景**: 搜索科技类企业

### 金融业
- **核心词**: 金融、银行、保险、证券、投资、基金
- **应用场景**: 搜索金融类企业

### 贸易业
- **核心词**: 贸易、商贸、进出口、外贸、商务、销售
- **应用场景**: 搜索贸易类企业

### 房地产
- **核心词**: 房地产、地产、置业、建筑、装修、装饰
- **应用场景**: 搜索房地产类企业

## 使用技巧

### 1. 地区搜索
- **推荐**: 使用简短地名，如"深圳"而非"深圳市"
- **效果**: 系统会自动扩展匹配所有相关形式

### 2. 行业搜索
- **推荐**: 使用核心行业词，如"科技"而非"高科技"
- **效果**: 系统会匹配所有相关的行业词汇

### 3. 组合搜索
- **推荐**: 地区+行业组合，如"深圳科技"
- **效果**: 获得精准的地区行业匹配结果

### 4. 避免过长搜索词
- **推荐**: 使用2-4个字的核心词汇
- **避免**: 过长的完整公司名称

## 性能优化

### 自动检测
- 系统自动检测搜索词是否包含中文
- 仅对中文搜索启用分词功能
- 英文搜索保持原有高效性能

### 缓存机制
- 分词结果智能缓存
- 扩展词汇预计算
- 减少重复计算开销

### 索引优化
- 数据库字段索引优化
- 支持部分匹配的高效查询
- 查询性能持续优化

## 常见问题

### Q: 为什么搜索"深圳"会出现"深圳市"的结果？
A: 系统智能扩展地区词汇，"深圳"会自动匹配"深圳市"、"深"等相关形式。

### Q: 搜索结果太多怎么办？
A: 可以使用更具体的组合搜索，如"深圳+科技"缩小范围。

### Q: 英文搜索是否受影响？
A: 不受影响。系统自动检测语言类型，英文搜索使用原有逻辑。

### Q: 如何提高搜索精确度？
A: 使用核心关键词，避免停用词，结合地区和行业词汇。

## 更新日志

### v1.1.0 (2025-07-15)
- ✅ 实现中文智能分词搜索
- ✅ 支持地区词汇智能扩展
- ✅ 添加行业词汇同义词匹配
- ✅ 优化搜索策略和相关性排序
- ✅ 完善中英文混合搜索支持

---

*中文智能搜索让您的数据查找更加便捷高效！*
