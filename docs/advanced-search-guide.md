# 高级模糊搜索功能使用指南

## 功能概述

网站监控系统现已集成强大的高级模糊搜索功能，支持多关键词、智能建议、实时统计等特性，大幅提升数据查找效率。

## 主要特性

### 🔍 多关键词搜索
- **空格分隔**: 使用空格分隔多个关键词，如 `广州 WordPress`
- **逻辑选择**: 支持 AND/OR 逻辑
  - **AND逻辑**: 所有关键词都必须匹配（默认）
  - **OR逻辑**: 任一关键词匹配即可
- **中英文支持**: 完美支持中文和英文混合搜索

### 🎯 智能匹配
- **模糊匹配**: 自动进行模糊匹配，无需完全匹配
- **大小写不敏感**: 自动忽略大小写差异
- **多字段搜索**: 同时搜索多个相关字段

### 📊 实时统计
- **匹配数量**: 显示找到的结果数量
- **匹配率**: 显示搜索匹配的百分比
- **关键词标签**: 可视化显示搜索关键词
- **逻辑指示**: 清晰显示当前使用的搜索逻辑

### 💡 智能建议
- **自动建议**: 基于历史数据提供搜索建议
- **快速选择**: 点击建议快速应用搜索
- **相关性排序**: 建议按相关性智能排序

## 搜索字段覆盖

### 网站基本信息
- **网站名称** (`siteName`): 网站的显示名称
- **域名** (`domain`): 网站域名
- **网站URL** (`siteUrl`): 完整的网站地址
- **备注** (`notes`): 网站相关备注信息

### 关联信息
- **平台名称** (`platform.name`): 如 WordPress、Drupal 等
- **服务器名称** (`server.name`): 服务器标识名称
- **服务器位置** (`server.location`): 服务器地理位置
- **服务商** (`server.provider`): 服务器提供商

### 状态信息
- **网站状态** (`status`): active、inactive 等状态

## 使用示例

### 基本搜索
```
WordPress          # 搜索所有WordPress网站
广州               # 搜索包含"广州"的网站
pppharmapack      # 搜索域名包含此关键词的网站
```

### 多关键词搜索
```
广州 WordPress     # 搜索同时包含"广州"和"WordPress"的网站
上海 active        # 搜索上海地区的活跃网站
SSL expired        # 搜索SSL过期的网站
```

### 复杂搜索
```
珐玛珈 智能设备     # 搜索公司相关网站
carlinks.com      # 精确域名搜索
阿里云 北京        # 搜索特定服务商和地区
```

## 搜索技巧

### 1. 关键词选择
- **使用核心词**: 选择最能代表目标的关键词
- **避免过长**: 关键词不宜过长，建议2-8个字符
- **组合使用**: 结合不同类型的关键词提高精确度

### 2. 逻辑运用
- **AND逻辑**: 用于精确查找，缩小结果范围
- **OR逻辑**: 用于扩大搜索范围，找到更多相关结果

### 3. 搜索优化
- **从宽到窄**: 先用宽泛关键词，再逐步细化
- **利用建议**: 使用系统提供的搜索建议
- **查看统计**: 关注匹配率，调整搜索策略

## 界面说明

### 搜索输入框
- **主输入区**: 输入搜索关键词
- **搜索按钮**: 执行搜索操作
- **清除按钮**: 快速清除搜索内容
- **设置按钮**: 配置搜索选项

### 搜索建议
- **下拉列表**: 显示相关搜索建议
- **建议标签**: 标识为"建议"的选项
- **快速选择**: 点击直接应用建议

### 关键词标签
- **蓝色标签**: 显示当前搜索关键词
- **逻辑标签**: 显示AND/OR逻辑状态
- **绿色**: AND逻辑
- **橙色**: OR逻辑

### 统计信息
- **结果数量**: 找到的匹配结果数
- **总记录数**: 数据库中的总记录数
- **匹配率**: 匹配结果的百分比
- **颜色指示**:
  - 绿色: 高匹配率 (>50%)
  - 黄色: 中等匹配率 (20-50%)
  - 红色: 低匹配率 (<20%)

## 技术实现

### 后端搜索
- **SearchUtils类**: 统一的搜索工具类
- **SQL优化**: 高效的数据库查询
- **参数化查询**: 防止SQL注入
- **多表联查**: 支持关联表搜索

### 前端搜索
- **useAdvancedSearch Hook**: 搜索状态管理
- **防抖处理**: 300ms防抖优化性能
- **实时更新**: 搜索结果实时更新
- **缓存机制**: 智能缓存提升体验

### 性能优化
- **索引优化**: 数据库字段索引
- **查询缓存**: 结果缓存机制
- **分页支持**: 大数据量分页处理
- **异步处理**: 非阻塞搜索操作

## 常见问题

### Q: 为什么搜索没有结果？
A: 检查关键词拼写，尝试使用更宽泛的关键词，或切换到OR逻辑。

### Q: 如何搜索特殊字符？
A: 系统自动处理特殊字符，直接输入即可。

### Q: 搜索速度慢怎么办？
A: 系统有防抖机制，稍等片刻。如持续缓慢，请联系管理员。

### Q: 如何重置搜索？
A: 点击搜索框右侧的清除按钮，或删除所有搜索内容。

## 更新日志

### v1.0.0 (2025-07-15)
- ✅ 实现基础模糊搜索功能
- ✅ 支持多关键词搜索
- ✅ 添加智能搜索建议
- ✅ 集成实时搜索统计
- ✅ 优化中文搜索支持
- ✅ 完善用户界面体验

---

*如有问题或建议，请联系系统管理员。*
