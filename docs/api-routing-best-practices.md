# API路由最佳实践指南

基于Node.js最佳实践和Context7分析，本文档总结了SiteManager项目中API路由的最佳实践。

## 🎯 核心原则

### 1. 路由定义顺序
**原则**：具体路由必须在参数路由之前定义

```javascript
// ✅ 正确：具体路由在前
app.get('/api/v1/users/with-permissions', handler);
app.get('/api/v1/users/search', handler);
app.get('/api/v1/users/:id', handler);

// ❌ 错误：参数路由会拦截具体路由
app.get('/api/v1/users/:id', handler);
app.get('/api/v1/users/with-permissions', handler); // 永远不会被匹配
```

### 2. 中间件应用顺序
**原则**：认证 → 权限检查 → 验证 → 业务逻辑

```javascript
app.get('/api/v1/users/with-permissions', 
  authenticateToken,           // 1. 认证
  checkPermission('user.list'), // 2. 权限检查
  validateRequest,             // 3. 请求验证
  getUsersHandler              // 4. 业务逻辑
);
```

## 🛠️ 当前实现分析

### 已解决的问题

1. **路由冲突修复** ✅
   - 将 `/api/v1/users/with-permissions` 移动到 `/api/v1/users/:id` 之前
   - 确保Express按正确顺序匹配路由

2. **API配置统一** ✅
   - 创建 `frontend/src/utils/apiConfig.ts` 统一配置
   - 所有前端API调用使用一致的基础URL

3. **权限检查集成** ✅
   - 实现 `checkPermission` 中间件
   - 应用到敏感API端点

## 🚀 优化建议

### 1. 模块化路由管理

```javascript
// routes/users.js
const express = require('express');
const router = express.Router();

// 具体路由在前
router.get('/with-permissions', authenticateToken, checkPermission('user.list'), getUsersWithPermissions);
router.get('/search', authenticateToken, searchUsers);

// 参数路由在后
router.get('/:id', authenticateToken, getUserById);
router.put('/:id', authenticateToken, checkPermission('user.edit'), updateUser);
router.delete('/:id', authenticateToken, checkPermission('user.delete'), deleteUser);

module.exports = router;
```

### 2. 请求验证中间件

```javascript
const { body, param, query, validationResult } = require('express-validator');

const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors: errors.array()
    });
  }
  next();
};

// 应用验证
router.get('/with-permissions',
  [
    query('page').optional().isInt({ min: 1 }),
    query('limit').optional().isInt({ min: 1, max: 100 }),
    query('search').optional().isLength({ max: 100 })
  ],
  validateRequest,
  authenticateToken,
  checkPermission('user.list'),
  getUsersWithPermissions
);
```

### 3. 请求限流保护

```javascript
const rateLimit = require('express-rate-limit');

const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 最多100个请求
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试'
  }
});

// 应用到敏感路由
app.use('/api/v1/users', apiLimiter);
```

### 4. 事务ID追踪

```javascript
const { v4: uuidv4 } = require('uuid');

const transactionIdMiddleware = (req, res, next) => {
  req.transactionId = req.headers['x-transaction-id'] || uuidv4();
  res.setHeader('x-transaction-id', req.transactionId);
  console.log(`🔍 [${req.transactionId}] ${req.method} ${req.url}`);
  next();
};

app.use(transactionIdMiddleware);
```

## 📊 性能优化

### 1. 响应缓存

```javascript
const NodeCache = require('node-cache');
const cache = new NodeCache({ stdTTL: 300 }); // 5分钟缓存

const cacheMiddleware = (duration) => {
  return (req, res, next) => {
    const key = req.originalUrl;
    const cached = cache.get(key);
    
    if (cached) {
      return res.json({ ...cached, cached: true });
    }
    
    res.sendResponse = res.json;
    res.json = (body) => {
      cache.set(key, body, duration);
      res.sendResponse(body);
    };
    
    next();
  };
};
```

### 2. 数据库查询优化

```javascript
// 使用预编译语句和参数化查询
const getUsersWithPermissions = async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    
    // 构建安全的参数化查询
    let query = `
      SELECT id, username, email, real_name, role, status, 
             avatar, phone, department, last_login, created_at, updated_at 
      FROM users 
      WHERE 1=1
    `;
    const params = [];
    
    if (search.trim()) {
      query += ' AND (username LIKE ? OR email LIKE ? OR real_name LIKE ?)';
      const searchParam = `%${search.trim()}%`;
      params.push(searchParam, searchParam, searchParam);
    }
    
    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(parseInt(limit), (parseInt(page) - 1) * parseInt(limit));
    
    const [users] = await db.execute(query, params);
    
    res.json({
      success: true,
      data: { users },
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error(`❌ [${req.transactionId}] 获取用户列表失败:`, error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      timestamp: new Date().toISOString()
    });
  }
};
```

## 🔒 安全最佳实践

### 1. 输入验证和清理

```javascript
const sanitize = require('sanitize-html');

const sanitizeInput = (req, res, next) => {
  if (req.body) {
    Object.keys(req.body).forEach(key => {
      if (typeof req.body[key] === 'string') {
        req.body[key] = sanitize(req.body[key]);
      }
    });
  }
  next();
};
```

### 2. CORS配置

```javascript
const cors = require('cors');

app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
  optionsSuccessStatus: 200
}));
```

## 📝 总结

当前SiteManager项目的API路由实现已经解决了主要的路由冲突问题，并符合Express.js的最佳实践。建议的优化措施可以进一步提升系统的可维护性、性能和安全性。

### 优先级建议

1. **高优先级**：实现模块化路由管理
2. **中优先级**：添加请求验证和限流保护  
3. **低优先级**：实现事务ID追踪和高级缓存策略

这些改进将使SiteManager的API更加健壮、安全和易于维护。
