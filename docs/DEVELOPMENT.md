# 开发文档

## 项目概述

WordPress站点管理后台系统是一个现代化的全栈Web应用，用于管理WordPress网站、客户信息、项目进度、服务器和域名等资源。

## 技术栈

### 前端
- **React 18** - 用户界面框架
- **TypeScript** - 类型安全的JavaScript
- **Vite** - 快速的构建工具
- **Ant Design** - 企业级UI组件库
- **Zustand** - 轻量级状态管理
- **React Router v6** - 客户端路由
- **Axios** - HTTP客户端

### 后端
- **Node.js 18+** - JavaScript运行时
- **Express.js** - Web应用框架
- **TypeScript** - 类型安全
- **MySQL 8.0** - 关系型数据库
- **Redis** - 缓存和会话存储
- **JWT** - 身份认证
- **Winston** - 日志管理

### 部署
- **Docker** - 容器化
- **Docker Compose** - 多容器编排
- **Nginx** - 反向代理和静态文件服务

## 项目结构

```
sitemanager/
├── backend/                 # 后端API服务
│   ├── src/
│   │   ├── config/         # 配置文件
│   │   ├── controllers/    # 控制器
│   │   ├── middleware/     # 中间件
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── types/          # 类型定义
│   ├── Dockerfile          # 生产环境镜像
│   ├── Dockerfile.dev      # 开发环境镜像
│   └── package.json
├── frontend/               # 前端React应用
│   ├── src/
│   │   ├── components/     # 可复用组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   ├── store/          # 状态管理
│   │   ├── router/         # 路由配置
│   │   ├── utils/          # 工具函数
│   │   └── types/          # 类型定义
│   ├── Dockerfile          # 生产环境镜像
│   ├── Dockerfile.dev      # 开发环境镜像
│   └── package.json
├── database/               # 数据库脚本
│   └── init.sql           # 初始化脚本
├── docs/                   # 项目文档
├── docker-compose.yml      # 生产环境编排
├── docker-compose.dev.yml  # 开发环境编排
├── start.sh               # 生产环境启动脚本
├── dev.sh                 # 开发环境启动脚本
└── stop.sh                # 停止脚本
```

## 开发环境设置

### 1. 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+ (本地开发)
- Git

### 2. 快速启动
```bash
# 克隆项目
git clone <repository-url>
cd sitemanager

# 启动开发环境
./dev.sh
```

### 3. 本地开发
```bash
# 后端开发
cd backend
npm install
npm run dev

# 前端开发
cd frontend
npm install
npm run dev
```

## 开发规范

### 代码风格
- 使用TypeScript严格模式
- 遵循ESLint和Prettier规则
- 组件使用函数式组件 + Hooks
- 使用语义化的变量和函数命名

### Git提交规范
```
type(scope): description

type: feat, fix, docs, style, refactor, test, chore
scope: 影响的模块或功能
description: 简短描述
```

### API设计规范
- 遵循RESTful设计原则
- 统一的响应格式
- 完整的错误处理
- 请求参数验证

## 数据库设计

### 核心表结构
- `users` - 用户表
- `customers` - 客户表
- `projects` - 项目表
- `websites` - 网站表
- `servers` - 服务器表
- `domains` - 域名表
- `platforms` - 平台类型表
- `user_permissions` - 用户权限表
- `monitor_logs` - 监控日志表
- `notifications` - 通知表

### 关系设计
- 用户与项目：多对多关系
- 客户与项目：一对多关系
- 项目与网站：一对多关系
- 服务器与网站：一对多关系

## API接口设计

### 认证接口
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/refresh` - 刷新令牌
- `GET /api/v1/auth/me` - 获取当前用户信息

### 用户管理
- `GET /api/v1/users` - 获取用户列表
- `POST /api/v1/users` - 创建用户
- `GET /api/v1/users/:id` - 获取用户详情
- `PUT /api/v1/users/:id` - 更新用户信息
- `DELETE /api/v1/users/:id` - 删除用户

### 客户管理
- `GET /api/v1/customers` - 获取客户列表
- `POST /api/v1/customers` - 创建客户
- `GET /api/v1/customers/:id` - 获取客户详情
- `PUT /api/v1/customers/:id` - 更新客户信息
- `DELETE /api/v1/customers/:id` - 删除客户

## 测试策略

### 单元测试
- 使用Jest进行单元测试
- 覆盖核心业务逻辑
- 测试覆盖率目标：80%+

### 集成测试
- API接口测试
- 数据库操作测试
- 认证授权测试

### E2E测试
- 关键用户流程测试
- 跨浏览器兼容性测试

## 部署指南

### 开发环境
```bash
./dev.sh
```

### 生产环境
```bash
./start.sh
```

### 环境变量配置
参考 `.env.example` 文件配置相应的环境变量。

## 监控和日志

### 应用监控
- 响应时间监控
- 错误率监控
- 用户行为分析

### 日志管理
- 结构化日志输出
- 日志级别分类
- 日志轮转和归档

## 安全考虑

### 认证安全
- JWT令牌管理
- 密码强度要求
- 登录失败限制

### 数据安全
- SQL注入防护
- XSS攻击防护
- CSRF保护
- 数据加密存储

## 性能优化

### 前端优化
- 代码分割和懒加载
- 静态资源缓存
- 图片优化

### 后端优化
- 数据库查询优化
- Redis缓存策略
- API响应压缩

## 下一步开发计划

### 短期目标（1-2周）
1. 完善用户管理功能
2. 实现客户管理CRUD操作
3. 添加项目管理基础功能
4. 完善权限控制系统

### 中期目标（1-2月）
1. 实现网站监控功能
2. 添加SSL证书监控
3. 实现域名管理功能
4. 添加服务器监控

### 长期目标（3-6月）
1. 实现自动化监控和告警
2. 添加数据分析和报表
3. 实现移动端适配
4. 添加第三方集成功能

## 常见问题

### 开发环境问题
Q: Docker容器启动失败？
A: 检查端口占用，确保3000、3001、3306端口可用。

Q: 数据库连接失败？
A: 检查MySQL容器是否正常启动，确认环境变量配置正确。

### 代码问题
Q: TypeScript编译错误？
A: 检查类型定义是否正确，确保导入路径正确。

Q: API请求失败？
A: 检查后端服务是否启动，确认API地址配置正确。
