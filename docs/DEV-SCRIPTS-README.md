# 🚀 开发脚本使用指南

本文档介绍WordPress站点管理系统的开发脚本使用方法，包括一键启动、日志调试和服务管理功能。

## 📋 脚本概览

| 脚本 | 功能 | 描述 |
|------|------|------|
| `dev-local.sh` | 一键启动开发环境 | 启动前后端服务，支持多种模式 |
| `stop-local.sh` | 停止所有服务 | 优雅停止所有运行的服务 |
| `logs-viewer.sh` | 日志查看器 | 查看和监控各种日志文件 |

## 🎯 快速开始

### 基本启动
```bash
# 正常启动（包含环境检查、依赖安装）
./dev-local.sh

# 快速启动（跳过检查，适合日常开发）
./dev-local.sh -q

# 调试模式启动
./dev-local.sh -d

# 启动并实时显示日志
./dev-local.sh -l

# 组合使用：快速启动 + 调试 + 日志监控
./dev-local.sh -q -d -l
```

### 停止服务
```bash
# 停止所有服务
./stop-local.sh

# 停止服务并清理日志
./stop-local.sh --clean-logs
```

## 🔧 dev-local.sh 详细说明

### 命令行选项

| 选项 | 描述 | 示例 |
|------|------|------|
| `-h, --help` | 显示帮助信息 | `./dev-local.sh -h` |
| `-d, --debug` | 启用调试模式，显示详细执行过程 | `./dev-local.sh -d` |
| `-l, --logs` | 实时显示日志输出 | `./dev-local.sh -l` |
| `-q, --quick` | 快速启动，跳过环境检查和依赖安装 | `./dev-local.sh -q` |
| `-c, --clean` | 清理并重新安装依赖 | `./dev-local.sh -c` |
| `--api-only` | 仅启动后端API服务 | `./dev-local.sh --api-only` |
| `--frontend-only` | 仅启动前端服务 | `./dev-local.sh --frontend-only` |

### 启动流程

1. **环境检查** (可跳过)
   - Node.js版本检查 (≥18)
   - npm版本检查
   - 端口占用检查

2. **数据库启动** (可跳过)
   - Docker环境检查
   - MySQL和Redis容器启动

3. **依赖安装** (可跳过)
   - 前端依赖安装
   - 后端依赖安装

4. **服务启动**
   - 后端API服务 (端口3001)
   - 前端开发服务 (端口3000)

5. **日志监控** (可选)
   - 实时显示所有日志

### 输出信息

启动成功后会显示：
- 🌐 **前端地址**: http://localhost:3000
- 🔧 **后端API**: http://localhost:3001
- 📁 **日志文件位置**
- 🔍 **调试信息** (调试模式下)

## 🛑 stop-local.sh 详细说明

### 命令行选项

| 选项 | 描述 | 示例 |
|------|------|------|
| `--clean-logs` | 停止服务后清理日志文件 | `./stop-local.sh --clean-logs` |
| `-h, --help` | 显示帮助信息 | `./stop-local.sh -h` |

### 停止流程

1. **进程停止**
   - 从PID文件读取进程ID
   - 优雅停止进程 (SIGTERM)
   - 强制停止未响应进程 (SIGKILL)

2. **数据库停止**
   - 停止MySQL容器
   - 停止Redis容器

3. **清理工作**
   - 清理临时文件
   - 清理日志文件 (可选)

4. **状态检查**
   - 验证端口释放
   - 验证容器停止

## 📊 logs-viewer.sh 详细说明

### 基本用法

```bash
# 查看服务状态
./logs-viewer.sh -s

# 查看前端日志
./logs-viewer.sh frontend

# 实时跟踪API日志
./logs-viewer.sh -f api

# 查看最后100行后端日志
./logs-viewer.sh -n 100 backend

# 查看所有日志
./logs-viewer.sh all

# 清空前端日志
./logs-viewer.sh -c frontend
```

### 命令行选项

| 选项 | 描述 | 示例 |
|------|------|------|
| `-f, --follow` | 实时跟踪日志 | `./logs-viewer.sh -f api` |
| `-n, --lines N` | 显示最后N行 | `./logs-viewer.sh -n 50 frontend` |
| `-c, --clear` | 清空日志文件 | `./logs-viewer.sh -c all` |
| `-s, --status` | 显示服务状态 | `./logs-viewer.sh -s` |
| `-h, --help` | 显示帮助信息 | `./logs-viewer.sh -h` |

### 日志类型

| 类型 | 描述 | 文件位置 |
|------|------|----------|
| `frontend` | 前端开发服务日志 | `./logs/frontend.log` |
| `backend` | 后端服务日志 | `./logs/backend.log` |
| `api` | API服务器日志 | `./logs/api.log` |
| `system` | 系统操作日志 | `./logs/system.log` |
| `all` | 所有日志 | 所有日志文件 |

## 🎨 特色功能

### 1. 彩色输出
- ✅ 成功信息 (绿色)
- ⚠️ 警告信息 (黄色)
- ❌ 错误信息 (红色)
- ℹ️ 信息提示 (蓝色)
- 🔍 调试信息 (紫色)

### 2. 智能检测
- 自动检测环境依赖
- 智能选择API服务器类型
- 端口占用检测和处理
- 进程状态监控

### 3. 灵活配置
- 支持仅启动前端或后端
- 支持跳过各种检查步骤
- 支持清理重装依赖
- 支持调试和日志模式

### 4. 完善的日志系统
- 分类日志记录
- 实时日志监控
- 日志着色显示
- 日志清理功能

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :3000
   lsof -i :3001
   
   # 强制停止
   ./stop-local.sh
   ```

2. **依赖安装失败**
   ```bash
   # 清理重装
   ./dev-local.sh -c
   ```

3. **服务启动失败**
   ```bash
   # 调试模式启动
   ./dev-local.sh -d
   
   # 查看详细日志
   ./logs-viewer.sh -s
   ./logs-viewer.sh frontend
   ```

4. **Docker问题**
   ```bash
   # 检查Docker状态
   docker info
   
   # 仅启动应用服务
   ./dev-local.sh --api-only
   ./dev-local.sh --frontend-only
   ```

### 调试技巧

1. **使用调试模式**
   ```bash
   ./dev-local.sh -d -l
   ```

2. **查看实时日志**
   ```bash
   ./logs-viewer.sh -f all
   ```

3. **检查服务状态**
   ```bash
   ./logs-viewer.sh -s
   ```

4. **分步启动**
   ```bash
   # 先启动后端
   ./dev-local.sh --api-only
   
   # 再启动前端
   ./dev-local.sh --frontend-only
   ```

## 📚 最佳实践

### 日常开发
```bash
# 快速启动开发环境
./dev-local.sh -q

# 开发完成后停止
./stop-local.sh
```

### 调试问题
```bash
# 调试模式启动并监控日志
./dev-local.sh -d -l

# 查看特定服务日志
./logs-viewer.sh -f frontend
```

### 清理环境
```bash
# 停止服务并清理日志
./stop-local.sh --clean-logs

# 重新安装依赖
./dev-local.sh -c
```

### 性能优化
```bash
# 仅启动需要的服务
./dev-local.sh --api-only     # 仅后端开发
./dev-local.sh --frontend-only # 仅前端开发

# 跳过不必要的检查
./dev-local.sh -q             # 快速启动
```

## 🎉 总结

这套开发脚本提供了完整的本地开发环境管理功能：

- 🚀 **一键启动**: 快速启动完整开发环境
- 🔧 **灵活配置**: 支持多种启动模式和选项
- 📊 **日志管理**: 完善的日志记录和查看功能
- 🛑 **优雅停止**: 安全停止所有服务
- 🔍 **故障诊断**: 详细的调试和状态检查功能

使用这些脚本可以大大提高开发效率，简化环境管理，让开发者专注于代码开发而不是环境配置。
