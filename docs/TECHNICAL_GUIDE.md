# SiteManager 技术指南

## 📋 目录

1. [系统架构](#系统架构)
2. [核心组件](#核心组件)
3. [数据库设计](#数据库设计)
4. [API接口](#api接口)
5. [监控系统](#监控系统)
6. [通知系统](#通知系统)
7. [权限系统](#权限系统)
8. [性能优化](#性能优化)
9. [部署指南](#部署指南)
10. [故障排除](#故障排除)

## 🏗️ 系统架构

### 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  后端 (Node.js)  │    │  数据库 (MySQL)  │
│                 │    │                 │    │                 │
│ - React 18      │◄──►│ - Express.js    │◄──►│ - MySQL 8.0+    │
│ - Ant Design    │    │ - TypeScript    │    │ - 连接池        │
│ - TypeScript    │    │ - JWT认证       │    │ - 事务支持      │
│ - Vite构建      │    │ - 中间件        │    │ - 存储过程      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   监控服务层     │              │
         │              │                 │              │
         └──────────────┤ - 网站监控      │──────────────┘
                        │ - SSL检查       │
                        │ - 性能监控      │
                        │ - 通知服务      │
                        └─────────────────┘
```

### 技术栈

#### 前端技术栈
- **React 18**: 现代化的用户界面框架
- **TypeScript**: 类型安全的JavaScript超集
- **Ant Design**: 企业级UI组件库
- **Vite**: 快速的构建工具
- **React Router**: 客户端路由
- **Axios**: HTTP客户端
- **Chart.js**: 数据可视化

#### 后端技术栈
- **Node.js 18+**: JavaScript运行时
- **Express.js**: Web应用框架
- **TypeScript**: 类型安全开发
- **MySQL2**: 数据库驱动
- **JWT**: 身份认证
- **Nodemailer**: 邮件发送
- **Winston**: 日志管理
- **PM2**: 进程管理

#### 数据库技术
- **MySQL 8.0+**: 主数据库
- **连接池**: 数据库连接管理
- **存储过程**: 复杂业务逻辑
- **触发器**: 数据一致性保证
- **索引优化**: 查询性能优化

## 🔧 核心组件

### 1. 监控服务管理器 (MonitoringServiceManager)

负责统一管理所有监控服务的核心组件。

```typescript
class MonitoringServiceManager {
  private services: Map<string, any> = new Map();
  private config: MonitoringConfig;
  private logger: Logger;

  async initialize(): Promise<void> {
    // 初始化所有监控服务
    await this.initializeWebsiteMonitoring();
    await this.initializeSSLMonitoring();
    await this.initializePerformanceMonitoring();
    await this.initializeNotificationService();
  }

  async startMonitoring(): Promise<void> {
    // 启动所有监控服务
  }

  async stopMonitoring(): Promise<void> {
    // 停止所有监控服务
  }
}
```

**主要功能**:
- 服务生命周期管理
- 配置统一管理
- 健康检查和故障恢复
- 性能监控和优化

### 2. 增强监控服务 (EnhancedMonitoringService)

基于原PHP系统设计的高性能监控服务。

```typescript
class EnhancedMonitoringService {
  private db: Database;
  private config: MonitoringConfig;
  private workers: WorkerPool;

  async checkWebsites(websites: Website[]): Promise<CheckResult[]> {
    // 批量检测网站状态
    const results = await this.workers.execute(websites);
    await this.updateDatabase(results);
    return results;
  }

  async checkSSLCertificates(websites: Website[]): Promise<SSLResult[]> {
    // SSL证书检查
  }

  async detectCDN(website: Website): Promise<CDNInfo> {
    // CDN检测
  }
}
```

**核心特性**:
- 多线程并发检测
- 智能重试机制
- CDN自动识别
- 实时状态更新

### 3. 增强通知服务 (EnhancedNotificationService)

支持多渠道的智能通知系统。

```typescript
class EnhancedNotificationService {
  private channels: Map<string, NotificationChannel> = new Map();
  private templates: TemplateManager;
  private queue: NotificationQueue;

  async sendNotification(notification: Notification): Promise<void> {
    // 发送通知
    const template = await this.templates.render(notification);
    const channel = this.channels.get(notification.channel);
    await channel.send(template);
  }

  async sendBatchNotifications(notifications: Notification[]): Promise<void> {
    // 批量发送通知
  }
}
```

**支持渠道**:
- 邮件 (SMTP)
- 飞书 (Webhook)
- 微信 (企业微信API)
- 短信 (阿里云SMS)
- Webhook (自定义)

### 4. 性能监控服务 (PerformanceMonitoringService)

实时监控系统性能指标。

```typescript
class PerformanceMonitoringService {
  private metrics: MetricsCollector;
  private alerts: AlertManager;
  private history: HistoryManager;

  async collectMetrics(): Promise<PerformanceMetrics> {
    // 收集性能指标
    return {
      cpu: await this.getCPUUsage(),
      memory: await this.getMemoryUsage(),
      database: await this.getDatabaseMetrics(),
      monitoring: await this.getMonitoringMetrics()
    };
  }

  async checkAlerts(metrics: PerformanceMetrics): Promise<void> {
    // 检查报警条件
  }
}
```

**监控指标**:
- CPU使用率
- 内存使用率
- 数据库性能
- 网络连接数
- 响应时间
- 错误率

## 🗄️ 数据库设计

### 核心表结构

#### 1. 网站表 (websites)
```sql
CREATE TABLE websites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    site_name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) NOT NULL,
    site_url TEXT NOT NULL,
    platform_id INT,
    server_id INT,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    status_check BOOLEAN DEFAULT TRUE,
    ssl_check BOOLEAN DEFAULT TRUE,
    monitor_level ENUM('high', 'normal', 'low') DEFAULT 'normal',
    check_interval INT DEFAULT 300,
    timeout_seconds INT DEFAULT 30,
    retry_count INT DEFAULT 3,
    consecutive_failures INT DEFAULT 0,
    last_check_time TIMESTAMP NULL,
    last_success_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### 2. 网站状态检查表 (website_status_checks)
```sql
CREATE TABLE website_status_checks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL,
    check_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status_code INT,
    response_time INT,
    is_accessible BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    cdn_detected VARCHAR(100),
    server_ip VARCHAR(45),
    ssl_days_remaining INT,
    ssl_issuer VARCHAR(255),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);
```

#### 3. 通知日志表 (notification_logs)
```sql
CREATE TABLE notification_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT,
    notification_type ENUM('status', 'ssl', 'renewal') NOT NULL,
    channel ENUM('email', 'feishu', 'wechat', 'sms', 'webhook') NOT NULL,
    trigger_reason VARCHAR(255),
    message TEXT,
    status ENUM('pending', 'sent', 'failed') DEFAULT 'pending',
    sent_at TIMESTAMP NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. 权限表 (website_permissions)
```sql
CREATE TABLE website_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    website_id INT NOT NULL,
    permission_type ENUM('view', 'monitor', 'manage') DEFAULT 'view',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_user_website (user_id, website_id)
);
```

### 存储过程

#### 1. 获取待检测网站 (GetSitesToCheck)
```sql
DELIMITER //
CREATE PROCEDURE GetSitesToCheck(
    IN p_limit INT DEFAULT 20,
    IN p_interval_seconds INT DEFAULT 300
)
BEGIN
    SELECT
        w.id,
        w.site_name,
        w.domain,
        w.site_url,
        w.platform_id,
        w.server_id,
        w.status_check,
        w.ssl_check,
        w.monitor_level,
        w.check_interval,
        w.timeout_seconds,
        w.retry_count,
        w.consecutive_failures,
        w.last_check_time,
        CASE
            WHEN w.last_check_time IS NULL THEN 1
            WHEN TIMESTAMPDIFF(SECOND, w.last_check_time, NOW()) >= w.check_interval THEN 1
            ELSE 0
        END as should_check,
        CASE
            WHEN w.monitor_level = 'high' THEN 1
            WHEN w.monitor_level = 'normal' THEN 2
            ELSE 3
        END as priority
    FROM websites w
    WHERE w.status = 'active'
      AND w.status_check = 1
      AND (
          w.last_check_time IS NULL
          OR TIMESTAMPDIFF(SECOND, w.last_check_time, NOW()) >= w.check_interval
      )
    ORDER BY priority ASC, w.last_check_time ASC
    LIMIT p_limit;
END //
DELIMITER ;
```

#### 2. 更新网站状态统计 (UpdateWebsiteStatusStats)
```sql
DELIMITER //
CREATE PROCEDURE UpdateWebsiteStatusStats(
    IN p_website_id INT,
    IN p_status_code INT,
    IN p_response_time INT,
    IN p_is_accessible BOOLEAN,
    IN p_error_message TEXT,
    IN p_cdn_detected VARCHAR(100),
    IN p_server_ip VARCHAR(45),
    IN p_ssl_days_remaining INT,
    IN p_ssl_issuer VARCHAR(255)
)
BEGIN
    DECLARE v_consecutive_failures INT DEFAULT 0;

    START TRANSACTION;

    -- 插入检查记录
    INSERT INTO website_status_checks (
        website_id, status_code, response_time, is_accessible,
        error_message, cdn_detected, server_ip, ssl_days_remaining, ssl_issuer
    ) VALUES (
        p_website_id, p_status_code, p_response_time, p_is_accessible,
        p_error_message, p_cdn_detected, p_server_ip, p_ssl_days_remaining, p_ssl_issuer
    );

    -- 更新连续失败次数
    IF p_is_accessible THEN
        SET v_consecutive_failures = 0;
        UPDATE websites SET
            consecutive_failures = 0,
            last_success_time = NOW(),
            last_check_time = NOW()
        WHERE id = p_website_id;
    ELSE
        SELECT consecutive_failures + 1 INTO v_consecutive_failures
        FROM websites WHERE id = p_website_id;

        UPDATE websites SET
            consecutive_failures = v_consecutive_failures,
            last_check_time = NOW()
        WHERE id = p_website_id;
    END IF;

    COMMIT;
END //
DELIMITER ;
```

## 🔌 API接口

### 认证接口

#### POST /api/v1/auth/login
用户登录接口

**请求参数**:
```json
{
  "username": "admin",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "role": "super_admin",
      "permissions": ["all"]
    },
    "expiresIn": "24h"
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### 监控接口

#### GET /api/v1/monitor-api/sites-to-check
获取待检测网站列表

**查询参数**:
- `limit`: 限制数量 (默认: 20)
- `interval`: 检测间隔秒数 (默认: 300)
- `level`: 监控级别 (high/normal/low)

**响应**:
```json
{
  "success": true,
  "message": "获取待检测网站成功",
  "data": {
    "sites": [
      {
        "id": 1,
        "site_name": "示例网站",
        "domain": "example.com",
        "url": "https://example.com",
        "platform_id": 1,
        "server_id": 1,
        "status_check": 1,
        "ssl_check": 1,
        "monitor_level": "high",
        "should_check": 1,
        "priority": 1
      }
    ],
    "total": 1,
    "limit": 20,
    "interval": 300
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

#### POST /api/v1/monitor-api/batch-update-results
批量更新检测结果

**请求参数**:
```json
{
  "results": [
    {
      "websiteId": 1,
      "statusCode": 200,
      "responseTime": 850,
      "isAccessible": true,
      "errorMessage": null,
      "cdnDetected": "cloudflare",
      "serverIp": "***********",
      "sslDaysRemaining": 90,
      "sslIssuer": "Let's Encrypt"
    }
  ]
}
```

### 权限接口

#### GET /api/v1/permissions/users
获取用户权限列表

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `role`: 角色筛选 (all/super_admin/admin/user)
- `search`: 搜索关键词

#### POST /api/v1/permissions/assign
分配网站权限

**请求参数**:
```json
{
  "userId": 1,
  "websiteId": 1,
  "permissionType": "monitor"
}
```

### 性能接口

#### GET /api/v1/performance/status
获取当前性能状态

**响应**:
```json
{
  "success": true,
  "data": {
    "status": {
      "cpu_usage": 45.2,
      "memory_usage": 68.5,
      "avg_response_time": 850,
      "checks_per_second": 12.5,
      "error_rate": 2.1
    },
    "health": {
      "overall": "healthy",
      "cpu": "normal",
      "memory": "normal",
      "responseTime": "fast",
      "errorRate": "low"
    }
  }
}
```