# 增强版权限系统设计文档

## 概述

基于用户需求，设计一个三层权限控制系统，支持页面级、模块级、资源级的细粒度权限管理。

## 权限层级结构

### 1. 页面级权限（Page Level Permissions）
控制用户能够访问哪些页面/模块

```json
{
  "pagePermissions": {
    "website.basic": {
      "name": "网站管理-基础管理",
      "description": "访问网站基础管理功能",
      "route": "/websites/basic"
    },
    "website.advanced": {
      "name": "网站管理-高级管理", 
      "description": "访问网站高级管理功能",
      "route": "/websites/advanced"
    },
    "server.list": {
      "name": "服务器管理-服务器列表",
      "description": "访问服务器列表页面",
      "route": "/servers/list"
    },
    "server.inventory": {
      "name": "服务器管理-台账管理",
      "description": "访问服务器台账管理",
      "route": "/servers/inventory"
    },
    "user.management": {
      "name": "用户管理",
      "description": "访问用户管理页面",
      "route": "/users"
    },
    "system.settings": {
      "name": "系统设置",
      "description": "访问系统设置页面",
      "route": "/settings"
    },
    "dashboard": {
      "name": "仪表盘",
      "description": "访问系统仪表盘",
      "route": "/dashboard"
    }
  }
}
```

### 2. 模块级权限（Module Level Permissions）
在页面内的具体操作权限

```json
{
  "modulePermissions": {
    "website": {
      "basic": {
        "view": "查看网站基础信息",
        "create": "创建网站",
        "edit": "编辑网站基础信息",
        "delete": "删除网站",
        "monitor": "监控网站状态"
      },
      "advanced": {
        "view": "查看网站高级信息",
        "ssl.manage": "SSL证书管理",
        "backup.manage": "备份管理",
        "security.config": "安全配置",
        "performance.optimize": "性能优化"
      }
    },
    "server": {
      "list": {
        "view": "查看服务器列表",
        "create": "创建服务器",
        "edit": "编辑服务器信息",
        "delete": "删除服务器",
        "connect": "SSH连接服务器"
      },
      "inventory": {
        "view": "查看服务器台账",
        "export": "导出台账数据",
        "import": "导入台账数据",
        "audit": "审计台账变更"
      }
    },
    "user": {
      "view": "查看用户信息",
      "create": "创建用户",
      "edit": "编辑用户信息",
      "delete": "删除用户",
      "permission.manage": "管理用户权限"
    },
    "system": {
      "settings.view": "查看系统设置",
      "settings.edit": "编辑系统设置",
      "logs.view": "查看系统日志",
      "backup.manage": "系统备份管理"
    }
  }
}
```

### 3. 资源级权限（Resource Level Permissions）
对具体资源的访问权限

```json
{
  "resourcePermissions": {
    "websites": {
      "type": "website_access",
      "structure": {
        "website_id": "number",
        "permissions": {
          "access": "boolean - 是否可访问",
          "edit": "boolean - 是否可编辑",
          "view_credentials": "boolean - 是否可查看密码",
          "manage_ssl": "boolean - 是否可管理SSL",
          "manage_backup": "boolean - 是否可管理备份"
        },
        "granted_by": "number - 授权人ID",
        "granted_at": "datetime - 授权时间",
        "expires_at": "datetime - 过期时间（可选）"
      }
    },
    "servers": {
      "type": "server_access",
      "structure": {
        "server_id": "number",
        "permissions": {
          "access": "boolean - 是否可访问",
          "edit": "boolean - 是否可编辑",
          "connect": "boolean - 是否可SSH连接",
          "view_credentials": "boolean - 是否可查看连接密码"
        },
        "granted_by": "number - 授权人ID",
        "granted_at": "datetime - 授权时间",
        "expires_at": "datetime - 过期时间（可选）"
      }
    }
  }
}
```

## 数据库表结构设计

### 1. 页面权限表
```sql
CREATE TABLE page_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限代码',
    permission_name VARCHAR(200) NOT NULL COMMENT '权限名称',
    description TEXT COMMENT '权限描述',
    route_path VARCHAR(200) COMMENT '对应路由路径',
    parent_code VARCHAR(100) COMMENT '父权限代码',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_permission_code (permission_code),
    INDEX idx_parent_code (parent_code),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面权限定义表';
```

### 2. 用户页面权限表
```sql
CREATE TABLE user_page_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    permission_code VARCHAR(100) NOT NULL COMMENT '权限代码',
    granted BOOLEAN DEFAULT TRUE COMMENT '是否授予权限',
    granted_by INT COMMENT '授权人ID',
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at DATETIME COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_permission (user_id, permission_code),
    INDEX idx_user_id (user_id),
    INDEX idx_permission_code (permission_code),
    INDEX idx_granted (granted),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户页面权限表';
```

### 3. 增强版网站权限表
```sql
CREATE TABLE enhanced_website_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    website_id INT NOT NULL COMMENT '网站ID',
    access_granted BOOLEAN DEFAULT TRUE COMMENT '是否可访问',
    edit_granted BOOLEAN DEFAULT FALSE COMMENT '是否可编辑',
    view_credentials BOOLEAN DEFAULT FALSE COMMENT '是否可查看密码',
    manage_ssl BOOLEAN DEFAULT FALSE COMMENT '是否可管理SSL',
    manage_backup BOOLEAN DEFAULT FALSE COMMENT '是否可管理备份',
    granted_by INT COMMENT '授权人ID',
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at DATETIME COMMENT '过期时间',
    notes TEXT COMMENT '备注',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_website (user_id, website_id),
    INDEX idx_user_id (user_id),
    INDEX idx_website_id (website_id),
    INDEX idx_is_active (is_active),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='增强版网站权限表';
```

## 权限检查逻辑

### 1. 页面访问权限检查
```javascript
async function checkPagePermission(userId, pageCode) {
    // 1. 检查用户角色（超级管理员拥有所有权限）
    const user = await getUserById(userId);
    if (user.role === 'super_admin') return true;
    
    // 2. 检查用户是否有该页面权限
    const permission = await getUserPagePermission(userId, pageCode);
    return permission && permission.granted;
}
```

### 2. 资源访问权限检查
```javascript
async function checkWebsitePermission(userId, websiteId, action) {
    // 1. 检查用户角色
    const user = await getUserById(userId);
    if (user.role === 'super_admin') return true;
    
    // 2. 检查页面权限（必须先有页面权限）
    const hasPagePermission = await checkPagePermission(userId, 'website.basic');
    if (!hasPagePermission) return false;
    
    // 3. 检查具体网站权限
    const websitePermission = await getWebsitePermission(userId, websiteId);
    if (!websitePermission || !websitePermission.access_granted) return false;
    
    // 4. 检查具体操作权限
    switch (action) {
        case 'edit':
            return websitePermission.edit_granted;
        case 'view_credentials':
            return websitePermission.view_credentials;
        case 'manage_ssl':
            return websitePermission.manage_ssl;
        default:
            return true;
    }
}
```

## 前端权限守卫更新

### 1. 路由权限守卫
```javascript
// 更新路由配置，支持页面级权限检查
const routes = [
    {
        path: '/websites/basic',
        component: WebsiteBasicManagement,
        meta: { 
            requiresAuth: true,
            pagePermission: 'website.basic'
        }
    },
    {
        path: '/websites/advanced', 
        component: WebsiteAdvancedManagement,
        meta: {
            requiresAuth: true,
            pagePermission: 'website.advanced'
        }
    }
];
```

### 2. 组件级权限控制
```javascript
// 使用增强版权限守卫组件
<PagePermissionGuard permission="website.basic">
    <WebsiteBasicManagement />
</PagePermissionGuard>

<ResourcePermissionGuard 
    resourceType="website" 
    resourceId={websiteId}
    action="edit"
>
    <EditWebsiteButton />
</ResourcePermissionGuard>
```

## 实施计划

1. **第一阶段**：创建数据库表结构和基础权限数据
2. **第二阶段**：实现后端权限API和权限检查逻辑
3. **第三阶段**：创建前端权限编辑界面
4. **第四阶段**：更新前端路由和权限守卫
5. **第五阶段**：全面测试和优化

## 兼容性考虑

- 保持与现有权限系统的兼容性
- 渐进式迁移，避免影响现有功能
- 提供权限数据迁移脚本
