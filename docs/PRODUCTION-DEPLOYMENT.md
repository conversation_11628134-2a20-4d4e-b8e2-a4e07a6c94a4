# SiteManager 生产环境部署指南

## 📋 概述

本指南提供了 SiteManager 系统的完整生产环境部署方案，包括传统部署和 Docker 容器化部署两种方式。

## 🚀 快速开始

### 方式一：一键自动部署（推荐）

```bash
# 下载部署脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/sitemanager/main/deploy-production.sh -o deploy-production.sh
chmod +x deploy-production.sh

# 执行部署
./deploy-production.sh --domain=your-domain.com --nginx --ssl --pm2
```

### 方式二：Docker 容器化部署

```bash
# 克隆项目
git clone https://github.com/your-repo/sitemanager.git
cd sitemanager

# 配置环境变量
cp .env.production.template .env.production
# 编辑 .env.production 文件

# 启动服务
docker-compose -f docker-compose.production.yml up -d
```

## 📁 部署文件说明

### 核心部署文件

- `deploy-production.sh` - 生产环境自动部署脚本
- `production-manager.sh` - 生产环境管理工具
- `docker-compose.production.yml` - Docker 编排文件
- `.env.production.template` - 环境变量模板

### Docker 相关文件

- `Dockerfile.backend` - 后端服务容器化配置
- `Dockerfile.frontend` - 前端服务容器化配置
- `nginx/nginx.conf` - Nginx 主配置文件
- `nginx/conf.d/default.conf` - 站点配置文件

## 🔧 部署脚本功能

### deploy-production.sh 主要功能

- ✅ 自动检测操作系统和环境
- ✅ 安装系统依赖（Node.js、MySQL、Nginx等）
- ✅ 创建项目用户和目录结构
- ✅ 配置数据库和用户权限
- ✅ 安装项目依赖并构建前端
- ✅ 配置 Nginx 反向代理
- ✅ 设置 SSL 证书（自签名或 Let's Encrypt）
- ✅ 配置服务管理（systemd 或 PM2）
- ✅ 设置日志轮转和防火墙
- ✅ 执行部署后健康检查
- ✅ 支持脚本自动更新

### 部署选项

```bash
# 基础部署
./deploy-production.sh

# 完整部署（推荐）
./deploy-production.sh --domain=example.com --nginx --ssl --pm2

# 集群部署
./deploy-production.sh --mode=cluster --nginx --ssl --systemd

# Docker 部署
./deploy-production.sh --mode=docker

# 仅检查环境
./deploy-production.sh --check-only

# 强制重新安装
./deploy-production.sh --force

# 更新脚本
./deploy-production.sh --update
```

## 🛠️ 生产环境管理

### production-manager.sh 管理工具

```bash
# 查看服务状态
./production-manager.sh status

# 启动/停止/重启服务
./production-manager.sh start
./production-manager.sh stop
./production-manager.sh restart

# 查看日志
./production-manager.sh logs
./production-manager.sh logs -f  # 跟踪日志

# 数据备份和恢复
./production-manager.sh backup
./production-manager.sh restore -d 20231201

# 应用更新
./production-manager.sh update

# 系统监控
./production-manager.sh monitor
./production-manager.sh health

# 系统清理
./production-manager.sh cleanup
```

## 🐳 Docker 部署详解

### 服务组件

- **mysql**: MySQL 8.0 数据库
- **redis**: Redis 缓存服务
- **backend**: Node.js 后端 API
- **frontend**: Nginx + 前端静态文件
- **monitoring**: coolmonitor 监控服务（可选）

### Docker 命令

```bash
# 启动所有服务
docker-compose -f docker-compose.production.yml up -d

# 查看服务状态
docker-compose -f docker-compose.production.yml ps

# 查看日志
docker-compose -f docker-compose.production.yml logs -f

# 停止服务
docker-compose -f docker-compose.production.yml down

# 重建并启动
docker-compose -f docker-compose.production.yml up -d --build

# 数据备份
docker-compose -f docker-compose.production.yml exec mysql mysqldump -u root -p sitemanager > backup.sql
```

## ⚙️ 环境配置

### 必需配置项

```bash
# 数据库配置
DB_HOST=localhost
DB_USER=sitemanager
DB_PASSWORD=your-secure-password

# 安全配置
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-encryption-key

# 域名配置
DOMAIN=your-domain.com
APP_URL=https://your-domain.com
```

### 可选配置项

```bash
# 邮件通知
MAIL_HOST=smtp.example.com
MAIL_USER=<EMAIL>

# 飞书通知
FEISHU_WEBHOOK_URL=https://open.feishu.cn/...

# SSL证书
SSL_CERT_PATH=/etc/ssl/certs/your-domain.crt
SSL_KEY_PATH=/etc/ssl/private/your-domain.key
```

## 🔒 安全配置

### 防火墙设置

```bash
# Ubuntu/Debian (UFW)
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### SSL 证书配置

```bash
# 使用 Let's Encrypt (推荐)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# 或使用自签名证书
sudo openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/ssl/private/your-domain.key \
  -out /etc/ssl/certs/your-domain.crt
```

## 📊 监控和维护

### 系统监控

```bash
# 查看系统资源
./production-manager.sh monitor

# 健康检查
./production-manager.sh health

# 查看服务状态
systemctl status sitemanager
```

### 日志管理

```bash
# 查看应用日志
sudo journalctl -u sitemanager -f

# 查看 Nginx 日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# 查看应用日志文件
sudo tail -f /var/log/sitemanager/*.log
```

### 数据备份

```bash
# 自动备份（建议设置定时任务）
./production-manager.sh backup

# 手动数据库备份
mysqldump -u sitemanager -p sitemanager > backup_$(date +%Y%m%d).sql

# 备份上传文件
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz /opt/sitemanager/backend/uploads
```

## 🔄 更新和升级

### 应用更新

```bash
# 使用管理脚本更新
./production-manager.sh update

# 手动更新
cd /opt/sitemanager
sudo -u sitemanager git pull origin main
sudo systemctl restart sitemanager
```

### 脚本更新

```bash
# 自动更新部署脚本
./deploy-production.sh --update

# 手动下载最新脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/sitemanager/main/deploy-production.sh -o deploy-production.sh
```

## 🚨 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   sudo journalctl -u sitemanager -n 50
   sudo systemctl status sitemanager
   ```

2. **数据库连接失败**
   ```bash
   mysql -u sitemanager -p -h localhost
   sudo systemctl status mysql
   ```

3. **Nginx 配置错误**
   ```bash
   sudo nginx -t
   sudo systemctl status nginx
   ```

4. **端口被占用**
   ```bash
   sudo netstat -tlnp | grep :3001
   sudo lsof -i :3001
   ```

### 紧急恢复

```bash
# 恢复到最近备份
./production-manager.sh restore -d $(ls /var/backups/sitemanager | tail -1 | cut -d'_' -f1)

# 重置服务
sudo systemctl stop sitemanager
sudo systemctl start sitemanager
```

## 📞 技术支持

如果遇到部署问题，请：

1. 查看部署日志和错误信息
2. 检查系统要求和依赖
3. 参考故障排除章节
4. 提交 Issue 并附上详细日志

---

**注意**: 生产环境部署前请务必：
- 备份现有数据
- 在测试环境验证
- 准备回滚方案
- 通知相关人员
