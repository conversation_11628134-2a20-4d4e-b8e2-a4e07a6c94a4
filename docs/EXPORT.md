# 数据导出功能说明

## 功能概述

WordPress站点管理系统提供了完整的数据导出功能，支持将系统中的各类数据导出为Excel、CSV、JSON等格式，方便用户进行数据分析、备份和迁移。

## 支持的导出格式

### 1. Excel格式 (.xlsx)
- **特点**: 支持多工作表、样式格式、列宽设置
- **适用场景**: 数据分析、报表制作、演示文档
- **优势**: 格式美观、功能丰富、兼容性好

### 2. CSV格式 (.csv)
- **特点**: 纯文本格式、通用性强
- **适用场景**: 数据迁移、系统集成、简单分析
- **优势**: 文件小、加载快、兼容性最佳

### 3. JSON格式 (.json)
- **特点**: 结构化数据、保留数据类型
- **适用场景**: API集成、程序处理、数据备份
- **优势**: 结构清晰、易于解析、支持嵌套

## 导出功能类型

### 1. 单表导出
在各个数据列表页面（用户、客户、项目、网站等），可以导出当前筛选条件下的数据。

**功能特点**:
- 支持当前筛选条件
- 支持排序设置
- 支持多种格式
- 实时数据统计

**使用方法**:
1. 在列表页面设置筛选条件
2. 点击"导出"按钮
3. 选择导出格式
4. 确认导出设置
5. 自动下载文件

### 2. 批量导出
通过用户菜单中的"批量导出"功能，可以一次性导出多种类型的数据。

**功能特点**:
- 支持多表数据
- 统一导出格式
- 进度实时显示
- 权限控制

**使用方法**:
1. 点击用户头像菜单
2. 选择"批量导出"
3. 勾选需要导出的数据类型
4. 选择导出格式
5. 开始批量导出

## 导出数据类型

### 1. 用户数据
**包含字段**:
- 基本信息：ID、用户名、邮箱、真实姓名
- 权限信息：角色、状态
- 联系信息：手机号、部门
- 时间信息：最后登录、创建时间、更新时间

**权限要求**: 管理员及以上

### 2. 客户数据
**包含字段**:
- 基本信息：客户姓名、公司名称、联系人
- 联系方式：手机号、邮箱、地址
- 业务信息：行业、来源、状态
- 管理信息：销售负责人、备注、创建时间

**权限要求**: 所有用户

### 3. 项目数据
**包含字段**:
- 项目信息：项目名称、类型、状态
- 客户信息：客户公司
- 合同信息：合同编号、签订日期、服务费用
- 进度信息：计划上线、实际上线
- 团队信息：项目经理、销售负责人
- 其他信息：预览链接、备注、创建时间

**权限要求**: 所有用户

### 4. 网站数据
**包含字段**:
- 基本信息：域名、网站URL、平台类型
- 服务器信息：服务器名称、IP地址
- 关联信息：客户公司、关联项目
- 状态信息：网站状态、访问状态
- 时间信息：上线日期、到期日期、SSL到期、域名到期
- 监控信息：最后检查时间
- 财务信息：续费金额

**权限要求**: 所有用户

### 5. 服务器数据
**包含字段**:
- 基本信息：服务器名称、IP地址、位置
- 配置信息：CPU、内存、存储、带宽
- 系统信息：操作系统、SSH端口
- 管理信息：服务商、到期时间、续费费用
- 状态信息：服务器状态、备注

**权限要求**: 管理员及以上

### 6. 域名数据
**包含字段**:
- 基本信息：域名、注册商
- 时间信息：注册日期、到期日期
- 管理信息：自动续费、DNS服务商
- 状态信息：域名状态、最后检查时间
- 技术信息：WHOIS信息
- 财务信息：续费费用

**权限要求**: 所有用户

## 导出设置选项

### 1. 导出范围
- **当前筛选结果**: 导出当前页面筛选条件下的所有数据
- **全部数据**: 导出该类型的所有数据（忽略筛选条件）

### 2. 数据格式化
- **日期格式**: 自动转换为本地化格式
- **状态翻译**: 将状态码转换为中文描述
- **金额格式**: 添加货币符号和千分位分隔符
- **空值处理**: 空值显示为"-"或"未设置"

### 3. 文件命名
- **自动命名**: 数据类型_导出时间.格式
- **示例**: 用户列表_2024-01-22_14-30-25.xlsx

## 技术实现

### 后端实现
- **导出引擎**: 使用xlsx、csv-writer等库
- **数据处理**: 支持大数据量分页导出
- **文件管理**: 临时文件自动清理
- **权限控制**: 基于用户角色的导出权限

### 前端实现
- **用户界面**: Ant Design组件
- **文件下载**: 自动触发浏览器下载
- **进度显示**: 实时显示导出进度
- **错误处理**: 友好的错误提示

## 性能优化

### 1. 大数据量处理
- **分批导出**: 大数据量自动分批处理
- **内存优化**: 流式处理避免内存溢出
- **超时控制**: 设置合理的超时时间

### 2. 文件管理
- **临时文件**: 导出文件存储在临时目录
- **自动清理**: 定期清理过期的导出文件
- **存储限制**: 限制单次导出的数据量

### 3. 并发控制
- **队列机制**: 大量导出请求排队处理
- **资源限制**: 限制同时进行的导出任务数量

## 安全考虑

### 1. 权限控制
- **角色验证**: 基于用户角色控制导出权限
- **数据过滤**: 只能导出有权限查看的数据
- **敏感信息**: 自动过滤敏感字段（如密码）

### 2. 数据保护
- **临时存储**: 导出文件仅临时存储
- **访问控制**: 导出文件仅限创建者下载
- **日志记录**: 记录所有导出操作

## 使用建议

### 1. 最佳实践
- **合理筛选**: 导出前设置合适的筛选条件
- **选择格式**: 根据用途选择合适的导出格式
- **及时下载**: 导出完成后及时下载文件
- **数据备份**: 定期导出重要数据作为备份

### 2. 注意事项
- **数据量限制**: 单次导出建议不超过10万条记录
- **网络稳定**: 大文件导出需要稳定的网络环境
- **浏览器兼容**: 建议使用现代浏览器
- **文件安全**: 导出的文件包含敏感信息，请妥善保管

## 常见问题

### Q: 导出的Excel文件打开乱码怎么办？
A: 确保使用支持UTF-8编码的软件打开，或在Excel中选择正确的编码格式。

### Q: 为什么导出的数据不完整？
A: 检查筛选条件设置，确认是否选择了"当前筛选结果"还是"全部数据"。

### Q: 导出文件太大无法下载怎么办？
A: 可以通过设置筛选条件减少导出数据量，或分批导出。

### Q: 导出功能对系统性能有影响吗？
A: 系统已做性能优化，正常使用不会影响系统性能，但建议避免在高峰期进行大量数据导出。

## 更新日志

### v1.0.0 (2024-01-22)
- 初始版本发布
- 支持Excel、CSV、JSON格式导出
- 支持单表和批量导出
- 实现权限控制和数据安全
