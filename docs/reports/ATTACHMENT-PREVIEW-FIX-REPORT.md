# 网站管理附件预览功能修复报告

## 问题描述

用户反馈网站管理系统中的附件预览功能存在问题，无法正常预览图片和PDF文件。

## 问题分析

通过详细调试，发现了以下关键问题：

### 1. 环境变量配置错误
- **问题**: 前端代码中使用了 `process.env.REACT_APP_API_BASE_URL`，但在Vite项目中应该使用 `import.meta.env.VITE_API_URL`
- **错误信息**: `Uncaught ReferenceError: process is not defined`
- **影响**: 导致预览URL生成失败，无法正确访问后端API

### 2. API URL构建问题
- **问题**: URL构建逻辑不正确，缺少 `/api/v1` 前缀
- **修复**: 统一API URL构建逻辑

### 3. 开发环境认证问题
- **问题**: 前端在开发模式下缺少认证token
- **修复**: 添加开发模式下的模拟token支持

## 修复方案

### 1. 修复环境变量使用
**文件**: `frontend/src/services/websiteAttachment.ts`

```typescript
// 修复前
const baseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001/api/v1';

// 修复后  
const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
return `${baseUrl}/api/v1/websites/${websiteId}/attachments/${attachmentId}/preview`;
```

### 2. 添加环境变量配置
**文件**: `frontend/.env.development`

```env
VITE_API_URL=http://localhost:3001
VITE_APP_TITLE=网站管理系统
VITE_APP_VERSION=2.2.0
```

### 3. 改进开发模式认证
**文件**: `frontend/src/services/api.ts`

```typescript
// 开发模式下使用模拟token
if (import.meta.env.DEV && config.headers) {
  config.headers.Authorization = `Bearer dev-token`;
  console.log('使用开发模式token');
}
```

### 4. 增强错误处理
- 添加了详细的控制台日志输出
- 改进了图片加载错误处理
- 优化了API错误响应处理

## 测试验证

### 1. API连接测试
- ✅ 后端API服务正常运行 (端口3001)
- ✅ 附件列表API正常返回数据
- ✅ 预览API正常响应图片内容

### 2. 前端功能测试
- ✅ 附件列表正常加载
- ✅ 预览URL正确生成
- ✅ 图片预览功能正常
- ✅ PDF预览功能正常

### 3. 测试页面
创建了专门的测试页面 `test-attachment-preview.html` 用于验证功能：
- API连接测试
- 附件列表获取测试
- 预览功能测试
- 直接URL访问测试

## 技术细节

### 后端API支持
后端 `simple-server.js` 已包含完整的附件管理API：
- `GET /api/v1/websites/:id/attachments` - 获取附件列表
- `GET /api/v1/websites/:id/attachments/:attachmentId/preview` - 预览附件
- `GET /api/v1/websites/:id/attachments/:attachmentId/download` - 下载附件
- `POST /api/v1/websites/:id/attachments` - 上传附件
- `PUT /api/v1/websites/:id/attachments/:attachmentId` - 更新附件信息
- `DELETE /api/v1/websites/:id/attachments/:attachmentId` - 删除附件

### 前端组件功能
`AttachmentPreview` 组件支持：
- 附件列表展示
- 图片预览（支持缩放、旋转、拖拽）
- PDF预览
- 文件下载
- 附件统计

## 修复结果

✅ **问题已完全解决**
- 附件预览功能正常工作
- 支持图片和PDF文件预览
- 错误处理完善
- 开发环境配置正确

## 后续建议

1. **生产环境配置**: 创建 `.env.production` 文件配置生产环境API地址
2. **认证系统**: 完善用户认证系统，替换开发模式的模拟token
3. **文件类型扩展**: 考虑支持更多文件类型的预览
4. **性能优化**: 添加图片缩略图生成功能
5. **安全加固**: 添加文件访问权限验证

## 测试说明

用户可以通过以下方式测试修复结果：

1. **访问网站管理页面**: http://localhost:3000/websites
2. **查看附件列**: 在网站列表中可以看到附件图标
3. **点击预览**: 点击附件图标即可预览图片或PDF
4. **测试页面**: http://localhost:3000/test-attachment-preview.html

## 中文文件名编码问题修复 (2025-06-17 13:25)

### 问题描述
用户上传中文文件名的附件时，文件名在系统中显示为乱码，如：
- 原始文件名: `测试文档.pdf`
- 显示乱码: `æµ‹è¯•æ–‡æ¡£.pdf`

### 问题分析
这是典型的字符编码问题：
1. **multer处理问题**: multer在处理multipart/form-data时，中文文件名可能被错误编码
2. **编码转换**: 文件名从UTF-8被错误解释为Latin-1编码
3. **存储问题**: 乱码的文件名直接存储到数据库中

### 修复方案

#### 1. 添加中文文件名修复函数
```javascript
const fixChineseFilename = (filename) => {
  if (!filename) return filename;

  try {
    // 检查是否包含中文字符，如果已经正确则直接返回
    if (/[\u4e00-\u9fa5]/.test(filename)) {
      return filename;
    }

    // 检查是否是乱码并尝试多种修复方式
    if (/[^\x00-\x7F]/.test(filename)) {
      const methods = [
        () => Buffer.from(filename, 'latin1').toString('utf8'),
        () => decodeURIComponent(escape(filename)),
        () => {
          const step1 = Buffer.from(filename, 'latin1').toString('utf8');
          return /[\u4e00-\u9fa5]/.test(step1) ? step1 : filename;
        }
      ];

      for (const method of methods) {
        try {
          const fixed = method();
          if (/[\u4e00-\u9fa5]/.test(fixed)) {
            console.log(`文件名编码修复成功: ${filename} -> ${fixed}`);
            return fixed;
          }
        } catch (e) {
          continue;
        }
      }
    }

    return filename;
  } catch (error) {
    console.error('文件名编码修复失败:', error);
    return filename;
  }
};
```

#### 2. 在文件上传处理中应用修复
```javascript
// 修复中文文件名编码
const fixedOriginalName = fixChineseFilename(file.originalname);

const [result] = await db.execute(insertQuery, [
  websiteId,
  file.filename,
  fixedOriginalName, // 使用修复后的文件名
  file.path,
  file.size,
  path.extname(fixedOriginalName).toLowerCase(),
  file.mimetype,
  getFileCategory(file.mimetype),
  description || null,
  1,
  isPreviewSupported(file.mimetype)
]);
```

### 修复效果
✅ **中文文件名正确显示**: 上传的中文文件名能够正确保存和显示
✅ **多种编码兼容**: 支持多种可能的编码错误情况
✅ **向后兼容**: 不影响已有的英文文件名
✅ **错误处理**: 修复失败时使用原始文件名，不会导致系统错误

### 测试验证
- 上传中文文件名的图片、PDF等文件
- 检查文件列表中的文件名显示
- 验证预览和下载功能正常
- 确认数据库中存储的文件名正确

修复完成时间: 2025-06-17 13:25
修复状态: ✅ 完成
