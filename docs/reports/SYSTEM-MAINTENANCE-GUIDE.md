# 🔧 系统维护指南

## 概述

本指南介绍了网站管理系统的维护工具和最佳实践，帮助管理员保持系统的健康运行。

## 🚀 快速检查

### 系统健康检查
```bash
# 检查系统整体健康状态
curl http://localhost:3001/api/health

# 检查监控状态
curl http://localhost:3001/api/status
```

### 服务状态检查
```bash
# 检查系统服务状态
systemctl status sitemanager-fullstack.service

# 查看服务日志
journalctl -u sitemanager-fullstack.service --since "1 hour ago"
```

## 🛠️ 维护工具

### 1. 系统维护脚本
**位置**: `backend/scripts/system-maintenance.js`

**功能**:
- 重置异常网站状态
- 清理过期记录
- 优化数据库表
- 生成系统报告

**使用方法**:
```bash
# 手动执行系统维护
node backend/scripts/system-maintenance.js
```

**建议频率**: 每周执行一次

### 2. 数据库修复脚本
**位置**: `fix-abnormal-website-status.sql`

**功能**:
- 重置连续失败次数异常高的网站
- 查看网站状态分布统计
- 列出需要关注的异常网站

**使用方法**:
```bash
# 执行数据库修复
mysql -u sitemanager -psitemanager123 sitemanager < fix-abnormal-website-status.sql
```

### 3. 网站状态优化服务
**位置**: `backend/services/WebsiteStatusOptimizer.js`

**功能**:
- 智能网站状态检测
- 减少误报和重复通知
- 临时性错误的智能处理
- 通知状态管理

**集成方式**: 已集成到定时任务系统中

### 4. 错误监控服务
**位置**: `backend/services/ErrorMonitoringService.js`

**功能**:
- 错误日志记录（文件+数据库）
- 日志轮转管理
- 错误告警机制
- 错误统计和分析

**日志位置**: `backend/logs/errors.log`

## 📊 监控指标

### 网站状态分布
- **正常**: 连续失败次数为0的网站
- **轻微异常**: 连续失败次数1-5次的网站
- **需要关注**: 连续失败次数6-20次的网站
- **严重异常**: 连续失败次数超过20次的网站

### 关键指标
- 活跃网站总数
- 异常网站数量
- 平均响应时间
- 最近检查时间

## 🚨 告警机制

### 网站状态告警
- **触发条件**: 网站连续失败5次以上
- **通知方式**: 飞书机器人通知
- **通知间隔**: 30分钟（避免重复通知）

### 系统错误告警
- **触发条件**: 5分钟内发生10个以上错误
- **告警间隔**: 30分钟
- **记录方式**: 错误日志表 + 日志文件

## 🔧 常见问题处理

### 问题1: 大量网站连续失败次数异常高
**症状**: 网站连续失败次数达到数千次
**原因**: 检测逻辑异常或网络问题导致的累积错误
**解决方案**:
```bash
# 执行数据库修复脚本
mysql -u sitemanager -psitemanager123 sitemanager < fix-abnormal-website-status.sql

# 或运行系统维护脚本
node backend/scripts/system-maintenance.js
```

### 问题2: 系统响应缓慢
**症状**: API响应时间过长
**原因**: 数据库表未优化或缓存失效
**解决方案**:
```bash
# 运行系统维护脚本优化数据库
node backend/scripts/system-maintenance.js

# 重启服务清理缓存
systemctl restart sitemanager-fullstack.service
```

### 问题3: 错误日志过多
**症状**: 错误日志文件过大
**原因**: 系统错误频发或日志轮转失效
**解决方案**:
```bash
# 检查错误统计
curl http://localhost:3001/api/health

# 手动清理日志（保留最近7天）
find backend/logs -name "*.log*" -mtime +7 -delete
```

## 📅 维护计划

### 日常维护（每天）
- [x] 检查系统健康状态
- [x] 查看异常网站数量
- [x] 检查服务运行状态

### 周度维护（每周）
- [x] 执行系统维护脚本
- [x] 检查错误日志统计
- [x] 优化数据库表

### 月度维护（每月）
- [x] 清理过期日志文件
- [x] 备份重要数据
- [x] 检查系统性能指标
- [x] 更新维护文档

## 🔍 性能优化建议

### 数据库优化
1. 定期执行 `OPTIMIZE TABLE` 命令
2. 清理过期的状态记录
3. 添加必要的索引

### 缓存优化
1. 合理设置缓存TTL
2. 定期清理无效缓存
3. 监控缓存命中率

### 监控优化
1. 调整检测频率（避免过于频繁）
2. 优化通知逻辑（减少误报）
3. 智能处理临时性错误

## 📞 技术支持

如果遇到无法解决的问题，请：

1. 收集相关日志信息
2. 记录问题复现步骤
3. 检查系统资源使用情况
4. 联系技术支持团队

## 📝 更新记录

- **2025-07-16**: 创建系统维护指南
- **2025-07-16**: 添加健康检查API和维护工具
- **2025-07-16**: 完善错误监控和告警机制