# 🚀 站点存活检测与通知系统使用指南

## 📋 系统概述

这是一个专为1000+站点设计的高效存活检测和实时通知系统，具有以下特点：

- ✅ **高效并发**：分批并发处理，避免服务器过载
- ✅ **智能重试**：减少误报，提高检测准确性
- ✅ **多种通知**：支持邮件、微信、钉钉、Webhook等多种通知方式
- ✅ **实时监控**：故障第一时间通知，恢复时自动通知
- ✅ **统计分析**：详细的监控统计和历史数据
- ✅ **易于部署**：一键启动，自动化管理

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   定时调度器    │───▶│   检测任务队列   │───▶│   并发检测引擎   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   通知触发器    │◀───│   状态变化检测   │◀───│   结果处理器    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────────────────────────────────────────────────────┐
│                        通知渠道                                │
│  📧 邮件  💬 微信  📢 钉钉  🔗 Webhook  📱 短信                │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 快速开始

### 1. 初始化数据库

```bash
# 初始化监控相关数据库表
./start-monitoring.sh init
```

### 2. 配置通知方式

编辑 `.env.monitoring` 文件：

```bash
# 邮件通知配置
EMAIL_ENABLED=true
EMAIL_HOST=smtp.qq.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-password
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>,<EMAIL>

# 企业微信通知配置
WECHAT_ENABLED=true
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key

# 钉钉通知配置
DINGTALK_ENABLED=true
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=your-token
DINGTALK_SECRET=your-secret
```

### 3. 启动监控服务

```bash
# 前台启动（用于调试）
./start-monitoring.sh start

# 后台启动（生产环境）
./start-monitoring.sh start -d

# 查看服务状态
./start-monitoring.sh status

# 查看实时日志
./start-monitoring.sh logs
```

## ⚙️ 配置说明

### 核心参数配置

| 参数 | 默认值 | 说明 |
|------|--------|------|
| CHECK_INTERVAL | 300000 | 检测间隔（毫秒），5分钟 |
| BATCH_SIZE | 50 | 每批检测站点数 |
| CONCURRENCY | 10 | 并发检测数 |
| TIMEOUT | 15000 | 请求超时时间（毫秒） |
| FAILURE_THRESHOLD | 3 | 连续失败次数阈值 |
| RETRY_ATTEMPTS | 2 | 失败重试次数 |

### 通知配置详解

#### 📧 邮件通知

```bash
EMAIL_ENABLED=true
EMAIL_HOST=smtp.qq.com          # SMTP服务器
EMAIL_PORT=587                  # SMTP端口
EMAIL_SECURE=false              # 是否使用SSL
EMAIL_USER=<EMAIL>    # 邮箱用户名
EMAIL_PASS=your-password        # 邮箱密码或授权码
EMAIL_FROM=<EMAIL>    # 发件人
EMAIL_TO=<EMAIL>,<EMAIL>  # 收件人（逗号分隔）
```

#### 💬 企业微信通知

1. 创建企业微信群机器人
2. 获取Webhook URL
3. 配置环境变量：

```bash
WECHAT_ENABLED=true
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key
WECHAT_MENTIONED=@all           # @成员列表
```

#### 📢 钉钉通知

1. 创建钉钉群机器人
2. 获取Webhook URL和签名密钥
3. 配置环境变量：

```bash
DINGTALK_ENABLED=true
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=your-token
DINGTALK_SECRET=your-secret     # 签名密钥（可选）
DINGTALK_AT_MOBILES=13800138000,13900139000  # @手机号
DINGTALK_AT_ALL=false           # 是否@所有人
```

#### 🔗 Webhook通知

```bash
WEBHOOK_ENABLED=true
WEBHOOK_URLS=https://your-webhook-url.com/notify,https://backup-url.com/notify
```

Webhook请求格式：
```json
{
  "type": "site_down",
  "site": {
    "id": 123,
    "name": "示例站点",
    "domain": "example.com",
    "url": "https://example.com"
  },
  "error": {
    "message": "连接超时",
    "statusCode": 0,
    "responseTime": 15000
  },
  "timestamp": "2025-06-24T12:00:00.000Z"
}
```

## 📊 监控面板

### API接口

监控服务提供以下API接口（默认端口3002）：

- `GET /status` - 获取监控服务状态
- `GET /stats` - 获取监控统计数据
- `POST /trigger-check` - 手动触发检测
- `POST /test-notification` - 测试通知功能

### 前端界面

访问 `http://localhost:3000/monitoring` 查看监控配置界面，包括：

- 📈 实时监控状态
- ⚙️ 基础配置管理
- 📧 通知方式配置
- 🧪 通知测试功能

## 🔧 管理命令

```bash
# 启动服务
./start-monitoring.sh start     # 前台启动
./start-monitoring.sh start -d  # 后台启动

# 管理服务
./start-monitoring.sh stop      # 停止服务
./start-monitoring.sh restart   # 重启服务
./start-monitoring.sh status    # 查看状态

# 日志和测试
./start-monitoring.sh logs      # 查看实时日志
./start-monitoring.sh test      # 测试通知功能

# 数据库管理
./start-monitoring.sh init      # 初始化数据库表
```

## 📈 性能优化

### 1. 分批并发策略

- **批次大小**：50个站点/批
- **并发数量**：10个请求/批
- **批次延迟**：2秒间隔
- **总处理时间**：1000个站点约需10-15分钟

### 2. 智能重试机制

- **重试次数**：失败后重试2次
- **重试延迟**：递增延迟（3秒、6秒）
- **故障阈值**：连续失败3次才报警
- **恢复阈值**：连续成功2次才恢复

### 3. 数据库优化

- **索引优化**：关键字段建立索引
- **数据清理**：自动清理30天前的历史数据
- **统计触发器**：实时更新统计数据

## 🚨 故障处理

### 常见问题

1. **监控服务无法启动**
   ```bash
   # 检查端口占用
   lsof -i :3002
   
   # 检查数据库连接
   mysql -h localhost -u root -p sitemanager
   ```

2. **通知发送失败**
   ```bash
   # 测试邮件配置
   ./start-monitoring.sh test
   
   # 查看详细日志
   tail -f logs/monitoring.log
   ```

3. **检测延迟或超时**
   ```bash
   # 调整并发参数
   vim .env.monitoring
   # 减少 CONCURRENCY 或 BATCH_SIZE
   ```

### 日志分析

```bash
# 查看错误日志
grep "ERROR" logs/monitoring.log

# 查看通知日志
grep "通知" logs/monitoring.log

# 查看性能统计
grep "统计" logs/monitoring.log
```

## 📊 监控指标

### 关键指标

- **检测成功率**：成功检测次数 / 总检测次数
- **平均响应时间**：所有成功请求的平均响应时间
- **故障站点数**：当前处于故障状态的站点数量
- **通知发送数**：已发送的通知总数

### 统计报表

系统自动生成以下统计数据：

- 📅 **每日统计**：检测次数、成功率、平均响应时间
- 📈 **趋势分析**：7天内的检测趋势
- 🚨 **故障报告**：故障站点列表和故障时长
- 📤 **通知统计**：各种通知方式的发送统计

## 🔒 安全建议

1. **网络安全**
   - 限制监控API的访问IP
   - 使用HTTPS进行通信
   - 定期更新依赖包

2. **数据安全**
   - 定期备份监控数据
   - 加密敏感配置信息
   - 设置数据保留策略

3. **访问控制**
   - 限制监控配置的修改权限
   - 记录重要操作日志
   - 定期审查通知配置

## 📞 技术支持

如遇到问题，请：

1. 查看日志文件：`logs/monitoring.log`
2. 检查配置文件：`.env.monitoring`
3. 验证数据库连接和表结构
4. 测试网络连接和DNS解析

---

**🎉 恭喜！您的站点监控系统已配置完成，现在可以实时监控1000+站点的存活状态，并在故障发生时第一时间收到通知！**
