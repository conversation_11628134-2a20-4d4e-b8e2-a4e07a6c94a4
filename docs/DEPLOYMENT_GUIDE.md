# SiteManager 部署指南

## 📋 部署概述

本指南详细介绍了SiteManager监控系统在不同环境下的部署方法，包括开发环境、测试环境和生产环境。

## 🏗️ 部署架构

### 单机部署架构
```
┌─────────────────────────────────────────────────────────────┐
│                    服务器 (Ubuntu 20.04+)                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Nginx     │  │   Node.js   │  │       MySQL         │  │
│  │  (反向代理)  │  │  (后端API)   │  │     (数据库)        │  │
│  │   Port 80   │  │  Port 3001  │  │    Port 3306       │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
│         │               │                      │            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              静态文件 (React Build)                      │ │
│  │                /var/www/sitemanager                     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 集群部署架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   负载均衡器     │    │   应用服务器1    │    │   应用服务器2    │
│    (Nginx)      │    │   (Node.js)     │    │   (Node.js)     │
│   Port 80/443   │◄──►│   Port 3001     │    │   Port 3001     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │   数据库集群     │              │
         └──────────────┤   MySQL Master  │──────────────┘
                        │   MySQL Slave   │
                        └─────────────────┘
```

## 🛠️ 环境准备

### 系统要求

#### 最低配置
- **CPU**: 2核
- **内存**: 4GB
- **存储**: 50GB SSD
- **网络**: 10Mbps

#### 推荐配置
- **CPU**: 4核
- **内存**: 8GB
- **存储**: 100GB SSD
- **网络**: 100Mbps

#### 操作系统
- Ubuntu 20.04+ (推荐)
- CentOS 8+
- Debian 11+

### 软件依赖

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础工具
sudo apt install -y curl wget git vim htop

# 安装Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装MySQL 8.0
sudo apt install -y mysql-server

# 安装Nginx
sudo apt install -y nginx

# 安装PM2 (进程管理器)
sudo npm install -g pm2

# 安装其他工具
sudo apt install -y certbot python3-certbot-nginx
```

## 🚀 部署步骤

### 1. 代码部署

```bash
# 创建部署目录
sudo mkdir -p /opt/sitemanager
sudo chown $USER:$USER /opt/sitemanager

# 克隆代码
cd /opt/sitemanager
git clone https://github.com/your-repo/sitemanager.git .

# 或者上传代码包
# scp -r sitemanager.tar.gz user@server:/opt/sitemanager/
# tar -xzf sitemanager.tar.gz
```

### 2. 数据库配置

```bash
# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
sudo mysql -u root -p
```

```sql
-- 创建数据库
CREATE DATABASE sitemanager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'sitemanager'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON sitemanager.* TO 'sitemanager'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

```bash
# 导入数据库结构
cd /opt/sitemanager
mysql -u sitemanager -p sitemanager < backend/database/migrations/001_create_base_tables.sql
mysql -u sitemanager -p sitemanager < backend/database/migrations/002_create_monitoring_tables.sql
mysql -u sitemanager -p sitemanager < backend/database/migrations/003_create_notification_tables.sql
mysql -u sitemanager -p sitemanager < backend/database/migrations/004_create_permission_tables.sql
mysql -u sitemanager -p sitemanager < backend/database/migrations/005_create_config_tables.sql
mysql -u sitemanager -p sitemanager < backend/database/migrations/006_create_procedures.sql
mysql -u sitemanager -p sitemanager < backend/database/migrations/007_create_performance_metrics_table.sql

# 导入初始数据
mysql -u sitemanager -p sitemanager < backend/database/seeds/initial_data.sql
```

### 3. 后端配置

```bash
cd /opt/sitemanager/backend

# 安装依赖
npm install --production

# 复制配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

生产环境配置示例：
```env
# 环境配置
NODE_ENV=production
API_PORT=3001
API_BASE_URL=https://your-domain.com

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=sitemanager
DB_PASSWORD=your_secure_password
DB_NAME=sitemanager
DB_CONNECTION_LIMIT=20
DB_TIMEOUT=60000

# JWT配置
JWT_SECRET=your_very_secure_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# 邮件配置
SMTP_HOST=smtp.feishu.cn
SMTP_PORT=465
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password

# 监控配置
MONITOR_INTERVAL=300
MONITOR_TIMEOUT=30
MONITOR_BATCH_SIZE=20
MONITOR_CONCURRENCY=10
MONITOR_RETRY_COUNT=3

# 日志配置
LOG_LEVEL=info
LOG_FILE=/var/log/sitemanager/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# 性能配置
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_METRICS_INTERVAL=60
PERFORMANCE_ALERT_CPU_THRESHOLD=80
PERFORMANCE_ALERT_MEMORY_THRESHOLD=85

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=https://your-domain.com
```

### 4. 前端构建

```bash
cd /opt/sitemanager/frontend

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env.production
nano .env.production
```

```env
VITE_API_URL=https://your-domain.com
VITE_API_LOGGING=false
VITE_WEBSOCKET_URL=wss://your-domain.com
```

```bash
# 构建生产版本
npm run build

# 复制构建文件到Web目录
sudo mkdir -p /var/www/sitemanager
sudo cp -r dist/* /var/www/sitemanager/
sudo chown -R www-data:www-data /var/www/sitemanager
```

### 5. Nginx配置

```bash
# 创建Nginx配置文件
sudo nano /etc/nginx/sites-available/sitemanager
```

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 前端静态文件
    location / {
        root /var/www/sitemanager;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        
        # 限制请求大小
        client_max_body_size 10M;
    }
    
    # WebSocket代理
    location /ws/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 健康检查
    location /health {
        proxy_pass http://localhost:3001/api/health;
        access_log off;
    }
    
    # 日志配置
    access_log /var/log/nginx/sitemanager_access.log;
    error_log /var/log/nginx/sitemanager_error.log;
}
```

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/sitemanager /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 6. SSL证书配置

```bash
# 使用Let's Encrypt获取免费SSL证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. PM2进程管理

```bash
cd /opt/sitemanager/backend

# 创建PM2配置文件
nano ecosystem.config.js
```

```javascript
module.exports = {
  apps: [
    {
      name: 'sitemanager-api',
      script: 'src/app.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      error_file: '/var/log/sitemanager/pm2-error.log',
      out_file: '/var/log/sitemanager/pm2-out.log',
      log_file: '/var/log/sitemanager/pm2-combined.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      watch: false,
      ignore_watch: ['node_modules', 'logs'],
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s'
    },
    {
      name: 'sitemanager-monitor',
      script: 'scripts/monitor-health-check.js',
      instances: 1,
      exec_mode: 'fork',
      cron_restart: '0 */6 * * *', // 每6小时重启一次
      env: {
        NODE_ENV: 'production'
      },
      error_file: '/var/log/sitemanager/monitor-error.log',
      out_file: '/var/log/sitemanager/monitor-out.log',
      time: true
    }
  ]
};
```

```bash
# 创建日志目录
sudo mkdir -p /var/log/sitemanager
sudo chown $USER:$USER /var/log/sitemanager

# 启动应用
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

### 8. 系统服务配置

```bash
# 创建systemd服务文件
sudo nano /etc/systemd/system/sitemanager.service
```

```ini
[Unit]
Description=SiteManager Monitoring System
After=network.target mysql.service

[Service]
Type=forking
User=sitemanager
WorkingDirectory=/opt/sitemanager/backend
ExecStart=/usr/bin/pm2 start ecosystem.config.js
ExecReload=/usr/bin/pm2 reload ecosystem.config.js
ExecStop=/usr/bin/pm2 stop ecosystem.config.js
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# 创建专用用户
sudo useradd -r -s /bin/false sitemanager
sudo chown -R sitemanager:sitemanager /opt/sitemanager

# 启用服务
sudo systemctl daemon-reload
sudo systemctl enable sitemanager
sudo systemctl start sitemanager
```

## 🔧 配置优化

### MySQL优化

```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

```ini
[mysqld]
# 基础配置
max_connections = 200
max_connect_errors = 10000
table_open_cache = 2000
max_allowed_packet = 16M

# InnoDB配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1

# 查询缓存
query_cache_type = 1
query_cache_size = 128M
query_cache_limit = 2M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

### Node.js优化

```bash
# 设置Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=2048"

# 优化垃圾回收
export NODE_OPTIONS="--max-old-space-size=2048 --optimize-for-size"
```

### 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 65536" | sudo tee -a /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65536" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## 📊 监控和日志

### 日志配置

```bash
# 配置logrotate
sudo nano /etc/logrotate.d/sitemanager
```

```
/var/log/sitemanager/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 sitemanager sitemanager
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 监控脚本

```bash
# 创建健康检查脚本
nano /opt/sitemanager/scripts/health-check.sh
```

```bash
#!/bin/bash

# 检查服务状态
check_service() {
    local service=$1
    if systemctl is-active --quiet $service; then
        echo "✅ $service is running"
        return 0
    else
        echo "❌ $service is not running"
        return 1
    fi
}

# 检查端口
check_port() {
    local port=$1
    if netstat -tuln | grep -q ":$port "; then
        echo "✅ Port $port is listening"
        return 0
    else
        echo "❌ Port $port is not listening"
        return 1
    fi
}

# 检查数据库连接
check_database() {
    if mysql -u sitemanager -p$DB_PASSWORD -e "SELECT 1" sitemanager >/dev/null 2>&1; then
        echo "✅ Database connection successful"
        return 0
    else
        echo "❌ Database connection failed"
        return 1
    fi
}

echo "🔍 SiteManager Health Check - $(date)"
echo "=================================="

# 检查系统服务
check_service nginx
check_service mysql
check_service sitemanager

# 检查端口
check_port 80
check_port 443
check_port 3001
check_port 3306

# 检查数据库
check_database

# 检查磁盘空间
df -h | grep -E "/$|/opt|/var"

# 检查内存使用
free -h

echo "=================================="
echo "Health check completed"
```

```bash
chmod +x /opt/sitemanager/scripts/health-check.sh

# 添加到crontab
crontab -e
# 每5分钟执行一次健康检查
*/5 * * * * /opt/sitemanager/scripts/health-check.sh >> /var/log/sitemanager/health-check.log 2>&1
```

## 🔒 安全配置

### 防火墙配置

```bash
# 安装UFW
sudo apt install ufw

# 配置防火墙规则
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow from 127.0.0.1 to any port 3001
sudo ufw allow from 127.0.0.1 to any port 3306

# 启用防火墙
sudo ufw enable
```

### 安全加固

```bash
# 禁用root SSH登录
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config

# 更改SSH端口（可选）
sudo sed -i 's/#Port 22/Port 2222/' /etc/ssh/sshd_config

# 重启SSH服务
sudo systemctl restart ssh

# 安装fail2ban
sudo apt install fail2ban

# 配置fail2ban
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
```

## 🚀 部署验证

### 验证清单

```bash
# 运行部署验证脚本
cd /opt/sitemanager/backend
node scripts/deployment-verification.js

# 检查服务状态
sudo systemctl status nginx
sudo systemctl status mysql
sudo systemctl status sitemanager
pm2 status

# 检查日志
tail -f /var/log/sitemanager/app.log
tail -f /var/log/nginx/sitemanager_access.log

# 测试API
curl -k https://your-domain.com/api/health

# 测试前端
curl -k https://your-domain.com
```

### 性能测试

```bash
# 安装测试工具
npm install -g artillery

# 运行负载测试
artillery quick --count 10 --num 100 https://your-domain.com/api/health
```

## 📋 维护任务

### 定期维护

```bash
# 创建维护脚本
nano /opt/sitemanager/scripts/maintenance.sh
```

```bash
#!/bin/bash

echo "🔧 Starting maintenance tasks - $(date)"

# 清理旧日志
find /var/log/sitemanager -name "*.log" -mtime +30 -delete

# 清理旧的监控数据
mysql -u sitemanager -p$DB_PASSWORD sitemanager -e "
DELETE FROM website_status_checks WHERE check_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
DELETE FROM notification_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
DELETE FROM performance_metrics WHERE timestamp < DATE_SUB(NOW(), INTERVAL 7 DAY);
"

# 优化数据库
mysql -u sitemanager -p$DB_PASSWORD sitemanager -e "OPTIMIZE TABLE websites, website_status_checks, notification_logs;"

# 重启PM2进程
pm2 restart all

echo "✅ Maintenance tasks completed - $(date)"
```

```bash
chmod +x /opt/sitemanager/scripts/maintenance.sh

# 添加到crontab（每周执行）
crontab -e
0 2 * * 0 /opt/sitemanager/scripts/maintenance.sh >> /var/log/sitemanager/maintenance.log 2>&1
```

### 备份策略

```bash
# 创建备份脚本
nano /opt/sitemanager/scripts/backup.sh
```

```bash
#!/bin/bash

BACKUP_DIR="/opt/backups/sitemanager"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# 备份数据库
mysqldump -u sitemanager -p$DB_PASSWORD sitemanager > $BACKUP_DIR/database_$DATE.sql

# 备份配置文件
tar -czf $BACKUP_DIR/config_$DATE.tar.gz /opt/sitemanager/backend/.env /etc/nginx/sites-available/sitemanager

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "✅ Backup completed - $(date)"
```

```bash
chmod +x /opt/sitemanager/scripts/backup.sh

# 每天凌晨2点备份
crontab -e
0 2 * * * /opt/sitemanager/scripts/backup.sh >> /var/log/sitemanager/backup.log 2>&1
```

---

通过以上步骤，您可以成功部署SiteManager监控系统到生产环境。记住定期检查系统状态、更新依赖包和执行维护任务。
