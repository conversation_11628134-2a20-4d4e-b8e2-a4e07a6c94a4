# 网站监控系统架构详解

## 🏗️ 系统架构概览

我们的网站监控系统采用多层架构设计，集成了coolmonitor的优秀监控逻辑，提供高性能、高可靠性的网站存活检测服务。

## ⏰ 定时任务系统

### 📅 定时任务调度器 (`SchedulerService`)

**启动位置**: `simple-server.js` 第7523行
```javascript
schedulerService = new SchedulerService(db);
schedulerService.start();
```

**定时任务列表**:

#### 1. 🌍 网站状态检测任务
- **执行频率**: 每5分钟 (`*/5 * * * *`)
- **执行时间**: 24小时不间断
- **时区**: Asia/Shanghai
- **主要功能**: 检测所有活跃网站的存活状态

#### 2. 🔒 SSL证书检测任务  
- **执行频率**: 每天凌晨00:30 (`30 0 * * *`)
- **执行时间**: 每日一次
- **主要功能**: 检测HTTPS网站的SSL证书状态和到期时间

#### 3. 🔄 服务器配置更新任务
- **执行频率**: 每天凌晨2点 (`0 2 * * *`)
- **执行时间**: 每日一次  
- **主要功能**: 自动更新所有服务器的配置信息

## 🔍 网站检测机制

### 🎯 检测流程架构

```
定时任务触发 → SchedulerService → WebsiteStatusService → 检测策略选择
                                                              ↓
                                    ┌─────────────────────────┴─────────────────────────┐
                                    ↓                                                   ↓
                            网站数量 < 100                                    网站数量 ≥ 100
                                    ↓                                                   ↓
                        CoolmonitorEnhancedChecker                           Worker Threads
                        (coolmonitor增强检查器)                              + coolmonitor逻辑
                                    ↓                                                   ↓
                            并发检测(15-20个)                                  高性能并发检测
                                    ↓                                                   ↓
                                    └─────────────────────────┬─────────────────────────┘
                                                              ↓
                                                        结果汇总和处理
                                                              ↓
                                                    ┌─────────┴─────────┐
                                                    ↓                   ↓
                                            数据库状态更新        通知检查和发送
```

### 🚀 检测策略详解

#### **策略1: coolmonitor增强检查器模式** (网站数量 < 100)
- **使用场景**: 小规模网站检测
- **检测器**: `CoolmonitorEnhancedChecker`
- **并发数**: 15-20个
- **特性**:
  - ✅ 灵活状态码配置 (`200-299,301,302`)
  - ✅ SSL证书集成检测
  - ✅ 智能重试机制 (重试1次)
  - ✅ 详细错误分类
  - ✅ 8秒连接超时

#### **策略2: Worker Threads高性能模式** (网站数量 ≥ 100)
- **使用场景**: 大规模网站检测
- **检测器**: `WebsiteCheckerService` + coolmonitor逻辑
- **特性**:
  - ✅ 多线程并行处理
  - ✅ 30秒内检测1000个网站
  - ✅ 动态并发控制
  - ✅ Worker内部使用coolmonitor状态码逻辑

## 🔧 检测配置参数

### coolmonitor增强检查器配置
```javascript
{
  concurrency: 15,           // 并发数
  enableSslCheck: true,      // 启用SSL检查
  statusCodes: '200-299,301,302',  // 支持的状态码
  connectTimeout: 8,         // 连接超时(秒)
  retries: 1,               // 重试次数
  retryInterval: 5,         // 重试间隔(秒)
  ignoreTls: true,          // 忽略SSL错误
  httpMethod: 'HEAD'        // HTTP方法
}
```

### Worker Threads配置
```javascript
{
  maxWorkers: CPU核心数,     // 最大工作线程数
  chunkSize: 网站数/线程数,   // 每个线程处理的网站数
  timeout: 8000,            // 请求超时(毫秒)
  method: 'HEAD',           // HTTP方法
  maxRedirects: 3           // 最大重定向次数
}
```

## 📊 数据存储机制

### 🗄️ 数据表结构

#### 1. **website_status_checks** - 检测记录表
```sql
- website_id: 网站ID
- status_code: HTTP状态码
- response_time: 响应时间(毫秒)
- is_accessible: 是否可访问
- error_message: 错误信息
- check_type: 检测类型('scheduled', 'manual')
- created_at: 检测时间
```

#### 2. **website_status_stats** - 状态统计表
```sql
- website_id: 网站ID
- consecutive_failures: 连续失败次数
- total_checks: 总检测次数
- successful_checks: 成功检测次数
- last_success_time: 最后成功时间
- last_failure_time: 最后失败时间
- notification_sent: 是否已发送通知
- notification_count: 通知发送次数
```

#### 3. **websites** - 网站主表(更新字段)
```sql
- access_status: 访问状态('online', 'offline', 'error', 'unknown')
- last_check_time: 最后检测时间
- response_time: 响应时间
- ssl_status: SSL状态('valid', 'invalid', 'expired')
- ssl_expire_date: SSL到期日期
- ssl_days_remaining: SSL剩余天数
```

## 🔔 通知机制

### 📢 通知触发条件
- **连续失败阈值**: 5次连续失败
- **通知去重**: 同一网站24小时内只发送一次通知
- **恢复通知**: 网站恢复正常时发送恢复通知

### 📱 通知渠道
- **飞书机器人**: 主要通知渠道
- **邮件通知**: 备用通知渠道
- **Webhook**: 第三方系统集成

### 📋 通知内容
```
【网站监控告警】
平台类型: {platformType}
网站名称: {siteName}
网站URL: {url}
服务器: {serverName}
错误信息: {errorMessage}
持续时长: {duration}
检测时间: {checkTime}
```

## 🎛️ 手动触发接口

### API端点
- **手动触发网站检测**: `POST /api/v1/websites/trigger-access-check`
- **手动触发SSL检测**: `POST /api/v1/websites/trigger-ssl-check`
- **手动触发服务器更新**: `POST /api/v1/servers/trigger-config-update`

## 📈 性能指标

### 🚀 检测性能
- **小规模检测** (< 100网站): 平均每个网站2-5秒
- **大规模检测** (≥ 100网站): 30秒内完成1000个网站
- **并发能力**: 最高支持50个并发连接
- **内存使用**: Worker Threads模式下内存使用优化

### 📊 系统统计
- **总检测次数**: 实时统计
- **成功率**: 按时间段统计
- **平均响应时间**: 动态计算
- **通知发送数**: 实时记录

## 🔄 系统流程总结

1. **定时触发**: 每5分钟自动触发检测任务
2. **策略选择**: 根据网站数量选择最优检测策略
3. **并发检测**: 使用coolmonitor逻辑进行高效检测
4. **结果处理**: 更新数据库状态和统计信息
5. **通知发送**: 检查失败阈值并发送相应通知
6. **数据清理**: 定期清理过期的检测记录

## 🎯 系统优势

- ✅ **高性能**: Worker Threads + coolmonitor逻辑
- ✅ **高可靠**: 智能重试 + 错误分类
- ✅ **高精度**: 灵活状态码配置 + SSL检测
- ✅ **高扩展**: 模块化架构 + 配置驱动
- ✅ **高可用**: 24/7不间断监控 + 实时通知

这套监控系统结合了coolmonitor的专业监控能力和我们系统的高性能架构，为网站管理提供了企业级的监控解决方案。
