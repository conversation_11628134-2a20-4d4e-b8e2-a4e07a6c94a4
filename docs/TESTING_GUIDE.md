# SiteManager 测试指南

## 📋 测试概述

本文档详细介绍了SiteManager监控系统的测试策略、测试用例和测试执行方法。

## 🧪 测试分类

### 1. 单元测试 (Unit Tests)
测试单个函数或组件的功能。

### 2. 集成测试 (Integration Tests)
测试多个组件之间的交互。

### 3. 功能测试 (Functional Tests)
测试完整的业务功能流程。

### 4. 性能测试 (Performance Tests)
测试系统在各种负载下的性能表现。

### 5. 安全测试 (Security Tests)
测试系统的安全性和权限控制。

## 🔧 测试环境设置

### 环境要求
- Node.js 18+
- MySQL 8.0+
- 测试数据库
- 测试邮箱账号

### 环境配置

```bash
# 复制测试配置
cp backend/.env.example backend/.env.test

# 编辑测试配置
nano backend/.env.test
```

测试环境配置示例：
```env
NODE_ENV=test
DB_HOST=localhost
DB_PORT=3306
DB_USER=test_user
DB_PASSWORD=test_password
DB_NAME=sitemanager_test

# 测试邮箱配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=test_password

# 测试API配置
API_PORT=3002
JWT_SECRET=test_jwt_secret
```

### 测试数据库初始化

```bash
# 创建测试数据库
mysql -u root -p -e "CREATE DATABASE sitemanager_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入测试数据
cd backend
npm run test:setup
```

## 🚀 运行测试

### 运行所有测试

```bash
# 后端测试
cd backend
npm test

# 前端测试
cd frontend
npm test
```

### 运行特定类型的测试

```bash
# 单元测试
npm run test:unit

# 集成测试
npm run test:integration

# 功能测试
npm run test:functional

# 性能测试
npm run test:performance

# 安全测试
npm run test:security
```

### 运行特定测试文件

```bash
# 运行特定测试文件
npm test -- --testPathPattern=monitoring

# 运行特定测试用例
npm test -- --testNamePattern="should check website status"
```

## 📝 测试用例

### 1. 监控系统测试

#### 1.1 网站状态检测测试

**测试目标**: 验证网站状态检测功能的正确性

**测试用例**:

```javascript
describe('Website Status Monitoring', () => {
  test('should detect accessible website', async () => {
    const website = {
      id: 1,
      url: 'https://httpbin.org/status/200',
      timeout: 30000
    };
    
    const result = await monitoringService.checkWebsite(website);
    
    expect(result.isAccessible).toBe(true);
    expect(result.statusCode).toBe(200);
    expect(result.responseTime).toBeGreaterThan(0);
  });
  
  test('should detect inaccessible website', async () => {
    const website = {
      id: 2,
      url: 'https://httpbin.org/status/500',
      timeout: 30000
    };
    
    const result = await monitoringService.checkWebsite(website);
    
    expect(result.isAccessible).toBe(false);
    expect(result.statusCode).toBe(500);
    expect(result.errorMessage).toBeDefined();
  });
  
  test('should handle timeout', async () => {
    const website = {
      id: 3,
      url: 'https://httpbin.org/delay/10',
      timeout: 5000
    };
    
    const result = await monitoringService.checkWebsite(website);
    
    expect(result.isAccessible).toBe(false);
    expect(result.errorMessage).toContain('timeout');
  });
});
```

#### 1.2 SSL证书检测测试

```javascript
describe('SSL Certificate Monitoring', () => {
  test('should detect valid SSL certificate', async () => {
    const website = {
      id: 1,
      url: 'https://google.com'
    };
    
    const result = await monitoringService.checkSSL(website);
    
    expect(result.isValid).toBe(true);
    expect(result.daysRemaining).toBeGreaterThan(0);
    expect(result.issuer).toBeDefined();
  });
  
  test('should detect expired SSL certificate', async () => {
    const website = {
      id: 2,
      url: 'https://expired.badssl.com'
    };
    
    const result = await monitoringService.checkSSL(website);
    
    expect(result.isValid).toBe(false);
    expect(result.daysRemaining).toBeLessThan(0);
  });
});
```

#### 1.3 批量检测测试

```javascript
describe('Batch Monitoring', () => {
  test('should handle batch website checking', async () => {
    const websites = [
      { id: 1, url: 'https://httpbin.org/status/200' },
      { id: 2, url: 'https://httpbin.org/status/404' },
      { id: 3, url: 'https://httpbin.org/status/500' }
    ];
    
    const results = await monitoringService.checkWebsitesBatch(websites);
    
    expect(results).toHaveLength(3);
    expect(results[0].isAccessible).toBe(true);
    expect(results[1].isAccessible).toBe(false);
    expect(results[2].isAccessible).toBe(false);
  });
  
  test('should respect concurrency limits', async () => {
    const websites = Array.from({ length: 50 }, (_, i) => ({
      id: i + 1,
      url: `https://httpbin.org/delay/1`
    }));
    
    const startTime = Date.now();
    const results = await monitoringService.checkWebsitesBatch(websites, { concurrency: 10 });
    const endTime = Date.now();
    
    expect(results).toHaveLength(50);
    expect(endTime - startTime).toBeLessThan(15000); // 应该在15秒内完成
  });
});
```

### 2. 通知系统测试

#### 2.1 邮件通知测试

```javascript
describe('Email Notification', () => {
  test('should send email notification', async () => {
    const notification = {
      type: 'status_alert',
      website: { id: 1, name: '测试网站', url: 'https://example.com' },
      message: '网站无法访问',
      recipients: ['<EMAIL>']
    };
    
    const result = await notificationService.sendEmail(notification);
    
    expect(result.success).toBe(true);
    expect(result.messageId).toBeDefined();
  });
  
  test('should handle email sending failure', async () => {
    const notification = {
      type: 'status_alert',
      website: { id: 1, name: '测试网站' },
      message: '网站无法访问',
      recipients: ['invalid-email']
    };
    
    const result = await notificationService.sendEmail(notification);
    
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });
});
```

#### 2.2 飞书通知测试

```javascript
describe('Feishu Notification', () => {
  test('should send feishu webhook notification', async () => {
    const notification = {
      type: 'status_alert',
      website: { id: 1, name: '测试网站', url: 'https://example.com' },
      message: '网站无法访问'
    };
    
    const result = await notificationService.sendFeishu(notification);
    
    expect(result.success).toBe(true);
  });
});
```

### 3. 权限系统测试

#### 3.1 用户认证测试

```javascript
describe('User Authentication', () => {
  test('should authenticate valid user', async () => {
    const credentials = {
      username: 'admin',
      password: 'admin123'
    };
    
    const result = await authService.login(credentials);
    
    expect(result.success).toBe(true);
    expect(result.token).toBeDefined();
    expect(result.user.role).toBe('super_admin');
  });
  
  test('should reject invalid credentials', async () => {
    const credentials = {
      username: 'admin',
      password: 'wrongpassword'
    };
    
    const result = await authService.login(credentials);
    
    expect(result.success).toBe(false);
    expect(result.error).toContain('Invalid credentials');
  });
});
```

#### 3.2 权限控制测试

```javascript
describe('Permission Control', () => {
  test('should allow admin to access all websites', async () => {
    const user = { id: 1, role: 'admin' };
    const website = { id: 1 };
    
    const hasPermission = await permissionService.checkWebsitePermission(user, website, 'view');
    
    expect(hasPermission).toBe(true);
  });
  
  test('should restrict user to assigned websites only', async () => {
    const user = { id: 2, role: 'user' };
    const website = { id: 1 };
    
    // 用户没有被分配这个网站的权限
    const hasPermission = await permissionService.checkWebsitePermission(user, website, 'view');
    
    expect(hasPermission).toBe(false);
  });
  
  test('should allow user to access assigned websites', async () => {
    const user = { id: 2, role: 'user' };
    const website = { id: 1 };
    
    // 先分配权限
    await permissionService.assignWebsitePermission(user.id, website.id, 'view');
    
    const hasPermission = await permissionService.checkWebsitePermission(user, website, 'view');
    
    expect(hasPermission).toBe(true);
  });
});
```

### 4. 性能测试

#### 4.1 并发检测性能测试

```javascript
describe('Performance Tests', () => {
  test('should handle high concurrency monitoring', async () => {
    const websites = Array.from({ length: 1000 }, (_, i) => ({
      id: i + 1,
      url: `https://httpbin.org/status/200`
    }));
    
    const startTime = Date.now();
    const results = await monitoringService.checkWebsitesBatch(websites, { 
      concurrency: 50,
      timeout: 30000
    });
    const endTime = Date.now();
    
    const duration = endTime - startTime;
    const throughput = websites.length / (duration / 1000);
    
    expect(results).toHaveLength(1000);
    expect(throughput).toBeGreaterThan(10); // 每秒至少检测10个网站
    expect(duration).toBeLessThan(120000); // 2分钟内完成
  });
  
  test('should maintain performance under load', async () => {
    const iterations = 10;
    const websitesPerIteration = 100;
    const durations = [];
    
    for (let i = 0; i < iterations; i++) {
      const websites = Array.from({ length: websitesPerIteration }, (_, j) => ({
        id: j + 1,
        url: `https://httpbin.org/status/200`
      }));
      
      const startTime = Date.now();
      await monitoringService.checkWebsitesBatch(websites);
      const endTime = Date.now();
      
      durations.push(endTime - startTime);
    }
    
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
    const maxDuration = Math.max(...durations);
    
    expect(avgDuration).toBeLessThan(30000); // 平均30秒内完成
    expect(maxDuration).toBeLessThan(60000); // 最大60秒内完成
  });
});
```

#### 4.2 数据库性能测试

```javascript
describe('Database Performance', () => {
  test('should handle large batch inserts efficiently', async () => {
    const checkResults = Array.from({ length: 10000 }, (_, i) => ({
      website_id: (i % 100) + 1,
      status_code: 200,
      response_time: Math.floor(Math.random() * 1000) + 100,
      is_accessible: true,
      check_time: new Date()
    }));
    
    const startTime = Date.now();
    await database.batchInsertCheckResults(checkResults);
    const endTime = Date.now();
    
    const duration = endTime - startTime;
    const throughput = checkResults.length / (duration / 1000);
    
    expect(throughput).toBeGreaterThan(1000); // 每秒至少插入1000条记录
  });
});
```

## 🔍 测试工具和框架

### 后端测试工具
- **Jest**: JavaScript测试框架
- **Supertest**: HTTP断言库
- **Sinon**: 测试间谍、存根和模拟
- **Faker**: 测试数据生成
- **Artillery**: 负载测试工具

### 前端测试工具
- **Jest**: JavaScript测试框架
- **React Testing Library**: React组件测试
- **MSW**: API模拟
- **Cypress**: 端到端测试

### 性能测试工具
- **Artillery**: HTTP负载测试
- **k6**: 现代负载测试工具
- **Clinic.js**: Node.js性能分析

## 📊 测试报告

### 生成测试报告

```bash
# 生成覆盖率报告
npm run test:coverage

# 生成HTML报告
npm run test:report

# 生成性能测试报告
npm run test:performance:report
```

### 测试覆盖率要求

- **单元测试覆盖率**: ≥ 80%
- **集成测试覆盖率**: ≥ 70%
- **功能测试覆盖率**: ≥ 90%

### 性能基准

- **网站检测吞吐量**: ≥ 10 sites/second
- **API响应时间**: ≤ 500ms (95th percentile)
- **数据库查询时间**: ≤ 100ms (平均)
- **内存使用**: ≤ 512MB (正常负载)

## 🚨 故障测试

### 网络故障模拟

```javascript
describe('Network Failure Handling', () => {
  test('should handle DNS resolution failure', async () => {
    const website = {
      id: 1,
      url: 'https://nonexistent-domain-12345.com'
    };
    
    const result = await monitoringService.checkWebsite(website);
    
    expect(result.isAccessible).toBe(false);
    expect(result.errorMessage).toContain('ENOTFOUND');
  });
  
  test('should handle connection timeout', async () => {
    const website = {
      id: 1,
      url: 'https://httpbin.org/delay/30',
      timeout: 5000
    };
    
    const result = await monitoringService.checkWebsite(website);
    
    expect(result.isAccessible).toBe(false);
    expect(result.errorMessage).toContain('timeout');
  });
});
```

### 数据库故障模拟

```javascript
describe('Database Failure Handling', () => {
  test('should handle database connection failure', async () => {
    // 模拟数据库连接失败
    const mockDb = {
      execute: jest.fn().mockRejectedValue(new Error('Connection lost'))
    };
    
    const service = new MonitoringService(mockDb);
    
    await expect(service.updateWebsiteStatus(1, {})).rejects.toThrow('Connection lost');
  });
});
```

## 🔧 持续集成测试

### GitHub Actions配置

```yaml
name: Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: sitemanager_test
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        cd backend && npm ci
        cd ../frontend && npm ci
    
    - name: Run backend tests
      run: cd backend && npm test
      env:
        DB_HOST: 127.0.0.1
        DB_USER: root
        DB_PASSWORD: root
        DB_NAME: sitemanager_test
    
    - name: Run frontend tests
      run: cd frontend && npm test
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v1
```

## 📋 测试检查清单

### 部署前检查

- [ ] 所有单元测试通过
- [ ] 所有集成测试通过
- [ ] 功能测试覆盖主要业务流程
- [ ] 性能测试满足基准要求
- [ ] 安全测试通过
- [ ] 数据库迁移测试通过
- [ ] 配置文件验证通过
- [ ] 日志记录正常
- [ ] 错误处理测试通过
- [ ] 权限控制测试通过

### 生产环境验证

- [ ] 健康检查端点响应正常
- [ ] 监控服务正常启动
- [ ] 通知服务配置正确
- [ ] 数据库连接正常
- [ ] 日志输出正常
- [ ] 性能指标在正常范围内
- [ ] 用户认证功能正常
- [ ] 权限控制生效
- [ ] 备份和恢复流程测试

---

通过完整的测试流程，确保SiteManager监控系统的稳定性、可靠性和性能表现。
