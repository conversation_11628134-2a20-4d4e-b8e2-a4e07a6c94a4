#!/bin/bash

echo "🛑 停止SiteManager全栈服务..."

# 停止后端
if [ -f /var/run/sitemanager-backend.pid ]; then
    BACKEND_PID=$(cat /var/run/sitemanager-backend.pid)
    if kill -0 $BACKEND_PID 2>/dev/null; then
        kill $BACKEND_PID
        echo "✅ 后端服务已停止"
    fi
    rm -f /var/run/sitemanager-backend.pid
fi

# 停止前端
if [ -f /var/run/sitemanager-frontend.pid ]; then
    FRONTEND_PID=$(cat /var/run/sitemanager-frontend.pid)
    if kill -0 $FRONTEND_PID 2>/dev/null; then
        kill $FRONTEND_PID
        echo "✅ 前端服务已停止"
    fi
    rm -f /var/run/sitemanager-frontend.pid
fi

# 清理可能残留的进程
pkill -f "node.*simple-server.js" 2>/dev/null || true
pkill -f "node backend/simple-server.js" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true

echo "🎉 全栈服务已完全停止"
