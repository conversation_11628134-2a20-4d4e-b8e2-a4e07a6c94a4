<!DOCTYPE html>
<html>

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
    <title>忆星辰 | 博客のSSL透明度报告</title>
   <link rel="apple-touch-icon" sizes="76x76" href="https://es-blogimg.oss-cn-hangzhou.aliyuncs.com/img/avatar/favicon-cicle.png?x-oss-process=style/WebSiteCover">
<link rel="icon" type="image/png" href="https://es-blogimg.oss-cn-hangzhou.aliyuncs.com/img/avatar/favicon-cicle.png?x-oss-process=style/WebSiteCover">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no">
<meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="description" content="忆星辰 | 博客のSSL透明度报告">
<meta name="theme-color" content="#F6B352">
<meta name="author" content="CYF">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/semantic-ui@2.3.1/dist/semantic.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/suka.css@0.1.2">
    <style>
        .item-title {
            color: #adadad
        }

        .ui.table tr td,
        .ui.table,
        .ui.table tbody {
            border: none;
        }

        .ui.table tr td {
            padding: .3rem;
            line-height: 1.2rem;
        }
    </style>
    <!--[if lt IE 9]><script src="https://cdn.jsdelivr.net/npm/html5shiv@3.7.3"></script><script src="https://cdn.jsdelivr.net/npm/respond.js@1.4.2/dest/respond.min.js"></script><![endif]-->
    <meta property="og:title" content="SSL Status - SukkaLab">
    <meta property="og:type" content="Website">
    <meta property="og:url" content="https://ssl.extingstudio.com">
    <meta name="twitter:card" content="summary">
</head>

<body class="sk-p-4 sk-pt-8 sk-pb-6">
    <h1 class="ui center aligned header">
        <i id="icon" class="expeditedssl icon" data-position="left center" style="color: #009688;"></i>SSL Status | SSL证书状态</h1>
    <div class="ui container sk-pt-4">
        <div class="ui container two column stackable grid container" id="result"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery@3.3.1"></script>
    <script src="https://cdn.jsdelivr.net/npm/semantic-ui@2.3.1/dist/semantic.min.js"></script>
    <script type="text/javascript">
        var ctJson = "./ct.json?" + new Date().getTime() + Math.random()
        $.getJSON(ctJson, function (data) {
            $.each(data, function (index, value) {
                $("#result").append(`
                    <div class="column" v-for="item in items">
                        <div class="ui segment">
                            <h3 class="ui floated header sk-pl-2">${value.domain}&nbsp;&nbsp;
                                <small class="sk-text-${value.statuscolor}">${value.status}</small>
                            </h3>
                            <div class="ui clearing divider"></div>
                            <div class="sk-pl-2">
                                <table class="ui collapsing table unstackable">
                                    <tbody>
                                        <tr>
                                            <td class="item-title sk-text-right">Last check</td>
                                            <td>${value.check}</td>
                                        </tr>
                                        <tr>
                                            <td class="item-title sk-text-right">Subject</td>
                                            <td>${value.subject}</td>
                                        </tr>
                                        <tr>
                                            <td class="item-title sk-text-right">Valid from</td>
                                            <td>${value.start}</td>
                                        </tr>
                                        <tr>
                                            <td class="item-title sk-text-right">Valid until</td>
                                            <td>${value.expire}</td>
                                        </tr>
                                        <tr>
                                            <td class="item-title sk-text-right">Remaining</td>
                                            <td>${value.remain} Days</td>
                                        </tr>
                                        <tr>
                                            <td class="item-title sk-text-right">Issuer</td>
                                            <td>${value.issuer}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                `);
            });
        });
    </script>
</body>

</html>
