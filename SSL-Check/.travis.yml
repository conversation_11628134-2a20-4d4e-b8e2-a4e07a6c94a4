language: node_js
dist: trusty
node_js:
  - "10"
install:
  - npm i
  - git clone https://github.com/ChenYFan/CheckSSL --depth=2
script:
  - npm run build
  - cd CheckSSL
  - chmod +x checker.sh
  - ./checker.sh renewx.extingstudio.com extingstudio.com www.extingstudio.com monitor.extingstudio.com monitor.extingstudio.ga extingstudio.ga ssl.extingstudio.com status.extingstudio.com blog.extingstudio.com game.extingstudio.com api-artitalk.extingstudio.com traffic.extingstudio.com editor.extingstudio.com eu.extingstudio.com donate.extingstudio.com leancloud.extingstudio.com
  - cp -rf ./output/ct.json ../public/ct.json
  - echo "ssl.extingstudio.com" > ../public/CNAME
branches:
  only:
    - master
cache:
  directories:
    - node_modules
deploy:
  provider: pages
  skip-cleanup: true
  github-token: $GITHUB_TOKEN
  target_branch: gh-pages
  email: $GITHUB_EMAIL
  name: $GITHUB_USER
  local-dir: public
  on:
    branch: master
