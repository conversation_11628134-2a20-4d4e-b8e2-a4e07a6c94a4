/**
 * 第二步：统一权限检查中间件测试 - 简化版
 * 使用原生Node.js和fetch API进行测试
 */

const https = require('https');
const http = require('http');

// 测试配置
const BASE_URL = 'http://localhost:3001';
const TEST_USERS = {
  superAdmin: { username: 'admin', password: 'admin123' },
  normalUser: { username: 'user', password: 'user123' }
};

// 简单的HTTP请求函数
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    };

    const client = urlObj.protocol === 'https:' ? https : http;
    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    req.end();
  });
}

// 获取认证令牌
async function getAuthToken(username, password) {
  const response = await makeRequest(`${BASE_URL}/api/v1/auth/login`, {
    method: 'POST',
    body: { username, password }
  });
  
  if (response.data.success) {
    return response.data.data.token;
  }
  throw new Error(`登录失败: ${response.data.message}`);
}

// 测试函数
async function runTests() {
  console.log('🚀 开始第二步：统一权限检查中间件测试\n');
  
  try {
    // 获取认证令牌
    console.log('📝 获取认证令牌...');
    const adminToken = await getAuthToken(TEST_USERS.superAdmin.username, TEST_USERS.superAdmin.password);
    const userToken = await getAuthToken(TEST_USERS.normalUser.username, TEST_USERS.normalUser.password);
    console.log('✅ 认证令牌获取成功\n');

    // 测试1：第一组简单权限检查替换
    console.log('🔍 测试1：第一组简单权限检查替换');
    
    // 测试用户列表API
    const usersListResponse = await makeRequest(`${BASE_URL}/api/v1/users/list`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log(`   用户列表API: ${usersListResponse.status === 200 ? '✅ 通过' : '❌ 失败'} (状态码: ${usersListResponse.status})`);
    
    // 测试权限列表API
    const permissionsResponse = await makeRequest(`${BASE_URL}/api/v1/permissions`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log(`   权限列表API: ${permissionsResponse.status === 200 ? '✅ 通过' : '❌ 失败'} (状态码: ${permissionsResponse.status})`);
    
    // 测试网站列表API
    const websitesResponse = await makeRequest(`${BASE_URL}/api/v1/websites`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log(`   网站列表API: ${websitesResponse.status === 200 ? '✅ 通过' : '❌ 失败'} (状态码: ${websitesResponse.status})\n`);

    // 测试2：组合权限检查
    console.log('🔍 测试2：组合权限检查');
    
    // 管理员访问用户详情
    const adminUserDetailResponse = await makeRequest(`${BASE_URL}/api/v1/users/17`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log(`   管理员访问用户详情: ${adminUserDetailResponse.status === 200 ? '✅ 通过' : '❌ 失败'} (状态码: ${adminUserDetailResponse.status})`);
    
    // 用户访问自己的详情
    const selfDetailResponse = await makeRequest(`${BASE_URL}/api/v1/users/17`, {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    console.log(`   用户访问自己详情: ${selfDetailResponse.status === 200 ? '✅ 通过' : '❌ 失败'} (状态码: ${selfDetailResponse.status})`);
    
    // 用户访问其他用户详情（应该被拒绝）
    const otherUserResponse = await makeRequest(`${BASE_URL}/api/v1/users/1`, {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    console.log(`   用户访问其他用户详情: ${otherUserResponse.status === 403 ? '✅ 正确拒绝' : '❌ 失败'} (状态码: ${otherUserResponse.status})\n`);

    // 测试3：特殊逻辑保留
    console.log('🔍 测试3：特殊逻辑保留');
    
    // 管理员获取自己权限
    const adminAuthPermResponse = await makeRequest(`${BASE_URL}/api/v1/auth/permissions`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    console.log(`   管理员获取自己权限: ${adminAuthPermResponse.status === 200 ? '✅ 通过' : '❌ 失败'} (状态码: ${adminAuthPermResponse.status})`);
    
    // 普通用户获取自己权限
    const userAuthPermResponse = await makeRequest(`${BASE_URL}/api/v1/auth/permissions`, {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    console.log(`   普通用户获取自己权限: ${userAuthPermResponse.status === 200 ? '✅ 通过' : '❌ 失败'} (状态码: ${userAuthPermResponse.status})\n`);

    // 测试4：权限拒绝测试
    console.log('🔍 测试4：权限拒绝测试');
    
    // 普通用户访问权限列表（应该被拒绝）
    const userPermissionsResponse = await makeRequest(`${BASE_URL}/api/v1/permissions`, {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    console.log(`   普通用户访问权限列表: ${userPermissionsResponse.status === 403 ? '✅ 正确拒绝' : '❌ 失败'} (状态码: ${userPermissionsResponse.status})`);
    
    // 普通用户访问用户列表（应该被拒绝）
    const userUsersListResponse = await makeRequest(`${BASE_URL}/api/v1/users/list`, {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    console.log(`   普通用户访问用户列表: ${userUsersListResponse.status === 403 ? '✅ 正确拒绝' : '❌ 失败'} (状态码: ${userUsersListResponse.status})\n`);

    // 测试5：权限一致性验证
    console.log('🔍 测试5：权限一致性验证');
    
    if (adminAuthPermResponse.status === 200 && adminAuthPermResponse.data.success) {
      const authPermissions = adminAuthPermResponse.data.data.effectivePermissions;
      
      // 获取用户权限API的权限信息
      const userPermResponse = await makeRequest(`${BASE_URL}/api/v1/users/1/permissions`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      
      if (userPermResponse.status === 200 && userPermResponse.data.success) {
        const userPermissions = userPermResponse.data.data.effectivePermissions;
        
        const isConsistent = authPermissions.length === userPermissions.length;
        console.log(`   权限数量一致性: ${isConsistent ? '✅ 通过' : '❌ 失败'} (auth: ${authPermissions.length}, user: ${userPermissions.length})`);
        
        if (isConsistent) {
          const authPermSet = new Set(authPermissions);
          const userPermSet = new Set(userPermissions);
          
          let contentConsistent = true;
          for (const perm of authPermissions) {
            if (!userPermSet.has(perm)) {
              contentConsistent = false;
              break;
            }
          }
          
          console.log(`   权限内容一致性: ${contentConsistent ? '✅ 通过' : '❌ 失败'}`);
        }
      } else {
        console.log(`   获取用户权限失败: ❌ 失败 (状态码: ${userPermResponse.status})`);
      }
    } else {
      console.log(`   获取认证权限失败: ❌ 失败 (状态码: ${adminAuthPermResponse.status})`);
    }

    console.log('\n🎉 第二步：统一权限检查中间件测试完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
