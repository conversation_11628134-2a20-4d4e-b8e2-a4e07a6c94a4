/**
 * 增强版监控系统测试脚本
 */

const mysql = require('mysql2/promise');

async function testEnhancedMonitoring() {
  console.log('🧪 开始测试增强版监控系统...\n');
  
  try {
    // 连接数据库
    const db = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root123',
      database: 'sitemanager',
      charset: 'utf8mb4'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 1. 检查状态检测表
    console.log('\n📋 检查状态检测表...');
    
    const [statusCheckStats] = await db.execute(`
      SELECT 
        COUNT(*) as total_sites,
        SUM(CASE WHEN error_count = 0 THEN 1 ELSE 0 END) as healthy_sites,
        SUM(CASE WHEN error_count >= 3 THEN 1 ELSE 0 END) as failed_sites,
        SUM(CASE WHEN error_count > 0 AND error_count < 3 THEN 1 ELSE 0 END) as warning_sites,
        AVG(response_time) as avg_response_time,
        MAX(last_check_time) as last_check_time
      FROM status_check
    `);
    
    const stats = statusCheckStats[0];
    console.log(`✅ 状态检测表统计:`);
    console.log(`   总站点数: ${stats.total_sites}`);
    console.log(`   健康站点: ${stats.healthy_sites} (错误次数=0)`);
    console.log(`   警告站点: ${stats.warning_sites} (错误次数1-2)`);
    console.log(`   故障站点: ${stats.failed_sites} (错误次数>=3)`);
    console.log(`   平均响应时间: ${Math.round(stats.avg_response_time || 0)}ms`);
    console.log(`   最后检测时间: ${stats.last_check_time || '未检测'}`);
    
    // 2. 检查故障站点详情
    console.log('\n🚨 故障站点详情 (错误次数>=3)...');
    
    const [failedSites] = await db.execute(`
      SELECT * FROM v_failed_sites LIMIT 10
    `);
    
    if (failedSites.length > 0) {
      console.log(`⚠️  发现 ${failedSites.length} 个故障站点:`);
      failedSites.forEach((site, index) => {
        console.log(`   ${index + 1}. ${site.site_name || site.domain}`);
        console.log(`      域名: ${site.domain}`);
        console.log(`      状态码: ${site.status_code}`);
        console.log(`      错误次数: ${site.error_count}`);
        console.log(`      服务器: ${site.server_name || '未知'}`);
        console.log(`      持续时间: ${site.failure_duration_text}`);
        console.log(`      最后检测: ${site.last_check_time}`);
        console.log('');
      });
    } else {
      console.log('✅ 没有发现故障站点');
    }
    
    // 3. 检查最近检测活动
    console.log('\n📊 最近检测活动...');
    
    const [recentActivity] = await db.execute(`
      SELECT 
        COUNT(*) as total_checks,
        COUNT(DISTINCT platform_id) as checked_sites,
        MIN(last_check_time) as earliest_check,
        MAX(last_check_time) as latest_check
      FROM status_check 
      WHERE last_check_time >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
    `);
    
    if (recentActivity[0].total_checks > 0) {
      const activity = recentActivity[0];
      console.log(`✅ 最近10分钟检测活动:`);
      console.log(`   检测次数: ${activity.total_checks}`);
      console.log(`   涉及站点: ${activity.checked_sites}`);
      console.log(`   最早检测: ${activity.earliest_check}`);
      console.log(`   最新检测: ${activity.latest_check}`);
    } else {
      console.log('ℹ️  最近10分钟内没有检测活动');
    }
    
    // 4. 测试存储过程
    console.log('\n🔧 测试存储过程...');
    
    try {
      // 测试获取故障站点通知
      const [notificationSites] = await db.execute('CALL GetFailedSitesForNotification()');
      console.log(`✅ 需要通知的故障站点: ${notificationSites.length} 个`);
      
      if (notificationSites.length > 0) {
        console.log('   故障站点列表:');
        notificationSites.slice(0, 5).forEach(site => {
          console.log(`   - ${site.site_name || site.domain}: 错误${site.error_count}次`);
        });
      }
    } catch (error) {
      console.log(`❌ 存储过程测试失败: ${error.message}`);
    }
    
    // 5. 检查监控API
    console.log('\n🔗 测试监控API...');
    
    try {
      // 测试状态API
      const statusResponse = await fetch('http://localhost:3002/status');
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        console.log('✅ 监控状态API正常');
        console.log(`   服务运行: ${statusData.data.isRunning ? '是' : '否'}`);
        console.log(`   运行时间: ${Math.floor(statusData.data.uptime / 60)}分钟`);
        
        const monitorStats = statusData.data.stats;
        console.log(`   总检测: ${monitorStats.totalChecks}`);
        console.log(`   成功: ${monitorStats.successfulChecks}`);
        console.log(`   失败: ${monitorStats.failedChecks}`);
        console.log(`   重试队列: ${monitorStats.retryQueueSize || 0}`);
      } else {
        console.log('❌ 监控状态API响应异常');
      }
      
      // 测试统计API
      const statsResponse = await fetch('http://localhost:3002/stats');
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        console.log('✅ 监控统计API正常');
        
        const status = statsData.data.status;
        console.log(`   健康站点: ${status.healthy_sites}/${status.total_sites}`);
        console.log(`   故障站点: ${status.failed_sites}`);
        console.log(`   警告站点: ${status.warning_sites}`);
      } else {
        console.log('❌ 监控统计API响应异常');
      }
      
    } catch (error) {
      console.log(`❌ API测试失败: ${error.message}`);
    }
    
    // 6. 检查检测逻辑配置
    console.log('\n⚙️  检测逻辑配置验证...');
    
    console.log('✅ 新检测逻辑特性:');
    console.log('   🕐 主检测间隔: 5分钟');
    console.log('   🔄 重试间隔: 10秒');
    console.log('   📤 通知检查间隔: 5分钟');
    console.log('   ⚠️  故障阈值: 错误次数>=3');
    console.log('   🔁 最大重试次数: 5次');
    console.log('');
    console.log('✅ 检测流程:');
    console.log('   1. 状态正常 → 直接写入状态检测表，错误次数清零');
    console.log('   2. 状态异常 → 加入重试队列，每10秒重试一次');
    console.log('   3. 重试成功 → 错误次数清零，移出重试队列');
    console.log('   4. 重试失败 → 错误次数+1，继续重试');
    console.log('   5. 达到阈值 → 每5分钟检查一次并发送通知');
    
    await db.end();
    
    console.log('\n🎉 增强版监控系统测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
testEnhancedMonitoring();
