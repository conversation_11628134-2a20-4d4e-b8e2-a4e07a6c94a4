#!/usr/bin/env node

/**
 * 服务器删除功能测试脚本
 * 测试前端和后端的服务器删除功能是否正常工作
 */

const axios = require('axios');

const API_BASE = 'http://localhost:3001/api/v1';

async function testServerDelete() {
  console.log('🧪 开始测试服务器删除功能...\n');

  try {
    // 1. 获取当前服务器列表
    console.log('1️⃣ 获取当前服务器列表...');
    const listResponse = await axios.get(`${API_BASE}/servers`);
    
    if (!listResponse.data.success) {
      throw new Error('获取服务器列表失败');
    }

    const servers = listResponse.data.data.servers;
    const initialCount = servers.length;
    console.log(`   ✅ 当前服务器数量: ${initialCount}`);

    if (initialCount === 0) {
      console.log('   ⚠️  没有服务器可以删除，测试结束');
      return;
    }

    // 2. 选择一个测试服务器进行删除（选择最后一个）
    const testServer = servers[servers.length - 1];
    console.log(`   📋 选择删除服务器: ${testServer.name} (ID: ${testServer.id})`);

    // 3. 执行删除操作
    console.log('\n2️⃣ 执行删除操作...');
    const deleteResponse = await axios.delete(`${API_BASE}/servers/${testServer.id}`);
    
    if (!deleteResponse.data.success) {
      throw new Error('删除服务器失败: ' + deleteResponse.data.message);
    }

    console.log(`   ✅ 删除成功: ${deleteResponse.data.message}`);

    // 4. 验证删除结果
    console.log('\n3️⃣ 验证删除结果...');
    const verifyResponse = await axios.get(`${API_BASE}/servers`);
    
    if (!verifyResponse.data.success) {
      throw new Error('验证删除结果失败');
    }

    const newServers = verifyResponse.data.data.servers;
    const newCount = newServers.length;
    
    console.log(`   📊 删除前服务器数量: ${initialCount}`);
    console.log(`   📊 删除后服务器数量: ${newCount}`);

    if (newCount === initialCount - 1) {
      console.log('   ✅ 删除验证成功！服务器数量正确减少');
    } else {
      throw new Error(`删除验证失败！期望数量: ${initialCount - 1}, 实际数量: ${newCount}`);
    }

    // 5. 验证被删除的服务器不再存在
    const deletedServerExists = newServers.some(s => s.id === testServer.id);
    if (deletedServerExists) {
      throw new Error('删除验证失败！被删除的服务器仍然存在');
    }

    console.log('   ✅ 被删除的服务器已从列表中移除');

    // 6. 尝试获取已删除的服务器（应该返回404）
    console.log('\n4️⃣ 验证已删除服务器不可访问...');
    try {
      await axios.get(`${API_BASE}/servers/${testServer.id}`);
      throw new Error('删除验证失败！已删除的服务器仍可访问');
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('   ✅ 已删除的服务器正确返回404错误');
      } else {
        throw error;
      }
    }

    console.log('\n🎉 服务器删除功能测试全部通过！');
    console.log('✅ 后端删除API正常工作');
    console.log('✅ 数据库删除操作成功');
    console.log('✅ 删除后数据一致性正确');

  } catch (error) {
    console.error('\n❌ 测试失败:', error.message);
    if (error.response) {
      console.error('   响应状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }
    process.exit(1);
  }
}

// 运行测试
testServerDelete();
