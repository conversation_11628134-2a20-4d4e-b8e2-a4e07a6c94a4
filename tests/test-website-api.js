// 测试网站API的脚本
const axios = require('axios');

const API_BASE = 'http://localhost:3001/api/v1';

async function testCreateWebsite() {
  console.log('🧪 测试创建网站API...');
  
  const testData = {
    siteName: '测试博客网站',
    siteUrl: 'https://test-blog.example.com',
    platformId: 1,
    serverId: 1,
    status: 'active',
    onlineDate: '2024-06-16',
    expireDate: '2025-06-16'
  };

  try {
    const response = await axios.post(`${API_BASE}/websites`, testData);
    console.log('✅ 创建成功:', response.data);
    return response.data.data.id;
  } catch (error) {
    console.error('❌ 创建失败:', error.response?.data || error.message);
    return null;
  }
}

async function testGetWebsites() {
  console.log('\n🧪 测试获取网站列表API...');
  
  try {
    const response = await axios.get(`${API_BASE}/websites`);
    console.log('✅ 获取成功，网站数量:', response.data.data.length);
    console.log('网站列表:');
    response.data.data.forEach(site => {
      console.log(`  - ${site.siteName || site.domain} (${site.siteUrl})`);
    });
    return response.data.data;
  } catch (error) {
    console.error('❌ 获取失败:', error.response?.data || error.message);
    return [];
  }
}

async function testUpdateWebsite(id) {
  if (!id) return;
  
  console.log('\n🧪 测试更新网站API...');
  
  const updateData = {
    siteName: '更新后的博客网站',
    siteUrl: 'https://updated-blog.example.com',
    status: 'active'
  };

  try {
    const response = await axios.put(`${API_BASE}/websites/${id}`, updateData);
    console.log('✅ 更新成功:', response.data);
  } catch (error) {
    console.error('❌ 更新失败:', error.response?.data || error.message);
  }
}

async function runTests() {
  console.log('🚀 开始测试网站API功能...\n');
  
  // 测试获取现有网站
  await testGetWebsites();
  
  // 测试创建新网站
  const newId = await testCreateWebsite();
  
  // 再次获取网站列表
  await testGetWebsites();
  
  // 测试更新网站
  if (newId) {
    await testUpdateWebsite(newId);
    await testGetWebsites();
  }
  
  console.log('\n🎉 测试完成！');
}

// 运行测试
runTests().catch(console.error);
