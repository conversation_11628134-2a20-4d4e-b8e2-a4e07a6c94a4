/**
 * 第二步：统一权限检查中间件测试
 * 验证所有API端点都使用统一的权限中间件
 */

const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:3001';
const TEST_USERS = {
  superAdmin: { username: 'admin', password: 'admin123' },
  normalUser: { username: 'user', password: 'user123' }
};

// 获取认证令牌
async function getAuthToken(page, username, password) {
  const response = await page.request.post(`${BASE_URL}/api/v1/auth/login`, {
    data: { username, password }
  });
  
  const result = await response.json();
  if (result.success) {
    return result.data.token;
  }
  throw new Error(`登录失败: ${result.message}`);
}

test.describe('第二步：统一权限检查中间件测试', () => {
  
  test('1. 第一组：简单权限检查替换验证', async ({ page }) => {
    console.log('🔍 测试第一组：简单权限检查替换');
    
    // 获取超级管理员令牌
    const adminToken = await getAuthToken(page, TEST_USERS.superAdmin.username, TEST_USERS.superAdmin.password);
    
    // 测试用户列表API (user.list.view)
    console.log('测试 /api/v1/users/list');
    const usersListResponse = await page.request.get(`${BASE_URL}/api/v1/users/list`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    expect(usersListResponse.status()).toBe(200);
    const usersListData = await usersListResponse.json();
    expect(usersListData.success).toBe(true);
    console.log('✅ 用户列表API权限检查通过');
    
    // 测试带权限的用户列表API (user.list.view)
    console.log('测试 /api/v1/users/with-permissions');
    const usersWithPermResponse = await page.request.get(`${BASE_URL}/api/v1/users/with-permissions`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    expect(usersWithPermResponse.status()).toBe(200);
    const usersWithPermData = await usersWithPermResponse.json();
    expect(usersWithPermData.success).toBe(true);
    console.log('✅ 带权限用户列表API权限检查通过');
    
    // 测试权限列表API (system.permission.manage)
    console.log('测试 /api/v1/permissions');
    const permissionsResponse = await page.request.get(`${BASE_URL}/api/v1/permissions`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    expect(permissionsResponse.status()).toBe(200);
    const permissionsData = await permissionsResponse.json();
    expect(permissionsData.success).toBe(true);
    console.log('✅ 权限列表API权限检查通过');
    
    // 测试网站列表API (site.list.view)
    console.log('测试 /api/v1/websites');
    const websitesResponse = await page.request.get(`${BASE_URL}/api/v1/websites`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    expect(websitesResponse.status()).toBe(200);
    const websitesData = await websitesResponse.json();
    expect(websitesData.success).toBe(true);
    console.log('✅ 网站列表API权限检查通过');
  });
  
  test('2. 第二组：组合权限检查验证', async ({ page }) => {
    console.log('🔍 测试第二组：组合权限检查');
    
    // 获取超级管理员令牌
    const adminToken = await getAuthToken(page, TEST_USERS.superAdmin.username, TEST_USERS.superAdmin.password);
    
    // 测试用户详情API - 管理员访问其他用户
    console.log('测试 /api/v1/users/:id (管理员访问)');
    const userDetailResponse = await page.request.get(`${BASE_URL}/api/v1/users/17`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    expect(userDetailResponse.status()).toBe(200);
    const userDetailData = await userDetailResponse.json();
    expect(userDetailData.success).toBe(true);
    console.log('✅ 用户详情API管理员权限检查通过');
    
    // 测试用户本人访问自己的信息
    console.log('测试用户本人访问自己的信息');
    const userToken = await getAuthToken(page, TEST_USERS.normalUser.username, TEST_USERS.normalUser.password);
    
    const selfDetailResponse = await page.request.get(`${BASE_URL}/api/v1/users/17`, {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    expect(selfDetailResponse.status()).toBe(200);
    const selfDetailData = await selfDetailResponse.json();
    expect(selfDetailData.success).toBe(true);
    console.log('✅ 用户本人访问权限检查通过');
    
    // 测试用户访问其他用户信息（应该被拒绝）
    console.log('测试用户访问其他用户信息（应该被拒绝）');
    const otherUserResponse = await page.request.get(`${BASE_URL}/api/v1/users/1`, {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    expect(otherUserResponse.status()).toBe(403);
    const otherUserData = await otherUserResponse.json();
    expect(otherUserData.success).toBe(false);
    console.log('✅ 用户访问其他用户被正确拒绝');
  });
  
  test('3. 第三组：特殊逻辑保留验证', async ({ page }) => {
    console.log('🔍 测试第三组：特殊逻辑保留');
    
    // 获取超级管理员令牌
    const adminToken = await getAuthToken(page, TEST_USERS.superAdmin.username, TEST_USERS.superAdmin.password);
    
    // 测试获取当前用户权限API（应该允许所有认证用户）
    console.log('测试 /api/v1/auth/permissions');
    const authPermResponse = await page.request.get(`${BASE_URL}/api/v1/auth/permissions`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    expect(authPermResponse.status()).toBe(200);
    const authPermData = await authPermResponse.json();
    expect(authPermData.success).toBe(true);
    expect(authPermData.data.effectivePermissions).toBeDefined();
    console.log('✅ 获取当前用户权限API正常工作');
    
    // 测试普通用户也能获取自己的权限
    const userToken = await getAuthToken(page, TEST_USERS.normalUser.username, TEST_USERS.normalUser.password);
    const userAuthPermResponse = await page.request.get(`${BASE_URL}/api/v1/auth/permissions`, {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    expect(userAuthPermResponse.status()).toBe(200);
    const userAuthPermData = await userAuthPermResponse.json();
    expect(userAuthPermData.success).toBe(true);
    expect(userAuthPermData.data.effectivePermissions).toBeDefined();
    console.log('✅ 普通用户获取自己权限正常工作');
  });
  
  test('4. 权限拒绝测试', async ({ page }) => {
    console.log('🔍 测试权限拒绝情况');
    
    // 获取普通用户令牌
    const userToken = await getAuthToken(page, TEST_USERS.normalUser.username, TEST_USERS.normalUser.password);
    
    // 测试普通用户访问需要管理员权限的API
    console.log('测试普通用户访问权限列表（应该被拒绝）');
    const permissionsResponse = await page.request.get(`${BASE_URL}/api/v1/permissions`, {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    expect(permissionsResponse.status()).toBe(403);
    const permissionsData = await permissionsResponse.json();
    expect(permissionsData.success).toBe(false);
    console.log('✅ 普通用户访问权限列表被正确拒绝');
    
    // 测试普通用户访问用户列表（应该被拒绝）
    console.log('测试普通用户访问用户列表（应该被拒绝）');
    const usersResponse = await page.request.get(`${BASE_URL}/api/v1/users/list`, {
      headers: { 'Authorization': `Bearer ${userToken}` }
    });
    expect(usersResponse.status()).toBe(403);
    const usersData = await usersResponse.json();
    expect(usersData.success).toBe(false);
    console.log('✅ 普通用户访问用户列表被正确拒绝');
  });
  
  test('5. 权限一致性验证', async ({ page }) => {
    console.log('🔍 测试权限计算一致性');
    
    // 获取超级管理员令牌
    const adminToken = await getAuthToken(page, TEST_USERS.superAdmin.username, TEST_USERS.superAdmin.password);
    
    // 获取权限API的权限信息
    const authPermResponse = await page.request.get(`${BASE_URL}/api/v1/auth/permissions`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    const authPermData = await authPermResponse.json();
    const authPermissions = authPermData.data.effectivePermissions;
    
    // 获取用户权限API的权限信息
    const userPermResponse = await page.request.get(`${BASE_URL}/api/v1/users/1/permissions`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    const userPermData = await userPermResponse.json();
    const userPermissions = userPermData.data.effectivePermissions;
    
    // 验证两个API返回的权限是否一致
    expect(authPermissions.length).toBe(userPermissions.length);
    console.log(`✅ 权限数量一致: ${authPermissions.length} 个权限`);
    
    // 验证权限内容是否一致
    const authPermSet = new Set(authPermissions);
    const userPermSet = new Set(userPermissions);
    
    for (const perm of authPermissions) {
      expect(userPermSet.has(perm)).toBe(true);
    }
    
    for (const perm of userPermissions) {
      expect(authPermSet.has(perm)).toBe(true);
    }
    
    console.log('✅ 权限内容完全一致，权限计算统一化成功');
  });
});
