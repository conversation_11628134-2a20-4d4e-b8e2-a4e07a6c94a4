/**
 * 测试增强的API缓存功能
 */

const express = require('express');
const { websiteListCache, clearWebsiteCache, getCacheStats } = require('./backend/middleware/cacheMiddleware');

const app = express();
const PORT = 3002;

// 模拟网站数据
const mockWebsites = [
    { id: 1, name: '测试网站1', url: 'https://example1.com', status: 'active' },
    { id: 2, name: '测试网站2', url: 'https://example2.com', status: 'active' },
    { id: 3, name: '测试网站3', url: 'https://example3.com', status: 'inactive' }
];

// 应用缓存中间件到网站列表API
app.get('/api/v1/websites', websiteListCache, (req, res) => {
    console.log('🔄 处理网站列表请求...');
    
    // 模拟数据库查询延迟
    setTimeout(() => {
        res.json({
            success: true,
            data: mockWebsites,
            total: mockWebsites.length,
            timestamp: new Date().toISOString()
        });
    }, 100); // 100ms延迟模拟数据库查询
});

// 缓存统计API
app.get('/api/cache/stats', async (req, res) => {
    const stats = await getCacheStats();
    res.json(stats);
});

// 清除缓存API
app.post('/api/cache/clear', async (req, res) => {
    await clearWebsiteCache();
    res.json({ success: true, message: '缓存已清除' });
});

// 启动测试服务器
app.listen(PORT, () => {
    console.log(`🧪 测试服务器启动在端口 ${PORT}`);
    console.log(`访问 http://localhost:${PORT}/api/v1/websites 测试缓存功能`);
    console.log(`访问 http://localhost:${PORT}/api/cache/stats 查看缓存统计`);
    console.log(`POST http://localhost:${PORT}/api/cache/clear 清除缓存`);
});

// 自动测试
setTimeout(async () => {
    console.log('\n🧪 开始自动测试...');
    
    const axios = require('axios');
    const baseUrl = `http://localhost:${PORT}`;
    
    try {
        // 第一次请求（缓存未命中）
        console.log('\n1. 第一次请求（应该缓存未命中）...');
        const start1 = Date.now();
        const response1 = await axios.get(`${baseUrl}/api/v1/websites`);
        const time1 = Date.now() - start1;
        console.log(`响应时间: ${time1}ms`);
        console.log(`数据: ${JSON.stringify(response1.data, null, 2)}`);
        
        // 第二次请求（缓存命中）
        console.log('\n2. 第二次请求（应该缓存命中）...');
        const start2 = Date.now();
        const response2 = await axios.get(`${baseUrl}/api/v1/websites`);
        const time2 = Date.now() - start2;
        console.log(`响应时间: ${time2}ms`);
        console.log(`缓存效果: ${time1 > time2 ? '✅ 缓存生效，响应更快' : '⚠️ 缓存可能未生效'}`);
        
        // 查看缓存统计
        console.log('\n3. 查看缓存统计...');
        const statsResponse = await axios.get(`${baseUrl}/api/cache/stats`);
        console.log(`缓存统计: ${JSON.stringify(statsResponse.data, null, 2)}`);
        
        // 清除缓存
        console.log('\n4. 清除缓存...');
        await axios.post(`${baseUrl}/api/cache/clear`);
        console.log('缓存已清除');
        
        // 清除缓存后再次请求
        console.log('\n5. 清除缓存后再次请求（应该缓存未命中）...');
        const start3 = Date.now();
        const response3 = await axios.get(`${baseUrl}/api/v1/websites`);
        const time3 = Date.now() - start3;
        console.log(`响应时间: ${time3}ms`);
        console.log(`清除效果: ${time3 > time2 ? '✅ 缓存清除成功' : '⚠️ 缓存可能未清除'}`);
        
        console.log('\n✅ 自动测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
    
}, 2000); // 2秒后开始测试