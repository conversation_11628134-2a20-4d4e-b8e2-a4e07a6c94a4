const mysql = require('mysql2/promise');
const WebsiteStatusService = require('./backend/services/website-status-service');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: 'root123',
  database: 'sitemanager',
  charset: 'utf8mb4',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

async function testWebsiteStatusWithCurl() {
  let db;
  
  try {
    console.log('🔗 连接数据库...');
    db = await mysql.createPool(dbConfig);
    
    console.log('✅ 数据库连接成功');
    
    console.log('🚀 初始化网站状态服务...');
    const websiteStatusService = new WebsiteStatusService(db);
    
    console.log('⏳ 等待通知服务初始化...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('🔍 测试获取前5个活跃网站...');
    const activeWebsites = await websiteStatusService.getActiveWebsites();
    const testWebsites = activeWebsites.slice(0, 5);
    
    console.log(`找到 ${testWebsites.length} 个测试网站:`);
    testWebsites.forEach((w, i) => {
      console.log(`  ${i+1}. ${w.siteName} - ${w.siteUrl}`);
    });
    
    console.log('\n🌍 执行curl检测...');
    const results = await websiteStatusService.performBatchCheck(testWebsites);
    
    console.log('\n📊 检测结果:');
    results.forEach((result, i) => {
      console.log(`  ${i+1}. 网站ID: ${result.websiteId}`);
      console.log(`     状态码: ${result.statusCode}`);
      console.log(`     响应时间: ${result.responseTime}ms`);
      console.log(`     是否成功: ${result.isSuccess}`);
      if (result.error) {
        console.log(`     错误: ${result.error}`);
      }
      if (result.ssl) {
        console.log(`     SSL到期: ${result.ssl.expireDate || 'N/A'}`);
      }
      console.log('');
    });
    
    console.log('✅ 测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误堆栈:', error.stack);
  } finally {
    if (db) {
      await db.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 运行测试
testWebsiteStatusWithCurl();
