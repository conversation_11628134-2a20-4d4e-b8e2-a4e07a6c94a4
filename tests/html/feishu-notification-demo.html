<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>飞书网站状态异常通知演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f7fa;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #1f2937;
            text-align: center;
            margin-bottom: 30px;
            font-size: 28px;
        }
        h2 {
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
            margin-top: 30px;
        }
        .config-form {
            display: grid;
            gap: 20px;
            margin-bottom: 30px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        label {
            font-weight: 600;
            color: #374151;
        }
        input, textarea {
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        input:focus, textarea:focus {
            outline: none;
            border-color: #3b82f6;
        }
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        .btn-warning {
            background-color: #f59e0b;
            color: white;
        }
        .btn-secondary {
            background-color: #6b7280;
            color: white;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        .error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .info {
            background-color: #dbeafe;
            border: 1px solid #3b82f6;
            color: #1e40af;
        }
        .table-demo {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            overflow-x: auto;
        }
        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        .status-enabled {
            background-color: #d1fae5;
            color: #065f46;
        }
        .status-disabled {
            background-color: #fee2e2;
            color: #991b1b;
        }
        .help-text {
            font-size: 14px;
            color: #6b7280;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 飞书网站状态异常通知演示</h1>
        
        <h2>📋 通知格式预览</h2>
        <div class="table-demo">🚨 网站访问异常提醒

报告数量：2
生成时间：2025-07-11 11:22:47

┌─────┬──────────┬─────────────────────┬─────────────────────┬────────┬─────────────────────┬──────────────┐
│Site │ Platform │      Site Name      │         URL         │ Status │    Lastest Alarm    │Alarm Duration│
│ ID  │          │                     │                     │  Code  │                     │              │
├─────┼──────────┼─────────────────────┼─────────────────────┼────────┼─────────────────────┼──────────────┤
│1410 │WordPress │威尼斯美食标准运作(上海)有限公司-中文│https://cn.uni-mann.ru│0      │2025-07-11 08:23:34 │0天0小时12分钟│
│1383 │WordPress │深圳市世格美容医疗科技有限公司-中文│https://www.surgscience.cn│0      │2025-07-11 08:23:54 │0天0小时11分钟│
└─────┴──────────┴─────────────────────┴─────────────────────┴────────┴─────────────────────┴──────────────┘

🔧 处理建议：
1. 优先处理持续时间最长的异常网站
2. 检查服务器资源使用情况
3. 验证域名解析和网络连接
4. 查看网站服务日志排查问题

📊 统计信息：
• 异常网站总数：2
• 平台分布：WordPress(2)
• 状态码分布：0(2)

⏰ 下次检查时间：2025-07-11 11:32:47
🤖 网站监控机器人</div>
    </div>

    <div class="container">
        <h2>⚙️ 飞书通知配置</h2>
        <div class="config-form">
            <div class="form-group">
                <label for="webhookUrl">飞书Webhook URL *</label>
                <input type="url" id="webhookUrl" placeholder="https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-url">
                <div class="help-text">在飞书群中添加机器人后获取的Webhook URL</div>
            </div>
            
            <div class="form-group">
                <label for="botName">机器人名称</label>
                <input type="text" id="botName" value="网站监控机器人" placeholder="网站监控机器人">
                <div class="help-text">显示在通知消息末尾的机器人名称</div>
            </div>
            
            <div class="form-group">
                <label for="notificationThreshold">通知阈值（连续失败次数）</label>
                <input type="number" id="notificationThreshold" value="5" min="1" max="20">
                <div class="help-text">网站连续失败多少次后发送通知</div>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enabled" checked> 启用飞书通知
                    <span id="statusBadge" class="status-badge status-enabled">已启用</span>
                </label>
            </div>
        </div>

        <div class="button-group">
            <button class="btn btn-primary" onclick="saveConfig()">
                <span id="saveIcon">💾</span> 保存配置
            </button>
            <button class="btn btn-success" onclick="testNotification()">
                <span id="testIcon">🧪</span> 测试通知
            </button>
            <button class="btn btn-warning" onclick="triggerAbnormalNotification()">
                <span id="triggerIcon">📢</span> 触发异常通知
            </button>
            <button class="btn btn-secondary" onclick="loadCurrentConfig()">
                <span id="loadIcon">🔄</span> 加载当前配置
            </button>
        </div>

        <div id="result" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>📚 使用说明</h2>
        <div style="color: #374151;">
            <h3>🔧 配置步骤：</h3>
            <ol>
                <li><strong>创建飞书群机器人</strong>：在飞书群中添加"自定义机器人"</li>
                <li><strong>获取Webhook URL</strong>：复制机器人的Webhook地址</li>
                <li><strong>配置通知参数</strong>：填写上方表单并保存配置</li>
                <li><strong>测试通知功能</strong>：点击"测试通知"验证配置是否正确</li>
            </ol>

            <h3>🚨 自动通知机制：</h3>
            <ul>
                <li><strong>检测频率</strong>：每10分钟自动检查一次网站状态</li>
                <li><strong>通知条件</strong>：网站连续失败5次（可配置）后发送通知</li>
                <li><strong>通知间隔</strong>：同一网站30分钟内最多发送一次通知</li>
                <li><strong>批量通知</strong>：一次最多通知20个异常网站</li>
            </ul>

            <h3>📊 通知内容包含：</h3>
            <ul>
                <li>异常网站的详细信息表格</li>
                <li>网站ID、平台、名称、URL、状态码</li>
                <li>最后告警时间和持续时间</li>
                <li>处理建议和统计信息</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1';

        // 更新状态徽章
        function updateStatusBadge() {
            const enabled = document.getElementById('enabled').checked;
            const badge = document.getElementById('statusBadge');
            
            if (enabled) {
                badge.textContent = '已启用';
                badge.className = 'status-badge status-enabled';
            } else {
                badge.textContent = '已禁用';
                badge.className = 'status-badge status-disabled';
            }
        }

        // 监听启用状态变化
        document.getElementById('enabled').addEventListener('change', updateStatusBadge);

        // 保存配置
        async function saveConfig() {
            const btn = document.querySelector('.btn-primary');
            const icon = document.getElementById('saveIcon');
            const result = document.getElementById('result');
            
            try {
                btn.disabled = true;
                icon.innerHTML = '<span class="spinner"></span>';
                
                const config = {
                    webhookUrl: document.getElementById('webhookUrl').value,
                    botName: document.getElementById('botName').value,
                    notificationThreshold: parseInt(document.getElementById('notificationThreshold').value),
                    enabled: document.getElementById('enabled').checked
                };

                if (!config.webhookUrl) {
                    throw new Error('请填写飞书Webhook URL');
                }

                const response = await fetch(`${API_BASE}/notifications/feishu/config`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(config)
                });

                const data = await response.json();

                if (data.success) {
                    result.className = 'result success';
                    result.textContent = `✅ 配置保存成功！\n\n配置信息：\n• Webhook URL: ${config.webhookUrl}\n• 机器人名称: ${config.botName}\n• 通知阈值: ${config.notificationThreshold}次\n• 状态: ${config.enabled ? '已启用' : '已禁用'}`;
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ 配置保存失败: ${data.message}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ 配置保存失败: ${error.message}`;
            } finally {
                btn.disabled = false;
                icon.innerHTML = '💾';
                result.style.display = 'block';
            }
        }

        // 测试通知
        async function testNotification() {
            const btn = document.querySelector('.btn-success');
            const icon = document.getElementById('testIcon');
            const result = document.getElementById('result');
            
            try {
                btn.disabled = true;
                icon.innerHTML = '<span class="spinner"></span>';
                
                result.className = 'result info';
                result.style.display = 'block';
                result.textContent = '正在发送测试通知...';

                const response = await fetch(`${API_BASE}/notifications/feishu/test`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (data.success) {
                    result.className = 'result success';
                    result.textContent = `✅ 测试通知发送成功！\n\n请检查飞书群是否收到测试消息。\n\n发送时间: ${data.timestamp}`;
                } else {
                    result.className = 'result error';
                    result.textContent = `❌ 测试通知发送失败: ${data.message}\n\n请检查：\n1. Webhook URL是否正确\n2. 机器人是否已添加到群中\n3. 网络连接是否正常`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ 测试通知发送失败: ${error.message}`;
            } finally {
                btn.disabled = false;
                icon.innerHTML = '🧪';
            }
        }

        // 触发异常通知
        async function triggerAbnormalNotification() {
            const btn = document.querySelector('.btn-warning');
            const icon = document.getElementById('triggerIcon');
            const result = document.getElementById('result');
            
            try {
                btn.disabled = true;
                icon.innerHTML = '<span class="spinner"></span>';
                
                result.className = 'result info';
                result.style.display = 'block';
                result.textContent = '正在检查异常网站并发送通知...';

                const response = await fetch(`${API_BASE}/notifications/feishu/trigger-abnormal`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();

                if (data.success) {
                    result.className = 'result success';
                    result.textContent = `✅ 异常通知发送成功！\n\n已向飞书群发送网站状态异常通知。\n请检查飞书群查看详细的异常网站表格信息。\n\n发送时间: ${data.timestamp}`;
                } else {
                    result.className = 'result info';
                    result.textContent = `ℹ️  ${data.message}\n\n这可能意味着：\n1. 当前没有满足通知条件的异常网站\n2. 异常网站在30分钟内已经发送过通知\n3. 飞书通知功能未启用\n\n检查时间: ${data.timestamp}`;
                }
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ 触发异常通知失败: ${error.message}`;
            } finally {
                btn.disabled = false;
                icon.innerHTML = '📢';
            }
        }

        // 加载当前配置
        async function loadCurrentConfig() {
            const btn = document.querySelector('.btn-secondary');
            const icon = document.getElementById('loadIcon');
            const result = document.getElementById('result');
            
            try {
                btn.disabled = true;
                icon.innerHTML = '<span class="spinner"></span>';
                
                result.className = 'result info';
                result.style.display = 'block';
                result.textContent = '功能开发中，当前显示默认配置...';
                
                // 这里可以添加加载当前配置的API调用
                // 暂时显示提示信息
                setTimeout(() => {
                    result.className = 'result info';
                    result.textContent = 'ℹ️  当前显示的是默认配置\n\n如需查看已保存的配置，请联系管理员或查看数据库中的notification_configs表。';
                }, 1000);
                
            } catch (error) {
                result.className = 'result error';
                result.textContent = `❌ 加载配置失败: ${error.message}`;
            } finally {
                btn.disabled = false;
                icon.innerHTML = '🔄';
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatusBadge();
        });
    </script>
</body>
</html>
