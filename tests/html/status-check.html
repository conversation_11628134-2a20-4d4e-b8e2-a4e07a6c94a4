<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .status-label {
            font-weight: bold;
            color: #555;
        }
        .status-value {
            color: #333;
        }
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .status-unknown { color: #6c757d; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .refresh-time {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 网站状态实时检查</h1>
        <p style="text-align: center;">显示数据库中的真实状态数据</p>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="loadWebsiteStatus()">🔄 刷新状态</button>
            <button onclick="checkSSL()">🔒 检测SSL</button>
            <button onclick="checkAccess()">🌐 检测访问</button>
        </div>
        
        <div id="status-container">
            <p style="text-align: center; color: #666;">点击"刷新状态"获取最新数据</p>
        </div>
        
        <div class="refresh-time" id="refresh-time"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1';

        async function loadWebsiteStatus() {
            try {
                document.getElementById('refresh-time').textContent = '正在加载...';
                
                const response = await fetch(`${API_BASE}/websites?page=1&pageSize=10`);
                const data = await response.json();
                
                if (data.success) {
                    displayWebsiteStatus(data.data.websites);
                    document.getElementById('refresh-time').textContent = 
                        `最后更新时间: ${new Date().toLocaleString('zh-CN')}`;
                } else {
                    document.getElementById('status-container').innerHTML = 
                        `<p style="color: red;">获取数据失败: ${data.message}</p>`;
                }
            } catch (error) {
                document.getElementById('status-container').innerHTML = 
                    `<p style="color: red;">网络错误: ${error.message}</p>`;
            }
        }

        function displayWebsiteStatus(websites) {
            const container = document.getElementById('status-container');
            
            if (websites.length === 0) {
                container.innerHTML = '<p>没有找到网站数据</p>';
                return;
            }

            let html = '';
            
            websites.forEach(website => {
                const sslStatusClass = getStatusClass(website.sslStatus);
                const accessStatusClass = getStatusClass(website.accessStatus);
                
                html += `
                    <div class="status-card">
                        <h3>${website.siteName} (${website.domain})</h3>
                        
                        <div class="status-item">
                            <span class="status-label">网站ID:</span>
                            <span class="status-value">${website.id}</span>
                        </div>
                        
                        <div class="status-item">
                            <span class="status-label">访问状态:</span>
                            <span class="status-value ${accessStatusClass}">
                                ${getStatusText(website.accessStatus)} 
                                ${website.accessStatusCode ? `(${website.accessStatusCode})` : ''}
                            </span>
                        </div>
                        
                        <div class="status-item">
                            <span class="status-label">响应时间:</span>
                            <span class="status-value">${website.responseTime || '未知'}ms</span>
                        </div>
                        
                        <div class="status-item">
                            <span class="status-label">SSL状态:</span>
                            <span class="status-value ${sslStatusClass}">
                                ${getSSLStatusText(website.sslStatus)}
                            </span>
                        </div>
                        
                        <div class="status-item">
                            <span class="status-label">SSL颁发者:</span>
                            <span class="status-value">${website.sslIssuer || '未知'}</span>
                        </div>
                        
                        <div class="status-item">
                            <span class="status-label">SSL到期时间:</span>
                            <span class="status-value">${website.sslExpireDate || '未知'}</span>
                        </div>
                        
                        <div class="status-item">
                            <span class="status-label">最后检测时间:</span>
                            <span class="status-value">${website.lastCheckTime ? new Date(website.lastCheckTime).toLocaleString('zh-CN') : '未知'}</span>
                        </div>
                        
                        <div class="status-item">
                            <span class="status-label">性能评分:</span>
                            <span class="status-value">${website.performanceScore || '未检测'}</span>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function getStatusClass(status) {
            switch(status) {
                case 'online':
                case 'valid':
                    return 'status-good';
                case 'offline':
                case 'expired':
                case 'error':
                    return 'status-error';
                case 'expiring_soon':
                    return 'status-warning';
                case 'unknown':
                default:
                    return 'status-unknown';
            }
        }

        function getStatusText(status) {
            const statusMap = {
                'online': '在线',
                'offline': '离线',
                'error': '错误',
                'unknown': '未知'
            };
            return statusMap[status] || status;
        }

        function getSSLStatusText(status) {
            const statusMap = {
                'valid': '有效',
                'expired': '已过期',
                'expiring_soon': '即将过期',
                'invalid': '无效',
                'unknown': '未检测'
            };
            return statusMap[status] || status;
        }

        async function checkSSL() {
            try {
                const response = await fetch(`${API_BASE}/websites/trigger-ssl-check`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    alert('SSL检测任务已触发，请等待几秒后刷新状态查看结果');
                } else {
                    alert('SSL检测触发失败: ' + data.message);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        async function checkAccess() {
            try {
                const response = await fetch(`${API_BASE}/websites/trigger-access-check`, {
                    method: 'POST'
                });
                const data = await response.json();
                
                if (data.success) {
                    alert('访问状态检测任务已触发，请等待几秒后刷新状态查看结果');
                } else {
                    alert('访问状态检测触发失败: ' + data.message);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }

        // 页面加载时自动获取状态
        window.onload = function() {
            loadWebsiteStatus();
        };
    </script>
</body>
</html>
