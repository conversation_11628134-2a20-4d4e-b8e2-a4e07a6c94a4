/**
 * 权限系统统一化测试
 * 测试目标：验证权限计算逻辑的一致性和安全性
 * 
 * 测试场景：
 * 1. 不同用户角色的权限计算一致性
 * 2. 自定义权限的正确应用
 * 3. 权限中间件的统一验证
 * 4. API响应数据结构的一致性
 */

const { test, expect } = require('@playwright/test');

// 测试配置
const BASE_URL = 'http://localhost:3001';
const TEST_USERS = {
  admin: {
    username: 'admin',
    password: 'admin123',
    expectedRole: 'admin'
  },
  user: {
    username: 'testuser',
    password: 'test123',
    expectedRole: 'user'
  },
  superAdmin: {
    username: 'superadmin',
    password: 'super123',
    expectedRole: 'super_admin'
  }
};

/**
 * 用户登录辅助函数
 */
async function loginUser(page, username, password) {
  await page.goto(`${BASE_URL}/login`);
  
  // 填写登录表单
  await page.getByLabel('用户名').fill(username);
  await page.getByLabel('密码').fill(password);
  await page.getByRole('button', { name: '登录' }).click();
  
  // 等待登录成功
  await page.waitForURL(`${BASE_URL}/dashboard`);
  
  // 获取认证token
  const token = await page.evaluate(() => {
    return localStorage.getItem('token');
  });
  
  return token;
}

/**
 * API请求辅助函数
 */
async function apiRequest(page, endpoint, options = {}) {
  const token = await page.evaluate(() => localStorage.getItem('token'));
  
  const response = await page.request.get(`${BASE_URL}${endpoint}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    ...options
  });
  
  return response;
}

test.describe('权限系统统一化测试', () => {
  
  test('管理员用户权限计算一致性测试', async ({ page, context }) => {
    console.log('🧪 开始测试管理员用户权限计算一致性');
    
    // 1. 登录管理员用户
    const token = await loginUser(page, TEST_USERS.admin.username, TEST_USERS.admin.password);
    expect(token).toBeTruthy();
    console.log('✅ 管理员用户登录成功');
    
    // 2. 获取用户权限 - 通过权限API
    const permissionsResponse = await apiRequest(page, '/api/v1/auth/permissions');
    expect(permissionsResponse.ok()).toBeTruthy();
    
    const permissionsData = await permissionsResponse.json();
    console.log('📋 权限API响应:', permissionsData);
    
    expect(permissionsData.success).toBe(true);
    expect(permissionsData.data.role).toBe(TEST_USERS.admin.expectedRole);
    expect(permissionsData.data.effectivePermissions).toBeDefined();
    expect(Array.isArray(permissionsData.data.effectivePermissions)).toBe(true);
    
    // 3. 获取用户列表 - 验证权限计算一致性
    const usersResponse = await apiRequest(page, '/api/v1/users');
    expect(usersResponse.ok()).toBeTruthy();
    
    const usersData = await usersResponse.json();
    console.log('👥 用户列表API响应:', usersData);
    
    // 4. 验证数据结构一致性
    expect(usersData.success).toBe(true);
    expect(usersData.data.users).toBeDefined();
    
    // 查找当前用户的权限信息
    const currentUser = usersData.data.users.find(u => u.username === TEST_USERS.admin.username);
    expect(currentUser).toBeDefined();
    expect(currentUser.effectivePermissions).toBeDefined();
    
    // 5. 验证两个API返回的权限数据一致性
    const permissionsFromAuthAPI = permissionsData.data.effectivePermissions.sort();
    const permissionsFromUsersAPI = currentUser.effectivePermissions.sort();
    
    console.log('🔍 权限一致性对比:');
    console.log('  - 权限API权限数量:', permissionsFromAuthAPI.length);
    console.log('  - 用户API权限数量:', permissionsFromUsersAPI.length);
    
    expect(permissionsFromAuthAPI).toEqual(permissionsFromUsersAPI);
    console.log('✅ 权限计算一致性验证通过');
  });
  
  test('普通用户权限验证测试', async ({ page }) => {
    console.log('🧪 开始测试普通用户权限验证');
    
    // 1. 登录普通用户
    const token = await loginUser(page, TEST_USERS.user.username, TEST_USERS.user.password);
    expect(token).toBeTruthy();
    console.log('✅ 普通用户登录成功');
    
    // 2. 获取用户权限
    const permissionsResponse = await apiRequest(page, '/api/v1/auth/permissions');
    expect(permissionsResponse.ok()).toBeTruthy();
    
    const permissionsData = await permissionsResponse.json();
    expect(permissionsData.success).toBe(true);
    expect(permissionsData.data.role).toBe(TEST_USERS.user.expectedRole);
    
    console.log('📋 普通用户权限:', permissionsData.data.effectivePermissions);
    
    // 3. 测试权限限制 - 尝试访问管理员功能
    const adminResponse = await apiRequest(page, '/api/v1/users');
    
    // 普通用户应该没有用户管理权限
    if (!adminResponse.ok()) {
      expect(adminResponse.status()).toBe(403);
      console.log('✅ 权限限制正常工作 - 普通用户无法访问用户管理');
    } else {
      // 如果可以访问，检查是否有相应权限
      const userData = await adminResponse.json();
      const hasUserManagePermission = permissionsData.data.effectivePermissions.includes('user.list.view');
      expect(hasUserManagePermission).toBe(true);
      console.log('✅ 权限验证正常 - 用户有相应权限');
    }
  });
  
  test('超级管理员权限测试', async ({ page }) => {
    console.log('🧪 开始测试超级管理员权限');
    
    // 1. 登录超级管理员
    const token = await loginUser(page, TEST_USERS.superAdmin.username, TEST_USERS.superAdmin.password);
    expect(token).toBeTruthy();
    console.log('✅ 超级管理员登录成功');
    
    // 2. 获取权限信息
    const permissionsResponse = await apiRequest(page, '/api/v1/auth/permissions');
    expect(permissionsResponse.ok()).toBeTruthy();
    
    const permissionsData = await permissionsResponse.json();
    expect(permissionsData.success).toBe(true);
    expect(permissionsData.data.role).toBe(TEST_USERS.superAdmin.expectedRole);
    
    console.log('📋 超级管理员权限数量:', permissionsData.data.effectivePermissions.length);
    
    // 3. 验证超级管理员拥有所有权限
    expect(permissionsData.data.effectivePermissions.length).toBeGreaterThan(0);
    
    // 4. 测试访问所有管理功能
    const testEndpoints = [
      '/api/v1/users',
      '/api/v1/permissions',
      '/api/v1/roles'
    ];
    
    for (const endpoint of testEndpoints) {
      const response = await apiRequest(page, endpoint);
      expect(response.ok()).toBeTruthy();
      console.log(`✅ 超级管理员可以访问: ${endpoint}`);
    }
  });
  
  test('权限中间件统一性测试', async ({ page }) => {
    console.log('🧪 开始测试权限中间件统一性');
    
    // 1. 登录管理员用户
    await loginUser(page, TEST_USERS.admin.username, TEST_USERS.admin.password);
    
    // 2. 测试多个需要权限的API端点
    const protectedEndpoints = [
      { path: '/api/v1/users', permission: 'user.list.view' },
      { path: '/api/v1/permissions', permission: 'system.permission.view' },
      { path: '/api/v1/roles', permission: 'user.role.view' }
    ];
    
    for (const endpoint of protectedEndpoints) {
      const response = await apiRequest(page, endpoint.path);
      
      if (response.ok()) {
        console.log(`✅ 权限验证通过: ${endpoint.path}`);
      } else {
        expect(response.status()).toBe(403);
        console.log(`🚫 权限验证失败: ${endpoint.path} (预期行为)`);
      }
    }
  });
  
  test('API响应数据结构一致性测试', async ({ page }) => {
    console.log('🧪 开始测试API响应数据结构一致性');
    
    // 1. 登录管理员用户
    await loginUser(page, TEST_USERS.admin.username, TEST_USERS.admin.password);
    
    // 2. 测试权限相关API的响应格式
    const permissionsResponse = await apiRequest(page, '/api/v1/auth/permissions');
    const permissionsData = await permissionsResponse.json();
    
    // 验证响应结构
    expect(permissionsData).toHaveProperty('success');
    expect(permissionsData).toHaveProperty('message');
    expect(permissionsData).toHaveProperty('data');
    expect(permissionsData).toHaveProperty('timestamp');
    
    // 验证权限数据结构
    const data = permissionsData.data;
    expect(data).toHaveProperty('userId');
    expect(data).toHaveProperty('username');
    expect(data).toHaveProperty('role');
    expect(data).toHaveProperty('effectivePermissions');
    expect(data).toHaveProperty('rolePermissions');
    expect(data).toHaveProperty('customPermissions');
    expect(data).toHaveProperty('deniedPermissions');
    
    console.log('✅ API响应数据结构验证通过');
    console.log('📊 权限数据统计:', {
      effectivePermissions: data.effectivePermissions.length,
      rolePermissions: data.rolePermissions.length,
      customPermissions: data.customPermissions.length,
      deniedPermissions: data.deniedPermissions.length
    });
  });
});
