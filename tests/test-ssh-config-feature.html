<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSH配置选择功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
        }
        .api-test {
            background: #fff3cd;
            border-left-color: #ffc107;
        }
        .code {
            background: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 SSH配置选择功能测试</h1>
        <p>测试服务器编辑页面的SSH配置选择功能</p>
    </div>

    <div class="test-section success">
        <h2>✅ 功能实现完成</h2>
        <p><strong>新功能</strong>：在服务器编辑页面添加了SSH配置选择器</p>
        <ul>
            <li>可以从系统设置中保存的SSH配置里选择</li>
            <li>显示配置名称、用户名、认证方式和描述信息</li>
            <li>选择后自动填充SSH连接信息</li>
            <li>支持清空选择，手动填写SSH配置</li>
        </ul>
    </div>

    <div class="test-section api-test">
        <h2>🔍 API测试</h2>
        <p>测试SSH配置相关的API接口</p>
        
        <button class="button" onclick="testSshConfigsList()">测试获取SSH配置列表</button>
        <button class="button" onclick="testSshConfigDetail()">测试获取SSH配置详情</button>
        
        <div id="api-results" class="result" style="display: none;">
            <h4>API测试结果：</h4>
            <div id="api-output"></div>
        </div>
    </div>

    <div class="test-section">
        <h2>📋 测试步骤</h2>
        <ol>
            <li><strong>访问系统设置</strong>：进入系统设置 → 服务器设置 → SSH配置管理</li>
            <li><strong>创建SSH配置</strong>：添加一些测试用的SSH配置</li>
            <li><strong>编辑服务器</strong>：进入服务器管理页面，点击编辑服务器</li>
            <li><strong>选择SSH配置</strong>：在SSH配置部分，使用新增的选择器选择预设配置</li>
            <li><strong>验证自动填充</strong>：确认SSH信息自动填充到表单中</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🎯 功能特点</h2>
        <ul>
            <li><strong>便捷性</strong>：无需重复输入SSH配置信息</li>
            <li><strong>安全性</strong>：SSH配置统一管理，减少配置错误</li>
            <li><strong>灵活性</strong>：支持选择预设配置或手动填写</li>
            <li><strong>智能化</strong>：自动识别认证方式并填充对应字段</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔗 相关链接</h2>
        <ul>
            <li><a href="http://localhost:3000" target="_blank">前端应用 (http://localhost:3000)</a></li>
            <li><a href="http://localhost:3000/servers" target="_blank">服务器管理页面</a></li>
            <li><a href="http://localhost:3000/settings" target="_blank">系统设置页面</a></li>
            <li><a href="http://localhost:3001/api/v1/settings/ssh-configs" target="_blank">SSH配置API</a></li>
        </ul>
    </div>

    <script>
        async function testSshConfigsList() {
            try {
                const response = await fetch('http://localhost:3001/api/v1/settings/ssh-configs');
                const data = await response.json();
                showApiResult('SSH配置列表', data);
            } catch (error) {
                showApiResult('SSH配置列表', { error: error.message });
            }
        }

        async function testSshConfigDetail() {
            try {
                // 先获取列表，然后获取第一个配置的详情
                const listResponse = await fetch('http://localhost:3001/api/v1/settings/ssh-configs');
                const listData = await listResponse.json();
                
                if (listData.success && listData.data.length > 0) {
                    const firstConfigId = listData.data[0].id;
                    const detailResponse = await fetch(`http://localhost:3001/api/v1/settings/ssh-configs/${firstConfigId}`);
                    const detailData = await detailResponse.json();
                    showApiResult(`SSH配置详情 (ID: ${firstConfigId})`, detailData);
                } else {
                    showApiResult('SSH配置详情', { error: '没有找到SSH配置' });
                }
            } catch (error) {
                showApiResult('SSH配置详情', { error: error.message });
            }
        }

        function showApiResult(title, data) {
            const resultsDiv = document.getElementById('api-results');
            const outputDiv = document.getElementById('api-output');
            
            outputDiv.innerHTML = `
                <h5>${title}:</h5>
                <pre class="code">${JSON.stringify(data, null, 2)}</pre>
            `;
            
            resultsDiv.style.display = 'block';
        }
    </script>
</body>
</html>
