#!/bin/bash

# 测试增强监控API接口
echo "🚀 开始测试增强监控API接口..."
echo ""

API_BASE="http://localhost:3001/api/v1"

# 1. 测试获取监控概览
echo "📊 测试获取监控概览..."
curl -s -X GET "${API_BASE}/enhanced-monitoring/overview" | jq '.' || echo "❌ 获取监控概览失败"
echo ""
echo "────────────────────────────────────────────────────────────"

# 2. 获取一个网站ID进行测试
echo ""
echo "🔍 获取测试网站..."
WEBSITE_RESPONSE=$(curl -s -X GET "${API_BASE}/websites?limit=1")
WEBSITE_ID=$(echo "$WEBSITE_RESPONSE" | jq -r '.data.websites[0].id // empty')

if [ -z "$WEBSITE_ID" ] || [ "$WEBSITE_ID" = "null" ]; then
    echo "❌ 没有找到网站进行测试"
    exit 1
fi

WEBSITE_NAME=$(echo "$WEBSITE_RESPONSE" | jq -r '.data.websites[0].siteName // "未知网站"')
echo "✅ 使用测试网站: $WEBSITE_NAME (ID: $WEBSITE_ID)"
echo ""
echo "────────────────────────────────────────────────────────────"

# 3. 测试单个网站检查
echo ""
echo "🔍 测试单个网站检查..."
curl -s -X POST "${API_BASE}/enhanced-monitoring/websites/${WEBSITE_ID}/check" \
  -H "Content-Type: application/json" \
  -d '{
    "enableHttpCheck": true,
    "enableSslCheck": true,
    "enableKeywordCheck": false,
    "connectTimeout": 10,
    "retries": 1,
    "statusCodes": "200-299,301,302"
  }' | jq '.' || echo "❌ 网站检查失败"
echo ""
echo "────────────────────────────────────────────────────────────"

# 4. 测试获取网站监控配置
echo ""
echo "⚙️ 测试获取网站监控配置..."
curl -s -X GET "${API_BASE}/enhanced-monitoring/websites/${WEBSITE_ID}/config" | jq '.' || echo "❌ 获取监控配置失败"
echo ""
echo "────────────────────────────────────────────────────────────"

# 5. 测试更新网站监控配置
echo ""
echo "⚙️ 测试更新网站监控配置..."
curl -s -X PUT "${API_BASE}/enhanced-monitoring/websites/${WEBSITE_ID}/config" \
  -H "Content-Type: application/json" \
  -d '{
    "http_method": "GET",
    "status_codes": "200-299,301,302",
    "connect_timeout": 15,
    "retries": 2,
    "retry_interval": 30,
    "enable_http_check": true,
    "enable_ssl_check": true,
    "enable_keyword_check": false,
    "notify_cert_expiry": true,
    "ignore_tls": false,
    "keyword": "",
    "request_headers": "",
    "request_body": "",
    "max_redirects": 10,
    "check_interval": 300
  }' | jq '.' || echo "❌ 更新监控配置失败"
echo ""
echo "────────────────────────────────────────────────────────────"

# 6. 测试获取网站监控历史
echo ""
echo "📊 测试获取网站监控历史..."
curl -s -X GET "${API_BASE}/enhanced-monitoring/websites/${WEBSITE_ID}/history?range=24h&limit=5" | jq '.' || echo "❌ 获取监控历史失败"
echo ""
echo "────────────────────────────────────────────────────────────"

# 7. 测试批量网站检查
echo ""
echo "🔍 测试批量网站检查..."
BATCH_WEBSITES=$(curl -s -X GET "${API_BASE}/websites?limit=3")
WEBSITE_IDS=$(echo "$BATCH_WEBSITES" | jq -r '.data.websites[].id' | head -3 | tr '\n' ',' | sed 's/,$//')

if [ -n "$WEBSITE_IDS" ]; then
    echo "📋 准备批量检查网站 IDs: $WEBSITE_IDS"
    
    # 构建JSON数组
    IDS_ARRAY=$(echo "$WEBSITE_IDS" | sed 's/,/,/g' | sed 's/^/[/' | sed 's/$/]/')
    
    curl -s -X POST "${API_BASE}/enhanced-monitoring/websites/batch-check" \
      -H "Content-Type: application/json" \
      -d "{
        \"websiteIds\": $IDS_ARRAY,
        \"config\": {
          \"concurrency\": 2,
          \"enableHttpCheck\": true,
          \"enableSslCheck\": true,
          \"connectTimeout\": 10,
          \"statusCodes\": \"200-299,301,302\"
        }
      }" | jq '.' || echo "❌ 批量检查失败"
else
    echo "❌ 没有找到网站进行批量测试"
fi

echo ""
echo "🎉 增强监控API接口测试完成!"
