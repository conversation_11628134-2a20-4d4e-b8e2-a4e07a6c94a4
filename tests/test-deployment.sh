#!/bin/bash

# SiteManager 部署脚本测试工具
# 用于验证部署脚本的各项功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() { echo -e "${BLUE}[TEST]${NC} $1"; }
log_success() { echo -e "${GREEN}[PASS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[FAIL]${NC} $1"; }

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    log_info "测试: $test_name"
    
    if eval "$test_command" &>/dev/null; then
        log_success "$test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
        return 0
    else
        log_error "$test_name"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# 显示测试横幅
show_banner() {
    echo -e "${BLUE}"
    echo "=================================================================="
    echo "    🧪 SiteManager 部署脚本测试工具"
    echo "    📋 验证部署脚本的各项功能"
    echo "=================================================================="
    echo -e "${NC}"
}

# 测试脚本文件存在性
test_script_files() {
    log_info "检查脚本文件..."
    
    run_test "部署脚本存在" "[ -f 'deploy-production.sh' ]"
    run_test "管理脚本存在" "[ -f 'production-manager.sh' ]"
    run_test "Docker编排文件存在" "[ -f 'docker-compose.production.yml' ]"
    run_test "环境变量模板存在" "[ -f '.env.production.template' ]"
    run_test "部署文档存在" "[ -f 'PRODUCTION-DEPLOYMENT.md' ]"
}

# 测试脚本权限
test_script_permissions() {
    log_info "检查脚本权限..."
    
    run_test "部署脚本可执行" "[ -x 'deploy-production.sh' ]"
    run_test "管理脚本可执行" "[ -x 'production-manager.sh' ]"
}

# 测试脚本语法
test_script_syntax() {
    log_info "检查脚本语法..."
    
    run_test "部署脚本语法正确" "bash -n deploy-production.sh"
    run_test "管理脚本语法正确" "bash -n production-manager.sh"
}

# 测试脚本帮助信息
test_script_help() {
    log_info "检查脚本帮助信息..."
    
    run_test "部署脚本帮助信息" "./deploy-production.sh --help | grep -q '用法'"
    run_test "管理脚本帮助信息" "./production-manager.sh --help | grep -q '用法'"
}

# 测试Docker配置
test_docker_config() {
    log_info "检查Docker配置..."
    
    if command -v docker-compose &> /dev/null; then
        run_test "Docker编排配置语法" "docker-compose -f docker-compose.production.yml config"
    else
        log_warning "Docker Compose 未安装，跳过Docker配置测试"
    fi
}

# 测试Nginx配置
test_nginx_config() {
    log_info "检查Nginx配置..."
    
    run_test "Nginx配置文件存在" "[ -f 'nginx/nginx.conf' ]"
    run_test "Nginx站点配置存在" "[ -f 'nginx/conf.d/default.conf' ]"
    
    if command -v nginx &> /dev/null; then
        # 创建临时测试目录
        local temp_dir="/tmp/nginx-test-$$"
        mkdir -p "$temp_dir/conf.d"
        cp nginx/nginx.conf "$temp_dir/"
        cp nginx/conf.d/default.conf "$temp_dir/conf.d/"
        
        # 修改配置文件中的路径
        sed -i 's|/usr/share/nginx/html|/tmp|g' "$temp_dir/conf.d/default.conf"
        sed -i 's|backend:3001|localhost:3001|g' "$temp_dir/conf.d/default.conf"
        
        run_test "Nginx配置语法正确" "nginx -t -c $temp_dir/nginx.conf"
        
        # 清理临时文件
        rm -rf "$temp_dir"
    else
        log_warning "Nginx 未安装，跳过配置语法测试"
    fi
}

# 测试Dockerfile
test_dockerfile() {
    log_info "检查Dockerfile..."
    
    run_test "后端Dockerfile存在" "[ -f 'Dockerfile.backend' ]"
    run_test "前端Dockerfile存在" "[ -f 'Dockerfile.frontend' ]"
    
    if command -v docker &> /dev/null; then
        # 测试Dockerfile语法（不实际构建）
        run_test "后端Dockerfile语法" "docker build -f Dockerfile.backend --dry-run . 2>/dev/null || true"
        run_test "前端Dockerfile语法" "docker build -f Dockerfile.frontend --dry-run . 2>/dev/null || true"
    else
        log_warning "Docker 未安装，跳过Dockerfile语法测试"
    fi
}

# 测试环境变量模板
test_env_template() {
    log_info "检查环境变量模板..."
    
    run_test "包含数据库配置" "grep -q 'DB_HOST' .env.production.template"
    run_test "包含安全配置" "grep -q 'JWT_SECRET' .env.production.template"
    run_test "包含应用配置" "grep -q 'NODE_ENV' .env.production.template"
    run_test "包含邮件配置" "grep -q 'MAIL_HOST' .env.production.template"
}

# 测试项目结构
test_project_structure() {
    log_info "检查项目结构..."
    
    run_test "后端目录存在" "[ -d 'backend' ]"
    run_test "前端目录存在" "[ -d 'frontend' ]"
    run_test "数据库目录存在" "[ -d 'database' ]"
    run_test "后端package.json存在" "[ -f 'backend/package.json' ]"
    run_test "前端package.json存在" "[ -f 'frontend/package.json' ]"
    run_test "数据库初始化脚本存在" "[ -f 'database/init.sql' ]"
}

# 测试脚本版本信息
test_script_versions() {
    log_info "检查脚本版本信息..."
    
    run_test "部署脚本包含版本号" "grep -q 'SCRIPT_VERSION=' deploy-production.sh"
    run_test "管理脚本包含版本号" "grep -q 'SCRIPT_VERSION=' production-manager.sh"
    
    # 提取版本号
    local deploy_version=$(grep 'SCRIPT_VERSION=' deploy-production.sh | head -1 | cut -d'"' -f2)
    local manager_version=$(grep 'SCRIPT_VERSION=' production-manager.sh | head -1 | cut -d'"' -f2)
    
    log_info "部署脚本版本: $deploy_version"
    log_info "管理脚本版本: $manager_version"
}

# 测试脚本功能选项
test_script_options() {
    log_info "检查脚本功能选项..."
    
    # 测试部署脚本选项
    run_test "支持检查模式" "./deploy-production.sh --help | grep -q 'check-only'"
    run_test "支持强制模式" "./deploy-production.sh --help | grep -q 'force'"
    run_test "支持更新模式" "./deploy-production.sh --help | grep -q 'update'"
    run_test "支持域名配置" "./deploy-production.sh --help | grep -q 'domain'"
    run_test "支持SSL配置" "./deploy-production.sh --help | grep -q 'ssl'"
    run_test "支持Nginx配置" "./deploy-production.sh --help | grep -q 'nginx'"
    
    # 测试管理脚本选项
    run_test "支持状态检查" "./production-manager.sh --help | grep -q 'status'"
    run_test "支持日志查看" "./production-manager.sh --help | grep -q 'logs'"
    run_test "支持备份功能" "./production-manager.sh --help | grep -q 'backup'"
    run_test "支持监控功能" "./production-manager.sh --help | grep -q 'monitor'"
}

# 测试文档完整性
test_documentation() {
    log_info "检查文档完整性..."
    
    run_test "部署文档包含快速开始" "grep -q '快速开始' PRODUCTION-DEPLOYMENT.md"
    run_test "部署文档包含Docker部署" "grep -q 'Docker' PRODUCTION-DEPLOYMENT.md"
    run_test "部署文档包含故障排除" "grep -q '故障排除' PRODUCTION-DEPLOYMENT.md"
    run_test "README包含生产部署说明" "grep -q '生产环境部署' README.md"
}

# 显示测试结果
show_test_results() {
    echo ""
    echo -e "${BLUE}=================================================================="
    echo "                        测试结果汇总"
    echo "==================================================================${NC}"
    echo ""
    
    echo -e "${BLUE}总测试数: ${NC}$TOTAL_TESTS"
    echo -e "${GREEN}通过测试: ${NC}$PASSED_TESTS"
    echo -e "${RED}失败测试: ${NC}$FAILED_TESTS"
    
    local success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "${BLUE}成功率: ${NC}$success_rate%"
    
    echo ""
    if [ "$FAILED_TESTS" -eq 0 ]; then
        echo -e "${GREEN}🎉 所有测试通过！部署脚本已就绪。${NC}"
        exit 0
    else
        echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查相关问题。${NC}"
        exit 1
    fi
}

# 主函数
main() {
    show_banner
    
    test_script_files
    test_script_permissions
    test_script_syntax
    test_script_help
    test_docker_config
    test_nginx_config
    test_dockerfile
    test_env_template
    test_project_structure
    test_script_versions
    test_script_options
    test_documentation
    
    show_test_results
}

# 运行测试
main "$@"
