#!/bin/bash

# 批量操作功能测试脚本
# 测试网站管理和服务器管理的批量操作功能

API_BASE="http://localhost:3001/api/v1"

echo "🧪 开始测试批量操作功能..."
echo ""

# 1. 测试网站批量操作
echo "1️⃣ 测试网站批量操作..."

# 获取网站列表
echo "   📋 获取网站列表..."
websites_response=$(curl -s "$API_BASE/websites")
websites_success=$(echo "$websites_response" | jq -r '.success')

if [ "$websites_success" != "true" ]; then
    echo "   ❌ 获取网站列表失败"
    exit 1
fi

website_count=$(echo "$websites_response" | jq '.data.websites | length')
echo "   ✅ 当前网站数量: $website_count"

if [ "$website_count" -gt 0 ]; then
    # 选择前两个网站进行批量操作测试
    website_ids=$(echo "$websites_response" | jq -r '.data.websites[0:2] | map(.id) | @json')
    echo "   📋 选择网站ID进行测试: $website_ids"

    # 测试批量更新状态
    echo "   🔄 测试批量更新网站状态..."
    batch_response=$(curl -s -X POST "$API_BASE/websites/batch" \
      -H "Content-Type: application/json" \
      -d "{
        \"action\": \"updateStatus\",
        \"websiteIds\": $website_ids,
        \"data\": {
          \"status\": \"inactive\"
        }
      }")

    batch_success=$(echo "$batch_response" | jq -r '.success')
    if [ "$batch_success" = "true" ]; then
        affected_rows=$(echo "$batch_response" | jq -r '.data.affectedRows')
        echo "   ✅ 批量更新网站状态成功，影响 $affected_rows 个网站"
    else
        batch_message=$(echo "$batch_response" | jq -r '.message')
        echo "   ❌ 批量更新网站状态失败: $batch_message"
    fi

    # 恢复状态
    echo "   🔄 恢复网站状态..."
    restore_response=$(curl -s -X POST "$API_BASE/websites/batch" \
      -H "Content-Type: application/json" \
      -d "{
        \"action\": \"updateStatus\",
        \"websiteIds\": $website_ids,
        \"data\": {
          \"status\": \"active\"
        }
      }")

    restore_success=$(echo "$restore_response" | jq -r '.success')
    if [ "$restore_success" = "true" ]; then
        echo "   ✅ 网站状态恢复成功"
    else
        echo "   ⚠️  网站状态恢复失败"
    fi
else
    echo "   ⚠️  没有网站可以测试批量操作"
fi

echo ""

# 2. 测试服务器批量操作
echo "2️⃣ 测试服务器批量操作..."

# 获取服务器列表
echo "   📋 获取服务器列表..."
servers_response=$(curl -s "$API_BASE/servers")
servers_success=$(echo "$servers_response" | jq -r '.success')

if [ "$servers_success" != "true" ]; then
    echo "   ❌ 获取服务器列表失败"
    exit 1
fi

server_count=$(echo "$servers_response" | jq '.data.servers | length')
echo "   ✅ 当前服务器数量: $server_count"

if [ "$server_count" -gt 0 ]; then
    # 选择前两个服务器进行批量操作测试
    server_ids=$(echo "$servers_response" | jq -r '.data.servers[0:2] | map(.id) | @json')
    echo "   📋 选择服务器ID进行测试: $server_ids"

    # 测试批量更新监控状态
    echo "   🔄 测试批量更新服务器监控状态..."
    batch_response=$(curl -s -X POST "$API_BASE/servers/batch" \
      -H "Content-Type: application/json" \
      -d "{
        \"operation\": \"updateMonitoring\",
        \"serverIds\": $server_ids,
        \"data\": {
          \"monitoringEnabled\": false
        }
      }")

    batch_success=$(echo "$batch_response" | jq -r '.success')
    if [ "$batch_success" = "true" ]; then
        affected_rows=$(echo "$batch_response" | jq -r '.data.affectedRows')
        echo "   ✅ 批量更新服务器监控状态成功，影响 $affected_rows 个服务器"
    else
        batch_message=$(echo "$batch_response" | jq -r '.message')
        echo "   ❌ 批量更新服务器监控状态失败: $batch_message"
    fi

    # 恢复监控状态
    echo "   🔄 恢复服务器监控状态..."
    restore_response=$(curl -s -X POST "$API_BASE/servers/batch" \
      -H "Content-Type: application/json" \
      -d "{
        \"operation\": \"updateMonitoring\",
        \"serverIds\": $server_ids,
        \"data\": {
          \"monitoringEnabled\": true
        }
      }")

    restore_success=$(echo "$restore_response" | jq -r '.success')
    if [ "$restore_success" = "true" ]; then
        echo "   ✅ 服务器监控状态恢复成功"
    else
        echo "   ⚠️  服务器监控状态恢复失败"
    fi
else
    echo "   ⚠️  没有服务器可以测试批量操作"
fi

echo ""

# 3. 测试错误处理
echo "3️⃣ 测试错误处理..."

# 测试无效操作类型
echo "   📋 测试无效操作类型..."
error_response=$(curl -s -X POST "$API_BASE/websites/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "invalidAction",
    "websiteIds": [1],
    "data": {}
  }')

error_success=$(echo "$error_response" | jq -r '.success')
if [ "$error_success" = "false" ]; then
    error_message=$(echo "$error_response" | jq -r '.message')
    echo "   ✅ 无效操作类型正确被拒绝: $error_message"
else
    echo "   ❌ 无效操作类型验证失败"
fi

# 测试空ID列表
echo "   📋 测试空ID列表..."
empty_response=$(curl -s -X POST "$API_BASE/websites/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "action": "updateStatus",
    "websiteIds": [],
    "data": {"status": "active"}
  }')

empty_success=$(echo "$empty_response" | jq -r '.success')
if [ "$empty_success" = "false" ]; then
    empty_message=$(echo "$empty_response" | jq -r '.message')
    echo "   ✅ 空ID列表正确被拒绝: $empty_message"
else
    echo "   ❌ 空ID列表验证失败"
fi

echo ""
echo "🎉 批量操作功能测试完成！"
echo ""
echo "📋 测试总结:"
echo "✅ 网站批量操作API正常工作"
echo "✅ 服务器批量操作API正常工作"
echo "✅ 错误处理机制正常"
echo "✅ 参数验证功能正常"
