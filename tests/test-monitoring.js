/**
 * 监控系统测试脚本
 */

const mysql = require('mysql2/promise');

async function testMonitoring() {
  console.log('🧪 开始测试监控系统...\n');
  
  try {
    // 连接数据库
    const db = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root123',
      database: 'sitemanager',
      charset: 'utf8mb4'
    });
    
    console.log('✅ 数据库连接成功');
    
    // 1. 检查监控表是否存在
    console.log('\n📋 检查监控表结构...');
    
    const tables = [
      'site_check_history',
      'monitoring_config', 
      'notification_config',
      'notification_history',
      'monitoring_stats'
    ];
    
    for (const table of tables) {
      try {
        const [rows] = await db.execute(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`✅ ${table}: ${rows[0].count} 条记录`);
      } catch (error) {
        console.log(`❌ ${table}: 表不存在或有错误`);
      }
    }
    
    // 2. 检查websites表的监控字段
    console.log('\n🔍 检查websites表监控字段...');
    
    const [websiteFields] = await db.execute(`
      SELECT COUNT(*) as total,
             SUM(CASE WHEN consecutive_failures > 0 THEN 1 ELSE 0 END) as with_failures,
             SUM(CASE WHEN notification_sent = 1 THEN 1 ELSE 0 END) as with_notifications,
             SUM(CASE WHEN last_check_time IS NOT NULL THEN 1 ELSE 0 END) as checked
      FROM websites 
      WHERE status = 'active'
    `);
    
    const stats = websiteFields[0];
    console.log(`✅ 活跃站点总数: ${stats.total}`);
    console.log(`⚠️  有失败记录的站点: ${stats.with_failures}`);
    console.log(`📤 已发送通知的站点: ${stats.with_notifications}`);
    console.log(`🔍 已检测过的站点: ${stats.checked}`);
    
    // 3. 检查最近的检测记录
    console.log('\n📊 检查最近检测记录...');
    
    const [recentChecks] = await db.execute(`
      SELECT 
        COUNT(*) as total_checks,
        SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful,
        SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as failed,
        AVG(CASE WHEN success = 1 THEN response_time END) as avg_response_time
      FROM site_check_history 
      WHERE check_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    `);
    
    if (recentChecks[0].total_checks > 0) {
      const recent = recentChecks[0];
      console.log(`✅ 最近1小时检测次数: ${recent.total_checks}`);
      console.log(`✅ 成功: ${recent.successful}, 失败: ${recent.failed}`);
      console.log(`⏱️  平均响应时间: ${Math.round(recent.avg_response_time || 0)}ms`);
      
      const successRate = ((recent.successful / recent.total_checks) * 100).toFixed(1);
      console.log(`📈 成功率: ${successRate}%`);
    } else {
      console.log('ℹ️  最近1小时内没有检测记录');
    }
    
    // 4. 检查配置
    console.log('\n⚙️  检查监控配置...');
    
    const [configs] = await db.execute(`
      SELECT config_key, config_value 
      FROM monitoring_config 
      ORDER BY config_key
    `);
    
    if (configs.length > 0) {
      console.log('✅ 监控配置:');
      configs.forEach(config => {
        console.log(`   ${config.config_key}: ${config.config_value}`);
      });
    } else {
      console.log('⚠️  没有找到监控配置');
    }
    
    // 5. 检查通知配置
    console.log('\n📤 检查通知配置...');
    
    const [notifications] = await db.execute(`
      SELECT name, type, enabled 
      FROM notification_config 
      ORDER BY type
    `);
    
    if (notifications.length > 0) {
      console.log('✅ 通知配置:');
      notifications.forEach(notif => {
        const status = notif.enabled ? '✅ 启用' : '❌ 禁用';
        console.log(`   ${notif.type} (${notif.name}): ${status}`);
      });
    } else {
      console.log('⚠️  没有找到通知配置');
    }
    
    // 6. 测试API连接
    console.log('\n🔗 测试监控API...');
    
    try {
      const response = await fetch('http://localhost:3002/status');
      if (response.ok) {
        const data = await response.json();
        console.log('✅ 监控API响应正常');
        console.log(`   服务状态: ${data.data.isRunning ? '运行中' : '已停止'}`);
        console.log(`   运行时间: ${Math.floor(data.data.uptime / 3600)}小时`);
        console.log(`   总检测次数: ${data.data.stats.totalChecks}`);
      } else {
        console.log('❌ 监控API响应异常');
      }
    } catch (error) {
      console.log('❌ 无法连接监控API:', error.message);
    }
    
    // 7. 显示最近的故障站点
    console.log('\n🚨 最近故障站点...');
    
    const [failedSites] = await db.execute(`
      SELECT site_name, domain, consecutive_failures, last_failure_time, notification_sent
      FROM websites 
      WHERE consecutive_failures > 0 
      ORDER BY consecutive_failures DESC, last_failure_time DESC
      LIMIT 10
    `);
    
    if (failedSites.length > 0) {
      console.log('⚠️  发现故障站点:');
      failedSites.forEach(site => {
        const notified = site.notification_sent ? '📤 已通知' : '⏳ 未通知';
        console.log(`   ${site.site_name || site.domain}: 连续失败${site.consecutive_failures}次 ${notified}`);
      });
    } else {
      console.log('✅ 没有发现故障站点');
    }
    
    await db.end();
    
    console.log('\n🎉 监控系统测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
testMonitoring();
