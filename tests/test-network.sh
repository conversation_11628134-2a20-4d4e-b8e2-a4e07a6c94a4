#!/bin/bash

# 网络连接测试脚本
echo "🌐 测试网络连接..."

# 测试基本网络连接
echo "📡 测试基本网络连接..."
if ping -c 1 8.8.8.8 &> /dev/null; then
    echo "✅ 基本网络连接正常"
else
    echo "❌ 基本网络连接失败"
    exit 1
fi

# 测试DNS解析
echo "🔍 测试DNS解析..."
if nslookup registry.npmmirror.com &> /dev/null; then
    echo "✅ DNS解析正常"
else
    echo "❌ DNS解析失败"
fi

# 测试npm镜像源连接
echo "📦 测试npm镜像源连接..."
if curl -s --connect-timeout 5 https://registry.npmmirror.com/ &> /dev/null; then
    echo "✅ npm镜像源连接正常"
else
    echo "❌ npm镜像源连接失败，尝试其他源..."
    if curl -s --connect-timeout 5 https://registry.npmjs.org/ &> /dev/null; then
        echo "✅ 官方npm源连接正常"
    else
        echo "❌ 所有npm源连接失败"
    fi
fi

# 测试Alpine镜像源连接
echo "🏔️ 测试Alpine镜像源连接..."
if curl -s --connect-timeout 5 https://mirrors.aliyun.com/alpine/ &> /dev/null; then
    echo "✅ 阿里云Alpine镜像源连接正常"
else
    echo "❌ 阿里云Alpine镜像源连接失败，尝试其他源..."
    if curl -s --connect-timeout 5 https://mirrors.tuna.tsinghua.edu.cn/alpine/ &> /dev/null; then
        echo "✅ 清华Alpine镜像源连接正常"
    else
        echo "❌ 所有Alpine镜像源连接失败"
    fi
fi

# 测试Docker Hub连接
echo "🐳 测试Docker Hub连接..."
if curl -s --connect-timeout 5 https://hub.docker.com/ &> /dev/null; then
    echo "✅ Docker Hub连接正常"
else
    echo "❌ Docker Hub连接失败"
fi

echo "🎉 网络连接测试完成"
