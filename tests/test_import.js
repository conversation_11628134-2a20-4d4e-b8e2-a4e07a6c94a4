// 测试导入功能的脚本
const XLSX = require('./backend/node_modules/xlsx');

// 创建测试数据
const testData = [
  ['ID', '站点ID', '站点名称', '站点URL', '平台类型ID(1=WordPress, 2=易营宝, 3=Shopify, 4=多谷, 5=巨旺, 6=弗米乐, 7=领动, 8=test)', '服务器', '上线时间', '到期时间', '项目金额', '续费金额', 'Onboard', '备注', '状态'],
  ['', '2001', 'WordPress测试站', 'https://wp-test.com', '1', 'Server-1', '2024-01-01', '2024-12-31', 15000, 3600, '已安装', 'WordPress网站', 'active'],
  ['', '2002', '易营宝测试站', 'https://yinyingbao-test.com', '2', 'Server-2', '2024-02-01', '2024-12-31', 20000, 4000, '已安装', '易营宝网站', 'active'],
  ['', '2003', 'Shopify测试站', 'https://shopify-test.com', '3', 'Server-3', '2024-03-01', '2024-12-31', 25000, 5000, '已安装', 'Shopify网站', 'active'],
  ['', '2004', '多谷测试站', 'https://duogu-test.com', '4', 'Server-4', '2024-04-01', '2024-12-31', 18000, 3800, '已安装', '多谷网站', 'active'],
  ['', '2005', '巨旺测试站', 'https://juwang-test.com', '5', 'Server-5', '2024-05-01', '2024-12-31', 22000, 4200, '已安装', '巨旺网站', 'active'],
  ['', '2006', '弗米乐测试站', 'https://fumile-test.com', '6', 'Server-6', '2024-06-01', '2024-12-31', 19000, 3900, '已安装', '弗米乐网站', 'active']
];

// 创建工作簿
const ws = XLSX.utils.aoa_to_sheet(testData);
const wb = XLSX.utils.book_new();
XLSX.utils.book_append_sheet(wb, ws, '测试导入');

// 保存文件
XLSX.writeFile(wb, 'test_import.xlsx');

console.log('测试Excel文件已创建: test_import.xlsx');
console.log('包含以下平台类型的测试数据:');
console.log('1 = WordPress');
console.log('2 = 易营宝');
console.log('3 = Shopify');
console.log('4 = 多谷');
console.log('5 = 巨旺');
console.log('6 = 弗米乐');
