/**
 * 测试缓存服务
 */

const CacheService = require('./backend/services/CacheService');

async function testCacheService() {
    console.log('🧪 开始测试缓存服务...');
    
    try {
        // 等待连接建立
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 测试设置缓存
        console.log('\n1. 测试设置缓存...');
        const testData = { 
            message: '这是测试数据', 
            timestamp: new Date().toISOString(),
            number: 12345
        };
        
        const setResult = await CacheService.set('test:key', testData, 60);
        console.log('设置结果:', setResult);
        
        // 测试获取缓存
        console.log('\n2. 测试获取缓存...');
        const getData = await CacheService.get('test:key');
        console.log('获取结果:', getData);
        
        // 测试缓存是否存在
        console.log('\n3. 测试缓存存在检查...');
        const exists = await CacheService.exists('test:key');
        console.log('缓存存在:', exists);
        
        // 测试获取统计信息
        console.log('\n4. 测试获取统计信息...');
        const stats = await CacheService.getStats();
        console.log('统计信息:', stats);
        
        // 测试删除缓存
        console.log('\n5. 测试删除缓存...');
        const delResult = await CacheService.del('test:key');
        console.log('删除结果:', delResult);
        
        // 再次检查是否存在
        console.log('\n6. 再次检查缓存是否存在...');
        const existsAfterDel = await CacheService.exists('test:key');
        console.log('删除后缓存存在:', existsAfterDel);
        
        console.log('\n✅ 缓存服务测试完成！');
        
    } catch (error) {
        console.error('❌ 缓存服务测试失败:', error);
    } finally {
        // 关闭连接
        await CacheService.close();
        process.exit(0);
    }
}

// 运行测试
testCacheService();