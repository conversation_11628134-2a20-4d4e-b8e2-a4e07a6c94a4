<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强网站管理API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.valid { background: #d4edda; color: #155724; }
        .status.expired { background: #f8d7da; color: #721c24; }
        .status.online { background: #d1ecf1; color: #0c5460; }
        .status.offline { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 增强网站管理API测试页面</h1>
        <p>测试所有新增的API功能，包括SSL检测、域名检测、性能评分和密码管理。</p>
        <div style="background: #e6f7ff; border: 1px solid #91d5ff; border-radius: 5px; padding: 10px; margin: 10px 0;">
            <strong>🎉 功能状态更新:</strong>
            <ul style="margin: 5px 0; padding-left: 20px;">
                <li>✅ SSL检测已修复 - 支持百度、GitHub等各种网站</li>
                <li>✅ 定时任务正常运行 - 访问状态每5分钟检测，SSL每天8:30检测</li>
                <li>✅ 前端状态联动 - 显示数据库中的真实状态</li>
                <li>✅ 手动触发功能 - 支持立即检测</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <div class="test-section">
            <h2>📋 网站列表</h2>
            <button onclick="fetchWebsites()">获取网站列表</button>
            <div id="websites-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🔒 SSL证书检测</h2>
            <button onclick="checkSSL(1)">检测网站1 SSL</button>
            <button onclick="checkSSL(2)">检测网站2 SSL</button>
            <div id="ssl-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🌐 域名状态检测</h2>
            <button onclick="checkDomain(1)">检测网站1 域名</button>
            <button onclick="checkDomain(2)">检测网站2 域名</button>
            <div id="domain-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>⚡ 性能检测</h2>
            <button onclick="checkPerformance(1)">检测网站1 性能</button>
            <button onclick="checkPerformance(2)">检测网站2 性能</button>
            <div id="performance-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🌍 访问状态检测</h2>
            <button onclick="checkAccess(1)">检测网站1 访问</button>
            <button onclick="checkAccess(2)">检测网站2 访问</button>
            <div id="access-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>🔑 密码管理</h2>
            <button onclick="getCredentials(1)">获取网站1 密码</button>
            <button onclick="getCredentials(2)">获取网站2 密码</button>
            <div id="credentials-result" class="result"></div>
        </div>

        <div class="test-section">
            <h2>⏰ 定时任务管理</h2>
            <button onclick="triggerAccessCheck()">手动触发访问状态检测</button>
            <button onclick="triggerSSLCheck()">手动触发SSL检测</button>
            <div id="scheduler-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001/api/v1';

        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, options);
                const data = await response.json();
                return data;
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function fetchWebsites() {
            const result = await apiCall(`${API_BASE}/websites`);
            const resultDiv = document.getElementById('websites-result');
            
            if (result.success) {
                resultDiv.className = 'result success';
                let output = `✅ 获取成功！共 ${result.data.websites.length} 个网站\n\n`;
                
                result.data.websites.forEach(site => {
                    output += `🌐 ${site.siteName} (${site.domain})\n`;
                    output += `   SSL状态: ${getStatusBadge(site.sslStatus)} | `;
                    output += `访问状态: ${getStatusBadge(site.accessStatus)} | `;
                    output += `性能评分: ${site.performanceScore || '未检测'}\n`;
                    output += `   到期时间: ${site.expireDate || '未设置'} | `;
                    output += `SSL到期: ${site.sslExpireDate || '未检测'}\n\n`;
                });
                
                resultDiv.textContent = output;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 获取失败: ${result.message || result.error}`;
            }
        }

        async function checkSSL(websiteId) {
            const result = await apiCall(`${API_BASE}/websites/${websiteId}/check-ssl`, { method: 'POST' });
            const resultDiv = document.getElementById('ssl-result');
            
            if (result.success) {
                resultDiv.className = 'result success';
                const data = result.data;
                resultDiv.textContent = `✅ SSL检测完成 (网站${websiteId})\n\n` +
                    `状态: ${data.status}\n` +
                    `颁发者: ${data.issuer}\n` +
                    `到期日期: ${data.expireDate}\n` +
                    `剩余天数: ${data.daysUntilExpiry}天\n` +
                    `主题: ${data.subject}\n` +
                    `序列号: ${data.serialNumber}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ SSL检测失败 (网站${websiteId}): ${result.message || result.error}`;
            }
        }

        async function checkDomain(websiteId) {
            const result = await apiCall(`${API_BASE}/websites/${websiteId}/check-domain`, { method: 'POST' });
            const resultDiv = document.getElementById('domain-result');
            
            if (result.success) {
                resultDiv.className = 'result success';
                const data = result.data;
                resultDiv.textContent = `✅ 域名检测完成 (网站${websiteId})\n\n` +
                    `状态: ${data.status}\n` +
                    `注册商: ${data.registrar}\n` +
                    `到期日期: ${data.expireDate}\n` +
                    `剩余天数: ${data.daysUntilExpiry}天\n` +
                    `DNS有效: ${data.hasValidDNS ? '是' : '否'}\n` +
                    `IP地址: ${data.ipAddresses?.join(', ') || '无'}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 域名检测失败 (网站${websiteId}): ${result.message || result.error}`;
            }
        }

        async function checkPerformance(websiteId) {
            const result = await apiCall(`${API_BASE}/websites/${websiteId}/check-performance`, { method: 'POST' });
            const resultDiv = document.getElementById('performance-result');
            
            if (result.success) {
                resultDiv.className = 'result success';
                const data = result.data;
                resultDiv.textContent = `✅ 性能检测完成 (网站${websiteId})\n\n` +
                    `综合评分: ${data.performanceScore}/100\n` +
                    `移动端评分: ${data.mobileScore}/100\n` +
                    `桌面端评分: ${data.desktopScore}/100\n` +
                    `页面加载时间: ${data.pageLoadTime}秒\n` +
                    `首次内容绘制: ${data.firstContentfulPaint}秒\n` +
                    `最大内容绘制: ${data.largestContentfulPaint}秒\n` +
                    `累积布局偏移: ${data.cumulativeLayoutShift}\n` +
                    `响应时间: ${data.responseTime}ms`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 性能检测失败 (网站${websiteId}): ${result.message || result.error}`;
            }
        }

        async function checkAccess(websiteId) {
            const result = await apiCall(`${API_BASE}/websites/${websiteId}/access-check`, { method: 'POST' });
            const resultDiv = document.getElementById('access-result');
            
            if (result.success) {
                resultDiv.className = 'result success';
                const data = result.data;
                resultDiv.textContent = `✅ 访问检测完成 (网站${websiteId})\n\n` +
                    `状态码: ${data.statusCode}\n` +
                    `状态: ${data.status}\n` +
                    `响应时间: ${data.responseTime}ms\n` +
                    `服务器: ${data.headers?.server || '未知'}\n` +
                    `内容类型: ${data.headers?.['content-type'] || '未知'}\n` +
                    `检测时间: ${data.lastChecked}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 访问检测失败 (网站${websiteId}): ${result.message || result.error}`;
            }
        }

        async function getCredentials(websiteId) {
            const result = await apiCall(`${API_BASE}/websites/${websiteId}/credentials`);
            const resultDiv = document.getElementById('credentials-result');
            
            if (result.success) {
                resultDiv.className = 'result success';
                let output = `✅ 密码获取成功 (网站${websiteId})\n\n`;
                
                if (result.data.length === 0) {
                    output += '暂无密码记录';
                } else {
                    result.data.forEach((cred, index) => {
                        output += `${index + 1}. 账号类型: ${cred.account_type}\n`;
                        output += `   用户名: ${cred.username}\n`;
                        output += `   密码: ${cred.password}\n`;
                        output += `   邮箱: ${cred.email || '未设置'}\n`;
                        output += `   登录URL: ${cred.url || '未设置'}\n`;
                        output += `   描述: ${cred.description || '无'}\n\n`;
                    });
                }
                
                resultDiv.textContent = output;
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 密码获取失败 (网站${websiteId}): ${result.message || result.error}`;
            }
        }

        async function triggerAccessCheck() {
            const result = await apiCall(`${API_BASE}/websites/trigger-access-check`, { method: 'POST' });
            const resultDiv = document.getElementById('scheduler-result');

            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ ${result.message}\n\n请等待几秒钟后刷新网站列表查看更新结果。`;

                // 3秒后自动刷新网站列表
                setTimeout(() => {
                    fetchWebsites();
                }, 3000);
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 触发失败: ${result.message || result.error}`;
            }
        }

        async function triggerSSLCheck() {
            const result = await apiCall(`${API_BASE}/websites/trigger-ssl-check`, { method: 'POST' });
            const resultDiv = document.getElementById('scheduler-result');

            if (result.success) {
                resultDiv.className = 'result success';
                resultDiv.textContent = `✅ ${result.message}\n\n请等待几秒钟后刷新网站列表查看更新结果。`;

                // 5秒后自动刷新网站列表
                setTimeout(() => {
                    fetchWebsites();
                }, 5000);
            } else {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 触发失败: ${result.message || result.error}`;
            }
        }

        function getStatusBadge(status) {
            const statusMap = {
                'valid': '✅ 有效',
                'expired': '❌ 已过期',
                'expiring_soon': '⚠️ 即将过期',
                'online': '🟢 在线',
                'offline': '🔴 离线',
                'error': '❌ 错误',
                'unknown': '❓ 未知'
            };
            return statusMap[status] || status;
        }

        // 页面加载时自动获取网站列表
        window.onload = function() {
            fetchWebsites();
        };
    </script>
</body>
</html>
