#!/bin/bash

# 服务器删除功能测试脚本
# 测试前端和后端的服务器删除功能是否正常工作

API_BASE="http://localhost:3001/api/v1"

echo "🧪 开始测试服务器删除功能..."
echo ""

# 1. 获取当前服务器列表
echo "1️⃣ 获取当前服务器列表..."
response=$(curl -s "$API_BASE/servers")
success=$(echo "$response" | jq -r '.success')

if [ "$success" != "true" ]; then
    echo "❌ 获取服务器列表失败"
    exit 1
fi

initial_count=$(echo "$response" | jq '.data.servers | length')
echo "   ✅ 当前服务器数量: $initial_count"

if [ "$initial_count" -eq 0 ]; then
    echo "   ⚠️  没有服务器可以删除，测试结束"
    exit 0
fi

# 2. 选择一个测试服务器进行删除（选择最后一个）
test_server_id=$(echo "$response" | jq -r '.data.servers[-1].id')
test_server_name=$(echo "$response" | jq -r '.data.servers[-1].name')
echo "   📋 选择删除服务器: $test_server_name (ID: $test_server_id)"

# 3. 执行删除操作
echo ""
echo "2️⃣ 执行删除操作..."
delete_response=$(curl -s -X DELETE "$API_BASE/servers/$test_server_id")
delete_success=$(echo "$delete_response" | jq -r '.success')

if [ "$delete_success" != "true" ]; then
    echo "❌ 删除服务器失败"
    echo "$delete_response" | jq .
    exit 1
fi

delete_message=$(echo "$delete_response" | jq -r '.message')
echo "   ✅ 删除成功: $delete_message"

# 4. 验证删除结果
echo ""
echo "3️⃣ 验证删除结果..."
verify_response=$(curl -s "$API_BASE/servers")
verify_success=$(echo "$verify_response" | jq -r '.success')

if [ "$verify_success" != "true" ]; then
    echo "❌ 验证删除结果失败"
    exit 1
fi

new_count=$(echo "$verify_response" | jq '.data.servers | length')
echo "   📊 删除前服务器数量: $initial_count"
echo "   📊 删除后服务器数量: $new_count"

expected_count=$((initial_count - 1))
if [ "$new_count" -eq "$expected_count" ]; then
    echo "   ✅ 删除验证成功！服务器数量正确减少"
else
    echo "   ❌ 删除验证失败！期望数量: $expected_count, 实际数量: $new_count"
    exit 1
fi

# 5. 验证被删除的服务器不再存在
deleted_server_exists=$(echo "$verify_response" | jq --arg id "$test_server_id" '.data.servers | any(.id == ($id | tonumber))')
if [ "$deleted_server_exists" = "true" ]; then
    echo "   ❌ 删除验证失败！被删除的服务器仍然存在"
    exit 1
fi

echo "   ✅ 被删除的服务器已从列表中移除"

# 6. 尝试获取已删除的服务器（应该返回404）
echo ""
echo "4️⃣ 验证已删除服务器不可访问..."
status_code=$(curl -s -o /dev/null -w "%{http_code}" "$API_BASE/servers/$test_server_id")

if [ "$status_code" = "404" ]; then
    echo "   ✅ 已删除的服务器正确返回404错误"
else
    echo "   ❌ 已删除的服务器返回状态码: $status_code (期望: 404)"
    exit 1
fi

echo ""
echo "🎉 服务器删除功能测试全部通过！"
echo "✅ 后端删除API正常工作"
echo "✅ 数据库删除操作成功"
echo "✅ 删除后数据一致性正确"
echo "✅ 已删除资源正确返回404"
