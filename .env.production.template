# SiteManager 生产环境配置模板
# 复制此文件为 .env.production 并修改相应配置

# ================================
# 应用基础配置
# ================================
NODE_ENV=production
PORT=3001
FRONTEND_PORT=3000

# 应用域名
DOMAIN=your-domain.com
APP_URL=https://your-domain.com

# ================================
# 数据库配置
# ================================
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sitemanager
DB_USER=sitemanager
DB_PASSWORD=your-secure-password

# MySQL Root 密码 (仅用于初始化)
MYSQL_ROOT_PASSWORD=your-root-password

# ================================
# Redis 配置 (可选)
# ================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ================================
# 安全配置
# ================================
# JWT密钥 (使用 openssl rand -base64 64 生成)
JWT_SECRET=your-jwt-secret-key

# 加密密钥 (使用 openssl rand -base64 32 生成)
ENCRYPTION_KEY=your-encryption-key

# 会话密钥
SESSION_SECRET=your-session-secret

# ================================
# 文件上传配置
# ================================
UPLOAD_MAX_SIZE=50MB
UPLOAD_PATH=/opt/sitemanager/backend/uploads

# ================================
# 邮件配置
# ================================
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_SECURE=false
MAIL_USER=<EMAIL>
MAIL_PASS=your-email-password
MAIL_FROM=SiteManager <<EMAIL>>

# ================================
# 通知配置
# ================================
# 飞书机器人
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-key

# 钉钉机器人
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=your-token

# 企业微信机器人
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key

# ================================
# 监控配置
# ================================
# 监控检查间隔 (分钟)
MONITOR_INTERVAL=5

# 监控重试次数
MONITOR_RETRIES=3

# 监控超时时间 (秒)
MONITOR_TIMEOUT=30

# ================================
# SSL配置
# ================================
# SSL证书路径
SSL_CERT_PATH=/etc/ssl/certs/your-domain.crt
SSL_KEY_PATH=/etc/ssl/private/your-domain.key

# ================================
# 日志配置
# ================================
LOG_LEVEL=info
LOG_PATH=/var/log/sitemanager
LOG_MAX_SIZE=10MB
LOG_MAX_FILES=30

# ================================
# 备份配置
# ================================
BACKUP_PATH=/var/backups/sitemanager
BACKUP_RETENTION_DAYS=30

# ================================
# 性能配置
# ================================
# 工作进程数 (0 = CPU核心数)
WORKER_PROCESSES=0

# 最大连接数
MAX_CONNECTIONS=1000

# 请求超时时间 (秒)
REQUEST_TIMEOUT=60

# ================================
# 开发调试配置
# ================================
DEBUG=false
VERBOSE_LOGGING=false

# ================================
# 第三方服务配置
# ================================
# coolmonitor 配置
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://your-domain.com/monitoring

# ================================
# Docker 配置 (仅Docker部署时使用)
# ================================
COMPOSE_PROJECT_NAME=sitemanager
COMPOSE_FILE=docker-compose.production.yml

# ================================
# 安全增强配置
# ================================
# 启用HTTPS重定向
FORCE_HTTPS=true

# 启用HSTS
ENABLE_HSTS=true

# 启用CSP
ENABLE_CSP=true

# 允许的域名 (CORS)
ALLOWED_ORIGINS=https://your-domain.com

# ================================
# 监控告警配置
# ================================
# 告警阈值
ALERT_CPU_THRESHOLD=80
ALERT_MEMORY_THRESHOLD=80
ALERT_DISK_THRESHOLD=85

# 告警通知间隔 (分钟)
ALERT_INTERVAL=30

# ================================
# 数据库连接池配置
# ================================
DB_CONNECTION_LIMIT=10
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000

# ================================
# 缓存配置
# ================================
CACHE_TTL=300
CACHE_MAX_SIZE=100

# ================================
# API限流配置
# ================================
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=1000

# ================================
# 文件存储配置
# ================================
# 存储类型: local | s3 | oss
STORAGE_TYPE=local

# S3配置 (如果使用S3)
S3_BUCKET=your-bucket
S3_REGION=us-east-1
S3_ACCESS_KEY=your-access-key
S3_SECRET_KEY=your-secret-key

# ================================
# 其他配置
# ================================
# 时区
TZ=Asia/Shanghai

# 语言
LANG=zh_CN.UTF-8
