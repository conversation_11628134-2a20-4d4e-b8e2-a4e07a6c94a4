# 魔改版SSL证书检查器

基于 [ChenYFan/CheckSSL](https://github.com/ChenYFan/CheckSSL) 项目魔改，集成到网站管理系统中。

## 🚀 功能特性

- **批量SSL检查**：从数据库读取网站列表，批量检查SSL证书状态
- **实时报告生成**：生成详细的SSL检查报告（JSON格式）
- **数据库集成**：自动更新数据库中的SSL信息
- **Web界面**：提供美观的Web界面查看SSL状态
- **API接口**：提供RESTful API接口
- **兼容性**：生成兼容原项目的ct.json格式

## 📊 检查内容

- SSL证书有效性
- 证书到期时间
- 剩余天数计算
- 证书颁发者信息
- 证书序列号
- 证书指纹
- 响应时间统计

## 🔧 使用方法

### 1. 命令行运行

```bash
# 进入SSL检查器目录
cd ssl-checker

# 运行SSL检查
node ssl-checker.js
```

### 2. API接口

```bash
# 批量SSL检查
curl -X POST http://localhost:3001/api/v1/ssl/check-all

# 获取SSL报告
curl http://localhost:3001/api/v1/ssl/report
```

### 3. Web界面

访问：http://localhost:3001/ssl-checker/web/

## 📁 文件结构

```
ssl-checker/
├── ssl-checker.js          # 主要检查器类
├── package.json            # 依赖配置
├── README.md              # 说明文档
├── web/
│   └── index.html         # Web界面
└── output/
    ├── ssl-report.json    # 详细报告
    └── ct.json           # 兼容格式报告
```

## 📈 报告格式

### 详细报告 (ssl-report.json)

```json
{
  "generatedAt": "2024-01-01T00:00:00.000Z",
  "totalWebsites": 100,
  "summary": {
    "valid": 85,
    "expired": 5,
    "expiring": 8,
    "error": 2,
    "total": 100
  },
  "websites": [
    {
      "domain": "example.com",
      "subject": "example.com",
      "start": "2023-01-01T00:00:00.000Z",
      "expire": "2024-12-31T23:59:59.000Z",
      "issuer": "Let's Encrypt",
      "status": "Valid",
      "statuscolor": "success",
      "check": "2024-01-01T00:00:00.000Z",
      "remain": 365,
      "serialNumber": "ABC123",
      "fingerprint": "SHA256:...",
      "keySize": 2048,
      "selfSigned": false,
      "responseTime": 150,
      "websiteId": 123,
      "siteName": "示例网站"
    }
  ]
}
```

### 兼容格式 (ct.json)

```json
[
  {
    "domain": "example.com",
    "subject": "example.com",
    "start": "2023-01-01T00:00:00.000Z",
    "expire": "2024-12-31T23:59:59.000Z",
    "issuer": "Let's Encrypt",
    "status": "Valid",
    "statuscolor": "success",
    "check": "2024-01-01T00:00:00.000Z",
    "remain": 365
  }
]
```

## 🎯 状态说明

| 状态 | 颜色 | 说明 |
|------|------|------|
| Valid | success (绿色) | 证书有效且未即将过期 |
| Expiring Soon | warning (黄色) | 证书即将过期 (≤30天) |
| Soon Expired | warning (黄色) | 证书即将过期 (≤7天) |
| Expired | error (红色) | 证书已过期 |
| Error | error (红色) | 检查过程中出现错误 |

## ⚙️ 配置说明

### 数据库配置

SSL检查器会自动使用系统的数据库配置：

```javascript
{
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: 'root123',
  database: 'sitemanager',
  charset: 'utf8mb4'
}
```

### 并发控制

- 并发检查数量：5个网站同时检查
- 请求间隔：每批次间隔1秒
- 超时时间：10秒

## 🔄 集成说明

### 与原系统的集成

1. **数据库更新**：检查结果自动更新到 `websites` 表的SSL相关字段
2. **API集成**：提供API接口供前端调用
3. **定时任务**：可集成到系统的定时任务中
4. **Web界面**：独立的SSL检查报告页面

### 数据库字段映射

| 检查器字段 | 数据库字段 | 说明 |
|------------|------------|------|
| status | ssl_status | SSL状态 |
| issuer | ssl_issuer | 证书颁发者 |
| expire | ssl_expire_date | 到期日期 |
| subject | ssl_subject | 证书主题 |
| start | ssl_valid_from | 生效日期 |
| serialNumber | ssl_serial_number | 序列号 |
| remain | ssl_days_until_expiry | 剩余天数 |

## 🎨 Web界面功能

- **实时统计**：显示各种状态的证书数量
- **筛选功能**：按状态筛选证书
- **搜索功能**：按域名搜索
- **导出功能**：导出检查报告
- **刷新功能**：手动触发SSL检查
- **响应式设计**：支持移动端访问

## 🚀 性能优化

- **并发控制**：避免过多并发请求
- **错误处理**：优雅处理连接失败
- **缓存机制**：报告文件缓存
- **超时控制**：避免长时间等待

## 📝 日志说明

检查过程中会输出详细的日志信息：

```
🔒 开始批量SSL检查...
✅ 数据库连接成功
📊 从数据库获取到 1253 个活跃网站
🔍 检查SSL证书: example.com
✅ SSL检查完成: example.com - Valid (365天)
🎉 SSL检查完成！共检查 1253 个网站
📄 JSON报告已生成: /path/to/output/ssl-report.json
📄 兼容格式报告已生成: /path/to/output/ct.json
💾 更新数据库SSL信息...
✅ 已更新 1253 个网站的SSL信息到数据库
🔌 数据库连接已关闭

📊 SSL检查统计:
   ✅ 有效证书: 1180 个
   ⚠️  即将过期: 58 个
   ❌ 已过期: 13 个
   🚫 错误: 2 个
   📊 总计: 1253 个
```

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置
   - 确认数据库服务运行正常

2. **SSL检查超时**
   - 检查网络连接
   - 增加超时时间设置

3. **权限问题**
   - 确认output目录写入权限
   - 检查文件系统权限

### 调试模式

可以通过修改代码启用更详细的调试信息：

```javascript
// 在ssl-checker.js中添加
console.log('调试信息:', result);
```

## 📄 许可证

MIT License - 基于原项目许可证
