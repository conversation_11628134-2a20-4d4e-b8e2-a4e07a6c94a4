/**
 * 魔改版SSL证书检查器
 * 基于 https://github.com/ChenYFan/CheckSSL 项目魔改
 * 集成到我们的网站管理系统中
 */

const mysql = require('mysql2/promise');
const https = require('https');
const tls = require('tls');
const fs = require('fs').promises;
const path = require('path');
const NotificationService = require('../backend/services/NotificationService');

class SSLChecker {
  constructor() {
    this.db = null;
    this.outputDir = path.join(__dirname, 'output');
    this.results = [];
    this.notificationService = new NotificationService();
    this.sslExpiryThreshold = 30; // SSL证书到期前30天开始通知
  }

  // 初始化数据库连接
  async initDatabase() {
    try {
      this.db = await mysql.createConnection({
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'root123',
        database: 'sitemanager',
        charset: 'utf8mb4'
      });
      console.log('✅ 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  // 从数据库获取网站列表
  async getWebsitesFromDatabase() {
    try {
      const [rows] = await this.db.execute(`
        SELECT 
          id, site_name, domain, site_url, status,
          ssl_status, ssl_expire_date, ssl_days_until_expiry
        FROM websites 
        WHERE status = 'active' 
        ORDER BY site_name
      `);
      
      console.log(`📊 从数据库获取到 ${rows.length} 个活跃网站`);
      return rows;
    } catch (error) {
      console.error('❌ 获取网站列表失败:', error.message);
      throw error;
    }
  }

  // 检查单个网站的SSL证书
  async checkSSLCertificate(domain) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      
      // 清理域名，去掉协议和路径
      let cleanDomain = domain;
      if (domain.includes('://')) {
        cleanDomain = new URL(domain).hostname;
      }
      
      console.log(`🔍 检查SSL证书: ${cleanDomain}`);

      const options = {
        hostname: cleanDomain,
        port: 443,
        method: 'HEAD',
        timeout: 10000,
        rejectUnauthorized: false
      };

      const req = https.request(options, (res) => {
        try {
          const cert = res.socket.getPeerCertificate(true);
          
          if (!cert || Object.keys(cert).length === 0) {
            resolve(this.createErrorResult(cleanDomain, 'No SSL certificate found'));
            return;
          }

          const now = new Date();
          const validFrom = new Date(cert.valid_from);
          const validTo = new Date(cert.valid_to);
          const daysUntilExpiry = Math.ceil((validTo - now) / (1000 * 60 * 60 * 24));
          
          // 确定状态和颜色
          let status, statusColor;
          if (validTo < now) {
            status = 'Expired';
            statusColor = 'error';
          } else if (daysUntilExpiry <= 7) {
            status = 'Soon Expired';
            statusColor = 'warning';
          } else if (daysUntilExpiry <= 30) {
            status = 'Expiring Soon';
            statusColor = 'warning';
          } else {
            status = 'Valid';
            statusColor = 'success';
          }

          const result = {
            domain: cleanDomain,
            subject: cert.subject?.CN || cleanDomain,
            start: validFrom.toISOString(),
            expire: validTo.toISOString(),
            issuer: cert.issuer?.CN || cert.issuer?.O || 'Unknown',
            status: status,
            statuscolor: statusColor,
            check: new Date().toISOString(),
            remain: daysUntilExpiry,
            serialNumber: cert.serialNumber || 'Unknown',
            fingerprint: cert.fingerprint || 'Unknown',
            keySize: cert.bits || 'Unknown',
            selfSigned: cert.selfSigned || false,
            responseTime: Date.now() - startTime
          };

          console.log(`✅ SSL检查完成: ${cleanDomain} - ${status} (${daysUntilExpiry}天)`);
          resolve(result);

        } catch (error) {
          console.warn(`⚠️  SSL证书解析失败: ${cleanDomain} - ${error.message}`);
          resolve(this.createErrorResult(cleanDomain, error.message));
        }
      });

      req.on('error', (error) => {
        console.warn(`❌ SSL连接失败: ${cleanDomain} - ${error.message}`);
        resolve(this.createErrorResult(cleanDomain, error.message));
      });

      req.on('timeout', () => {
        req.destroy();
        console.warn(`⏰ SSL检查超时: ${cleanDomain}`);
        resolve(this.createErrorResult(cleanDomain, 'Connection timeout'));
      });

      req.setTimeout(10000);
      req.end();
    });
  }

  // 创建错误结果
  createErrorResult(domain, errorMessage) {
    return {
      domain: domain,
      subject: domain,
      start: null,
      expire: null,
      issuer: 'Error',
      status: 'Error',
      statuscolor: 'error',
      check: new Date().toISOString(),
      remain: 0,
      serialNumber: 'N/A',
      fingerprint: 'N/A',
      keySize: 'N/A',
      selfSigned: false,
      responseTime: 0,
      error: errorMessage
    };
  }

  // 批量检查所有网站
  async checkAllWebsites() {
    try {
      console.log('🚀 开始批量SSL证书检查...');
      
      const websites = await this.getWebsitesFromDatabase();
      this.results = [];

      // 并发检查，但限制并发数量
      const concurrencyLimit = 5;
      const chunks = this.chunkArray(websites, concurrencyLimit);

      for (const chunk of chunks) {
        const promises = chunk.map(async (website) => {
          const domain = website.site_url || `https://${website.domain}`;
          const result = await this.checkSSLCertificate(domain);
          
          // 添加网站ID和名称信息
          result.websiteId = website.id;
          result.siteName = website.site_name;
          result.originalDomain = website.domain;
          
          return result;
        });

        const chunkResults = await Promise.all(promises);
        this.results.push(...chunkResults);
        
        // 避免过于频繁的请求
        if (chunks.indexOf(chunk) < chunks.length - 1) {
          await this.sleep(1000);
        }
      }

      console.log(`🎉 SSL检查完成！共检查 ${this.results.length} 个网站`);
      return this.results;

    } catch (error) {
      console.error('❌ 批量SSL检查失败:', error.message);
      throw error;
    }
  }

  // 将数组分块
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  // 延迟函数
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 生成JSON报告
  async generateJSONReport() {
    try {
      await fs.mkdir(this.outputDir, { recursive: true });
      
      const reportPath = path.join(this.outputDir, 'ssl-report.json');
      const report = {
        generatedAt: new Date().toISOString(),
        totalWebsites: this.results.length,
        summary: this.generateSummary(),
        websites: this.results
      };

      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
      console.log(`📄 JSON报告已生成: ${reportPath}`);
      
      return reportPath;
    } catch (error) {
      console.error('❌ 生成JSON报告失败:', error.message);
      throw error;
    }
  }

  // 生成兼容原项目的ct.json文件
  async generateCompatibleJSON() {
    try {
      await fs.mkdir(this.outputDir, { recursive: true });
      
      const ctPath = path.join(this.outputDir, 'ct.json');
      const compatibleData = this.results.map(result => ({
        domain: result.domain,
        subject: result.subject,
        start: result.start,
        expire: result.expire,
        issuer: result.issuer,
        status: result.status,
        statuscolor: result.statuscolor,
        check: result.check,
        remain: result.remain
      }));

      await fs.writeFile(ctPath, JSON.stringify(compatibleData, null, 2));
      console.log(`📄 兼容格式报告已生成: ${ctPath}`);
      
      return ctPath;
    } catch (error) {
      console.error('❌ 生成兼容报告失败:', error.message);
      throw error;
    }
  }

  // 生成统计摘要
  generateSummary() {
    const summary = {
      valid: 0,
      expired: 0,
      expiring: 0,
      error: 0,
      total: this.results.length
    };

    this.results.forEach(result => {
      switch (result.statuscolor) {
        case 'success':
          summary.valid++;
          break;
        case 'warning':
          summary.expiring++;
          break;
        case 'error':
          if (result.status === 'Expired') {
            summary.expired++;
          } else {
            summary.error++;
          }
          break;
        default:
          summary.error++;
      }
    });

    return summary;
  }

  // 更新数据库中的SSL信息
  async updateDatabaseSSLInfo() {
    try {
      console.log('💾 更新数据库SSL信息...');

      let updateCount = 0;
      for (const result of this.results) {
        if (result.websiteId && result.status !== 'Error') {
          await this.db.execute(`
            UPDATE websites SET
              ssl_status = ?,
              ssl_issuer = ?,
              ssl_expire_date = ?,
              ssl_subject = ?,
              ssl_valid_from = ?,
              ssl_serial_number = ?,
              ssl_days_until_expiry = ?,
              ssl_last_check = NOW(),
              updated_at = NOW()
            WHERE id = ?
          `, [
            result.statuscolor === 'success' ? 'valid' :
            result.statuscolor === 'warning' ? 'expiring_soon' : 'expired',
            result.issuer,
            result.expire ? result.expire.split('T')[0] : null,
            result.subject,
            result.start ? result.start.split('T')[0] : null,
            result.serialNumber,
            result.remain,
            result.websiteId
          ]);
          updateCount++;
        }
      }

      console.log(`✅ 已更新 ${updateCount} 个网站的SSL信息到数据库`);
    } catch (error) {
      console.error('❌ 更新数据库失败:', error.message);
      throw error;
    }
  }

  // 检查并发送SSL到期通知
  async checkAndSendSSLNotifications() {
    try {
      console.log('🔔 检查SSL证书到期通知...');

      // 初始化通知服务
      await this.notificationService.loadConfigFromDatabase();

      let notificationCount = 0;

      for (const result of this.results) {
        if (result.websiteId && result.status !== 'Error') {
          // 检查是否需要发送通知
          const shouldNotify = this.shouldSendSSLNotification(result);

          if (shouldNotify) {
            try {
              // 获取网站详细信息
              const [websiteRows] = await this.db.execute(`
                SELECT id, site_name, domain, site_url
                FROM websites
                WHERE id = ?
              `, [result.websiteId]);

              if (websiteRows.length > 0) {
                const website = websiteRows[0];

                // 发送SSL到期通知
                await this.notificationService.sendSSLExpiryNotification(
                  {
                    id: website.id,
                    site_name: website.site_name,
                    domain: website.domain,
                    url: website.site_url || `https://${website.domain}`
                  },
                  {
                    issuer: result.issuer,
                    expireDate: result.expire,
                    daysUntilExpiry: result.remain
                  }
                );

                notificationCount++;
                console.log(`📢 已发送SSL到期通知: ${website.site_name} (${result.remain}天后到期)`);
              }
            } catch (error) {
              console.error(`发送SSL通知失败 ${result.domain}:`, error);
            }
          }
        }
      }

      if (notificationCount > 0) {
        console.log(`🎯 SSL到期通知发送完成: ${notificationCount} 个通知已发送`);
      } else {
        console.log('ℹ️  没有需要发送SSL到期通知的网站');
      }

    } catch (error) {
      console.error('❌ 检查SSL到期通知失败:', error);
    }
  }

  // 判断是否应该发送SSL通知
  shouldSendSSLNotification(result) {
    // 如果证书已过期或即将过期（30天内）
    if (result.statuscolor === 'error' ||
        (result.statuscolor === 'warning' && result.remain <= this.sslExpiryThreshold)) {
      return true;
    }
    return false;
  }

  // 关闭数据库连接
  async close() {
    if (this.db) {
      await this.db.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 命令行运行支持
async function runSSLCheck() {
  const checker = new SSLChecker();

  try {
    await checker.initDatabase();
    await checker.checkAllWebsites();

    // 生成报告
    await checker.generateJSONReport();
    await checker.generateCompatibleJSON();

    // 更新数据库
    await checker.updateDatabaseSSLInfo();

    // 检查并发送SSL到期通知
    await checker.checkAndSendSSLNotifications();

    // 显示统计信息
    const summary = checker.generateSummary();
    console.log('\n📊 SSL检查统计:');
    console.log(`   ✅ 有效证书: ${summary.valid} 个`);
    console.log(`   ⚠️  即将过期: ${summary.expiring} 个`);
    console.log(`   ❌ 已过期: ${summary.expired} 个`);
    console.log(`   🚫 错误: ${summary.error} 个`);
    console.log(`   📊 总计: ${summary.total} 个`);

  } catch (error) {
    console.error('❌ SSL检查失败:', error.message);
    process.exit(1);
  } finally {
    await checker.close();
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runSSLCheck();
}

module.exports = SSLChecker;
