<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSL证书检查报告 - 网站管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-valid { @apply bg-green-100 text-green-800 border-green-200; }
        .status-warning { @apply bg-yellow-100 text-yellow-800 border-yellow-200; }
        .status-error { @apply bg-red-100 text-red-800 border-red-200; }
        .loading { animation: spin 1s linear infinite; }
        @keyframes spin { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- 头部 -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-blue-600 text-2xl mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900">SSL证书检查报告</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="refreshBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center">
                        <i class="fas fa-sync-alt mr-2"></i>
                        刷新检查
                    </button>
                    <button id="exportBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center">
                        <i class="fas fa-download mr-2"></i>
                        导出报告
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- 加载状态 -->
        <div id="loadingState" class="text-center py-12">
            <i class="fas fa-spinner loading text-4xl text-blue-600 mb-4"></i>
            <p class="text-gray-600">正在加载SSL检查报告...</p>
        </div>

        <!-- 统计卡片 -->
        <div id="statsCards" class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8" style="display: none;">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100">
                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">有效证书</p>
                        <p id="validCount" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100">
                        <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">即将过期</p>
                        <p id="expiringCount" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100">
                        <i class="fas fa-times-circle text-red-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">已过期</p>
                        <p id="expiredCount" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-gray-100">
                        <i class="fas fa-ban text-gray-600 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">检查错误</p>
                        <p id="errorCount" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 筛选和搜索 -->
        <div id="filterSection" class="bg-white rounded-lg shadow p-6 mb-6" style="display: none;">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">状态筛选</label>
                        <select id="statusFilter" class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">全部状态</option>
                            <option value="success">有效</option>
                            <option value="warning">即将过期</option>
                            <option value="error">已过期/错误</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">搜索域名</label>
                        <input type="text" id="searchInput" placeholder="输入域名搜索..." 
                               class="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="text-sm text-gray-500">
                    <span id="reportTime">报告生成时间: --</span>
                </div>
            </div>
        </div>

        <!-- SSL证书列表 -->
        <div id="sslTable" class="bg-white rounded-lg shadow overflow-hidden" style="display: none;">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">SSL证书详情</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">域名</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">剩余天数</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">颁发者</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">到期时间</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">检查时间</th>
                        </tr>
                    </thead>
                    <tbody id="sslTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 动态内容 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 错误状态 -->
        <div id="errorState" class="text-center py-12" style="display: none;">
            <i class="fas fa-exclamation-triangle text-4xl text-red-600 mb-4"></i>
            <p class="text-gray-600 mb-4">加载SSL检查报告失败</p>
            <button onclick="loadSSLReport()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                重试
            </button>
        </div>
    </main>

    <script>
        let sslData = [];
        let filteredData = [];

        // 页面加载时获取数据
        document.addEventListener('DOMContentLoaded', function() {
            loadSSLReport();
            
            // 绑定事件
            document.getElementById('refreshBtn').addEventListener('click', refreshSSLCheck);
            document.getElementById('exportBtn').addEventListener('click', exportReport);
            document.getElementById('statusFilter').addEventListener('change', filterData);
            document.getElementById('searchInput').addEventListener('input', filterData);
        });

        // 加载SSL报告
        async function loadSSLReport() {
            try {
                showLoading();
                
                const response = await fetch('/ssl-checker/output/ssl-report.json');
                if (!response.ok) {
                    throw new Error('无法加载SSL报告');
                }
                
                const data = await response.json();
                sslData = data.websites || [];
                
                updateStats(data.summary);
                updateReportTime(data.generatedAt);
                filterData();
                showContent();
                
            } catch (error) {
                console.error('加载SSL报告失败:', error);
                showError();
            }
        }

        // 刷新SSL检查
        async function refreshSSLCheck() {
            const btn = document.getElementById('refreshBtn');
            const originalText = btn.innerHTML;
            
            try {
                btn.innerHTML = '<i class="fas fa-spinner loading mr-2"></i>检查中...';
                btn.disabled = true;
                
                const response = await fetch('/api/v1/ssl/check-all', { method: 'POST' });
                if (!response.ok) {
                    throw new Error('SSL检查失败');
                }
                
                // 等待一下再重新加载数据
                setTimeout(() => {
                    loadSSLReport();
                }, 2000);
                
            } catch (error) {
                console.error('刷新SSL检查失败:', error);
                alert('SSL检查失败，请稍后重试');
            } finally {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }

        // 导出报告
        function exportReport() {
            const dataStr = JSON.stringify(sslData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `ssl-report-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
        }

        // 更新统计信息
        function updateStats(summary) {
            document.getElementById('validCount').textContent = summary.valid || 0;
            document.getElementById('expiringCount').textContent = summary.expiring || 0;
            document.getElementById('expiredCount').textContent = summary.expired || 0;
            document.getElementById('errorCount').textContent = summary.error || 0;
        }

        // 更新报告时间
        function updateReportTime(timestamp) {
            const date = new Date(timestamp);
            document.getElementById('reportTime').textContent = 
                `报告生成时间: ${date.toLocaleString('zh-CN')}`;
        }

        // 筛选数据
        function filterData() {
            const statusFilter = document.getElementById('statusFilter').value;
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            
            filteredData = sslData.filter(item => {
                const matchesStatus = !statusFilter || item.statuscolor === statusFilter;
                const matchesSearch = !searchTerm || 
                    item.domain.toLowerCase().includes(searchTerm) ||
                    (item.siteName && item.siteName.toLowerCase().includes(searchTerm));
                
                return matchesStatus && matchesSearch;
            });
            
            renderTable();
        }

        // 渲染表格
        function renderTable() {
            const tbody = document.getElementById('sslTableBody');
            tbody.innerHTML = '';
            
            filteredData.forEach(item => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';
                
                const statusClass = `status-${item.statuscolor}`;
                const statusIcon = getStatusIcon(item.statuscolor);
                
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">${item.domain}</div>
                        ${item.siteName ? `<div class="text-sm text-gray-500">${item.siteName}</div>` : ''}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${statusClass}">
                            <i class="${statusIcon} mr-1"></i>
                            ${item.status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${item.remain > 0 ? `${item.remain} 天` : item.remain === 0 ? '今天' : '已过期'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${item.issuer}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${item.expire ? new Date(item.expire).toLocaleDateString('zh-CN') : 'N/A'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${new Date(item.check).toLocaleString('zh-CN')}
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }

        // 获取状态图标
        function getStatusIcon(statusColor) {
            switch (statusColor) {
                case 'success': return 'fas fa-check-circle';
                case 'warning': return 'fas fa-exclamation-triangle';
                case 'error': return 'fas fa-times-circle';
                default: return 'fas fa-question-circle';
            }
        }

        // 显示状态函数
        function showLoading() {
            document.getElementById('loadingState').style.display = 'block';
            document.getElementById('statsCards').style.display = 'none';
            document.getElementById('filterSection').style.display = 'none';
            document.getElementById('sslTable').style.display = 'none';
            document.getElementById('errorState').style.display = 'none';
        }

        function showContent() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('statsCards').style.display = 'grid';
            document.getElementById('filterSection').style.display = 'block';
            document.getElementById('sslTable').style.display = 'block';
            document.getElementById('errorState').style.display = 'none';
        }

        function showError() {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('statsCards').style.display = 'none';
            document.getElementById('filterSection').style.display = 'none';
            document.getElementById('sslTable').style.display = 'none';
            document.getElementById('errorState').style.display = 'block';
        }
    </script>
</body>
</html>
