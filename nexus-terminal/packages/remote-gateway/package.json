{"name": "@nexus-terminal/remote-gateway", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "start": "node dist/server.js", "dev": "nodemon --exec \"ts-node --files src/server.ts\""}, "keywords": [], "author": "", "license": "ISC", "description": "Unified Remote Desktop Gateway for Nexus Terminal", "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/node": "^22.15.2", "@types/ws": "^8.18.1", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "guacamole-lite": "^0.7.3", "ws": "^8.18.1"}}