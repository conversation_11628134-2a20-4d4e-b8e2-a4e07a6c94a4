server {
    listen 80;
    server_name localhost;
    client_max_body_size 10m; # 允许最大 10MB 的请求体

    # Root directory for static files
    root /usr/share/nginx/html;
    index index.html index.htm;

    # Serve static files directly
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Proxy API requests to the backend service
    location ^~ /api/ { # Use ^~ for preferential prefix matching
        # rewrite ^/api(/.*)$ $1 break; # Remove /api prefix before proxying - REMOVED to match backend routes
        proxy_pass http://backend:3001; # Proxy to backend root, backend expects /api/v1/...

        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket support (if needed by the backend API)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Proxy WebSocket requests to the backend service
    location /ws/ {
        proxy_pass http://backend:3001; # Assuming backend handles WS on the same port

        # Required headers for WebSocket proxying
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # Standard proxy headers
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade; # Prevent caching issues
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Optional: Add headers for caching, security, etc.
    # Example: Cache assets aggressively
    location ~* \.(?:css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public";
    }
}
