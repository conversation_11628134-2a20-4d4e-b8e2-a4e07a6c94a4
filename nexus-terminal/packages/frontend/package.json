{"name": "@nexus-terminal/frontend", "version": "0.8.1", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview"}, "dependencies": {"@codemirror/commands": "^6.8.1", "@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-go": "^6.0.1", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/lang-php": "^6.0.1", "@codemirror/lang-python": "^6.2.1", "@codemirror/lang-rust": "^6.0.1", "@codemirror/lang-sql": "^6.9.0", "@codemirror/lang-xml": "^6.1.0", "@codemirror/lang-yaml": "^6.1.2", "@codemirror/legacy-modes": "^6.5.1", "@codemirror/search": "^6.5.11", "@fortawesome/fontawesome-free": "^6.7.2", "@hcaptcha/vue3-hcaptcha": "^1.3.0", "@lezer/markdown": "^1.4.3", "@simplewebauthn/browser": "^9.0.1", "@tailwindcss/vite": "^4.1.4", "@uiw/codemirror-theme-vscode": "^4.23.12", "@vscode/iconv-lite-umd": "^0.7.0", "@vueuse/core": "^13.1.0", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-search": "^0.15.0", "axios": "^1.8.4", "buffer": "^6.0.3", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "guacamole-common-js": "^1.5.0", "iconv-lite": "^0.6.3", "mitt": "^3.0.1", "monaco-editor": "^0.52.2", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "splitpanes": "^4.0.3", "vite-plugin-monaco-editor": "^1.1.0", "vue": "^3.3.0", "vue-chartjs": "^5.3.2", "vue-i18n": "^9.14.4", "vue-recaptcha-v3": "^2.0.1", "vue-router": "^4.5.0", "vue3-recaptcha2": "^1.8.0", "vuedraggable": "^4.1.0", "xterm": "^5.3.0", "xterm-addon-web-links": "^0.9.0"}, "devDependencies": {"@types/node": "^20", "@types/splitpanes": "^2.2.6", "@vitejs/plugin-vue": "^4.2.0", "typescript": "^5.0.0", "vite": ">=5.4.19", "vue-tsc": "^2.2.8"}}