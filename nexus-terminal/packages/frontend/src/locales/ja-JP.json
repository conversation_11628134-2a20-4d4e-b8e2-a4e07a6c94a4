{"appName": "星枢ターミナル", "auditLog": {"actions": {"2FA_DISABLED": "2段階認証無効", "2FA_ENABLED": "2段階認証有効", "ADMIN_SETUP_COMPLETE": "初期管理者設定完了", "CONNECTION_CREATED": "接続作成", "CONNECTION_DELETED": "接続削除", "CONNECTION_UPDATED": "接続更新", "DATABASE_MIGRATION": "データベース移行", "IP_WHITELIST_UPDATED": "IP ホワイトリスト更新", "LOGIN_FAILURE": "ログイン失敗", "LOGIN_SUCCESS": "ログイン成功", "LOGOUT": "ログアウト", "NOTIFICATION_SETTING_CREATED": "通知設定作成", "NOTIFICATION_SETTING_DELETED": "通知設定削除", "NOTIFICATION_SETTING_UPDATED": "通知設定更新", "PASSWORD_CHANGED": "パスワード変更", "PROXY_CREATED": "プロキシ作成", "PROXY_DELETED": "プロキシ削除", "PROXY_UPDATED": "プロキシ更新", "REMOTE_DESKTOP_CONNECTED": "リモートデスクトップ接続済", "REMOTE_DESKTOP_CONNECTING": "リモートデスクトップ接続中", "REMOTE_DESKTOP_DISCONNECTED": "リモートデスクトップ切断", "SETTINGS_UPDATED": "設定更新", "SSH_CONNECT_FAILURE": "SSH 接続失敗", "SSH_CONNECT_SUCCESS": "SSH 接続成功", "SSH_SHELL_FAILURE": "SSH Shell オープン失敗", "TAG_CREATED": "タグ作成", "TAG_DELETED": "タグ削除", "TAG_UPDATED": "タグ更新", "PASSKEY_REGISTERED": "パスキー登録済み", "PASSKEY_AUTH_SUCCESS": "パスキー認証成功", "PASSKEY_AUTH_FAILURE": "パスキー認証失敗", "PASSKEY_DELETED": "パスキー削除済み", "PASSKEY_DELETE_UNAUTHORIZED": "パスキー削除権限なし", "PASSKEY_NAME_UPDATED": "パスキー名更新済み", "PASSKEY_NAME_UPDATE_UNAUTHORIZED": "パスキー名更新権限なし"}, "noLogs": "監査ログが見つかりませんでした。", "paginationInfo": "{currentPage} ページ / 全 {totalPages} ページ ({totalLogs} 件のログ)", "searchPlaceholder": "詳細を", "table": {"actionType": "アクションタイプ", "details": "詳細", "timestamp": "タイムスタンプ"}, "title": "監査ログ"}, "commandHistory": {"clear": "クリア", "confirmClear": "すべての履歴をクリアしますか？", "copied": "クリップボードにコピーしました", "copy": "コピー", "copyFailed": "コピーに失敗しました", "delete": "削除", "empty": "履歴はありません", "loading": "ロード中...", "searchPlaceholder": "履歴を検索...", "actions": {"sendToAllSessions": "すべてのセッションに送信"}, "notifications": {"sentToAllSessions": "コマンドは {count} 個のセッションに送信されました。", "noActiveSshSessions": "コマンドを送信するアクティブな SSH セッションはありません。"}}, "commandInputBar": {"closeSearch": "ターミナル検索を閉じる", "configureFocusSwitch": "フォーカススイッチャーを設定", "findNext": "次を検索", "findPrevious": "前を検索", "openSearch": "ターミナル検索を開く", "placeholder": "ここにコマンドを入力して Enter キーを押すと、ターミナルに送信されます...", "searchPlaceholder": "ターミナル内で検索...", "clearTerminal": "ターミナルをクリア"}, "common": {"confirm": "確認", "ok": "確認", "success": "成功", "error": "失敗", "alert": "お知らせ", "apply": "適用", "all": "すべて", "cancel": "キャンセル", "close": "閉じる", "collapse": "折りたたむ", "delete": "削除", "disabled": "無効", "edit": "編集", "enabled": "有効", "errorOccurred": "エラーが発生しました。", "expand": "展開", "filter": "フィルター", "height": "高さ", "loading": "ロード中...", "reconnect": "再接続", "remove": "削除", "retry": "再試行", "save": "保存", "saving": "保存中...", "saved": "保存済み", "search": "検索", "settings": "設定", "sortAscending": "昇順", "sortDescending": "降順", "testing": "テスト中...", "width": "幅", "restore": "元に戻す", "minimize": "最小化", "send": "送信する", "updateSuccess": "更新に成功しました", "copied": "クリップボードにコピーしました"}, "connections": {"batchEdit": {"toggleLabel": "一括編集", "selectAll": "すべて選択", "deselectAll": "すべて選択解除", "invertSelection": "選択を反転", "title": "接続の一括編集", "editSelected": "選択した項目を編集", "noChange": "変更なし", "selectedItems": "{count} 件選択済み", "deleteSelectedButton": "選択した項目を削除", "deleteSelectedTooltip": "選択した接続を削除します", "confirmMessage": "選択した {count} 件の接続を本当に削除しますか？この操作は元に戻せません。", "successMessage": "選択した接続は正常に削除されました。", "errorMessage": "接続の一括削除に失敗しました: {error}"}, "actions": {"testAllFiltered": "すべてテスト", "connect": "接続", "delete": "削除", "edit": "編集", "test": "テスト", "testing": "テスト中...", "clone": "クローン"}, "addConnection": "新しい接続を追加", "addFirstConnection": "最初の接続を追加", "errors": {"deleteFailed": "接続の削除に失敗しました: {error}", "createFailed": "接続の追加に失敗しました: {error}", "cloneFailed": "接続のクローン作成に失敗しました: {error}"}, "form": {"adding": "追加中...", "authMethod": "認証方法:", "authMethodKey": "SSHキー", "authMethodPassword": "パスワード", "cancel": "キャンセル", "confirm": "追加", "confirmEdit": "編集を確定", "connectionType": "接続タイプ:", "errorAdd": "接続の追加に失敗しました: {error}", "errorPasswordRequired": "パスワード認証を使用する場合は、パスワードは必須です。", "errorPasswordRequiredOnSwitch": "パスワード認証に切り替える場合は、パスワードは必須です。", "errorPort": "ポート番号は 1 から 65535 の間である必要があります。", "errorPrivateKeyRequired": "キー認証を使用する場合は、秘密鍵は必須です。", "errorPrivateKeyRequiredOnSwitch": "キー認証に切り替える場合は、秘密鍵は必須です。", "errorRequiredFields": "すべての必須フィールドを入力してください。", "errorUpdate": "接続の更新に失敗しました: {error}", "host": "ホスト/IP:", "keyUpdateNote": "秘密鍵とパスフレーズを空のままにして、既存のキーを保持します。", "name": "名前:", "noProxy": "プロキシなし", "optional": "オプション", "passphrase": "パスフレーズ:", "password": "パスワード:", "port": "ポート:", "privateKey": "秘密鍵:", "noSshKey": "SSHキ<PERSON>なし", "proxy": "プロキシ:", "saving": "保存中...", "sectionAdvanced": "詳細設定", "sectionAuth": "認証情報", "sectionBasic": "基本情報", "tags": "タグ:", "notes": "備考：", "notesPlaceholder": "接続に関する備考を入力してください...", "connectionMode": "プロキシタイプ:", "connectionModeProxy": "プロキシサーバー", "connectionModeJumpHost": "踏み台サーバー", "testConnection": "接続をテスト", "testing": "テスト中...", "title": "新しい接続を追加", "titleEdit": "接続の編集", "typeRdp": "RDP", "typeSsh": "SSH", "username": "ユーザー名:", "sshKey": "SSH キー", "privateKeyDirect": "秘密鍵の内容", "keyUpdateNoteDirect": "編集時に既存のキーを保持するには、秘密鍵とパスフレーズを空のままにしてください。", "keyUpdateNoteSelected": "編集時にキーを変更するには、別のキーを選択するか、直接入力を使用してください。", "hostTooltip": "IP範囲の一括追加をサポート (例: ************~************、追加モードのみ)", "errorInvalidIpRangeFormat": "IP範囲の形式は start_ip~end_ip である必要があります", "errorInvalidIpFormat": "開始または終了IPアドレスの形式が無効です", "errorIpRangeNotSameSubnet": "IP範囲は同じCクラスサブネット内にある必要があります (例: 1.2.3.x ~ 1.2.3.y)", "errorInvalidIpSuffix": "IPアドレスの最後の部分は0〜255の数字である必要があります", "errorIpRangeStartAfterEnd": "範囲の開始IPは終了IPよりも大きくすることはできません", "errorIpRangeEmpty": "IP範囲を空にすることはできません。", "errorSshKeyRequired": "キー認証を使用する場合は、SSHキーを選択する必要があります。", "errorSshKeyRequiredOnSwitch": "キー認証に切り替える場合は、SSHキーを選択する必要があります。", "errorVncPasswordRequired": "VNCパスワードは必須です。", "errorSshKeyRequiredForBatch": "SSH (キー認証) 接続を一括追加する場合、SSHキーを選択する必要があります。", "errorPasswordRequiredForBatchSSH": "SSH (パスワード認証) 接続を一括追加する場合、パスワードを提供する必要があります。", "errorPasswordRequiredForBatchRDP": "RDP接続を一括追加する場合、パスワードを提供する必要があります。", "errorPasswordRequiredForBatchVNC": "VNC接続を一括追加する場合、VNCパスワードを提供する必要があります。", "errorBatchAddResult": "一括追加: {successCount} 件成功, {errorCount} 件失敗。最初のエラー: {firstErrorEncountered}", "successBatchAddResult": "一括追加成功: {successCount} 件の接続が作成されました。", "errorIpRangeNotAllowedInEditMode": "編集モードではIP範囲はサポートされていません。単一のIPアドレスを使用してください。", "scriptModeSubmitPlaceholder": "スクリプトモードの送信ロジックは実装予定です。", "scriptModeEmpty": "スクリプト入力は空にできません。", "scriptModeSubmitPending": "スクリプトモードの送信を処理中...", "sectionScriptMode": "スクリプトモード", "scriptModeInputLabel": "接続スクリプト (1行に1つ)", "scriptModePlaceholder": "接続スクリプトを入力してください。1行に1つの接続設定。", "scriptModeFormatInfo": "書式: ユーザー名@ホスト名またはIP:ポート [-type タイプ] [-name 名前] [-p パスワード] [-k キー名] [-proxy PROXY_NAME] [-tags タグ1 タグ2...] [-note 注意書き]\nパラメータ説明:\n  ユーザー名@ホスト名またはIP:ポート  - ユーザー名@ホスト名またはIPアドレス:ポート番号 (必須)\n  -type タイプ      - 接続タイプ (SSH, RDP, VNC; デフォルトは SSH)\n  -name 名前      - 接続の表示名 (任意; デフォルトは ユーザー名@ホスト名)\n  -p パスワード     - パスワード (SSHパスワード認証, RDP, VNC の場合に必要)\n  -k キー名     - SSHキー名 (SSHキー認証の場合に必要, アップロード済みのキーに対応)\n  -proxy PROXY_NAME - プロキシ名 (任意, 設定済みのプロキシに対応)\n  -tags タグ1 タグ2 - タグのリスト, スペース区切り (任意)\n  -note 注意書き - 接続に関する注意書き (任意, スペースを含むテキストをサポート, 特殊文字を含む場合は引用符を使用)", "scriptErrorMissingHost": "スクリプト行 '{line}' には 'user@host:port' 部分がありません。", "scriptErrorInvalidType": "スクリプト行 '{line}' のタイプ '{type}' は無効です。有効なタイプは SSH, RDP, VNC です。", "scriptErrorUnknownArg": "スクリプト行 '{line}' に不明な引数 '{arg}' があります。", "scriptErrorUnexpectedToken": "スクリプト行 '{line}' に予期しないトークン '{token}' があります。", "scriptErrorInvalidUserHostPort": "スクリプト行 '{line}' の '{part}' 部分の形式が無効です。期待される形式は 'user@host' または 'user@host:port' です。", "scriptErrorInLine": "スクリプト行の解析中にエラーが発生しました: \"{line}\" - エラー: {error}", "scriptErrorMissingType": "スクリプト行 '{line}' には接続タイプがないか、タイプが無効です。", "scriptErrorInvalidUserHostFormat": "スクリプト行 '{line}' の user@host 部分の形式が無効です。", "scriptErrorInvalidPort": "スクリプト行 '{line}' のポート '{port}' は無効です。", "scriptErrorMissingPasswordForSsh": "スクリプト行 '{line}' (SSHパスワード認証) にはパスワード (-p) がありません。", "scriptErrorMissingKeyNameForSsh": "スクリプト行 '{line}' (SSHキー認証) にはキー名 (-k) がありません。", "scriptErrorMissingPasswordForType": "スクリプト行 '{line}' ({type}タイプ) にはパスワード (-p) がありません。", "scriptErrorInternal": "スクリプト入力の処理中に内部解析エラーが発生しました。", "scriptErrorTagNotFound": "スクリプト処理エラー: タグ '{tagName}' が見つかりません。", "scriptErrorSshKeyNotFound": "スクリプト処理エラー: SSH キー '{keyName}' が見つかりません。", "scriptErrorNothingToProcess": "処理する有効な接続データがありません。", "scriptErrorMissingAuthForSsh": "SSH接続にはパスワード (-p) またはキー名 (-k) が必要です", "scriptErrorMissingPasswordForRdp": "RDP接続にはパスワード (-p) が必要です", "scriptErrorKeyNotApplicableForRdp": "キー名 (-k) はRDP接続には適用されません", "scriptErrorMissingPasswordForVnc": "VNC接続にはパスワード (-p) が必要です", "scriptErrorKeyNotApplicableForVnc": "キー名 (-k) はVNC接続には適用されません", "scriptErrorMissingValueForKey": "パラメータ '{key}' に対応する値がありません", "scriptErrorUnknownOption": "不明なオプション '{option}'", "scriptErrorUnexpectedArgument": "予期しない引数 '{argument}'", "scriptErrorEmptyLine": "入力行は空にできません", "scriptErrorInvalidUserHostPortFormat": "'{part}' の形式が無効です、期待される形式は 'user@host' または 'user@host:port' です", "scriptTagCreated": "タグ '{tagName}' が作成されました", "scriptErrorTagCreationFailed": "タグ '{tagName}' の作成に失敗しました", "scriptModeAddingConnections": "スクリプトモードで {count} 件の接続を追加中...", "jumpHostsTitle": "ジャンプホストチェーン設定", "jumpHostLabel": "ジャンプホスト", "selectJumpHost": "ジャンプホストを選択してください", "removeJumpHostTitle": "このジャンプホストを削除", "addJumpHost": "ジャンプホストを追加", "noAvailableSshConnectionsForJump": "ジャンプホストとして使用できるSSH接続がありません。先にSSH接続を作成してください。"}, "noConnections": "接続がありません。'新しい接続を追加'をクリックして作成してください。", "noUntaggedConnections": "タグなしの接続はありません。", "prompts": {"confirmDelete": "\"{name}\"接続を削除しますか？この操作は元に戻せません。"}, "status": {"never": "なし"}, "table": {"actions": "アクション", "authMethod": "認証方法", "host": "ホスト", "lastConnected": "最終接続", "name": "名前", "port": "ポート", "tags": "タグ", "user": "ユーザー名"}, "test": {"errorMissingFields": "ホスト、ポート、ユーザー名を入力し、認証方法を選択してください。", "errorNetwork": "ネットワークエラーまたはサーバーにアクセスできません。", "errorPrefix": "エラー:", "errorUnknown": "テスト中に不明なエラーが発生しました。", "failed": "接続テストに失敗しました: {error}", "latencyTooltip": "このレイテンシは、TCP接続、プロキシネゴシエーション、SSHハンドシェイク、認証などのステップを含む、新しいSSH接続の確立にかかる時間を測定します。通常、確立された接続上のインタラクションのレイテンシよりも高くなります。", "success": "接続テストに成功しました！", "testingInProgress": "テスト実行中..."}, "untaggedGroup": "タグなし"}, "dashboard": {"connectionList": "接続リスト", "lastConnected": "最終接続:", "noConnections": "接続記録がありません", "noRecentActivity": "最近のアクティビティ記録はありません", "noRecentConnections": "最近の接続記録はありません", "recentActivity": "最近のアクティビティ", "recentConnections": "最近の接続", "sortOptions": {"created": "作成日時", "lastConnected": "最終接続", "name": "名前", "type": "タイプ", "updated": "更新日時"}, "filterTags": {"all": "すべてのタグ"}, "noConnectionsWithTag": "このタグには接続記録がありません", "noConnectionsMatchSearch": "検索条件に一致する接続はありません。", "searchConnectionsPlaceholder": "接続を検索...", "viewAllConnections": "すべての接続を表示", "viewFullAuditLog": "完全な監査ログを表示"}, "dockerManager": {"action": {"remove": "削除", "restart": "再起動", "start": "起動", "stop": "停止", "enter": "入力", "logs": "ログ"}, "error": {"commandFailed": "リモートコマンド'{command}'の実行に失敗しました", "connectFirst": "最初にセッションに接続してください", "fetchFailed": "リモートコンテナの状態の取得に失敗しました", "invalidResponse": "無効なサーバー応答を受信しました", "noActiveSession": "アクティブなセッションはありません", "sshDisconnected": "SSHセッションが切断されました。", "sshError": "SSH接続エラー", "sshNotConnected": "SSHセッションは接続されていません。"}, "header": {"actions": "操作", "image": "イメージ", "name": "名前", "ports": "ポート", "status": "ステータス"}, "installHintRemote": "リモートホストにDockerがインストールされ、実行されていることを確認してください。", "loading": "Dockerコンテナをロード中...", "noContainers": "リモートホストで実行中または停止中のコンテナは見つかりませんでした。", "notAvailable": "リモートホストのDockerは利用できません", "stats": {"blockIO": "ブロックI/O", "cpu": "CPU使用率", "memory": "メモリ使用量/制限", "netIO": "ネットワークI/O", "noData": "利用可能な統計データはありません。", "pids": "プロセス数"}, "waitingForSsh": "SSH接続を待機中..."}, "fileEditor": {"title": "ファイルエディタ"}, "fileManager": {"modalTitle": "ファイルマネージャー", "actions": {"cancel": "キャンセル", "cdToTerminal": "ターミナルのディレクトリを現在のパスに変更", "changePermissions": "権限を変更", "closeEditor": "エディターを閉じる", "closeTab": "タブを閉じる", "copy": "コピー", "cut": "切り取り", "delete": "削除", "deleteMultiple": "{count} 個の項目を削除", "download": "ダウンロード", "downloadFolder": "フォルダをダウンロード", "downloadMultiple": "{count} 個のアイテムをダウンロード", "newFile": "新しいファイル", "newFolder": "新しいフォルダー", "parentDirectory": "上位ディレクトリ", "paste": "貼り付け", "openEditor": "エディターを開く", "refresh": "更新", "rename": "名前を変更", "save": "保存", "upload": "アップロード", "uploadFile": "ファイルをアップロード", "copyPath": "パスをコピー"}, "contextMenu": {"compress": "圧縮", "compressZip": "zip に圧縮", "compressTarGz": "tar.gz に圧縮", "compressTarBz2": "tar.bz2 に圧縮", "decompress": "解凍", "sendTo": "送信..."}, "currentPath": "現在のパス", "dropFilesHere": "ファイルをここにドラッグ＆ドロップしてアップロード", "editPathTooltip": "パスをクリックして編集", "editingFile": "編集中", "emptyDirectory": "ディレクトリは空です", "errors": {"chmodFailed": "権限の変更に失敗しました", "copyFailed": "コピーに失敗しました", "createFolderFailed": "フォルダーの作成に失敗しました", "deleteFailed": "削除に失敗しました", "downloadDirectoryFailed": "フォルダのダウンロードに失敗しました", "downloadDirectoryNotImplemented": "サーバーはフォルダダウンロード機能をまだ実装していません。", "fileDecodeError": "ファイルのデコードに失敗しました (UTF-8 エンコーディングではない可能性があります)", "fileExists": "ファイル \"{name}\" はすでに存在します。", "generic": "エラー", "invalidPermissionsFormat": "無効な権限形式です。3 桁または 4 桁の 8 進数を入力してください (例: 755 または 0755)。", "loadDirectoryFailed": "ディレクトリの読み込みに失敗しました", "missingConnectionId": "現在の接続 ID を取得できません", "moveFailed": "移動に失敗しました", "noActiveSession": "アクティブなセッションが見つかりません", "readFileError": "ファイルの読み取り中にエラーが発生しました", "readFileFailed": "ファイルの読み取りに失敗しました", "renameFailed": "名前の変更に失敗しました", "saveFailed": "ファイルの保存に失敗しました", "saveTimeout": "保存がタイムアウトしました", "sendCommandFailed": "コマンドの送信に失敗しました", "sftpManagerNotFound": "SFTP マネージャーが見つかりません", "sftpNotReady": "SFTP セッションの準備ができていません", "terminalManagerNotFound": "ターミナルマネージャーが見つかりません", "compressFailed": "圧縮に失敗しました", "compressTimeout": "圧縮がタイムアウトしました", "compressErrorDetailed": "圧縮に失敗しました: {error}", "decompressFailed": "解凍に失敗しました", "decompressTimeout": "解凍がタイムアウトしました", "decompressErrorDetailed": "解凍に失敗しました: {error}", "commandNotFoundCompress": "サーバーにコマンド '{command}' が見つからないため、圧縮操作を完了できません。", "commandNotFoundDecompress": "サーバーにコマンド '{command}' が見つからないため、解凍操作を完了できません。", "genericCommandNotFound": "サーバーにコマンド '{command}' が見つからないため、'{operation}' 操作を完了できません。", "copyPathFailed": "パスのコピーに失敗しました"}, "headers": {"modified": "変更日", "name": "名前", "permissions": "権限", "size": "サイズ", "type": "タイプ"}, "loading": "ディレクトリをロード中...", "loadingFile": "ファイルをロード中...", "noOpenFile": "ファイルが開いていません", "notifications": {"cdCommandSent": "CD コマンドがターミナルに送信されました", "copySuccess": "コピーに成功しました", "moveSuccess": "移動に成功しました", "compressSuccess": "{name} を正常に圧縮しました", "decompressSuccess": "{name} を正常に解凍しました", "pathCopied": "パスがクリップボードにコピーされました"}, "prompts": {"confirmDeleteFile": "ファイル \"{name}\" を削除しますか？この操作は元に戻せません。", "confirmDeleteFolder": "フォルダー \"{name}\" とそのすべての内容を削除しますか？この操作は元に戻せません。", "confirmDeleteMultiple": "選択した {count} 個の項目を削除しますか？この操作は元に戻せません。", "confirmOverwrite": "ファイル \"{name}\" はすでに存在します。上書きしますか？", "enterFileName": "新しいファイルの名前を入力してください:", "enterFolderName": "新しいフォルダーの名前を入力してください:", "enterNewName": "\"{oldName}\" の新しい名前を入力してください:", "enterNewPermissions": "\"{name}\" の新しい権限を入力してください (8 進数, 例: 755):"}, "saveError": "保存中にエラーが発生しました", "saveSuccess": "保存に成功しました", "saving": "保存中", "searchPlaceholder": "ファイルを検索...", "selectFileToEdit": "ファイルマネージャーから編集するファイルを選択してください。", "uploadStatus": {"cancelled": "キャンセルされました", "pending": "待機中", "uploading": "アップロード中"}, "uploadTasks": "アップロードタスク", "warnings": {"moveSameDirectory": "同じディレクトリ内で切り取りと貼り付けはできません。"}, "changeEncodingTooltip": "ファイルエンコーディングを変更", "loadingEncoding": "読み込み中...", "noSearchResults": "検索結果が見つかりませんでした", "modals": {"titles": {"delete": "\"{name}\" を削除", "deleteMultiple": "{count} 個のアイテムを削除", "rename": "\"{name}\" の名前を変更", "chmod": "\"{name}\" の権限を変更", "newFile": "新しいファイルを作成", "newFolder": "新しいフォルダーを作成"}, "buttons": {"delete": "削除", "rename": "名前を変更", "changePermissions": "権限を設定", "create": "作成", "confirm": "確認", "cancel": "キャンセル", "close": "閉じる"}, "messages": {"confirmDelete": "{type} \"{name}\" を削除してもよろしいですか？この操作は元に戻せません。", "confirmDeleteMultiple": "これらの {count} 個のアイテムを削除してもよろしいですか？この操作は元に戻せません。\nアイテム: {names}"}, "labels": {"newName": "新しい名前:", "newPermissions": "新しい権限 (8進数):", "fileName": "ファイル名:", "folderName": "フォルダー名:", "folder": "フォルダー", "file": "ファイル"}, "placeholders": {"newName": "新しい名前を入力", "newPermissions": "例: 755 または 0755", "newFile": "ファイル名を入力", "newFolder": "フォルダー名を入力"}, "chmodHelp": "8進数形式で権限を入力してください (例: 755 または 0755)。"}}, "focusSwitcher": {"allInputsConfigured": "すべての利用可能な入力ソースが設定されました", "altSwitchHint": "ヒント：Alt キーを押すと、設定された入力ソース間でフォーカスを素早く切り替えることができます。", "availableInputs": "利用可能な入力ソース", "configTitle": "フォーカススイッチャーの設定", "configuredSequence": "設定されたシーケンス（ドラッグしてソート）", "confirmClose": "未保存の変更があります。閉じてもよろしいですか？", "dragHere": "左側からここに入力ボックスをドラッグ", "input": {"commandHistorySearch": "コマンド履歴検索", "commandInput": "コマンド入力", "connectionListSearch": "接続リスト検索", "fileEditorActive": "ファイルエディター", "fileManagerPathInput": "ファイルマネージャーパス入力", "fileManagerSearch": "ファイルマネージャー検索", "quickCommandsSearch": "クイックコマンド検索", "terminalSearch": "ターミナル内検索"}, "noInputsAvailable": "設定可能な入力項目はありません", "shortcutPlaceholder": "例：Alt + K", "shortcutSettings": "ショートカット設定"}, "header": {"hide": "非表示"}, "layout": {"configure": "レイアウトを設定", "loading": "ロード中...", "noActiveSession": {"fileManagerSidebar": "ファイルマネージャーにはアクティブなセッションが必要です", "message": "最初にセッションに接続してください", "statusMonitorSidebar": "ステータスモニターにはアクティブなセッションが必要です", "title": "アクティブなセッションはありません"}, "pane": {"commandBar": "コマンドバー", "commandHistory": "コマンド履歴", "connections": "接続リスト", "dockerManager": "Docker マネージャー", "editor": "エディター", "fileManager": "ファイルマネージャー", "quickCommands": "クイックコマンド", "statusMonitor": "ステータスモニター", "terminal": "ターミナル", "suspendedSshSessions": "中断されたセッション管理"}, "panes": {"suspendedSshSessions": "中断されたセッション管理者"}}, "layoutConfigurator": {"availablePanes": "利用可能なパネル", "confirmClearLayout": "レイアウト全体をクリアしますか？すべてのパネルが利用可能なリストに戻ります。", "confirmClose": "未保存の変更があります。閉じてもよろしいですか？", "confirmReset": "デフォルトのレイアウトとサイドバー構成に戻しますか？現在の変更は失われます。", "dropHere": "利用可能なパネルからここにドラッグ", "emptyLayout": "レイアウトが空です。左側からパネルをドラッグするか、コンテナーを追加してください。", "layoutPreview": "メインレイアウトプレビュー (ここにドラッグ)", "leftSidebar": "左サイドバーパネル", "noAvailablePanes": "すべてのパネルがレイアウトにあります", "resetDefault": "デフォルトに戻す", "rightSidebar": "右サイドバーパネル", "saveError": "レイアウトの保存中にエラーが発生しました。後でもう一度お試しください。", "title": "レイアウトマネージャー", "lockLayout": "レイアウトをロック", "lockUpdateError": "レイアウトロック状態の更新に失敗しました。"}, "layoutNodeEditor": {"addHorizontalContainer": "水平コンテナーを追加", "addVerticalContainer": "垂直コンテナーを追加", "containerLabel": "コンテナー ({direction})", "dragHandle": "ドラッグして順序を調整または移動", "dropHere": "パネルまたはコンテナーをここにドラッグ", "horizontal": "水平", "removeNode": "このノードを削除", "toggleDirection": "方向を切り替える", "vertical": "垂直"}, "login": {"captchaPrompt": "以下の認証を完了してください：", "error": {"captchaLoadFailed": "CAPTCHA の読み込みに失敗しました。ページをリロードしてください。", "captchaRequired": "CAPTCHA を完了してください。", "usernameRequiredForPasskey": "Passkey を使用するにはユーザー名が必要です。", "passkeyAuthOptionsFailed": "サーバーから Passkey 認証オプションを取得できませんでした。", "passkeyAuthFailed": "Passkey 認証に失敗しました。もう一度試すか、パスワードを使用してください。"}, "loggingIn": "ログイン中...", "loginButton": "ログイン", "loginWithPasskey": "Passkeyでログイン", "password": "パスワード", "recaptchaV3Notice": "このサイトは reCAPTCHA によって保護されており、Google のプライバシーポリシーと利用規約が適用されます。", "rememberMe": "ログイン状態を保持", "title": "ユーザーログイン", "twoFactorPrompt": "2段階認証コードを入力してください:", "username": "ユーザー名", "verifyButton": "検証"}, "nav": {"auditLogs": "監査ログ", "customizeStyle": "外観のカスタマイズ", "dashboard": "ダッシュボード", "connections": "接続管理", "login": "ログイン", "logout": "ログアウト", "notifications": "通知管理", "proxies": "プロキシ管理", "settings": "設定", "terminal": "ターミナル"}, "notificationController": {"errorCreateSetting": "通知設定の作成に失敗しました", "errorDeleteNotFound": "ID {id} の通知設定の削除に失敗しました。すでに削除されている可能性があります", "errorDeleteSetting": "通知設定の削除に失敗しました", "errorFetchSettings": "通知設定の取得に失敗しました", "errorInvalidChannelType": "無効なチャネルタイプ", "errorInvalidId": "無効な通知設定 ID", "errorMissingFields": "必須の通知設定フィールドが不足しています (channel_type, name, config)", "errorMissingTestInfo": "必須のテスト情報が不足しています (channel_type, config)", "errorNoUpdateData": "更新するデータが提供されていません", "errorNotFound": "ID {id} の通知設定が見つかりません", "errorTriggerTest": "テスト通知のトリガー中に内部エラーが発生しました", "errorUpdateSetting": "通知設定の更新に失敗しました", "testEventTriggered": "テスト通知イベントがトリガーされました。対応するチャネルで受信を確認してください。", "testMessageSaved": "設定 ID {id} ({name}) のテストがトリガーされました", "testMessageUnsaved": "未保存の {channelType} 設定のテストがトリガーされました"}, "projectName": "星枢ターミナル", "proxies": {"actions": {"delete": "削除", "edit": "編集"}, "addProxy": "新しいプロキシを追加", "error": "プロキシリストのロードに失敗しました: {error}", "errors": {"deleteFailed": "プロキシの削除に失敗しました: {error}"}, "form": {"adding": "追加中...", "cancel": "キャンセル", "confirm": "追加", "confirmEdit": "編集を確定", "errorAdd": "プロキシの追加に失敗しました: {error}", "errorPort": "ポート番号は 1 から 65535 の間である必要があります。", "errorRequiredFields": "すべての必須フィールドを入力してください。", "errorUpdate": "プロキシの更新に失敗しました: {error}", "host": "ホスト/IP:", "name": "名前:", "optional": "オプション", "password": "パスワード:", "passwordUpdateNote": "パスワードを空のままにして、既存のパスワードを保持します。", "port": "ポート:", "saving": "保存中...", "title": "新しいプロキシを追加", "titleEdit": "プロキシの編集", "type": "タイプ:", "username": "ユーザー名:"}, "loading": "プロキシをロード中...", "noProxies": "プロキシがありません。'新しいプロキシを追加'をクリックして作成してください。", "prompts": {"confirmDelete": "\"{name}\"プロキシを削除しますか？この操作は元に戻せません。"}, "title": "プロキシ管理"}, "quickCommands": {"add": "追加", "addFirst": "最初のクイックコマンドを追加", "confirmDelete": "クイックコマンド\"{name}\"を削除しますか？", "empty": "クイックコマンドはありません。'+'ボタンをクリックして作成してください！", "form": {"add": "追加", "tags": "タグ:", "tagsPlaceholder": "タグを選択または作成...", "command": "コマンド:", "commandPlaceholder": "例：", "errorCommandRequired": "コマンドは空にできません", "name": "名前:", "namePlaceholder": "オプション。素早く認識するために使用", "titleAdd": "クイックコマンドの追加", "titleEdit": "クイックコマンドの編集", "variablesTitle": "変数管理", "noVariables": "変数はまだありません。下のボタンをクリックして追加してください。", "variableNamePlaceholder": "変数名", "variableValuePlaceholder": "変数値", "addVariable": "+ 変数を追加", "execute": "実行", "warningUndefinedVariables": "警告：コマンドテンプレートに未定義の変数があります: {variables}", "errorNoActiveSession": "コマンドを実行するためのアクティブなSSHセッションがありません。"}, "untagged": "タグなし", "tags": {"clickToEditTag": "クリックしてタグ名を編集"}, "searchPlaceholder": "名前またはコマンドを検索...", "sortByName": "名前", "sortByUsage": "使用頻度", "usageCount": "使用回数", "actions": {"sendToAllSessions": "すべてのセッションに送信"}, "notifications": {"sentToAllSessions": "コマンドは {count} 個のセッションに送信されました。", "noActiveSshSessions": "コマンドを送信するアクティブな SSH セッションはありません。"}}, "remoteDesktopModal": {"errors": {"clientError": "クライアントエラー", "connectionFailed": "接続に失敗しました", "inputError": "入力リスナーの設定中にエラーが発生しました。", "missingInfo": "接続情報または表示要素がありません。", "noConnection": "接続情報が提供されていません。", "tokenError": "トークンの取得に失敗しました", "tunnelError": "トンネルエラー"}, "reconnectTooltip": "リモートデスクトップに再接続", "status": {"connected": "接続済み", "connecting": "接続中...", "connectingRdp": "リモートデスクトップに接続中...", "connectingWs": "WebSocket に接続中...", "disconnected": "切断済み", "disconnecting": "切断中...", "error": "エラー", "fetchingToken": "接続トークンを取得中...", "idle": "アイドル", "unknownState": "不明な状態", "waiting": "サーバーの応答を待機中..."}, "title": "リモートデスクトップ", "titlePlaceholder": "リモートデスクトップ接続"}, "vncModal": {"title": "VNCセッション", "textInputPlaceholder": "VNC に送信するテキストをここに入力", "sendButtonTitle": "VNC にテキストを送信 (キーボード入力をシミュレート)", "errors": {"simulateInputError": "キーボード入力のシミュレート中にエラーが発生しました: {error}"}}, "settings": {"popupFileManager": {"title": "ポップアップファイルマネージャー", "enableLabel": "ポップアップファイルマネージャーを有効にする", "description": "有効にすると、コマンド入力バーにファイルマネージャーボタンが表示され、ポップアップファイルマネージャーを開くことができます。", "success": {"saved": "ポップアップファイルマネージャーの設定が保存されました。"}, "error": {"saveFailed": "ポップアップファイルマネージャーの設定の保存に失敗しました。"}}, "appearance": {"customizeButton": "外観をカスタマイズ", "description": "アプリケーションのビジュアルテーマと背景をカスタマイズします。", "title": "外観設定"}, "timezone": {"description": "通知のタイムスタンプはこのタイムゾーンに基づいてフォーマットされます。", "error": {"saveFailed": "タイムゾーン設定の保存に失敗しました。"}, "selectLabel": "タイムゾーンを選択:", "success": {"saved": "タイムゾーン設定が正常に保存されました。"}, "title": "タイムゾーン設定"}, "autoCopyOnSelect": {"enableLabel": "マウスボタンを離したときに選択したテキストを自動的にコピーする", "error": {"saveFailed": "自動コピーの設定の保存に失敗しました。"}, "saveButton": "保存", "success": {"saved": "自動コピーの設定を保存しました。"}, "title": "ターミナル自動コピー"}, "captcha": {"description": "自動化された攻撃を防ぐために、ログインページに CAPTCHA 検証を設定します。", "enableLabel": "ログインページで CAPTCHA を有効にする", "error": {"saveFailed": "CAPTCHA 設定の保存に失敗しました。", "hcaptchaKeysRequired": "hCaptcha サイトキーとシークレットキーは検証に必要です。", "recaptchaKeysRequired": "reCAPTCHA サイトキーとシークレットキーは検証に必要です。", "verificationFailed": "CAPTCHA 認証情報検証に失敗しました。サイトキーとシークレットキーを確認してください。"}, "hcaptchaHint": "から", "providerLabel": "CAPTCHA プロバイダー:", "providerNone": "なし (無効)", "recaptchaHint": "から", "saveButton": "CAPTCHA 設定を保存", "secretKeyHint": "このキーは安全に保管してください。サーバーに安全に保存されます。", "secretKeyLabel": "シークレットキー (非公開):", "siteKeyLabel": "サイトキー (公開):", "success": {"saved": "CAPTCHA 設定を保存しました。"}, "title": "CAPTCHA 設定"}, "category": {"appearance": "外観設定", "security": "セキュリティ設定", "system": "システム設定", "about": "バージョン情報", "dataManagement": "データ管理"}, "changePassword": {"confirmPassword": "新しいパスワードを再入力:", "currentPassword": "現在のパスワード:", "error": {"generic": "パスワードの変更に失敗しました。後でもう一度お試しください。", "passwordsDoNotMatch": "新しいパスワードと確認用パスワードが一致しません。"}, "newPassword": "新しいパスワード:", "submit": "変更を確定", "success": "パスワードの変更に成功しました！", "title": "パスワードを変更"}, "commandInputSync": {"description": "コマンド入力バーの内容を選択したパネルの検索ボックスにリアルタイムで同期します。上下キーで選択した後、Enter を押してコマンドを実行します。", "error": {"saveFailed": "同期ターゲットの保存に失敗しました。"}, "selectLabel": "同期ターゲット:", "success": {"saved": "同期ターゲットを保存しました。"}, "targetCommandHistory": "コマンド履歴", "targetNone": "なし", "targetQuickCommands": "クイックコマンド", "title": "コマンド入力同期"}, "docker": {"defaultExpandLabel": "デフォルトでコンテナの詳細を展開", "error": {"invalidInterval": "更新間隔は正の整数である必要があります。", "saveFailed": "Docker 設定の保存に失敗しました。"}, "refreshIntervalHint": "Docker コンテナのステータスと統計情報を取得する頻度 (最小値は 1)。", "refreshIntervalLabel": "ステータス更新間隔 (秒):", "saveButton": "Docker 設定を保存", "success": {"saved": "Docker 設定を保存しました。"}, "title": "Docker マネージャー設定"}, "ipBlacklist": {"banDurationLabel": "禁止時間 (秒):", "confirmRemoveIp": "IP アドレス \"{ip}\" をブラックリストから削除しますか？", "currentBannedTitle": "現在禁止されている IP アドレス", "description": "ログイン試行回数制限と自動禁止時間を設定します。ローカルアドレス (127.0.0.1, ::1) は禁止されません。", "error": {"deleteFailed": "削除に失敗しました", "fetchFailed": "ブラックリストの取得に失敗しました", "invalidBanDuration": "禁止時間は正の整数 (秒) である必要があります。", "invalidMaxAttempts": "最大試行回数は正の整数である必要があります。", "updateConfigFailed": "ブラックリスト構成の更新に失敗しました"}, "loadingList": "ブラックリストを読み込み中...", "maxAttemptsLabel": "最大試行回数:", "noBannedIps": "ブラックリストに IP アドレスはありません。", "saveConfigButton": "構成を保存", "success": {"configUpdated": "ブラックリスト構成を保存しました。"}, "table": {"actions": "アクション", "attempts": "試行回数", "bannedUntil": "禁止終了時間", "deleting": "削除中...", "ipAddress": "IP アドレス", "lastAttempt": "最終試行時間", "removeButton": "削除"}, "title": "IP ブラックリスト管理"}, "ipWhitelist": {"description": "このアプリケーションへのアクセスを許可する IP アドレスまたは範囲を設定します。空白のままにすると、すべての IP が許可されます。", "error": {"saveFailed": "IP ホワイトリストの保存に失敗しました。"}, "hint": "IPv4, IPv6 および CIDR をサポート (例: ************0, 10.0.0.0/8, 2001:db8::/32)。", "label": "許可された IP アドレス/範囲 (1 行に 1 つまたはカンマ区切り):", "saveButton": "ホワイトリストを保存", "success": {"saved": "IP ホワイトリストを保存しました。"}, "title": "IP ホワイトリスト"}, "language": {"error": {"saveFailed": "言語設定の保存に失敗しました。"}, "saveButton": "言語を保存", "selectLabel": "インターフェース言語:", "success": {"saved": "言語設定を保存しました。"}, "title": "言語設定"}, "notifications": {"addChannel": "通知チャンネルを追加", "confirmDelete": "通知チャンネル\"{name}\"を削除しますか？この操作は元に戻せません。", "events": {"2FA_DISABLED": "2段階認証無効", "2FA_ENABLED": "2段階認証有効", "PASSKEY_REGISTERED": "パスキー登録済み", "PASSKEY_AUTH_SUCCESS": "パスキー認証成功", "PASSKEY_AUTH_FAILURE": "パスキー認証失敗", "PASSKEY_DELETED": "パスキー削除済み", "ADMIN_SETUP_COMPLETE": "初期管理者設定完了", "CONNECTIONS_EXPORTED": "接続がエクスポートされました", "CONNECTION_CREATED": "接続作成", "CONNECTION_DELETED": "接続削除", "CONNECTION_UPDATED": "接続更新", "DATABASE_MIGRATION": "データベース移行", "IP_WHITELIST_UPDATED": "IP ホワイトリスト更新", "IP_BLOCKED": "IPブロック済み", "LOGIN_FAILURE": "ログイン失敗", "LOGIN_SUCCESS": "ログイン成功", "LOGOUT": "ログアウト", "NOTIFICATION_SETTING_CREATED": "通知設定作成", "NOTIFICATION_SETTING_DELETED": "通知設定削除", "NOTIFICATION_SETTING_UPDATED": "通知設定更新", "PASSWORD_CHANGED": "パスワード変更", "PROXY_CREATED": "プロキシ作成", "PROXY_DELETED": "プロキシ削除", "PROXY_UPDATED": "プロキシ更新", "SETTINGS_UPDATED": "設定更新", "SSH_CONNECT_FAILURE": "SSH 接続失敗", "SSH_CONNECT_SUCCESS": "SSH 接続成功", "SSH_SHELL_FAILURE": "SSH Shell オープン失敗", "TAG_CREATED": "タグ作成", "TAG_DELETED": "タグ削除", "TAG_UPDATED": "タグ更新"}, "form": {"addTitle": "通知チャンネルの追加", "channelType": "チャンネルタイプ:", "channelTypeEditNote": "作成後はチャンネルタイプを変更できません。", "editTitle": "通知チャンネルの編集", "emailBodyPlaceholder": "デフォルト: イベントベースの通知内容。利用可能:", "emailBodyTemplate": "メール本文テンプレート (オプション)", "emailTo": "宛先メールアドレス:", "emailToHelp": "複数のメールアドレスをカンマで区切ります。", "enabledEvents": "有効なイベント:", "fillRequiredToTest": "テストを有効にするには、必須フィールドを入力してください。", "invalidJson": "無効な JSON 形式", "name": "チャンネル名:", "smtpFrom": "送信元メールアドレス:", "smtpFromHelp": "'From' フィールドで使用されるアドレス。", "smtpHost": "SMTP ホスト:", "smtpPass": "SMTP パスワード:", "smtpPort": "SMTP ポート:", "smtpSecure": "TLS/SSL を使用", "smtpUser": "SMTP ユーザー名:", "telegramChatId": "チャット ID:", "telegramMessagePlaceholder": "デフォルト: Markdown 形式。利用可能:", "telegramMessageTemplate": "メッセージテンプレート (オプション)", "telegramCustomDomain": "カスタム Telegram API ドメイン", "telegramToken": "ボットトークン:", "telegramTokenHelp": "安全に保管してください。環境変数の使用をお勧めします。", "templateHelp": "利用可能なプレースホルダー:", "testButton": "テスト通知", "testFailed": "テスト通知の送信に失敗しました", "testSuccess": "テスト通知の送信に成功しました！", "webhookBodyPlaceholder": "デフォルト: JSON フォーマットのペイロード。利用可能:", "webhookBodyTemplate": "リクエスト本文テンプレート (オプション)", "webhookHeaders": "カスタムヘッダー", "webhookMethod": "HTTP メソッド:"}, "noChannels": "通知チャンネルが設定されていません。", "noEventsEnabled": "有効なイベントはありません", "title": "通知設定", "triggers": "トリガーイベント", "types": {"email": "メール", "telegram": "Telegram", "webhook": "Webhook"}}, "passkey": {"title": "Passkey 管理", "description": "Passkey (生体認証またはセキュリティキー) を使用してパスワードなし認証を行い", "nameLabel": "Passkey 名", "namePlaceholder": "例: マイノートパソコン", "registerNewButton": "新しい Passkey を登録", "registeredKeysTitle": "登録済みの Passkey", "unnamedKey": "名前のない Passkey", "createdDate": "作成日", "lastUsedDate": "最終使用日", "noKeysRegistered": "Passkey はまだ登録されていません。", "confirmDelete": "この Passkey を削除しますか？この操作は元に戻せません。", "error": {"nameRequired": "Passkey 名を入力してください。", "cancelled": "Passkey の登録がキャンセルされました。", "genericRegistration": "Passkey を登録できません: {message}", "verificationFailed": "登録に失敗しました: {message}", "userNotLoggedIn": "ユーザーがログインしていないか、ユーザー名が利用できません。", "registrationCancelled": "Passkey の登録がキャンセルされました。", "registrationFailed": "Passkey の登録に失敗しました。", "deleteFailedGeneral": "Passkey の削除に失敗しました。もう一度お試しください。"}, "success": {"registered": "新しい Passkey が正常に登録されました！", "deleted": "Passkey が正常に削除されました。", "nameUpdated": "Passkey 名が更新されました。"}}, "popupEditor": {"enableLabel": "ファイルを開くときにポップアップエディターを表示する", "error": {"saveFailed": "ポップアップエディターの設定の保存に失敗しました。"}, "saveButton": "設定を保存", "success": {"saved": "ポップアップエディターの設定を保存しました。"}, "title": "ポップアップファイルエディター"}, "shareEditorTabs": {"description": "有効にすると、すべての SSH セッションが同じ開いているファイルエディタータブのセットを共有します。無効にすると、各セッションには独自の独立したタブのセットがあります。", "enableLabel": "すべてのセッションでエディタータブを共有する", "error": {"saveFailed": "エディタータブの共有設定の保存に失敗しました。"}, "saveButton": "設定を保存", "success": {"saved": "エディタータブの共有設定を保存しました。"}, "title": "エディタータブ"}, "statusMonitor": {"error": {"invalidInterval": "更新間隔は正の整数である必要があります。", "saveFailed": "ステータスモニター設定の保存に失敗しました。"}, "refreshIntervalHint": "サーバーの CPU、メモリ、ディスクなどのステータスを取得する頻度 (最小値は 1)。", "refreshIntervalLabel": "ステータス更新間隔 (秒):", "saveButton": "ステータスモニター設定を保存", "success": {"saved": "ステータスモニター設定を保存しました。"}, "title": "ステータスモニター設定"}, "title": "設定", "twoFactor": {"disable": {"button": "2段階認証を無効にする", "passwordPrompt": "現在のログインパスワードを入力して、無効にすることを確認してください:"}, "enable": {"button": "2段階認証を有効にする"}, "error": {"codeRequired": "コードを入力してください。", "disableFailed": "2段階認証の無効化に失敗しました。", "passwordRequiredForDisable": "無効にするには、現在のパスワードを入力する必要があります。", "setupFailed": "2段階認証の設定情報の取得に失敗しました。", "verificationFailed": "無効なコードまたは期限切れのコードです。"}, "setup": {"enterCode": "アプリで生成された 6 桁のコードを入力してください:", "orEnterSecret": "または、シークレットキーを手動で入力してください:", "scanQrCode": "Authenticator アプリを使用して、以下の QR コードをスキャンしてください:", "verifyButton": "確認して有効にする"}, "status": {"disabled": "2段階認証は現在無効です。", "enabled": "2段階認証は有効です。"}, "success": {"activated": "2段階認証が有効になりました！", "disabled": "2段階認証が無効になりました。"}, "title": "2段階認証 (TOTP)"}, "workspace": {"error": {"sidebarPersistentSaveFailed": "サイドバーの設定の保存に失敗しました。", "showConnectionTagsSaveFailed": "接続タグ表示設定の保存に失敗しました。", "showQuickCommandTagsSaveFailed": "クイックコマンドタグ表示設定の保存に失敗しました。"}, "sidebarPersistentDescription": "有効にすると、サイドバーの外側をクリックしてもサイドバーは自動的に折りたたまれません。", "sidebarPersistentLabel": "ポップアップ後にサイドバーを固定 (自動的に折りたたまない)", "sidebarPersistentTitle": "サイドバーの動作", "success": {"sidebarPersistentSaved": "サイドバーの設定を保存しました。", "showConnectionTagsSaved": "接続タグ表示設定を保存しました。", "showQuickCommandTagsSaved": "クイックコマンドタグ表示設定を保存しました。"}, "title": "ワークスペースとターミナル", "showConnectionTagsTitle": "接続タグを表示", "showConnectionTagsLabel": "接続リストにタグを表示", "showConnectionTagsDescription": "無効にすると、接続リストのタグが非表示になり、検索から除外されます。", "showQuickCommandTagsTitle": "クイックコマンドタグを表示", "showQuickCommandTagsLabel": "クイックコマンドリストにタグを表示", "showQuickCommandTagsDescription": "無効にすると、クイックコマンドリストのタグが非表示になり、検索から除外されます。", "fileManagerDeleteConfirmTitle": "ファイルマネージャー削除確認", "fileManagerShowDeleteConfirmationLabel": "ファイルまたはフォルダーを削除する際に確認ダイアログを表示する", "fileManagerDeleteConfirmSuccess": "ファイルマネージャーの削除確認設定を保存しました。", "fileManagerDeleteConfirmError": "ファイルマネージャーの削除確認設定の保存に失敗しました。", "terminalRightClickPasteTitle": "ターミナル右クリック貼り付け", "terminalEnableRightClickPasteLabel": "ターミナルの右クリック貼り付けを有効にする", "terminalEnableRightClickPasteDescription": "ターミナル領域内でマウスの右ボタンを使用してクリップボードの内容を貼り付けることを許可します。", "terminalRightClickPasteSuccess": "ターミナルの右クリック貼り付け設定が保存されました。", "terminalRightClickPasteError": "ターミナルの右クリック貼り付け設定の保存に失敗しました。"}, "statusMonitorShowIp": {"title": "ステータスモニターでIPアドレスを表示", "enableLabel": "ステータスモニターにIPアドレスを表示する"}, "terminalScrollback": {"title": "ターミナルスクロールバック制限", "limitLabel": "最大行数", "limitHint": "ターミナルが保持する最大出力行数を設定します。0 は無制限を意味します (デフォルト値 5000 を使用)。この設定は、次にターミナルを開いたときに有効になります。", "saveButton": "保存", "success": {"saved": "ターミナルスクロールバック制限の設定が正常に保存されました。"}, "error": {"saveFailed": "ターミナルスクロールバック制限の設定の保存に失敗しました。", "invalidInput": "有効な非負整数を入力してください。"}}, "about": {"version": "バージョン", "checkingUpdate": "更新を確認中...", "latestVersion": "最新バージョンです", "updateAvailable": "新しいバージョン {version} が利用可能です！", "error": {"checkFailed": "更新の確認に失敗しました", "checkFailedShort": "確認失敗", "noReleases": "リリースバージョンが見つかりません", "rateLimit": "GitHub API レート制限。後でもう一度お試しください"}}}, "exportConnections": {"title": "接続データのエクスポート", "decryptKeyInfo": "解凍パスワードは、data/.env ファイル内の ENCRYPTION_KEY です。このファイルを安全に保管してください。", "buttonText": "エクスポートを開始"}, "tabs": {"security": "セキュリティ", "ipControl": "IP制御", "workspace": "ワークスペース", "system": "システム", "dataManagement": "データ管理", "appearance": "外観", "about": "バージョン情報"}, "loading": "読み込み中...", "setup": {"confirmPassword": "パスワードを再入力", "confirmPasswordPlaceholder": "パスワードを再入力して確認", "description": "最初の管理者アカウントを作成します。", "error": {"fieldsRequired": "ユーザー名とパスワードは必須です。", "generic": "設定中にエラーが発生しました。サーバーログを確認してください。", "passwordsDoNotMatch": "入力したパスワードが一致しません。"}, "password": "パスワード", "passwordPlaceholder": "パスワードを入力", "settingUp": "アカウントを作成中...", "submitButton": "アカウントを作成", "success": "アカウントの作成に成功しました！ログインページにリダイレクトしています...", "title": "初期設定", "username": "ユーザー名", "usernamePlaceholder": "ユーザー名を入力"}, "slogan": "星垂平野闊，枢動万端通", "statusMonitor": {"bytesPerSecond": "B/秒", "cpuLabel": "CPU:", "cpuModelLabel": "CPU モデル:", "diskLabel": "ディスク:", "errorPrefix": "エラー:", "gigaBytes": "GB", "gigaBytesPerSecond": "GB/秒", "kiloBytesPerSecond": "KB/秒", "loading": "データを待機中...", "megaBytes": "MB", "megaBytesPerSecond": "MB/秒", "memoryLabel": "メモリ:", "networkLabel": "ネットワーク", "notAvailable": "N/A", "osLabel": "OS:", "swapLabel": "スワップ:", "swapNotAvailable": "スワップは利用できません", "title": "サーバー状態", "cpuUsageTitle": "CPU使用率", "memoryUsageTitleUnit": "メモリ使用状況 ({unit})", "networkSpeedTitleUnit": "ネットワーク速度 ({unit})", "cpuUsageLabel": "CPU使用率 (%)", "memoryUsageLabelUnit": "メモリ使用量 ({unit})", "networkDownloadLabelUnit": "ダウンロード ({unit})", "networkUploadLabelUnit": "アップロード ({unit})", "latestCpuValue": "{value}%", "latestMemoryValue": "{value} {unit}", "latestNetworkValue": "↓ {download} ↑ {upload} {unit}"}, "styleCustomizer": {"activeTheme": "現在のテーマ", "addNewTheme": "新しいテーマを作成", "applyButton": "適用", "previewButton": "プレビュー", "applyThemeTooltip": "このテーマを適用", "backgroundSettings": "背景設定", "cannotDeletePreset": "プリセットテーマは削除できません", "darkMode": "ダークモード", "darkModeApplied": "ダークモードが適用されました", "darkModeApplyFailed": "ダークモードの適用に失敗しました: {message}", "defaultMode": "デフォルトモード", "editAsCopy": "コピーとして編集", "editThemeTitle": "ターミナルテーマの編集", "editorFontSize": "エディターフォントサイズ", "editorFontSizeSaveFailed": "エディターフォントサイズの保存に失敗しました: {message}", "editorFontSizeSaved": "エディターフォントサイズを保存しました。", "editorFontFamily": "エディターフォントファミリー", "editorFontFamilySaved": "エディターフォントファミリーを保存しました。", "editorFontFamilySaveFailed": "エディターフォントファミリーの保存に失敗しました: {message}", "errorFixJsonBeforeSave": "保存する前に JSON フォーマットのエラーを修正してください。", "errorInvalidEditorFontSize": "無効なフォントサイズです。正数を入力してください。", "errorInvalidFontSize": "無効なフォントサイズです。正数を入力してください。", "errorInvalidJsonConfig": "無効な JSON 設定", "errorInvalidJsonObject": "無効な入力です。有効な JSON オブジェクトを入力してください。", "errorThemeNameRequired": "テーマ名を入力してください。", "exportActiveTheme": "現在のテーマをエクスポート", "exportActiveThemeTooltip": "現在アクティブなテーマを JSON ファイルとしてエクスポート", "exportFailed": "テーマのエクスポートに失敗しました: {message}", "importFailed": "テーマのインポートに失敗しました。", "importSuccess": "テーマのインポートに成功しました。", "importTheme": "テーマをインポート", "newThemeDefaultName": "新しいテーマ", "newThemeTitle": "ターミナルテーマの作成", "noBackground": "背景なし", "otherSettings": "その他の設定", "pageBackground": "ページの背景", "pageBgRemoved": "ページの背景を削除しました。", "pageBgUploadSuccess": "ページの背景のアップロードに成功しました。", "removeBgFailed": "背景の削除に失敗しました: {message}", "removePageBg": "ページの背景を削除", "removeTerminalBg": "ターミナルの背景を削除", "resetUiTheme": "UI テーマをリセット", "saveUiTheme": "UI テーマを保存", "searchThemePlaceholder": "テーマ名を検索...", "setActiveThemeFailed": "アクティブなターミナルテーマの設定に失敗しました: {message}", "terminalBackground": "ターミナルの背景", "terminalBgRemoved": "ターミナルの背景を削除しました。", "terminalBgUploadSuccess": "ターミナルの背景のアップロードに成功しました。", "terminalFontDescription": "フォント名を入力し、カンマで区切ります。フォント名にスペースが含まれる場合は、引用符で囲んでください。", "terminalFontFamily": "ターミナルフォント", "terminalFontPlaceholder": "例: \"Fira Code\", Consolas, monospace", "terminalFontSaveFailed": "ターミナルフォントの保存に失敗しました: {message}", "terminalFontSaved": "ターミナルフォントを保存しました。", "terminalFontSize": "ターミナルフォントサイズ", "terminalFontSizeSaveFailed": "ターミナルフォントサイズの保存に失敗しました: {message}", "terminalFontSizeSaved": "ターミナルフォントサイズを保存しました。", "terminalStyles": "ターミナルスタイル", "terminalThemeColorEditorTitle": "ターミナルテーマカラーエディター", "terminalThemeJsonEditorDesc": "JSON を使用してターミナルテーマ設定を直接編集します。ここで変更を加えてテキスト領域からフォーカスを外すと、下のカラーピッカーが同期的に更新されます。", "terminalThemeJsonEditorTitle": "ターミナルテーマ JSON エディター", "terminalThemeSelection": "ターミナルテーマ", "themeCreatedSuccess": "テーマの作成に成功しました。", "themeDeleteFailed": "テーマの削除に失敗しました: {message}", "themeDeletedSuccess": "テーマの削除に成功しました。", "themeModeLabel": "テーマモード:", "themeName": "テーマ名", "themeSaveFailed": "テーマの保存に失敗しました。", "themeUpdatedSuccess": "テーマの更新に成功しました。", "title": "外観のカスタマイズ", "uiDescription": "アプリケーションの UI の色、フォントなどを調整します。", "uiStyles": "UI スタイル", "uiThemeJsonEditorDesc": "JSON を使用して UI テーマ設定を直接編集します。ここで変更を加えてテキスト領域からフォーカスを外すと、上記のカラーピッカーが同期的に更新されます。", "uiThemeJsonEditorTitle": "UI テーマ JSON エディター", "uiThemeReset": "UI テーマをデフォルトにリセットしました。", "uiThemeResetFailed": "UI テーマのリセットに失敗しました: {message}", "uiThemeSaveFailed": "UI テーマの保存に失敗しました: {message}", "uploadFailed": "アップロードに失敗しました: {message}", "uploadPageBg": "ページの背景をアップロード", "uploadTerminalBg": "ターミナルの背景をアップロード", "terminalBgOverlayOpacity": "ターミナル背景オーバーレイの不透明度:", "terminalBgOverlayOpacityDesc": "背景画像上の黒いオーバーレイの不透明度を制御します。0は完全に透明、1は完全に不透明です。", "errorInvalidOpacityValue": "無効な不透明度の値です。0から1の間でなければなりません。", "terminalBgOverlayOpacitySaved": "ターミナル背景オーバーレイの不透明度が保存されました。", "terminalBgOverlayOpacitySaveFailed": "ターミナル背景オーバーレイの不透明度の保存に失敗しました: {message}", "terminalBgDisabled": "ターミナルの背景機能は無効です。", "htmlBackgroundThemes": "HTML背景テーマ", "localThemes": "ローカルテーマ", "remoteThemes": "リモートテーマ", "newLocalPreset": "新しいローカルテーマを作成", "noLocalPresetsFound": "ローカルHTMLテーマが見つかりません。", "errorFetchingPresetContentForEdit": "編集のためにテーマコンテンツの取得に失敗しました: {message}", "remoteHtmlPresetsRepositoryUrl": "リモートHTMLテーマリポジトリURL", "remoteRepoUrlPlaceholder": "例: https://github.com/user/repo/tree/main/themes", "saveUrl": "URLを保存", "loadRemoteThemes": "リフレッシュ", "pleaseSetRemoteUrl": "最初にリモートHTMLテーマリポジトリのURLを設定してください。", "noRemotePresetsFound": "リモートリポジトリにHTMLテーマが見つからないか、URLが無効です。", "editLocalPreset": "ローカルテーマを編集", "presetName": "テーマ名", "presetNamePlaceholder": "例: my-theme.html", "presetContent": "テーマコンテンツ", "customTerminalHTMLPlaceholder": "例: <h1>こんにちは</h1>", "errorToggleTerminalBg": "ターミナルの背景有効状態の更新に失敗しました: {message}", "htmlPresetApplied": "HTMLテーマが適用されました。", "htmlPresetApplyFailed": "HTMLテーマの適用に失敗しました: {message}", "errorPresetContentRequired": "テーマのコンテンツは空にできません。", "localPresetUpdated": "ローカルHTMLテーマが更新されました。", "localPresetUpdateFailed": "ローカルHTMLテーマの更新に失敗しました: {message}", "errorPresetNameAndContentRequired": "テーマ名とコンテンツは空にできません。", "localPresetCreated": "ローカルHTMLテーマが作成されました。", "localPresetCreateFailed": "ローカルHTMLテーマの作成に失敗しました: {message}", "confirmDeletePreset": "HTMLテーマ「{name}」を削除してもよろしいですか？", "localPresetDeleted": "ローカルHTMLテーマが削除されました。", "localPresetDeleteFailed": "ローカルHTMLテーマの削除に失敗しました: {message}", "errorRemoteUrlRequired": "リモートHTMLテーマリポジトリのURLは空にできません。", "remoteUrlSaved": "リモートHTMLテーマリポジトリのURLが保存されました。", "remoteUrlSaveFailed": "リモートHTMLテーマリポジトリのURLの保存に失敗しました: {message}", "errorSetRemoteUrlFirst": "最初にリモートHTMLテーマリポジトリのURLを設定して保存してください。", "remotePresetsLoaded": "リモートHTMLテーマリストが読み込まれました。", "remotePresetsLoadFailed": "リモートHTMLテーマリストの読み込みに失敗しました: {message}", "localPresetApplyFailed": "ローカルHTMLテーマの適用に失敗しました: {message}", "errorMissingDownloadUrl": "リモートテーマのダウンロードURLがありません。", "errorPresetNameRequired": "プリセット名を空にすることはできません。", "remotePresetApplyFailed": "リモートHTMLテーマの適用に失敗しました: {message}", "customHtmlResetSuccess": "カスタム HTML がリセットされました。", "searchLocalThemesPlaceholder": "ローカルテーマを検索...", "searchRemoteThemesPlaceholder": "リモートテーマを検索...", "noMatchingLocalPresetsFound": "一致するローカルテーマが見つかりませんでした", "noMatchingRemotePresetsFound": "一致するリモートテーマが見つかりませんでした", "editAsNewTooltip": "新しいカスタムテーマとして編集", "presetTag": "プリセット", "customTag": "カスタム", "textStrokeSettingsSaved": "文字の縁取り設定が保存されました。", "textStrokeSettingsSaveFailed": "文字の縁取り設定の保存に失敗しました: {message}", "textShadowSettingsSaved": "文字の影設定が保存されました。", "textShadowSettingsSaveFailed": "文字の影設定の保存に失敗しました: {message}", "textStrokeSettings": "文字の縁取り設定", "enableTextStroke": "文字の縁取りを有効にする", "textStrokeWidth": "縁取りの太さ (px)", "textStrokeColor": "縁取りの色", "saveStrokeSettings": "縁取り設定を保存", "textShadowSettings": "文字の影設定", "enableTextShadow": "文字の影を有効にする", "textShadowOffsetX": "影の X オフセット (px)", "textShadowOffsetY": "影の Y オフセット (px)", "textShadowBlur": "影のぼかし半径 (px)", "textShadowColor": "影の色", "saveShadowSettings": "影の設定を保存", "setActiveThemeSuccess": "テーマ {themeName} が正常に適用されました。", "errorEditThemeNoId": "テーマ編集エラー：テーマに ID がありません。", "errorLoadThemeDataFailed": "テーマデータの読み込みに失敗しました。", "errorEditThemeFailed": "テーマの編集に失敗しました。", "errorJsonSyntax": "JSON 構文エラー", "noThemeSelected": "テーマが選択されていません", "unknownTheme": "不明なテーマ", "noThemesFound": "一致するテーマが見つかりません"}, "tags": {"addTag": "新しいタグを追加", "deleteTagGlobally": "このタグをグローバルに削除", "error": "タグリストのロードに失敗しました: {error}", "inputPlaceholder": "検索またはタグを作成...", "loading": "タグをロード中...", "noTags": "タグがありません。'新しいタグを追加'をクリックして作成してください。", "prompts": {"confirmDelete": "\"{name}\"タグを削除しますか？この操作は元に戻せません。"}, "removeSelection": "このタグの選択を解除", "title": "タグ管理", "createSuccess": "タグが正常に作成されました。", "updateSuccess": "タグが正常に更新されました。", "deleteSuccess": "タグ「{name}」が正常に削除されました。", "deleteFailed": "タグ「{name}」の削除に失敗しました: {error}", "errorDelete": "タグの削除中にエラーが発生しました: {error}"}, "terminalTabBar": {"selectServerTitle": "接続するサーバーを選択", "showTransferProgressTooltip": "転送進捗の表示/非表示"}, "workspace": {"terminal": {"reconnectingMsg": "再接続を試行中..."}}, "workspaceConnectionList": {"noResults": "\"{searchTerm}\"に一致する接続は見つかりませんでした。", "searchPlaceholder": "名前またはホストを検索...", "untagged": "タグなし", "allConnectionsTaggedSuccess": "すべての接続にタグが正常に追加されました。", "noConnectionsToTag": "タグ付けする接続はありません。", "clickToEditTag": "クリックしてタグ名を編集", "connectAllInGroup": "グループ内のすべてに接続 (SSH)", "connectingAllInGroup": "グループ '{groupName}' 内のすべてに接続しています...", "noConnectionsInGroup": "グループ '{groupName}' 内に接続可能な項目がありません。", "noConnectionsToConnect": "接続可能な項目がありません", "connectingAllSshInGroup": "グループ '{groupName}' 内の {count} 個の SSH 接続に接続しています...", "noSshConnectionsInGroup": "グループ '{groupName}' 内に接続可能な SSH 接続がありません。", "connectAllSshInGroupMenu": "すべて接続", "noSshConnectionsToConnectMenu": "SSH 接続なし", "manageTags": {"title": "タグ接続の管理", "searchPlaceholder": "接続を検索...", "selectAll": "すべて選択", "deselectAll": "すべて選択解除", "noConnectionsFound": "接続が見つかりません。", "saveSuccess": "タグ接続が正常に更新されました。", "saveFailed": "タグ接続の更新に失敗しました。", "menuItem": "タグを管理", "cannotManageUntagged": "「タグなし」グループの接続は管理できません。", "invertSelection": "選択を反転"}, "deleteAllConnectionsInGroupMenu": "グループ内の全接続を削除", "noConnectionsToDeleteInGroup": "グループ '{groupName}' に削除可能な接続がありません。", "confirmDeleteAllConnectionsInGroup": "グループ '{groupName}' 内の全 {count} 個の接続を削除してもよろしいですか？この操作は元に戻せません。", "allConnectionsInGroupDeletedSuccess": "グループ '{groupName}' から {count} 個の接続を正常に削除しました。", "cannotDeleteFromUntagged": "このオプションを使用して「未分類」グループから接続を削除することはできません。", "someConnectionsInGroupDeleteFailed": "グループ '{groupName}' 内の {count} 個の接続の削除に失敗しました。"}, "sshKeys": {"selector": {"selectPlaceholder": "SSH キーを選択...", "useDirectInput": "またはキーの内容を直接入力", "manageKeysTitle": "SSH キーを管理", "loadingKeys": "キーを読み込み中..."}, "modal": {"title": "SSH キー管理", "addKey": "キーを追加", "keyName": "キー名", "actions": "操作", "loading": "読み込み中...", "noKeys": "SSH キーが見つかりません。追加してください。", "close": "閉じる", "addTitle": "新しい SSH キーを追加", "editTitle": "SSH キーを編集", "privateKey": "秘密鍵の内容", "passphrase": "パスフレーズ", "cancel": "キャンセル", "saveChanges": "変更を保存", "edit": "編集", "delete": "削除", "errorFetchDetails": "キーの詳細の取得に失敗しました", "errorRequiredFields": "キー名と秘密鍵の内容は空にできません。", "confirmDelete": "キー \"{name}\" を削除しますか？この操作は元に戻せません。", "keyUpdateNote": "既存のキーを保持するには、秘密鍵を空のままにしてください。パスフレーズは必要に応じて再入力する必要があります。", "passphraseUpdateNote": "パスフレーズを保持または削除するには空のままにします。更新するには新しいパスフレーズを入力してください。"}}, "time": {"unknown": "不明な時間", "invalidDate": "無効な日付"}, "sshSuspend": {"notifications": {"markedForSuspendInfo": "セッション {id} は中断マークが付けられました。タブを閉じるかアプリケーションを終了すると中断されます。", "wsNotConnectedError": "WebSocket が接続されていません。中断/再開アクションを実行できません。", "sessionNotFoundError": "セッションが見つからないか、WebSocket マネージャーが利用できません。", "notMarkedWarning": "セッションに中断マークは付けられていませんでした。", "fetchListError": "中断されたセッションリストの取得に失敗しました: {error}", "resumeErrorInfoNotFound": "再開エラー: 中断されたセッション {id} がリストに見つかりません。", "resumeErrorConnectionConfigNotFound": "再開エラー: {id} の元の接続設定が見つかりません。", "resumeErrorGeneric": "セッションの再開に失敗しました: {error}", "terminatedSuccess": "中断されたセッション「{name}」は正常に終了しました。", "terminateError": "中断されたセッションの終了に失敗しました: {error}", "entryRemovedSuccess": "中断されたセッションエントリ「{name}」は正常に削除されました。", "entryRemovedError": "中断されたセッションエントリの削除に失敗しました: {error}", "nameEditedSuccess": "中断されたセッション名は「{name}」に更新されました。", "nameEditedError": "中断されたセッション名の編集に失敗しました: {error}", "markedForSuspendSuccess": "セッション {id} は正常に中断マークが付けられました。", "markForSuspendError": "セッションの中断マーク付けに失敗しました: {error}", "unmarkedSuccess": "セッション {id} の中断マークは正常に解除されました。", "unmarkError": "セッションの中断マーク解除に失敗しました: {error}", "defaultSessionName": "セッション", "resumeSuccess": "セッション「{name}」は正常に再開されました。", "resumeErrorBackend": "バックエンドがセッションの再開に失敗しました: {error}", "autoTerminated": "中断されたセッション「{name}」は、理由: {reason} によりバックエンドによって自動終了されました。"}}, "transferProgressModal": {"title": "ファイル転送進捗", "loading": "転送タスクを読み込み中...", "errorLoadingTitle": "読み込みエラー", "errorLoading": "転送タスクの読み込みに失敗しました：{error}", "error": {"unknown": "不明なエラー", "cancelFailed": "タスクの终止に失敗しました。"}, "noTasks": "現在アクティブな転送タスクはありません。", "task": {"idLabel": "タスク", "createdAt": "作成日時", "overallProgress": "全体進捗"}, "status": {"queued": "待機中", "inProgress": "進行中", "completed": "完了", "failed": "失敗", "partiallyCompleted": "一部完了", "connecting": "接続中", "transferring": "転送中", "cancelling": "终止中", "cancelled": "终止済み"}, "confirmCancel": "この転送タスクを终止してもよろしいですか？", "cancelRequested": "终止リクエストを送信しました。", "cancelTaskTooltip": "タスクを终止", "cancellingTooltip": "终止中...", "cancelButton": "终止", "cancellingButton": "终止中", "subTasks": {"titleToggle": "{count} 個のサブタスクを表示", "noSubTasks": "サブタスクはありません。"}, "subTask": {"source": "送信元ファイル", "connectionId": "宛先接続", "status": "状態", "method": "方法", "error": "エラー"}, "connectionIdFallback": "接続ID: {connectionId}", "unknownFileName": "[ファイル名不明]", "unknownSourceServer": "[送信元サーバー不明]", "unknownTargetPath": "[宛先パス不明]", "taskIdFallback": "タスクID: {taskId}"}, "sendFilesModal": {"title": "ファイル送信", "searchConnectionsPlaceholder": "接続を検索...", "targetPathLabel": "ターゲットパス", "targetPathPlaceholder": "目的のパス", "transferMethodLabel": "転送方法", "transferMethodAuto": "自動", "loadingConnections": "接続を読み込み中...", "noConnections": "利用可能な接続がありません。まず接続を追加してください。", "noConnectionsFound": "検索に一致する接続が見つかりませんでした。", "untaggedConnections": "タグなし", "itemsToSendTitle": "送信するアイテム:", "noItemsSelected": "送信するアイテムが選択されていません。", "sendButton": "送信", "cancelButton": "キャンセル", "errorFetchingData": "モーダルデータの取得中にエラーが発生しました。", "errorFetchingConnections": "接続データの取得中にエラーが発生しました。", "errorFetchingTags": "タグデータの取得中にエラーが発生しました。", "validationError": "少なくとも1つの接続を選択し、ターゲットパスを指定してください。", "transferInitiated": "転送タスクが作成されました", "transferInitiatedGeneric": "転送タスクが正常に作成されました。", "transferFailedError": "転送の開始に失敗しました。もう一度お試しください。"}, "favoritePaths": {"addEditForm": {"validation": {"pathRequired": "Path is required."}, "editTitle": "Edit Favorite Path", "addTitle": "Add New Favorite Path", "pathLabel": "Path", "pathPlaceholder": "/example/folder/path", "nameLabel": "Name (Optional)", "namePlaceholder": "My Documents", "errors": {"genericSaveError": "Failed to save favorite path."}}, "confirmDelete": "Are you sure you want to delete \"{name}\"?", "searchPlaceholder": "Search by name or path...", "addNew": "Add new favorite path", "loading": "Loading favorites...", "noResults": "No matching favorites found.", "noFavorites": "No favorite paths yet. Add one!", "notifications": {"fetchError": "Failed to load favorite paths.", "addSuccess": "Favorite path added successfully.", "addError": "Failed to add favorite path.", "updateSuccess": "Favorite path updated successfully.", "updateError": "Failed to update favorite path.", "deleteSuccess": "Favorite path deleted successfully.", "deleteError": "Failed to delete favorite path."}}, "pathHistory": {"loading": "読み込み中...", "empty": "パス履歴がありません", "copy": "パスをコピー", "delete": "この履歴を削除", "copiedSuccess": "パスがクリップボードにコピーされました", "copiedError": "パスのコピーに失敗しました"}}