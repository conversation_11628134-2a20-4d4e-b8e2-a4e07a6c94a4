<style>
  #static-geometric-pattern {
    width: 100%;
    height: 100%;
    background-color: #2c3340; /* 深灰蓝 */



    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='60' height='69.28' viewBox='0 0 60 69.28'%3E%3Cdefs%3E%3Cstyle%3E.hex { stroke: rgba(180,190,210,0.07); stroke-width: 0.5px; fill: none; }%3C/style%3E%3C/defs%3E%3Cpath class='hex' d='M30 0 L60 17.32 L60 51.96 L30 69.28 L0 51.96 L0 17.32 Z M0 17.32 L30 34.64 M30 0 L30 34.64 M60 17.32 L30 34.64 M0 51.96 L30 34.64 M30 69.28 L30 34.64 M60 51.96 L30 34.64'/%3E%3C/svg%3E");
    background-size: 60px 69.28px; 
    background-repeat: repeat;

  }
  #static-geometric-pattern::after { 
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: radial-gradient(ellipse at center, rgba(255,255,255,0.02) 0%, transparent 70%);
    opacity: 0.5;
    pointer-events: none;
  }
</style>
<div id="static-geometric-pattern">

</div>