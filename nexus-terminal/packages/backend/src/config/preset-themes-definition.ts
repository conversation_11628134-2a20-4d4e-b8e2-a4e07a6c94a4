
import type { TerminalTheme } from '../types/terminal-theme.types';


type PresetThemeDefinition = Omit<TerminalTheme, '_id' | 'createdAt' | 'updatedAt'> & { preset_key: string };

export const presetTerminalThemes: PresetThemeDefinition[] = [
  {
    preset_key: '0x96f',
    name: '0x96f',
    isPreset: true,
    themeData: {
      foreground: '#fcfcfa',
      background: '#262427',
      cursor: '#fcfcfa',
      cursorAccent: '#000000',
      selectionBackground: '#fcfcfa',
      selectionForeground: '#262427',
      black: '#262427',
      red: '#ff7272',
      green: '#bcdf59',
      yellow: '#ffca58',
      blue: '#49cae4',
      magenta: '#a093e2',
      cyan: '#aee8f4',
      white: '#fcfcfa',
      brightBlack: '#545452',
      brightRed: '#ff8787',
      brightGreen: '#c6e472',
      brightYellow: '#ffd271',
      brightBlue: '#64d2e8',
      brightMagenta: '#aea3e6',
      brightCyan: '#baebf6',
      brightWhite: '#fcfcfa',
    },
  },
  {
    preset_key: '3024_Day',
    name: '3024 Day',
    isPreset: true,
    themeData: {
      foreground: '#4a4543',
      background: '#f7f7f7',
      cursor: '#4a4543',
      cursorAccent: '#f7f7f7',
      selectionBackground: '#a5a2a2',
      selectionForeground: '#4a4543',
      black: '#090300',
      red: '#db2d20',
      green: '#01a252',
      yellow: '#fded02',
      blue: '#01a0e4',
      magenta: '#a16a94',
      cyan: '#b5e4f4',
      white: '#a5a2a2',
      brightBlack: '#5c5855',
      brightRed: '#e8bbd0',
      brightGreen: '#3a3432',
      brightYellow: '#4a4543',
      brightBlue: '#807d7c',
      brightMagenta: '#d6d5d4',
      brightCyan: '#cdab53',
      brightWhite: '#f7f7f7',
    },
  },
  {
    preset_key: '3024_Night',
    name: '3024 Night',
    isPreset: true,
    themeData: {
      foreground: '#a5a2a2',
      background: '#090300',
      cursor: '#a5a2a2',
      cursorAccent: '#090300',
      selectionBackground: '#4a4543',
      selectionForeground: '#a5a2a2',
      black: '#090300',
      red: '#db2d20',
      green: '#01a252',
      yellow: '#fded02',
      blue: '#01a0e4',
      magenta: '#a16a94',
      cyan: '#b5e4f4',
      white: '#a5a2a2',
      brightBlack: '#5c5855',
      brightRed: '#e8bbd0',
      brightGreen: '#3a3432',
      brightYellow: '#4a4543',
      brightBlue: '#807d7c',
      brightMagenta: '#d6d5d4',
      brightCyan: '#cdab53',
      brightWhite: '#f7f7f7',
    },
  },
  {
    preset_key: 'Aardvark_Blue',
    name: 'Aardvark Blue',
    isPreset: true,
    themeData: {
      foreground: '#dddddd',
      background: '#102040',
      cursor: '#007acc',
      cursorAccent: '#bfdbfe',
      selectionBackground: '#bfdbfe',
      selectionForeground: '#000000',
      black: '#191919',
      red: '#aa342e',
      green: '#4b8c0f',
      yellow: '#dbba00',
      blue: '#1370d3',
      magenta: '#c43ac3',
      cyan: '#008eb0',
      white: '#bebebe',
      brightBlack: '#454545',
      brightRed: '#f05b50',
      brightGreen: '#95dc55',
      brightYellow: '#ffe763',
      brightBlue: '#60a4ec',
      brightMagenta: '#e26be2',
      brightCyan: '#60b6cb',
      brightWhite: '#f7f7f7',
    },
  },
  {
    preset_key: 'Abernathy',
    name: 'Abernathy',
    isPreset: true,
    themeData: {
      foreground: '#eeeeec',
      background: '#111416',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#eeeeec',
      selectionForeground: '#333333',
      black: '#000000',
      red: '#cd0000',
      green: '#00cd00',
      yellow: '#cdcd00',
      blue: '#1093f5',
      magenta: '#cd00cd',
      cyan: '#00cdcd',
      white: '#faebd7',
      brightBlack: '#404040',
      brightRed: '#ff0000',
      brightGreen: '#00ff00',
      brightYellow: '#ffff00',
      brightBlue: '#11b5f6',
      brightMagenta: '#ff00ff',
      brightCyan: '#00ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Adventure',
    name: 'Adventure',
    isPreset: true,
    themeData: {
      foreground: '#feffff',
      background: '#040404',
      cursor: '#feffff',
      cursorAccent: '#000000',
      selectionBackground: '#606060',
      selectionForeground: '#ffffff',
      black: '#040404',
      red: '#d84a33',
      green: '#5da602',
      yellow: '#eebb6e',
      blue: '#417ab3',
      magenta: '#e5c499',
      cyan: '#bdcfe5',
      white: '#dbded8',
      brightBlack: '#685656',
      brightRed: '#d76b42',
      brightGreen: '#99b52c',
      brightYellow: '#ffb670',
      brightBlue: '#97d7ef',
      brightMagenta: '#aa7900',
      brightCyan: '#bdcfe5',
      brightWhite: '#e4d5c7',
    },
  },
  {
    preset_key: 'AdventureTime',
    name: 'AdventureTime',
    isPreset: true,
    themeData: {
      foreground: '#f8dcc0',
      background: '#1f1d45',
      cursor: '#efbf38',
      cursorAccent: '#08080a',
      selectionBackground: '#706b4e',
      selectionForeground: '#f3d9c4',
      black: '#050404',
      red: '#bd0013',
      green: '#4ab118',
      yellow: '#e7741e',
      blue: '#0f4ac6',
      magenta: '#665993',
      cyan: '#70a598',
      white: '#f8dcc0',
      brightBlack: '#4e7cbf',
      brightRed: '#fc5f5a',
      brightGreen: '#9eff6e',
      brightYellow: '#efc11a',
      brightBlue: '#1997c6',
      brightMagenta: '#9b5953',
      brightCyan: '#c8faf4',
      brightWhite: '#f6f5fb',
    },
  },
  {
    preset_key: 'Adwaita',
    name: 'Adwaita',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#000000',
      cursorAccent: '#ffffff',
      selectionBackground: '#c0bfbc',
      selectionForeground: '#000000',
      black: '#241f31',
      red: '#c01c28',
      green: '#2ec27e',
      yellow: '#f5c211',
      blue: '#1e78e4',
      magenta: '#9841bb',
      cyan: '#0ab9dc',
      white: '#c0bfbc',
      brightBlack: '#5e5c64',
      brightRed: '#ed333b',
      brightGreen: '#57e389',
      brightYellow: '#f8e45c',
      brightBlue: '#51a1ff',
      brightMagenta: '#c061cb',
      brightCyan: '#4fd2fd',
      brightWhite: '#f6f5f4',
    },
  },
  {
    preset_key: 'Adwaita_Dark',
    name: 'Adwaita Dark',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#1e1e1e',
      cursor: '#ffffff',
      cursorAccent: '#1e1e1e',
      selectionBackground: '#ffffff',
      selectionForeground: '#5e5c64',
      black: '#241f31',
      red: '#c01c28',
      green: '#2ec27e',
      yellow: '#f5c211',
      blue: '#1e78e4',
      magenta: '#9841bb',
      cyan: '#0ab9dc',
      white: '#c0bfbc',
      brightBlack: '#5e5c64',
      brightRed: '#ed333b',
      brightGreen: '#57e389',
      brightYellow: '#f8e45c',
      brightBlue: '#51a1ff',
      brightMagenta: '#c061cb',
      brightCyan: '#4fd2fd',
      brightWhite: '#f6f5f4',
    },
  },
  {
    preset_key: 'Afterglow',
    name: 'Afterglow',
    isPreset: true,
    themeData: {
      foreground: '#d0d0d0',
      background: '#212121',
      cursor: '#d0d0d0',
      cursorAccent: '#151515',
      selectionBackground: '#303030',
      selectionForeground: '#d0d0d0',
      black: '#151515',
      red: '#ac4142',
      green: '#7e8e50',
      yellow: '#e5b567',
      blue: '#6c99bb',
      magenta: '#9f4e85',
      cyan: '#7dd6cf',
      white: '#d0d0d0',
      brightBlack: '#505050',
      brightRed: '#ac4142',
      brightGreen: '#7e8e50',
      brightYellow: '#e5b567',
      brightBlue: '#6c99bb',
      brightMagenta: '#9f4e85',
      brightCyan: '#7dd6cf',
      brightWhite: '#f5f5f5',
    },
  },
  {
    preset_key: 'Alabaster',
    name: 'Alabaster',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#f7f7f7',
      cursor: '#007acc',
      cursorAccent: '#bfdbfe',
      selectionBackground: '#bfdbfe',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#aa3731',
      green: '#448c27',
      yellow: '#cb9000',
      blue: '#325cc0',
      magenta: '#7a3e9d',
      cyan: '#0083b2',
      white: '#f7f7f7',
      brightBlack: '#777777',
      brightRed: '#f05050',
      brightGreen: '#60cb00',
      brightYellow: '#ffbc5d',
      brightBlue: '#007acc',
      brightMagenta: '#e64ce6',
      brightCyan: '#00aacb',
      brightWhite: '#f7f7f7',
    },
  },
  {
    preset_key: 'AlienBlood',
    name: 'AlienBlood',
    isPreset: true,
    themeData: {
      foreground: '#637d75',
      background: '#0f1610',
      cursor: '#73fa91',
      cursorAccent: '#0f1610',
      selectionBackground: '#1d4125',
      selectionForeground: '#73fa91',
      black: '#112616',
      red: '#7f2b27',
      green: '#2f7e25',
      yellow: '#717f24',
      blue: '#2f6a7f',
      magenta: '#47587f',
      cyan: '#327f77',
      white: '#647d75',
      brightBlack: '#3c4812',
      brightRed: '#e08009',
      brightGreen: '#18e000',
      brightYellow: '#bde000',
      brightBlue: '#00aae0',
      brightMagenta: '#0058e0',
      brightCyan: '#00e0c4',
      brightWhite: '#73fa91',
    },
  },
  {
    preset_key: 'Andromeda',
    name: 'Andromeda',
    isPreset: true,
    themeData: {
      foreground: '#e5e5e5',
      background: '#262a33',
      cursor: '#f8f8f0',
      cursorAccent: '#cfcfc2',
      selectionBackground: '#5a5c62',
      selectionForeground: '#ece7e7',
      black: '#000000',
      red: '#cd3131',
      green: '#05bc79',
      yellow: '#e5e512',
      blue: '#2472c8',
      magenta: '#bc3fbc',
      cyan: '#0fa8cd',
      white: '#e5e5e5',
      brightBlack: '#666666',
      brightRed: '#cd3131',
      brightGreen: '#05bc79',
      brightYellow: '#e5e512',
      brightBlue: '#2472c8',
      brightMagenta: '#bc3fbc',
      brightCyan: '#0fa8cd',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'Apple_Classic',
    name: 'Apple Classic',
    isPreset: true,
    themeData: {
      foreground: '#d5a200',
      background: '#2c2b2b',
      cursor: '#c7c7c7',
      cursorAccent: '#ffffff',
      selectionBackground: '#6b5b02',
      selectionForeground: '#67e000',
      black: '#000000',
      red: '#c91b00',
      green: '#00c200',
      yellow: '#c7c400',
      blue: '#0225c7',
      magenta: '#ca30c7',
      cyan: '#00c5c7',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#ff6e67',
      brightGreen: '#5ffa68',
      brightYellow: '#fffc67',
      brightBlue: '#6871ff',
      brightMagenta: '#ff77ff',
      brightCyan: '#60fdff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Apple_System_Colors',
    name: 'Apple System Colors',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#1e1e1e',
      cursor: '#98989d',
      cursorAccent: '#ffffff',
      selectionBackground: '#3f638b',
      selectionForeground: '#ffffff',
      black: '#1a1a1a',
      red: '#cc372e',
      green: '#26a439',
      yellow: '#cdac08',
      blue: '#0869cb',
      magenta: '#9647bf',
      cyan: '#479ec2',
      white: '#98989d',
      brightBlack: '#464646',
      brightRed: '#ff453a',
      brightGreen: '#32d74b',
      brightYellow: '#ffd60a',
      brightBlue: '#0a84ff',
      brightMagenta: '#bf5af2',
      brightCyan: '#76d6ff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Apple_System_Colors_Light',
    name: 'Apple System Colors Light',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#feffff',
      cursor: '#98989d',
      cursorAccent: '#ffffff',
      selectionBackground: '#b4d7ff',
      selectionForeground: '#000000',
      black: '#1a1a1a',
      red: '#bc4437',
      green: '#51a148',
      yellow: '#c7ad3a',
      blue: '#2e68c5',
      magenta: '#8c4bb8',
      cyan: '#5e9cbe',
      white: '#98989d',
      brightBlack: '#464646',
      brightRed: '#eb5545',
      brightGreen: '#6bd45f',
      brightYellow: '#f8d84a',
      brightBlue: '#3b82f7',
      brightMagenta: '#b260ea',
      brightCyan: '#8dd3fb',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'arcoiris',
    name: 'arcoiris',
    isPreset: true,
    themeData: {
      foreground: '#eee4d9',
      background: '#201f1e',
      cursor: '#7a1c1c',
      cursorAccent: '#fffbf2',
      selectionBackground: '#25524a',
      selectionForeground: '#f3fffd',
      black: '#333333',
      red: '#da2700',
      green: '#12c258',
      yellow: '#ffc656',
      blue: '#518bfc',
      magenta: '#e37bd9',
      cyan: '#63fad5',
      white: '#bab2b2',
      brightBlack: '#777777',
      brightRed: '#ffb9b9',
      brightGreen: '#e3f6aa',
      brightYellow: '#ffddaa',
      brightBlue: '#b3e8f3',
      brightMagenta: '#cbbaf9',
      brightCyan: '#bcffc7',
      brightWhite: '#efefef',
    },
  },
  {
    preset_key: 'Ardoise',
    name: 'Ardoise',
    isPreset: true,
    themeData: {
      foreground: '#eaeaea',
      background: '#1e1e1e',
      cursor: '#f7f7f7',
      cursorAccent: '#000000',
      selectionBackground: '#46515e',
      selectionForeground: '#f1f3f5',
      black: '#2c2c2c',
      red: '#d3322d',
      green: '#588b35',
      yellow: '#fca93a',
      blue: '#2465c2',
      magenta: '#7332b4',
      cyan: '#64e1b8',
      white: '#f7f7f7',
      brightBlack: '#535353',
      brightRed: '#fa5852',
      brightGreen: '#8dc252',
      brightYellow: '#ffea51',
      brightBlue: '#6ab5f8',
      brightMagenta: '#be68ca',
      brightCyan: '#89ffdb',
      brightWhite: '#fefefe',
    },
  },
  {
    preset_key: 'Argonaut',
    name: 'Argonaut',
    isPreset: true,
    themeData: {
      foreground: '#fffaf4',
      background: '#0e1019',
      cursor: '#ff0018',
      cursorAccent: '#ff0018',
      selectionBackground: '#002a3b',
      selectionForeground: '#ffffff',
      black: '#232323',
      red: '#ff000f',
      green: '#8ce10b',
      yellow: '#ffb900',
      blue: '#008df8',
      magenta: '#6d43a6',
      cyan: '#00d8eb',
      white: '#ffffff',
      brightBlack: '#444444',
      brightRed: '#ff2740',
      brightGreen: '#abe15b',
      brightYellow: '#ffd242',
      brightBlue: '#0092ff',
      brightMagenta: '#9a5feb',
      brightCyan: '#67fff0',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Arthur',
    name: 'Arthur',
    isPreset: true,
    themeData: {
      foreground: '#ddeedd',
      background: '#1c1c1c',
      cursor: '#e2bbef',
      cursorAccent: '#000000',
      selectionBackground: '#4d4d4d',
      selectionForeground: '#ffffff',
      black: '#3d352a',
      red: '#cd5c5c',
      green: '#86af80',
      yellow: '#e8ae5b',
      blue: '#6495ed',
      magenta: '#deb887',
      cyan: '#b0c4de',
      white: '#bbaa99',
      brightBlack: '#554444',
      brightRed: '#cc5533',
      brightGreen: '#88aa22',
      brightYellow: '#ffa75d',
      brightBlue: '#87ceeb',
      brightMagenta: '#996600',
      brightCyan: '#b0c4de',
      brightWhite: '#ddccbb',
    },
  },
  {
    preset_key: 'AtelierSulphurpool',
    name: 'AtelierSulphurpool',
    isPreset: true,
    themeData: {
      foreground: '#979db4',
      background: '#202746',
      cursor: '#979db4',
      cursorAccent: '#202746',
      selectionBackground: '#5e6687',
      selectionForeground: '#979db4',
      black: '#202746',
      red: '#c94922',
      green: '#ac9739',
      yellow: '#c08b30',
      blue: '#3d8fd1',
      magenta: '#6679cc',
      cyan: '#22a2c9',
      white: '#979db4',
      brightBlack: '#6b7394',
      brightRed: '#c76b29',
      brightGreen: '#293256',
      brightYellow: '#5e6687',
      brightBlue: '#898ea4',
      brightMagenta: '#dfe2f1',
      brightCyan: '#9c637a',
      brightWhite: '#f5f7ff',
    },
  },
  {
    preset_key: 'Atom',
    name: 'Atom',
    isPreset: true,
    themeData: {
      foreground: '#c5c8c6',
      background: '#161719',
      cursor: '#d0d0d0',
      cursorAccent: '#151515',
      selectionBackground: '#444444',
      selectionForeground: '#c5c8c6',
      black: '#000000',
      red: '#fd5ff1',
      green: '#87c38a',
      yellow: '#ffd7b1',
      blue: '#85befd',
      magenta: '#b9b6fc',
      cyan: '#85befd',
      white: '#e0e0e0',
      brightBlack: '#000000',
      brightRed: '#fd5ff1',
      brightGreen: '#94fa36',
      brightYellow: '#f5ffa8',
      brightBlue: '#96cbfe',
      brightMagenta: '#b9b6fc',
      brightCyan: '#85befd',
      brightWhite: '#e0e0e0',
    },
  },
  {
    preset_key: 'AtomOneDark',
    name: 'AtomOneDark',
    isPreset: true,
    themeData: {
      foreground: '#abb2bf',
      background: '#21252b',
      cursor: '#abb2bf',
      cursorAccent: '#abb2bf',
      selectionBackground: '#323844',
      selectionForeground: '#abb2bf',
      black: '#21252b',
      red: '#e06c75',
      green: '#98c379',
      yellow: '#e5c07b',
      blue: '#61afef',
      magenta: '#c678dd',
      cyan: '#56b6c2',
      white: '#abb2bf',
      brightBlack: '#767676',
      brightRed: '#e06c75',
      brightGreen: '#98c379',
      brightYellow: '#e5c07b',
      brightBlue: '#61afef',
      brightMagenta: '#c678dd',
      brightCyan: '#56b6c2',
      brightWhite: '#abb2bf',
    },
  },
  {
    preset_key: 'AtomOneLight',
    name: 'AtomOneLight',
    isPreset: true,
    themeData: {
      foreground: '#2a2c33',
      background: '#f9f9f9',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#ededed',
      selectionForeground: '#2a2c33',
      black: '#000000',
      red: '#de3e35',
      green: '#3f953a',
      yellow: '#d2b67c',
      blue: '#2f5af3',
      magenta: '#950095',
      cyan: '#3f953a',
      white: '#bbbbbb',
      brightBlack: '#000000',
      brightRed: '#de3e35',
      brightGreen: '#3f953a',
      brightYellow: '#d2b67c',
      brightBlue: '#2f5af3',
      brightMagenta: '#a00095',
      brightCyan: '#3f953a',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Aura',
    name: 'Aura',
    isPreset: true,
    themeData: {
      foreground: '#edecee',
      background: '#15141b',
      cursor: '#a277ff',
      cursorAccent: '#edecee',
      selectionBackground: '#a277ff',
      selectionForeground: '#edecee',
      black: '#110f18',
      red: '#ff6767',
      green: '#61ffca',
      yellow: '#ffca85',
      blue: '#a277ff',
      magenta: '#a277ff',
      cyan: '#61ffca',
      white: '#edecee',
      brightBlack: '#4d4d4d',
      brightRed: '#ffca85',
      brightGreen: '#a277ff',
      brightYellow: '#ffca85',
      brightBlue: '#a277ff',
      brightMagenta: '#a277ff',
      brightCyan: '#61ffca',
      brightWhite: '#edecee',
    },
  },
  {
    preset_key: 'Aurora',
    name: 'Aurora',
    isPreset: true,
    themeData: {
      foreground: '#ffca28',
      background: '#23262e',
      cursor: '#ee5d43',
      cursorAccent: '#ffd29c',
      selectionBackground: '#292e38',
      selectionForeground: '#00e8c6',
      black: '#23262e',
      red: '#f0266f',
      green: '#8fd46d',
      yellow: '#ffe66d',
      blue: '#0321d7',
      magenta: '#ee5d43',
      cyan: '#03d6b8',
      white: '#c74ded',
      brightBlack: '#292e38',
      brightRed: '#f92672',
      brightGreen: '#8fd46d',
      brightYellow: '#ffe66d',
      brightBlue: '#03d6b8',
      brightMagenta: '#ee5d43',
      brightCyan: '#03d6b8',
      brightWhite: '#c74ded',
    },
  },
  {
    preset_key: 'ayu',
    name: 'ayu',
    isPreset: true,
    themeData: {
      foreground: '#e6e1cf',
      background: '#0f1419',
      cursor: '#f29718',
      cursorAccent: '#e6e1cf',
      selectionBackground: '#253340',
      selectionForeground: '#e6e1cf',
      black: '#000000',
      red: '#ff3333',
      green: '#b8cc52',
      yellow: '#e7c547',
      blue: '#36a3d9',
      magenta: '#f07178',
      cyan: '#95e6cb',
      white: '#ffffff',
      brightBlack: '#323232',
      brightRed: '#ff6565',
      brightGreen: '#eafe84',
      brightYellow: '#fff779',
      brightBlue: '#68d5ff',
      brightMagenta: '#ffa3aa',
      brightCyan: '#c7fffd',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Ayu_Mirage',
    name: 'Ayu Mirage',
    isPreset: true,
    themeData: {
      foreground: '#cbccc6',
      background: '#1f2430',
      cursor: '#ffcc66',
      cursorAccent: '#1f2430',
      selectionBackground: '#33415e',
      selectionForeground: '#cbccc6',
      black: '#191e2a',
      red: '#ed8274',
      green: '#a6cc70',
      yellow: '#fad07b',
      blue: '#6dcbfa',
      magenta: '#cfbafa',
      cyan: '#90e1c6',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#f28779',
      brightGreen: '#bae67e',
      brightYellow: '#ffd580',
      brightBlue: '#73d0ff',
      brightMagenta: '#d4bfff',
      brightCyan: '#95e6cb',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'ayu_light',
    name: 'ayu_light',
    isPreset: true,
    themeData: {
      foreground: '#5c6773',
      background: '#fafafa',
      cursor: '#ff6a00',
      cursorAccent: '#5c6773',
      selectionBackground: '#f0eee4',
      selectionForeground: '#5c6773',
      black: '#000000',
      red: '#ff3333',
      green: '#86b300',
      yellow: '#f29718',
      blue: '#41a6d9',
      magenta: '#f07178',
      cyan: '#4dbf99',
      white: '#ffffff',
      brightBlack: '#323232',
      brightRed: '#ff6565',
      brightGreen: '#b8e532',
      brightYellow: '#ffc94a',
      brightBlue: '#73d8ff',
      brightMagenta: '#ffa3aa',
      brightCyan: '#7ff1cb',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Banana_Blueberry',
    name: 'Banana Blueberry',
    isPreset: true,
    themeData: {
      foreground: '#cccccc',
      background: '#191323',
      cursor: '#e07d13',
      cursorAccent: '#ffffff',
      selectionBackground: '#220525',
      selectionForeground: '#f4f4f4',
      black: '#17141f',
      red: '#ff6b7f',
      green: '#00bd9c',
      yellow: '#e6c62f',
      blue: '#22e8df',
      magenta: '#dc396a',
      cyan: '#56b6c2',
      white: '#f1f1f1',
      brightBlack: '#495162',
      brightRed: '#fe9ea1',
      brightGreen: '#98c379',
      brightYellow: '#f9e46b',
      brightBlue: '#91fff4',
      brightMagenta: '#da70d6',
      brightCyan: '#bcf3ff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Batman',
    name: 'Batman',
    isPreset: true,
    themeData: {
      foreground: '#6f6f6f',
      background: '#1b1d1e',
      cursor: '#fcef0c',
      cursorAccent: '#000000',
      selectionBackground: '#4d504c',
      selectionForeground: '#f0e04a',
      black: '#1b1d1e',
      red: '#e6dc44',
      green: '#c8be46',
      yellow: '#f4fd22',
      blue: '#737174',
      magenta: '#747271',
      cyan: '#62605f',
      white: '#c6c5bf',
      brightBlack: '#505354',
      brightRed: '#fff78e',
      brightGreen: '#fff27d',
      brightYellow: '#feed6c',
      brightBlue: '#919495',
      brightMagenta: '#9a9a9d',
      brightCyan: '#a3a3a6',
      brightWhite: '#dadbd6',
    },
  },
  {
    preset_key: 'Belafonte_Day',
    name: 'Belafonte Day',
    isPreset: true,
    themeData: {
      foreground: '#45373c',
      background: '#d5ccba',
      cursor: '#45373c',
      cursorAccent: '#d5ccba',
      selectionBackground: '#968c83',
      selectionForeground: '#45373c',
      black: '#20111b',
      red: '#be100e',
      green: '#858162',
      yellow: '#eaa549',
      blue: '#426a79',
      magenta: '#97522c',
      cyan: '#989a9c',
      white: '#968c83',
      brightBlack: '#5e5252',
      brightRed: '#be100e',
      brightGreen: '#858162',
      brightYellow: '#eaa549',
      brightBlue: '#426a79',
      brightMagenta: '#97522c',
      brightCyan: '#989a9c',
      brightWhite: '#d5ccba',
    },
  },
  {
    preset_key: 'Belafonte_Night',
    name: 'Belafonte Night',
    isPreset: true,
    themeData: {
      foreground: '#968c83',
      background: '#20111b',
      cursor: '#968c83',
      cursorAccent: '#20111b',
      selectionBackground: '#45373c',
      selectionForeground: '#968c83',
      black: '#20111b',
      red: '#be100e',
      green: '#858162',
      yellow: '#eaa549',
      blue: '#426a79',
      magenta: '#97522c',
      cyan: '#989a9c',
      white: '#968c83',
      brightBlack: '#5e5252',
      brightRed: '#be100e',
      brightGreen: '#858162',
      brightYellow: '#eaa549',
      brightBlue: '#426a79',
      brightMagenta: '#97522c',
      brightCyan: '#989a9c',
      brightWhite: '#d5ccba',
    },
  },
  {
    preset_key: 'BirdsOfParadise',
    name: 'BirdsOfParadise',
    isPreset: true,
    themeData: {
      foreground: '#e0dbb7',
      background: '#2a1f1d',
      cursor: '#573d26',
      cursorAccent: '#573d26',
      selectionBackground: '#563c27',
      selectionForeground: '#e0dbbb',
      black: '#573d26',
      red: '#be2d26',
      green: '#6ba18a',
      yellow: '#e99d2a',
      blue: '#5a86ad',
      magenta: '#ac80a6',
      cyan: '#74a6ad',
      white: '#e0dbb7',
      brightBlack: '#9b6c4a',
      brightRed: '#e84627',
      brightGreen: '#95d8ba',
      brightYellow: '#d0d150',
      brightBlue: '#b8d3ed',
      brightMagenta: '#d19ecb',
      brightCyan: '#93cfd7',
      brightWhite: '#fff9d5',
    },
  },
  {
    preset_key: 'Blazer',
    name: 'Blazer',
    isPreset: true,
    themeData: {
      foreground: '#d9e6f2',
      background: '#0d1926',
      cursor: '#d9e6f2',
      cursorAccent: '#0d1926',
      selectionBackground: '#c1ddff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#b87a7a',
      green: '#7ab87a',
      yellow: '#b8b87a',
      blue: '#7a7ab8',
      magenta: '#b87ab8',
      cyan: '#7ab8b8',
      white: '#d9d9d9',
      brightBlack: '#262626',
      brightRed: '#dbbdbd',
      brightGreen: '#bddbbd',
      brightYellow: '#dbdbbd',
      brightBlue: '#bdbddb',
      brightMagenta: '#dbbddb',
      brightCyan: '#bddbdb',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Blue_Matrix',
    name: 'Blue Matrix',
    isPreset: true,
    themeData: {
      foreground: '#00a2ff',
      background: '#101116',
      cursor: '#76ff9f',
      cursorAccent: '#ffffff',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#101116',
      red: '#ff5680',
      green: '#00ff9c',
      yellow: '#fffc58',
      blue: '#00b0ff',
      magenta: '#d57bff',
      cyan: '#76c1ff',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#ff6e67',
      brightGreen: '#5ffa68',
      brightYellow: '#fffc67',
      brightBlue: '#6871ff',
      brightMagenta: '#d682ec',
      brightCyan: '#60fdff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'BlueBerryPie',
    name: 'BlueBerryPie',
    isPreset: true,
    themeData: {
      foreground: '#babab9',
      background: '#1c0c28',
      cursor: '#fcfad6',
      cursorAccent: '#000000',
      selectionBackground: '#606060',
      selectionForeground: '#ffffff',
      black: '#0a4c62',
      red: '#99246e',
      green: '#5cb1b3',
      yellow: '#eab9a8',
      blue: '#90a5bd',
      magenta: '#9d54a7',
      cyan: '#7e83cc',
      white: '#f0e8d6',
      brightBlack: '#201637',
      brightRed: '#c87272',
      brightGreen: '#0a6c7e',
      brightYellow: '#7a3188',
      brightBlue: '#39173d',
      brightMagenta: '#bc94b7',
      brightCyan: '#5e6071',
      brightWhite: '#0a6c7e',
    },
  },
  {
    preset_key: 'BlueDolphin',
    name: 'BlueDolphin',
    isPreset: true,
    themeData: {
      foreground: '#c5f2ff',
      background: '#006984',
      cursor: '#ffcc00',
      cursorAccent: '#292d3e',
      selectionBackground: '#2baeca',
      selectionForeground: '#eceff1',
      black: '#292d3e',
      red: '#ff8288',
      green: '#b4e88d',
      yellow: '#f4d69f',
      blue: '#82aaff',
      magenta: '#e9c1ff',
      cyan: '#89ebff',
      white: '#d0d0d0',
      brightBlack: '#434758',
      brightRed: '#ff8b92',
      brightGreen: '#ddffa7',
      brightYellow: '#ffe585',
      brightBlue: '#9cc4ff',
      brightMagenta: '#ddb0f6',
      brightCyan: '#a3f7ff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'BlulocoDark',
    name: 'BlulocoDark',
    isPreset: true,
    themeData: {
      foreground: '#b9c0cb',
      background: '#282c34',
      cursor: '#ffcc00',
      cursorAccent: '#282c34',
      selectionBackground: '#b9c0ca',
      selectionForeground: '#272b33',
      black: '#41444d',
      red: '#fc2f52',
      green: '#25a45c',
      yellow: '#ff936a',
      blue: '#3476ff',
      magenta: '#7a82da',
      cyan: '#4483aa',
      white: '#cdd4e0',
      brightBlack: '#8f9aae',
      brightRed: '#ff6480',
      brightGreen: '#3fc56b',
      brightYellow: '#f9c859',
      brightBlue: '#10b1fe',
      brightMagenta: '#ff78f8',
      brightCyan: '#5fb9bc',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'BlulocoLight',
    name: 'BlulocoLight',
    isPreset: true,
    themeData: {
      foreground: '#373a41',
      background: '#f9f9f9',
      cursor: '#f32759',
      cursorAccent: '#ffffff',
      selectionBackground: '#daf0ff',
      selectionForeground: '#373a41',
      black: '#373a41',
      red: '#d52753',
      green: '#23974a',
      yellow: '#df631c',
      blue: '#275fe4',
      magenta: '#823ff1',
      cyan: '#27618d',
      white: '#babbc2',
      brightBlack: '#676a77',
      brightRed: '#ff6480',
      brightGreen: '#3cbc66',
      brightYellow: '#c5a332',
      brightBlue: '#0099e1',
      brightMagenta: '#ce33c0',
      brightCyan: '#6d93bb',
      brightWhite: '#d3d3d3',
    },
  },
  {
    preset_key: 'Borland',
    name: 'Borland',
    isPreset: true,
    themeData: {
      foreground: '#ffff4e',
      background: '#0000a4',
      cursor: '#ffa560',
      cursorAccent: '#ffffff',
      selectionBackground: '#a4a4a4',
      selectionForeground: '#0000a4',
      black: '#4f4f4f',
      red: '#ff6c60',
      green: '#a8ff60',
      yellow: '#ffffb6',
      blue: '#96cbfe',
      magenta: '#ff73fd',
      cyan: '#c6c5fe',
      white: '#eeeeee',
      brightBlack: '#7c7c7c',
      brightRed: '#ffb6b0',
      brightGreen: '#ceffac',
      brightYellow: '#ffffcc',
      brightBlue: '#b5dcff',
      brightMagenta: '#ff9cfe',
      brightCyan: '#dfdffe',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Box',
    name: 'Box',
    isPreset: true,
    themeData: {
      foreground: '#9fef00',
      background: '#141d2b',
      cursor: '#9fef00',
      cursorAccent: '#111111',
      selectionBackground: '#a4b1cd',
      selectionForeground: '#141d2b',
      black: '#000000',
      red: '#cc0403',
      green: '#19cb00',
      yellow: '#cecb00',
      blue: '#0d73cc',
      magenta: '#cb1ed1',
      cyan: '#0dcdcd',
      white: '#dddddd',
      brightBlack: '#767676',
      brightRed: '#f2201f',
      brightGreen: '#23fd00',
      brightYellow: '#fffd00',
      brightBlue: '#1a8fff',
      brightMagenta: '#fd28ff',
      brightCyan: '#14ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Breeze',
    name: 'Breeze',
    isPreset: true,
    themeData: {
      foreground: '#eff0f1',
      background: '#31363b',
      cursor: '#eff0f1',
      cursorAccent: '#31363b',
      selectionBackground: '#eff0f1',
      selectionForeground: '#31363b',
      black: '#31363b',
      red: '#ed1515',
      green: '#11d116',
      yellow: '#f67400',
      blue: '#1d99f3',
      magenta: '#9b59b6',
      cyan: '#1abc9c',
      white: '#eff0f1',
      brightBlack: '#7f8c8d',
      brightRed: '#c0392b',
      brightGreen: '#1cdc9a',
      brightYellow: '#fdbc4b',
      brightBlue: '#3daee9',
      brightMagenta: '#8e44ad',
      brightCyan: '#16a085',
      brightWhite: '#fcfcfc',
    },
  },
  {
    preset_key: 'Bright_Lights',
    name: 'Bright Lights',
    isPreset: true,
    themeData: {
      foreground: '#b3c9d7',
      background: '#191919',
      cursor: '#f34b00',
      cursorAccent: '#002831',
      selectionBackground: '#b3c9d7',
      selectionForeground: '#191919',
      black: '#191919',
      red: '#ff355b',
      green: '#b7e876',
      yellow: '#ffc251',
      blue: '#76d4ff',
      magenta: '#ba76e7',
      cyan: '#6cbfb5',
      white: '#c2c8d7',
      brightBlack: '#191919',
      brightRed: '#ff355b',
      brightGreen: '#b7e876',
      brightYellow: '#ffc251',
      brightBlue: '#76d5ff',
      brightMagenta: '#ba76e7',
      brightCyan: '#6cbfb5',
      brightWhite: '#c2c8d7',
    },
  },
  {
    preset_key: 'Broadcast',
    name: 'Broadcast',
    isPreset: true,
    themeData: {
      foreground: '#e6e1dc',
      background: '#2b2b2b',
      cursor: '#ffffff',
      cursorAccent: '#e6e1dc',
      selectionBackground: '#5a647e',
      selectionForeground: '#e6e1dc',
      black: '#000000',
      red: '#da4939',
      green: '#519f50',
      yellow: '#ffd24a',
      blue: '#6d9cbe',
      magenta: '#d0d0ff',
      cyan: '#6e9cbe',
      white: '#ffffff',
      brightBlack: '#323232',
      brightRed: '#ff7b6b',
      brightGreen: '#83d182',
      brightYellow: '#ffff7c',
      brightBlue: '#9fcef0',
      brightMagenta: '#ffffff',
      brightCyan: '#a0cef0',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Brogrammer',
    name: 'Brogrammer',
    isPreset: true,
    themeData: {
      foreground: '#d6dbe5',
      background: '#131313',
      cursor: '#b9b9b9',
      cursorAccent: '#101010',
      selectionBackground: '#1f1f1f',
      selectionForeground: '#d6dbe5',
      black: '#1f1f1f',
      red: '#f81118',
      green: '#2dc55e',
      yellow: '#ecba0f',
      blue: '#2a84d2',
      magenta: '#4e5ab7',
      cyan: '#1081d6',
      white: '#d6dbe5',
      brightBlack: '#d6dbe5',
      brightRed: '#de352e',
      brightGreen: '#1dd361',
      brightYellow: '#f3bd09',
      brightBlue: '#1081d6',
      brightMagenta: '#5350b9',
      brightCyan: '#0f7ddb',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Builtin_Dark',
    name: 'Builtin Dark',
    isPreset: true,
    themeData: {
      foreground: '#bbbbbb',
      background: '#000000',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#bb0000',
      green: '#00bb00',
      yellow: '#bbbb00',
      blue: '#0000bb',
      magenta: '#bb00bb',
      cyan: '#00bbbb',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#ff5555',
      brightGreen: '#55ff55',
      brightYellow: '#ffff55',
      brightBlue: '#5555ff',
      brightMagenta: '#ff55ff',
      brightCyan: '#55ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Builtin_Light',
    name: 'Builtin Light',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#000000',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#bb0000',
      green: '#00bb00',
      yellow: '#bbbb00',
      blue: '#0000bb',
      magenta: '#bb00bb',
      cyan: '#00bbbb',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#ff5555',
      brightGreen: '#55ff55',
      brightYellow: '#ffff55',
      brightBlue: '#5555ff',
      brightMagenta: '#ff55ff',
      brightCyan: '#55ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Builtin_Pastel_Dark',
    name: 'Builtin Pastel Dark',
    isPreset: true,
    themeData: {
      foreground: '#bbbbbb',
      background: '#000000',
      cursor: '#ffa560',
      cursorAccent: '#ffffff',
      selectionBackground: '#363983',
      selectionForeground: '#f2f2f2',
      black: '#4f4f4f',
      red: '#ff6c60',
      green: '#a8ff60',
      yellow: '#ffffb6',
      blue: '#96cbfe',
      magenta: '#ff73fd',
      cyan: '#c6c5fe',
      white: '#eeeeee',
      brightBlack: '#7c7c7c',
      brightRed: '#ffb6b0',
      brightGreen: '#ceffac',
      brightYellow: '#ffffcc',
      brightBlue: '#b5dcff',
      brightMagenta: '#ff9cfe',
      brightCyan: '#dfdffe',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Builtin_Solarized_Dark',
    name: 'Builtin Solarized Dark',
    isPreset: true,
    themeData: {
      foreground: '#839496',
      background: '#002b36',
      cursor: '#839496',
      cursorAccent: '#073642',
      selectionBackground: '#073642',
      selectionForeground: '#93a1a1',
      black: '#073642',
      red: '#dc322f',
      green: '#859900',
      yellow: '#b58900',
      blue: '#268bd2',
      magenta: '#d33682',
      cyan: '#2aa198',
      white: '#eee8d5',
      brightBlack: '#002b36',
      brightRed: '#cb4b16',
      brightGreen: '#586e75',
      brightYellow: '#657b83',
      brightBlue: '#839496',
      brightMagenta: '#6c71c4',
      brightCyan: '#93a1a1',
      brightWhite: '#fdf6e3',
    },
  },
  {
    preset_key: 'Builtin_Solarized_Light',
    name: 'Builtin Solarized Light',
    isPreset: true,
    themeData: {
      foreground: '#657b83',
      background: '#fdf6e3',
      cursor: '#657b83',
      cursorAccent: '#eee8d5',
      selectionBackground: '#eee8d5',
      selectionForeground: '#586e75',
      black: '#073642',
      red: '#dc322f',
      green: '#859900',
      yellow: '#b58900',
      blue: '#268bd2',
      magenta: '#d33682',
      cyan: '#2aa198',
      white: '#eee8d5',
      brightBlack: '#002b36',
      brightRed: '#cb4b16',
      brightGreen: '#586e75',
      brightYellow: '#657b83',
      brightBlue: '#839496',
      brightMagenta: '#6c71c4',
      brightCyan: '#93a1a1',
      brightWhite: '#fdf6e3',
    },
  },
  {
    preset_key: 'Builtin_Tango_Dark',
    name: 'Builtin Tango Dark',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#000000',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#cc0000',
      green: '#4e9a06',
      yellow: '#c4a000',
      blue: '#3465a4',
      magenta: '#75507b',
      cyan: '#06989a',
      white: '#d3d7cf',
      brightBlack: '#555753',
      brightRed: '#ef2929',
      brightGreen: '#8ae234',
      brightYellow: '#fce94f',
      brightBlue: '#729fcf',
      brightMagenta: '#ad7fa8',
      brightCyan: '#34e2e2',
      brightWhite: '#eeeeec',
    },
  },
  {
    preset_key: 'Builtin_Tango_Light',
    name: 'Builtin Tango Light',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#000000',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#cc0000',
      green: '#4e9a06',
      yellow: '#c4a000',
      blue: '#3465a4',
      magenta: '#75507b',
      cyan: '#06989a',
      white: '#d3d7cf',
      brightBlack: '#555753',
      brightRed: '#ef2929',
      brightGreen: '#8ae234',
      brightYellow: '#fce94f',
      brightBlue: '#729fcf',
      brightMagenta: '#ad7fa8',
      brightCyan: '#34e2e2',
      brightWhite: '#eeeeec',
    },
  },
  {
    preset_key: 'C64',
    name: 'C64',
    isPreset: true,
    themeData: {
      foreground: '#7869c4',
      background: '#40318d',
      cursor: '#7869c4',
      cursorAccent: '#40318d',
      selectionBackground: '#7869c4',
      selectionForeground: '#40318d',
      black: '#090300',
      red: '#883932',
      green: '#55a049',
      yellow: '#bfce72',
      blue: '#40318d',
      magenta: '#8b3f96',
      cyan: '#67b6bd',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#883932',
      brightGreen: '#55a049',
      brightYellow: '#bfce72',
      brightBlue: '#40318d',
      brightMagenta: '#8b3f96',
      brightCyan: '#67b6bd',
      brightWhite: '#f7f7f7',
    },
  },
  {
    preset_key: 'Calamity',
    name: 'Calamity',
    isPreset: true,
    themeData: {
      foreground: '#d5ced9',
      background: '#2f2833',
      cursor: '#d5ced9',
      cursorAccent: '#2f2833',
      selectionBackground: '#7e6c88',
      selectionForeground: '#d5ced9',
      black: '#2f2833',
      red: '#fc644d',
      green: '#a5f69c',
      yellow: '#e9d7a5',
      blue: '#3b79c7',
      magenta: '#f92672',
      cyan: '#74d3de',
      white: '#d5ced9',
      brightBlack: '#7e6c88',
      brightRed: '#fc644d',
      brightGreen: '#a5f69c',
      brightYellow: '#e9d7a5',
      brightBlue: '#3b79c7',
      brightMagenta: '#f92672',
      brightCyan: '#74d3de',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'catppuccin-frappe',
    name: 'catppuccin-frappe',
    isPreset: true,
    themeData: {
      foreground: '#c6d0f5',
      background: '#303446',
      cursor: '#f2d5cf',
      cursorAccent: '#c6d0f5',
      selectionBackground: '#626880',
      selectionForeground: '#c6d0f5',
      black: '#51576d',
      red: '#e78284',
      green: '#a6d189',
      yellow: '#e5c890',
      blue: '#8caaee',
      magenta: '#f4b8e4',
      cyan: '#81c8be',
      white: '#a5adce',
      brightBlack: '#626880',
      brightRed: '#e67172',
      brightGreen: '#8ec772',
      brightYellow: '#d9ba73',
      brightBlue: '#7b9ef0',
      brightMagenta: '#f2a4db',
      brightCyan: '#5abfb5',
      brightWhite: '#b5bfe2',
    },
  },
  {
    preset_key: 'catppuccin-latte',
    name: 'catppuccin-latte',
    isPreset: true,
    themeData: {
      foreground: '#4c4f69',
      background: '#eff1f5',
      cursor: '#dc8a78',
      cursorAccent: '#4c4f69',
      selectionBackground: '#acb0be',
      selectionForeground: '#4c4f69',
      black: '#5c5f77',
      red: '#d20f39',
      green: '#40a02b',
      yellow: '#df8e1d',
      blue: '#1e66f5',
      magenta: '#ea76cb',
      cyan: '#179299',
      white: '#acb0be',
      brightBlack: '#6c6f85',
      brightRed: '#de293e',
      brightGreen: '#49af3d',
      brightYellow: '#eea02d',
      brightBlue: '#456eff',
      brightMagenta: '#fe85d8',
      brightCyan: '#2d9fa8',
      brightWhite: '#bcc0cc',
    },
  },
  {
    preset_key: 'catppuccin-macchiato',
    name: 'catppuccin-macchiato',
    isPreset: true,
    themeData: {
      foreground: '#cad3f5',
      background: '#24273a',
      cursor: '#f4dbd6',
      cursorAccent: '#cad3f5',
      selectionBackground: '#5b6078',
      selectionForeground: '#cad3f5',
      black: '#494d64',
      red: '#ed8796',
      green: '#a6da95',
      yellow: '#eed49f',
      blue: '#8aadf4',
      magenta: '#f5bde6',
      cyan: '#8bd5ca',
      white: '#a5adcb',
      brightBlack: '#5b6078',
      brightRed: '#ec7486',
      brightGreen: '#8ccf7f',
      brightYellow: '#e1c682',
      brightBlue: '#78a1f6',
      brightMagenta: '#f2a9dd',
      brightCyan: '#63cbc0',
      brightWhite: '#b8c0e0',
    },
  },
  {
    preset_key: 'catppuccin-mocha',
    name: 'catppuccin-mocha',
    isPreset: true,
    themeData: {
      foreground: '#cdd6f4',
      background: '#1e1e2e',
      cursor: '#f5e0dc',
      cursorAccent: '#cdd6f4',
      selectionBackground: '#585b70',
      selectionForeground: '#cdd6f4',
      black: '#45475a',
      red: '#f38ba8',
      green: '#a6e3a1',
      yellow: '#f9e2af',
      blue: '#89b4fa',
      magenta: '#f5c2e7',
      cyan: '#94e2d5',
      white: '#a6adc8',
      brightBlack: '#585b70',
      brightRed: '#f37799',
      brightGreen: '#89d88b',
      brightYellow: '#ebd391',
      brightBlue: '#74a8fc',
      brightMagenta: '#f2aede',
      brightCyan: '#6bd7ca',
      brightWhite: '#bac2de',
    },
  },
  {
    preset_key: 'CGA',
    name: 'CGA',
    isPreset: true,
    themeData: {
      foreground: '#aaaaaa',
      background: '#000000',
      cursor: '#b8b8b8',
      cursorAccent: '#ffffff',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#aa0000',
      green: '#00aa00',
      yellow: '#aa5500',
      blue: '#0000aa',
      magenta: '#aa00aa',
      cyan: '#00aaaa',
      white: '#aaaaaa',
      brightBlack: '#555555',
      brightRed: '#ff5555',
      brightGreen: '#55ff55',
      brightYellow: '#ffff55',
      brightBlue: '#5555ff',
      brightMagenta: '#ff55ff',
      brightCyan: '#55ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Chalk',
    name: 'Chalk',
    isPreset: true,
    themeData: {
      foreground: '#d2d8d9',
      background: '#2b2d2e',
      cursor: '#708284',
      cursorAccent: '#002831',
      selectionBackground: '#e4e8ed',
      selectionForeground: '#3f4041',
      black: '#7d8b8f',
      red: '#b23a52',
      green: '#789b6a',
      yellow: '#b9ac4a',
      blue: '#2a7fac',
      magenta: '#bd4f5a',
      cyan: '#44a799',
      white: '#d2d8d9',
      brightBlack: '#888888',
      brightRed: '#f24840',
      brightGreen: '#80c470',
      brightYellow: '#ffeb62',
      brightBlue: '#4196ff',
      brightMagenta: '#fc5275',
      brightCyan: '#53cdbd',
      brightWhite: '#d2d8d9',
    },
  },
  {
    preset_key: 'Chalkboard',
    name: 'Chalkboard',
    isPreset: true,
    themeData: {
      foreground: '#d9e6f2',
      background: '#29262f',
      cursor: '#d9e6f2',
      cursorAccent: '#29262f',
      selectionBackground: '#073642',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#c37372',
      green: '#72c373',
      yellow: '#c2c372',
      blue: '#7372c3',
      magenta: '#c372c2',
      cyan: '#72c2c3',
      white: '#d9d9d9',
      brightBlack: '#323232',
      brightRed: '#dbaaaa',
      brightGreen: '#aadbaa',
      brightYellow: '#dadbaa',
      brightBlue: '#aaaadb',
      brightMagenta: '#dbaada',
      brightCyan: '#aadadb',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'ChallengerDeep',
    name: 'ChallengerDeep',
    isPreset: true,
    themeData: {
      foreground: '#cbe1e7',
      background: '#1e1c31',
      cursor: '#fbfcfc',
      cursorAccent: '#ff271d',
      selectionBackground: '#cbe1e7',
      selectionForeground: '#1e1c31',
      black: '#141228',
      red: '#ff5458',
      green: '#62d196',
      yellow: '#ffb378',
      blue: '#65b2ff',
      magenta: '#906cff',
      cyan: '#63f2f1',
      white: '#a6b3cc',
      brightBlack: '#565575',
      brightRed: '#ff8080',
      brightGreen: '#95ffa4',
      brightYellow: '#ffe9aa',
      brightBlue: '#91ddff',
      brightMagenta: '#c991e1',
      brightCyan: '#aaffe4',
      brightWhite: '#cbe3e7',
    },
  },
  {
    preset_key: 'Chester',
    name: 'Chester',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#2c3643',
      cursor: '#b4b1b1',
      cursorAccent: '#000000',
      selectionBackground: '#67747c',
      selectionForeground: '#ffffff',
      black: '#080200',
      red: '#fa5e5b',
      green: '#16c98d',
      yellow: '#ffc83f',
      blue: '#288ad6',
      magenta: '#d34590',
      cyan: '#28ddde',
      white: '#e7e7e7',
      brightBlack: '#6f6b68',
      brightRed: '#fa5e5b',
      brightGreen: '#16c98d',
      brightYellow: '#feef6d',
      brightBlue: '#278ad6',
      brightMagenta: '#d34590',
      brightCyan: '#27dede',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Ciapre',
    name: 'Ciapre',
    isPreset: true,
    themeData: {
      foreground: '#aea47a',
      background: '#191c27',
      cursor: '#92805b',
      cursorAccent: '#181818',
      selectionBackground: '#172539',
      selectionForeground: '#aea47f',
      black: '#181818',
      red: '#810009',
      green: '#48513b',
      yellow: '#cc8b3f',
      blue: '#576d8c',
      magenta: '#724d7c',
      cyan: '#5c4f4b',
      white: '#aea47f',
      brightBlack: '#555555',
      brightRed: '#ac3835',
      brightGreen: '#a6a75d',
      brightYellow: '#dcdf7c',
      brightBlue: '#3097c6',
      brightMagenta: '#d33061',
      brightCyan: '#f3dbb2',
      brightWhite: '#f4f4f4',
    },
  },
  {
    preset_key: 'citruszest',
    name: 'citruszest',
    isPreset: true,
    themeData: {
      foreground: '#bfbfbf',
      background: '#121212',
      cursor: '#666666',
      cursorAccent: '#f9f9f9',
      selectionBackground: '#ff8c00',
      selectionForeground: '#f4f4f4',
      black: '#404040',
      red: '#ff5454',
      green: '#00cc7a',
      yellow: '#ffd400',
      blue: '#00bfff',
      magenta: '#ff90fe',
      cyan: '#48d1cc',
      white: '#bfbfbf',
      brightBlack: '#808080',
      brightRed: '#ff1a75',
      brightGreen: '#1affa3',
      brightYellow: '#ffff00',
      brightBlue: '#33cfff',
      brightMagenta: '#ffb2fe',
      brightCyan: '#00fff2',
      brightWhite: '#f9f9f9',
    },
  },
  {
    preset_key: 'CLRS',
    name: 'CLRS',
    isPreset: true,
    themeData: {
      foreground: '#262626',
      background: '#ffffff',
      cursor: '#6fd3fc',
      cursorAccent: '#ffffff',
      selectionBackground: '#6fd3fc',
      selectionForeground: '#041730',
      black: '#000000',
      red: '#f8282a',
      green: '#328a5d',
      yellow: '#fa701d',
      blue: '#135cd0',
      magenta: '#9f00bd',
      cyan: '#33c3c1',
      white: '#b3b3b3',
      brightBlack: '#555753',
      brightRed: '#fb0416',
      brightGreen: '#2cc631',
      brightYellow: '#fdd727',
      brightBlue: '#1670ff',
      brightMagenta: '#e900b0',
      brightCyan: '#3ad5ce',
      brightWhite: '#eeeeec',
    },
  },
  {
    preset_key: 'Cobalt_Neon',
    name: 'Cobalt Neon',
    isPreset: true,
    themeData: {
      foreground: '#8ff586',
      background: '#142838',
      cursor: '#c4206f',
      cursorAccent: '#8ff586',
      selectionBackground: '#094fb1',
      selectionForeground: '#8ff586',
      black: '#142631',
      red: '#ff2320',
      green: '#3ba5ff',
      yellow: '#e9e75c',
      blue: '#8ff586',
      magenta: '#781aa0',
      cyan: '#8ff586',
      white: '#ba46b2',
      brightBlack: '#fff688',
      brightRed: '#d4312e',
      brightGreen: '#8ff586',
      brightYellow: '#e9f06d',
      brightBlue: '#3c7dd2',
      brightMagenta: '#8230a7',
      brightCyan: '#6cbc67',
      brightWhite: '#8ff586',
    },
  },
  {
    preset_key: 'Cobalt2',
    name: 'Cobalt2',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#132738',
      cursor: '#f0cc09',
      cursorAccent: '#fefff2',
      selectionBackground: '#18354f',
      selectionForeground: '#b5b5b5',
      black: '#000000',
      red: '#ff0000',
      green: '#38de21',
      yellow: '#ffe50a',
      blue: '#1460d2',
      magenta: '#ff005d',
      cyan: '#00bbbb',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#f40e17',
      brightGreen: '#3bd01d',
      brightYellow: '#edc809',
      brightBlue: '#5555ff',
      brightMagenta: '#ff55ff',
      brightCyan: '#6ae3fa',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'CobaltNext',
    name: 'CobaltNext',
    isPreset: true,
    themeData: {
      foreground: '#d8dee9',
      background: '#1b2b34',
      cursor: '#fac863',
      cursorAccent: '#d8dee9',
      selectionBackground: '#4f5b66',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#ed5f7d',
      green: '#99c794',
      yellow: '#fac863',
      blue: '#5a9bcf',
      magenta: '#c5a5c5',
      cyan: '#5fb3b3',
      white: '#d8dee9',
      brightBlack: '#65737e',
      brightRed: '#d6838c',
      brightGreen: '#c1dcbe',
      brightYellow: '#ffde9b',
      brightBlue: '#8abee7',
      brightMagenta: '#edcded',
      brightCyan: '#9be2e2',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'CobaltNext-Dark',
    name: 'CobaltNext-Dark',
    isPreset: true,
    themeData: {
      foreground: '#d8dee9',
      background: '#0f1c23',
      cursor: '#fac863',
      cursorAccent: '#ffffff',
      selectionBackground: '#5fb3b3',
      selectionForeground: '#ffffff',
      black: '#282f36',
      red: '#e6576a',
      green: '#99c794',
      yellow: '#fac863',
      blue: '#5a9bcf',
      magenta: '#c5a5c5',
      cyan: '#5fb3b3',
      white: '#d8dee9',
      brightBlack: '#65737e',
      brightRed: '#d6838c',
      brightGreen: '#c1dcbe',
      brightYellow: '#ffde9b',
      brightBlue: '#8abee7',
      brightMagenta: '#edcded',
      brightCyan: '#9be2e2',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'CobaltNext-Minimal',
    name: 'CobaltNext-Minimal',
    isPreset: true,
    themeData: {
      foreground: '#d8dee9',
      background: '#0f1c23',
      cursor: '#5fb3b3',
      cursorAccent: '#ffffff',
      selectionBackground: '#5fb3b3',
      selectionForeground: '#ffffff',
      black: '#343d46',
      red: '#ed6f7d',
      green: '#99c794',
      yellow: '#fac863',
      blue: '#5a9bcf',
      magenta: '#c5a5c5',
      cyan: '#5fb3b3',
      white: '#d8dee9',
      brightBlack: '#65737e',
      brightRed: '#d6838c',
      brightGreen: '#c1dcbe',
      brightYellow: '#ffde9b',
      brightBlue: '#8abee7',
      brightMagenta: '#edcded',
      brightCyan: '#9be2e2',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'coffee_theme',
    name: 'coffee_theme',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#f5deb3',
      cursor: '#c7c7c7',
      cursorAccent: '#fffc67',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#c91b00',
      green: '#00c200',
      yellow: '#c7c400',
      blue: '#0225c7',
      magenta: '#ca30c7',
      cyan: '#00c5c7',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#ff6e67',
      brightGreen: '#5ffa68',
      brightYellow: '#fffc67',
      brightBlue: '#6871ff',
      brightMagenta: '#ff77ff',
      brightCyan: '#60fdff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'CrayonPonyFish',
    name: 'CrayonPonyFish',
    isPreset: true,
    themeData: {
      foreground: '#68525a',
      background: '#150707',
      cursor: '#68525a',
      cursorAccent: '#140707',
      selectionBackground: '#2b1b1d',
      selectionForeground: '#69525a',
      black: '#2b1b1d',
      red: '#91002b',
      green: '#579524',
      yellow: '#ab311b',
      blue: '#8c87b0',
      magenta: '#692f50',
      cyan: '#e8a866',
      white: '#68525a',
      brightBlack: '#3d2b2e',
      brightRed: '#c5255d',
      brightGreen: '#8dff57',
      brightYellow: '#c8381d',
      brightBlue: '#cfc9ff',
      brightMagenta: '#fc6cba',
      brightCyan: '#ffceaf',
      brightWhite: '#b0949d',
    },
  },
  {
    preset_key: 'CutiePro',
    name: 'CutiePro',
    isPreset: true,
    themeData: {
      foreground: '#d5d0c9',
      background: '#181818',
      cursor: '#efc4cd',
      cursorAccent: '#181818',
      selectionBackground: '#363636',
      selectionForeground: '#d5d0c9',
      black: '#000000',
      red: '#f56e7f',
      green: '#bec975',
      yellow: '#f58669',
      blue: '#42d9c5',
      magenta: '#d286b7',
      cyan: '#37cb8a',
      white: '#d5c3c3',
      brightBlack: '#88847f',
      brightRed: '#e5a1a3',
      brightGreen: '#e8d6a7',
      brightYellow: '#f1bb79',
      brightBlue: '#80c5de',
      brightMagenta: '#b294bb',
      brightCyan: '#9dccbb',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Cyberdyne',
    name: 'Cyberdyne',
    isPreset: true,
    themeData: {
      foreground: '#00ff92',
      background: '#151144',
      cursor: '#00ff9c',
      cursorAccent: '#ffffff',
      selectionBackground: '#454d96',
      selectionForeground: '#f4f4f4',
      black: '#080808',
      red: '#ff8373',
      green: '#00c172',
      yellow: '#d2a700',
      blue: '#0071cf',
      magenta: '#ff90fe',
      cyan: '#6bffdd',
      white: '#f1f1f1',
      brightBlack: '#2e2e2e',
      brightRed: '#ffc4be',
      brightGreen: '#d6fcba',
      brightYellow: '#fffed5',
      brightBlue: '#c2e3ff',
      brightMagenta: '#ffb2fe',
      brightCyan: '#e6e7fe',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'cyberpunk',
    name: 'cyberpunk',
    isPreset: true,
    themeData: {
      foreground: '#e5e5e5',
      background: '#332a57',
      cursor: '#21f6bc',
      cursorAccent: '#ffffff',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#ff7092',
      green: '#00fbac',
      yellow: '#fffa6a',
      blue: '#00bfff',
      magenta: '#df95ff',
      cyan: '#86cbfe',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#ff8aa4',
      brightGreen: '#21f6bc',
      brightYellow: '#fff787',
      brightBlue: '#1bccfd',
      brightMagenta: '#e6aefe',
      brightCyan: '#99d6fc',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'CyberpunkScarletProtocol',
    name: 'CyberpunkScarletProtocol',
    isPreset: true,
    themeData: {
      foreground: '#d13554',
      background: '#101116',
      cursor: '#9bfca8',
      cursorAccent: '#ffffff',
      selectionBackground: '#c7ddfc',
      selectionForeground: '#000000',
      black: '#101116',
      red: '#ea3356',
      green: '#64d98c',
      yellow: '#faf968',
      blue: '#306fb1',
      magenta: '#ba3ec1',
      cyan: '#59c2c6',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#ed776d',
      brightGreen: '#8df77a',
      brightYellow: '#fefc7f',
      brightBlue: '#6a71f6',
      brightMagenta: '#ae40e4',
      brightCyan: '#8efafd',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Dark_Modern',
    name: 'Dark Modern',
    isPreset: true,
    themeData: {
      foreground: '#cccccc',
      background: '#1f1f1f',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#3a3d41',
      selectionForeground: '#e0e0e0',
      black: '#272727',
      red: '#f74949',
      green: '#2ea043',
      yellow: '#9e6a03',
      blue: '#0078d4',
      magenta: '#d01273',
      cyan: '#1db4d6',
      white: '#cccccc',
      brightBlack: '#5d5d5d',
      brightRed: '#dc5452',
      brightGreen: '#23d18b',
      brightYellow: '#f5f543',
      brightBlue: '#3b8eea',
      brightMagenta: '#d670d6',
      brightCyan: '#29b8db',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'Dark_Pastel',
    name: 'Dark Pastel',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#000000',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#ff5555',
      green: '#55ff55',
      yellow: '#ffff55',
      blue: '#5555ff',
      magenta: '#ff55ff',
      cyan: '#55ffff',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#ff5555',
      brightGreen: '#55ff55',
      brightYellow: '#ffff55',
      brightBlue: '#5555ff',
      brightMagenta: '#ff55ff',
      brightCyan: '#55ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Dark+',
    name: 'Dark+',
    isPreset: true,
    themeData: {
      foreground: '#cccccc',
      background: '#1e1e1e',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#3a3d41',
      selectionForeground: '#e0e0e0',
      black: '#000000',
      red: '#cd3131',
      green: '#0dbc79',
      yellow: '#e5e510',
      blue: '#2472c8',
      magenta: '#bc3fbc',
      cyan: '#11a8cd',
      white: '#e5e5e5',
      brightBlack: '#666666',
      brightRed: '#f14c4c',
      brightGreen: '#23d18b',
      brightYellow: '#f5f543',
      brightBlue: '#3b8eea',
      brightMagenta: '#d670d6',
      brightCyan: '#29b8db',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'darkermatrix',
    name: 'darkermatrix',
    isPreset: true,
    themeData: {
      foreground: '#28380d',
      background: '#070c0e',
      cursor: '#373a26',
      cursorAccent: '#00ff87',
      selectionBackground: '#0f191c',
      selectionForeground: '#00ff87',
      black: '#091013',
      red: '#002e18',
      green: '#6fa64c',
      yellow: '#595900',
      blue: '#00cb6b',
      magenta: '#412a4d',
      cyan: '#125459',
      white: '#002e19',
      brightBlack: '#333333',
      brightRed: '#00381d',
      brightGreen: '#90d762',
      brightYellow: '#e2e500',
      brightBlue: '#00ff87',
      brightMagenta: '#412a4d',
      brightCyan: '#176c73',
      brightWhite: '#00381e',
    },
  },
  {
    preset_key: 'darkmatrix',
    name: 'darkmatrix',
    isPreset: true,
    themeData: {
      foreground: '#3e5715',
      background: '#070c0e',
      cursor: '#9fa86e',
      cursorAccent: '#00ff87',
      selectionBackground: '#0f191c',
      selectionForeground: '#00ff87',
      black: '#091013',
      red: '#006536',
      green: '#6fa64c',
      yellow: '#7e8000',
      blue: '#2c9a84',
      magenta: '#452d53',
      cyan: '#114d53',
      white: '#006536',
      brightBlack: '#333333',
      brightRed: '#00733d',
      brightGreen: '#90d762',
      brightYellow: '#e2e500',
      brightBlue: '#46d8b8',
      brightMagenta: '#4a3059',
      brightCyan: '#12545a',
      brightWhite: '#006536',
    },
  },
  {
    preset_key: 'Darkside',
    name: 'Darkside',
    isPreset: true,
    themeData: {
      foreground: '#bababa',
      background: '#222324',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#303333',
      selectionForeground: '#bababa',
      black: '#000000',
      red: '#e8341c',
      green: '#68c256',
      yellow: '#f2d42c',
      blue: '#1c98e8',
      magenta: '#8e69c9',
      cyan: '#1c98e8',
      white: '#bababa',
      brightBlack: '#000000',
      brightRed: '#e05a4f',
      brightGreen: '#77b869',
      brightYellow: '#efd64b',
      brightBlue: '#387cd3',
      brightMagenta: '#957bbe',
      brightCyan: '#3d97e2',
      brightWhite: '#bababa',
    },
  },
  {
    preset_key: 'dayfox',
    name: 'dayfox',
    isPreset: true,
    themeData: {
      foreground: '#3d2b5a',
      background: '#f6f2ee',
      cursor: '#3d2b5a',
      cursorAccent: '#f6f2ee',
      selectionBackground: '#e7d2be',
      selectionForeground: '#3d2b5a',
      black: '#352c24',
      red: '#a5222f',
      green: '#396847',
      yellow: '#ac5402',
      blue: '#2848a9',
      magenta: '#6e33ce',
      cyan: '#287980',
      white: '#f2e9e1',
      brightBlack: '#534c45',
      brightRed: '#b3434e',
      brightGreen: '#577f63',
      brightYellow: '#b86e28',
      brightBlue: '#4863b6',
      brightMagenta: '#8452d5',
      brightCyan: '#488d93',
      brightWhite: '#f4ece6',
    },
  },
  {
    preset_key: 'deep',
    name: 'deep',
    isPreset: true,
    themeData: {
      foreground: '#cdcdcd',
      background: '#090909',
      cursor: '#d0d0d0',
      cursorAccent: '#151515',
      selectionBackground: '#780002',
      selectionForeground: '#ececec',
      black: '#000000',
      red: '#d70005',
      green: '#1cd915',
      yellow: '#d9bd26',
      blue: '#5665ff',
      magenta: '#b052da',
      cyan: '#50d2da',
      white: '#e0e0e0',
      brightBlack: '#535353',
      brightRed: '#fb0007',
      brightGreen: '#22ff18',
      brightYellow: '#fedc2b',
      brightBlue: '#9fa9ff',
      brightMagenta: '#e09aff',
      brightCyan: '#8df9ff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Desert',
    name: 'Desert',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#333333',
      cursor: '#00ff00',
      cursorAccent: '#000000',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#4d4d4d',
      red: '#ff2b2b',
      green: '#98fb98',
      yellow: '#f0e68c',
      blue: '#cd853f',
      magenta: '#ffdead',
      cyan: '#ffa0a0',
      white: '#f5deb3',
      brightBlack: '#555555',
      brightRed: '#ff5555',
      brightGreen: '#55ff55',
      brightYellow: '#ffff55',
      brightBlue: '#87ceff',
      brightMagenta: '#ff55ff',
      brightCyan: '#ffd700',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'detuned',
    name: 'detuned',
    isPreset: true,
    themeData: {
      foreground: '#c7c7c7',
      background: '#000000',
      cursor: '#c7c7c7',
      cursorAccent: '#ffffff',
      selectionBackground: '#c7ddfc',
      selectionForeground: '#000000',
      black: '#171717',
      red: '#ea5386',
      green: '#b3e153',
      yellow: '#e4da81',
      blue: '#4192d3',
      magenta: '#8f3ef6',
      cyan: '#6cb4d5',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#ea86ac',
      brightGreen: '#c5e280',
      brightYellow: '#fdf38f',
      brightBlue: '#55bbf9',
      brightMagenta: '#b9a0f9',
      brightCyan: '#7fd4fb',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Dimidium',
    name: 'Dimidium',
    isPreset: true,
    themeData: {
      foreground: '#bab7b6',
      background: '#141414',
      cursor: '#37e57b',
      cursorAccent: '#141414',
      selectionBackground: '#8db8e5',
      selectionForeground: '#141414',
      black: '#000000',
      red: '#cf494c',
      green: '#60b442',
      yellow: '#db9c11',
      blue: '#0575d8',
      magenta: '#af5ed2',
      cyan: '#1db6bb',
      white: '#bab7b6',
      brightBlack: '#817e7e',
      brightRed: '#ff643b',
      brightGreen: '#37e57b',
      brightYellow: '#fccd1a',
      brightBlue: '#688dfd',
      brightMagenta: '#ed6fe9',
      brightCyan: '#32e0fb',
      brightWhite: '#d3d8d9',
    },
  },
  {
    preset_key: 'DimmedMonokai',
    name: 'DimmedMonokai',
    isPreset: true,
    themeData: {
      foreground: '#b9bcba',
      background: '#1f1f1f',
      cursor: '#f83e19',
      cursorAccent: '#171717',
      selectionBackground: '#2a2d32',
      selectionForeground: '#b9bcba',
      black: '#3a3d43',
      red: '#be3f48',
      green: '#879a3b',
      yellow: '#c5a635',
      blue: '#4f76a1',
      magenta: '#855c8d',
      cyan: '#578fa4',
      white: '#b9bcba',
      brightBlack: '#888987',
      brightRed: '#fb001f',
      brightGreen: '#0f722f',
      brightYellow: '#c47033',
      brightBlue: '#186de3',
      brightMagenta: '#fb0067',
      brightCyan: '#2e706d',
      brightWhite: '#fdffb9',
    },
  },
  {
    preset_key: 'Django',
    name: 'Django',
    isPreset: true,
    themeData: {
      foreground: '#f8f8f8',
      background: '#0b2f20',
      cursor: '#336442',
      cursorAccent: '#f8f8f8',
      selectionBackground: '#245032',
      selectionForeground: '#f8f8f8',
      black: '#000000',
      red: '#fd6209',
      green: '#41a83e',
      yellow: '#ffe862',
      blue: '#245032',
      magenta: '#f8f8f8',
      cyan: '#9df39f',
      white: '#ffffff',
      brightBlack: '#323232',
      brightRed: '#ff943b',
      brightGreen: '#73da70',
      brightYellow: '#ffff94',
      brightBlue: '#568264',
      brightMagenta: '#ffffff',
      brightCyan: '#cfffd1',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'DjangoRebornAgain',
    name: 'DjangoRebornAgain',
    isPreset: true,
    themeData: {
      foreground: '#dadedc',
      background: '#051f14',
      cursor: '#ffcc00',
      cursorAccent: '#dadedc',
      selectionBackground: '#203727',
      selectionForeground: '#dadedc',
      black: '#000000',
      red: '#fd6209',
      green: '#41a83e',
      yellow: '#ffe862',
      blue: '#245032',
      magenta: '#f8f8f8',
      cyan: '#9df39f',
      white: '#ffffff',
      brightBlack: '#323232',
      brightRed: '#ff943b',
      brightGreen: '#73da70',
      brightYellow: '#ffff94',
      brightBlue: '#568264',
      brightMagenta: '#ffffff',
      brightCyan: '#cfffd1',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'DjangoSmooth',
    name: 'DjangoSmooth',
    isPreset: true,
    themeData: {
      foreground: '#f8f8f8',
      background: '#245032',
      cursor: '#336442',
      cursorAccent: '#f8f8f8',
      selectionBackground: '#336442',
      selectionForeground: '#f8f8f8',
      black: '#000000',
      red: '#fd6209',
      green: '#41a83e',
      yellow: '#ffe862',
      blue: '#989898',
      magenta: '#f8f8f8',
      cyan: '#9df39f',
      white: '#e8e8e7',
      brightBlack: '#323232',
      brightRed: '#ff943b',
      brightGreen: '#73da70',
      brightYellow: '#ffff94',
      brightBlue: '#cacaca',
      brightMagenta: '#ffffff',
      brightCyan: '#cfffd1',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Doom_Peacock',
    name: 'Doom Peacock',
    isPreset: true,
    themeData: {
      foreground: '#ede0ce',
      background: '#2b2a27',
      cursor: '#9c9c9d',
      cursorAccent: '#36312b',
      selectionBackground: '#a60033',
      selectionForeground: '#ffffff',
      black: '#1c1f24',
      red: '#cb4b16',
      green: '#26a6a6',
      yellow: '#bcd42a',
      blue: '#2a6cc6',
      magenta: '#a9a1e1',
      cyan: '#5699af',
      white: '#ede0ce',
      brightBlack: '#2b2a27',
      brightRed: '#ff5d38',
      brightGreen: '#98be65',
      brightYellow: '#e6f972',
      brightBlue: '#51afef',
      brightMagenta: '#c678dd',
      brightCyan: '#46d9ff',
      brightWhite: '#dfdfdf',
    },
  },
  {
    preset_key: 'DoomOne',
    name: 'DoomOne',
    isPreset: true,
    themeData: {
      foreground: '#bbc2cf',
      background: '#282c34',
      cursor: '#51afef',
      cursorAccent: '#1b1b1b',
      selectionBackground: '#42444b',
      selectionForeground: '#bbc2cf',
      black: '#000000',
      red: '#ff6c6b',
      green: '#98be65',
      yellow: '#ecbe7b',
      blue: '#a9a1e1',
      magenta: '#c678dd',
      cyan: '#51afef',
      white: '#bbc2cf',
      brightBlack: '#000000',
      brightRed: '#ff6655',
      brightGreen: '#99bb66',
      brightYellow: '#ecbe7b',
      brightBlue: '#a9a1e1',
      brightMagenta: '#c678dd',
      brightCyan: '#51afef',
      brightWhite: '#bfbfbf',
    },
  },
  {
    preset_key: 'DotGov',
    name: 'DotGov',
    isPreset: true,
    themeData: {
      foreground: '#ebebeb',
      background: '#262c35',
      cursor: '#d9002f',
      cursorAccent: '#ffffff',
      selectionBackground: '#1a4080',
      selectionForeground: '#ffffff',
      black: '#191919',
      red: '#bf091d',
      green: '#3d9751',
      yellow: '#f6bb34',
      blue: '#17b2e0',
      magenta: '#7830b0',
      cyan: '#8bd2ed',
      white: '#ffffff',
      brightBlack: '#191919',
      brightRed: '#bf091d',
      brightGreen: '#3d9751',
      brightYellow: '#f6bb34',
      brightBlue: '#17b2e0',
      brightMagenta: '#7830b0',
      brightCyan: '#8bd2ed',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Dracula',
    name: 'Dracula',
    isPreset: true,
    themeData: {
      foreground: '#f8f8f2',
      background: '#282a36',
      cursor: '#f8f8f2',
      cursorAccent: '#282a36',
      selectionBackground: '#44475a',
      selectionForeground: '#ffffff',
      black: '#21222c',
      red: '#ff5555',
      green: '#50fa7b',
      yellow: '#f1fa8c',
      blue: '#bd93f9',
      magenta: '#ff79c6',
      cyan: '#8be9fd',
      white: '#f8f8f2',
      brightBlack: '#6272a4',
      brightRed: '#ff6e6e',
      brightGreen: '#69ff94',
      brightYellow: '#ffffa5',
      brightBlue: '#d6acff',
      brightMagenta: '#ff92df',
      brightCyan: '#a4ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Dracula+',
    name: 'Dracula+',
    isPreset: true,
    themeData: {
      foreground: '#f8f8f2',
      background: '#212121',
      cursor: '#eceff4',
      cursorAccent: '#282828',
      selectionBackground: '#f8f8f2',
      selectionForeground: '#545454',
      black: '#21222c',
      red: '#ff5555',
      green: '#50fa7b',
      yellow: '#ffcb6b',
      blue: '#82aaff',
      magenta: '#c792ea',
      cyan: '#8be9fd',
      white: '#f8f8f2',
      brightBlack: '#545454',
      brightRed: '#ff6e6e',
      brightGreen: '#69ff94',
      brightYellow: '#ffcb6b',
      brightBlue: '#d6acff',
      brightMagenta: '#ff92df',
      brightCyan: '#a4ffff',
      brightWhite: '#f8f8f2',
    },
  },
  {
    preset_key: 'duckbones',
    name: 'duckbones',
    isPreset: true,
    themeData: {
      foreground: '#ebefc0',
      background: '#0e101a',
      cursor: '#edf2c2',
      cursorAccent: '#0e101a',
      selectionBackground: '#37382d',
      selectionForeground: '#ebefc0',
      black: '#0e101a',
      red: '#e03600',
      green: '#5dcd97',
      yellow: '#e39500',
      blue: '#00a3cb',
      magenta: '#795ccc',
      cyan: '#00a3cb',
      white: '#ebefc0',
      brightBlack: '#2b2f46',
      brightRed: '#ff4821',
      brightGreen: '#58db9e',
      brightYellow: '#f6a100',
      brightBlue: '#00b4e0',
      brightMagenta: '#b3a1e6',
      brightCyan: '#00b4e0',
      brightWhite: '#b3b692',
    },
  },
  {
    preset_key: 'Duotone_Dark',
    name: 'Duotone Dark',
    isPreset: true,
    themeData: {
      foreground: '#b7a1ff',
      background: '#1f1d27',
      cursor: '#ff9839',
      cursorAccent: '#1f1d27',
      selectionBackground: '#353147',
      selectionForeground: '#b7a2ff',
      black: '#1f1d27',
      red: '#d9393e',
      green: '#2dcd73',
      yellow: '#d9b76e',
      blue: '#ffc284',
      magenta: '#de8d40',
      cyan: '#2488ff',
      white: '#b7a1ff',
      brightBlack: '#353147',
      brightRed: '#d9393e',
      brightGreen: '#2dcd73',
      brightYellow: '#d9b76e',
      brightBlue: '#ffc284',
      brightMagenta: '#de8d40',
      brightCyan: '#2488ff',
      brightWhite: '#eae5ff',
    },
  },
  {
    preset_key: 'Earthsong',
    name: 'Earthsong',
    isPreset: true,
    themeData: {
      foreground: '#e5c7a9',
      background: '#292520',
      cursor: '#f6f7ec',
      cursorAccent: '#292520',
      selectionBackground: '#121418',
      selectionForeground: '#e5c7a9',
      black: '#121418',
      red: '#c94234',
      green: '#85c54c',
      yellow: '#f5ae2e',
      blue: '#1398b9',
      magenta: '#d0633d',
      cyan: '#509552',
      white: '#e5c6aa',
      brightBlack: '#675f54',
      brightRed: '#ff645a',
      brightGreen: '#98e036',
      brightYellow: '#e0d561',
      brightBlue: '#5fdaff',
      brightMagenta: '#ff9269',
      brightCyan: '#84f088',
      brightWhite: '#f6f7ec',
    },
  },
  {
    preset_key: 'electron-highlighter',
    name: 'electron-highlighter',
    isPreset: true,
    themeData: {
      foreground: '#a8b5d1',
      background: '#24283b',
      cursor: '#a8b5d1',
      cursorAccent: '#1a1b26',
      selectionBackground: '#283457',
      selectionForeground: '#a8b5d1',
      black: '#15161e',
      red: '#f7768e',
      green: '#58ffc7',
      yellow: '#ffd9af',
      blue: '#82aaff',
      magenta: '#d2a6ef',
      cyan: '#57f9ff',
      white: '#7c8eac',
      brightBlack: '#506686',
      brightRed: '#f7768e',
      brightGreen: '#58ffc7',
      brightYellow: '#ffd9af',
      brightBlue: '#82aaff',
      brightMagenta: '#d2a6ef',
      brightCyan: '#57f9ff',
      brightWhite: '#c5cee0',
    },
  },
  {
    preset_key: 'Elegant',
    name: 'Elegant',
    isPreset: true,
    themeData: {
      foreground: '#cfd2d6',
      background: '#292b31',
      cursor: '#55bbf9',
      cursorAccent: '#ffffff',
      selectionBackground: '#d5d5d5',
      selectionForeground: '#224281',
      black: '#0c1221',
      red: '#ea335b',
      green: '#95ca9a',
      yellow: '#f7cd94',
      blue: '#93aadd',
      magenta: '#bf94e5',
      cyan: '#8ccaec',
      white: '#ffffff',
      brightBlack: '#575656',
      brightRed: '#ea335b',
      brightGreen: '#95ca9a',
      brightYellow: '#f7cd94',
      brightBlue: '#93aadd',
      brightMagenta: '#bf94e5',
      brightCyan: '#5faae9',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Elemental',
    name: 'Elemental',
    isPreset: true,
    themeData: {
      foreground: '#807a74',
      background: '#22211d',
      cursor: '#facb80',
      cursorAccent: '#161611',
      selectionBackground: '#413829',
      selectionForeground: '#facd77',
      black: '#3c3c30',
      red: '#98290f',
      green: '#479a43',
      yellow: '#7f7111',
      blue: '#497f7d',
      magenta: '#7f4e2f',
      cyan: '#387f58',
      white: '#807974',
      brightBlack: '#555445',
      brightRed: '#e0502a',
      brightGreen: '#61e070',
      brightYellow: '#d69927',
      brightBlue: '#79d9d9',
      brightMagenta: '#cd7c54',
      brightCyan: '#59d599',
      brightWhite: '#fff1e9',
    },
  },
  {
    preset_key: 'Elementary',
    name: 'Elementary',
    isPreset: true,
    themeData: {
      foreground: '#efefef',
      background: '#181818',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#242424',
      red: '#d71c15',
      green: '#5aa513',
      yellow: '#fdb40c',
      blue: '#063b8c',
      magenta: '#e40038',
      cyan: '#2595e1',
      white: '#efefef',
      brightBlack: '#4b4b4b',
      brightRed: '#fc1c18',
      brightGreen: '#6bc219',
      brightYellow: '#fec80e',
      brightBlue: '#0955ff',
      brightMagenta: '#fb0050',
      brightCyan: '#3ea8fc',
      brightWhite: '#8c00ec',
    },
  },
  {
    preset_key: 'Embark',
    name: 'Embark',
    isPreset: true,
    themeData: {
      foreground: '#eeffff',
      background: '#1e1c31',
      cursor: '#a1efd3',
      cursorAccent: '#1e1c31',
      selectionBackground: '#fbfcfc',
      selectionForeground: '#1e1c31',
      black: '#1e1c31',
      red: '#f0719b',
      green: '#a1efd3',
      yellow: '#ffe9aa',
      blue: '#57c7ff',
      magenta: '#c792ea',
      cyan: '#87dfeb',
      white: '#f8f8f2',
      brightBlack: '#585273',
      brightRed: '#f02e6e',
      brightGreen: '#2ce592',
      brightYellow: '#ffb378',
      brightBlue: '#1da0e2',
      brightMagenta: '#a742ea',
      brightCyan: '#63f2f1',
      brightWhite: '#a6b3cc',
    },
  },
  {
    preset_key: 'embers-dark',
    name: 'embers-dark',
    isPreset: true,
    themeData: {
      foreground: '#a39a90',
      background: '#16130f',
      cursor: '#a39a90',
      cursorAccent: '#16130f',
      selectionBackground: '#433b32',
      selectionForeground: '#a39a90',
      black: '#16130f',
      red: '#826d57',
      green: '#57826d',
      yellow: '#6d8257',
      blue: '#6d5782',
      magenta: '#82576d',
      cyan: '#576d82',
      white: '#a39a90',
      brightBlack: '#5a5047',
      brightRed: '#828257',
      brightGreen: '#2c2620',
      brightYellow: '#433b32',
      brightBlue: '#8a8075',
      brightMagenta: '#beb6ae',
      brightCyan: '#825757',
      brightWhite: '#dbd6d1',
    },
  },
  {
    preset_key: 'ENCOM',
    name: 'ENCOM',
    isPreset: true,
    themeData: {
      foreground: '#00a595',
      background: '#000000',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#00a48c',
      selectionForeground: '#3de1c9',
      black: '#000000',
      red: '#9f0000',
      green: '#008b00',
      yellow: '#ffd000',
      blue: '#0081ff',
      magenta: '#bc00ca',
      cyan: '#008b8b',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#ff0000',
      brightGreen: '#00ee00',
      brightYellow: '#ffff00',
      brightBlue: '#0000ff',
      brightMagenta: '#ff00ff',
      brightCyan: '#00cdcd',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Espresso',
    name: 'Espresso',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#323232',
      cursor: '#d6d6d6',
      cursorAccent: '#ffffff',
      selectionBackground: '#5b5b5b',
      selectionForeground: '#ffffff',
      black: '#353535',
      red: '#d25252',
      green: '#a5c261',
      yellow: '#ffc66d',
      blue: '#6c99bb',
      magenta: '#d197d9',
      cyan: '#bed6ff',
      white: '#eeeeec',
      brightBlack: '#535353',
      brightRed: '#f00c0c',
      brightGreen: '#c2e075',
      brightYellow: '#e1e48b',
      brightBlue: '#8ab7d9',
      brightMagenta: '#efb5f7',
      brightCyan: '#dcf4ff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Espresso_Libre',
    name: 'Espresso Libre',
    isPreset: true,
    themeData: {
      foreground: '#b8a898',
      background: '#2a211c',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#c3dcff',
      selectionForeground: '#b8a898',
      black: '#000000',
      red: '#cc0000',
      green: '#1a921c',
      yellow: '#f0e53a',
      blue: '#0066ff',
      magenta: '#c5656b',
      cyan: '#06989a',
      white: '#d3d7cf',
      brightBlack: '#555753',
      brightRed: '#ef2929',
      brightGreen: '#9aff87',
      brightYellow: '#fffb5c',
      brightBlue: '#43a8ed',
      brightMagenta: '#ff818a',
      brightCyan: '#34e2e2',
      brightWhite: '#eeeeec',
    },
  },
  {
    preset_key: 'Everblush',
    name: 'Everblush',
    isPreset: true,
    themeData: {
      foreground: '#dadada',
      background: '#141b1e',
      cursor: '#dadada',
      cursorAccent: '#141b1e',
      selectionBackground: '#141b1e',
      selectionForeground: '#dadada',
      black: '#232a2d',
      red: '#e57474',
      green: '#8ccf7e',
      yellow: '#e5c76b',
      blue: '#67b0e8',
      magenta: '#c47fd5',
      cyan: '#6cbfbf',
      white: '#b3b9b8',
      brightBlack: '#2d3437',
      brightRed: '#ef7e7e',
      brightGreen: '#96d988',
      brightYellow: '#f4d67a',
      brightBlue: '#71baf2',
      brightMagenta: '#ce89df',
      brightCyan: '#67cbe7',
      brightWhite: '#bdc3c2',
    },
  },
  {
    preset_key: 'Everforest_Dark_-_Hard',
    name: 'Everforest Dark - Hard',
    isPreset: true,
    themeData: {
      foreground: '#d3c6aa',
      background: '#1e2326',
      cursor: '#e69875',
      cursorAccent: '#4c3743',
      selectionBackground: '#4c3743',
      selectionForeground: '#d3c6aa',
      black: '#7a8478',
      red: '#e67e80',
      green: '#a7c080',
      yellow: '#dbbc7f',
      blue: '#7fbbb3',
      magenta: '#d699b6',
      cyan: '#83c092',
      white: '#f2efdf',
      brightBlack: '#a6b0a0',
      brightRed: '#f85552',
      brightGreen: '#8da101',
      brightYellow: '#dfa000',
      brightBlue: '#3a94c5',
      brightMagenta: '#df69ba',
      brightCyan: '#35a77c',
      brightWhite: '#fffbef',
    },
  },
  {
    preset_key: 'Fahrenheit',
    name: 'Fahrenheit',
    isPreset: true,
    themeData: {
      foreground: '#ffffce',
      background: '#000000',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#4e739f',
      selectionForeground: '#ffffce',
      black: '#1d1d1d',
      red: '#cda074',
      green: '#9e744d',
      yellow: '#fecf75',
      blue: '#720102',
      magenta: '#734c4d',
      cyan: '#979797',
      white: '#ffffce',
      brightBlack: '#000000',
      brightRed: '#fecea0',
      brightGreen: '#cc734d',
      brightYellow: '#fd9f4d',
      brightBlue: '#cb4a05',
      brightMagenta: '#4e739f',
      brightCyan: '#fed04d',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Fairyfloss',
    name: 'Fairyfloss',
    isPreset: true,
    themeData: {
      foreground: '#f8f8f2',
      background: '#5a5475',
      cursor: '#f8f8f0',
      cursorAccent: '#060709',
      selectionBackground: '#8077a8',
      selectionForeground: '#f6e1ce',
      black: '#040303',
      red: '#f92672',
      green: '#c2ffdf',
      yellow: '#e6c000',
      blue: '#c2ffdf',
      magenta: '#ffb8d1',
      cyan: '#c5a3ff',
      white: '#f8f8f0',
      brightBlack: '#6090cb',
      brightRed: '#ff857f',
      brightGreen: '#c2ffdf',
      brightYellow: '#ffea00',
      brightBlue: '#c2ffdf',
      brightMagenta: '#ffb8d1',
      brightCyan: '#c5a3ff',
      brightWhite: '#f8f8f0',
    },
  },
  {
    preset_key: 'farmhouse-dark',
    name: 'farmhouse-dark',
    isPreset: true,
    themeData: {
      foreground: '#e8e4e1',
      background: '#1d2027',
      cursor: '#006efe',
      cursorAccent: '#e8e4e1',
      selectionBackground: '#4d5658',
      selectionForeground: '#b3b1aa',
      black: '#1d2027',
      red: '#ba0004',
      green: '#549d00',
      yellow: '#c87300',
      blue: '#0049e6',
      magenta: '#9f1b61',
      cyan: '#1fb65c',
      white: '#e8e4e1',
      brightBlack: '#394047',
      brightRed: '#eb0009',
      brightGreen: '#7ac100',
      brightYellow: '#ea9a00',
      brightBlue: '#006efe',
      brightMagenta: '#bf3b7f',
      brightCyan: '#19e062',
      brightWhite: '#f4eef0',
    },
  },
  {
    preset_key: 'farmhouse-light',
    name: 'farmhouse-light',
    isPreset: true,
    themeData: {
      foreground: '#1d2027',
      background: '#e8e4e1',
      cursor: '#006efe',
      cursorAccent: '#1d2027',
      selectionBackground: '#b3b1aa',
      selectionForeground: '#4d5658',
      black: '#1d2027',
      red: '#8d0003',
      green: '#3a7d00',
      yellow: '#a95600',
      blue: '#092ccd',
      magenta: '#820046',
      cyan: '#229256',
      white: '#e8e4e1',
      brightBlack: '#394047',
      brightRed: '#eb0009',
      brightGreen: '#7ac100',
      brightYellow: '#ea9a00',
      brightBlue: '#006efe',
      brightMagenta: '#bf3b7f',
      brightCyan: '#19e062',
      brightWhite: '#f4eef0',
    },
  },
  {
    preset_key: 'Fideloper',
    name: 'Fideloper',
    isPreset: true,
    themeData: {
      foreground: '#dbdae0',
      background: '#292f33',
      cursor: '#d4605a',
      cursorAccent: '#fefff2',
      selectionBackground: '#efb8ac',
      selectionForeground: '#ffffff',
      black: '#292f33',
      red: '#cb1e2d',
      green: '#edb8ac',
      yellow: '#b7ab9b',
      blue: '#2e78c2',
      magenta: '#c0236f',
      cyan: '#309186',
      white: '#eae3ce',
      brightBlack: '#092028',
      brightRed: '#d4605a',
      brightGreen: '#d4605a',
      brightYellow: '#a86671',
      brightBlue: '#7c85c4',
      brightMagenta: '#5c5db2',
      brightCyan: '#819090',
      brightWhite: '#fcf4df',
    },
  },
  {
    preset_key: 'Firefly_Traditional',
    name: 'Firefly Traditional',
    isPreset: true,
    themeData: {
      foreground: '#f5f5f5',
      background: '#000000',
      cursor: '#00f900',
      cursorAccent: '#ffffff',
      selectionBackground: '#cfeac6',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#c23720',
      green: '#33bc26',
      yellow: '#afad24',
      blue: '#5a63ff',
      magenta: '#d53ad2',
      cyan: '#33bbc7',
      white: '#cccccc',
      brightBlack: '#828282',
      brightRed: '#ff3b1e',
      brightGreen: '#2ee720',
      brightYellow: '#ecec16',
      brightBlue: '#838dff',
      brightMagenta: '#ff5cfe',
      brightCyan: '#29f0f0',
      brightWhite: '#ebebeb',
    },
  },
  {
    preset_key: 'FirefoxDev',
    name: 'FirefoxDev',
    isPreset: true,
    themeData: {
      foreground: '#7c8fa4',
      background: '#0e1011',
      cursor: '#708284',
      cursorAccent: '#002831',
      selectionBackground: '#163c61',
      selectionForeground: '#f2f5f9',
      black: '#002831',
      red: '#e63853',
      green: '#5eb83c',
      yellow: '#a57706',
      blue: '#359ddf',
      magenta: '#d75cff',
      cyan: '#4b73a2',
      white: '#dcdcdc',
      brightBlack: '#001e27',
      brightRed: '#e1003f',
      brightGreen: '#1d9000',
      brightYellow: '#cd9409',
      brightBlue: '#006fc0',
      brightMagenta: '#a200da',
      brightCyan: '#005794',
      brightWhite: '#e2e2e2',
    },
  },
  {
    preset_key: 'Firewatch',
    name: 'Firewatch',
    isPreset: true,
    themeData: {
      foreground: '#9ba2b2',
      background: '#1e2027',
      cursor: '#f6f7ec',
      cursorAccent: '#c4c5b5',
      selectionBackground: '#2f363e',
      selectionForeground: '#7d8fa4',
      black: '#585f6d',
      red: '#d95360',
      green: '#5ab977',
      yellow: '#dfb563',
      blue: '#4d89c4',
      magenta: '#d55119',
      cyan: '#44a8b6',
      white: '#e6e5ff',
      brightBlack: '#585f6d',
      brightRed: '#d95360',
      brightGreen: '#5ab977',
      brightYellow: '#dfb563',
      brightBlue: '#4c89c5',
      brightMagenta: '#d55119',
      brightCyan: '#44a8b6',
      brightWhite: '#e6e5ff',
    },
  },
  {
    preset_key: 'FishTank',
    name: 'FishTank',
    isPreset: true,
    themeData: {
      foreground: '#ecf0fe',
      background: '#232537',
      cursor: '#fecd5e',
      cursorAccent: '#232537',
      selectionBackground: '#fcf7e9',
      selectionForeground: '#232537',
      black: '#03073c',
      red: '#c6004a',
      green: '#acf157',
      yellow: '#fecd5e',
      blue: '#525fb8',
      magenta: '#986f82',
      cyan: '#968763',
      white: '#ecf0fc',
      brightBlack: '#6c5b30',
      brightRed: '#da4b8a',
      brightGreen: '#dbffa9',
      brightYellow: '#fee6a9',
      brightBlue: '#b2befa',
      brightMagenta: '#fda5cd',
      brightCyan: '#a5bd86',
      brightWhite: '#f6ffec',
    },
  },
  {
    preset_key: 'Flat',
    name: 'Flat',
    isPreset: true,
    themeData: {
      foreground: '#2cc55d',
      background: '#002240',
      cursor: '#e5be0c',
      cursorAccent: '#ffffff',
      selectionBackground: '#792b9c',
      selectionForeground: '#ffffff',
      black: '#222d3f',
      red: '#a82320',
      green: '#32a548',
      yellow: '#e58d11',
      blue: '#3167ac',
      magenta: '#781aa0',
      cyan: '#2c9370',
      white: '#b0b6ba',
      brightBlack: '#212c3c',
      brightRed: '#d4312e',
      brightGreen: '#2d9440',
      brightYellow: '#e5be0c',
      brightBlue: '#3c7dd2',
      brightMagenta: '#8230a7',
      brightCyan: '#35b387',
      brightWhite: '#e7eced',
    },
  },
  {
    preset_key: 'Flatland',
    name: 'Flatland',
    isPreset: true,
    themeData: {
      foreground: '#b8dbef',
      background: '#1d1f21',
      cursor: '#708284',
      cursorAccent: '#002831',
      selectionBackground: '#2b2a24',
      selectionForeground: '#ffffff',
      black: '#1d1d19',
      red: '#f18339',
      green: '#9fd364',
      yellow: '#f4ef6d',
      blue: '#5096be',
      magenta: '#695abc',
      cyan: '#d63865',
      white: '#ffffff',
      brightBlack: '#1d1d19',
      brightRed: '#d22a24',
      brightGreen: '#a7d42c',
      brightYellow: '#ff8949',
      brightBlue: '#61b9d0',
      brightMagenta: '#695abc',
      brightCyan: '#d63865',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'flexoki-dark',
    name: 'flexoki-dark',
    isPreset: true,
    themeData: {
      foreground: '#cecdc3',
      background: '#100f0f',
      cursor: '#cecdc3',
      cursorAccent: '#100f0f',
      selectionBackground: '#cecdc3',
      selectionForeground: '#100f0f',
      black: '#100f0f',
      red: '#d14d41',
      green: '#879a39',
      yellow: '#d0a215',
      blue: '#4385be',
      magenta: '#ce5d97',
      cyan: '#3aa99f',
      white: '#878580',
      brightBlack: '#575653',
      brightRed: '#af3029',
      brightGreen: '#66800b',
      brightYellow: '#ad8301',
      brightBlue: '#205ea6',
      brightMagenta: '#a02f6f',
      brightCyan: '#24837b',
      brightWhite: '#cecdc3',
    },
  },
  {
    preset_key: 'flexoki-light',
    name: 'flexoki-light',
    isPreset: true,
    themeData: {
      foreground: '#100f0f',
      background: '#fffcf0',
      cursor: '#100f0f',
      cursorAccent: '#fffcf0',
      selectionBackground: '#6f6e69',
      selectionForeground: '#fffcf0',
      black: '#100f0f',
      red: '#af3029',
      green: '#66800b',
      yellow: '#ad8301',
      blue: '#205ea6',
      magenta: '#a02f6f',
      cyan: '#24837b',
      white: '#6f6e69',
      brightBlack: '#b7b5ac',
      brightRed: '#d14d41',
      brightGreen: '#879a39',
      brightYellow: '#d0a215',
      brightBlue: '#4385be',
      brightMagenta: '#ce5d97',
      brightCyan: '#3aa99f',
      brightWhite: '#cecdc3',
    },
  },
  {
    preset_key: 'Floraverse',
    name: 'Floraverse',
    isPreset: true,
    themeData: {
      foreground: '#dbd1b9',
      background: '#0e0d15',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#f3e0b8',
      selectionForeground: '#08002e',
      black: '#08002e',
      red: '#64002c',
      green: '#5d731a',
      yellow: '#cd751c',
      blue: '#1d6da1',
      magenta: '#b7077e',
      cyan: '#42a38c',
      white: '#f3e0b8',
      brightBlack: '#331e4d',
      brightRed: '#d02063',
      brightGreen: '#b4ce59',
      brightYellow: '#fac357',
      brightBlue: '#40a4cf',
      brightMagenta: '#f12aae',
      brightCyan: '#62caa8',
      brightWhite: '#fff5db',
    },
  },
  {
    preset_key: 'ForestBlue',
    name: 'ForestBlue',
    isPreset: true,
    themeData: {
      foreground: '#e2d8cd',
      background: '#051519',
      cursor: '#9e9ecb',
      cursorAccent: '#000000',
      selectionBackground: '#4d4d4d',
      selectionForeground: '#ffffff',
      black: '#333333',
      red: '#f8818e',
      green: '#92d3a2',
      yellow: '#1a8e63',
      blue: '#8ed0ce',
      magenta: '#5e468c',
      cyan: '#31658c',
      white: '#e2d8cd',
      brightBlack: '#3d3d3d',
      brightRed: '#fb3d66',
      brightGreen: '#6bb48d',
      brightYellow: '#30c85a',
      brightBlue: '#39a7a2',
      brightMagenta: '#7e62b3',
      brightCyan: '#6096bf',
      brightWhite: '#e2d8cd',
    },
  },
  {
    preset_key: 'Framer',
    name: 'Framer',
    isPreset: true,
    themeData: {
      foreground: '#777777',
      background: '#111111',
      cursor: '#fcdc08',
      cursorAccent: '#161616',
      selectionBackground: '#666666',
      selectionForeground: '#ffffff',
      black: '#141414',
      red: '#ff5555',
      green: '#98ec65',
      yellow: '#ffcc33',
      blue: '#00aaff',
      magenta: '#aa88ff',
      cyan: '#88ddff',
      white: '#cccccc',
      brightBlack: '#414141',
      brightRed: '#ff8888',
      brightGreen: '#b6f292',
      brightYellow: '#ffd966',
      brightBlue: '#33bbff',
      brightMagenta: '#cebbff',
      brightCyan: '#bbecff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'FrontEndDelight',
    name: 'FrontEndDelight',
    isPreset: true,
    themeData: {
      foreground: '#adadad',
      background: '#1b1c1d',
      cursor: '#cdcdcd',
      cursorAccent: '#1b1c1d',
      selectionBackground: '#ea6154',
      selectionForeground: '#1b1c1d',
      black: '#242526',
      red: '#f8511b',
      green: '#565747',
      yellow: '#fa771d',
      blue: '#2c70b7',
      magenta: '#f02e4f',
      cyan: '#3ca1a6',
      white: '#adadad',
      brightBlack: '#5fac6d',
      brightRed: '#f74319',
      brightGreen: '#74ec4c',
      brightYellow: '#fdc325',
      brightBlue: '#3393ca',
      brightMagenta: '#e75e4f',
      brightCyan: '#4fbce6',
      brightWhite: '#8c735b',
    },
  },
  {
    preset_key: 'FunForrest',
    name: 'FunForrest',
    isPreset: true,
    themeData: {
      foreground: '#dec165',
      background: '#251200',
      cursor: '#e5591c',
      cursorAccent: '#000000',
      selectionBackground: '#e5591c',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#d6262b',
      green: '#919c00',
      yellow: '#be8a13',
      blue: '#4699a3',
      magenta: '#8d4331',
      cyan: '#da8213',
      white: '#ddc265',
      brightBlack: '#7f6a55',
      brightRed: '#e55a1c',
      brightGreen: '#bfc65a',
      brightYellow: '#ffcb1b',
      brightBlue: '#7cc9cf',
      brightMagenta: '#d26349',
      brightCyan: '#e6a96b',
      brightWhite: '#ffeaa3',
    },
  },
  {
    preset_key: 'Galaxy',
    name: 'Galaxy',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#1d2837',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#f9555f',
      green: '#21b089',
      yellow: '#fef02a',
      blue: '#589df6',
      magenta: '#944d95',
      cyan: '#1f9ee7',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#fa8c8f',
      brightGreen: '#35bb9a',
      brightYellow: '#ffff55',
      brightBlue: '#589df6',
      brightMagenta: '#e75699',
      brightCyan: '#3979bc',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Galizur',
    name: 'Galizur',
    isPreset: true,
    themeData: {
      foreground: '#ddeeff',
      background: '#071317',
      cursor: '#ddeeff',
      cursorAccent: '#071317',
      selectionBackground: '#071317',
      selectionForeground: '#ddeeff',
      black: '#223344',
      red: '#aa1122',
      green: '#33aa11',
      yellow: '#ccaa22',
      blue: '#2255cc',
      magenta: '#7755aa',
      cyan: '#22bbdd',
      white: '#8899aa',
      brightBlack: '#556677',
      brightRed: '#ff1133',
      brightGreen: '#33ff11',
      brightYellow: '#ffdd33',
      brightBlue: '#3377ff',
      brightMagenta: '#aa77ff',
      brightCyan: '#33ddff',
      brightWhite: '#bbccdd',
    },
  },
  {
    preset_key: 'Ghostty_Default_StyleDark',
    name: 'Ghostty Default StyleDark',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#292c33',
      cursor: '#ffffff',
      cursorAccent: '#363a43',
      selectionBackground: '#ffffff',
      selectionForeground: '#292c33',
      black: '#1d1f21',
      red: '#bf6b69',
      green: '#b7bd73',
      yellow: '#e9c880',
      blue: '#88a1bb',
      magenta: '#ad95b8',
      cyan: '#95bdb7',
      white: '#c5c8c6',
      brightBlack: '#666666',
      brightRed: '#c55757',
      brightGreen: '#bcc95f',
      brightYellow: '#e1c65e',
      brightBlue: '#83a5d6',
      brightMagenta: '#bc99d4',
      brightCyan: '#83beb1',
      brightWhite: '#eaeaea',
    },
  },
  {
    preset_key: 'Github',
    name: 'Github',
    isPreset: true,
    themeData: {
      foreground: '#3e3e3e',
      background: '#f4f4f4',
      cursor: '#3f3f3f',
      cursorAccent: '#f4f4f4',
      selectionBackground: '#a9c1e2',
      selectionForeground: '#535353',
      black: '#3e3e3e',
      red: '#970b16',
      green: '#07962a',
      yellow: '#f8eec7',
      blue: '#003e8a',
      magenta: '#e94691',
      cyan: '#89d1ec',
      white: '#ffffff',
      brightBlack: '#666666',
      brightRed: '#de0000',
      brightGreen: '#87d5a2',
      brightYellow: '#f1d007',
      brightBlue: '#2e6cba',
      brightMagenta: '#ffa29f',
      brightCyan: '#1cfafe',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'GitHub_Dark',
    name: 'GitHub Dark',
    isPreset: true,
    themeData: {
      foreground: '#8b949e',
      background: '#101216',
      cursor: '#c9d1d9',
      cursorAccent: '#101216',
      selectionBackground: '#3b5070',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#f78166',
      green: '#56d364',
      yellow: '#e3b341',
      blue: '#6ca4f8',
      magenta: '#db61a2',
      cyan: '#2b7489',
      white: '#ffffff',
      brightBlack: '#4d4d4d',
      brightRed: '#f78166',
      brightGreen: '#56d364',
      brightYellow: '#e3b341',
      brightBlue: '#6ca4f8',
      brightMagenta: '#db61a2',
      brightCyan: '#2b7489',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'GitHub-Dark-Colorblind',
    name: 'GitHub-Dark-Colorblind',
    isPreset: true,
    themeData: {
      foreground: '#c9d1d9',
      background: '#0d1117',
      cursor: '#58a6ff',
      cursorAccent: '#58a6ff',
      selectionBackground: '#c9d1d9',
      selectionForeground: '#0d1117',
      black: '#484f58',
      red: '#ec8e2c',
      green: '#58a6ff',
      yellow: '#d29922',
      blue: '#58a6ff',
      magenta: '#bc8cff',
      cyan: '#39c5cf',
      white: '#b1bac4',
      brightBlack: '#6e7681',
      brightRed: '#fdac54',
      brightGreen: '#79c0ff',
      brightYellow: '#e3b341',
      brightBlue: '#79c0ff',
      brightMagenta: '#d2a8ff',
      brightCyan: '#56d4dd',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'GitHub-Dark-Default',
    name: 'GitHub-Dark-Default',
    isPreset: true,
    themeData: {
      foreground: '#e6edf3',
      background: '#0d1117',
      cursor: '#2f81f7',
      cursorAccent: '#2f81f7',
      selectionBackground: '#e6edf3',
      selectionForeground: '#0d1117',
      black: '#484f58',
      red: '#ff7b72',
      green: '#3fb950',
      yellow: '#d29922',
      blue: '#58a6ff',
      magenta: '#bc8cff',
      cyan: '#39c5cf',
      white: '#b1bac4',
      brightBlack: '#6e7681',
      brightRed: '#ffa198',
      brightGreen: '#56d364',
      brightYellow: '#e3b341',
      brightBlue: '#79c0ff',
      brightMagenta: '#d2a8ff',
      brightCyan: '#56d4dd',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'GitHub-Dark-Dimmed',
    name: 'GitHub-Dark-Dimmed',
    isPreset: true,
    themeData: {
      foreground: '#adbac7',
      background: '#22272e',
      cursor: '#539bf5',
      cursorAccent: '#539bf5',
      selectionBackground: '#adbac7',
      selectionForeground: '#22272e',
      black: '#545d68',
      red: '#f47067',
      green: '#57ab5a',
      yellow: '#c69026',
      blue: '#539bf5',
      magenta: '#b083f0',
      cyan: '#39c5cf',
      white: '#909dab',
      brightBlack: '#636e7b',
      brightRed: '#ff938a',
      brightGreen: '#6bc46d',
      brightYellow: '#daaa3f',
      brightBlue: '#6cb6ff',
      brightMagenta: '#dcbdfb',
      brightCyan: '#56d4dd',
      brightWhite: '#cdd9e5',
    },
  },
  {
    preset_key: 'GitHub-Dark-High-Contrast',
    name: 'GitHub-Dark-High-Contrast',
    isPreset: true,
    themeData: {
      foreground: '#f0f3f6',
      background: '#0a0c10',
      cursor: '#71b7ff',
      cursorAccent: '#71b7ff',
      selectionBackground: '#f0f3f6',
      selectionForeground: '#0a0c10',
      black: '#7a828e',
      red: '#ff9492',
      green: '#26cd4d',
      yellow: '#f0b72f',
      blue: '#71b7ff',
      magenta: '#cb9eff',
      cyan: '#39c5cf',
      white: '#d9dee3',
      brightBlack: '#9ea7b3',
      brightRed: '#ffb1af',
      brightGreen: '#4ae168',
      brightYellow: '#f7c843',
      brightBlue: '#91cbff',
      brightMagenta: '#dbb7ff',
      brightCyan: '#56d4dd',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'GitHub-Light-Colorblind',
    name: 'GitHub-Light-Colorblind',
    isPreset: true,
    themeData: {
      foreground: '#24292f',
      background: '#ffffff',
      cursor: '#0969da',
      cursorAccent: '#0969da',
      selectionBackground: '#24292f',
      selectionForeground: '#ffffff',
      black: '#24292f',
      red: '#b35900',
      green: '#0550ae',
      yellow: '#4d2d00',
      blue: '#0969da',
      magenta: '#8250df',
      cyan: '#1b7c83',
      white: '#6e7781',
      brightBlack: '#57606a',
      brightRed: '#8a4600',
      brightGreen: '#0969da',
      brightYellow: '#633c01',
      brightBlue: '#218bff',
      brightMagenta: '#a475f9',
      brightCyan: '#3192aa',
      brightWhite: '#8c959f',
    },
  },
  {
    preset_key: 'GitHub-Light-Default',
    name: 'GitHub-Light-Default',
    isPreset: true,
    themeData: {
      foreground: '#1f2328',
      background: '#ffffff',
      cursor: '#0969da',
      cursorAccent: '#0969da',
      selectionBackground: '#1f2328',
      selectionForeground: '#ffffff',
      black: '#24292f',
      red: '#cf222e',
      green: '#116329',
      yellow: '#4d2d00',
      blue: '#0969da',
      magenta: '#8250df',
      cyan: '#1b7c83',
      white: '#6e7781',
      brightBlack: '#57606a',
      brightRed: '#a40e26',
      brightGreen: '#1a7f37',
      brightYellow: '#633c01',
      brightBlue: '#218bff',
      brightMagenta: '#a475f9',
      brightCyan: '#3192aa',
      brightWhite: '#8c959f',
    },
  },
  {
    preset_key: 'GitHub-Light-High-Contrast',
    name: 'GitHub-Light-High-Contrast',
    isPreset: true,
    themeData: {
      foreground: '#0e1116',
      background: '#ffffff',
      cursor: '#0349b4',
      cursorAccent: '#0349b4',
      selectionBackground: '#0e1116',
      selectionForeground: '#ffffff',
      black: '#0e1116',
      red: '#a0111f',
      green: '#024c1a',
      yellow: '#3f2200',
      blue: '#0349b4',
      magenta: '#622cbc',
      cyan: '#1b7c83',
      white: '#66707b',
      brightBlack: '#4b535d',
      brightRed: '#86061d',
      brightGreen: '#055d20',
      brightYellow: '#4e2c00',
      brightBlue: '#1168e3',
      brightMagenta: '#844ae7',
      brightCyan: '#3192aa',
      brightWhite: '#88929d',
    },
  },
  {
    preset_key: 'GitLab-Dark',
    name: 'GitLab-Dark',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#28262b',
      cursor: '#ffffff',
      cursorAccent: '#ffffff',
      selectionBackground: '#ad95e9',
      selectionForeground: '#28262b',
      black: '#000000',
      red: '#f57f6c',
      green: '#52b87a',
      yellow: '#d99530',
      blue: '#7fb6ed',
      magenta: '#f88aaf',
      cyan: '#32c5d2',
      white: '#ffffff',
      brightBlack: '#666666',
      brightRed: '#fcb5aa',
      brightGreen: '#91d4a8',
      brightYellow: '#e9be74',
      brightBlue: '#498dd1',
      brightMagenta: '#fcacc5',
      brightCyan: '#5edee3',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'GitLab-Dark-Grey',
    name: 'GitLab-Dark-Grey',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#222222',
      cursor: '#ffffff',
      cursorAccent: '#ffffff',
      selectionBackground: '#ad95e9',
      selectionForeground: '#222222',
      black: '#000000',
      red: '#f57f6c',
      green: '#52b87a',
      yellow: '#d99530',
      blue: '#7fb6ed',
      magenta: '#f88aaf',
      cyan: '#32c5d2',
      white: '#ffffff',
      brightBlack: '#666666',
      brightRed: '#fcb5aa',
      brightGreen: '#91d4a8',
      brightYellow: '#e9be74',
      brightBlue: '#498dd1',
      brightMagenta: '#fcacc5',
      brightCyan: '#5edee3',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'GitLab-Light',
    name: 'GitLab-Light',
    isPreset: true,
    themeData: {
      foreground: '#303030',
      background: '#fafaff',
      cursor: '#303030',
      cursorAccent: '#303030',
      selectionBackground: '#ad95e9',
      selectionForeground: '#fafaff',
      black: '#303030',
      red: '#a31700',
      green: '#0a7f3d',
      yellow: '#af551d',
      blue: '#006cd8',
      magenta: '#583cac',
      cyan: '#00798a',
      white: '#303030',
      brightBlack: '#303030',
      brightRed: '#a31700',
      brightGreen: '#0a7f3d',
      brightYellow: '#af551d',
      brightBlue: '#006cd8',
      brightMagenta: '#583cac',
      brightCyan: '#00798a',
      brightWhite: '#303030',
    },
  },
  {
    preset_key: 'Glacier',
    name: 'Glacier',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#0c1115',
      cursor: '#6c6c6c',
      cursorAccent: '#6c6c6c',
      selectionBackground: '#bd2523',
      selectionForeground: '#ffffff',
      black: '#2e343c',
      red: '#bd0f2f',
      green: '#35a770',
      yellow: '#fb9435',
      blue: '#1f5872',
      magenta: '#bd2523',
      cyan: '#778397',
      white: '#ffffff',
      brightBlack: '#404a55',
      brightRed: '#bd0f2f',
      brightGreen: '#49e998',
      brightYellow: '#fddf6e',
      brightBlue: '#2a8bc1',
      brightMagenta: '#ea4727',
      brightCyan: '#a0b6d3',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Grape',
    name: 'Grape',
    isPreset: true,
    themeData: {
      foreground: '#9f9fa1',
      background: '#171423',
      cursor: '#a288f7',
      cursorAccent: '#171422',
      selectionBackground: '#493d70',
      selectionForeground: '#171422',
      black: '#2d283f',
      red: '#ed2261',
      green: '#1fa91b',
      yellow: '#8ddc20',
      blue: '#487df4',
      magenta: '#8d35c9',
      cyan: '#3bdeed',
      white: '#9e9ea0',
      brightBlack: '#59516a',
      brightRed: '#f0729a',
      brightGreen: '#53aa5e',
      brightYellow: '#b2dc87',
      brightBlue: '#a9bcec',
      brightMagenta: '#ad81c2',
      brightCyan: '#9de3eb',
      brightWhite: '#a288f7',
    },
  },
  {
    preset_key: 'Grass',
    name: 'Grass',
    isPreset: true,
    themeData: {
      foreground: '#fff0a5',
      background: '#13773d',
      cursor: '#8c2800',
      cursorAccent: '#ffffff',
      selectionBackground: '#b64926',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#bb0000',
      green: '#00bb00',
      yellow: '#e7b000',
      blue: '#0000a3',
      magenta: '#950062',
      cyan: '#00bbbb',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#bb0000',
      brightGreen: '#00bb00',
      brightYellow: '#e7b000',
      brightBlue: '#0000bb',
      brightMagenta: '#ff55ff',
      brightCyan: '#55ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Grey-green',
    name: 'Grey-green',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#002a1a',
      cursor: '#fff400',
      cursorAccent: '#e1e4e3',
      selectionBackground: '#517e50',
      selectionForeground: '#e2e2e2',
      black: '#000000',
      red: '#fe1414',
      green: '#74ff00',
      yellow: '#f1ff01',
      blue: '#00deff',
      magenta: '#ff00f0',
      cyan: '#00ffbc',
      white: '#ffffff',
      brightBlack: '#666666',
      brightRed: '#ff3939',
      brightGreen: '#00ff44',
      brightYellow: '#ffd100',
      brightBlue: '#00afff',
      brightMagenta: '#ff008a',
      brightCyan: '#00ffd3',
      brightWhite: '#f5ecec',
    },
  },
  {
    preset_key: 'gruber-darker',
    name: 'gruber-darker',
    isPreset: true,
    themeData: {
      foreground: '#e4e4e4',
      background: '#181818',
      cursor: '#ffdd33',
      cursorAccent: '#ffffff',
      selectionBackground: '#ffffff',
      selectionForeground: '#52494e',
      black: '#181818',
      red: '#f43841',
      green: '#73d936',
      yellow: '#ffdd33',
      blue: '#96a6c8',
      magenta: '#9e95c7',
      cyan: '#95a99f',
      white: '#e4e4e4',
      brightBlack: '#52494e',
      brightRed: '#ff4f58',
      brightGreen: '#73d936',
      brightYellow: '#ffdd33',
      brightBlue: '#96a6c8',
      brightMagenta: '#afafd7',
      brightCyan: '#95a99f',
      brightWhite: '#f5f5f5',
    },
  },
  {
    preset_key: 'gruvbox-material',
    name: 'gruvbox-material',
    isPreset: true,
    themeData: {
      foreground: '#d4be98',
      background: '#1d2021',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#2b2c3f',
      selectionForeground: '#7cfb70',
      black: '#141617',
      red: '#ea6926',
      green: '#c1d041',
      yellow: '#eecf75',
      blue: '#6da3ec',
      magenta: '#fd9bc1',
      cyan: '#fe9d6e',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#d3573b',
      brightGreen: '#c1d041',
      brightYellow: '#eecf75',
      brightBlue: '#2c86ff',
      brightMagenta: '#fd9bc1',
      brightCyan: '#92a5df',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'GruvboxDark',
    name: 'GruvboxDark',
    isPreset: true,
    themeData: {
      foreground: '#ebdbb2',
      background: '#282828',
      cursor: '#ebdbb2',
      cursorAccent: '#282828',
      selectionBackground: '#665c54',
      selectionForeground: '#ebdbb2',
      black: '#282828',
      red: '#cc241d',
      green: '#98971a',
      yellow: '#d79921',
      blue: '#458588',
      magenta: '#b16286',
      cyan: '#689d6a',
      white: '#a89984',
      brightBlack: '#928374',
      brightRed: '#fb4934',
      brightGreen: '#b8bb26',
      brightYellow: '#fabd2f',
      brightBlue: '#83a598',
      brightMagenta: '#d3869b',
      brightCyan: '#8ec07c',
      brightWhite: '#ebdbb2',
    },
  },
  {
    preset_key: 'GruvboxDarkHard',
    name: 'GruvboxDarkHard',
    isPreset: true,
    themeData: {
      foreground: '#ebdbb2',
      background: '#1d2021',
      cursor: '#ebdbb2',
      cursorAccent: '#1d2021',
      selectionBackground: '#665c54',
      selectionForeground: '#ebdbb2',
      black: '#1d2021',
      red: '#cc241d',
      green: '#98971a',
      yellow: '#d79921',
      blue: '#458588',
      magenta: '#b16286',
      cyan: '#689d6a',
      white: '#a89984',
      brightBlack: '#928374',
      brightRed: '#fb4934',
      brightGreen: '#b8bb26',
      brightYellow: '#fabd2f',
      brightBlue: '#83a598',
      brightMagenta: '#d3869b',
      brightCyan: '#8ec07c',
      brightWhite: '#ebdbb2',
    },
  },
  {
    preset_key: 'GruvboxLight',
    name: 'GruvboxLight',
    isPreset: true,
    themeData: {
      foreground: '#282828',
      background: '#fbf1c7',
      cursor: '#282828',
      cursorAccent: '#fbf1c7',
      selectionBackground: '#d5c4a1',
      selectionForeground: '#665c54',
      black: '#fbf1c7',
      red: '#9d0006',
      green: '#79740e',
      yellow: '#b57614',
      blue: '#076678',
      magenta: '#8f3f71',
      cyan: '#427b58',
      white: '#3c3836',
      brightBlack: '#9d8374',
      brightRed: '#cc241d',
      brightGreen: '#98971a',
      brightYellow: '#d79921',
      brightBlue: '#458588',
      brightMagenta: '#b16186',
      brightCyan: '#689d69',
      brightWhite: '#7c6f64',
    },
  },
  {
    preset_key: 'GruvboxLightHard',
    name: 'GruvboxLightHard',
    isPreset: true,
    themeData: {
      foreground: '#282828',
      background: '#f8f4d6',
      cursor: '#282828',
      cursorAccent: '#f8f4d6',
      selectionBackground: '#d5c4a1',
      selectionForeground: '#665c54',
      black: '#f8f4d6',
      red: '#9d0006',
      green: '#79740e',
      yellow: '#b57614',
      blue: '#076678',
      magenta: '#8f3f71',
      cyan: '#427b58',
      white: '#3c3836',
      brightBlack: '#9d8374',
      brightRed: '#cc241d',
      brightGreen: '#98971a',
      brightYellow: '#d79921',
      brightBlue: '#458588',
      brightMagenta: '#b16186',
      brightCyan: '#689d69',
      brightWhite: '#7c6f64',
    },
  },
  {
    preset_key: 'Guezwhoz',
    name: 'Guezwhoz',
    isPreset: true,
    themeData: {
      foreground: '#d0d0d0',
      background: '#1c1c1c',
      cursor: '#eeeeee',
      cursorAccent: '#eeeeee',
      selectionBackground: '#005f5f',
      selectionForeground: '#eeeeee',
      black: '#080808',
      red: '#ff5f5f',
      green: '#87d7af',
      yellow: '#d7d787',
      blue: '#5fafd7',
      magenta: '#afafff',
      cyan: '#5fd7d7',
      white: '#dadada',
      brightBlack: '#8a8a8a',
      brightRed: '#d75f5f',
      brightGreen: '#afd7af',
      brightYellow: '#d7d7af',
      brightBlue: '#87afd7',
      brightMagenta: '#afafd7',
      brightCyan: '#87d7d7',
      brightWhite: '#dadada',
    },
  },
  {
    preset_key: 'Hacktober',
    name: 'Hacktober',
    isPreset: true,
    themeData: {
      foreground: '#c9c9c9',
      background: '#141414',
      cursor: '#c9c9c9',
      cursorAccent: '#141414',
      selectionBackground: '#141414',
      selectionForeground: '#c9c9c9',
      black: '#191918',
      red: '#b34538',
      green: '#587744',
      yellow: '#d08949',
      blue: '#206ec5',
      magenta: '#864651',
      cyan: '#ac9166',
      white: '#f1eee7',
      brightBlack: '#2c2b2a',
      brightRed: '#b33323',
      brightGreen: '#42824a',
      brightYellow: '#c75a22',
      brightBlue: '#5389c5',
      brightMagenta: '#e795a5',
      brightCyan: '#ebc587',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Hardcore',
    name: 'Hardcore',
    isPreset: true,
    themeData: {
      foreground: '#a0a0a0',
      background: '#121212',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#453b39',
      selectionForeground: '#b6bbc0',
      black: '#1b1d1e',
      red: '#f92672',
      green: '#a6e22e',
      yellow: '#fd971f',
      blue: '#66d9ef',
      magenta: '#9e6ffe',
      cyan: '#5e7175',
      white: '#ccccc6',
      brightBlack: '#505354',
      brightRed: '#ff669d',
      brightGreen: '#beed5f',
      brightYellow: '#e6db74',
      brightBlue: '#66d9ef',
      brightMagenta: '#9e6ffe',
      brightCyan: '#a3babf',
      brightWhite: '#f8f8f2',
    },
  },
  {
    preset_key: 'Harper',
    name: 'Harper',
    isPreset: true,
    themeData: {
      foreground: '#a8a49d',
      background: '#010101',
      cursor: '#a8a49d',
      cursorAccent: '#010101',
      selectionBackground: '#5a5753',
      selectionForeground: '#a8a49d',
      black: '#010101',
      red: '#f8b63f',
      green: '#7fb5e1',
      yellow: '#d6da25',
      blue: '#489e48',
      magenta: '#b296c6',
      cyan: '#f5bfd7',
      white: '#a8a49d',
      brightBlack: '#726e6a',
      brightRed: '#f8b63f',
      brightGreen: '#7fb5e1',
      brightYellow: '#d6da25',
      brightBlue: '#489e48',
      brightMagenta: '#b296c6',
      brightCyan: '#f5bfd7',
      brightWhite: '#fefbea',
    },
  },
  {
    preset_key: 'Havn_Daggry',
    name: 'Havn Daggry',
    isPreset: true,
    themeData: {
      foreground: '#3e4a77',
      background: '#f8f9fb',
      cursor: '#386a51',
      cursorAccent: '#d7dbea',
      selectionBackground: '#d4e8de',
      selectionForeground: '#333c61',
      black: '#212840',
      red: '#8f564b',
      green: '#5c705b',
      yellow: '#b36f00',
      blue: '#40567a',
      magenta: '#775d93',
      cyan: '#8a5a7e',
      white: '#d7dbea',
      brightBlack: '#212840',
      brightRed: '#bd533e',
      brightGreen: '#79957b',
      brightYellow: '#f3b550',
      brightBlue: '#6988bc',
      brightMagenta: '#7b7393',
      brightCyan: '#a4879c',
      brightWhite: '#d7dbea',
    },
  },
  {
    preset_key: 'Havn_Skumring',
    name: 'Havn Skumring',
    isPreset: true,
    themeData: {
      foreground: '#d7dbea',
      background: '#121521',
      cursor: '#40786f',
      cursorAccent: '#e0e4f2',
      selectionBackground: '#34504b',
      selectionForeground: '#dce0ee',
      black: '#262c45',
      red: '#d96048',
      green: '#7cab7f',
      yellow: '#eeb64e',
      blue: '#5d6bef',
      magenta: '#7a729a',
      cyan: '#ca8cbe',
      white: '#dde0ed',
      brightBlack: '#212840',
      brightRed: '#c47768',
      brightGreen: '#8f9d90',
      brightYellow: '#e4c693',
      brightBlue: '#5d85c6',
      brightMagenta: '#967de7',
      brightCyan: '#c57eb3',
      brightWhite: '#fdf6e3',
    },
  },
  {
    preset_key: 'HaX0R_BLUE',
    name: 'HaX0R_BLUE',
    isPreset: true,
    themeData: {
      foreground: '#11b7ff',
      background: '#010515',
      cursor: '#10b6ff',
      cursorAccent: '#ffffff',
      selectionBackground: '#c1e4ff',
      selectionForeground: '#f6f6f6',
      black: '#010921',
      red: '#10b6ff',
      green: '#10b6ff',
      yellow: '#10b6ff',
      blue: '#10b6ff',
      magenta: '#10b6ff',
      cyan: '#10b6ff',
      white: '#fafafa',
      brightBlack: '#080117',
      brightRed: '#00b3f7',
      brightGreen: '#00b3f7',
      brightYellow: '#00b3f7',
      brightBlue: '#00b3f7',
      brightMagenta: '#00b3f7',
      brightCyan: '#00b3f7',
      brightWhite: '#fefefe',
    },
  },
  {
    preset_key: 'HaX0R_GR33N',
    name: 'HaX0R_GR33N',
    isPreset: true,
    themeData: {
      foreground: '#16b10e',
      background: '#020f01',
      cursor: '#15d00d',
      cursorAccent: '#ffffff',
      selectionBackground: '#d4ffc1',
      selectionForeground: '#fdfdfd',
      black: '#001f0b',
      red: '#15d00d',
      green: '#15d00d',
      yellow: '#15d00d',
      blue: '#15d00d',
      magenta: '#15d00d',
      cyan: '#15d00d',
      white: '#fafafa',
      brightBlack: '#001510',
      brightRed: '#19e20e',
      brightGreen: '#19e20e',
      brightYellow: '#19e20e',
      brightBlue: '#19e20e',
      brightMagenta: '#19e20e',
      brightCyan: '#19e20e',
      brightWhite: '#fefefe',
    },
  },
  {
    preset_key: 'HaX0R_R3D',
    name: 'HaX0R_R3D',
    isPreset: true,
    themeData: {
      foreground: '#b10e0e',
      background: '#200101',
      cursor: '#b00d0d',
      cursorAccent: '#ffffff',
      selectionBackground: '#ebc1ff',
      selectionForeground: '#fdfdfd',
      black: '#1f0000',
      red: '#b00d0d',
      green: '#b00d0d',
      yellow: '#b00d0d',
      blue: '#b00d0d',
      magenta: '#b00d0d',
      cyan: '#b00d0d',
      white: '#fafafa',
      brightBlack: '#150000',
      brightRed: '#ff1111',
      brightGreen: '#ff1010',
      brightYellow: '#ff1010',
      brightBlue: '#ff1010',
      brightMagenta: '#ff1010',
      brightCyan: '#ff1010',
      brightWhite: '#fefefe',
    },
  },
  {
    preset_key: 'heeler',
    name: 'heeler',
    isPreset: true,
    themeData: {
      foreground: '#fdfdfd',
      background: '#211f44',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#2b2c3f',
      selectionForeground: '#7cfb70',
      black: '#000000',
      red: '#d3573b',
      green: '#c1d041',
      yellow: '#eecf75',
      blue: '#6da3ec',
      magenta: '#fd9bc1',
      cyan: '#fe9d6e',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#d3573b',
      brightGreen: '#c1d041',
      brightYellow: '#eecf75',
      brightBlue: '#2c86ff',
      brightMagenta: '#fd9bc1',
      brightCyan: '#92a5df',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Highway',
    name: 'Highway',
    isPreset: true,
    themeData: {
      foreground: '#ededed',
      background: '#222225',
      cursor: '#e0d9b9',
      cursorAccent: '#1f192a',
      selectionBackground: '#384564',
      selectionForeground: '#ededed',
      black: '#000000',
      red: '#d00e18',
      green: '#138034',
      yellow: '#ffcb3e',
      blue: '#006bb3',
      magenta: '#6b2775',
      cyan: '#384564',
      white: '#ededed',
      brightBlack: '#5d504a',
      brightRed: '#f07e18',
      brightGreen: '#b1d130',
      brightYellow: '#fff120',
      brightBlue: '#4fc2fd',
      brightMagenta: '#de0071',
      brightCyan: '#5d504a',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Hipster_Green',
    name: 'Hipster Green',
    isPreset: true,
    themeData: {
      foreground: '#84c138',
      background: '#100b05',
      cursor: '#23ff18',
      cursorAccent: '#ff0018',
      selectionBackground: '#083905',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#b6214a',
      green: '#00a600',
      yellow: '#bfbf00',
      blue: '#246eb2',
      magenta: '#b200b2',
      cyan: '#00a6b2',
      white: '#bfbfbf',
      brightBlack: '#666666',
      brightRed: '#e50000',
      brightGreen: '#86a93e',
      brightYellow: '#e5e500',
      brightBlue: '#0000ff',
      brightMagenta: '#e500e5',
      brightCyan: '#00e5e5',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'Hivacruz',
    name: 'Hivacruz',
    isPreset: true,
    themeData: {
      foreground: '#ede4e4',
      background: '#132638',
      cursor: '#979db4',
      cursorAccent: '#202746',
      selectionBackground: '#5e6687',
      selectionForeground: '#979db4',
      black: '#202746',
      red: '#c94922',
      green: '#ac9739',
      yellow: '#c08b30',
      blue: '#3d8fd1',
      magenta: '#6679cc',
      cyan: '#22a2c9',
      white: '#979db4',
      brightBlack: '#6b7394',
      brightRed: '#c76b29',
      brightGreen: '#73ad43',
      brightYellow: '#5e6687',
      brightBlue: '#898ea4',
      brightMagenta: '#dfe2f1',
      brightCyan: '#9c637a',
      brightWhite: '#f5f7ff',
    },
  },
  {
    preset_key: 'Homebrew',
    name: 'Homebrew',
    isPreset: true,
    themeData: {
      foreground: '#00ff00',
      background: '#000000',
      cursor: '#23ff18',
      cursorAccent: '#ff0018',
      selectionBackground: '#083905',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#990000',
      green: '#00a600',
      yellow: '#999900',
      blue: '#0000b2',
      magenta: '#b200b2',
      cyan: '#00a6b2',
      white: '#bfbfbf',
      brightBlack: '#666666',
      brightRed: '#e50000',
      brightGreen: '#00d900',
      brightYellow: '#e5e500',
      brightBlue: '#0000ff',
      brightMagenta: '#e500e5',
      brightCyan: '#00e5e5',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'Hopscotch',
    name: 'Hopscotch',
    isPreset: true,
    themeData: {
      foreground: '#b9b5b8',
      background: '#322931',
      cursor: '#b9b5b8',
      cursorAccent: '#322931',
      selectionBackground: '#5c545b',
      selectionForeground: '#b9b5b8',
      black: '#322931',
      red: '#dd464c',
      green: '#8fc13e',
      yellow: '#fdcc59',
      blue: '#1290bf',
      magenta: '#c85e7c',
      cyan: '#149b93',
      white: '#b9b5b8',
      brightBlack: '#797379',
      brightRed: '#fd8b19',
      brightGreen: '#433b42',
      brightYellow: '#5c545b',
      brightBlue: '#989498',
      brightMagenta: '#d5d3d5',
      brightCyan: '#b33508',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Hopscotch.256',
    name: 'Hopscotch.256',
    isPreset: true,
    themeData: {
      foreground: '#b9b5b8',
      background: '#322931',
      cursor: '#b9b5b8',
      cursorAccent: '#322931',
      selectionBackground: '#5c545b',
      selectionForeground: '#b9b5b8',
      black: '#322931',
      red: '#dd464c',
      green: '#8fc13e',
      yellow: '#fdcc59',
      blue: '#1290bf',
      magenta: '#c85e7c',
      cyan: '#149b93',
      white: '#b9b5b8',
      brightBlack: '#797379',
      brightRed: '#dd464c',
      brightGreen: '#8fc13e',
      brightYellow: '#fdcc59',
      brightBlue: '#1290bf',
      brightMagenta: '#c85e7c',
      brightCyan: '#149b93',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Horizon',
    name: 'Horizon',
    isPreset: true,
    themeData: {
      foreground: '#d5d8da',
      background: '#1c1e26',
      cursor: '#6c6f93',
      cursorAccent: '#6c6f93',
      selectionBackground: '#6c6f93',
      selectionForeground: '#1c1e26',
      black: '#000000',
      red: '#e95678',
      green: '#29d398',
      yellow: '#fab795',
      blue: '#26bbd9',
      magenta: '#ee64ac',
      cyan: '#59e1e3',
      white: '#e5e5e5',
      brightBlack: '#666666',
      brightRed: '#ec6a88',
      brightGreen: '#3fdaa4',
      brightYellow: '#fbc3a7',
      brightBlue: '#3fc4de',
      brightMagenta: '#f075b5',
      brightCyan: '#6be4e6',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'Horizon-Bright',
    name: 'Horizon-Bright',
    isPreset: true,
    themeData: {
      foreground: '#16161c',
      background: '#fbf0ee',
      cursor: '#f2d0c5',
      cursorAccent: '#f2d0c5',
      selectionBackground: '#f2d0c5',
      selectionForeground: '#16161c',
      black: '#16161c',
      red: '#e95678',
      green: '#29d398',
      yellow: '#fab795',
      blue: '#26bbd9',
      magenta: '#ee64ae',
      cyan: '#59e3e3',
      white: '#fdf0ed',
      brightBlack: '#1a1c23',
      brightRed: '#ec6a88',
      brightGreen: '#3fdaa4',
      brightYellow: '#fbc3a7',
      brightBlue: '#3fc6de',
      brightMagenta: '#f075b7',
      brightCyan: '#6be6e6',
      brightWhite: '#fff3f0',
    },
  },
  {
    preset_key: 'Hurtado',
    name: 'Hurtado',
    isPreset: true,
    themeData: {
      foreground: '#dbdbdb',
      background: '#000000',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#575757',
      red: '#ff1b00',
      green: '#a5e055',
      yellow: '#fbe74a',
      blue: '#496487',
      magenta: '#fd5ff1',
      cyan: '#86e9fe',
      white: '#cbcccb',
      brightBlack: '#262626',
      brightRed: '#d51d00',
      brightGreen: '#a5df55',
      brightYellow: '#fbe84a',
      brightBlue: '#89beff',
      brightMagenta: '#c001c1',
      brightCyan: '#86eafe',
      brightWhite: '#dbdbdb',
    },
  },
  {
    preset_key: 'Hybrid',
    name: 'Hybrid',
    isPreset: true,
    themeData: {
      foreground: '#b7bcba',
      background: '#161719',
      cursor: '#b7bcba',
      cursorAccent: '#1e1f22',
      selectionBackground: '#1e1f22',
      selectionForeground: '#b7bcba',
      black: '#2a2e33',
      red: '#b84d51',
      green: '#b3bf5a',
      yellow: '#e4b55e',
      blue: '#6e90b0',
      magenta: '#a17eac',
      cyan: '#7fbfb4',
      white: '#b5b9b6',
      brightBlack: '#1d1f22',
      brightRed: '#8d2e32',
      brightGreen: '#798431',
      brightYellow: '#e58a50',
      brightBlue: '#4b6b88',
      brightMagenta: '#6e5079',
      brightCyan: '#4d7b74',
      brightWhite: '#5a626a',
    },
  },
  {
    preset_key: 'IC_Green_PPL',
    name: 'IC_Green_PPL',
    isPreset: true,
    themeData: {
      foreground: '#e0f1dc',
      background: '#2c2c2c',
      cursor: '#47fa6b',
      cursorAccent: '#292929',
      selectionBackground: '#116b41',
      selectionForeground: '#e0f1dc',
      black: '#014401',
      red: '#ff2736',
      green: '#41a638',
      yellow: '#76a831',
      blue: '#2ec3b9',
      magenta: '#50a096',
      cyan: '#3ca078',
      white: '#e6fef2',
      brightBlack: '#035c03',
      brightRed: '#b4fa5c',
      brightGreen: '#aefb86',
      brightYellow: '#dafa87',
      brightBlue: '#2efaeb',
      brightMagenta: '#50fafa',
      brightCyan: '#3cfac8',
      brightWhite: '#e0f1dc',
    },
  },
  {
    preset_key: 'IC_Orange_PPL',
    name: 'IC_Orange_PPL',
    isPreset: true,
    themeData: {
      foreground: '#ffcb83',
      background: '#262626',
      cursor: '#fc531d',
      cursorAccent: '#ffc88a',
      selectionBackground: '#c14020',
      selectionForeground: '#ffc88a',
      black: '#000000',
      red: '#c13900',
      green: '#a4a900',
      yellow: '#caaf00',
      blue: '#bd6d00',
      magenta: '#fc5e00',
      cyan: '#f79500',
      white: '#ffc88a',
      brightBlack: '#6a4f2a',
      brightRed: '#ff8c68',
      brightGreen: '#f6ff40',
      brightYellow: '#ffe36e',
      brightBlue: '#ffbe55',
      brightMagenta: '#fc874f',
      brightCyan: '#c69752',
      brightWhite: '#fafaff',
    },
  },
  {
    preset_key: 'iceberg-dark',
    name: 'iceberg-dark',
    isPreset: true,
    themeData: {
      foreground: '#c6c8d1',
      background: '#161821',
      cursor: '#c6c8d1',
      cursorAccent: '#161821',
      selectionBackground: '#c6c8d1',
      selectionForeground: '#161821',
      black: '#1e2132',
      red: '#e27878',
      green: '#b4be82',
      yellow: '#e2a478',
      blue: '#84a0c6',
      magenta: '#a093c7',
      cyan: '#89b8c2',
      white: '#c6c8d1',
      brightBlack: '#6b7089',
      brightRed: '#e98989',
      brightGreen: '#c0ca8e',
      brightYellow: '#e9b189',
      brightBlue: '#91acd1',
      brightMagenta: '#ada0d3',
      brightCyan: '#95c4ce',
      brightWhite: '#d2d4de',
    },
  },
  {
    preset_key: 'iceberg-light',
    name: 'iceberg-light',
    isPreset: true,
    themeData: {
      foreground: '#33374c',
      background: '#e8e9ec',
      cursor: '#33374c',
      cursorAccent: '#e8e9ec',
      selectionBackground: '#33374c',
      selectionForeground: '#e8e9ec',
      black: '#dcdfe7',
      red: '#cc517a',
      green: '#668e3d',
      yellow: '#c57339',
      blue: '#2d539e',
      magenta: '#7759b4',
      cyan: '#3f83a6',
      white: '#33374c',
      brightBlack: '#8389a3',
      brightRed: '#cc3768',
      brightGreen: '#598030',
      brightYellow: '#b6662d',
      brightBlue: '#22478e',
      brightMagenta: '#6845ad',
      brightCyan: '#327698',
      brightWhite: '#262a3f',
    },
  },
  {
    preset_key: 'idea',
    name: 'idea',
    isPreset: true,
    themeData: {
      foreground: '#adadad',
      background: '#202020',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#44475a',
      selectionForeground: '#ffffff',
      black: '#adadad',
      red: '#fc5256',
      green: '#98b61c',
      yellow: '#ccb444',
      blue: '#437ee7',
      magenta: '#9d74b0',
      cyan: '#248887',
      white: '#181818',
      brightBlack: '#ffffff',
      brightRed: '#fc7072',
      brightGreen: '#98b61c',
      brightYellow: '#ffff0b',
      brightBlue: '#6c9ced',
      brightMagenta: '#fc7eff',
      brightCyan: '#248887',
      brightWhite: '#181818',
    },
  },
  {
    preset_key: 'idleToes',
    name: 'idleToes',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#323232',
      cursor: '#d6d6d6',
      cursorAccent: '#000000',
      selectionBackground: '#5b5b5b',
      selectionForeground: '#000000',
      black: '#323232',
      red: '#d25252',
      green: '#7fe173',
      yellow: '#ffc66d',
      blue: '#4099ff',
      magenta: '#f680ff',
      cyan: '#bed6ff',
      white: '#eeeeec',
      brightBlack: '#535353',
      brightRed: '#f07070',
      brightGreen: '#9dff91',
      brightYellow: '#ffe48b',
      brightBlue: '#5eb7f7',
      brightMagenta: '#ff9dff',
      brightCyan: '#dcf4ff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'IR_Black',
    name: 'IR_Black',
    isPreset: true,
    themeData: {
      foreground: '#f1f1f1',
      background: '#000000',
      cursor: '#808080',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#4f4f4f',
      red: '#fa6c60',
      green: '#a8ff60',
      yellow: '#fffeb7',
      blue: '#96cafe',
      magenta: '#fa73fd',
      cyan: '#c6c5fe',
      white: '#efedef',
      brightBlack: '#7b7b7b',
      brightRed: '#fcb6b0',
      brightGreen: '#cfffab',
      brightYellow: '#ffffcc',
      brightBlue: '#b5dcff',
      brightMagenta: '#fb9cfe',
      brightCyan: '#e0e0fe',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'IRIX_Console',
    name: 'IRIX Console',
    isPreset: true,
    themeData: {
      foreground: '#f2f2f2',
      background: '#0c0c0c',
      cursor: '#c7c7c7',
      cursorAccent: '#ffffff',
      selectionBackground: '#c2deff',
      selectionForeground: '#000000',
      black: '#1a1919',
      red: '#d42426',
      green: '#37a327',
      yellow: '#c29d28',
      blue: '#0739e2',
      magenta: '#911f9c',
      cyan: '#4497df',
      white: '#cccccc',
      brightBlack: '#767676',
      brightRed: '#f34f59',
      brightGreen: '#45c731',
      brightYellow: '#f9f2a7',
      brightBlue: '#4079ff',
      brightMagenta: '#c31ba2',
      brightCyan: '#6ed7d7',
      brightWhite: '#f2f2f2',
    },
  },
  {
    preset_key: 'IRIX_Terminal',
    name: 'IRIX Terminal',
    isPreset: true,
    themeData: {
      foreground: '#f2f2f2',
      background: '#000043',
      cursor: '#c7c7c7',
      cursorAccent: '#ffffff',
      selectionBackground: '#c2deff',
      selectionForeground: '#000000',
      black: '#1a1919',
      red: '#ff2b1e',
      green: '#57ff3d',
      yellow: '#ffff44',
      blue: '#0004ff',
      magenta: '#ff2cff',
      cyan: '#56ffff',
      white: '#ffffff',
      brightBlack: '#ffff44',
      brightRed: '#ffff44',
      brightGreen: '#ffff44',
      brightYellow: '#fffc72',
      brightBlue: '#ffff44',
      brightMagenta: '#ffff44',
      brightCyan: '#ffff44',
      brightWhite: '#ffff44',
    },
  },
  {
    preset_key: 'iTerm2_Dark_Background',
    name: 'iTerm2 Dark Background',
    isPreset: true,
    themeData: {
      foreground: '#c7c7c7',
      background: '#000000',
      cursor: '#c7c7c7',
      cursorAccent: '#ffffff',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#c91b00',
      green: '#00c200',
      yellow: '#c7c400',
      blue: '#0225c7',
      magenta: '#ca30c7',
      cyan: '#00c5c7',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#ff6e67',
      brightGreen: '#5ffa68',
      brightYellow: '#fffc67',
      brightBlue: '#6871ff',
      brightMagenta: '#ff77ff',
      brightCyan: '#60fdff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'iTerm2_Default',
    name: 'iTerm2 Default',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#000000',
      cursor: '#e5e5e5',
      cursorAccent: '#000000',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#c91b00',
      green: '#00c200',
      yellow: '#c7c400',
      blue: '#2225c4',
      magenta: '#ca30c7',
      cyan: '#00c5c7',
      white: '#ffffff',
      brightBlack: '#686868',
      brightRed: '#ff6e67',
      brightGreen: '#5ffa68',
      brightYellow: '#fffc67',
      brightBlue: '#6871ff',
      brightMagenta: '#ff77ff',
      brightCyan: '#60fdff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'iTerm2_Light_Background',
    name: 'iTerm2 Light Background',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#000000',
      cursorAccent: '#ffffff',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#c91b00',
      green: '#00c200',
      yellow: '#c7c400',
      blue: '#0225c7',
      magenta: '#ca30c7',
      cyan: '#00c5c7',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#ff6e67',
      brightGreen: '#5ffa68',
      brightYellow: '#fffc67',
      brightBlue: '#6871ff',
      brightMagenta: '#ff77ff',
      brightCyan: '#60fdff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'iTerm2_Pastel_Dark_Background',
    name: 'iTerm2 Pastel Dark Background',
    isPreset: true,
    themeData: {
      foreground: '#c7c7c7',
      background: '#000000',
      cursor: '#ffb473',
      cursorAccent: '#ffffff',
      selectionBackground: '#454d96',
      selectionForeground: '#f4f4f4',
      black: '#626262',
      red: '#ff8373',
      green: '#b4fb73',
      yellow: '#fffdc3',
      blue: '#a5d5fe',
      magenta: '#ff90fe',
      cyan: '#d1d1fe',
      white: '#f1f1f1',
      brightBlack: '#8f8f8f',
      brightRed: '#ffc4be',
      brightGreen: '#d6fcba',
      brightYellow: '#fffed5',
      brightBlue: '#c2e3ff',
      brightMagenta: '#ffb2fe',
      brightCyan: '#e6e6fe',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'iTerm2_Smoooooth',
    name: 'iTerm2 Smoooooth',
    isPreset: true,
    themeData: {
      foreground: '#dcdcdc',
      background: '#15191f',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#b3d7ff',
      selectionForeground: '#000000',
      black: '#14191e',
      red: '#b43c2a',
      green: '#00c200',
      yellow: '#c7c400',
      blue: '#2744c7',
      magenta: '#c040be',
      cyan: '#00c5c7',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#dd7975',
      brightGreen: '#58e790',
      brightYellow: '#ece100',
      brightBlue: '#a7abf2',
      brightMagenta: '#e17ee1',
      brightCyan: '#60fdff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'iTerm2_Solarized_Dark',
    name: 'iTerm2 Solarized Dark',
    isPreset: true,
    themeData: {
      foreground: '#839496',
      background: '#002b36',
      cursor: '#839496',
      cursorAccent: '#073642',
      selectionBackground: '#073642',
      selectionForeground: '#93a1a1',
      black: '#073642',
      red: '#dc322f',
      green: '#859900',
      yellow: '#b58900',
      blue: '#268bd2',
      magenta: '#d33682',
      cyan: '#2aa198',
      white: '#eee8d5',
      brightBlack: '#002b36',
      brightRed: '#cb4b16',
      brightGreen: '#586e75',
      brightYellow: '#657b83',
      brightBlue: '#839496',
      brightMagenta: '#6c71c4',
      brightCyan: '#93a1a1',
      brightWhite: '#fdf6e3',
    },
  },
  {
    preset_key: 'iTerm2_Solarized_Light',
    name: 'iTerm2 Solarized Light',
    isPreset: true,
    themeData: {
      foreground: '#657b83',
      background: '#fdf6e3',
      cursor: '#657b83',
      cursorAccent: '#eee8d5',
      selectionBackground: '#eee8d5',
      selectionForeground: '#586e75',
      black: '#073642',
      red: '#dc322f',
      green: '#859900',
      yellow: '#b58900',
      blue: '#268bd2',
      magenta: '#d33682',
      cyan: '#2aa198',
      white: '#eee8d5',
      brightBlack: '#002b36',
      brightRed: '#cb4b16',
      brightGreen: '#586e75',
      brightYellow: '#657b83',
      brightBlue: '#839496',
      brightMagenta: '#6c71c4',
      brightCyan: '#93a1a1',
      brightWhite: '#fdf6e3',
    },
  },
  {
    preset_key: 'iTerm2_Tango_Dark',
    name: 'iTerm2 Tango Dark',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#000000',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#d81e00',
      green: '#5ea702',
      yellow: '#cfae00',
      blue: '#427ab3',
      magenta: '#89658e',
      cyan: '#00a7aa',
      white: '#dbded8',
      brightBlack: '#686a66',
      brightRed: '#f54235',
      brightGreen: '#99e343',
      brightYellow: '#fdeb61',
      brightBlue: '#84b0d8',
      brightMagenta: '#bc94b7',
      brightCyan: '#37e6e8',
      brightWhite: '#f1f1f0',
    },
  },
  {
    preset_key: 'iTerm2_Tango_Light',
    name: 'iTerm2 Tango Light',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#000000',
      cursorAccent: '#ffffff',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#d81e00',
      green: '#5ea702',
      yellow: '#cfae00',
      blue: '#427ab3',
      magenta: '#89658e',
      cyan: '#00a7aa',
      white: '#dbded8',
      brightBlack: '#686a66',
      brightRed: '#f54235',
      brightGreen: '#99e343',
      brightYellow: '#fdeb61',
      brightBlue: '#84b0d8',
      brightMagenta: '#bc94b7',
      brightCyan: '#37e6e8',
      brightWhite: '#f1f1f0',
    },
  },
  {
    preset_key: 'Jackie_Brown',
    name: 'Jackie Brown',
    isPreset: true,
    themeData: {
      foreground: '#ffcc2f',
      background: '#2c1d16',
      cursor: '#23ff18',
      cursorAccent: '#ff0018',
      selectionBackground: '#af8d21',
      selectionForeground: '#ffffff',
      black: '#2c1d16',
      red: '#ef5734',
      green: '#2baf2b',
      yellow: '#bebf00',
      blue: '#246eb2',
      magenta: '#d05ec1',
      cyan: '#00acee',
      white: '#bfbfbf',
      brightBlack: '#666666',
      brightRed: '#e50000',
      brightGreen: '#86a93e',
      brightYellow: '#e5e500',
      brightBlue: '#0000ff',
      brightMagenta: '#e500e5',
      brightCyan: '#00e5e5',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'Japanesque',
    name: 'Japanesque',
    isPreset: true,
    themeData: {
      foreground: '#f7f6ec',
      background: '#1e1e1e',
      cursor: '#edcf4f',
      cursorAccent: '#343935',
      selectionBackground: '#175877',
      selectionForeground: '#f7f6ec',
      black: '#343935',
      red: '#cf3f61',
      green: '#7bb75b',
      yellow: '#e9b32a',
      blue: '#4c9ad4',
      magenta: '#a57fc4',
      cyan: '#389aad',
      white: '#fafaf6',
      brightBlack: '#595b59',
      brightRed: '#d18fa6',
      brightGreen: '#767f2c',
      brightYellow: '#78592f',
      brightBlue: '#135979',
      brightMagenta: '#604291',
      brightCyan: '#76bbca',
      brightWhite: '#b2b5ae',
    },
  },
  {
    preset_key: 'Jellybeans',
    name: 'Jellybeans',
    isPreset: true,
    themeData: {
      foreground: '#dedede',
      background: '#121212',
      cursor: '#ffa560',
      cursorAccent: '#ffffff',
      selectionBackground: '#474e91',
      selectionForeground: '#f4f4f4',
      black: '#929292',
      red: '#e27373',
      green: '#94b979',
      yellow: '#ffba7b',
      blue: '#97bedc',
      magenta: '#e1c0fa',
      cyan: '#00988e',
      white: '#dedede',
      brightBlack: '#bdbdbd',
      brightRed: '#ffa1a1',
      brightGreen: '#bddeab',
      brightYellow: '#ffdca0',
      brightBlue: '#b1d8f6',
      brightMagenta: '#fbdaff',
      brightCyan: '#1ab2a8',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'JetBrains_Darcula',
    name: 'JetBrains Darcula',
    isPreset: true,
    themeData: {
      foreground: '#adadad',
      background: '#202020',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#1a3272',
      selectionForeground: '#adadad',
      black: '#000000',
      red: '#fa5355',
      green: '#126e00',
      yellow: '#c2c300',
      blue: '#4581eb',
      magenta: '#fa54ff',
      cyan: '#33c2c1',
      white: '#adadad',
      brightBlack: '#555555',
      brightRed: '#fb7172',
      brightGreen: '#67ff4f',
      brightYellow: '#ffff00',
      brightBlue: '#6d9df1',
      brightMagenta: '#fb82ff',
      brightCyan: '#60d3d1',
      brightWhite: '#eeeeee',
    },
  },
  {
    preset_key: 'jubi',
    name: 'jubi',
    isPreset: true,
    themeData: {
      foreground: '#c3d3de',
      background: '#262b33',
      cursor: '#c3d3de',
      cursorAccent: '#1e1b2e',
      selectionBackground: '#5b5184',
      selectionForeground: '#1e1b2e',
      black: '#3b3750',
      red: '#cf7b98',
      green: '#90a94b',
      yellow: '#6ebfc0',
      blue: '#576ea6',
      magenta: '#bc4f68',
      cyan: '#75a7d2',
      white: '#c3d3de',
      brightBlack: '#a874ce',
      brightRed: '#de90ab',
      brightGreen: '#bcdd61',
      brightYellow: '#87e9ea',
      brightBlue: '#8c9fcd',
      brightMagenta: '#e16c87',
      brightCyan: '#b7c9ef',
      brightWhite: '#d5e5f1',
    },
  },
  {
    preset_key: 'Kanagawa_Dragon',
    name: 'Kanagawa Dragon',
    isPreset: true,
    themeData: {
      foreground: '#c8c093',
      background: '#181616',
      cursor: '#c5c9c5',
      cursorAccent: '#1d202f',
      selectionBackground: '#223249',
      selectionForeground: '#c5c9c5',
      black: '#0d0c0c',
      red: '#c4746e',
      green: '#8a9a7b',
      yellow: '#c4b28a',
      blue: '#8ba4b0',
      magenta: '#a292a3',
      cyan: '#8ea4a2',
      white: '#c8c093',
      brightBlack: '#a6a69c',
      brightRed: '#e46876',
      brightGreen: '#87a987',
      brightYellow: '#e6c384',
      brightBlue: '#7fb4ca',
      brightMagenta: '#938aa9',
      brightCyan: '#7aa89f',
      brightWhite: '#c5c9c5',
    },
  },
  {
    preset_key: 'Kanagawa_Wave',
    name: 'Kanagawa Wave',
    isPreset: true,
    themeData: {
      foreground: '#dcd7ba',
      background: '#1f1f28',
      cursor: '#c8c093',
      cursorAccent: '#1d202f',
      selectionBackground: '#2d4f67',
      selectionForeground: '#c8c093',
      black: '#090618',
      red: '#c34043',
      green: '#76946a',
      yellow: '#c0a36e',
      blue: '#7e9cd8',
      magenta: '#957fb8',
      cyan: '#6a9589',
      white: '#c8c093',
      brightBlack: '#727169',
      brightRed: '#e82424',
      brightGreen: '#98bb6c',
      brightYellow: '#e6c384',
      brightBlue: '#7fb4ca',
      brightMagenta: '#938aa9',
      brightCyan: '#7aa89f',
      brightWhite: '#dcd7ba',
    },
  },
  {
    preset_key: 'kanagawabones',
    name: 'kanagawabones',
    isPreset: true,
    themeData: {
      foreground: '#ddd8bb',
      background: '#1f1f28',
      cursor: '#e6e0c2',
      cursorAccent: '#1f1f28',
      selectionBackground: '#49473e',
      selectionForeground: '#ddd8bb',
      black: '#1f1f28',
      red: '#e46a78',
      green: '#98bc6d',
      yellow: '#e5c283',
      blue: '#7eb3c9',
      magenta: '#957fb8',
      cyan: '#7eb3c9',
      white: '#ddd8bb',
      brightBlack: '#3c3c51',
      brightRed: '#ec818c',
      brightGreen: '#9ec967',
      brightYellow: '#f1c982',
      brightBlue: '#7bc2df',
      brightMagenta: '#a98fd2',
      brightCyan: '#7bc2df',
      brightWhite: '#a8a48d',
    },
  },
  {
    preset_key: 'Kibble',
    name: 'Kibble',
    isPreset: true,
    themeData: {
      foreground: '#f7f7f7',
      background: '#0e100a',
      cursor: '#9fda9c',
      cursorAccent: '#000000',
      selectionBackground: '#9ba787',
      selectionForeground: '#000000',
      black: '#4d4d4d',
      red: '#c70031',
      green: '#29cf13',
      yellow: '#d8e30e',
      blue: '#3449d1',
      magenta: '#8400ff',
      cyan: '#0798ab',
      white: '#e2d1e3',
      brightBlack: '#5a5a5a',
      brightRed: '#f01578',
      brightGreen: '#6ce05c',
      brightYellow: '#f3f79e',
      brightBlue: '#97a4f7',
      brightMagenta: '#c495f0',
      brightCyan: '#68f2e0',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Kolorit',
    name: 'Kolorit',
    isPreset: true,
    themeData: {
      foreground: '#efecec',
      background: '#1d1a1e',
      cursor: '#c7c7c7',
      cursorAccent: '#ffffff',
      selectionBackground: '#e1925c',
      selectionForeground: '#1d1a1e',
      black: '#1d1a1e',
      red: '#ff5b82',
      green: '#47d7a1',
      yellow: '#e8e562',
      blue: '#5db4ee',
      magenta: '#da6cda',
      cyan: '#57e9eb',
      white: '#ededed',
      brightBlack: '#1d1a1e',
      brightRed: '#ff5b82',
      brightGreen: '#47d7a1',
      brightYellow: '#e8e562',
      brightBlue: '#5db4ee',
      brightMagenta: '#da6cda',
      brightCyan: '#57e9eb',
      brightWhite: '#ededed',
    },
  },
  {
    preset_key: 'Konsolas',
    name: 'Konsolas',
    isPreset: true,
    themeData: {
      foreground: '#c8c1c1',
      background: '#060606',
      cursor: '#c8c1c1',
      cursorAccent: '#060606',
      selectionBackground: '#060606',
      selectionForeground: '#c8c1c1',
      black: '#000000',
      red: '#aa1717',
      green: '#18b218',
      yellow: '#ebae1f',
      blue: '#2323a5',
      magenta: '#ad1edc',
      cyan: '#42b0c8',
      white: '#c8c1c1',
      brightBlack: '#7b716e',
      brightRed: '#ff4141',
      brightGreen: '#5fff5f',
      brightYellow: '#ffff55',
      brightBlue: '#4b4bff',
      brightMagenta: '#ff54ff',
      brightCyan: '#69ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'kurokula',
    name: 'kurokula',
    isPreset: true,
    themeData: {
      foreground: '#ddd0c4',
      background: '#141515',
      cursor: '#702420',
      cursorAccent: '#fefbf3',
      selectionBackground: '#515151',
      selectionForeground: '#ffc663',
      black: '#333333',
      red: '#b66056',
      green: '#85b1a9',
      yellow: '#dbbb43',
      blue: '#6890d7',
      magenta: '#887aa3',
      cyan: '#837369',
      white: '#ddd0c4',
      brightBlack: '#515151',
      brightRed: '#ffc663',
      brightGreen: '#c1ffae',
      brightYellow: '#fff700',
      brightBlue: '#a1d9ff',
      brightMagenta: '#a994ff',
      brightCyan: '#f9cfb9',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Lab_Fox',
    name: 'Lab Fox',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#2e2e2e',
      cursor: '#7f7f7f',
      cursorAccent: '#7f7f7f',
      selectionBackground: '#cb392e',
      selectionForeground: '#ffffff',
      black: '#2e2e2e',
      red: '#fc6d26',
      green: '#3eb383',
      yellow: '#fca121',
      blue: '#db3b21',
      magenta: '#380d75',
      cyan: '#6e49cb',
      white: '#ffffff',
      brightBlack: '#464646',
      brightRed: '#ff6517',
      brightGreen: '#53eaa8',
      brightYellow: '#fca013',
      brightBlue: '#db501f',
      brightMagenta: '#441090',
      brightCyan: '#7d53e7',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Laser',
    name: 'Laser',
    isPreset: true,
    themeData: {
      foreground: '#f106e3',
      background: '#030d18',
      cursor: '#00ff9c',
      cursorAccent: '#ffffff',
      selectionBackground: '#2e206a',
      selectionForeground: '#f4f4f4',
      black: '#626262',
      red: '#ff8373',
      green: '#b4fb73',
      yellow: '#09b4bd',
      blue: '#fed300',
      magenta: '#ff90fe',
      cyan: '#d1d1fe',
      white: '#f1f1f1',
      brightBlack: '#8f8f8f',
      brightRed: '#ffc4be',
      brightGreen: '#d6fcba',
      brightYellow: '#fffed5',
      brightBlue: '#f92883',
      brightMagenta: '#ffb2fe',
      brightCyan: '#e6e7fe',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Later_This_Evening',
    name: 'Later This Evening',
    isPreset: true,
    themeData: {
      foreground: '#959595',
      background: '#222222',
      cursor: '#424242',
      cursorAccent: '#959595',
      selectionBackground: '#424242',
      selectionForeground: '#959595',
      black: '#2b2b2b',
      red: '#d45a60',
      green: '#afba67',
      yellow: '#e5d289',
      blue: '#a0bad6',
      magenta: '#c092d6',
      cyan: '#91bfb7',
      white: '#3c3d3d',
      brightBlack: '#454747',
      brightRed: '#d3232f',
      brightGreen: '#aabb39',
      brightYellow: '#e5be39',
      brightBlue: '#6699d6',
      brightMagenta: '#ab53d6',
      brightCyan: '#5fc0ae',
      brightWhite: '#c1c2c2',
    },
  },
  {
    preset_key: 'Lavandula',
    name: 'Lavandula',
    isPreset: true,
    themeData: {
      foreground: '#736e7d',
      background: '#050014',
      cursor: '#8c91fa',
      cursorAccent: '#050014',
      selectionBackground: '#37323c',
      selectionForeground: '#8c91fa',
      black: '#230046',
      red: '#7d1625',
      green: '#337e6f',
      yellow: '#7f6f49',
      blue: '#4f4a7f',
      magenta: '#5a3f7f',
      cyan: '#58777f',
      white: '#736e7d',
      brightBlack: '#372d46',
      brightRed: '#e05167',
      brightGreen: '#52e0c4',
      brightYellow: '#e0c386',
      brightBlue: '#8e87e0',
      brightMagenta: '#a776e0',
      brightCyan: '#9ad4e0',
      brightWhite: '#8c91fa',
    },
  },
  {
    preset_key: 'LightOwl',
    name: 'LightOwl',
    isPreset: true,
    themeData: {
      foreground: '#403f53',
      background: '#fbfbfb',
      cursor: '#403f53',
      cursorAccent: '#f6f6f6',
      selectionBackground: '#e0e0e0',
      selectionForeground: '#403f53',
      black: '#403f53',
      red: '#de3d3b',
      green: '#08916a',
      yellow: '#e0af02',
      blue: '#288ed7',
      magenta: '#d6438a',
      cyan: '#2aa298',
      white: '#f0f0f0',
      brightBlack: '#989fb1',
      brightRed: '#de3d3b',
      brightGreen: '#08916a',
      brightYellow: '#daaa01',
      brightBlue: '#288ed7',
      brightMagenta: '#d6438a',
      brightCyan: '#2aa298',
      brightWhite: '#f0f0f0',
    },
  },
  {
    preset_key: 'LiquidCarbon',
    name: 'LiquidCarbon',
    isPreset: true,
    themeData: {
      foreground: '#afc2c2',
      background: '#303030',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#7dbeff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#ff3030',
      green: '#559a70',
      yellow: '#ccac00',
      blue: '#0099cc',
      magenta: '#cc69c8',
      cyan: '#7ac4cc',
      white: '#bccccc',
      brightBlack: '#000000',
      brightRed: '#ff3030',
      brightGreen: '#559a70',
      brightYellow: '#ccac00',
      brightBlue: '#0099cc',
      brightMagenta: '#cc69c8',
      brightCyan: '#7ac4cc',
      brightWhite: '#bccccc',
    },
  },
  {
    preset_key: 'LiquidCarbonTransparent',
    name: 'LiquidCarbonTransparent',
    isPreset: true,
    themeData: {
      foreground: '#afc2c2',
      background: '#000000',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#7dbeff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#ff3030',
      green: '#559a70',
      yellow: '#ccac00',
      blue: '#0099cc',
      magenta: '#cc69c8',
      cyan: '#7ac4cc',
      white: '#bccccc',
      brightBlack: '#000000',
      brightRed: '#ff3030',
      brightGreen: '#559a70',
      brightYellow: '#ccac00',
      brightBlue: '#0099cc',
      brightMagenta: '#cc69c8',
      brightCyan: '#7ac4cc',
      brightWhite: '#bccccc',
    },
  },
  {
    preset_key: 'LiquidCarbonTransparentInverse',
    name: 'LiquidCarbonTransparentInverse',
    isPreset: true,
    themeData: {
      foreground: '#afc2c2',
      background: '#000000',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#7dbeff',
      selectionForeground: '#000000',
      black: '#bccccd',
      red: '#ff3030',
      green: '#559a70',
      yellow: '#ccac00',
      blue: '#0099cc',
      magenta: '#cc69c8',
      cyan: '#7ac4cc',
      white: '#000000',
      brightBlack: '#ffffff',
      brightRed: '#ff3030',
      brightGreen: '#559a70',
      brightYellow: '#ccac00',
      brightBlue: '#0099cc',
      brightMagenta: '#cc69c8',
      brightCyan: '#7ac4cc',
      brightWhite: '#000000',
    },
  },
  {
    preset_key: 'lovelace',
    name: 'lovelace',
    isPreset: true,
    themeData: {
      foreground: '#fdfdfd',
      background: '#1d1f28',
      cursor: '#c574dd',
      cursorAccent: '#ffffff',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#282a36',
      red: '#f37f97',
      green: '#5adecd',
      yellow: '#f2a272',
      blue: '#8897f4',
      magenta: '#c574dd',
      cyan: '#79e6f3',
      white: '#fdfdfd',
      brightBlack: '#414458',
      brightRed: '#ff4971',
      brightGreen: '#18e3c8',
      brightYellow: '#ff8037',
      brightBlue: '#556fff',
      brightMagenta: '#b043d1',
      brightCyan: '#3fdcee',
      brightWhite: '#bebec1',
    },
  },
  {
    preset_key: 'Man_Page',
    name: 'Man Page',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#fef49c',
      cursor: '#7f7f7f',
      cursorAccent: '#000000',
      selectionBackground: '#a4c9cd',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#cc0000',
      green: '#00a600',
      yellow: '#999900',
      blue: '#0000b2',
      magenta: '#b200b2',
      cyan: '#00a6b2',
      white: '#cccccc',
      brightBlack: '#666666',
      brightRed: '#e50000',
      brightGreen: '#00d900',
      brightYellow: '#e5e500',
      brightBlue: '#0000ff',
      brightMagenta: '#e500e5',
      brightCyan: '#00e5e5',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'Mariana',
    name: 'Mariana',
    isPreset: true,
    themeData: {
      foreground: '#d8dee9',
      background: '#343d46',
      cursor: '#fcbb6a',
      cursorAccent: '#ffffff',
      selectionBackground: '#4e5a65',
      selectionForeground: '#d8dee9',
      black: '#000000',
      red: '#ec5f66',
      green: '#99c794',
      yellow: '#f9ae58',
      blue: '#6699cc',
      magenta: '#c695c6',
      cyan: '#5fb4b4',
      white: '#f7f7f7',
      brightBlack: '#333333',
      brightRed: '#f97b58',
      brightGreen: '#acd1a8',
      brightYellow: '#fac761',
      brightBlue: '#85add6',
      brightMagenta: '#d8b6d8',
      brightCyan: '#82c4c4',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Material',
    name: 'Material',
    isPreset: true,
    themeData: {
      foreground: '#232322',
      background: '#eaeaea',
      cursor: '#16afca',
      cursorAccent: '#2e2e2d',
      selectionBackground: '#c2c2c2',
      selectionForeground: '#4e4e4e',
      black: '#212121',
      red: '#b7141f',
      green: '#457b24',
      yellow: '#f6981e',
      blue: '#134eb2',
      magenta: '#560088',
      cyan: '#0e717c',
      white: '#efefef',
      brightBlack: '#424242',
      brightRed: '#e83b3f',
      brightGreen: '#7aba3a',
      brightYellow: '#ffea2e',
      brightBlue: '#54a4f3',
      brightMagenta: '#aa4dbc',
      brightCyan: '#26bbd1',
      brightWhite: '#d9d9d9',
    },
  },
  {
    preset_key: 'MaterialDark',
    name: 'MaterialDark',
    isPreset: true,
    themeData: {
      foreground: '#e5e5e5',
      background: '#232322',
      cursor: '#16afca',
      cursorAccent: '#dfdfdf',
      selectionBackground: '#dfdfdf',
      selectionForeground: '#3d3d3d',
      black: '#212121',
      red: '#b7141f',
      green: '#457b24',
      yellow: '#f6981e',
      blue: '#134eb2',
      magenta: '#560088',
      cyan: '#0e717c',
      white: '#efefef',
      brightBlack: '#424242',
      brightRed: '#e83b3f',
      brightGreen: '#7aba3a',
      brightYellow: '#ffea2e',
      brightBlue: '#54a4f3',
      brightMagenta: '#aa4dbc',
      brightCyan: '#26bbd1',
      brightWhite: '#d9d9d9',
    },
  },
  {
    preset_key: 'MaterialDarker',
    name: 'MaterialDarker',
    isPreset: true,
    themeData: {
      foreground: '#eeffff',
      background: '#212121',
      cursor: '#ffffff',
      cursorAccent: '#ffffff',
      selectionBackground: '#eeffff',
      selectionForeground: '#545454',
      black: '#000000',
      red: '#ff5370',
      green: '#c3e88d',
      yellow: '#ffcb6b',
      blue: '#82aaff',
      magenta: '#c792ea',
      cyan: '#89ddff',
      white: '#ffffff',
      brightBlack: '#545454',
      brightRed: '#ff5370',
      brightGreen: '#c3e88d',
      brightYellow: '#ffcb6b',
      brightBlue: '#82aaff',
      brightMagenta: '#c792ea',
      brightCyan: '#89ddff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'MaterialDesignColors',
    name: 'MaterialDesignColors',
    isPreset: true,
    themeData: {
      foreground: '#e7ebed',
      background: '#1d262a',
      cursor: '#eaeaea',
      cursorAccent: '#000000',
      selectionBackground: '#4e6a78',
      selectionForeground: '#e7ebed',
      black: '#435b67',
      red: '#fc3841',
      green: '#5cf19e',
      yellow: '#fed032',
      blue: '#37b6ff',
      magenta: '#fc226e',
      cyan: '#59ffd1',
      white: '#ffffff',
      brightBlack: '#a1b0b8',
      brightRed: '#fc746d',
      brightGreen: '#adf7be',
      brightYellow: '#fee16c',
      brightBlue: '#70cfff',
      brightMagenta: '#fc669b',
      brightCyan: '#9affe6',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'MaterialOcean',
    name: 'MaterialOcean',
    isPreset: true,
    themeData: {
      foreground: '#8f93a2',
      background: '#0f111a',
      cursor: '#ffcc00',
      cursorAccent: '#0f111a',
      selectionBackground: '#1f2233',
      selectionForeground: '#8f93a2',
      black: '#546e7a',
      red: '#ff5370',
      green: '#c3e88d',
      yellow: '#ffcb6b',
      blue: '#82aaff',
      magenta: '#c792ea',
      cyan: '#89ddff',
      white: '#ffffff',
      brightBlack: '#546e7a',
      brightRed: '#ff5370',
      brightGreen: '#c3e88d',
      brightYellow: '#ffcb6b',
      brightBlue: '#82aaff',
      brightMagenta: '#c792ea',
      brightCyan: '#89ddff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Mathias',
    name: 'Mathias',
    isPreset: true,
    themeData: {
      foreground: '#bbbbbb',
      background: '#000000',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#555555',
      selectionForeground: '#f2f2f2',
      black: '#000000',
      red: '#e52222',
      green: '#a6e32d',
      yellow: '#fc951e',
      blue: '#c48dff',
      magenta: '#fa2573',
      cyan: '#67d9f0',
      white: '#f2f2f2',
      brightBlack: '#555555',
      brightRed: '#ff5555',
      brightGreen: '#55ff55',
      brightYellow: '#ffff55',
      brightBlue: '#5555ff',
      brightMagenta: '#ff55ff',
      brightCyan: '#55ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'matrix',
    name: 'matrix',
    isPreset: true,
    themeData: {
      foreground: '#426644',
      background: '#0f191c',
      cursor: '#384545',
      cursorAccent: '#00ff00',
      selectionBackground: '#18282e',
      selectionForeground: '#00ff87',
      black: '#0f191c',
      red: '#23755a',
      green: '#82d967',
      yellow: '#ffd700',
      blue: '#3f5242',
      magenta: '#409931',
      cyan: '#50b45a',
      white: '#507350',
      brightBlack: '#688060',
      brightRed: '#2fc079',
      brightGreen: '#90d762',
      brightYellow: '#faff00',
      brightBlue: '#4f7e7e',
      brightMagenta: '#11ff25',
      brightCyan: '#c1ff8a',
      brightWhite: '#678c61',
    },
  },
  {
    preset_key: 'Medallion',
    name: 'Medallion',
    isPreset: true,
    themeData: {
      foreground: '#cac296',
      background: '#1d1908',
      cursor: '#d3ba30',
      cursorAccent: '#d2bc3d',
      selectionBackground: '#626dac',
      selectionForeground: '#cac29a',
      black: '#000000',
      red: '#b64c00',
      green: '#7c8b16',
      yellow: '#d3bd26',
      blue: '#616bb0',
      magenta: '#8c5a90',
      cyan: '#916c25',
      white: '#cac29a',
      brightBlack: '#5e5219',
      brightRed: '#ff9149',
      brightGreen: '#b2ca3b',
      brightYellow: '#ffe54a',
      brightBlue: '#acb8ff',
      brightMagenta: '#ffa0ff',
      brightCyan: '#ffbc51',
      brightWhite: '#fed698',
    },
  },
  {
    preset_key: 'Melange_dark',
    name: 'Melange_dark',
    isPreset: true,
    themeData: {
      foreground: '#ece1d7',
      background: '#292522',
      cursor: '#ece1d7',
      cursorAccent: '#292522',
      selectionBackground: '#ece1d7',
      selectionForeground: '#403a36',
      black: '#34302c',
      red: '#bd8183',
      green: '#78997a',
      yellow: '#e49b5d',
      blue: '#7f91b2',
      magenta: '#b380b0',
      cyan: '#7b9695',
      white: '#c1a78e',
      brightBlack: '#867462',
      brightRed: '#d47766',
      brightGreen: '#85b695',
      brightYellow: '#ebc06d',
      brightBlue: '#a3a9ce',
      brightMagenta: '#cf9bc2',
      brightCyan: '#89b3b6',
      brightWhite: '#ece1d7',
    },
  },
  {
    preset_key: 'Melange_light',
    name: 'Melange_light',
    isPreset: true,
    themeData: {
      foreground: '#54433a',
      background: '#f1f1f1',
      cursor: '#54433a',
      cursorAccent: '#f1f1f1',
      selectionBackground: '#54433a',
      selectionForeground: '#d9d3ce',
      black: '#e9e1db',
      red: '#c77b8b',
      green: '#6e9b72',
      yellow: '#bc5c00',
      blue: '#7892bd',
      magenta: '#be79bb',
      cyan: '#739797',
      white: '#7d6658',
      brightBlack: '#a98a78',
      brightRed: '#bf0021',
      brightGreen: '#3a684a',
      brightYellow: '#a06d00',
      brightBlue: '#465aa4',
      brightMagenta: '#904180',
      brightCyan: '#3d6568',
      brightWhite: '#54433a',
    },
  },
  {
    preset_key: 'Mellifluous',
    name: 'Mellifluous',
    isPreset: true,
    themeData: {
      foreground: '#dadada',
      background: '#1a1a1a',
      cursor: '#bfad9e',
      cursorAccent: '#1a1a1a',
      selectionBackground: '#2d2d2d',
      selectionForeground: '#c0af8c',
      black: '#1a1a1a',
      red: '#d29393',
      green: '#b3b393',
      yellow: '#cbaa89',
      blue: '#a8a1be',
      magenta: '#b39fb0',
      cyan: '#c0af8c',
      white: '#dadada',
      brightBlack: '#5b5b5b',
      brightRed: '#c95954',
      brightGreen: '#828040',
      brightYellow: '#a6794c',
      brightBlue: '#5a6599',
      brightMagenta: '#9c6995',
      brightCyan: '#74a39e',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'mellow',
    name: 'mellow',
    isPreset: true,
    themeData: {
      foreground: '#c9c7cd',
      background: '#161617',
      cursor: '#cac9dd',
      cursorAccent: '#161617',
      selectionBackground: '#2a2a2d',
      selectionForeground: '#c1c0d4',
      black: '#27272a',
      red: '#f5a191',
      green: '#90b99f',
      yellow: '#e6b99d',
      blue: '#aca1cf',
      magenta: '#e29eca',
      cyan: '#ea83a5',
      white: '#c1c0d4',
      brightBlack: '#353539',
      brightRed: '#ffae9f',
      brightGreen: '#9dc6ac',
      brightYellow: '#f0c5a9',
      brightBlue: '#b9aeda',
      brightMagenta: '#ecaad6',
      brightCyan: '#f591b2',
      brightWhite: '#cac9dd',
    },
  },
  {
    preset_key: 'miasma',
    name: 'miasma',
    isPreset: true,
    themeData: {
      foreground: '#c2c2b0',
      background: '#222222',
      cursor: '#c7c7c7',
      cursorAccent: '#eeeeee',
      selectionBackground: '#e5c47b',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#685742',
      green: '#5f875f',
      yellow: '#b36d43',
      blue: '#78824b',
      magenta: '#bb7744',
      cyan: '#c9a554',
      white: '#d7c483',
      brightBlack: '#666666',
      brightRed: '#685742',
      brightGreen: '#5f875f',
      brightYellow: '#b36d43',
      brightBlue: '#78824b',
      brightMagenta: '#bb7744',
      brightCyan: '#c9a554',
      brightWhite: '#d7c483',
    },
  },
  {
    preset_key: 'midnight-in-mojave',
    name: 'midnight-in-mojave',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#1e1e1e',
      cursor: '#32d74b',
      cursorAccent: '#1c1c1c',
      selectionBackground: '#4a504d',
      selectionForeground: '#ffffff',
      black: '#1e1e1e',
      red: '#ff453a',
      green: '#32d74b',
      yellow: '#ffd60a',
      blue: '#0a84ff',
      magenta: '#bf5af2',
      cyan: '#5ac8fa',
      white: '#ffffff',
      brightBlack: '#1e1e1e',
      brightRed: '#ff453a',
      brightGreen: '#32d74b',
      brightYellow: '#ffd60a',
      brightBlue: '#0a84ff',
      brightMagenta: '#bf5af2',
      brightCyan: '#5ac8fa',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Mirage',
    name: 'Mirage',
    isPreset: true,
    themeData: {
      foreground: '#a6b2c0',
      background: '#1b2738',
      cursor: '#ddb3ff',
      cursorAccent: '#ffffff',
      selectionBackground: '#273951',
      selectionForeground: '#d3dbe5',
      black: '#011627',
      red: '#ff9999',
      green: '#85cc95',
      yellow: '#ffd700',
      blue: '#7fb5ff',
      magenta: '#ddb3ff',
      cyan: '#21c7a8',
      white: '#ffffff',
      brightBlack: '#575656',
      brightRed: '#ff9999',
      brightGreen: '#85cc95',
      brightYellow: '#ffd700',
      brightBlue: '#7fb5ff',
      brightMagenta: '#ddb3ff',
      brightCyan: '#85cc95',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Misterioso',
    name: 'Misterioso',
    isPreset: true,
    themeData: {
      foreground: '#e1e1e0',
      background: '#2d3743',
      cursor: '#000000',
      cursorAccent: '#ffffff',
      selectionBackground: '#2d37ff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#ff4242',
      green: '#74af68',
      yellow: '#ffad29',
      blue: '#338f86',
      magenta: '#9414e6',
      cyan: '#23d7d7',
      white: '#e1e1e0',
      brightBlack: '#555555',
      brightRed: '#ff3242',
      brightGreen: '#74cd68',
      brightYellow: '#ffb929',
      brightBlue: '#23d7d7',
      brightMagenta: '#ff37ff',
      brightCyan: '#00ede1',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Molokai',
    name: 'Molokai',
    isPreset: true,
    themeData: {
      foreground: '#bbbbbb',
      background: '#121212',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#121212',
      red: '#fa2573',
      green: '#98e123',
      yellow: '#dfd460',
      blue: '#1080d0',
      magenta: '#8700ff',
      cyan: '#43a8d0',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#f6669d',
      brightGreen: '#b1e05f',
      brightYellow: '#fff26d',
      brightBlue: '#00afff',
      brightMagenta: '#af87ff',
      brightCyan: '#51ceff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'MonaLisa',
    name: 'MonaLisa',
    isPreset: true,
    themeData: {
      foreground: '#f7d66a',
      background: '#120b0d',
      cursor: '#c46c32',
      cursorAccent: '#120b0d',
      selectionBackground: '#f7d66a',
      selectionForeground: '#120b0d',
      black: '#351b0e',
      red: '#9b291c',
      green: '#636232',
      yellow: '#c36e28',
      blue: '#515c5d',
      magenta: '#9b1d29',
      cyan: '#588056',
      white: '#f7d75c',
      brightBlack: '#874228',
      brightRed: '#ff4331',
      brightGreen: '#b4b264',
      brightYellow: '#ff9566',
      brightBlue: '#9eb2b4',
      brightMagenta: '#ff5b6a',
      brightCyan: '#8acd8f',
      brightWhite: '#ffe598',
    },
  },
  {
    preset_key: 'Monokai_Classic',
    name: 'Monokai Classic',
    isPreset: true,
    themeData: {
      foreground: '#fdfff1',
      background: '#272822',
      cursor: '#c0c1b5',
      cursorAccent: '#c0c1b5',
      selectionBackground: '#57584f',
      selectionForeground: '#fdfff1',
      black: '#272822',
      red: '#f92672',
      green: '#a6e22e',
      yellow: '#e6db74',
      blue: '#fd971f',
      magenta: '#ae81ff',
      cyan: '#66d9ef',
      white: '#fdfff1',
      brightBlack: '#6e7066',
      brightRed: '#f92672',
      brightGreen: '#a6e22e',
      brightYellow: '#e6db74',
      brightBlue: '#fd971f',
      brightMagenta: '#ae81ff',
      brightCyan: '#66d9ef',
      brightWhite: '#fdfff1',
    },
  },
  {
    preset_key: 'Monokai_Pro',
    name: 'Monokai Pro',
    isPreset: true,
    themeData: {
      foreground: '#fcfcfa',
      background: '#2d2a2e',
      cursor: '#c1c0c0',
      cursorAccent: '#c1c0c0',
      selectionBackground: '#5b595c',
      selectionForeground: '#fcfcfa',
      black: '#2d2a2e',
      red: '#ff6188',
      green: '#a9dc76',
      yellow: '#ffd866',
      blue: '#fc9867',
      magenta: '#ab9df2',
      cyan: '#78dce8',
      white: '#fcfcfa',
      brightBlack: '#727072',
      brightRed: '#ff6188',
      brightGreen: '#a9dc76',
      brightYellow: '#ffd866',
      brightBlue: '#fc9867',
      brightMagenta: '#ab9df2',
      brightCyan: '#78dce8',
      brightWhite: '#fcfcfa',
    },
  },
  {
    preset_key: 'Monokai_Pro_Light',
    name: 'Monokai Pro Light',
    isPreset: true,
    themeData: {
      foreground: '#29242a',
      background: '#faf4f2',
      cursor: '#706b6e',
      cursorAccent: '#706b6e',
      selectionBackground: '#bfb9ba',
      selectionForeground: '#29242a',
      black: '#faf4f2',
      red: '#e14775',
      green: '#269d69',
      yellow: '#cc7a0a',
      blue: '#e16032',
      magenta: '#7058be',
      cyan: '#1c8ca8',
      white: '#29242a',
      brightBlack: '#a59fa0',
      brightRed: '#e14775',
      brightGreen: '#269d69',
      brightYellow: '#cc7a0a',
      brightBlue: '#e16032',
      brightMagenta: '#7058be',
      brightCyan: '#1c8ca8',
      brightWhite: '#29242a',
    },
  },
  {
    preset_key: 'Monokai_Pro_Light_Sun',
    name: 'Monokai Pro Light Sun',
    isPreset: true,
    themeData: {
      foreground: '#2c232e',
      background: '#f8efe7',
      cursor: '#72696d',
      cursorAccent: '#72696d',
      selectionBackground: '#beb5b3',
      selectionForeground: '#2c232e',
      black: '#f8efe7',
      red: '#ce4770',
      green: '#218871',
      yellow: '#b16803',
      blue: '#d4572b',
      magenta: '#6851a2',
      cyan: '#2473b6',
      white: '#2c232e',
      brightBlack: '#a59c9c',
      brightRed: '#ce4770',
      brightGreen: '#218871',
      brightYellow: '#b16803',
      brightBlue: '#d4572b',
      brightMagenta: '#6851a2',
      brightCyan: '#2473b6',
      brightWhite: '#2c232e',
    },
  },
  {
    preset_key: 'Monokai_Pro_Machine',
    name: 'Monokai Pro Machine',
    isPreset: true,
    themeData: {
      foreground: '#f2fffc',
      background: '#273136',
      cursor: '#b8c4c3',
      cursorAccent: '#b8c4c3',
      selectionBackground: '#545f62',
      selectionForeground: '#f2fffc',
      black: '#273136',
      red: '#ff6d7e',
      green: '#a2e57b',
      yellow: '#ffed72',
      blue: '#ffb270',
      magenta: '#baa0f8',
      cyan: '#7cd5f1',
      white: '#f2fffc',
      brightBlack: '#6b7678',
      brightRed: '#ff6d7e',
      brightGreen: '#a2e57b',
      brightYellow: '#ffed72',
      brightBlue: '#ffb270',
      brightMagenta: '#baa0f8',
      brightCyan: '#7cd5f1',
      brightWhite: '#f2fffc',
    },
  },
  {
    preset_key: 'Monokai_Pro_Octagon',
    name: 'Monokai Pro Octagon',
    isPreset: true,
    themeData: {
      foreground: '#eaf2f1',
      background: '#282a3a',
      cursor: '#b2b9bd',
      cursorAccent: '#b2b9bd',
      selectionBackground: '#535763',
      selectionForeground: '#eaf2f1',
      black: '#282a3a',
      red: '#ff657a',
      green: '#bad761',
      yellow: '#ffd76d',
      blue: '#ff9b5e',
      magenta: '#c39ac9',
      cyan: '#9cd1bb',
      white: '#eaf2f1',
      brightBlack: '#696d77',
      brightRed: '#ff657a',
      brightGreen: '#bad761',
      brightYellow: '#ffd76d',
      brightBlue: '#ff9b5e',
      brightMagenta: '#c39ac9',
      brightCyan: '#9cd1bb',
      brightWhite: '#eaf2f1',
    },
  },
  {
    preset_key: 'Monokai_Pro_Ristretto',
    name: 'Monokai Pro Ristretto',
    isPreset: true,
    themeData: {
      foreground: '#fff1f3',
      background: '#2c2525',
      cursor: '#c3b7b8',
      cursorAccent: '#c3b7b8',
      selectionBackground: '#5b5353',
      selectionForeground: '#fff1f3',
      black: '#2c2525',
      red: '#fd6883',
      green: '#adda78',
      yellow: '#f9cc6c',
      blue: '#f38d70',
      magenta: '#a8a9eb',
      cyan: '#85dacc',
      white: '#fff1f3',
      brightBlack: '#72696a',
      brightRed: '#fd6883',
      brightGreen: '#adda78',
      brightYellow: '#f9cc6c',
      brightBlue: '#f38d70',
      brightMagenta: '#a8a9eb',
      brightCyan: '#85dacc',
      brightWhite: '#fff1f3',
    },
  },
  {
    preset_key: 'Monokai_Pro_Spectrum',
    name: 'Monokai Pro Spectrum',
    isPreset: true,
    themeData: {
      foreground: '#f7f1ff',
      background: '#222222',
      cursor: '#bab6c0',
      cursorAccent: '#bab6c0',
      selectionBackground: '#525053',
      selectionForeground: '#f7f1ff',
      black: '#222222',
      red: '#fc618d',
      green: '#7bd88f',
      yellow: '#fce566',
      blue: '#fd9353',
      magenta: '#948ae3',
      cyan: '#5ad4e6',
      white: '#f7f1ff',
      brightBlack: '#69676c',
      brightRed: '#fc618d',
      brightGreen: '#7bd88f',
      brightYellow: '#fce566',
      brightBlue: '#fd9353',
      brightMagenta: '#948ae3',
      brightCyan: '#5ad4e6',
      brightWhite: '#f7f1ff',
    },
  },
  {
    preset_key: 'Monokai_Remastered',
    name: 'Monokai Remastered',
    isPreset: true,
    themeData: {
      foreground: '#d9d9d9',
      background: '#0c0c0c',
      cursor: '#fc971f',
      cursorAccent: '#000000',
      selectionBackground: '#343434',
      selectionForeground: '#ffffff',
      black: '#1a1a1a',
      red: '#f4005f',
      green: '#98e024',
      yellow: '#fd971f',
      blue: '#9d65ff',
      magenta: '#f4005f',
      cyan: '#58d1eb',
      white: '#c4c5b5',
      brightBlack: '#625e4c',
      brightRed: '#f4005f',
      brightGreen: '#98e024',
      brightYellow: '#e0d561',
      brightBlue: '#9d65ff',
      brightMagenta: '#f4005f',
      brightCyan: '#58d1eb',
      brightWhite: '#f6f6ef',
    },
  },
  {
    preset_key: 'Monokai_Soda',
    name: 'Monokai Soda',
    isPreset: true,
    themeData: {
      foreground: '#c4c5b5',
      background: '#1a1a1a',
      cursor: '#f6f7ec',
      cursorAccent: '#c4c5b5',
      selectionBackground: '#343434',
      selectionForeground: '#c4c5b5',
      black: '#1a1a1a',
      red: '#f4005f',
      green: '#98e024',
      yellow: '#fa8419',
      blue: '#9d65ff',
      magenta: '#f4005f',
      cyan: '#58d1eb',
      white: '#c4c5b5',
      brightBlack: '#625e4c',
      brightRed: '#f4005f',
      brightGreen: '#98e024',
      brightYellow: '#e0d561',
      brightBlue: '#9d65ff',
      brightMagenta: '#f4005f',
      brightCyan: '#58d1eb',
      brightWhite: '#f6f6ef',
    },
  },
  {
    preset_key: 'Monokai_Vivid',
    name: 'Monokai Vivid',
    isPreset: true,
    themeData: {
      foreground: '#f9f9f9',
      background: '#121212',
      cursor: '#fb0007',
      cursorAccent: '#ea0009',
      selectionBackground: '#ffffff',
      selectionForeground: '#000000',
      black: '#121212',
      red: '#fa2934',
      green: '#98e123',
      yellow: '#fff30a',
      blue: '#0443ff',
      magenta: '#f800f8',
      cyan: '#01b6ed',
      white: '#ffffff',
      brightBlack: '#838383',
      brightRed: '#f6669d',
      brightGreen: '#b1e05f',
      brightYellow: '#fff26d',
      brightBlue: '#0443ff',
      brightMagenta: '#f200f6',
      brightCyan: '#51ceff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'moonfly',
    name: 'moonfly',
    isPreset: true,
    themeData: {
      foreground: '#bdbdbd',
      background: '#080808',
      cursor: '#9e9e9e',
      cursorAccent: '#080808',
      selectionBackground: '#b2ceee',
      selectionForeground: '#080808',
      black: '#323437',
      red: '#ff5454',
      green: '#8cc85f',
      yellow: '#e3c78a',
      blue: '#80a0ff',
      magenta: '#cf87e8',
      cyan: '#79dac8',
      white: '#c6c6c6',
      brightBlack: '#949494',
      brightRed: '#ff5189',
      brightGreen: '#36c692',
      brightYellow: '#c6c684',
      brightBlue: '#74b2ff',
      brightMagenta: '#ae81ff',
      brightCyan: '#85dc85',
      brightWhite: '#e4e4e4',
    },
  },
  {
    preset_key: 'N0tch2k',
    name: 'N0tch2k',
    isPreset: true,
    themeData: {
      foreground: '#a0a0a0',
      background: '#222222',
      cursor: '#aa9175',
      cursorAccent: '#000000',
      selectionBackground: '#4d4d4d',
      selectionForeground: '#ffffff',
      black: '#383838',
      red: '#a95551',
      green: '#666666',
      yellow: '#a98051',
      blue: '#657d3e',
      magenta: '#767676',
      cyan: '#c9c9c9',
      white: '#d0b8a3',
      brightBlack: '#474747',
      brightRed: '#a97775',
      brightGreen: '#8c8c8c',
      brightYellow: '#a99175',
      brightBlue: '#98bd5e',
      brightMagenta: '#a3a3a3',
      brightCyan: '#dcdcdc',
      brightWhite: '#d8c8bb',
    },
  },
  {
    preset_key: 'neobones_dark',
    name: 'neobones_dark',
    isPreset: true,
    themeData: {
      foreground: '#c6d5cf',
      background: '#0f191f',
      cursor: '#ceddd7',
      cursorAccent: '#0f191f',
      selectionBackground: '#3a3e3d',
      selectionForeground: '#c6d5cf',
      black: '#0f191f',
      red: '#de6e7c',
      green: '#90ff6b',
      yellow: '#b77e64',
      blue: '#8190d4',
      magenta: '#b279a7',
      cyan: '#66a5ad',
      white: '#c6d5cf',
      brightBlack: '#263945',
      brightRed: '#e8838f',
      brightGreen: '#a0ff85',
      brightYellow: '#d68c67',
      brightBlue: '#92a0e2',
      brightMagenta: '#cf86c1',
      brightCyan: '#65b8c1',
      brightWhite: '#98a39e',
    },
  },
  {
    preset_key: 'neobones_light',
    name: 'neobones_light',
    isPreset: true,
    themeData: {
      foreground: '#202e18',
      background: '#e5ede6',
      cursor: '#202e18',
      cursorAccent: '#e5ede6',
      selectionBackground: '#ade48c',
      selectionForeground: '#202e18',
      black: '#e5ede6',
      red: '#a8334c',
      green: '#567a30',
      yellow: '#944927',
      blue: '#286486',
      magenta: '#88507d',
      cyan: '#3b8992',
      white: '#202e18',
      brightBlack: '#b3c6b6',
      brightRed: '#94253e',
      brightGreen: '#3f5a22',
      brightYellow: '#803d1c',
      brightBlue: '#1d5573',
      brightMagenta: '#7b3b70',
      brightCyan: '#2b747c',
      brightWhite: '#415934',
    },
  },
  {
    preset_key: 'Neon',
    name: 'Neon',
    isPreset: true,
    themeData: {
      foreground: '#00fffc',
      background: '#14161a',
      cursor: '#c7c7c7',
      cursorAccent: '#ffffff',
      selectionBackground: '#0013ff',
      selectionForeground: '#08d2cf',
      black: '#000000',
      red: '#ff3045',
      green: '#5ffa74',
      yellow: '#fffc7e',
      blue: '#0208cb',
      magenta: '#f924e7',
      cyan: '#00fffc',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#ff5a5a',
      brightGreen: '#75ff88',
      brightYellow: '#fffd96',
      brightBlue: '#3c40cb',
      brightMagenta: '#f15be5',
      brightCyan: '#88fffe',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Neopolitan',
    name: 'Neopolitan',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#271f19',
      cursor: '#ffffff',
      cursorAccent: '#ffffff',
      selectionBackground: '#253b76',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#800000',
      green: '#61ce3c',
      yellow: '#fbde2d',
      blue: '#253b76',
      magenta: '#ff0080',
      cyan: '#8da6ce',
      white: '#f8f8f8',
      brightBlack: '#000000',
      brightRed: '#800000',
      brightGreen: '#61ce3c',
      brightYellow: '#fbde2d',
      brightBlue: '#253b76',
      brightMagenta: '#ff0080',
      brightCyan: '#8da6ce',
      brightWhite: '#f8f8f8',
    },
  },
  {
    preset_key: 'Neutron',
    name: 'Neutron',
    isPreset: true,
    themeData: {
      foreground: '#e6e8ef',
      background: '#1c1e22',
      cursor: '#f6f7ec',
      cursorAccent: '#c4c5b5',
      selectionBackground: '#2f363e',
      selectionForeground: '#7d8fa4',
      black: '#23252b',
      red: '#b54036',
      green: '#5ab977',
      yellow: '#deb566',
      blue: '#6a7c93',
      magenta: '#a4799d',
      cyan: '#3f94a8',
      white: '#e6e8ef',
      brightBlack: '#23252b',
      brightRed: '#b54036',
      brightGreen: '#5ab977',
      brightYellow: '#deb566',
      brightBlue: '#6a7c93',
      brightMagenta: '#a4799d',
      brightCyan: '#3f94a8',
      brightWhite: '#ebedf2',
    },
  },
  {
    preset_key: 'Night_Owlish_Light',
    name: 'Night Owlish Light',
    isPreset: true,
    themeData: {
      foreground: '#403f53',
      background: '#ffffff',
      cursor: '#403f53',
      cursorAccent: '#fbfbfb',
      selectionBackground: '#f2f2f2',
      selectionForeground: '#403f53',
      black: '#011627',
      red: '#d3423e',
      green: '#2aa298',
      yellow: '#daaa01',
      blue: '#4876d6',
      magenta: '#403f53',
      cyan: '#08916a',
      white: '#7a8181',
      brightBlack: '#7a8181',
      brightRed: '#f76e6e',
      brightGreen: '#49d0c5',
      brightYellow: '#dac26b',
      brightBlue: '#5ca7e4',
      brightMagenta: '#697098',
      brightCyan: '#00c990',
      brightWhite: '#989fb1',
    },
  },
  {
    preset_key: 'nightfox',
    name: 'nightfox',
    isPreset: true,
    themeData: {
      foreground: '#cdcecf',
      background: '#192330',
      cursor: '#cdcecf',
      cursorAccent: '#192330',
      selectionBackground: '#2b3b51',
      selectionForeground: '#cdcecf',
      black: '#393b44',
      red: '#c94f6d',
      green: '#81b29a',
      yellow: '#dbc074',
      blue: '#719cd6',
      magenta: '#9d79d6',
      cyan: '#63cdcf',
      white: '#dfdfe0',
      brightBlack: '#575860',
      brightRed: '#d16983',
      brightGreen: '#8ebaa4',
      brightYellow: '#e0c989',
      brightBlue: '#86abdc',
      brightMagenta: '#baa1e2',
      brightCyan: '#7ad5d6',
      brightWhite: '#e4e4e5',
    },
  },
  {
    preset_key: 'NightLion_v1',
    name: 'NightLion v1',
    isPreset: true,
    themeData: {
      foreground: '#bbbbbb',
      background: '#000000',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#4c4c4c',
      red: '#bb0000',
      green: '#5fde8f',
      yellow: '#f3f167',
      blue: '#276bd8',
      magenta: '#bb00bb',
      cyan: '#00dadf',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#ff5555',
      brightGreen: '#55ff55',
      brightYellow: '#ffff55',
      brightBlue: '#5555ff',
      brightMagenta: '#ff55ff',
      brightCyan: '#55ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'NightLion_v2',
    name: 'NightLion v2',
    isPreset: true,
    themeData: {
      foreground: '#bbbbbb',
      background: '#171717',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#4c4c4c',
      red: '#bb0000',
      green: '#04f623',
      yellow: '#f3f167',
      blue: '#64d0f0',
      magenta: '#ce6fdb',
      cyan: '#00dadf',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#ff5555',
      brightGreen: '#7df71d',
      brightYellow: '#ffff55',
      brightBlue: '#62cbe8',
      brightMagenta: '#ff9bf5',
      brightCyan: '#00ccd8',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'NightOwl',
    name: 'NightOwl',
    isPreset: true,
    themeData: {
      foreground: '#d6deeb',
      background: '#011627',
      cursor: '#7e57c2',
      cursorAccent: '#ffffff',
      selectionBackground: '#5f7e97',
      selectionForeground: '#dfe5ee',
      black: '#011627',
      red: '#ef5350',
      green: '#22da6e',
      yellow: '#addb67',
      blue: '#82aaff',
      magenta: '#c792ea',
      cyan: '#21c7a8',
      white: '#ffffff',
      brightBlack: '#575656',
      brightRed: '#ef5350',
      brightGreen: '#22da6e',
      brightYellow: '#ffeb95',
      brightBlue: '#82aaff',
      brightMagenta: '#c792ea',
      brightCyan: '#7fdbca',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'niji',
    name: 'niji',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#141515',
      cursor: '#ffc663',
      cursorAccent: '#141515',
      selectionBackground: '#515151',
      selectionForeground: '#ffc663',
      black: '#333333',
      red: '#d23e08',
      green: '#54ca74',
      yellow: '#fff700',
      blue: '#2ab9ff',
      magenta: '#ff50da',
      cyan: '#1ef9f5',
      white: '#ddd0c4',
      brightBlack: '#515151',
      brightRed: '#ffb7b7',
      brightGreen: '#c1ffae',
      brightYellow: '#fcffb8',
      brightBlue: '#8efff3',
      brightMagenta: '#ffa2ed',
      brightCyan: '#bcffc7',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Nocturnal_Winter',
    name: 'Nocturnal Winter',
    isPreset: true,
    themeData: {
      foreground: '#e6e5e5',
      background: '#0d0d17',
      cursor: '#e6e5e5',
      cursorAccent: '#ffffff',
      selectionBackground: '#adbdd0',
      selectionForeground: '#000000',
      black: '#4d4d4d',
      red: '#f12d52',
      green: '#09cd7e',
      yellow: '#f5f17a',
      blue: '#3182e0',
      magenta: '#ff2b6d',
      cyan: '#09c87a',
      white: '#fcfcfc',
      brightBlack: '#808080',
      brightRed: '#f16d86',
      brightGreen: '#0ae78d',
      brightYellow: '#fffc67',
      brightBlue: '#6096ff',
      brightMagenta: '#ff78a2',
      brightCyan: '#0ae78d',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'nord',
    name: 'nord',
    isPreset: true,
    themeData: {
      foreground: '#d8dee9',
      background: '#2e3440',
      cursor: '#eceff4',
      cursorAccent: '#282828',
      selectionBackground: '#eceff4',
      selectionForeground: '#4c566a',
      black: '#3b4252',
      red: '#bf616a',
      green: '#a3be8c',
      yellow: '#ebcb8b',
      blue: '#81a1c1',
      magenta: '#b48ead',
      cyan: '#88c0d0',
      white: '#e5e9f0',
      brightBlack: '#4c566a',
      brightRed: '#bf616a',
      brightGreen: '#a3be8c',
      brightYellow: '#ebcb8b',
      brightBlue: '#81a1c1',
      brightMagenta: '#b48ead',
      brightCyan: '#8fbcbb',
      brightWhite: '#eceff4',
    },
  },
  {
    preset_key: 'nord-light',
    name: 'nord-light',
    isPreset: true,
    themeData: {
      foreground: '#414858',
      background: '#e5e9f0',
      cursor: '#88c0d0',
      cursorAccent: '#3b4252',
      selectionBackground: '#d8dee9',
      selectionForeground: '#4c556a',
      black: '#3b4252',
      red: '#bf616a',
      green: '#a3be8c',
      yellow: '#ebcb8b',
      blue: '#81a1c1',
      magenta: '#b48ead',
      cyan: '#88c0d0',
      white: '#d8dee9',
      brightBlack: '#4c566a',
      brightRed: '#bf616a',
      brightGreen: '#a3be8c',
      brightYellow: '#ebcb8b',
      brightBlue: '#81a1c1',
      brightMagenta: '#b48ead',
      brightCyan: '#8fbcbb',
      brightWhite: '#eceff4',
    },
  },
  {
    preset_key: 'nord-wave',
    name: 'nord-wave',
    isPreset: true,
    themeData: {
      foreground: '#d8dee9',
      background: '#212121',
      cursor: '#ebcb8b',
      cursorAccent: '#ebcb8b',
      selectionBackground: '#d8dee9',
      selectionForeground: '#212121',
      black: '#3b4252',
      red: '#bf616a',
      green: '#a3be8c',
      yellow: '#ebcb8b',
      blue: '#81a1c1',
      magenta: '#b48ead',
      cyan: '#88c0d0',
      white: '#e5e9f0',
      brightBlack: '#4c566a',
      brightRed: '#bf616a',
      brightGreen: '#a3be8c',
      brightYellow: '#ebcb8b',
      brightBlue: '#81a1c1',
      brightMagenta: '#b48ead',
      brightCyan: '#8fbcbb',
      brightWhite: '#eceff4',
    },
  },
  {
    preset_key: 'Novel',
    name: 'Novel',
    isPreset: true,
    themeData: {
      foreground: '#3b2322',
      background: '#dfdbc3',
      cursor: '#73635a',
      cursorAccent: '#000000',
      selectionBackground: '#a4a390',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#cc0000',
      green: '#009600',
      yellow: '#d06b00',
      blue: '#0000cc',
      magenta: '#cc00cc',
      cyan: '#0087cc',
      white: '#cccccc',
      brightBlack: '#808080',
      brightRed: '#cc0000',
      brightGreen: '#009600',
      brightYellow: '#d06b00',
      brightBlue: '#0000cc',
      brightMagenta: '#cc00cc',
      brightCyan: '#0087cc',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'NvimDark',
    name: 'NvimDark',
    isPreset: true,
    themeData: {
      foreground: '#e0e2ea',
      background: '#14161b',
      cursor: '#9b9ea4',
      cursorAccent: '#e0e2ea',
      selectionBackground: '#4f5258',
      selectionForeground: '#e0e2ea',
      black: '#07080d',
      red: '#ffc0b9',
      green: '#b3f6c0',
      yellow: '#fce094',
      blue: '#a6dbff',
      magenta: '#ffcaff',
      cyan: '#8cf8f7',
      white: '#eef1f8',
      brightBlack: '#4f5258',
      brightRed: '#ffc0b9',
      brightGreen: '#b3f6c0',
      brightYellow: '#fce094',
      brightBlue: '#a6dbff',
      brightMagenta: '#ffcaff',
      brightCyan: '#8cf8f7',
      brightWhite: '#eef1f8',
    },
  },
  {
    preset_key: 'NvimLight',
    name: 'NvimLight',
    isPreset: true,
    themeData: {
      foreground: '#14161b',
      background: '#e0e2ea',
      cursor: '#9b9ea4',
      cursorAccent: '#14161b',
      selectionBackground: '#9b9ea4',
      selectionForeground: '#14161b',
      black: '#07080d',
      red: '#590008',
      green: '#005523',
      yellow: '#6b5300',
      blue: '#004c73',
      magenta: '#470045',
      cyan: '#007373',
      white: '#eef1f8',
      brightBlack: '#4f5258',
      brightRed: '#590008',
      brightGreen: '#005523',
      brightYellow: '#6b5300',
      brightBlue: '#004c73',
      brightMagenta: '#470045',
      brightCyan: '#007373',
      brightWhite: '#eef1f8',
    },
  },
  {
    preset_key: 'Obsidian',
    name: 'Obsidian',
    isPreset: true,
    themeData: {
      foreground: '#cdcdcd',
      background: '#283033',
      cursor: '#c0cad0',
      cursorAccent: '#cdcdcd',
      selectionBackground: '#3e4c4f',
      selectionForeground: '#dfe1e2',
      black: '#000000',
      red: '#a60001',
      green: '#00bb00',
      yellow: '#fecd22',
      blue: '#3a9bdb',
      magenta: '#bb00bb',
      cyan: '#00bbbb',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#ff0003',
      brightGreen: '#93c863',
      brightYellow: '#fef874',
      brightBlue: '#a1d7ff',
      brightMagenta: '#ff55ff',
      brightCyan: '#55ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Ocean',
    name: 'Ocean',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#224fbc',
      cursor: '#7f7f7f',
      cursorAccent: '#ffffff',
      selectionBackground: '#216dff',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#990000',
      green: '#00a600',
      yellow: '#999900',
      blue: '#0000b2',
      magenta: '#b200b2',
      cyan: '#00a6b2',
      white: '#bfbfbf',
      brightBlack: '#666666',
      brightRed: '#e50000',
      brightGreen: '#00d900',
      brightYellow: '#e5e500',
      brightBlue: '#0000ff',
      brightMagenta: '#e500e5',
      brightCyan: '#00e5e5',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'Oceanic-Next',
    name: 'Oceanic-Next',
    isPreset: true,
    themeData: {
      foreground: '#c1c5cd',
      background: '#1b2b34',
      cursor: '#c1c5cd',
      cursorAccent: '#1e2b33',
      selectionBackground: '#515b65',
      selectionForeground: '#c1c5cd',
      black: '#1b2b34',
      red: '#db686b',
      green: '#a2c699',
      yellow: '#f2ca73',
      blue: '#7198c8',
      magenta: '#bd96c2',
      cyan: '#74b1b2',
      white: '#ffffff',
      brightBlack: '#68737d',
      brightRed: '#db686b',
      brightGreen: '#a2c699',
      brightYellow: '#f2ca73',
      brightBlue: '#7198c8',
      brightMagenta: '#bd96c2',
      brightCyan: '#74b1b2',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'OceanicMaterial',
    name: 'OceanicMaterial',
    isPreset: true,
    themeData: {
      foreground: '#c2c8d7',
      background: '#1c262b',
      cursor: '#b3b8c3',
      cursorAccent: '#ffffff',
      selectionBackground: '#6dc2b8',
      selectionForeground: '#c2c8d7',
      black: '#000000',
      red: '#ee2b2a',
      green: '#40a33f',
      yellow: '#ffea2e',
      blue: '#1e80f0',
      magenta: '#8800a0',
      cyan: '#16afca',
      white: '#a4a4a4',
      brightBlack: '#777777',
      brightRed: '#dc5c60',
      brightGreen: '#70be71',
      brightYellow: '#fff163',
      brightBlue: '#54a4f3',
      brightMagenta: '#aa4dbc',
      brightCyan: '#42c7da',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Ollie',
    name: 'Ollie',
    isPreset: true,
    themeData: {
      foreground: '#8a8dae',
      background: '#222125',
      cursor: '#5b6ea7',
      cursorAccent: '#2a292d',
      selectionBackground: '#1e3a66',
      selectionForeground: '#8a8eac',
      black: '#000000',
      red: '#ac2e31',
      green: '#31ac61',
      yellow: '#ac4300',
      blue: '#2d57ac',
      magenta: '#b08528',
      cyan: '#1fa6ac',
      white: '#8a8eac',
      brightBlack: '#5b3725',
      brightRed: '#ff3d48',
      brightGreen: '#3bff99',
      brightYellow: '#ff5e1e',
      brightBlue: '#4488ff',
      brightMagenta: '#ffc21d',
      brightCyan: '#1ffaff',
      brightWhite: '#5b6ea7',
    },
  },
  {
    preset_key: 'One_Double_Dark',
    name: 'One Double Dark',
    isPreset: true,
    themeData: {
      foreground: '#dcdfe4',
      background: '#292c33',
      cursor: '#f1e1dd',
      cursorAccent: '#cfd6f1',
      selectionBackground: '#595b6e',
      selectionForeground: '#cfd6f1',
      black: '#3e4451',
      red: '#e06c75',
      green: '#98c379',
      yellow: '#e5c07b',
      blue: '#61afef',
      magenta: '#c678dd',
      cyan: '#56b6c2',
      white: '#dcdfe4',
      brightBlack: '#545d6d',
      brightRed: '#fd807f',
      brightGreen: '#96d58b',
      brightYellow: '#edc273',
      brightBlue: '#84c8ff',
      brightMagenta: '#ee82ee',
      brightCyan: '#3ae1f7',
      brightWhite: '#f7f9fc',
    },
  },
  {
    preset_key: 'One_Double_Light',
    name: 'One Double Light',
    isPreset: true,
    themeData: {
      foreground: '#383a42',
      background: '#fafafa',
      cursor: '#1a1a1a',
      cursorAccent: '#dcdfe4',
      selectionBackground: '#474e5d',
      selectionForeground: '#1a1a1a',
      black: '#464b57',
      red: '#e45649',
      green: '#50a14f',
      yellow: '#c18401',
      blue: '#0184bc',
      magenta: '#a626a4',
      cyan: '#0997b3',
      white: '#e8d9d9',
      brightBlack: '#0f131e',
      brightRed: '#f24c2d',
      brightGreen: '#3db637',
      brightYellow: '#e09d00',
      brightBlue: '#2e63d6',
      brightMagenta: '#d21fd1',
      brightCyan: '#06b1d8',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'OneHalfDark',
    name: 'OneHalfDark',
    isPreset: true,
    themeData: {
      foreground: '#dcdfe4',
      background: '#282c34',
      cursor: '#a3b3cc',
      cursorAccent: '#dcdfe4',
      selectionBackground: '#474e5d',
      selectionForeground: '#dcdfe4',
      black: '#282c34',
      red: '#e06c75',
      green: '#98c379',
      yellow: '#e5c07b',
      blue: '#61afef',
      magenta: '#c678dd',
      cyan: '#56b6c2',
      white: '#dcdfe4',
      brightBlack: '#282c34',
      brightRed: '#e06c75',
      brightGreen: '#98c379',
      brightYellow: '#e5c07b',
      brightBlue: '#61afef',
      brightMagenta: '#c678dd',
      brightCyan: '#56b6c2',
      brightWhite: '#dcdfe4',
    },
  },
  {
    preset_key: 'OneHalfLight',
    name: 'OneHalfLight',
    isPreset: true,
    themeData: {
      foreground: '#383a42',
      background: '#fafafa',
      cursor: '#bfceff',
      cursorAccent: '#383a42',
      selectionBackground: '#bfceff',
      selectionForeground: '#383a42',
      black: '#383a42',
      red: '#e45649',
      green: '#50a14f',
      yellow: '#c18401',
      blue: '#0184bc',
      magenta: '#a626a4',
      cyan: '#0997b3',
      white: '#fafafa',
      brightBlack: '#4f525e',
      brightRed: '#e06c75',
      brightGreen: '#98c379',
      brightYellow: '#e5c07b',
      brightBlue: '#61afef',
      brightMagenta: '#c678dd',
      brightCyan: '#56b6c2',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Operator_Mono_Dark',
    name: 'Operator Mono Dark',
    isPreset: true,
    themeData: {
      foreground: '#c3cac2',
      background: '#191919',
      cursor: '#fcdc08',
      cursorAccent: '#161616',
      selectionBackground: '#19273b',
      selectionForeground: '#dde5dc',
      black: '#5a5a5a',
      red: '#ca372d',
      green: '#4d7b3a',
      yellow: '#d4d697',
      blue: '#4387cf',
      magenta: '#b86cb4',
      cyan: '#72d5c6',
      white: '#ced4cd',
      brightBlack: '#9a9b99',
      brightRed: '#c37d62',
      brightGreen: '#83d0a2',
      brightYellow: '#fdfdc5',
      brightBlue: '#89d3f6',
      brightMagenta: '#ff2c7a',
      brightCyan: '#82eada',
      brightWhite: '#fdfdf6',
    },
  },
  {
    preset_key: 'Overnight_Slumber',
    name: 'Overnight Slumber',
    isPreset: true,
    themeData: {
      foreground: '#ced2d6',
      background: '#0e1729',
      cursor: '#ffa7c4',
      cursorAccent: '#ffffff',
      selectionBackground: '#1f2b41',
      selectionForeground: '#ced2d6',
      black: '#0a1222',
      red: '#ffa7c4',
      green: '#85cc95',
      yellow: '#ffcb8b',
      blue: '#8dabe1',
      magenta: '#c792eb',
      cyan: '#78ccf0',
      white: '#ffffff',
      brightBlack: '#575656',
      brightRed: '#ffa7c4',
      brightGreen: '#85cc95',
      brightYellow: '#ffcb8b',
      brightBlue: '#8dabe1',
      brightMagenta: '#c792eb',
      brightCyan: '#ffa7c4',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Oxocarbon',
    name: 'Oxocarbon',
    isPreset: true,
    themeData: {
      foreground: '#f2f4f8',
      background: '#161616',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#393939',
      selectionForeground: '#161616',
      black: '#161616',
      red: '#3ddbd9',
      green: '#33b1ff',
      yellow: '#ee5396',
      blue: '#42be65',
      magenta: '#be95ff',
      cyan: '#ff7eb6',
      white: '#f2f4f8',
      brightBlack: '#585858',
      brightRed: '#3ddbd9',
      brightGreen: '#33b1ff',
      brightYellow: '#ee5396',
      brightBlue: '#42be65',
      brightMagenta: '#be95ff',
      brightCyan: '#ff7eb6',
      brightWhite: '#f2f4f8',
    },
  },
  {
    preset_key: 'PaleNightHC',
    name: 'PaleNightHC',
    isPreset: true,
    themeData: {
      foreground: '#cccccc',
      background: '#3e4251',
      cursor: '#ffcb6b',
      cursorAccent: '#323232',
      selectionBackground: '#717cb4',
      selectionForeground: '#80cbc4',
      black: '#000000',
      red: '#f07178',
      green: '#c3e88d',
      yellow: '#ffcb6b',
      blue: '#82aaff',
      magenta: '#c792ea',
      cyan: '#89ddff',
      white: '#ffffff',
      brightBlack: '#666666',
      brightRed: '#f6a9ae',
      brightGreen: '#dbf1ba',
      brightYellow: '#ffdfa6',
      brightBlue: '#b4ccff',
      brightMagenta: '#ddbdf2',
      brightCyan: '#b8eaff',
      brightWhite: '#999999',
    },
  },
  {
    preset_key: 'Pandora',
    name: 'Pandora',
    isPreset: true,
    themeData: {
      foreground: '#e1e1e1',
      background: '#141e43',
      cursor: '#43d58e',
      cursorAccent: '#ffffff',
      selectionBackground: '#2d37ff',
      selectionForeground: '#82e0ff',
      black: '#000000',
      red: '#ff4242',
      green: '#74af68',
      yellow: '#ffad29',
      blue: '#338f86',
      magenta: '#9414e6',
      cyan: '#23d7d7',
      white: '#e2e2e2',
      brightBlack: '#3f5648',
      brightRed: '#ff3242',
      brightGreen: '#74cd68',
      brightYellow: '#ffb929',
      brightBlue: '#23d7d7',
      brightMagenta: '#ff37ff',
      brightCyan: '#00ede1',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Paraiso_Dark',
    name: 'Paraiso Dark',
    isPreset: true,
    themeData: {
      foreground: '#a39e9b',
      background: '#2f1e2e',
      cursor: '#a39e9b',
      cursorAccent: '#2f1e2e',
      selectionBackground: '#4f424c',
      selectionForeground: '#a39e9b',
      black: '#2f1e2e',
      red: '#ef6155',
      green: '#48b685',
      yellow: '#fec418',
      blue: '#06b6ef',
      magenta: '#815ba4',
      cyan: '#5bc4bf',
      white: '#a39e9b',
      brightBlack: '#776e71',
      brightRed: '#ef6155',
      brightGreen: '#48b685',
      brightYellow: '#fec418',
      brightBlue: '#06b6ef',
      brightMagenta: '#815ba4',
      brightCyan: '#5bc4bf',
      brightWhite: '#e7e9db',
    },
  },
  {
    preset_key: 'PaulMillr',
    name: 'PaulMillr',
    isPreset: true,
    themeData: {
      foreground: '#f2f2f2',
      background: '#000000',
      cursor: '#4d4d4d',
      cursorAccent: '#ffffff',
      selectionBackground: '#414141',
      selectionForeground: '#ffffff',
      black: '#2a2a2a',
      red: '#ff0000',
      green: '#79ff0f',
      yellow: '#e7bf00',
      blue: '#396bd7',
      magenta: '#b449be',
      cyan: '#66ccff',
      white: '#bbbbbb',
      brightBlack: '#666666',
      brightRed: '#ff0080',
      brightGreen: '#66ff66',
      brightYellow: '#f3d64e',
      brightBlue: '#709aed',
      brightMagenta: '#db67e6',
      brightCyan: '#7adff2',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'PencilDark',
    name: 'PencilDark',
    isPreset: true,
    themeData: {
      foreground: '#f1f1f1',
      background: '#212121',
      cursor: '#20bbfc',
      cursorAccent: '#f1f1f1',
      selectionBackground: '#b6d6fd',
      selectionForeground: '#f1f1f1',
      black: '#212121',
      red: '#c30771',
      green: '#10a778',
      yellow: '#a89c14',
      blue: '#008ec4',
      magenta: '#523c79',
      cyan: '#20a5ba',
      white: '#d9d9d9',
      brightBlack: '#424242',
      brightRed: '#fb007a',
      brightGreen: '#5fd7af',
      brightYellow: '#f3e430',
      brightBlue: '#20bbfc',
      brightMagenta: '#6855de',
      brightCyan: '#4fb8cc',
      brightWhite: '#f1f1f1',
    },
  },
  {
    preset_key: 'PencilLight',
    name: 'PencilLight',
    isPreset: true,
    themeData: {
      foreground: '#424242',
      background: '#f1f1f1',
      cursor: '#20bbfc',
      cursorAccent: '#424242',
      selectionBackground: '#b6d6fd',
      selectionForeground: '#424242',
      black: '#212121',
      red: '#c30771',
      green: '#10a778',
      yellow: '#a89c14',
      blue: '#008ec4',
      magenta: '#523c79',
      cyan: '#20a5ba',
      white: '#d9d9d9',
      brightBlack: '#424242',
      brightRed: '#fb007a',
      brightGreen: '#5fd7af',
      brightYellow: '#f3e430',
      brightBlue: '#20bbfc',
      brightMagenta: '#6855de',
      brightCyan: '#4fb8cc',
      brightWhite: '#f1f1f1',
    },
  },
  {
    preset_key: 'Peppermint',
    name: 'Peppermint',
    isPreset: true,
    themeData: {
      foreground: '#c8c8c8',
      background: '#000000',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#e6e6e6',
      selectionForeground: '#000000',
      black: '#353535',
      red: '#e74669',
      green: '#89d287',
      yellow: '#dab853',
      blue: '#449fd0',
      magenta: '#da62dc',
      cyan: '#65aaaf',
      white: '#b4b4b4',
      brightBlack: '#535353',
      brightRed: '#e4859b',
      brightGreen: '#a3cca2',
      brightYellow: '#e1e487',
      brightBlue: '#6fbce2',
      brightMagenta: '#e586e7',
      brightCyan: '#96dcdb',
      brightWhite: '#dfdfdf',
    },
  },
  {
    preset_key: 'Piatto_Light',
    name: 'Piatto Light',
    isPreset: true,
    themeData: {
      foreground: '#414141',
      background: '#ffffff',
      cursor: '#5e77c8',
      cursorAccent: '#abbee5',
      selectionBackground: '#706b4e',
      selectionForeground: '#acbcdc',
      black: '#414141',
      red: '#b23771',
      green: '#66781e',
      yellow: '#cd6f34',
      blue: '#3c5ea8',
      magenta: '#a454b2',
      cyan: '#66781e',
      white: '#ffffff',
      brightBlack: '#3f3f3f',
      brightRed: '#db3365',
      brightGreen: '#829429',
      brightYellow: '#cd6f34',
      brightBlue: '#3c5ea8',
      brightMagenta: '#a454b2',
      brightCyan: '#829429',
      brightWhite: '#f2f2f2',
    },
  },
  {
    preset_key: 'Pnevma',
    name: 'Pnevma',
    isPreset: true,
    themeData: {
      foreground: '#d0d0d0',
      background: '#1c1c1c',
      cursor: '#e4c9af',
      cursorAccent: '#000000',
      selectionBackground: '#4d4d4d',
      selectionForeground: '#ffffff',
      black: '#2f2e2d',
      red: '#a36666',
      green: '#90a57d',
      yellow: '#d7af87',
      blue: '#7fa5bd',
      magenta: '#c79ec4',
      cyan: '#8adbb4',
      white: '#d0d0d0',
      brightBlack: '#4a4845',
      brightRed: '#d78787',
      brightGreen: '#afbea2',
      brightYellow: '#e4c9af',
      brightBlue: '#a1bdce',
      brightMagenta: '#d7beda',
      brightCyan: '#b1e7dd',
      brightWhite: '#efefef',
    },
  },
  {
    preset_key: 'Popping_and_Locking',
    name: 'Popping and Locking',
    isPreset: true,
    themeData: {
      foreground: '#ebdbb2',
      background: '#181921',
      cursor: '#c7c7c7',
      cursorAccent: '#ffffff',
      selectionBackground: '#ebdbb2',
      selectionForeground: '#928374',
      black: '#1d2021',
      red: '#cc241d',
      green: '#98971a',
      yellow: '#d79921',
      blue: '#458588',
      magenta: '#b16286',
      cyan: '#689d6a',
      white: '#a89984',
      brightBlack: '#928374',
      brightRed: '#f42c3e',
      brightGreen: '#b8bb26',
      brightYellow: '#fabd2f',
      brightBlue: '#99c6ca',
      brightMagenta: '#d3869b',
      brightCyan: '#7ec16e',
      brightWhite: '#ebdbb2',
    },
  },
  {
    preset_key: 'primary',
    name: 'primary',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#000000',
      cursorAccent: '#ffffff',
      selectionBackground: '#656565',
      selectionForeground: '#eeeeee',
      black: '#000000',
      red: '#db4437',
      green: '#0f9d58',
      yellow: '#f4b400',
      blue: '#4285f4',
      magenta: '#db4437',
      cyan: '#4285f4',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#db4437',
      brightGreen: '#0f9d58',
      brightYellow: '#f4b400',
      brightBlue: '#4285f4',
      brightMagenta: '#4285f4',
      brightCyan: '#0f9d58',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Pro',
    name: 'Pro',
    isPreset: true,
    themeData: {
      foreground: '#f2f2f2',
      background: '#000000',
      cursor: '#4d4d4d',
      cursorAccent: '#ffffff',
      selectionBackground: '#414141',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#990000',
      green: '#00a600',
      yellow: '#999900',
      blue: '#2009db',
      magenta: '#b200b2',
      cyan: '#00a6b2',
      white: '#bfbfbf',
      brightBlack: '#666666',
      brightRed: '#e50000',
      brightGreen: '#00d900',
      brightYellow: '#e5e500',
      brightBlue: '#0000ff',
      brightMagenta: '#e500e5',
      brightCyan: '#00e5e5',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'Pro_Light',
    name: 'Pro Light',
    isPreset: true,
    themeData: {
      foreground: '#191919',
      background: '#ffffff',
      cursor: '#4d4d4d',
      cursorAccent: '#f2f2f2',
      selectionBackground: '#c1ddff',
      selectionForeground: '#191919',
      black: '#000000',
      red: '#e5492b',
      green: '#50d148',
      yellow: '#c6c440',
      blue: '#3b75ff',
      magenta: '#ed66e8',
      cyan: '#4ed2de',
      white: '#dcdcdc',
      brightBlack: '#9f9f9f',
      brightRed: '#ff6640',
      brightGreen: '#61ef57',
      brightYellow: '#f2f156',
      brightBlue: '#0082ff',
      brightMagenta: '#ff7eff',
      brightCyan: '#61f7f8',
      brightWhite: '#f2f2f2',
    },
  },
  {
    preset_key: 'Purple_Rain',
    name: 'Purple Rain',
    isPreset: true,
    themeData: {
      foreground: '#fffbf6',
      background: '#21084a',
      cursor: '#ff271d',
      cursorAccent: '#ff271d',
      selectionBackground: '#287691',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#ff260e',
      green: '#9be205',
      yellow: '#ffc400',
      blue: '#00a2fa',
      magenta: '#815bb5',
      cyan: '#00deef',
      white: '#ffffff',
      brightBlack: '#565656',
      brightRed: '#ff4250',
      brightGreen: '#b8e36e',
      brightYellow: '#ffd852',
      brightBlue: '#00a6ff',
      brightMagenta: '#ac7bf0',
      brightCyan: '#74fdf3',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'purplepeter',
    name: 'purplepeter',
    isPreset: true,
    themeData: {
      foreground: '#ece7fa',
      background: '#2a1a4a',
      cursor: '#c7c7c7',
      cursorAccent: '#ffffff',
      selectionBackground: '#8689c2',
      selectionForeground: '#271c50',
      black: '#0a0520',
      red: '#ff796d',
      green: '#99b481',
      yellow: '#efdfac',
      blue: '#66d9ef',
      magenta: '#e78fcd',
      cyan: '#ba8cff',
      white: '#ffba81',
      brightBlack: '#100b23',
      brightRed: '#f99f92',
      brightGreen: '#b4be8f',
      brightYellow: '#f2e9bf',
      brightBlue: '#79daed',
      brightMagenta: '#ba91d4',
      brightCyan: '#a0a0d6',
      brightWhite: '#b9aed3',
    },
  },
  {
    preset_key: 'Rapture',
    name: 'Rapture',
    isPreset: true,
    themeData: {
      foreground: '#c0c9e5',
      background: '#111e2a',
      cursor: '#ffffff',
      cursorAccent: '#111e2a',
      selectionBackground: '#304b66',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#fc644d',
      green: '#7afde1',
      yellow: '#fff09b',
      blue: '#6c9bf5',
      magenta: '#ff4fa1',
      cyan: '#64e0ff',
      white: '#c0c9e5',
      brightBlack: '#304b66',
      brightRed: '#fc644d',
      brightGreen: '#7afde1',
      brightYellow: '#fff09b',
      brightBlue: '#6c9bf5',
      brightMagenta: '#ff4fa1',
      brightCyan: '#64e0ff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Raycast_Dark',
    name: 'Raycast_Dark',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#1a1a1a',
      cursor: '#cccccc',
      cursorAccent: '#ffffff',
      selectionBackground: '#333333',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#ff5360',
      green: '#59d499',
      yellow: '#ffc531',
      blue: '#56c2ff',
      magenta: '#cf2f98',
      cyan: '#52eee5',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#ff6363',
      brightGreen: '#59d499',
      brightYellow: '#ffc531',
      brightBlue: '#56c2ff',
      brightMagenta: '#cf2f98',
      brightCyan: '#52eee5',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Raycast_Light',
    name: 'Raycast_Light',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#000000',
      cursorAccent: '#000000',
      selectionBackground: '#e5e5e5',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#b12424',
      green: '#006b4f',
      yellow: '#f8a300',
      blue: '#138af2',
      magenta: '#9a1b6e',
      cyan: '#3eb8bf',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#b12424',
      brightGreen: '#006b4f',
      brightYellow: '#f8a300',
      brightBlue: '#138af2',
      brightMagenta: '#9a1b6e',
      brightCyan: '#3eb8bf',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'rebecca',
    name: 'rebecca',
    isPreset: true,
    themeData: {
      foreground: '#e8e6ed',
      background: '#292a44',
      cursor: '#b89bf9',
      cursorAccent: '#292a44',
      selectionBackground: '#663399',
      selectionForeground: '#f4f2f9',
      black: '#12131e',
      red: '#dd7755',
      green: '#04dbb5',
      yellow: '#f2e7b7',
      blue: '#7aa5ff',
      magenta: '#bf9cf9',
      cyan: '#56d3c2',
      white: '#e4e3e9',
      brightBlack: '#666699',
      brightRed: '#ff92cd',
      brightGreen: '#01eac0',
      brightYellow: '#fffca8',
      brightBlue: '#69c0fa',
      brightMagenta: '#c17ff8',
      brightCyan: '#8bfde1',
      brightWhite: '#f4f2f9',
    },
  },
  {
    preset_key: 'Red_Alert',
    name: 'Red Alert',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#762423',
      cursor: '#ffffff',
      cursorAccent: '#762423',
      selectionBackground: '#073642',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#d62e4e',
      green: '#71be6b',
      yellow: '#beb86b',
      blue: '#489bee',
      magenta: '#e979d7',
      cyan: '#6bbeb8',
      white: '#d6d6d6',
      brightBlack: '#262626',
      brightRed: '#e02553',
      brightGreen: '#aff08c',
      brightYellow: '#dfddb7',
      brightBlue: '#65aaf1',
      brightMagenta: '#ddb7df',
      brightCyan: '#b7dfdd',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Red_Planet',
    name: 'Red Planet',
    isPreset: true,
    themeData: {
      foreground: '#c2b790',
      background: '#222222',
      cursor: '#c2b790',
      cursorAccent: '#202020',
      selectionBackground: '#1b324a',
      selectionForeground: '#bcb291',
      black: '#202020',
      red: '#8c3432',
      green: '#728271',
      yellow: '#e8bf6a',
      blue: '#69819e',
      magenta: '#896492',
      cyan: '#5b8390',
      white: '#b9aa99',
      brightBlack: '#676767',
      brightRed: '#b55242',
      brightGreen: '#869985',
      brightYellow: '#ebeb91',
      brightBlue: '#60827e',
      brightMagenta: '#de4974',
      brightCyan: '#38add8',
      brightWhite: '#d6bfb8',
    },
  },
  {
    preset_key: 'Red_Sands',
    name: 'Red Sands',
    isPreset: true,
    themeData: {
      foreground: '#d7c9a7',
      background: '#7a251e',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#a4a390',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#ff3f00',
      green: '#00bb00',
      yellow: '#e7b000',
      blue: '#0072ff',
      magenta: '#bb00bb',
      cyan: '#00bbbb',
      white: '#bbbbbb',
      brightBlack: '#555555',
      brightRed: '#bb0000',
      brightGreen: '#00bb00',
      brightYellow: '#e7b000',
      brightBlue: '#0072ae',
      brightMagenta: '#ff55ff',
      brightCyan: '#55ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Relaxed',
    name: 'Relaxed',
    isPreset: true,
    themeData: {
      foreground: '#d9d9d9',
      background: '#353a44',
      cursor: '#d9d9d9',
      cursorAccent: '#1b1b1b',
      selectionBackground: '#6a7985',
      selectionForeground: '#d9d9d9',
      black: '#151515',
      red: '#bc5653',
      green: '#909d63',
      yellow: '#ebc17a',
      blue: '#6a8799',
      magenta: '#b06698',
      cyan: '#c9dfff',
      white: '#d9d9d9',
      brightBlack: '#636363',
      brightRed: '#bc5653',
      brightGreen: '#a0ac77',
      brightYellow: '#ebc17a',
      brightBlue: '#7eaac7',
      brightMagenta: '#b06698',
      brightCyan: '#acbbd0',
      brightWhite: '#f7f7f7',
    },
  },
  {
    preset_key: 'Retro',
    name: 'Retro',
    isPreset: true,
    themeData: {
      foreground: '#13a10e',
      background: '#000000',
      cursor: '#13a10e',
      cursorAccent: '#000000',
      selectionBackground: '#ffffff',
      selectionForeground: '#000000',
      black: '#13a10e',
      red: '#13a10e',
      green: '#13a10e',
      yellow: '#13a10e',
      blue: '#13a10e',
      magenta: '#13a10e',
      cyan: '#13a10e',
      white: '#13a10e',
      brightBlack: '#16ba10',
      brightRed: '#16ba10',
      brightGreen: '#16ba10',
      brightYellow: '#16ba10',
      brightBlue: '#16ba10',
      brightMagenta: '#16ba10',
      brightCyan: '#16ba10',
      brightWhite: '#16ba10',
    },
  },
  {
    preset_key: 'RetroLegends',
    name: 'RetroLegends',
    isPreset: true,
    themeData: {
      foreground: '#45eb45',
      background: '#0d0d0d',
      cursor: '#45eb45',
      cursorAccent: '#0d0d0d',
      selectionBackground: '#336633',
      selectionForeground: '#f2fff2',
      black: '#262626',
      red: '#de5454',
      green: '#45eb45',
      yellow: '#f7bf2b',
      blue: '#4066f2',
      magenta: '#bf4df2',
      cyan: '#40d9e6',
      white: '#bfe6bf',
      brightBlack: '#4d594d',
      brightRed: '#ff6666',
      brightGreen: '#59ff59',
      brightYellow: '#ffd933',
      brightBlue: '#4d80ff',
      brightMagenta: '#e666ff',
      brightCyan: '#59e6ff',
      brightWhite: '#f2fff2',
    },
  },
  {
    preset_key: 'Rippedcasts',
    name: 'Rippedcasts',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#2b2b2b',
      cursor: '#7f7f7f',
      cursorAccent: '#ffffff',
      selectionBackground: '#5a647e',
      selectionForeground: '#f2f2f2',
      black: '#000000',
      red: '#cdaf95',
      green: '#a8ff60',
      yellow: '#bfbb1f',
      blue: '#75a5b0',
      magenta: '#ff73fd',
      cyan: '#5a647e',
      white: '#bfbfbf',
      brightBlack: '#666666',
      brightRed: '#eecbad',
      brightGreen: '#bcee68',
      brightYellow: '#e5e500',
      brightBlue: '#86bdc9',
      brightMagenta: '#e500e5',
      brightCyan: '#8c9bc4',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'rose-pine',
    name: 'rose-pine',
    isPreset: true,
    themeData: {
      foreground: '#e0def4',
      background: '#191724',
      cursor: '#e0def4',
      cursorAccent: '#191724',
      selectionBackground: '#403d52',
      selectionForeground: '#e0def4',
      black: '#26233a',
      red: '#eb6f92',
      green: '#31748f',
      yellow: '#f6c177',
      blue: '#9ccfd8',
      magenta: '#c4a7e7',
      cyan: '#ebbcba',
      white: '#e0def4',
      brightBlack: '#6e6a86',
      brightRed: '#eb6f92',
      brightGreen: '#31748f',
      brightYellow: '#f6c177',
      brightBlue: '#9ccfd8',
      brightMagenta: '#c4a7e7',
      brightCyan: '#ebbcba',
      brightWhite: '#e0def4',
    },
  },
  {
    preset_key: 'rose-pine-dawn',
    name: 'rose-pine-dawn',
    isPreset: true,
    themeData: {
      foreground: '#575279',
      background: '#faf4ed',
      cursor: '#575279',
      cursorAccent: '#faf4ed',
      selectionBackground: '#dfdad9',
      selectionForeground: '#575279',
      black: '#f2e9e1',
      red: '#b4637a',
      green: '#286983',
      yellow: '#ea9d34',
      blue: '#56949f',
      magenta: '#907aa9',
      cyan: '#d7827e',
      white: '#575279',
      brightBlack: '#9893a5',
      brightRed: '#b4637a',
      brightGreen: '#286983',
      brightYellow: '#ea9d34',
      brightBlue: '#56949f',
      brightMagenta: '#907aa9',
      brightCyan: '#d7827e',
      brightWhite: '#575279',
    },
  },
  {
    preset_key: 'rose-pine-moon',
    name: 'rose-pine-moon',
    isPreset: true,
    themeData: {
      foreground: '#e0def4',
      background: '#232136',
      cursor: '#e0def4',
      cursorAccent: '#232136',
      selectionBackground: '#44415a',
      selectionForeground: '#e0def4',
      black: '#393552',
      red: '#eb6f92',
      green: '#3e8fb0',
      yellow: '#f6c177',
      blue: '#9ccfd8',
      magenta: '#c4a7e7',
      cyan: '#ea9a97',
      white: '#e0def4',
      brightBlack: '#6e6a86',
      brightRed: '#eb6f92',
      brightGreen: '#3e8fb0',
      brightYellow: '#f6c177',
      brightBlue: '#9ccfd8',
      brightMagenta: '#c4a7e7',
      brightCyan: '#ea9a97',
      brightWhite: '#e0def4',
    },
  },
  {
    preset_key: 'Rouge_2',
    name: 'Rouge 2',
    isPreset: true,
    themeData: {
      foreground: '#a2a3aa',
      background: '#17182b',
      cursor: '#969e92',
      cursorAccent: '#ffffff',
      selectionBackground: '#5d5d6b',
      selectionForeground: '#dfe5ee',
      black: '#5d5d6b',
      red: '#c6797e',
      green: '#969e92',
      yellow: '#dbcdab',
      blue: '#6e94b9',
      magenta: '#4c4e78',
      cyan: '#8ab6c1',
      white: '#e8e8ea',
      brightBlack: '#616274',
      brightRed: '#c6797e',
      brightGreen: '#e6dcc4',
      brightYellow: '#e6dcc4',
      brightBlue: '#98b3cd',
      brightMagenta: '#8283a1',
      brightCyan: '#abcbd3',
      brightWhite: '#e8e8ea',
    },
  },
  {
    preset_key: 'Royal',
    name: 'Royal',
    isPreset: true,
    themeData: {
      foreground: '#514968',
      background: '#100815',
      cursor: '#524966',
      cursorAccent: '#100613',
      selectionBackground: '#1f1d2b',
      selectionForeground: '#a593cd',
      black: '#241f2b',
      red: '#91284c',
      green: '#23801c',
      yellow: '#b49d27',
      blue: '#6580b0',
      magenta: '#674d96',
      cyan: '#8aaabe',
      white: '#524966',
      brightBlack: '#312d3d',
      brightRed: '#d5356c',
      brightGreen: '#2cd946',
      brightYellow: '#fde83b',
      brightBlue: '#90baf9',
      brightMagenta: '#a479e3',
      brightCyan: '#acd4eb',
      brightWhite: '#9e8cbd',
    },
  },
  {
    preset_key: 'Ryuuko',
    name: 'Ryuuko',
    isPreset: true,
    themeData: {
      foreground: '#ececec',
      background: '#2c3941',
      cursor: '#ececec',
      cursorAccent: '#002831',
      selectionBackground: '#002831',
      selectionForeground: '#819090',
      black: '#2c3941',
      red: '#865f5b',
      green: '#66907d',
      yellow: '#b1a990',
      blue: '#6a8e95',
      magenta: '#b18a73',
      cyan: '#88b2ac',
      white: '#ececec',
      brightBlack: '#5d7079',
      brightRed: '#865f5b',
      brightGreen: '#66907d',
      brightYellow: '#b1a990',
      brightBlue: '#6a8e95',
      brightMagenta: '#b18a73',
      brightCyan: '#88b2ac',
      brightWhite: '#ececec',
    },
  },
  {
    preset_key: 'Sakura',
    name: 'Sakura',
    isPreset: true,
    themeData: {
      foreground: '#dd7bdc',
      background: '#18131e',
      cursor: '#ff65fd',
      cursorAccent: '#24242e',
      selectionBackground: '#c05cbf',
      selectionForeground: '#24242e',
      black: '#000000',
      red: '#d52370',
      green: '#41af1a',
      yellow: '#bc7053',
      blue: '#6964ab',
      magenta: '#c71fbf',
      cyan: '#939393',
      white: '#998eac',
      brightBlack: '#786d69',
      brightRed: '#f41d99',
      brightGreen: '#22e529',
      brightYellow: '#f59574',
      brightBlue: '#9892f1',
      brightMagenta: '#e90cdd',
      brightCyan: '#eeeeee',
      brightWhite: '#cbb6ff',
    },
  },
  {
    preset_key: 'Scarlet_Protocol',
    name: 'Scarlet Protocol',
    isPreset: true,
    themeData: {
      foreground: '#e41951',
      background: '#1c153d',
      cursor: '#76ff9f',
      cursorAccent: '#ffffff',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#101116',
      red: '#ff0051',
      green: '#00dc84',
      yellow: '#faf945',
      blue: '#0271b6',
      magenta: '#ca30c7',
      cyan: '#00c5c7',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#ff6e67',
      brightGreen: '#5ffa68',
      brightYellow: '#fffc67',
      brightBlue: '#6871ff',
      brightMagenta: '#bd35ec',
      brightCyan: '#60fdff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Seafoam_Pastel',
    name: 'Seafoam Pastel',
    isPreset: true,
    themeData: {
      foreground: '#d4e7d4',
      background: '#243435',
      cursor: '#57647a',
      cursorAccent: '#323232',
      selectionBackground: '#ffffff',
      selectionForeground: '#9e8b13',
      black: '#757575',
      red: '#825d4d',
      green: '#728c62',
      yellow: '#ada16d',
      blue: '#4d7b82',
      magenta: '#8a7267',
      cyan: '#729494',
      white: '#e0e0e0',
      brightBlack: '#8a8a8a',
      brightRed: '#cf937a',
      brightGreen: '#98d9aa',
      brightYellow: '#fae79d',
      brightBlue: '#7ac3cf',
      brightMagenta: '#d6b2a1',
      brightCyan: '#ade0e0',
      brightWhite: '#e0e0e0',
    },
  },
  {
    preset_key: 'SeaShells',
    name: 'SeaShells',
    isPreset: true,
    themeData: {
      foreground: '#deb88d',
      background: '#09141b',
      cursor: '#fca02f',
      cursorAccent: '#08131a',
      selectionBackground: '#1e4962',
      selectionForeground: '#fee4ce',
      black: '#17384c',
      red: '#d15123',
      green: '#027c9b',
      yellow: '#fca02f',
      blue: '#1e4950',
      magenta: '#68d4f1',
      cyan: '#50a3b5',
      white: '#deb88d',
      brightBlack: '#434b53',
      brightRed: '#d48678',
      brightGreen: '#628d98',
      brightYellow: '#fdd39f',
      brightBlue: '#1bbcdd',
      brightMagenta: '#bbe3ee',
      brightCyan: '#87acb4',
      brightWhite: '#fee4ce',
    },
  },
  {
    preset_key: 'seoulbones_dark',
    name: 'seoulbones_dark',
    isPreset: true,
    themeData: {
      foreground: '#dddddd',
      background: '#4b4b4b',
      cursor: '#e2e2e2',
      cursorAccent: '#4b4b4b',
      selectionBackground: '#777777',
      selectionForeground: '#dddddd',
      black: '#4b4b4b',
      red: '#e388a3',
      green: '#98bd99',
      yellow: '#ffdf9b',
      blue: '#97bdde',
      magenta: '#a5a6c5',
      cyan: '#6fbdbe',
      white: '#dddddd',
      brightBlack: '#6c6465',
      brightRed: '#eb99b1',
      brightGreen: '#8fcd92',
      brightYellow: '#ffe5b3',
      brightBlue: '#a2c8e9',
      brightMagenta: '#b2b3da',
      brightCyan: '#6bcacb',
      brightWhite: '#a8a8a8',
    },
  },
  {
    preset_key: 'seoulbones_light',
    name: 'seoulbones_light',
    isPreset: true,
    themeData: {
      foreground: '#555555',
      background: '#e2e2e2',
      cursor: '#555555',
      cursorAccent: '#e2e2e2',
      selectionBackground: '#cccccc',
      selectionForeground: '#555555',
      black: '#e2e2e2',
      red: '#dc5284',
      green: '#628562',
      yellow: '#c48562',
      blue: '#0084a3',
      magenta: '#896788',
      cyan: '#008586',
      white: '#555555',
      brightBlack: '#bfbabb',
      brightRed: '#be3c6d',
      brightGreen: '#487249',
      brightYellow: '#a76b48',
      brightBlue: '#006f89',
      brightMagenta: '#7f4c7e',
      brightCyan: '#006f70',
      brightWhite: '#777777',
    },
  },
  {
    preset_key: 'Seti',
    name: 'Seti',
    isPreset: true,
    themeData: {
      foreground: '#cacecd',
      background: '#111213',
      cursor: '#e3bf21',
      cursorAccent: '#e0be2e',
      selectionBackground: '#303233',
      selectionForeground: '#cacecd',
      black: '#323232',
      red: '#c22832',
      green: '#8ec43d',
      yellow: '#e0c64f',
      blue: '#43a5d5',
      magenta: '#8b57b5',
      cyan: '#8ec43d',
      white: '#eeeeee',
      brightBlack: '#323232',
      brightRed: '#c22832',
      brightGreen: '#8ec43d',
      brightYellow: '#e0c64f',
      brightBlue: '#43a5d5',
      brightMagenta: '#8b57b5',
      brightCyan: '#8ec43d',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'shades-of-purple',
    name: 'shades-of-purple',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#1e1d40',
      cursor: '#fad000',
      cursorAccent: '#fefff4',
      selectionBackground: '#b362ff',
      selectionForeground: '#c2c2c2',
      black: '#000000',
      red: '#d90429',
      green: '#3ad900',
      yellow: '#ffe700',
      blue: '#6943ff',
      magenta: '#ff2c70',
      cyan: '#00c5c7',
      white: '#c7c7c7',
      brightBlack: '#686868',
      brightRed: '#f92a1c',
      brightGreen: '#43d426',
      brightYellow: '#f1d000',
      brightBlue: '#6871ff',
      brightMagenta: '#ff77ff',
      brightCyan: '#79e8fb',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Shaman',
    name: 'Shaman',
    isPreset: true,
    themeData: {
      foreground: '#405555',
      background: '#001015',
      cursor: '#4afcd6',
      cursorAccent: '#031413',
      selectionBackground: '#415555',
      selectionForeground: '#5afad6',
      black: '#012026',
      red: '#b2302d',
      green: '#00a941',
      yellow: '#5e8baa',
      blue: '#449a86',
      magenta: '#00599d',
      cyan: '#5d7e19',
      white: '#405555',
      brightBlack: '#384451',
      brightRed: '#ff4242',
      brightGreen: '#2aea5e',
      brightYellow: '#8ed4fd',
      brightBlue: '#61d5ba',
      brightMagenta: '#1298ff',
      brightCyan: '#98d028',
      brightWhite: '#58fbd6',
    },
  },
  {
    preset_key: 'Slate',
    name: 'Slate',
    isPreset: true,
    themeData: {
      foreground: '#35b1d2',
      background: '#222222',
      cursor: '#87d3c4',
      cursorAccent: '#323232',
      selectionBackground: '#0f3754',
      selectionForeground: '#2dffc0',
      black: '#222222',
      red: '#e2a8bf',
      green: '#81d778',
      yellow: '#c4c9c0',
      blue: '#264b49',
      magenta: '#a481d3',
      cyan: '#15ab9c',
      white: '#02c5e0',
      brightBlack: '#ffffff',
      brightRed: '#ffcdd9',
      brightGreen: '#beffa8',
      brightYellow: '#d0ccca',
      brightBlue: '#7ab0d2',
      brightMagenta: '#c5a7d9',
      brightCyan: '#8cdfe0',
      brightWhite: '#e0e0e0',
    },
  },
  {
    preset_key: 'SleepyHollow',
    name: 'SleepyHollow',
    isPreset: true,
    themeData: {
      foreground: '#af9a91',
      background: '#121214',
      cursor: '#af9a91',
      cursorAccent: '#391a02',
      selectionBackground: '#575256',
      selectionForeground: '#d2c7a9',
      black: '#572100',
      red: '#ba3934',
      green: '#91773f',
      yellow: '#b55600',
      blue: '#5f63b4',
      magenta: '#a17c7b',
      cyan: '#8faea9',
      white: '#af9a91',
      brightBlack: '#4e4b61',
      brightRed: '#d9443f',
      brightGreen: '#d6b04e',
      brightYellow: '#f66813',
      brightBlue: '#8086ef',
      brightMagenta: '#e2c2bb',
      brightCyan: '#a4dce7',
      brightWhite: '#d2c7a9',
    },
  },
  {
    preset_key: 'Smyck',
    name: 'Smyck',
    isPreset: true,
    themeData: {
      foreground: '#f7f7f7',
      background: '#1b1b1b',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#207483',
      selectionForeground: '#f7f7f7',
      black: '#000000',
      red: '#b84131',
      green: '#7da900',
      yellow: '#c4a500',
      blue: '#62a3c4',
      magenta: '#ba8acc',
      cyan: '#207383',
      white: '#a1a1a1',
      brightBlack: '#7a7a7a',
      brightRed: '#d6837c',
      brightGreen: '#c4f137',
      brightYellow: '#fee14d',
      brightBlue: '#8dcff0',
      brightMagenta: '#f79aff',
      brightCyan: '#6ad9cf',
      brightWhite: '#f7f7f7',
    },
  },
  {
    preset_key: 'Snazzy',
    name: 'Snazzy',
    isPreset: true,
    themeData: {
      foreground: '#ebece6',
      background: '#1e1f29',
      cursor: '#e4e4e4',
      cursorAccent: '#f6f6f6',
      selectionBackground: '#81aec6',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#fc4346',
      green: '#50fb7c',
      yellow: '#f0fb8c',
      blue: '#49baff',
      magenta: '#fc4cb4',
      cyan: '#8be9fe',
      white: '#ededec',
      brightBlack: '#555555',
      brightRed: '#fc4346',
      brightGreen: '#50fb7c',
      brightYellow: '#f0fb8c',
      brightBlue: '#49baff',
      brightMagenta: '#fc4cb4',
      brightCyan: '#8be9fe',
      brightWhite: '#ededec',
    },
  },
  {
    preset_key: 'Snazzy_Soft',
    name: 'Snazzy Soft',
    isPreset: true,
    themeData: {
      foreground: '#eff0eb',
      background: '#282a36',
      cursor: '#eaeaea',
      cursorAccent: '#282a36',
      selectionBackground: '#92bcd0',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#ff5c57',
      green: '#5af78e',
      yellow: '#f3f99d',
      blue: '#57c7ff',
      magenta: '#ff6ac1',
      cyan: '#9aedfe',
      white: '#f1f1f0',
      brightBlack: '#686868',
      brightRed: '#ff5c57',
      brightGreen: '#5af78e',
      brightYellow: '#f3f99d',
      brightBlue: '#57c7ff',
      brightMagenta: '#ff6ac1',
      brightCyan: '#9aedfe',
      brightWhite: '#f1f1f0',
    },
  },
  {
    preset_key: 'SoftServer',
    name: 'SoftServer',
    isPreset: true,
    themeData: {
      foreground: '#99a3a2',
      background: '#242626',
      cursor: '#d2e0de',
      cursorAccent: '#000000',
      selectionBackground: '#7f8786',
      selectionForeground: '#effffe',
      black: '#000000',
      red: '#a2686a',
      green: '#9aa56a',
      yellow: '#a3906a',
      blue: '#6b8fa3',
      magenta: '#6a71a3',
      cyan: '#6ba58f',
      white: '#99a3a2',
      brightBlack: '#666c6c',
      brightRed: '#dd5c60',
      brightGreen: '#bfdf55',
      brightYellow: '#deb360',
      brightBlue: '#62b1df',
      brightMagenta: '#606edf',
      brightCyan: '#64e39c',
      brightWhite: '#d2e0de',
    },
  },
  {
    preset_key: 'Solarized_Darcula',
    name: 'Solarized Darcula',
    isPreset: true,
    themeData: {
      foreground: '#d2d8d9',
      background: '#3d3f41',
      cursor: '#708284',
      cursorAccent: '#002831',
      selectionBackground: '#214283',
      selectionForeground: '#d2d8d9',
      black: '#25292a',
      red: '#f24840',
      green: '#629655',
      yellow: '#b68800',
      blue: '#2075c7',
      magenta: '#797fd4',
      cyan: '#15968d',
      white: '#d2d8d9',
      brightBlack: '#25292a',
      brightRed: '#f24840',
      brightGreen: '#629655',
      brightYellow: '#b68800',
      brightBlue: '#2075c7',
      brightMagenta: '#797fd4',
      brightCyan: '#15968d',
      brightWhite: '#d2d8d9',
    },
  },
  {
    preset_key: 'Solarized_Dark_-_Patched',
    name: 'Solarized Dark - Patched',
    isPreset: true,
    themeData: {
      foreground: '#708284',
      background: '#001e27',
      cursor: '#708284',
      cursorAccent: '#002831',
      selectionBackground: '#002831',
      selectionForeground: '#819090',
      black: '#002831',
      red: '#d11c24',
      green: '#738a05',
      yellow: '#a57706',
      blue: '#2176c7',
      magenta: '#c61c6f',
      cyan: '#259286',
      white: '#eae3cb',
      brightBlack: '#475b62',
      brightRed: '#bd3613',
      brightGreen: '#475b62',
      brightYellow: '#536870',
      brightBlue: '#708284',
      brightMagenta: '#5956ba',
      brightCyan: '#819090',
      brightWhite: '#fcf4dc',
    },
  },
  {
    preset_key: 'Solarized_Dark_Higher_Contrast',
    name: 'Solarized Dark Higher Contrast',
    isPreset: true,
    themeData: {
      foreground: '#9cc2c3',
      background: '#001e27',
      cursor: '#f34b00',
      cursorAccent: '#002831',
      selectionBackground: '#003748',
      selectionForeground: '#7a8f8e',
      black: '#002831',
      red: '#d11c24',
      green: '#6cbe6c',
      yellow: '#a57706',
      blue: '#2176c7',
      magenta: '#c61c6f',
      cyan: '#259286',
      white: '#eae3cb',
      brightBlack: '#006488',
      brightRed: '#f5163b',
      brightGreen: '#51ef84',
      brightYellow: '#b27e28',
      brightBlue: '#178ec8',
      brightMagenta: '#e24d8e',
      brightCyan: '#00b39e',
      brightWhite: '#fcf4dc',
    },
  },
  {
    preset_key: 'solarized-osaka-night',
    name: 'solarized-osaka-night',
    isPreset: true,
    themeData: {
      foreground: '#c2caf1',
      background: '#1a1b25',
      cursor: '#c2caf1',
      cursorAccent: '#1a1b25',
      selectionBackground: '#2a3454',
      selectionForeground: '#c2caf1',
      black: '#15161d',
      red: '#e77d8f',
      green: '#a8cd76',
      yellow: '#d8b172',
      blue: '#82a1f1',
      magenta: '#b69bf1',
      cyan: '#90cdfa',
      white: '#aab1d3',
      brightBlack: '#424866',
      brightRed: '#e77d8f',
      brightGreen: '#a8cd76',
      brightYellow: '#d8b172',
      brightBlue: '#82a1f1',
      brightMagenta: '#b69bf1',
      brightCyan: '#90cdfa',
      brightWhite: '#c2caf1',
    },
  },
  {
    preset_key: 'sonokai',
    name: 'sonokai',
    isPreset: true,
    themeData: {
      foreground: '#e2e2e3',
      background: '#2c2e34',
      cursor: '#e2e2e3',
      cursorAccent: '#2c2e34',
      selectionBackground: '#414550',
      selectionForeground: '#e2e2e3',
      black: '#181819',
      red: '#fc5d7c',
      green: '#9ed072',
      yellow: '#e7c664',
      blue: '#76cce0',
      magenta: '#b39df3',
      cyan: '#f39660',
      white: '#e2e2e3',
      brightBlack: '#7f8490',
      brightRed: '#fc5d7c',
      brightGreen: '#9ed072',
      brightYellow: '#e7c664',
      brightBlue: '#76cce0',
      brightMagenta: '#b39df3',
      brightCyan: '#f39660',
      brightWhite: '#e2e2e3',
    },
  },
  {
    preset_key: 'Spacedust',
    name: 'Spacedust',
    isPreset: true,
    themeData: {
      foreground: '#ecf0c1',
      background: '#0a1e24',
      cursor: '#708284',
      cursorAccent: '#002831',
      selectionBackground: '#0a385c',
      selectionForeground: '#ffffff',
      black: '#6e5346',
      red: '#e35b00',
      green: '#5cab96',
      yellow: '#e3cd7b',
      blue: '#0f548b',
      magenta: '#e35b00',
      cyan: '#06afc7',
      white: '#f0f1ce',
      brightBlack: '#684c31',
      brightRed: '#ff8a3a',
      brightGreen: '#aecab8',
      brightYellow: '#ffc878',
      brightBlue: '#67a0ce',
      brightMagenta: '#ff8a3a',
      brightCyan: '#83a7b4',
      brightWhite: '#fefff1',
    },
  },
  {
    preset_key: 'SpaceGray',
    name: 'SpaceGray',
    isPreset: true,
    themeData: {
      foreground: '#b3b8c3',
      background: '#20242d',
      cursor: '#b3b8c3',
      cursorAccent: '#1d1f21',
      selectionBackground: '#16181e',
      selectionForeground: '#b3b8c3',
      black: '#000000',
      red: '#b04b57',
      green: '#87b379',
      yellow: '#e5c179',
      blue: '#7d8fa4',
      magenta: '#a47996',
      cyan: '#85a7a5',
      white: '#b3b8c3',
      brightBlack: '#000000',
      brightRed: '#b04b57',
      brightGreen: '#87b379',
      brightYellow: '#e5c179',
      brightBlue: '#7d8fa4',
      brightMagenta: '#a47996',
      brightCyan: '#85a7a5',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'SpaceGray_Bright',
    name: 'SpaceGray Bright',
    isPreset: true,
    themeData: {
      foreground: '#f3f3f3',
      background: '#2a2e3a',
      cursor: '#c6c6c6',
      cursorAccent: '#ffffff',
      selectionBackground: '#cacaca',
      selectionForeground: '#000000',
      black: '#080808',
      red: '#bc5553',
      green: '#a0b56c',
      yellow: '#f6c987',
      blue: '#7baec1',
      magenta: '#b98aae',
      cyan: '#85c9b8',
      white: '#d8d8d8',
      brightBlack: '#626262',
      brightRed: '#bc5553',
      brightGreen: '#a0b56c',
      brightYellow: '#f6c987',
      brightBlue: '#7baec1',
      brightMagenta: '#b98aae',
      brightCyan: '#85c9b8',
      brightWhite: '#f7f7f7',
    },
  },
  {
    preset_key: 'SpaceGray_Eighties',
    name: 'SpaceGray Eighties',
    isPreset: true,
    themeData: {
      foreground: '#bdbaae',
      background: '#222222',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#272e35',
      selectionForeground: '#ffffff',
      black: '#15171c',
      red: '#ec5f67',
      green: '#81a764',
      yellow: '#fec254',
      blue: '#5486c0',
      magenta: '#bf83c1',
      cyan: '#57c2c1',
      white: '#efece7',
      brightBlack: '#555555',
      brightRed: '#ff6973',
      brightGreen: '#93d493',
      brightYellow: '#ffd256',
      brightBlue: '#4d84d1',
      brightMagenta: '#ff55ff',
      brightCyan: '#83e9e4',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'SpaceGray_Eighties_Dull',
    name: 'SpaceGray Eighties Dull',
    isPreset: true,
    themeData: {
      foreground: '#c9c6bc',
      background: '#222222',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#272e36',
      selectionForeground: '#ffffff',
      black: '#15171c',
      red: '#b24a56',
      green: '#92b477',
      yellow: '#c6735a',
      blue: '#7c8fa5',
      magenta: '#a5789e',
      cyan: '#80cdcb',
      white: '#b3b8c3',
      brightBlack: '#555555',
      brightRed: '#ec5f67',
      brightGreen: '#89e986',
      brightYellow: '#fec254',
      brightBlue: '#5486c0',
      brightMagenta: '#bf83c1',
      brightCyan: '#58c2c1',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Spiderman',
    name: 'Spiderman',
    isPreset: true,
    themeData: {
      foreground: '#e3e3e3',
      background: '#1b1d1e',
      cursor: '#2c3fff',
      cursorAccent: '#000000',
      selectionBackground: '#070e50',
      selectionForeground: '#f0272d',
      black: '#1b1d1e',
      red: '#e60813',
      green: '#e22928',
      yellow: '#e24756',
      blue: '#2c3fff',
      magenta: '#2435db',
      cyan: '#3256ff',
      white: '#fffef6',
      brightBlack: '#505354',
      brightRed: '#ff0325',
      brightGreen: '#ff3338',
      brightYellow: '#fe3a35',
      brightBlue: '#1d50ff',
      brightMagenta: '#747cff',
      brightCyan: '#6184ff',
      brightWhite: '#fffff9',
    },
  },
  {
    preset_key: 'Spring',
    name: 'Spring',
    isPreset: true,
    themeData: {
      foreground: '#4d4d4c',
      background: '#ffffff',
      cursor: '#4d4d4c',
      cursorAccent: '#ffffff',
      selectionBackground: '#d6d6d6',
      selectionForeground: '#4d4d4c',
      black: '#000000',
      red: '#ff4d83',
      green: '#1f8c3b',
      yellow: '#1fc95b',
      blue: '#1dd3ee',
      magenta: '#8959a8',
      cyan: '#3e999f',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#ff0021',
      brightGreen: '#1fc231',
      brightYellow: '#d5b807',
      brightBlue: '#15a9fd',
      brightMagenta: '#8959a8',
      brightCyan: '#3e999f',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Square',
    name: 'Square',
    isPreset: true,
    themeData: {
      foreground: '#acacab',
      background: '#1a1a1a',
      cursor: '#fcfbcc',
      cursorAccent: '#000000',
      selectionBackground: '#4d4d4d',
      selectionForeground: '#ffffff',
      black: '#050505',
      red: '#e9897c',
      green: '#b6377d',
      yellow: '#ecebbe',
      blue: '#a9cdeb',
      magenta: '#75507b',
      cyan: '#c9caec',
      white: '#f2f2f2',
      brightBlack: '#141414',
      brightRed: '#f99286',
      brightGreen: '#c3f786',
      brightYellow: '#fcfbcc',
      brightBlue: '#b6defb',
      brightMagenta: '#ad7fa8',
      brightCyan: '#d7d9fc',
      brightWhite: '#e2e2e2',
    },
  },
  {
    preset_key: 'Squirrelsong_Dark',
    name: 'Squirrelsong Dark',
    isPreset: true,
    themeData: {
      foreground: '#ad9c8b',
      background: '#352a21',
      cursor: '#ad9c8b',
      cursorAccent: '#352a21',
      selectionBackground: '#574131',
      selectionForeground: '#ad9c8b',
      black: '#352a21',
      red: '#ac493e',
      green: '#558240',
      yellow: '#ceb250',
      blue: '#5993c2',
      magenta: '#7f61b3',
      cyan: '#4f9593',
      white: '#cfbaa5',
      brightBlack: '#6b503c',
      brightRed: '#ce574a',
      brightGreen: '#719955',
      brightYellow: '#e2c358',
      brightBlue: '#63a2d6',
      brightMagenta: '#9672d4',
      brightCyan: '#72aaa8',
      brightWhite: '#edd5be',
    },
  },
  {
    preset_key: 'srcery',
    name: 'srcery',
    isPreset: true,
    themeData: {
      foreground: '#fce8c3',
      background: '#1c1b19',
      cursor: '#fbb829',
      cursorAccent: '#1c1b19',
      selectionBackground: '#fce8c3',
      selectionForeground: '#1c1b19',
      black: '#1c1b19',
      red: '#ef2f27',
      green: '#519f50',
      yellow: '#fbb829',
      blue: '#2c78bf',
      magenta: '#e02c6d',
      cyan: '#0aaeb3',
      white: '#baa67f',
      brightBlack: '#918175',
      brightRed: '#f75341',
      brightGreen: '#98bc37',
      brightYellow: '#fed06e',
      brightBlue: '#68a8e4',
      brightMagenta: '#ff5c8f',
      brightCyan: '#2be4d0',
      brightWhite: '#fce8c3',
    },
  },
  {
    preset_key: 'starlight',
    name: 'starlight',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#242424',
      cursor: '#ffffff',
      cursorAccent: '#242424',
      selectionBackground: '#ffffff',
      selectionForeground: '#242424',
      black: '#242424',
      red: '#e2425d',
      green: '#66b238',
      yellow: '#dec541',
      blue: '#54aad0',
      magenta: '#e8b2f8',
      cyan: '#5abf9b',
      white: '#e6e6e6',
      brightBlack: '#616161',
      brightRed: '#ec5b58',
      brightGreen: '#6bd162',
      brightYellow: '#e9e85c',
      brightBlue: '#78c3f3',
      brightMagenta: '#f2afee',
      brightCyan: '#6adcc5',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Sublette',
    name: 'Sublette',
    isPreset: true,
    themeData: {
      foreground: '#ccced0',
      background: '#202535',
      cursor: '#ccced0',
      cursorAccent: '#202535',
      selectionBackground: '#ccced0',
      selectionForeground: '#202535',
      black: '#253045',
      red: '#ee5577',
      green: '#55ee77',
      yellow: '#ffdd88',
      blue: '#5588ff',
      magenta: '#ff77cc',
      cyan: '#44eeee',
      white: '#f5f5da',
      brightBlack: '#405570',
      brightRed: '#ee6655',
      brightGreen: '#99ee77',
      brightYellow: '#ffff77',
      brightBlue: '#77bbff',
      brightMagenta: '#aa88ff',
      brightCyan: '#55ffbb',
      brightWhite: '#ffffee',
    },
  },
  {
    preset_key: 'Subliminal',
    name: 'Subliminal',
    isPreset: true,
    themeData: {
      foreground: '#d4d4d4',
      background: '#282c35',
      cursor: '#c7c7c7',
      cursorAccent: '#ffffff',
      selectionBackground: '#484e5b',
      selectionForeground: '#ffffff',
      black: '#7f7f7f',
      red: '#e15a60',
      green: '#a9cfa4',
      yellow: '#ffe2a9',
      blue: '#6699cc',
      magenta: '#f1a5ab',
      cyan: '#5fb3b3',
      white: '#d4d4d4',
      brightBlack: '#7f7f7f',
      brightRed: '#e15a60',
      brightGreen: '#a9cfa4',
      brightYellow: '#ffe2a9',
      brightBlue: '#6699cc',
      brightMagenta: '#f1a5ab',
      brightCyan: '#5fb3b3',
      brightWhite: '#d4d4d4',
    },
  },
  {
    preset_key: 'Sugarplum',
    name: 'Sugarplum',
    isPreset: true,
    themeData: {
      foreground: '#db7ddd',
      background: '#111147',
      cursor: '#53b397',
      cursorAccent: '#53b397',
      selectionBackground: '#5ca8dc',
      selectionForeground: '#d0beee',
      black: '#111147',
      red: '#5ca8dc',
      green: '#53b397',
      yellow: '#249a84',
      blue: '#db7ddd',
      magenta: '#d0beee',
      cyan: '#f9f3f9',
      white: '#a175d4',
      brightBlack: '#111147',
      brightRed: '#5cb5dc',
      brightGreen: '#52deb5',
      brightYellow: '#01f5c7',
      brightBlue: '#fa5dfd',
      brightMagenta: '#c6a5fd',
      brightCyan: '#ffffff',
      brightWhite: '#b577fd',
    },
  },
  {
    preset_key: 'Sundried',
    name: 'Sundried',
    isPreset: true,
    themeData: {
      foreground: '#c9c9c9',
      background: '#1a1818',
      cursor: '#ffffff',
      cursorAccent: '#191717',
      selectionBackground: '#302b2a',
      selectionForeground: '#c9c9c9',
      black: '#302b2a',
      red: '#a7463d',
      green: '#587744',
      yellow: '#9d602a',
      blue: '#485b98',
      magenta: '#864651',
      cyan: '#9c814f',
      white: '#c9c9c9',
      brightBlack: '#4d4e48',
      brightRed: '#aa000c',
      brightGreen: '#128c21',
      brightYellow: '#fc6a21',
      brightBlue: '#7999f7',
      brightMagenta: '#fd8aa1',
      brightCyan: '#fad484',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Symfonic',
    name: 'Symfonic',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#000000',
      cursor: '#dc322f',
      cursorAccent: '#ffffff',
      selectionBackground: '#073642',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#dc322f',
      green: '#56db3a',
      yellow: '#ff8400',
      blue: '#0084d4',
      magenta: '#b729d9',
      cyan: '#ccccff',
      white: '#ffffff',
      brightBlack: '#1b1d21',
      brightRed: '#dc322f',
      brightGreen: '#56db3a',
      brightYellow: '#ff8400',
      brightBlue: '#0084d4',
      brightMagenta: '#b729d9',
      brightCyan: '#ccccff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'synthwave',
    name: 'synthwave',
    isPreset: true,
    themeData: {
      foreground: '#dad9c7',
      background: '#000000',
      cursor: '#19cde6',
      cursorAccent: '#dad9c7',
      selectionBackground: '#19cde6',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#f6188f',
      green: '#1ebb2b',
      yellow: '#fdf834',
      blue: '#2186ec',
      magenta: '#f85a21',
      cyan: '#12c3e2',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#f841a0',
      brightGreen: '#25c141',
      brightYellow: '#fdf454',
      brightBlue: '#2f9ded',
      brightMagenta: '#f97137',
      brightCyan: '#19cde6',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'synthwave-everything',
    name: 'synthwave-everything',
    isPreset: true,
    themeData: {
      foreground: '#f0eff1',
      background: '#2a2139',
      cursor: '#72f1b8',
      cursorAccent: '#1a1a1a',
      selectionBackground: '#181521',
      selectionForeground: '#f0eff1',
      black: '#fefefe',
      red: '#f97e72',
      green: '#72f1b8',
      yellow: '#fede5d',
      blue: '#6d77b3',
      magenta: '#c792ea',
      cyan: '#f772e0',
      white: '#fefefe',
      brightBlack: '#fefefe',
      brightRed: '#f88414',
      brightGreen: '#72f1b8',
      brightYellow: '#fff951',
      brightBlue: '#36f9f6',
      brightMagenta: '#e1acff',
      brightCyan: '#f92aad',
      brightWhite: '#fefefe',
    },
  },
  {
    preset_key: 'SynthwaveAlpha',
    name: 'SynthwaveAlpha',
    isPreset: true,
    themeData: {
      foreground: '#f2f2e3',
      background: '#241b30',
      cursor: '#f2f2e3',
      cursorAccent: '#241b30',
      selectionBackground: '#6e29ad',
      selectionForeground: '#f2f2e3',
      black: '#241b30',
      red: '#e60a70',
      green: '#00986c',
      yellow: '#adad3e',
      blue: '#6e29ad',
      magenta: '#b300ad',
      cyan: '#00b0b1',
      white: '#b9b1bc',
      brightBlack: '#7f7094',
      brightRed: '#e60a70',
      brightGreen: '#0ae4a4',
      brightYellow: '#f9f972',
      brightBlue: '#aa54f9',
      brightMagenta: '#ff00f6',
      brightCyan: '#00fbfd',
      brightWhite: '#f2f2e3',
    },
  },
  {
    preset_key: 'Tango_Adapted',
    name: 'Tango Adapted',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#000000',
      cursorAccent: '#ffffff',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#ff0000',
      green: '#59d600',
      yellow: '#f0cb00',
      blue: '#00a2ff',
      magenta: '#c17ecc',
      cyan: '#00d0d6',
      white: '#e6ebe1',
      brightBlack: '#8f928b',
      brightRed: '#ff0013',
      brightGreen: '#93ff00',
      brightYellow: '#fff121',
      brightBlue: '#88c9ff',
      brightMagenta: '#e9a7e1',
      brightCyan: '#00feff',
      brightWhite: '#f6f6f4',
    },
  },
  {
    preset_key: 'Tango_Half_Adapted',
    name: 'Tango Half Adapted',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#000000',
      cursorAccent: '#ffffff',
      selectionBackground: '#c1deff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#ff0000',
      green: '#4cc300',
      yellow: '#e2c000',
      blue: '#008ef6',
      magenta: '#a96cb3',
      cyan: '#00bdc3',
      white: '#e0e5db',
      brightBlack: '#797d76',
      brightRed: '#ff0013',
      brightGreen: '#8af600',
      brightYellow: '#ffec00',
      brightBlue: '#76bfff',
      brightMagenta: '#d898d1',
      brightCyan: '#00f6fa',
      brightWhite: '#f4f4f2',
    },
  },
  {
    preset_key: 'Teerb',
    name: 'Teerb',
    isPreset: true,
    themeData: {
      foreground: '#d0d0d0',
      background: '#262626',
      cursor: '#e4c9af',
      cursorAccent: '#000000',
      selectionBackground: '#4d4d4d',
      selectionForeground: '#ffffff',
      black: '#1c1c1c',
      red: '#d68686',
      green: '#aed686',
      yellow: '#d7af87',
      blue: '#86aed6',
      magenta: '#d6aed6',
      cyan: '#8adbb4',
      white: '#d0d0d0',
      brightBlack: '#1c1c1c',
      brightRed: '#d68686',
      brightGreen: '#aed686',
      brightYellow: '#e4c9af',
      brightBlue: '#86aed6',
      brightMagenta: '#d6aed6',
      brightCyan: '#b1e7dd',
      brightWhite: '#efefef',
    },
  },
  {
    preset_key: 'terafox',
    name: 'terafox',
    isPreset: true,
    themeData: {
      foreground: '#e6eaea',
      background: '#152528',
      cursor: '#e6eaea',
      cursorAccent: '#152528',
      selectionBackground: '#293e40',
      selectionForeground: '#e6eaea',
      black: '#2f3239',
      red: '#e85c51',
      green: '#7aa4a1',
      yellow: '#fda47f',
      blue: '#5a93aa',
      magenta: '#ad5c7c',
      cyan: '#a1cdd8',
      white: '#ebebeb',
      brightBlack: '#4e5157',
      brightRed: '#eb746b',
      brightGreen: '#8eb2af',
      brightYellow: '#fdb292',
      brightBlue: '#73a3b7',
      brightMagenta: '#b97490',
      brightCyan: '#afd4de',
      brightWhite: '#eeeeee',
    },
  },
  {
    preset_key: 'Terminal_Basic',
    name: 'Terminal Basic',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#7f7f7f',
      cursorAccent: '#000000',
      selectionBackground: '#a4c9ff',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#990000',
      green: '#00a600',
      yellow: '#999900',
      blue: '#0000b2',
      magenta: '#b200b2',
      cyan: '#00a6b2',
      white: '#bfbfbf',
      brightBlack: '#666666',
      brightRed: '#e50000',
      brightGreen: '#00d900',
      brightYellow: '#e5e500',
      brightBlue: '#0000ff',
      brightMagenta: '#e500e5',
      brightCyan: '#00e5e5',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'Terminal_Basic_Dark',
    name: 'Terminal Basic Dark',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#1d1e1d',
      cursor: '#9d9d9d',
      cursorAccent: '#1d1e1d',
      selectionBackground: '#3f638a',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#c65339',
      green: '#6ac44b',
      yellow: '#b8b74a',
      blue: '#6444ed',
      magenta: '#d357db',
      cyan: '#69c1cf',
      white: '#d1d1d1',
      brightBlack: '#909090',
      brightRed: '#eb5a3a',
      brightGreen: '#77ea51',
      brightYellow: '#efef53',
      brightBlue: '#d09af9',
      brightMagenta: '#eb5af7',
      brightCyan: '#78f1f2',
      brightWhite: '#ededed',
    },
  },
  {
    preset_key: 'Thayer_Bright',
    name: 'Thayer Bright',
    isPreset: true,
    themeData: {
      foreground: '#f8f8f8',
      background: '#1b1d1e',
      cursor: '#fc971f',
      cursorAccent: '#000000',
      selectionBackground: '#4d4d4d',
      selectionForeground: '#ffffff',
      black: '#1b1d1e',
      red: '#f92672',
      green: '#4df840',
      yellow: '#f4fd22',
      blue: '#2757d6',
      magenta: '#8c54fe',
      cyan: '#38c8b5',
      white: '#ccccc6',
      brightBlack: '#505354',
      brightRed: '#ff5995',
      brightGreen: '#b6e354',
      brightYellow: '#feed6c',
      brightBlue: '#3f78ff',
      brightMagenta: '#9e6ffe',
      brightCyan: '#23cfd5',
      brightWhite: '#f8f8f2',
    },
  },
  {
    preset_key: 'The_Hulk',
    name: 'The Hulk',
    isPreset: true,
    themeData: {
      foreground: '#b5b5b5',
      background: '#1b1d1e',
      cursor: '#16b61b',
      cursorAccent: '#000000',
      selectionBackground: '#4d504c',
      selectionForeground: '#0b6309',
      black: '#1b1d1e',
      red: '#269d1b',
      green: '#13ce30',
      yellow: '#63e457',
      blue: '#2525f5',
      magenta: '#641f74',
      cyan: '#378ca9',
      white: '#d9d8d1',
      brightBlack: '#505354',
      brightRed: '#8dff2a',
      brightGreen: '#48ff77',
      brightYellow: '#3afe16',
      brightBlue: '#506b95',
      brightMagenta: '#72589d',
      brightCyan: '#4085a6',
      brightWhite: '#e5e6e1',
    },
  },
  {
    preset_key: 'Tinacious_Design_Dark',
    name: 'Tinacious Design (Dark)',
    isPreset: true,
    themeData: {
      foreground: '#cbcbf0',
      background: '#1d1d26',
      cursor: '#cbcbf0',
      cursorAccent: '#ffffff',
      selectionBackground: '#ff3399',
      selectionForeground: '#ffffff',
      black: '#1d1d26',
      red: '#ff3399',
      green: '#00d364',
      yellow: '#ffcc66',
      blue: '#00cbff',
      magenta: '#cc66ff',
      cyan: '#00ceca',
      white: '#cbcbf0',
      brightBlack: '#636667',
      brightRed: '#ff2f92',
      brightGreen: '#00d364',
      brightYellow: '#ffd479',
      brightBlue: '#00cbff',
      brightMagenta: '#d783ff',
      brightCyan: '#00d5d4',
      brightWhite: '#d5d6f3',
    },
  },
  {
    preset_key: 'Tinacious_Design_Light',
    name: 'Tinacious Design (Light)',
    isPreset: true,
    themeData: {
      foreground: '#1d1d26',
      background: '#f8f8ff',
      cursor: '#cbcbf0',
      cursorAccent: '#ffffff',
      selectionBackground: '#ff3399',
      selectionForeground: '#ffffff',
      black: '#1d1d26',
      red: '#ff3399',
      green: '#00d364',
      yellow: '#ffcc66',
      blue: '#00cbff',
      magenta: '#cc66ff',
      cyan: '#00ceca',
      white: '#cbcbf0',
      brightBlack: '#636667',
      brightRed: '#ff2f92',
      brightGreen: '#00d364',
      brightYellow: '#ffd479',
      brightBlue: '#00cbff',
      brightMagenta: '#d783ff',
      brightCyan: '#00d5d4',
      brightWhite: '#d5d6f3',
    },
  },
  {
    preset_key: 'tokyonight',
    name: 'tokyonight',
    isPreset: true,
    themeData: {
      foreground: '#c0caf5',
      background: '#1a1b26',
      cursor: '#c0caf5',
      cursorAccent: '#15161e',
      selectionBackground: '#33467c',
      selectionForeground: '#c0caf5',
      black: '#15161e',
      red: '#f7768e',
      green: '#9ece6a',
      yellow: '#e0af68',
      blue: '#7aa2f7',
      magenta: '#bb9af7',
      cyan: '#7dcfff',
      white: '#a9b1d6',
      brightBlack: '#414868',
      brightRed: '#f7768e',
      brightGreen: '#9ece6a',
      brightYellow: '#e0af68',
      brightBlue: '#7aa2f7',
      brightMagenta: '#bb9af7',
      brightCyan: '#7dcfff',
      brightWhite: '#c0caf5',
    },
  },
  {
    preset_key: 'tokyonight_moon',
    name: 'tokyonight_moon',
    isPreset: true,
    themeData: {
      foreground: '#c8d3f5',
      background: '#222436',
      cursor: '#c8d3f5',
      cursorAccent: '#222436',
      selectionBackground: '#2d3f76',
      selectionForeground: '#c8d3f5',
      black: '#1b1d2b',
      red: '#ff757f',
      green: '#c3e88d',
      yellow: '#ffc777',
      blue: '#82aaff',
      magenta: '#c099ff',
      cyan: '#86e1fc',
      white: '#828bb8',
      brightBlack: '#444a73',
      brightRed: '#ff757f',
      brightGreen: '#c3e88d',
      brightYellow: '#ffc777',
      brightBlue: '#82aaff',
      brightMagenta: '#c099ff',
      brightCyan: '#86e1fc',
      brightWhite: '#c8d3f5',
    },
  },
  {
    preset_key: 'tokyonight_night',
    name: 'tokyonight_night',
    isPreset: true,
    themeData: {
      foreground: '#c0caf5',
      background: '#1a1b26',
      cursor: '#c0caf5',
      cursorAccent: '#1a1b26',
      selectionBackground: '#283457',
      selectionForeground: '#c0caf5',
      black: '#15161e',
      red: '#f7768e',
      green: '#9ece6a',
      yellow: '#e0af68',
      blue: '#7aa2f7',
      magenta: '#bb9af7',
      cyan: '#7dcfff',
      white: '#a9b1d6',
      brightBlack: '#414868',
      brightRed: '#f7768e',
      brightGreen: '#9ece6a',
      brightYellow: '#e0af68',
      brightBlue: '#7aa2f7',
      brightMagenta: '#bb9af7',
      brightCyan: '#7dcfff',
      brightWhite: '#c0caf5',
    },
  },
  {
    preset_key: 'tokyonight-day',
    name: 'tokyonight-day',
    isPreset: true,
    themeData: {
      foreground: '#3760bf',
      background: '#e1e2e7',
      cursor: '#3760bf',
      cursorAccent: '#e1e2e7',
      selectionBackground: '#99a7df',
      selectionForeground: '#3760bf',
      black: '#e9e9ed',
      red: '#f52a65',
      green: '#587539',
      yellow: '#8c6c3e',
      blue: '#2e7de9',
      magenta: '#9854f1',
      cyan: '#007197',
      white: '#6172b0',
      brightBlack: '#a1a6c5',
      brightRed: '#f52a65',
      brightGreen: '#587539',
      brightYellow: '#8c6c3e',
      brightBlue: '#2e7de9',
      brightMagenta: '#9854f1',
      brightCyan: '#007197',
      brightWhite: '#3760bf',
    },
  },
  {
    preset_key: 'tokyonight-storm',
    name: 'tokyonight-storm',
    isPreset: true,
    themeData: {
      foreground: '#c0caf5',
      background: '#24283b',
      cursor: '#c0caf5',
      cursorAccent: '#1d202f',
      selectionBackground: '#364a82',
      selectionForeground: '#c0caf5',
      black: '#1d202f',
      red: '#f7768e',
      green: '#9ece6a',
      yellow: '#e0af68',
      blue: '#7aa2f7',
      magenta: '#bb9af7',
      cyan: '#7dcfff',
      white: '#a9b1d6',
      brightBlack: '#414868',
      brightRed: '#f7768e',
      brightGreen: '#9ece6a',
      brightYellow: '#e0af68',
      brightBlue: '#7aa2f7',
      brightMagenta: '#bb9af7',
      brightCyan: '#7dcfff',
      brightWhite: '#c0caf5',
    },
  },
  {
    preset_key: 'Tomorrow',
    name: 'Tomorrow',
    isPreset: true,
    themeData: {
      foreground: '#4d4d4c',
      background: '#ffffff',
      cursor: '#4d4d4c',
      cursorAccent: '#ffffff',
      selectionBackground: '#d6d6d6',
      selectionForeground: '#4d4d4c',
      black: '#000000',
      red: '#c82829',
      green: '#718c00',
      yellow: '#eab700',
      blue: '#4271ae',
      magenta: '#8959a8',
      cyan: '#3e999f',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#c82829',
      brightGreen: '#718c00',
      brightYellow: '#eab700',
      brightBlue: '#4271ae',
      brightMagenta: '#8959a8',
      brightCyan: '#3e999f',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Tomorrow_Night',
    name: 'Tomorrow Night',
    isPreset: true,
    themeData: {
      foreground: '#c5c8c6',
      background: '#1d1f21',
      cursor: '#c5c8c6',
      cursorAccent: '#1d1f21',
      selectionBackground: '#373b41',
      selectionForeground: '#c5c8c6',
      black: '#000000',
      red: '#cc6666',
      green: '#b5bd68',
      yellow: '#f0c674',
      blue: '#81a2be',
      magenta: '#b294bb',
      cyan: '#8abeb7',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#cc6666',
      brightGreen: '#b5bd68',
      brightYellow: '#f0c674',
      brightBlue: '#81a2be',
      brightMagenta: '#b294bb',
      brightCyan: '#8abeb7',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Tomorrow_Night_Blue',
    name: 'Tomorrow Night Blue',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#002451',
      cursor: '#ffffff',
      cursorAccent: '#003f8e',
      selectionBackground: '#003f8e',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#ff9da4',
      green: '#d1f1a9',
      yellow: '#ffeead',
      blue: '#bbdaff',
      magenta: '#ebbbff',
      cyan: '#99ffff',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#ff9da4',
      brightGreen: '#d1f1a9',
      brightYellow: '#ffeead',
      brightBlue: '#bbdaff',
      brightMagenta: '#ebbbff',
      brightCyan: '#99ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Tomorrow_Night_Bright',
    name: 'Tomorrow Night Bright',
    isPreset: true,
    themeData: {
      foreground: '#eaeaea',
      background: '#000000',
      cursor: '#eaeaea',
      cursorAccent: '#000000',
      selectionBackground: '#424242',
      selectionForeground: '#eaeaea',
      black: '#000000',
      red: '#d54e53',
      green: '#b9ca4a',
      yellow: '#e7c547',
      blue: '#7aa6da',
      magenta: '#c397d8',
      cyan: '#70c0b1',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#d54e53',
      brightGreen: '#b9ca4a',
      brightYellow: '#e7c547',
      brightBlue: '#7aa6da',
      brightMagenta: '#c397d8',
      brightCyan: '#70c0b1',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Tomorrow_Night_Burns',
    name: 'Tomorrow Night Burns',
    isPreset: true,
    themeData: {
      foreground: '#a1b0b8',
      background: '#151515',
      cursor: '#ff443e',
      cursorAccent: '#708284',
      selectionBackground: '#b0bec5',
      selectionForeground: '#2a2d32',
      black: '#252525',
      red: '#832e31',
      green: '#a63c40',
      yellow: '#d3494e',
      blue: '#fc595f',
      magenta: '#df9395',
      cyan: '#ba8586',
      white: '#f5f5f5',
      brightBlack: '#5d6f71',
      brightRed: '#832e31',
      brightGreen: '#a63c40',
      brightYellow: '#d2494e',
      brightBlue: '#fc595f',
      brightMagenta: '#df9395',
      brightCyan: '#ba8586',
      brightWhite: '#f5f5f5',
    },
  },
  {
    preset_key: 'Tomorrow_Night_Eighties',
    name: 'Tomorrow Night Eighties',
    isPreset: true,
    themeData: {
      foreground: '#cccccc',
      background: '#2d2d2d',
      cursor: '#cccccc',
      cursorAccent: '#2d2d2d',
      selectionBackground: '#515151',
      selectionForeground: '#cccccc',
      black: '#000000',
      red: '#f2777a',
      green: '#99cc99',
      yellow: '#ffcc66',
      blue: '#6699cc',
      magenta: '#cc99cc',
      cyan: '#66cccc',
      white: '#ffffff',
      brightBlack: '#000000',
      brightRed: '#f2777a',
      brightGreen: '#99cc99',
      brightYellow: '#ffcc66',
      brightBlue: '#6699cc',
      brightMagenta: '#cc99cc',
      brightCyan: '#66cccc',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'ToyChest',
    name: 'ToyChest',
    isPreset: true,
    themeData: {
      foreground: '#31d07b',
      background: '#24364b',
      cursor: '#d5d5d5',
      cursorAccent: '#141c25',
      selectionBackground: '#5f217a',
      selectionForeground: '#d5d5d5',
      black: '#2c3f58',
      red: '#be2d26',
      green: '#1a9172',
      yellow: '#db8e27',
      blue: '#325d96',
      magenta: '#8a5edc',
      cyan: '#35a08f',
      white: '#23d183',
      brightBlack: '#336889',
      brightRed: '#dd5944',
      brightGreen: '#31d07b',
      brightYellow: '#e7d84b',
      brightBlue: '#34a6da',
      brightMagenta: '#ae6bdc',
      brightCyan: '#42c3ae',
      brightWhite: '#d5d5d5',
    },
  },
  {
    preset_key: 'Treehouse',
    name: 'Treehouse',
    isPreset: true,
    themeData: {
      foreground: '#786b53',
      background: '#191919',
      cursor: '#fac814',
      cursorAccent: '#191919',
      selectionBackground: '#786b53',
      selectionForeground: '#fac800',
      black: '#321300',
      red: '#b2270e',
      green: '#44a900',
      yellow: '#aa820c',
      blue: '#58859a',
      magenta: '#97363d',
      cyan: '#b25a1e',
      white: '#786b53',
      brightBlack: '#433626',
      brightRed: '#ed5d20',
      brightGreen: '#55f238',
      brightYellow: '#f2b732',
      brightBlue: '#85cfed',
      brightMagenta: '#e14c5a',
      brightCyan: '#f07d14',
      brightWhite: '#ffc800',
    },
  },
  {
    preset_key: 'Twilight',
    name: 'Twilight',
    isPreset: true,
    themeData: {
      foreground: '#ffffd4',
      background: '#141414',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#313131',
      selectionForeground: '#ffffd4',
      black: '#141414',
      red: '#c06d44',
      green: '#afb97a',
      yellow: '#c2a86c',
      blue: '#44474a',
      magenta: '#b4be7c',
      cyan: '#778385',
      white: '#ffffd4',
      brightBlack: '#262626',
      brightRed: '#de7c4c',
      brightGreen: '#ccd88c',
      brightYellow: '#e2c47e',
      brightBlue: '#5a5e62',
      brightMagenta: '#d0dc8e',
      brightCyan: '#8a989b',
      brightWhite: '#ffffd4',
    },
  },
  {
    preset_key: 'Ubuntu',
    name: 'Ubuntu',
    isPreset: true,
    themeData: {
      foreground: '#eeeeec',
      background: '#300a24',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#2e3436',
      red: '#cc0000',
      green: '#4e9a06',
      yellow: '#c4a000',
      blue: '#3465a4',
      magenta: '#75507b',
      cyan: '#06989a',
      white: '#d3d7cf',
      brightBlack: '#555753',
      brightRed: '#ef2929',
      brightGreen: '#8ae234',
      brightYellow: '#fce94f',
      brightBlue: '#729fcf',
      brightMagenta: '#ad7fa8',
      brightCyan: '#34e2e2',
      brightWhite: '#eeeeec',
    },
  },
  {
    preset_key: 'UltraDark',
    name: 'UltraDark',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#000000',
      cursor: '#fefefe',
      cursorAccent: '#000000',
      selectionBackground: '#222222',
      selectionForeground: '#cccccc',
      black: '#000000',
      red: '#f07178',
      green: '#c3e88d',
      yellow: '#ffcb6b',
      blue: '#82aaff',
      magenta: '#c792ea',
      cyan: '#89ddff',
      white: '#cccccc',
      brightBlack: '#333333',
      brightRed: '#f6a9ae',
      brightGreen: '#dbf1ba',
      brightYellow: '#ffdfa6',
      brightBlue: '#b4ccff',
      brightMagenta: '#ddbdf2',
      brightCyan: '#b8eaff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'UltraViolent',
    name: 'UltraViolent',
    isPreset: true,
    themeData: {
      foreground: '#c1c1c1',
      background: '#242728',
      cursor: '#c1c1c1',
      cursorAccent: '#ffffff',
      selectionBackground: '#574c49',
      selectionForeground: '#c3c7cb',
      black: '#242728',
      red: '#ff0090',
      green: '#b6ff00',
      yellow: '#fff727',
      blue: '#47e0fb',
      magenta: '#d731ff',
      cyan: '#0effbb',
      white: '#e1e1e1',
      brightBlack: '#636667',
      brightRed: '#fb58b4',
      brightGreen: '#deff8c',
      brightYellow: '#ebe087',
      brightBlue: '#7fecff',
      brightMagenta: '#e681ff',
      brightCyan: '#69fcd3',
      brightWhite: '#f9f9f5',
    },
  },
  {
    preset_key: 'UnderTheSea',
    name: 'UnderTheSea',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#011116',
      cursor: '#4afcd6',
      cursorAccent: '#031413',
      selectionBackground: '#415555',
      selectionForeground: '#4dffda',
      black: '#022026',
      red: '#b2302d',
      green: '#00a941',
      yellow: '#59819c',
      blue: '#459a86',
      magenta: '#00599d',
      cyan: '#5d7e19',
      white: '#405555',
      brightBlack: '#384451',
      brightRed: '#ff4242',
      brightGreen: '#2aea5e',
      brightYellow: '#8ed4fd',
      brightBlue: '#61d5ba',
      brightMagenta: '#1298ff',
      brightCyan: '#98d028',
      brightWhite: '#58fbd6',
    },
  },
  {
    preset_key: 'Unikitty',
    name: 'Unikitty',
    isPreset: true,
    themeData: {
      foreground: '#0b0b0b',
      background: '#ff8cd9',
      cursor: '#bafc8b',
      cursorAccent: '#202020',
      selectionBackground: '#3ea9fe',
      selectionForeground: '#ffffff',
      black: '#0c0c0c',
      red: '#a80f20',
      green: '#bafc8b',
      yellow: '#eedf4b',
      blue: '#145fcd',
      magenta: '#ff36a2',
      cyan: '#6bd1bc',
      white: '#e2d7e1',
      brightBlack: '#434343',
      brightRed: '#d91329',
      brightGreen: '#d3ffaf',
      brightYellow: '#ffef50',
      brightBlue: '#0075ea',
      brightMagenta: '#fdd5e5',
      brightCyan: '#79ecd5',
      brightWhite: '#fff3fe',
    },
  },
  {
    preset_key: 'Urple',
    name: 'Urple',
    isPreset: true,
    themeData: {
      foreground: '#877a9b',
      background: '#1b1b23',
      cursor: '#a063eb',
      cursorAccent: '#1b1b22',
      selectionBackground: '#a063eb',
      selectionForeground: '#1b1b22',
      black: '#000000',
      red: '#b0425b',
      green: '#37a415',
      yellow: '#ad5c42',
      blue: '#564d9b',
      magenta: '#6c3ca1',
      cyan: '#808080',
      white: '#87799c',
      brightBlack: '#5d3225',
      brightRed: '#ff6388',
      brightGreen: '#29e620',
      brightYellow: '#f08161',
      brightBlue: '#867aed',
      brightMagenta: '#a05eee',
      brightCyan: '#eaeaea',
      brightWhite: '#bfa3ff',
    },
  },
  {
    preset_key: 'Vaughn',
    name: 'Vaughn',
    isPreset: true,
    themeData: {
      foreground: '#dcdccc',
      background: '#25234f',
      cursor: '#ff5555',
      cursorAccent: '#ffffff',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#25234f',
      red: '#705050',
      green: '#60b48a',
      yellow: '#dfaf8f',
      blue: '#5555ff',
      magenta: '#f08cc3',
      cyan: '#8cd0d3',
      white: '#709080',
      brightBlack: '#709080',
      brightRed: '#dca3a3',
      brightGreen: '#60b48a',
      brightYellow: '#f0dfaf',
      brightBlue: '#5555ff',
      brightMagenta: '#ec93d3',
      brightCyan: '#93e0e3',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Vesper',
    name: 'Vesper',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#101010',
      cursor: '#acb1ab',
      cursorAccent: '#ffffff',
      selectionBackground: '#988049',
      selectionForeground: '#acb1ab',
      black: '#101010',
      red: '#f5a191',
      green: '#90b99f',
      yellow: '#e6b99d',
      blue: '#aca1cf',
      magenta: '#e29eca',
      cyan: '#ea83a5',
      white: '#a0a0a0',
      brightBlack: '#7e7e7e',
      brightRed: '#ff8080',
      brightGreen: '#99ffe4',
      brightYellow: '#ffc799',
      brightBlue: '#b9aeda',
      brightMagenta: '#ecaad6',
      brightCyan: '#f591b2',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'VibrantInk',
    name: 'VibrantInk',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#000000',
      cursor: '#ffffff',
      cursorAccent: '#000000',
      selectionBackground: '#b5d5ff',
      selectionForeground: '#000000',
      black: '#878787',
      red: '#ff6600',
      green: '#ccff04',
      yellow: '#ffcc00',
      blue: '#44b4cc',
      magenta: '#9933cc',
      cyan: '#44b4cc',
      white: '#f5f5f5',
      brightBlack: '#555555',
      brightRed: '#ff0000',
      brightGreen: '#00ff00',
      brightYellow: '#ffff00',
      brightBlue: '#0000ff',
      brightMagenta: '#ff00ff',
      brightCyan: '#00ffff',
      brightWhite: '#e5e5e5',
    },
  },
  {
    preset_key: 'vimbones',
    name: 'vimbones',
    isPreset: true,
    themeData: {
      foreground: '#353535',
      background: '#f0f0ca',
      cursor: '#353535',
      cursorAccent: '#f0f0ca',
      selectionBackground: '#d7d7d7',
      selectionForeground: '#353535',
      black: '#f0f0ca',
      red: '#a8334c',
      green: '#4f6c31',
      yellow: '#944927',
      blue: '#286486',
      magenta: '#88507d',
      cyan: '#3b8992',
      white: '#353535',
      brightBlack: '#c6c6a3',
      brightRed: '#94253e',
      brightGreen: '#3f5a22',
      brightYellow: '#803d1c',
      brightBlue: '#1d5573',
      brightMagenta: '#7b3b70',
      brightCyan: '#2b747c',
      brightWhite: '#5c5c5c',
    },
  },
  {
    preset_key: 'Violet_Dark',
    name: 'Violet Dark',
    isPreset: true,
    themeData: {
      foreground: '#708284',
      background: '#1c1d1f',
      cursor: '#708284',
      cursorAccent: '#1c1d1f',
      selectionBackground: '#595ab7',
      selectionForeground: '#1c1d1f',
      black: '#56595c',
      red: '#c94c22',
      green: '#85981c',
      yellow: '#b4881d',
      blue: '#2e8bce',
      magenta: '#d13a82',
      cyan: '#32a198',
      white: '#c9c6bd',
      brightBlack: '#45484b',
      brightRed: '#bd3613',
      brightGreen: '#738a04',
      brightYellow: '#a57705',
      brightBlue: '#2176c7',
      brightMagenta: '#c61c6f',
      brightCyan: '#259286',
      brightWhite: '#c9c6bd',
    },
  },
  {
    preset_key: 'Violet_Light',
    name: 'Violet Light',
    isPreset: true,
    themeData: {
      foreground: '#536870',
      background: '#fcf4dc',
      cursor: '#536870',
      cursorAccent: '#fcf4dc',
      selectionBackground: '#595ab7',
      selectionForeground: '#fcf4dc',
      black: '#56595c',
      red: '#c94c22',
      green: '#85981c',
      yellow: '#b4881d',
      blue: '#2e8bce',
      magenta: '#d13a82',
      cyan: '#32a198',
      white: '#d3d0c9',
      brightBlack: '#45484b',
      brightRed: '#bd3613',
      brightGreen: '#738a04',
      brightYellow: '#a57705',
      brightBlue: '#2176c7',
      brightMagenta: '#c61c6f',
      brightCyan: '#259286',
      brightWhite: '#c9c6bd',
    },
  },
  {
    preset_key: 'WarmNeon',
    name: 'WarmNeon',
    isPreset: true,
    themeData: {
      foreground: '#afdab6',
      background: '#404040',
      cursor: '#30ff24',
      cursorAccent: '#3eef37',
      selectionBackground: '#b0ad21',
      selectionForeground: '#ffffff',
      black: '#000000',
      red: '#e24346',
      green: '#39b13a',
      yellow: '#dae145',
      blue: '#4261c5',
      magenta: '#f920fb',
      cyan: '#2abbd4',
      white: '#d0b8a3',
      brightBlack: '#fefcfc',
      brightRed: '#e97071',
      brightGreen: '#9cc090',
      brightYellow: '#ddda7a',
      brightBlue: '#7b91d6',
      brightMagenta: '#f674ba',
      brightCyan: '#5ed1e5',
      brightWhite: '#d8c8bb',
    },
  },
  {
    preset_key: 'Wez',
    name: 'Wez',
    isPreset: true,
    themeData: {
      foreground: '#b3b3b3',
      background: '#000000',
      cursor: '#53ae71',
      cursorAccent: '#000000',
      selectionBackground: '#4d52f8',
      selectionForeground: '#000000',
      black: '#000000',
      red: '#cc5555',
      green: '#55cc55',
      yellow: '#cdcd55',
      blue: '#5555cc',
      magenta: '#cc55cc',
      cyan: '#7acaca',
      white: '#cccccc',
      brightBlack: '#555555',
      brightRed: '#ff5555',
      brightGreen: '#55ff55',
      brightYellow: '#ffff55',
      brightBlue: '#5555ff',
      brightMagenta: '#ff55ff',
      brightCyan: '#55ffff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Whimsy',
    name: 'Whimsy',
    isPreset: true,
    themeData: {
      foreground: '#b3b0d6',
      background: '#29283b',
      cursor: '#b3b0d6',
      cursorAccent: '#535178',
      selectionBackground: '#3d3c58',
      selectionForeground: '#ffffff',
      black: '#535178',
      red: '#ef6487',
      green: '#5eca89',
      yellow: '#fdd877',
      blue: '#65aef7',
      magenta: '#aa7ff0',
      cyan: '#43c1be',
      white: '#ffffff',
      brightBlack: '#535178',
      brightRed: '#ef6487',
      brightGreen: '#5eca89',
      brightYellow: '#fdd877',
      brightBlue: '#65aef7',
      brightMagenta: '#aa7ff0',
      brightCyan: '#43c1be',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'WildCherry',
    name: 'WildCherry',
    isPreset: true,
    themeData: {
      foreground: '#dafaff',
      background: '#1f1726',
      cursor: '#dd00ff',
      cursorAccent: '#ff00fe',
      selectionBackground: '#002831',
      selectionForeground: '#e4ffff',
      black: '#000507',
      red: '#d94085',
      green: '#2ab250',
      yellow: '#ffd16f',
      blue: '#883cdc',
      magenta: '#ececec',
      cyan: '#c1b8b7',
      white: '#fff8de',
      brightBlack: '#009cc9',
      brightRed: '#da6bac',
      brightGreen: '#f4dca5',
      brightYellow: '#eac066',
      brightBlue: '#308cba',
      brightMagenta: '#ae636b',
      brightCyan: '#ff919d',
      brightWhite: '#e4838d',
    },
  },
  {
    preset_key: 'wilmersdorf',
    name: 'wilmersdorf',
    isPreset: true,
    themeData: {
      foreground: '#c6c6c6',
      background: '#282b33',
      cursor: '#7ebebd',
      cursorAccent: '#1f2024',
      selectionBackground: '#1f2024',
      selectionForeground: '#c6c6c6',
      black: '#34373e',
      red: '#e06383',
      green: '#7ebebd',
      yellow: '#cccccc',
      blue: '#a6c1e0',
      magenta: '#e1c1ee',
      cyan: '#5b94ab',
      white: '#ababab',
      brightBlack: '#434750',
      brightRed: '#fa7193',
      brightGreen: '#8fd7d6',
      brightYellow: '#d1dfff',
      brightBlue: '#b2cff0',
      brightMagenta: '#efccfd',
      brightCyan: '#69abc5',
      brightWhite: '#d3d3d3',
    },
  },
  {
    preset_key: 'Wombat',
    name: 'Wombat',
    isPreset: true,
    themeData: {
      foreground: '#dedacf',
      background: '#171717',
      cursor: '#bbbbbb',
      cursorAccent: '#ffffff',
      selectionBackground: '#453b39',
      selectionForeground: '#b6bbc0',
      black: '#000000',
      red: '#ff615a',
      green: '#b1e969',
      yellow: '#ebd99c',
      blue: '#5da9f6',
      magenta: '#e86aff',
      cyan: '#82fff7',
      white: '#dedacf',
      brightBlack: '#313131',
      brightRed: '#f58c80',
      brightGreen: '#ddf88f',
      brightYellow: '#eee5b2',
      brightBlue: '#a5c7ff',
      brightMagenta: '#ddaaff',
      brightCyan: '#b7fff9',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'Wryan',
    name: 'Wryan',
    isPreset: true,
    themeData: {
      foreground: '#999993',
      background: '#101010',
      cursor: '#9e9ecb',
      cursorAccent: '#000000',
      selectionBackground: '#4d4d4d',
      selectionForeground: '#ffffff',
      black: '#333333',
      red: '#8c4665',
      green: '#287373',
      yellow: '#7c7c99',
      blue: '#395573',
      magenta: '#5e468c',
      cyan: '#31658c',
      white: '#899ca1',
      brightBlack: '#3d3d3d',
      brightRed: '#bf4d80',
      brightGreen: '#53a6a6',
      brightYellow: '#9e9ecb',
      brightBlue: '#477ab3',
      brightMagenta: '#7e62b3',
      brightCyan: '#6096bf',
      brightWhite: '#c0c0c0',
    },
  },
  {
    preset_key: 'xcodedark',
    name: 'xcodedark',
    isPreset: true,
    themeData: {
      foreground: '#dfdfe0',
      background: '#292a30',
      cursor: '#dfdfe0',
      cursorAccent: '#292a30',
      selectionBackground: '#414453',
      selectionForeground: '#dfdfe0',
      black: '#414453',
      red: '#ff8170',
      green: '#78c2b3',
      yellow: '#d9c97c',
      blue: '#4eb0cc',
      magenta: '#ff7ab2',
      cyan: '#b281eb',
      white: '#dfdfe0',
      brightBlack: '#7f8c98',
      brightRed: '#ff8170',
      brightGreen: '#acf2e4',
      brightYellow: '#ffa14f',
      brightBlue: '#6bdfff',
      brightMagenta: '#ff7ab2',
      brightCyan: '#dabaff',
      brightWhite: '#dfdfe0',
    },
  },
  {
    preset_key: 'xcodedarkhc',
    name: 'xcodedarkhc',
    isPreset: true,
    themeData: {
      foreground: '#ffffff',
      background: '#1f1f24',
      cursor: '#ffffff',
      cursorAccent: '#1f1f24',
      selectionBackground: '#43454b',
      selectionForeground: '#ffffff',
      black: '#43454b',
      red: '#ff8a7a',
      green: '#83c9bc',
      yellow: '#d9c668',
      blue: '#4ec4e6',
      magenta: '#ff85b8',
      cyan: '#cda1ff',
      white: '#ffffff',
      brightBlack: '#838991',
      brightRed: '#ff8a7a',
      brightGreen: '#b1faeb',
      brightYellow: '#ffa14f',
      brightBlue: '#6bdfff',
      brightMagenta: '#ff85b8',
      brightCyan: '#e5cfff',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'xcodelight',
    name: 'xcodelight',
    isPreset: true,
    themeData: {
      foreground: '#262626',
      background: '#ffffff',
      cursor: '#262626',
      cursorAccent: '#ffffff',
      selectionBackground: '#b4d8fd',
      selectionForeground: '#262626',
      black: '#b4d8fd',
      red: '#d12f1b',
      green: '#3e8087',
      yellow: '#78492a',
      blue: '#0f68a0',
      magenta: '#ad3da4',
      cyan: '#804fb8',
      white: '#262626',
      brightBlack: '#8a99a6',
      brightRed: '#d12f1b',
      brightGreen: '#23575c',
      brightYellow: '#78492a',
      brightBlue: '#0b4f79',
      brightMagenta: '#ad3da4',
      brightCyan: '#4b21b0',
      brightWhite: '#262626',
    },
  },
  {
    preset_key: 'xcodelighthc',
    name: 'xcodelighthc',
    isPreset: true,
    themeData: {
      foreground: '#000000',
      background: '#ffffff',
      cursor: '#000000',
      cursorAccent: '#ffffff',
      selectionBackground: '#b4d8fd',
      selectionForeground: '#000000',
      black: '#b4d8fd',
      red: '#ad1805',
      green: '#355d61',
      yellow: '#78492a',
      blue: '#0058a1',
      magenta: '#9c2191',
      cyan: '#703daa',
      white: '#000000',
      brightBlack: '#8a99a6',
      brightRed: '#ad1805',
      brightGreen: '#174145',
      brightYellow: '#78492a',
      brightBlue: '#003f73',
      brightMagenta: '#9c2191',
      brightCyan: '#441ea1',
      brightWhite: '#000000',
    },
  },
  {
    preset_key: 'xcodewwdc',
    name: 'xcodewwdc',
    isPreset: true,
    themeData: {
      foreground: '#e7e8eb',
      background: '#292c36',
      cursor: '#e7e8eb',
      cursorAccent: '#292c36',
      selectionBackground: '#494d5c',
      selectionForeground: '#e7e8eb',
      black: '#494d5c',
      red: '#bb383a',
      green: '#94c66e',
      yellow: '#d28e5d',
      blue: '#8884c5',
      magenta: '#b73999',
      cyan: '#00aba4',
      white: '#e7e8eb',
      brightBlack: '#7f869e',
      brightRed: '#bb383a',
      brightGreen: '#94c66e',
      brightYellow: '#d28e5d',
      brightBlue: '#8884c5',
      brightMagenta: '#b73999',
      brightCyan: '#00aba4',
      brightWhite: '#e7e8eb',
    },
  },
  {
    preset_key: 'zenbones',
    name: 'zenbones',
    isPreset: true,
    themeData: {
      foreground: '#2c363c',
      background: '#f0edec',
      cursor: '#2c363c',
      cursorAccent: '#f0edec',
      selectionBackground: '#cbd9e3',
      selectionForeground: '#2c363c',
      black: '#f0edec',
      red: '#a8334c',
      green: '#4f6c31',
      yellow: '#944927',
      blue: '#286486',
      magenta: '#88507d',
      cyan: '#3b8992',
      white: '#2c363c',
      brightBlack: '#cfc1ba',
      brightRed: '#94253e',
      brightGreen: '#3f5a22',
      brightYellow: '#803d1c',
      brightBlue: '#1d5573',
      brightMagenta: '#7b3b70',
      brightCyan: '#2b747c',
      brightWhite: '#4f5e68',
    },
  },
  {
    preset_key: 'zenbones_dark',
    name: 'zenbones_dark',
    isPreset: true,
    themeData: {
      foreground: '#b4bdc3',
      background: '#1c1917',
      cursor: '#c4cacf',
      cursorAccent: '#1c1917',
      selectionBackground: '#3d4042',
      selectionForeground: '#b4bdc3',
      black: '#1c1917',
      red: '#de6e7c',
      green: '#819b69',
      yellow: '#b77e64',
      blue: '#6099c0',
      magenta: '#b279a7',
      cyan: '#66a5ad',
      white: '#b4bdc3',
      brightBlack: '#403833',
      brightRed: '#e8838f',
      brightGreen: '#8bae68',
      brightYellow: '#d68c67',
      brightBlue: '#61abda',
      brightMagenta: '#cf86c1',
      brightCyan: '#65b8c1',
      brightWhite: '#888f94',
    },
  },
  {
    preset_key: 'zenbones_light',
    name: 'zenbones_light',
    isPreset: true,
    themeData: {
      foreground: '#2c363c',
      background: '#f0edec',
      cursor: '#2c363c',
      cursorAccent: '#f0edec',
      selectionBackground: '#cbd9e3',
      selectionForeground: '#2c363c',
      black: '#f0edec',
      red: '#a8334c',
      green: '#4f6c31',
      yellow: '#944927',
      blue: '#286486',
      magenta: '#88507d',
      cyan: '#3b8992',
      white: '#2c363c',
      brightBlack: '#cfc1ba',
      brightRed: '#94253e',
      brightGreen: '#3f5a22',
      brightYellow: '#803d1c',
      brightBlue: '#1d5573',
      brightMagenta: '#7b3b70',
      brightCyan: '#2b747c',
      brightWhite: '#4f5e68',
    },
  },
  {
    preset_key: 'Zenburn',
    name: 'Zenburn',
    isPreset: true,
    themeData: {
      foreground: '#dcdccc',
      background: '#3f3f3f',
      cursor: '#73635a',
      cursorAccent: '#000000',
      selectionBackground: '#21322f',
      selectionForeground: '#c2d87a',
      black: '#4d4d4d',
      red: '#705050',
      green: '#60b48a',
      yellow: '#f0dfaf',
      blue: '#506070',
      magenta: '#dc8cc3',
      cyan: '#8cd0d3',
      white: '#dcdccc',
      brightBlack: '#709080',
      brightRed: '#dca3a3',
      brightGreen: '#c3bf9f',
      brightYellow: '#e0cf9f',
      brightBlue: '#94bff3',
      brightMagenta: '#ec93d3',
      brightCyan: '#93e0e3',
      brightWhite: '#ffffff',
    },
  },
  {
    preset_key: 'zenburned',
    name: 'zenburned',
    isPreset: true,
    themeData: {
      foreground: '#f0e4cf',
      background: '#404040',
      cursor: '#f3eadb',
      cursorAccent: '#404040',
      selectionBackground: '#746956',
      selectionForeground: '#f0e4cf',
      black: '#404040',
      red: '#e3716e',
      green: '#819b69',
      yellow: '#b77e64',
      blue: '#6099c0',
      magenta: '#b279a7',
      cyan: '#66a5ad',
      white: '#f0e4cf',
      brightBlack: '#625a5b',
      brightRed: '#ec8685',
      brightGreen: '#8bae68',
      brightYellow: '#d68c67',
      brightBlue: '#61abda',
      brightMagenta: '#cf86c1',
      brightCyan: '#65b8c1',
      brightWhite: '#c0ab86',
    },
  },
  {
    preset_key: 'zenwritten_dark',
    name: 'zenwritten_dark',
    isPreset: true,
    themeData: {
      foreground: '#bbbbbb',
      background: '#191919',
      cursor: '#c9c9c9',
      cursorAccent: '#191919',
      selectionBackground: '#404040',
      selectionForeground: '#bbbbbb',
      black: '#191919',
      red: '#de6e7c',
      green: '#819b69',
      yellow: '#b77e64',
      blue: '#6099c0',
      magenta: '#b279a7',
      cyan: '#66a5ad',
      white: '#bbbbbb',
      brightBlack: '#3d3839',
      brightRed: '#e8838f',
      brightGreen: '#8bae68',
      brightYellow: '#d68c67',
      brightBlue: '#61abda',
      brightMagenta: '#cf86c1',
      brightCyan: '#65b8c1',
      brightWhite: '#8e8e8e',
    },
  },
  {
    preset_key: 'zenwritten_light',
    name: 'zenwritten_light',
    isPreset: true,
    themeData: {
      foreground: '#353535',
      background: '#eeeeee',
      cursor: '#353535',
      cursorAccent: '#eeeeee',
      selectionBackground: '#d7d7d7',
      selectionForeground: '#353535',
      black: '#eeeeee',
      red: '#a8334c',
      green: '#4f6c31',
      yellow: '#944927',
      blue: '#286486',
      magenta: '#88507d',
      cyan: '#3b8992',
      white: '#353535',
      brightBlack: '#c6c3c3',
      brightRed: '#94253e',
      brightGreen: '#3f5a22',
      brightYellow: '#803d1c',
      brightBlue: '#1d5573',
      brightMagenta: '#7b3b70',
      brightCyan: '#2b747c',
      brightWhite: '#5c5c5c',
    },
  },
];
