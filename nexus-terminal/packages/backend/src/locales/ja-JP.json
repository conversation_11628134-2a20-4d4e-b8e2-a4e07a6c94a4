{"testNotification": {"subject": "星枢ターミナル テスト通知 ({event})", "email": {"body": "これは、星枢ターミナルからのイベント'{event}'に関するテストメールです。\n\nこのメールを受信した場合、SMTP 設定は正常に機能しています。\n\nタイムスタンプ: {timestamp}", "bodyHtml": "<p>これは、<b>星枢ターミナル</b>からのイベント'{event}'に関するテストメールです。</p><p>このメールを受信した場合、SMTP 設定は正常に機能しています。</p><p>タイムスタンプ: {timestamp}</p>"}, "webhook": {"detailsMessage": "これは星枢ターミナルからのテスト通知 (Webhook - i18n) です。 イベント：'{event}'。"}, "telegram": {"detailsMessage": "これは星枢ターミナルからのテスト通知 (Telegram - i18n) です。 イベント：'{event}'。", "bodyTemplate": "*星枢ターミナル テスト通知*\nイベント: `{event}`\nタイムスタンプ: {timestamp}\n詳細:\n```\n{details}\n```"}}, "event": {"LOGIN_SUCCESS": "ログイン成功", "LOGIN_FAILURE": "ログイン失敗", "LOGOUT": "ログアウト", "PASSWORD_CHANGED": "パスワード変更", "2FA_ENABLED": "2段階認証有効", "2FA_DISABLED": "2段階認証無効", "CONNECTION_CREATED": "接続を作成しました", "CONNECTION_UPDATED": "接続を更新しました", "CONNECTION_DELETED": "接続を削除しました", "PROXY_CREATED": "プロキシを作成しました", "PROXY_UPDATED": "プロキシを更新しました", "PROXY_DELETED": "プロキシを削除しました", "TAG_CREATED": "タグを作成しました", "TAG_UPDATED": "タグを更新しました", "TAG_DELETED": "タグを削除しました", "SETTINGS_UPDATED": "設定を更新しました", "IP_WHITELIST_UPDATED": "IP ホワイトリストを更新しました", "NOTIFICATION_SETTING_CREATED": "通知設定を作成しました", "NOTIFICATION_SETTING_UPDATED": "通知設定を更新しました", "NOTIFICATION_SETTING_DELETED": "通知設定を削除しました", "SSH_CONNECT_SUCCESS": "SSH 接続成功", "SSH_CONNECT_FAILURE": "SSH 接続失敗", "SSH_SHELL_FAILURE": "SSH Shell オープン失敗", "DATABASE_MIGRATION": "データベース移行", "ADMIN_SETUP_COMPLETE": "初期管理者設定完了", "PASSKEY_REGISTERED": "パスキー登録済み", "PASSKEY_AUTH_SUCCESS": "パスキー認証成功", "PASSKEY_AUTH_FAILURE": "パスキー認証失敗", "PASSKEY_DELETED": "パスキー削除済み", "PASSKEY_DELETE_UNAUTHORIZED": "パスキー削除権限なし", "PASSKEY_NAME_UPDATED": "パスキー名更新済み", "PASSKEY_NAME_UPDATE_UNAUTHORIZED": "パスキー名更新権限なし"}}