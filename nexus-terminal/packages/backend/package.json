{"name": "@nexus-terminal/backend", "version": "0.1.0", "private": true, "main": "dist/index.js", "scripts": {"build": "tsc && copyfiles -u 1 \"src/locales/**/*.json\" dist/src", "start": "node dist/index.js", "dev": "cross-env NODE_ENV=development npx ts-node-dev --respawn --transpile-only src/index.ts"}, "dependencies": {"@simplewebauthn/server": "^13.1.1", "@types/archiver": "^6.0.3", "@types/multer": "^1.4.12", "@types/session-file-store": "^1.2.5", "@types/uuid": "^10.0.0", "adm-zip": "^0.5.16", "archiver": "^7.0.1", "archiver-zip-encrypted": "^2.0.0", "axios": "^1.9.0", "bcrypt": "^5.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.5.0", "easyzip": "^1.0.3", "express": "^5.1.0", "express-session": "^1.18.1", "i18next": "^25.0.0", "i18next-fs-backend": "^2.6.0", "iconv-lite": "^0.6.3", "ipaddr.js": "^1.9.1", "jschardet": "^3.1.4", "multer": ">=2.0.1", "nodemailer": "^6.10.1", "qrcode": "^1.5.4", "sanitize-filename": "^1.6.3", "session-file-store": "^1.5.0", "socks": "^2.8.4", "speakeasy": "^2.0.0", "sqlite3": "^5.1.7", "ssh2": "^1.16.0", "uuid": "^11.1.0", "ws": "^8.18.1", "xterm": "^5.3.0"}, "devDependencies": {"@types/adm-zip": "^0.5.7", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.1", "@types/express-session": "^1.18.1", "@types/node": "^20.0.0", "@types/nodemailer": "^6.4.17", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "@types/sqlite3": "^3.1.11", "@types/ssh2": "^1.15.5", "@types/ws": "^8.18.1", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "ts-node-dev": "^2.0.0", "typescript": "^5.0.0"}}