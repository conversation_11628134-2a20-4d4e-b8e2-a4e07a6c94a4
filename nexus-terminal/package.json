{"name": "nexus-terminal", "version": "1.0.0", "main": "index.js", "private": true, "workspaces": ["packages/*"], "directories": {"doc": "doc"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "postinstall": "patch-package"}, "repository": {"type": "git", "url": "git+https://github.com/Heavrnl/nexus-terminal.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Heavrnl/nexus-terminal/issues"}, "homepage": "https://github.com/Heavrnl/nexus-terminal#readme", "description": "", "dependencies": {"archiver-zip-encrypted": "^2.0.0", "axios": "^1.8.4", "element-plus": "^2.9.11", "fs-extra": "^11.3.0", "pinia-plugin-persistedstate": "^4.2.0", "plist": "^3.1.0", "vuedraggable": "^4.1.0", "ws": "^8.18.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.4", "@types/qrcode": "^1.5.5", "@types/speakeasy": "^2.0.10", "patch-package": "^8.0.0", "qrcode": "^1.5.4", "speakeasy": "^2.0.0", "sqlite3": "^5.1.7"}, "overrides": {"esbuild": "^0.25.0"}}