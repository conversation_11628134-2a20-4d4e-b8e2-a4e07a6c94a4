services:
  frontend:
    image: heavrnl/nexus-terminal-frontend:latest
    container_name: nexus-terminal-frontend
    ports:
      - "18111:80"
    depends_on:
      - backend
      - remote-gateway
    networks:
      - nexus-terminal-network

  backend:
    image: heavrnl/nexus-terminal-backend:latest
    container_name: nexus-terminal-backend
    env_file:
      - .env
    environment:
      NODE_ENV: production
      PORT: 3001
    volumes:
      - ./data:/app/data
    networks:
      - nexus-terminal-network

  remote-gateway:
    image: heavrnl/nexus-terminal-remote-gateway:latest
    container_name: nexus-terminal-remote-gateway
    environment:
      GUACD_HOST: guacd
      GUACD_PORT: 4822
      REMOTE_GATEWAY_API_PORT: 9090
      REMOTE_GATEWAY_WS_PORT: 8080
      FRONTEND_URL: http://frontend
      MAIN_BACKEND_URL: http://backend:3001
      NODE_ENV: production
    networks:
      - nexus-terminal-network
    depends_on:
      - guacd
      - backend

  guacd:
    image: guacamole/guacd:latest
    container_name: nexus-terminal-guacd
    networks:
      - nexus-terminal-network
    restart: unless-stopped

networks:
  nexus-terminal-network:
    driver: bridge
    name: nexus-terminal-network
    enable_ipv6: true
    ipam:
      config:
        - subnet: fd01::/80    
          gateway: fd01::1    
