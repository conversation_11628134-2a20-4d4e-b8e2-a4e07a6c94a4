# 权限系统配置管理指南

## 概述

本文档详细介绍权限系统的配置管理，包括配置文件结构、环境变量设置、生产环境部署配置等内容。

## 目录

1. [配置文件结构](#配置文件结构)
2. [环境变量配置](#环境变量配置)
3. [数据库配置](#数据库配置)
4. [缓存配置](#缓存配置)
5. [安全配置](#安全配置)
6. [性能配置](#性能配置)
7. [生产环境配置](#生产环境配置)
8. [配置热重载](#配置热重载)
9. [配置验证](#配置验证)
10. [故障排查](#故障排查)

## 配置文件结构

### 主配置文件

权限系统的主配置文件位于 `backend/config/permissions.json`：

```json
{
  "version": "1.0.0",
  "environment": "development",
  "cache": {
    "enabled": true,
    "type": "redis",
    "ttl": 300,
    "maxSize": 10000,
    "localCache": {
      "enabled": true,
      "maxSize": 1000,
      "ttl": 60
    }
  },
  "audit": {
    "enabled": true,
    "retention": 90,
    "logLevel": "info",
    "batchSize": 100,
    "flushInterval": 5000
  },
  "security": {
    "requireSecondAuth": [
      "user.delete.any",
      "system.backup",
      "role.assign"
    ],
    "ipWhitelist": [
      "***********/24",
      "10.0.0.0/8"
    ],
    "maxFailedAttempts": 5,
    "lockoutDuration": 900,
    "sessionTimeout": 3600
  },
  "performance": {
    "maxResponseTime": 50,
    "cacheHitRateThreshold": 0.95,
    "maxConcurrentRequests": 1000,
    "requestTimeout": 30000
  },
  "monitoring": {
    "enabled": true,
    "metricsInterval": 60,
    "alertThresholds": {
      "responseTime": 100,
      "errorRate": 0.05,
      "cacheHitRate": 0.90
    }
  }
}
```

### 生产环境配置

生产环境配置文件 `backend/config/permissions.production.json`：

```json
{
  "version": "1.0.0",
  "environment": "production",
  "cache": {
    "enabled": true,
    "type": "redis",
    "ttl": 600,
    "maxSize": 50000,
    "localCache": {
      "enabled": true,
      "maxSize": 5000,
      "ttl": 120
    },
    "cluster": {
      "enabled": true,
      "nodes": [
        "redis-1:6379",
        "redis-2:6379",
        "redis-3:6379"
      ]
    }
  },
  "audit": {
    "enabled": true,
    "retention": 365,
    "logLevel": "warn",
    "batchSize": 500,
    "flushInterval": 1000,
    "archiveEnabled": true,
    "archiveInterval": 86400
  },
  "security": {
    "requireSecondAuth": [
      "user.delete.any",
      "system.backup",
      "role.assign",
      "system.settings"
    ],
    "ipWhitelist": [
      "10.0.0.0/8"
    ],
    "maxFailedAttempts": 3,
    "lockoutDuration": 1800,
    "sessionTimeout": 1800,
    "encryptionEnabled": true
  },
  "performance": {
    "maxResponseTime": 30,
    "cacheHitRateThreshold": 0.98,
    "maxConcurrentRequests": 5000,
    "requestTimeout": 15000
  },
  "monitoring": {
    "enabled": true,
    "metricsInterval": 30,
    "alertThresholds": {
      "responseTime": 50,
      "errorRate": 0.01,
      "cacheHitRate": 0.95
    },
    "exportMetrics": true,
    "metricsEndpoint": "/metrics"
  }
}
```

## 环境变量配置

### 基础环境变量

```bash
# 应用环境
NODE_ENV=production
PORT=3000

# 权限系统配置
PERMISSION_CONFIG_PATH=/app/config/permissions.production.json
PERMISSION_CACHE_ENABLED=true
PERMISSION_AUDIT_ENABLED=true

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sitemanager
DB_USER=permission_user
DB_PASSWORD=secure_password
DB_CONNECTION_LIMIT=20
DB_TIMEOUT=30000

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password
REDIS_DB=0
REDIS_CLUSTER_ENABLED=false
REDIS_CONNECTION_TIMEOUT=5000

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# 安全配置
ENCRYPTION_KEY=your-32-character-encryption-key
SALT_ROUNDS=12
SESSION_SECRET=your-session-secret

# 监控配置
MONITORING_ENABLED=true
METRICS_PORT=9090
LOG_LEVEL=info
```

### Docker环境变量

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    environment:
      - NODE_ENV=production
      - PERMISSION_CONFIG_PATH=/app/config/permissions.production.json
      - DB_HOST=mysql
      - REDIS_HOST=redis
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
    env_file:
      - .env.production
```

### Kubernetes配置

```yaml
# k8s-configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: permission-config
data:
  NODE_ENV: "production"
  PERMISSION_CACHE_ENABLED: "true"
  PERMISSION_AUDIT_ENABLED: "true"
  DB_HOST: "mysql-service"
  REDIS_HOST: "redis-service"
  MONITORING_ENABLED: "true"

---
apiVersion: v1
kind: Secret
metadata:
  name: permission-secrets
type: Opaque
data:
  JWT_SECRET: <base64-encoded-secret>
  DB_PASSWORD: <base64-encoded-password>
  REDIS_PASSWORD: <base64-encoded-password>
  ENCRYPTION_KEY: <base64-encoded-key>
```

## 数据库配置

### 连接配置

```javascript
// config/database.js
module.exports = {
  development: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    database: process.env.DB_NAME || 'sitemanager',
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    dialect: 'mysql',
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000
    },
    logging: console.log
  },
  production: {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT || 3306,
    database: process.env.DB_NAME,
    username: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    dialect: 'mysql',
    pool: {
      max: 20,
      min: 5,
      acquire: 60000,
      idle: 10000
    },
    logging: false,
    ssl: {
      require: true,
      rejectUnauthorized: false
    }
  }
};
```

### 权限表索引优化

```sql
-- 权限查询优化索引
CREATE INDEX idx_user_role ON users(role);
CREATE INDEX idx_role_permissions_role ON role_permissions(role_id);
CREATE INDEX idx_user_custom_permissions_user ON user_custom_permissions(user_id);
CREATE INDEX idx_audit_logs_user_action ON audit_logs(user_id, action);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_resource_ownership_type_id ON resource_ownership(resource_type, resource_id);
```

## 缓存配置

### Redis单机配置

```javascript
// config/redis.js
const Redis = require('ioredis');

const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD,
  db: process.env.REDIS_DB || 0,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000
};

module.exports = new Redis(redisConfig);
```

### Redis集群配置

```javascript
// config/redis-cluster.js
const Redis = require('ioredis');

const clusterConfig = {
  enableOfflineQueue: false,
  redisOptions: {
    password: process.env.REDIS_PASSWORD,
    connectTimeout: 10000,
    commandTimeout: 5000
  },
  clusterRetryDelayOnFailover: 100,
  maxRetriesPerRequest: 3
};

const cluster = new Redis.Cluster([
  { host: 'redis-1', port: 6379 },
  { host: 'redis-2', port: 6379 },
  { host: 'redis-3', port: 6379 }
], clusterConfig);

module.exports = cluster;
```

### 缓存策略配置

```javascript
// config/cache-strategy.js
module.exports = {
  // 权限缓存配置
  permissions: {
    ttl: 300,           // 5分钟
    maxSize: 10000,     // 最大缓存项数
    updateStrategy: 'write-through'
  },
  
  // 角色缓存配置
  roles: {
    ttl: 600,           // 10分钟
    maxSize: 1000,
    updateStrategy: 'write-behind'
  },
  
  // 用户会话缓存
  sessions: {
    ttl: 1800,          // 30分钟
    maxSize: 50000,
    updateStrategy: 'write-through'
  }
};
```

## 安全配置

### 权限安全配置

```json
{
  "security": {
    "passwordPolicy": {
      "minLength": 8,
      "requireUppercase": true,
      "requireLowercase": true,
      "requireNumbers": true,
      "requireSpecialChars": true,
      "maxAge": 90
    },
    "sessionSecurity": {
      "httpOnly": true,
      "secure": true,
      "sameSite": "strict",
      "maxAge": 3600
    },
    "rateLimiting": {
      "enabled": true,
      "windowMs": 900000,
      "maxRequests": 100,
      "skipSuccessfulRequests": false
    },
    "bruteForceProtection": {
      "enabled": true,
      "maxAttempts": 5,
      "lockoutDuration": 900,
      "progressiveDelay": true
    }
  }
}
```

### HTTPS和SSL配置

```javascript
// config/ssl.js
const fs = require('fs');
const path = require('path');

module.exports = {
  https: {
    enabled: process.env.HTTPS_ENABLED === 'true',
    port: process.env.HTTPS_PORT || 443,
    options: {
      key: fs.readFileSync(path.join(__dirname, '../ssl/private.key')),
      cert: fs.readFileSync(path.join(__dirname, '../ssl/certificate.crt')),
      ca: fs.readFileSync(path.join(__dirname, '../ssl/ca-bundle.crt'))
    }
  },
  hsts: {
    enabled: true,
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
};
```

## 性能配置

### 应用性能配置

```json
{
  "performance": {
    "clustering": {
      "enabled": true,
      "workers": "auto"
    },
    "compression": {
      "enabled": true,
      "level": 6,
      "threshold": 1024
    },
    "keepAlive": {
      "enabled": true,
      "timeout": 5000
    },
    "requestSizeLimit": {
      "json": "10mb",
      "urlencoded": "10mb",
      "raw": "10mb"
    }
  }
}
```

### 数据库性能配置

```javascript
// config/database-performance.js
module.exports = {
  pool: {
    max: 20,                    // 最大连接数
    min: 5,                     // 最小连接数
    acquire: 60000,             // 获取连接超时时间
    idle: 10000,                // 连接空闲时间
    evict: 1000,                // 连接回收检查间隔
    handleDisconnects: true     // 自动处理断开连接
  },
  dialectOptions: {
    charset: 'utf8mb4',
    collate: 'utf8mb4_unicode_ci',
    timezone: '+00:00',
    supportBigNumbers: true,
    bigNumberStrings: true,
    acquireTimeout: 60000,
    timeout: 60000,
    reconnect: true
  }
};
```

## 生产环境配置

### 部署配置检查清单

```bash
#!/bin/bash
# deployment-checklist.sh

echo "权限系统生产环境配置检查..."

# 检查环境变量
check_env_var() {
  if [ -z "${!1}" ]; then
    echo "❌ 环境变量 $1 未设置"
    exit 1
  else
    echo "✅ 环境变量 $1 已设置"
  fi
}

# 必需的环境变量
check_env_var "NODE_ENV"
check_env_var "DB_HOST"
check_env_var "DB_PASSWORD"
check_env_var "REDIS_HOST"
check_env_var "JWT_SECRET"
check_env_var "ENCRYPTION_KEY"

# 检查配置文件
if [ -f "config/permissions.production.json" ]; then
  echo "✅ 生产环境配置文件存在"
else
  echo "❌ 生产环境配置文件不存在"
  exit 1
fi

# 检查SSL证书
if [ -f "ssl/certificate.crt" ] && [ -f "ssl/private.key" ]; then
  echo "✅ SSL证书文件存在"
else
  echo "⚠️  SSL证书文件不存在，将使用HTTP"
fi

# 检查数据库连接
node -e "
const db = require('./utils/db');
db.execute('SELECT 1').then(() => {
  console.log('✅ 数据库连接正常');
  process.exit(0);
}).catch(err => {
  console.log('❌ 数据库连接失败:', err.message);
  process.exit(1);
});
"

# 检查Redis连接
node -e "
const Redis = require('ioredis');
const redis = new Redis(process.env.REDIS_HOST);
redis.ping().then(() => {
  console.log('✅ Redis连接正常');
  redis.disconnect();
  process.exit(0);
}).catch(err => {
  console.log('❌ Redis连接失败:', err.message);
  process.exit(1);
});
"

echo "🎉 生产环境配置检查完成"
```

### Docker生产配置

```dockerfile
# Dockerfile.production
FROM node:18-alpine

WORKDIR /app

# 安装依赖
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 复制应用代码
COPY . .

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 设置权限
RUN chown -R nodejs:nodejs /app
USER nodejs

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

EXPOSE 3000

CMD ["node", "simple-server.js"]
```

### Kubernetes生产配置

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: permission-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: permission-system
  template:
    metadata:
      labels:
        app: permission-system
    spec:
      containers:
      - name: app
        image: permission-system:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        envFrom:
        - configMapRef:
            name: permission-config
        - secretRef:
            name: permission-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 配置热重载

### 配置监听器

```javascript
// config/config-watcher.js
const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');

class ConfigWatcher extends EventEmitter {
  constructor(configPath) {
    super();
    this.configPath = configPath;
    this.config = null;
    this.watcher = null;
    this.loadConfig();
    this.startWatching();
  }

  loadConfig() {
    try {
      const configData = fs.readFileSync(this.configPath, 'utf8');
      const newConfig = JSON.parse(configData);
      
      if (this.validateConfig(newConfig)) {
        const oldConfig = this.config;
        this.config = newConfig;
        this.emit('configChanged', newConfig, oldConfig);
        console.log('配置文件已重新加载');
      } else {
        console.error('配置文件验证失败');
      }
    } catch (error) {
      console.error('加载配置文件失败:', error.message);
    }
  }

  validateConfig(config) {
    // 配置验证逻辑
    const requiredFields = ['cache', 'audit', 'security', 'performance'];
    return requiredFields.every(field => config.hasOwnProperty(field));
  }

  startWatching() {
    this.watcher = fs.watch(this.configPath, (eventType) => {
      if (eventType === 'change') {
        setTimeout(() => this.loadConfig(), 100); // 延迟加载避免文件写入未完成
      }
    });
  }

  stopWatching() {
    if (this.watcher) {
      this.watcher.close();
    }
  }

  getConfig() {
    return this.config;
  }
}

module.exports = ConfigWatcher;
```

### 配置更新处理

```javascript
// services/ConfigService.js
const ConfigWatcher = require('../config/config-watcher');
const PermissionCacheService = require('./PermissionCacheService');

class ConfigService {
  constructor() {
    this.configWatcher = new ConfigWatcher('./config/permissions.json');
    this.cacheService = new PermissionCacheService();
    
    this.configWatcher.on('configChanged', this.handleConfigChange.bind(this));
  }

  async handleConfigChange(newConfig, oldConfig) {
    console.log('处理配置变更...');
    
    // 缓存配置变更
    if (this.configChanged(newConfig.cache, oldConfig?.cache)) {
      await this.updateCacheConfig(newConfig.cache);
    }
    
    // 安全配置变更
    if (this.configChanged(newConfig.security, oldConfig?.security)) {
      await this.updateSecurityConfig(newConfig.security);
    }
    
    // 性能配置变更
    if (this.configChanged(newConfig.performance, oldConfig?.performance)) {
      await this.updatePerformanceConfig(newConfig.performance);
    }
    
    console.log('配置变更处理完成');
  }

  configChanged(newConfig, oldConfig) {
    return JSON.stringify(newConfig) !== JSON.stringify(oldConfig);
  }

  async updateCacheConfig(cacheConfig) {
    // 更新缓存配置
    this.cacheService.updateConfig(cacheConfig);
    
    if (!cacheConfig.enabled) {
      await this.cacheService.clearAllCache();
    }
  }

  async updateSecurityConfig(securityConfig) {
    // 更新安全配置
    global.securityConfig = securityConfig;
  }

  async updatePerformanceConfig(performanceConfig) {
    // 更新性能配置
    global.performanceConfig = performanceConfig;
  }
}

module.exports = ConfigService;
```

## 配置验证

### 配置验证器

```javascript
// utils/config-validator.js
const Joi = require('joi');

const configSchema = Joi.object({
  version: Joi.string().required(),
  environment: Joi.string().valid('development', 'production', 'test').required(),
  
  cache: Joi.object({
    enabled: Joi.boolean().required(),
    type: Joi.string().valid('redis', 'memory').required(),
    ttl: Joi.number().min(60).max(3600).required(),
    maxSize: Joi.number().min(1000).required(),
    localCache: Joi.object({
      enabled: Joi.boolean().required(),
      maxSize: Joi.number().min(100).required(),
      ttl: Joi.number().min(30).max(300).required()
    }).required()
  }).required(),
  
  audit: Joi.object({
    enabled: Joi.boolean().required(),
    retention: Joi.number().min(30).max(365).required(),
    logLevel: Joi.string().valid('debug', 'info', 'warn', 'error').required(),
    batchSize: Joi.number().min(10).max(1000).required(),
    flushInterval: Joi.number().min(1000).max(60000).required()
  }).required(),
  
  security: Joi.object({
    requireSecondAuth: Joi.array().items(Joi.string()).required(),
    ipWhitelist: Joi.array().items(Joi.string()).required(),
    maxFailedAttempts: Joi.number().min(3).max(10).required(),
    lockoutDuration: Joi.number().min(300).max(3600).required(),
    sessionTimeout: Joi.number().min(900).max(7200).required()
  }).required(),
  
  performance: Joi.object({
    maxResponseTime: Joi.number().min(10).max(1000).required(),
    cacheHitRateThreshold: Joi.number().min(0.8).max(1.0).required(),
    maxConcurrentRequests: Joi.number().min(100).required(),
    requestTimeout: Joi.number().min(5000).max(60000).required()
  }).required()
});

function validateConfig(config) {
  const { error, value } = configSchema.validate(config, {
    abortEarly: false,
    allowUnknown: true
  });
  
  if (error) {
    const errors = error.details.map(detail => detail.message);
    throw new Error(`配置验证失败: ${errors.join(', ')}`);
  }
  
  return value;
}

module.exports = { validateConfig, configSchema };
```

### 配置测试

```javascript
// test/config.test.js
const { validateConfig } = require('../utils/config-validator');
const fs = require('fs');

describe('配置验证测试', () => {
  test('开发环境配置应该有效', () => {
    const config = JSON.parse(fs.readFileSync('./config/permissions.json', 'utf8'));
    expect(() => validateConfig(config)).not.toThrow();
  });
  
  test('生产环境配置应该有效', () => {
    const config = JSON.parse(fs.readFileSync('./config/permissions.production.json', 'utf8'));
    expect(() => validateConfig(config)).not.toThrow();
  });
  
  test('无效配置应该抛出错误', () => {
    const invalidConfig = {
      version: '1.0.0',
      environment: 'invalid'
    };
    expect(() => validateConfig(invalidConfig)).toThrow();
  });
});
```

## 故障排查

### 配置问题诊断

```bash
#!/bin/bash
# config-diagnostic.sh

echo "权限系统配置诊断..."

# 检查配置文件语法
echo "检查配置文件语法..."
node -e "
try {
  const config = require('./config/permissions.json');
  console.log('✅ 配置文件语法正确');
} catch (error) {
  console.log('❌ 配置文件语法错误:', error.message);
  process.exit(1);
}
"

# 验证配置内容
echo "验证配置内容..."
node -e "
const { validateConfig } = require('./utils/config-validator');
const config = require('./config/permissions.json');
try {
  validateConfig(config);
  console.log('✅ 配置内容验证通过');
} catch (error) {
  console.log('❌ 配置内容验证失败:', error.message);
  process.exit(1);
}
"

# 检查环境变量
echo "检查关键环境变量..."
required_vars=("NODE_ENV" "DB_HOST" "REDIS_HOST" "JWT_SECRET")
for var in "${required_vars[@]}"; do
  if [ -z "${!var}" ]; then
    echo "❌ 环境变量 $var 未设置"
  else
    echo "✅ 环境变量 $var 已设置"
  fi
done

echo "配置诊断完成"
```

### 常见配置问题

1. **缓存连接失败**
   ```bash
   # 检查Redis连接
   redis-cli -h $REDIS_HOST -p $REDIS_PORT ping
   
   # 检查Redis配置
   redis-cli -h $REDIS_HOST -p $REDIS_PORT config get maxmemory
   ```

2. **数据库连接问题**
   ```bash
   # 测试数据库连接
   mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD -e "SELECT 1"
   
   # 检查数据库权限
   mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD -e "SHOW GRANTS"
   ```

3. **权限配置错误**
   ```javascript
   // 验证权限配置
   const PermissionService = require('./services/PermissionService');
   const service = new PermissionService();
   
   service.validatePermissionConfig().then(result => {
     console.log('权限配置验证结果:', result);
   }).catch(error => {
     console.error('权限配置验证失败:', error);
   });
   ```

### 配置备份和恢复

```bash
#!/bin/bash
# config-backup.sh

BACKUP_DIR="./backups/config"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份配置文件
cp config/permissions.json $BACKUP_DIR/permissions_$TIMESTAMP.json
cp config/permissions.production.json $BACKUP_DIR/permissions.production_$TIMESTAMP.json

# 备份环境变量
env | grep -E "(PERMISSION|DB_|REDIS_|JWT_)" > $BACKUP_DIR/env_$TIMESTAMP.txt

echo "配置备份完成: $BACKUP_DIR"

# 清理旧备份（保留最近10个）
ls -t $BACKUP_DIR/permissions_*.json | tail -n +11 | xargs -r rm
```

## 总结

权限系统的配置管理是确保系统安全和性能的关键环节。通过合理的配置结构、严格的配置验证和完善的配置监控，可以有效保障权限系统的稳定运行。

在配置管理过程中，请注意：

1. **安全性**: 敏感配置信息使用环境变量或密钥管理系统
2. **可维护性**: 配置文件结构清晰，注释完整
3. **可扩展性**: 配置支持热重载和动态更新
4. **可监控性**: 配置变更有审计记录和告警机制

如有配置相关问题，请参考故障排查部分或联系系统管理员。