# 权限系统故障排查手册

## 概述

本文档提供权限系统常见问题的诊断方法和解决方案，帮助管理员快速定位和解决权限相关故障。

## 目录

1. [故障分类](#故障分类)
2. [诊断工具](#诊断工具)
3. [权限验证问题](#权限验证问题)
4. [缓存相关问题](#缓存相关问题)
5. [性能问题](#性能问题)
6. [数据库问题](#数据库问题)
7. [配置问题](#配置问题)
8. [安全问题](#安全问题)
9. [监控和告警](#监控和告警)
10. [应急处理](#应急处理)

## 故障分类

### 按严重程度分类

#### 🔴 严重故障 (P0)
- 权限系统完全不可用
- 所有用户无法登录
- 数据库连接完全失败
- 安全漏洞导致权限绕过

#### 🟡 重要故障 (P1)
- 部分权限验证失败
- 缓存系统不可用
- 性能严重下降
- 审计日志记录失败

#### 🟢 一般故障 (P2)
- 个别用户权限异常
- 缓存命中率下降
- 响应时间轻微增加
- 配置警告

### 按功能模块分类

- **认证模块**: 登录、JWT令牌、会话管理
- **授权模块**: 权限验证、角色检查、资源访问
- **缓存模块**: Redis连接、缓存命中、数据一致性
- **审计模块**: 日志记录、查询、统计分析
- **配置模块**: 配置加载、热重载、验证

## 诊断工具

### 系统健康检查脚本

```bash
#!/bin/bash
# permission-health-check.sh

echo "🔍 权限系统健康检查开始..."
echo "=================================="

# 检查服务状态
check_service_status() {
  echo "📊 检查服务状态..."
  
  # 检查应用进程
  if pgrep -f "node.*simple-server.js" > /dev/null; then
    echo "✅ 应用服务运行正常"
  else
    echo "❌ 应用服务未运行"
    return 1
  fi
  
  # 检查端口监听
  if netstat -tlnp | grep :3000 > /dev/null; then
    echo "✅ 端口3000监听正常"
  else
    echo "❌ 端口3000未监听"
    return 1
  fi
}

# 检查数据库连接
check_database() {
  echo "🗄️  检查数据库连接..."
  
  node -e "
    const db = require('./utils/db');
    db.execute('SELECT 1 as test')
      .then(() => {
        console.log('✅ 数据库连接正常');
        process.exit(0);
      })
      .catch(err => {
        console.log('❌ 数据库连接失败:', err.message);
        process.exit(1);
      });
  " || return 1
}

# 检查Redis连接
check_redis() {
  echo "💾 检查Redis连接..."
  
  node -e "
    const Redis = require('ioredis');
    const redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: process.env.REDIS_PORT || 6379
    });
    
    redis.ping()
      .then(() => {
        console.log('✅ Redis连接正常');
        redis.disconnect();
        process.exit(0);
      })
      .catch(err => {
        console.log('❌ Redis连接失败:', err.message);
        process.exit(1);
      });
  " || return 1
}

# 检查权限API
check_permission_api() {
  echo "🔐 检查权限API..."
  
  # 获取管理员token（需要预先配置）
  if [ -z "$ADMIN_TOKEN" ]; then
    echo "⚠️  未设置ADMIN_TOKEN，跳过API检查"
    return 0
  fi
  
  # 测试权限查询API
  response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $ADMIN_TOKEN" \
    http://localhost:3000/api/v1/auth/permissions)
  
  http_code="${response: -3}"
  if [ "$http_code" = "200" ]; then
    echo "✅ 权限API响应正常"
  else
    echo "❌ 权限API响应异常: HTTP $http_code"
    return 1
  fi
}

# 检查缓存性能
check_cache_performance() {
  echo "⚡ 检查缓存性能..."
  
  node -e "
    const PermissionCacheService = require('./services/PermissionCacheService');
    const cache = new PermissionCacheService();
    
    const testKey = 'health_check_' + Date.now();
    const testData = { test: true, timestamp: Date.now() };
    
    const start = Date.now();
    cache.set(testKey, testData)
      .then(() => cache.get(testKey))
      .then(result => {
        const duration = Date.now() - start;
        if (duration < 100) {
          console.log('✅ 缓存性能正常 (' + duration + 'ms)');
        } else {
          console.log('⚠️  缓存性能较慢 (' + duration + 'ms)');
        }
        return cache.del(testKey);
      })
      .then(() => process.exit(0))
      .catch(err => {
        console.log('❌ 缓存测试失败:', err.message);
        process.exit(1);
      });
  " || return 1
}

# 执行所有检查
main() {
  local failed=0
  
  check_service_status || failed=1
  check_database || failed=1
  check_redis || failed=1
  check_permission_api || failed=1
  check_cache_performance || failed=1
  
  echo "=================================="
  if [ $failed -eq 0 ]; then
    echo "🎉 权限系统健康检查通过"
    exit 0
  else
    echo "⚠️  权限系统存在问题，请检查上述失败项"
    exit 1
  fi
}

main
```

### 权限诊断工具

```javascript
// scripts/permission-diagnostic.js
const PermissionService = require('../services/PermissionService');
const PermissionCacheService = require('../services/PermissionCacheService');
const AuditService = require('../services/AuditService');
const db = require('../utils/db');

class PermissionDiagnostic {
  constructor() {
    this.permissionService = new PermissionService();
    this.cacheService = new PermissionCacheService();
    this.auditService = new AuditService();
  }

  async runDiagnostic() {
    console.log('🔍 权限系统诊断开始...\n');
    
    const results = {
      database: await this.checkDatabase(),
      permissions: await this.checkPermissions(),
      cache: await this.checkCache(),
      audit: await this.checkAudit(),
      performance: await this.checkPerformance()
    };
    
    this.generateReport(results);
    return results;
  }

  async checkDatabase() {
    console.log('📊 检查数据库状态...');
    const result = { status: 'ok', issues: [] };
    
    try {
      // 检查权限相关表
      const tables = ['users', 'roles', 'permissions', 'role_permissions', 
                     'user_custom_permissions', 'audit_logs'];
      
      for (const table of tables) {
        const [rows] = await db.execute(`SELECT COUNT(*) as count FROM ${table}`);
        console.log(`  ✅ 表 ${table}: ${rows[0].count} 条记录`);
      }
      
      // 检查索引
      const [indexes] = await db.execute(`
        SELECT TABLE_NAME, INDEX_NAME, COLUMN_NAME 
        FROM INFORMATION_SCHEMA.STATISTICS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME IN ('users', 'role_permissions', 'user_custom_permissions', 'audit_logs')
        ORDER BY TABLE_NAME, INDEX_NAME
      `);
      
      console.log(`  ✅ 找到 ${indexes.length} 个索引`);
      
    } catch (error) {
      result.status = 'error';
      result.issues.push(`数据库检查失败: ${error.message}`);
      console.log(`  ❌ 数据库检查失败: ${error.message}`);
    }
    
    return result;
  }

  async checkPermissions() {
    console.log('🔐 检查权限配置...');
    const result = { status: 'ok', issues: [] };
    
    try {
      // 检查默认角色
      const roles = ['super_admin', 'admin', 'user'];
      for (const role of roles) {
        const [users] = await db.execute('SELECT COUNT(*) as count FROM users WHERE role = ?', [role]);
        console.log(`  ✅ 角色 ${role}: ${users[0].count} 个用户`);
      }
      
      // 检查权限完整性
      const [permissions] = await db.execute('SELECT COUNT(*) as count FROM permissions');
      const [rolePermissions] = await db.execute('SELECT COUNT(*) as count FROM role_permissions');
      
      console.log(`  ✅ 系统权限: ${permissions[0].count} 个`);
      console.log(`  ✅ 角色权限映射: ${rolePermissions[0].count} 个`);
      
      // 检查孤立权限
      const [orphanPermissions] = await db.execute(`
        SELECT p.code FROM permissions p
        LEFT JOIN role_permissions rp ON p.code = rp.permission_code
        WHERE rp.permission_code IS NULL
      `);
      
      if (orphanPermissions.length > 0) {
        result.issues.push(`发现 ${orphanPermissions.length} 个未分配的权限`);
        console.log(`  ⚠️  发现 ${orphanPermissions.length} 个未分配的权限`);
      }
      
    } catch (error) {
      result.status = 'error';
      result.issues.push(`权限检查失败: ${error.message}`);
      console.log(`  ❌ 权限检查失败: ${error.message}`);
    }
    
    return result;
  }

  async checkCache() {
    console.log('💾 检查缓存状态...');
    const result = { status: 'ok', issues: [], metrics: {} };
    
    try {
      // 测试缓存读写
      const testKey = 'diagnostic_test_' + Date.now();
      const testData = { test: true, timestamp: Date.now() };
      
      const writeStart = Date.now();
      await this.cacheService.set(testKey, testData);
      const writeTime = Date.now() - writeStart;
      
      const readStart = Date.now();
      const cachedData = await this.cacheService.get(testKey);
      const readTime = Date.now() - readStart;
      
      await this.cacheService.del(testKey);
      
      result.metrics = {
        writeTime,
        readTime,
        dataIntegrity: JSON.stringify(cachedData) === JSON.stringify(testData)
      };
      
      console.log(`  ✅ 缓存写入时间: ${writeTime}ms`);
      console.log(`  ✅ 缓存读取时间: ${readTime}ms`);
      console.log(`  ✅ 数据完整性: ${result.metrics.dataIntegrity ? '正常' : '异常'}`);
      
      if (writeTime > 100 || readTime > 50) {
        result.issues.push('缓存响应时间较慢');
        console.log('  ⚠️  缓存响应时间较慢');
      }
      
    } catch (error) {
      result.status = 'error';
      result.issues.push(`缓存检查失败: ${error.message}`);
      console.log(`  ❌ 缓存检查失败: ${error.message}`);
    }
    
    return result;
  }

  async checkAudit() {
    console.log('📝 检查审计日志...');
    const result = { status: 'ok', issues: [], metrics: {} };
    
    try {
      // 检查最近的审计日志
      const [recentLogs] = await db.execute(`
        SELECT COUNT(*) as count 
        FROM audit_logs 
        WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
      `);
      
      const [totalLogs] = await db.execute('SELECT COUNT(*) as count FROM audit_logs');
      
      result.metrics = {
        recentLogs: recentLogs[0].count,
        totalLogs: totalLogs[0].count
      };
      
      console.log(`  ✅ 最近1小时日志: ${recentLogs[0].count} 条`);
      console.log(`  ✅ 总日志数: ${totalLogs[0].count} 条`);
      
      // 检查日志写入性能
      const writeStart = Date.now();
      await this.auditService.logPermissionAccess(
        999999, 'diagnostic.test', 'granted', 
        { ip: '127.0.0.1', userAgent: 'diagnostic' }
      );
      const writeTime = Date.now() - writeStart;
      
      result.metrics.writeTime = writeTime;
      console.log(`  ✅ 日志写入时间: ${writeTime}ms`);
      
      if (writeTime > 100) {
        result.issues.push('审计日志写入较慢');
        console.log('  ⚠️  审计日志写入较慢');
      }
      
    } catch (error) {
      result.status = 'error';
      result.issues.push(`审计检查失败: ${error.message}`);
      console.log(`  ❌ 审计检查失败: ${error.message}`);
    }
    
    return result;
  }

  async checkPerformance() {
    console.log('⚡ 检查性能指标...');
    const result = { status: 'ok', issues: [], metrics: {} };
    
    try {
      // 测试权限验证性能
      const testUserId = 1;
      const testPermission = 'user.read.own';
      
      const iterations = 100;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        const start = Date.now();
        await this.permissionService.hasPermission(testUserId, testPermission);
        times.push(Date.now() - start);
      }
      
      const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length;
      const maxTime = Math.max(...times);
      const minTime = Math.min(...times);
      
      result.metrics = {
        avgPermissionCheckTime: avgTime,
        maxPermissionCheckTime: maxTime,
        minPermissionCheckTime: minTime,
        iterations
      };
      
      console.log(`  ✅ 平均权限检查时间: ${avgTime.toFixed(2)}ms`);
      console.log(`  ✅ 最大权限检查时间: ${maxTime}ms`);
      console.log(`  ✅ 最小权限检查时间: ${minTime}ms`);
      
      if (avgTime > 50) {
        result.issues.push('权限验证性能不达标');
        console.log('  ⚠️  权限验证性能不达标');
      }
      
    } catch (error) {
      result.status = 'error';
      result.issues.push(`性能检查失败: ${error.message}`);
      console.log(`  ❌ 性能检查失败: ${error.message}`);
    }
    
    return result;
  }

  generateReport(results) {
    console.log('\n📋 诊断报告');
    console.log('='.repeat(50));
    
    let totalIssues = 0;
    let criticalIssues = 0;
    
    for (const [module, result] of Object.entries(results)) {
      console.log(`\n${module.toUpperCase()}:`);
      console.log(`  状态: ${result.status === 'ok' ? '✅ 正常' : '❌ 异常'}`);
      
      if (result.issues && result.issues.length > 0) {
        console.log(`  问题 (${result.issues.length}):`);
        result.issues.forEach(issue => {
          console.log(`    - ${issue}`);
          totalIssues++;
          if (result.status === 'error') criticalIssues++;
        });
      }
      
      if (result.metrics) {
        console.log('  指标:');
        for (const [key, value] of Object.entries(result.metrics)) {
          console.log(`    ${key}: ${value}`);
        }
      }
    }
    
    console.log('\n' + '='.repeat(50));
    console.log(`总问题数: ${totalIssues}`);
    console.log(`严重问题: ${criticalIssues}`);
    
    if (totalIssues === 0) {
      console.log('🎉 权限系统运行正常');
    } else if (criticalIssues === 0) {
      console.log('⚠️  发现一些需要关注的问题');
    } else {
      console.log('🚨 发现严重问题，需要立即处理');
    }
  }
}

// 运行诊断
if (require.main === module) {
  const diagnostic = new PermissionDiagnostic();
  diagnostic.runDiagnostic()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('诊断过程出错:', error);
      process.exit(1);
    });
}

module.exports = PermissionDiagnostic;
```

## 权限验证问题

### 问题1: 用户无法访问资源

**症状**: 用户提示权限不足，无法访问某些功能

**诊断步骤**:

1. **检查用户角色和权限**
```bash
# 查询用户信息
mysql -e "SELECT id, username, role FROM users WHERE username = 'problem_user'"

# 查询用户有效权限
node -e "
const PermissionService = require('./services/PermissionService');
const service = new PermissionService();
service.getUserPermissions(USER_ID).then(console.log);
"
```

2. **检查权限配置**
```sql
-- 查看角色权限配置
SELECT r.role, p.code, p.name 
FROM role_permissions r 
JOIN permissions p ON r.permission_code = p.code 
WHERE r.role = 'user_role';

-- 查看用户自定义权限
SELECT ucp.permission_code, ucp.granted 
FROM user_custom_permissions ucp 
WHERE ucp.user_id = USER_ID;
```

3. **检查审计日志**
```sql
-- 查看权限拒绝日志
SELECT * FROM audit_logs 
WHERE user_id = USER_ID 
  AND action = 'permission_access' 
  AND result = 'denied' 
ORDER BY created_at DESC 
LIMIT 10;
```

**解决方案**:
- 为用户分配正确的角色
- 添加必要的自定义权限
- 检查权限代码是否正确
- 清除权限缓存

### 问题2: 权限检查异常缓慢

**症状**: 权限验证响应时间超过预期

**诊断步骤**:

1. **性能分析**
```javascript
// 测试权限检查性能
const { performance } = require('perf_hooks');
const PermissionService = require('./services/PermissionService');

async function testPermissionPerformance() {
  const service = new PermissionService();
  const iterations = 100;
  const times = [];
  
  for (let i = 0; i < iterations; i++) {
    const start = performance.now();
    await service.hasPermission(1, 'user.create');
    times.push(performance.now() - start);
  }
  
  console.log('平均时间:', times.reduce((a, b) => a + b) / times.length);
  console.log('最大时间:', Math.max(...times));
}
```

2. **数据库查询分析**
```sql
-- 启用慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 0.1;

-- 分析权限查询
EXPLAIN SELECT * FROM role_permissions WHERE role_id = 'admin';
EXPLAIN SELECT * FROM user_custom_permissions WHERE user_id = 1;
```

**解决方案**:
- 添加数据库索引
- 优化权限查询逻辑
- 启用权限缓存
- 使用连接池

### 问题3: 权限缓存不一致

**症状**: 权限更改后不立即生效

**诊断步骤**:

1. **检查缓存状态**
```javascript
const PermissionCacheService = require('./services/PermissionCacheService');
const cache = new PermissionCacheService();

// 检查用户权限缓存
cache.getUserPermissions(userId).then(cached => {
  console.log('缓存的权限:', cached);
});

// 检查缓存统计
cache.getCacheStats().then(stats => {
  console.log('缓存统计:', stats);
});
```

2. **验证数据一致性**
```javascript
// 比较缓存和数据库数据
const cachedPermissions = await cache.getUserPermissions(userId);
const dbPermissions = await permissionService.getUserPermissions(userId);

console.log('数据一致性:', 
  JSON.stringify(cachedPermissions) === JSON.stringify(dbPermissions)
);
```

**解决方案**:
- 清除相关用户的权限缓存
- 重启缓存服务
- 检查缓存更新逻辑
- 验证缓存过期时间设置

## 缓存相关问题

### 问题1: Redis连接失败

**症状**: 权限系统无法连接到Redis

**诊断步骤**:

1. **检查Redis服务状态**
```bash
# 检查Redis进程
ps aux | grep redis

# 检查Redis端口
netstat -tlnp | grep 6379

# 测试连接
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping
```

2. **检查网络连接**
```bash
# 测试网络连通性
telnet $REDIS_HOST $REDIS_PORT

# 检查防火墙规则
iptables -L | grep 6379
```

**解决方案**:
- 启动Redis服务
- 检查网络配置
- 更新防火墙规则
- 验证Redis配置

### 问题2: 缓存命中率低

**症状**: 缓存命中率低于预期，影响性能

**诊断步骤**:

1. **分析缓存统计**
```bash
# Redis统计信息
redis-cli info stats

# 应用缓存统计
node -e "
const cache = require('./services/PermissionCacheService');
cache.getCacheStats().then(console.log);
"
```

2. **分析缓存模式**
```javascript
// 监控缓存访问模式
const cache = new PermissionCacheService();
cache.on('hit', (key) => console.log('Cache hit:', key));
cache.on('miss', (key) => console.log('Cache miss:', key));
```

**解决方案**:
- 调整缓存TTL设置
- 增加缓存容量
- 优化缓存键设计
- 实现缓存预热

## 性能问题

### 问题1: 响应时间过长

**症状**: API响应时间超过预期阈值

**诊断步骤**:

1. **性能分析**
```javascript
// API响应时间监控
const express = require('express');
const app = express();

app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    if (duration > 100) {
      console.log(`Slow request: ${req.method} ${req.path} - ${duration}ms`);
    }
  });
  next();
});
```

2. **数据库性能分析**
```sql
-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看进程列表
SHOW PROCESSLIST;

-- 查看表状态
SHOW TABLE STATUS LIKE 'audit_logs';
```

**解决方案**:
- 优化数据库查询
- 添加适当索引
- 使用缓存减少数据库访问
- 实现异步处理

### 问题2: 内存使用过高

**症状**: 应用内存使用持续增长

**诊断步骤**:

1. **内存分析**
```javascript
// 内存使用监控
setInterval(() => {
  const usage = process.memoryUsage();
  console.log('Memory usage:', {
    rss: Math.round(usage.rss / 1024 / 1024) + 'MB',
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + 'MB',
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024) + 'MB'
  });
}, 30000);
```

2. **内存泄漏检测**
```bash
# 使用Node.js内存分析工具
node --inspect simple-server.js

# 生成堆快照
kill -USR2 <node_process_id>
```

**解决方案**:
- 清理未使用的对象引用
- 优化缓存大小限制
- 实现内存监控告警
- 定期重启应用

## 数据库问题

### 问题1: 连接池耗尽

**症状**: 数据库连接错误，连接池满

**诊断步骤**:

1. **检查连接池状态**
```javascript
// 监控连接池
const db = require('./utils/db');
setInterval(() => {
  console.log('Connection pool status:', {
    total: db.pool.totalCount,
    used: db.pool.usedCount,
    waiting: db.pool.waitingCount
  });
}, 10000);
```

2. **分析长时间运行的查询**
```sql
-- 查看当前连接
SELECT * FROM INFORMATION_SCHEMA.PROCESSLIST 
WHERE COMMAND != 'Sleep' 
ORDER BY TIME DESC;

-- 查看锁等待
SELECT * FROM INFORMATION_SCHEMA.INNODB_LOCKS;
```

**解决方案**:
- 增加连接池大小
- 优化长时间运行的查询
- 实现连接超时机制
- 检查连接泄漏

### 问题2: 死锁问题

**症状**: 数据库操作偶尔失败，提示死锁

**诊断步骤**:

1. **死锁检测**
```sql
-- 查看死锁信息
SHOW ENGINE INNODB STATUS;

-- 查看事务状态
SELECT * FROM INFORMATION_SCHEMA.INNODB_TRX;
```

2. **分析事务模式**
```javascript
// 事务监控
const originalExecute = db.execute;
db.execute = function(sql, params) {
  console.log('Executing:', sql.substring(0, 100));
  return originalExecute.call(this, sql, params);
};
```

**解决方案**:
- 优化事务顺序
- 减少事务持有时间
- 使用适当的隔离级别
- 实现死锁重试机制

## 配置问题

### 问题1: 配置文件错误

**症状**: 应用启动失败或功能异常

**诊断步骤**:

1. **配置验证**
```bash
# 验证JSON语法
node -e "console.log(JSON.parse(require('fs').readFileSync('./config/permissions.json')))"

# 配置完整性检查
node scripts/validate-config.js
```

2. **环境变量检查**
```bash
# 检查关键环境变量
env | grep -E "(DB_|REDIS_|JWT_|PERMISSION_)"

# 验证环境变量值
echo "DB_HOST: $DB_HOST"
echo "REDIS_HOST: $REDIS_HOST"
```

**解决方案**:
- 修复配置文件语法错误
- 补充缺失的配置项
- 验证环境变量设置
- 使用配置模板

### 问题2: 配置热重载失败

**症状**: 配置更改后不生效

**诊断步骤**:

1. **检查文件监听**
```javascript
// 测试文件监听
const fs = require('fs');
const configPath = './config/permissions.json';

fs.watch(configPath, (eventType, filename) => {
  console.log('Config file changed:', eventType, filename);
});
```

2. **验证配置加载**
```javascript
// 检查配置加载逻辑
const ConfigService = require('./services/ConfigService');
const config = new ConfigService();
config.on('configChanged', (newConfig) => {
  console.log('Config updated:', Object.keys(newConfig));
});
```

**解决方案**:
- 检查文件权限
- 验证监听逻辑
- 重启配置服务
- 手动重新加载配置

## 安全问题

### 问题1: 权限绕过攻击

**症状**: 发现未授权访问或权限提升

**诊断步骤**:

1. **安全审计**
```sql
-- 查看异常权限访问
SELECT * FROM audit_logs 
WHERE result = 'denied' 
  AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY created_at DESC;

-- 查看权限变更记录
SELECT * FROM audit_logs 
WHERE action = 'permission_change' 
ORDER BY created_at DESC 
LIMIT 20;
```

2. **访问模式分析**
```javascript
// 分析异常访问模式
const AuditService = require('./services/AuditService');
const audit = new AuditService();

audit.detectAnomalies({
  timeWindow: '1h',
  deniedThreshold: 10
}).then(anomalies => {
  console.log('检测到的异常:', anomalies);
});
```

**解决方案**:
- 立即撤销可疑用户权限
- 强制用户重新登录
- 加强权限验证逻辑
- 实施IP白名单

### 问题2: 暴力破解攻击

**症状**: 大量登录失败尝试

**诊断步骤**:

1. **攻击检测**
```sql
-- 查看登录失败记录
SELECT ip_address, COUNT(*) as attempts 
FROM audit_logs 
WHERE action = 'login_attempt' 
  AND result = 'denied' 
  AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY ip_address 
HAVING attempts > 10
ORDER BY attempts DESC;
```

2. **IP分析**
```bash
# 分析访问日志
tail -f /var/log/nginx/access.log | grep -E "(401|403)"

# 检查防火墙规则
iptables -L INPUT -n --line-numbers
```

**解决方案**:
- 实施IP封禁
- 启用账户锁定机制
- 增加验证码验证
- 配置WAF规则

## 监控和告警

### 监控指标

```javascript
// 权限系统监控指标
const metrics = {
  // 性能指标
  permissionCheckTime: 'histogram',
  cacheHitRate: 'gauge',
  apiResponseTime: 'histogram',
  
  // 业务指标
  activeUsers: 'gauge',
  permissionDenials: 'counter',
  loginAttempts: 'counter',
  
  // 系统指标
  databaseConnections: 'gauge',
  redisConnections: 'gauge',
  memoryUsage: 'gauge'
};
```

### 告警规则

```yaml
# 告警配置示例
alerts:
  - name: PermissionCheckSlow
    condition: permission_check_time_p95 > 100ms
    severity: warning
    
  - name: CacheHitRateLow
    condition: cache_hit_rate < 0.9
    severity: warning
    
  - name: PermissionDenialsHigh
    condition: permission_denials_rate > 10/min
    severity: critical
    
  - name: DatabaseConnectionsHigh
    condition: database_connections > 80% of max
    severity: warning
```

## 应急处理

### 紧急情况处理流程

1. **权限系统完全不可用**
```bash
# 应急处理脚本
#!/bin/bash
echo "权限系统应急处理..."

# 1. 检查服务状态
systemctl status permission-service

# 2. 重启服务
systemctl restart permission-service

# 3. 检查日志
tail -f /var/log/permission/error.log

# 4. 验证恢复
curl -f http://localhost:3000/health || exit 1

echo "权限系统已恢复"
```

2. **数据库连接失败**
```bash
# 数据库应急处理
# 1. 检查数据库状态
systemctl status mysql

# 2. 重启数据库
systemctl restart mysql

# 3. 验证连接
mysql -u root -p -e "SELECT 1"

# 4. 重启应用
systemctl restart permission-service
```

3. **缓存服务不可用**
```bash
# 缓存应急处理
# 1. 重启Redis
systemctl restart redis

# 2. 清除应用缓存
redis-cli FLUSHDB

# 3. 重启应用以重建缓存
systemctl restart permission-service
```

### 回滚计划

```bash
#!/bin/bash
# 权限系统回滚脚本

BACKUP_DIR="/backup/permission-system"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

echo "开始权限系统回滚..."

# 1. 备份当前状态
mysqldump sitemanager > $BACKUP_DIR/current_$TIMESTAMP.sql

# 2. 恢复数据库
mysql sitemanager < $BACKUP_DIR/last_known_good.sql

# 3. 恢复配置文件
cp $BACKUP_DIR/permissions.json.backup ./config/permissions.json

# 4. 重启服务
systemctl restart permission-service

# 5. 验证回滚
./scripts/permission-health-check.sh

echo "权限系统回滚完成"
```

## 总结

权限系统故障排查需要系统性的方法和完善的工具支持。通过本手册提供的诊断步骤和解决方案，可以快速定位和解决大部分权限相关问题。

关键要点：
1. **预防为主**: 完善的监控和告警机制
2. **快速响应**: 标准化的故障处理流程
3. **根因分析**: 深入分析问题根本原因
4. **持续改进**: 根据故障经验优化系统

如遇到本手册未涵盖的问题，请及时联系系统管理员或技术支持团队。