# 权限系统 API 文档

## 概述

本文档详细介绍权限系统的所有API接口，包括权限验证、用户管理、角色管理等功能。

## 基础信息

- **Base URL**: `/api/v1`
- **认证方式**: Bearer <PERSON>ken
- **响应格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

### 成功响应

```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功",
  "timestamp": "2024-01-16T10:00:00Z"
}
```

### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {
      // 错误详情
    }
  },
  "timestamp": "2024-01-16T10:00:00Z"
}
```

## 权限验证 API

### 检查用户权限

检查当前用户是否拥有指定权限。

**请求**

```http
GET /api/v1/permissions/check?permission=site.website.view
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| permission | string | 是 | 权限代码 |

**响应**

```json
{
  "success": true,
  "data": {
    "hasPermission": true,
    "permission": "site.website.view",
    "userId": 1
  }
}
```

### 批量检查权限

批量检查用户是否拥有多个权限。

**请求**

```http
POST /api/v1/permissions/check-multiple
```

**请求体**

```json
{
  "permissions": [
    "site.website.view",
    "site.website.edit",
    "site.website.delete"
  ],
  "requireAll": false
}
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| permissions | array | 是 | 权限代码数组 |
| requireAll | boolean | 否 | 是否需要拥有所有权限，默认false |

**响应**

```json
{
  "success": true,
  "data": {
    "userId": 1,
    "hasAllPermissions": false,
    "permissionResults": {
      "site.website.view": true,
      "site.website.edit": true,
      "site.website.delete": false
    }
  }
}
```

### 获取用户权限

获取用户的完整权限信息。

**请求**

```http
GET /api/v1/permissions/user/{userId}
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| userId | integer | 是 | 用户ID |

**响应**

```json
{
  "success": true,
  "data": {
    "userId": 1,
    "username": "testuser",
    "role": "user",
    "status": "active",
    "rolePermissions": [
      "site.website.view",
      "server.server.view"
    ],
    "customPermissions": [
      {
        "permission_code": "site.website.edit",
        "granted": true
      }
    ],
    "effectivePermissions": [
      "site.website.view",
      "site.website.edit",
      "server.server.view"
    ],
    "deniedPermissions": [],
    "lastUpdated": "2024-01-16T10:00:00Z"
  }
}
```

## 用户管理 API

### 获取用户列表

获取系统用户列表。

**请求**

```http
GET /api/v1/users?page=1&limit=20&search=test
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页数量，默认20 |
| search | string | 否 | 搜索关键词 |

**响应**

```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "role": "user",
        "status": "active",
        "created_at": "2024-01-01T00:00:00Z",
        "last_login": "2024-01-16T09:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

### 创建用户

创建新用户。

**请求**

```http
POST /api/v1/users
```

**请求体**

```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user"
}
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| username | string | 是 | 用户名 |
| email | string | 是 | 邮箱地址 |
| password | string | 是 | 密码 |
| role | string | 否 | 角色，默认user |

**响应**

```json
{
  "success": true,
  "data": {
    "id": 2,
    "username": "newuser",
    "email": "<EMAIL>",
    "role": "user",
    "status": "active",
    "created_at": "2024-01-16T10:00:00Z"
  },
  "message": "用户创建成功"
}
```

### 更新用户权限

更新用户的权限配置。

**请求**

```http
PUT /api/v1/users/{userId}/permissions
```

**请求体**

```json
{
  "role": "admin",
  "customPermissions": [
    {
      "code": "site.website.delete",
      "granted": true
    },
    {
      "code": "site.website.view",
      "granted": false
    }
  ]
}
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| role | string | 否 | 新角色 |
| customPermissions | array | 否 | 自定义权限配置 |

**响应**

```json
{
  "success": true,
  "data": {
    "userId": 1,
    "updatedRole": "admin",
    "addedPermissions": ["site.website.delete"],
    "revokedPermissions": ["site.website.view"],
    "errors": []
  },
  "message": "用户权限更新成功"
}
```

## 角色管理 API

### 获取角色列表

获取系统角色列表。

**请求**

```http
GET /api/v1/roles
```

**响应**

```json
{
  "success": true,
  "data": {
    "roles": [
      {
        "id": "super_admin",
        "name": "超级管理员",
        "description": "拥有系统所有权限",
        "level": 100,
        "isSystem": true
      },
      {
        "id": "admin",
        "name": "管理员",
        "description": "拥有大部分管理权限",
        "level": 80,
        "isSystem": true
      },
      {
        "id": "user",
        "name": "普通用户",
        "description": "基本操作权限",
        "level": 20,
        "isSystem": true
      }
    ]
  }
}
```

### 获取角色权限

获取指定角色的权限配置。

**请求**

```http
GET /api/v1/roles/{roleId}/permissions
```

**响应**

```json
{
  "success": true,
  "data": {
    "role": "admin",
    "roleInfo": {
      "name": "管理员",
      "description": "拥有大部分管理权限",
      "level": 80
    },
    "permissions": [
      {
        "id": 1,
        "code": "site.website.view",
        "name": "查看网站",
        "description": "允许查看网站信息",
        "module": "site",
        "resource": "website",
        "action": "view"
      }
    ],
    "permissionsByModule": {
      "site": [
        {
          "code": "site.website.view",
          "name": "查看网站"
        }
      ]
    },
    "totalPermissions": 15
  }
}
```

### 更新角色权限

更新角色的权限配置。

**请求**

```http
PUT /api/v1/roles/{roleId}/permissions
```

**请求体**

```json
{
  "permissions": [
    "site.website.view",
    "site.website.edit",
    "server.server.view"
  ]
}
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| permissions | array | 是 | 权限代码数组 |

**响应**

```json
{
  "success": true,
  "data": {
    "role": "admin",
    "addedPermissions": ["server.server.view"],
    "removedPermissions": ["site.website.delete"],
    "totalPermissions": 15,
    "errors": []
  },
  "message": "角色权限更新成功"
}
```

## 权限模板 API

### 获取权限模板列表

获取权限模板列表。

**请求**

```http
GET /api/v1/permission-templates?includeSystem=true&limit=50
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| includeSystem | boolean | 否 | 是否包含系统模板，默认true |
| createdBy | integer | 否 | 创建者ID |
| limit | integer | 否 | 限制数量，默认50 |

**响应**

```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": 1,
        "name": "网站管理员模板",
        "description": "网站管理相关权限",
        "permissions": [
          "site.website.view",
          "site.website.edit",
          "site.website.create"
        ],
        "isSystem": true,
        "createdBy": null,
        "createdAt": "2024-01-01T00:00:00Z",
        "creatorUsername": null
      }
    ]
  }
}
```

### 创建权限模板

创建新的权限模板。

**请求**

```http
POST /api/v1/permission-templates
```

**请求体**

```json
{
  "name": "内容管理员模板",
  "description": "内容管理相关权限",
  "permissions": [
    "site.website.view",
    "site.website.edit",
    "site.content.create"
  ]
}
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 是 | 模板名称 |
| description | string | 否 | 模板描述 |
| permissions | array | 是 | 权限代码数组 |

**响应**

```json
{
  "success": true,
  "data": {
    "id": 2,
    "name": "内容管理员模板",
    "description": "内容管理相关权限",
    "permissions": [
      "site.website.view",
      "site.website.edit",
      "site.content.create"
    ],
    "isSystem": false,
    "createdBy": 1
  },
  "message": "权限模板创建成功"
}
```

### 应用权限模板

将权限模板应用到角色。

**请求**

```http
POST /api/v1/roles/{roleId}/apply-template
```

**请求体**

```json
{
  "templateId": 1
}
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| templateId | integer | 是 | 模板ID |

**响应**

```json
{
  "success": true,
  "data": {
    "role": "user",
    "templateName": "网站管理员模板",
    "templateId": 1,
    "addedPermissions": [
      "site.website.create",
      "site.website.edit"
    ],
    "removedPermissions": [],
    "totalPermissions": 5
  },
  "message": "权限模板应用成功"
}
```

## 审计日志 API

### 获取审计日志

获取权限相关的审计日志。

**请求**

```http
GET /api/v1/audit/logs?action=permission_check&result=denied&page=1&limit=50
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| userId | integer | 否 | 用户ID |
| action | string | 否 | 操作类型 |
| result | string | 否 | 操作结果 |
| startDate | string | 否 | 开始日期 |
| endDate | string | 否 | 结束日期 |
| page | integer | 否 | 页码，默认1 |
| limit | integer | 否 | 每页数量，默认50 |

**响应**

```json
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": 1,
        "userId": 1,
        "username": "testuser",
        "action": "permission_check",
        "resource": "site.website.edit",
        "result": "denied",
        "details": {
          "requiredPermissions": ["site.website.edit"],
          "userRole": "user",
          "ip": "*************",
          "userAgent": "Mozilla/5.0...",
          "endpoint": "PUT /api/websites/1"
        },
        "createdAt": "2024-01-16T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 200,
      "totalPages": 4
    }
  }
}
```

### 获取权限统计

获取权限使用统计信息。

**请求**

```http
GET /api/v1/audit/stats?timeRange=7d&groupBy=permission
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| timeRange | string | 否 | 时间范围，如7d、30d |
| groupBy | string | 否 | 分组方式：permission、user、action |

**响应**

```json
{
  "success": true,
  "data": {
    "timeRange": "7d",
    "groupBy": "permission",
    "stats": [
      {
        "permission": "site.website.view",
        "totalChecks": 1500,
        "successfulChecks": 1450,
        "failedChecks": 50,
        "successRate": 96.67,
        "uniqueUsers": 25
      },
      {
        "permission": "site.website.edit",
        "totalChecks": 800,
        "successfulChecks": 720,
        "failedChecks": 80,
        "successRate": 90.0,
        "uniqueUsers": 15
      }
    ]
  }
}
```

## 系统管理 API

### 获取权限系统状态

获取权限系统的运行状态。

**请求**

```http
GET /api/v1/system/permission-status
```

**响应**

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": 86400,
    "cache": {
      "status": "ok",
      "hitRate": "95.5%",
      "size": 850,
      "maxSize": 1000
    },
    "database": {
      "status": "ok",
      "connectionPool": {
        "active": 5,
        "idle": 15,
        "total": 20
      }
    },
    "performance": {
      "avgPermissionCheckTime": 25,
      "p95PermissionCheckTime": 45,
      "requestsPerSecond": 150
    }
  }
}
```

### 清除权限缓存

清除指定用户或所有用户的权限缓存。

**请求**

```http
DELETE /api/v1/system/cache/permissions?userId=1
```

**参数**

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| userId | integer | 否 | 用户ID，不提供则清除所有缓存 |
| role | string | 否 | 角色名称，清除该角色所有用户的缓存 |

**响应**

```json
{
  "success": true,
  "data": {
    "clearedUsers": [1],
    "clearedCount": 1
  },
  "message": "权限缓存清除成功"
}
```

### 重新加载权限配置

重新加载权限系统配置。

**请求**

```http
POST /api/v1/system/reload-config
```

**响应**

```json
{
  "success": true,
  "data": {
    "configVersion": "1.2.0",
    "reloadedAt": "2024-01-16T10:00:00Z",
    "changes": [
      "添加新权限: site.analytics.view",
      "更新角色权限: admin"
    ]
  },
  "message": "权限配置重新加载成功"
}
```

## 错误代码

### 认证错误

| 错误代码 | HTTP状态码 | 描述 |
|----------|------------|------|
| UNAUTHORIZED | 401 | 用户未认证 |
| TOKEN_EXPIRED | 401 | 认证令牌已过期 |
| TOKEN_INVALID | 401 | 认证令牌无效 |

### 权限错误

| 错误代码 | HTTP状态码 | 描述 |
|----------|------------|------|
| INSUFFICIENT_PERMISSIONS | 403 | 权限不足 |
| PERMISSION_NOT_FOUND | 404 | 权限不存在 |
| ROLE_NOT_FOUND | 404 | 角色不存在 |
| PERMISSION_DENIED | 403 | 权限被拒绝 |

### 业务错误

| 错误代码 | HTTP状态码 | 描述 |
|----------|------------|------|
| USER_NOT_FOUND | 404 | 用户不存在 |
| ROLE_ASSIGNMENT_FAILED | 400 | 角色分配失败 |
| TEMPLATE_NOT_FOUND | 404 | 权限模板不存在 |
| INVALID_PERMISSION_CODE | 400 | 无效的权限代码 |

### 系统错误

| 错误代码 | HTTP状态码 | 描述 |
|----------|------------|------|
| INTERNAL_ERROR | 500 | 服务器内部错误 |
| DATABASE_ERROR | 500 | 数据库错误 |
| CACHE_ERROR | 500 | 缓存服务错误 |
| CONFIG_ERROR | 500 | 配置错误 |

## 使用示例

### JavaScript/Node.js

```javascript
// 权限检查
async function checkPermission(token, permission) {
  const response = await fetch('/api/v1/permissions/check?permission=' + permission, {
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    }
  });
  
  const result = await response.json();
  return result.data.hasPermission;
}

// 更新用户权限
async function updateUserPermissions(token, userId, permissions) {
  const response = await fetch(`/api/v1/users/${userId}/permissions`, {
    method: 'PUT',
    headers: {
      'Authorization': 'Bearer ' + token,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(permissions)
  });
  
  return await response.json();
}
```

### Python

```python
import requests

class PermissionAPI:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
    
    def check_permission(self, permission):
        url = f'{self.base_url}/api/v1/permissions/check'
        params = {'permission': permission}
        
        response = requests.get(url, headers=self.headers, params=params)
        result = response.json()
        
        return result['data']['hasPermission']
    
    def update_user_permissions(self, user_id, permissions):
        url = f'{self.base_url}/api/v1/users/{user_id}/permissions'
        
        response = requests.put(url, headers=self.headers, json=permissions)
        return response.json()
```

### cURL

```bash
# 检查权限
curl -X GET "http://localhost:3000/api/v1/permissions/check?permission=site.website.view" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# 更新用户权限
curl -X PUT "http://localhost:3000/api/v1/users/1/permissions" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "admin",
    "customPermissions": [
      {"code": "site.website.delete", "granted": true}
    ]
  }'
```

## 版本信息

- **当前版本**: v1.0.0
- **最后更新**: 2024-01-16
- **兼容性**: 向后兼容

## 联系支持

如有API使用问题，请参考：
- [权限系统使用指南](./permission-system-guide.md)
- [故障排查手册](./permission-troubleshooting.md)
- [配置管理指南](./permission-config-guide.md)