# 权限系统使用指南

## 概述

本文档详细介绍了网站管理系统的权限管理功能，包括权限模型、使用方法、配置指南和最佳实践。

## 目录

1. [权限模型](#权限模型)
2. [角色和权限](#角色和权限)
3. [权限验证](#权限验证)
4. [权限管理界面](#权限管理界面)
5. [API使用指南](#api使用指南)
6. [配置管理](#配置管理)
7. [故障排查](#故障排查)
8. [最佳实践](#最佳实践)

## 权限模型

### 基本概念

权限系统采用基于角色的访问控制（RBAC）模型，包含以下核心概念：

- **用户（User）**: 系统的使用者
- **角色（Role）**: 权限的集合，如管理员、普通用户等
- **权限（Permission）**: 具体的操作权限，如创建用户、删除服务器等
- **资源（Resource）**: 被保护的系统资源，如用户数据、服务器信息等

### 权限继承规则

```
用户最终权限 = 角色权限 + 自定义权限 - 撤销权限
```

1. **角色权限**: 用户通过角色获得的基础权限
2. **自定义权限**: 为用户单独分配的额外权限
3. **撤销权限**: 明确撤销的权限（优先级最高）

### 权限代码规范

权限代码采用三段式命名：`模块.资源.操作`

```
用户管理:
- user.create        // 创建用户
- user.read.any      // 查看任意用户
- user.read.own      // 查看自己
- user.update.any    // 更新任意用户
- user.update.own    // 更新自己
- user.delete.any    // 删除用户

服务器管理:
- server.create      // 创建服务器
- server.read.any    // 查看任意服务器
- server.read.own    // 查看自己的服务器
- server.update.any  // 更新任意服务器
- server.update.own  // 更新自己的服务器
- server.delete.any  // 删除任意服务器
- server.delete.own  // 删除自己的服务器

网站管理:
- website.create     // 创建网站
- website.read.any   // 查看任意网站
- website.read.own   // 查看自己的网站
- website.update.any // 更新任意网站
- website.update.own // 更新自己的网站
- website.delete.any // 删除任意网站
- website.delete.own // 删除自己的网站

系统管理:
- system.settings    // 系统设置
- system.backup      // 系统备份
- system.logs        // 系统日志
- role.assign        // 分配角色
- role.read          // 查看角色
```

## 角色和权限

### 预定义角色

#### 超级管理员 (super_admin)
- 拥有系统所有权限
- 可以管理其他管理员
- 可以修改系统核心配置

**权限列表:**
```
用户管理: user.create, user.read.any, user.update.any, user.delete.any
角色管理: role.assign, role.read
系统管理: system.settings, system.backup, system.logs
服务器管理: server.create, server.read.any, server.update.any, server.delete.any
网站管理: website.create, website.read.any, website.update.any, website.delete.any
```

#### 管理员 (admin)
- 拥有大部分管理权限
- 不能删除用户或分配角色
- 不能执行系统备份

**权限列表:**
```
用户管理: user.create, user.read.any, user.update.any
角色管理: role.read
系统管理: system.settings, system.logs
服务器管理: server.create, server.read.any, server.update.any, server.delete.any
网站管理: website.create, website.read.any, website.update.any, website.delete.any
```

#### 普通用户 (user)
- 只能管理自己的资源
- 可以创建服务器和网站
- 不能查看其他用户信息

**权限列表:**
```
用户管理: user.read.own, user.update.own
服务器管理: server.create, server.read.own, server.update.own, server.delete.own
网站管理: website.create, website.read.own, website.update.own, website.delete.own
```

### 自定义权限

管理员可以为用户分配额外的权限或撤销某些权限：

```javascript
// 为用户添加额外权限
{
  "customPermissions": [
    {"code": "system.backup", "granted": true},    // 授予系统备份权限
    {"code": "user.delete.any", "granted": false}  // 撤销用户删除权限
  ]
}
```

## 权限验证

### 后端权限验证

#### 使用权限中间件

```javascript
const { requirePermission, requireRole, requireOwnership } = require('./middleware/PermissionMiddleware');

// 需要特定权限
app.get('/api/users', requirePermission('user.read.any'), (req, res) => {
  // 只有拥有 user.read.any 权限的用户才能访问
});

// 需要特定角色
app.get('/api/admin/settings', requireRole('admin'), (req, res) => {
  // 只有管理员角色才能访问
});

// 需要资源所有权
app.get('/api/servers/:id', requireOwnership(getServerOwner, 'server.read'), (req, res) => {
  // 只有服务器所有者或拥有 server.read.any 权限的用户才能访问
});

// 多权限验证
app.post('/api/users', requirePermission(['user.create', 'role.read']), (req, res) => {
  // 需要同时拥有两个权限
});
```

#### 手动权限检查

```javascript
const PermissionService = require('./services/PermissionService');
const permissionService = new PermissionService();

async function checkUserPermission(userId, permission) {
  const hasPermission = await permissionService.hasPermission(userId, permission);
  if (!hasPermission) {
    throw new Error('权限不足');
  }
}

// 使用示例
app.post('/api/custom-action', async (req, res) => {
  try {
    await checkUserPermission(req.user.id, 'custom.action');
    // 执行操作
    res.json({ success: true });
  } catch (error) {
    res.status(403).json({ error: error.message });
  }
});
```

### 前端权限控制

#### 使用权限上下文

```jsx
import { usePermissions } from '../hooks/usePermissions';
import PermissionGuard from '../components/PermissionGuard';

function UserManagement() {
  const { hasPermission } = usePermissions();

  return (
    <div>
      <h1>用户管理</h1>
      
      {/* 条件渲染 */}
      {hasPermission('user.create') && (
        <button>创建用户</button>
      )}
      
      {/* 权限守卫组件 */}
      <PermissionGuard permissions="user.read.any">
        <UserList />
      </PermissionGuard>
      
      {/* 多权限守卫 */}
      <PermissionGuard 
        permissions={['user.update.any', 'role.assign']}
        fallback={<div>权限不足</div>}
      >
        <UserEditor />
      </PermissionGuard>
    </div>
  );
}
```

#### 路由权限保护

```jsx
import { ProtectedRoute } from '../components/ProtectedRoute';

function App() {
  return (
    <Router>
      <Routes>
        {/* 公开路由 */}
        <Route path="/login" element={<Login />} />
        
        {/* 需要登录的路由 */}
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <Dashboard />
          </ProtectedRoute>
        } />
        
        {/* 需要特定权限的路由 */}
        <Route path="/admin" element={
          <ProtectedRoute permissions="system.settings">
            <AdminPanel />
          </ProtectedRoute>
        } />
        
        {/* 需要特定角色的路由 */}
        <Route path="/super-admin" element={
          <ProtectedRoute roles="super_admin">
            <SuperAdminPanel />
          </ProtectedRoute>
        } />
      </Routes>
    </Router>
  );
}
```

## 权限管理界面

### 用户权限管理

1. **查看用户权限**
   - 进入用户管理页面
   - 点击用户行的"权限"按钮
   - 查看用户的角色权限和自定义权限

2. **编辑用户权限**
   - 在用户权限页面点击"编辑权限"
   - 选择要授予或撤销的权限
   - 点击"保存"确认更改

3. **批量权限操作**
   - 选择多个用户
   - 点击"批量操作" > "权限管理"
   - 选择要批量应用的权限更改

### 角色管理

1. **创建角色**
   - 进入角色管理页面
   - 点击"创建角色"
   - 输入角色名称和描述
   - 选择角色权限
   - 保存角色

2. **编辑角色**
   - 在角色列表中点击"编辑"
   - 修改角色信息和权限
   - 保存更改

3. **角色模板**
   - 使用预定义的角色模板
   - 基于现有角色创建新模板
   - 导入导出角色配置

## API使用指南

### 权限查询API

#### 获取用户权限
```http
GET /api/v1/auth/users/{userId}/permissions
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "userId": 123,
    "role": "admin",
    "rolePermissions": ["user.create", "user.read.any"],
    "customPermissions": [
      {"code": "system.backup", "granted": true}
    ],
    "effectivePermissions": ["user.create", "user.read.any", "system.backup"]
  }
}
```

#### 检查权限
```http
POST /api/v1/auth/permissions/check
Authorization: Bearer {token}
Content-Type: application/json

{
  "userId": 123,
  "permissions": ["user.create", "server.delete.any"]
}

Response:
{
  "success": true,
  "data": {
    "hasAllPermissions": false,
    "permissionResults": {
      "user.create": true,
      "server.delete.any": false
    }
  }
}
```

### 权限管理API

#### 更新用户权限
```http
PUT /api/v1/auth/users/{userId}/permissions
Authorization: Bearer {token}
Content-Type: application/json

{
  "role": "admin",
  "customPermissions": [
    {"code": "system.backup", "granted": true},
    {"code": "user.delete.any", "granted": false}
  ]
}

Response:
{
  "success": true,
  "message": "用户权限更新成功",
  "data": {
    "updatedPermissions": ["system.backup"],
    "revokedPermissions": ["user.delete.any"]
  }
}
```

#### 获取角色列表
```http
GET /api/v1/auth/roles
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "roles": [
      {
        "id": "admin",
        "name": "管理员",
        "description": "系统管理员角色",
        "permissions": ["user.create", "user.read.any"]
      }
    ]
  }
}
```

### 审计日志API

#### 获取审计日志
```http
GET /api/v1/audit/logs?page=1&limit=50&userId=123&action=permission_access
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": 1001,
        "userId": 123,
        "action": "permission_access",
        "resource": "user.create",
        "result": "granted",
        "ipAddress": "*************",
        "createdAt": "2025-07-16T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 150,
      "totalPages": 3
    }
  }
}
```

## 配置管理

### 权限配置文件

权限系统的配置文件位于 `backend/config/permissions.json`：

```json
{
  "cache": {
    "ttl": 300,
    "maxSize": 10000,
    "enabled": true
  },
  "audit": {
    "enabled": true,
    "retention": 90,
    "logLevel": "info"
  },
  "security": {
    "requireSecondAuth": ["user.delete.any", "system.backup"],
    "ipWhitelist": ["***********/24"],
    "maxFailedAttempts": 5
  },
  "performance": {
    "maxResponseTime": 50,
    "cacheHitRateThreshold": 0.95
  }
}
```

### 环境变量配置

```bash
# 权限系统配置
PERMISSION_CACHE_TTL=300
PERMISSION_CACHE_MAX_SIZE=10000
AUDIT_LOG_RETENTION_DAYS=90
PERMISSION_MAX_RESPONSE_TIME=50

# Redis配置（用于权限缓存）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sitemanager
DB_USER=root
DB_PASSWORD=
```

### 生产环境配置

生产环境的权限配置文件 `backend/config/permissions.production.json`：

```json
{
  "cache": {
    "ttl": 600,
    "maxSize": 50000,
    "enabled": true
  },
  "audit": {
    "enabled": true,
    "retention": 365,
    "logLevel": "warn"
  },
  "security": {
    "requireSecondAuth": [
      "user.delete.any", 
      "system.backup", 
      "role.assign"
    ],
    "ipWhitelist": ["10.0.0.0/8"],
    "maxFailedAttempts": 3
  },
  "performance": {
    "maxResponseTime": 30,
    "cacheHitRateThreshold": 0.98
  }
}
```

## 故障排查

### 常见问题

#### 1. 权限验证失败

**症状**: 用户无法访问某些功能，提示权限不足

**排查步骤**:
1. 检查用户角色是否正确
2. 验证权限配置是否正确
3. 查看审计日志确认权限检查结果
4. 检查权限缓存是否过期

```bash
# 查看用户权限
curl -H "Authorization: Bearer {token}" \
     http://localhost:3000/api/v1/auth/users/123/permissions

# 检查权限验证
curl -X POST -H "Authorization: Bearer {token}" \
     -H "Content-Type: application/json" \
     -d '{"userId": 123, "permissions": ["user.create"]}' \
     http://localhost:3000/api/v1/auth/permissions/check
```

#### 2. 权限缓存问题

**症状**: 权限更改后不立即生效

**解决方案**:
```javascript
// 清除用户权限缓存
const cacheService = new PermissionCacheService();
await cacheService.clearUserPermissions(userId);

// 清除角色权限缓存
await cacheService.clearRolePermissions('admin');
```

#### 3. 性能问题

**症状**: 权限验证响应缓慢

**排查步骤**:
1. 检查数据库权限查询性能
2. 验证缓存命中率
3. 查看权限验证响应时间
4. 检查数据库连接池状态

```bash
# 运行性能测试
npm run test:permission:performance

# 查看性能报告
node scripts/run-performance-tests.js all --report
```

### 日志分析

#### 权限访问日志
```sql
-- 查看权限拒绝日志
SELECT * FROM audit_logs 
WHERE action = 'permission_access' 
  AND result = 'denied' 
  AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY created_at DESC;

-- 查看用户权限变更日志
SELECT * FROM audit_logs 
WHERE action = 'permission_change' 
  AND user_id = 123
ORDER BY created_at DESC;
```

#### 系统日志
```bash
# 查看权限系统日志
tail -f logs/permission.log

# 查看错误日志
grep "permission" logs/error.log
```

## 最佳实践

### 权限设计原则

1. **最小权限原则**
   - 用户只获得完成工作所需的最小权限
   - 定期审查和清理不必要的权限

2. **职责分离**
   - 关键操作需要多个权限配合
   - 避免单一用户拥有过多权限

3. **权限继承**
   - 合理使用角色权限继承
   - 避免过度复杂的权限层次

### 安全建议

1. **定期权限审计**
   ```bash
   # 运行权限审计脚本
   node scripts/permission-audit.js
   
   # 生成权限报告
   node scripts/generate-permission-report.js
   ```

2. **监控异常访问**
   - 设置权限拒绝告警
   - 监控权限提升尝试
   - 记录可疑操作模式

3. **权限变更管控**
   - 重要权限变更需要审批
   - 记录权限变更原因
   - 定期备份权限配置

### 性能优化

1. **缓存策略**
   - 合理设置缓存过期时间
   - 使用多层缓存架构
   - 监控缓存命中率

2. **数据库优化**
   - 为权限查询添加索引
   - 优化权限表结构
   - 使用连接池管理

3. **代码优化**
   - 减少不必要的权限检查
   - 批量处理权限验证
   - 异步处理审计日志

### 开发建议

1. **权限测试**
   - 为每个权限编写测试用例
   - 测试权限边界情况
   - 验证权限继承逻辑

2. **错误处理**
   - 提供友好的权限错误提示
   - 记录详细的错误日志
   - 实现权限错误恢复机制

3. **文档维护**
   - 及时更新权限文档
   - 记录权限变更历史
   - 提供权限使用示例

## 总结

权限系统是保障系统安全的重要组成部分。通过合理的权限设计、严格的权限管控和持续的权限监控，可以有效保护系统资源，防止未授权访问。

在使用权限系统时，请遵循最小权限原则，定期进行权限审计，并关注系统性能和安全性。如有问题，请参考故障排查部分或联系系统管理员。