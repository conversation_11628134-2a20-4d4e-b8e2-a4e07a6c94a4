/**
 * 简单测试增强版HTTP检查器
 */

const { <PERSON>hanced<PERSON>ttp<PERSON><PERSON><PERSON>, MONITOR_STATUS } = require('./services/enhanced-http-checker');

async function simpleTest() {
  console.log('🚀 开始简单测试...');

  const checker = new EnhancedHttpChecker();

  try {
    // 测试基础HTTP检查
    console.log('📡 测试基础HTTP检查...');
    const result = await checker.checkHttpSingle({
      url: 'https://www.baidu.com',
      httpMethod: 'GET',
      statusCodes: '200-299',
      connectTimeout: 10
    });

    console.log('✅ 检查结果:');
    console.log(`   状态: ${result.status === MONITOR_STATUS.UP ? '✅ 正常' : '❌ 异常'}`);
    console.log(`   消息: ${result.message}`);
    console.log(`   响应时间: ${result.ping}ms`);

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }

  console.log('🎉 测试完成!');
}

simpleTest();
