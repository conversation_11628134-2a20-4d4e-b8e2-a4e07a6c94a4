#!/usr/bin/env node

/**
 * 权限系统测试运行脚本
 * 提供便捷的测试执行和报告功能
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 测试配置
const testConfigs = {
  unit: {
    name: '单元测试',
    command: 'npm',
    args: ['run', 'test:unit'],
    description: '运行权限服务和中间件的单元测试'
  },
  integration: {
    name: '集成测试',
    command: 'npm',
    args: ['run', 'test:integration'],
    description: '运行权限系统集成测试'
  },
  permission: {
    name: '权限系统测试',
    command: 'npm',
    args: ['run', 'test:permission'],
    description: '运行所有权限相关测试'
  },
  coverage: {
    name: '覆盖率测试',
    command: 'npm',
    args: ['run', 'test:coverage'],
    description: '运行测试并生成覆盖率报告'
  },
  all: {
    name: '完整测试',
    command: 'npm',
    args: ['test'],
    description: '运行所有测试'
  },
  watch: {
    name: '监听模式',
    command: 'npm',
    args: ['run', 'test:watch'],
    description: '以监听模式运行测试'
  }
};

// 解析命令行参数
const args = process.argv.slice(2);
const testType = args[0] || 'all';
const options = args.slice(1);

// 显示帮助信息
function showHelp() {
  colorLog('cyan', '\n权限系统测试运行器');
  colorLog('cyan', '='.repeat(50));
  
  console.log('\n使用方法:');
  console.log('  node scripts/run-tests.js [测试类型] [选项]');
  
  console.log('\n可用的测试类型:');
  Object.entries(testConfigs).forEach(([key, config]) => {
    console.log(`  ${colors.green}${key.padEnd(12)}${colors.reset} - ${config.description}`);
  });
  
  console.log('\n选项:');
  console.log('  --verbose     显示详细输出');
  console.log('  --silent      静默模式');
  console.log('  --bail        遇到错误时停止');
  console.log('  --help        显示此帮助信息');
  
  console.log('\n示例:');
  console.log('  node scripts/run-tests.js unit');
  console.log('  node scripts/run-tests.js coverage --verbose');
  console.log('  node scripts/run-tests.js permission --bail');
  console.log('');
}

// 检查测试环境
function checkTestEnvironment() {
  const requiredFiles = [
    'jest.config.js',
    'test/setup.js',
    'test/services/PermissionService.test.js',
    'test/services/RoleService.test.js',
    'test/services/PermissionCacheService.test.js',
    'test/middleware/PermissionMiddleware.test.js',
    'test/integration/PermissionSystem.integration.test.js'
  ];

  const missingFiles = requiredFiles.filter(file => 
    !fs.existsSync(path.join(__dirname, '..', file))
  );

  if (missingFiles.length > 0) {
    colorLog('red', '\n❌ 测试环境检查失败');
    colorLog('red', '缺少以下文件:');
    missingFiles.forEach(file => {
      console.log(`  - ${file}`);
    });
    console.log('');
    return false;
  }

  colorLog('green', '✅ 测试环境检查通过');
  return true;
}

// 运行测试
function runTest(config, additionalArgs = []) {
  return new Promise((resolve, reject) => {
    colorLog('blue', `\n🚀 开始运行: ${config.name}`);
    colorLog('blue', '-'.repeat(50));
    
    const allArgs = [...config.args, ...additionalArgs];
    const child = spawn(config.command, allArgs, {
      stdio: 'inherit',
      cwd: path.join(__dirname, '..'),
      shell: process.platform === 'win32'
    });

    child.on('close', (code) => {
      if (code === 0) {
        colorLog('green', `\n✅ ${config.name} 完成`);
        resolve(code);
      } else {
        colorLog('red', `\n❌ ${config.name} 失败 (退出码: ${code})`);
        reject(new Error(`测试失败，退出码: ${code}`));
      }
    });

    child.on('error', (error) => {
      colorLog('red', `\n❌ 运行 ${config.name} 时出错: ${error.message}`);
      reject(error);
    });
  });
}

// 生成测试报告
function generateTestReport() {
  const coverageDir = path.join(__dirname, '..', 'coverage');
  const reportFile = path.join(coverageDir, 'lcov-report', 'index.html');
  
  if (fs.existsSync(reportFile)) {
    colorLog('cyan', '\n📊 测试报告已生成:');
    console.log(`  覆盖率报告: file://${reportFile}`);
    
    const htmlReportDir = path.join(coverageDir, 'html-report');
    const htmlReportFile = path.join(htmlReportDir, 'report.html');
    
    if (fs.existsSync(htmlReportFile)) {
      console.log(`  详细报告: file://${htmlReportFile}`);
    }
  }
}

// 显示测试统计
function showTestStats() {
  const coverageDir = path.join(__dirname, '..', 'coverage');
  const coverageSummaryFile = path.join(coverageDir, 'coverage-summary.json');
  
  if (fs.existsSync(coverageSummaryFile)) {
    try {
      const summary = JSON.parse(fs.readFileSync(coverageSummaryFile, 'utf8'));
      const total = summary.total;
      
      colorLog('cyan', '\n📈 测试覆盖率统计:');
      console.log(`  语句覆盖率: ${total.statements.pct}%`);
      console.log(`  分支覆盖率: ${total.branches.pct}%`);
      console.log(`  函数覆盖率: ${total.functions.pct}%`);
      console.log(`  行覆盖率: ${total.lines.pct}%`);
      
      // 检查覆盖率阈值
      const thresholds = {
        statements: 80,
        branches: 80,
        functions: 80,
        lines: 80
      };
      
      let allPassed = true;
      Object.entries(thresholds).forEach(([key, threshold]) => {
        if (total[key].pct < threshold) {
          colorLog('yellow', `  ⚠️  ${key} 覆盖率 (${total[key].pct}%) 低于阈值 (${threshold}%)`);
          allPassed = false;
        }
      });
      
      if (allPassed) {
        colorLog('green', '  ✅ 所有覆盖率指标都达到了阈值要求');
      }
      
    } catch (error) {
      colorLog('yellow', '⚠️  无法读取覆盖率统计信息');
    }
  }
}

// 主函数
async function main() {
  // 显示帮助信息
  if (options.includes('--help') || testType === 'help') {
    showHelp();
    return;
  }

  // 检查测试类型
  if (!testConfigs[testType]) {
    colorLog('red', `❌ 未知的测试类型: ${testType}`);
    colorLog('yellow', '使用 --help 查看可用的测试类型');
    process.exit(1);
  }

  // 检查测试环境
  if (!checkTestEnvironment()) {
    process.exit(1);
  }

  // 准备额外参数
  const additionalArgs = [];
  
  if (options.includes('--verbose')) {
    additionalArgs.push('--verbose');
  }
  
  if (options.includes('--silent')) {
    additionalArgs.push('--silent');
  }
  
  if (options.includes('--bail')) {
    additionalArgs.push('--bail');
  }

  try {
    const startTime = Date.now();
    
    // 运行测试
    await runTest(testConfigs[testType], additionalArgs);
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    colorLog('green', `\n🎉 测试完成! 耗时: ${duration}秒`);
    
    // 生成报告（如果是覆盖率测试）
    if (testType === 'coverage' || additionalArgs.includes('--coverage')) {
      generateTestReport();
      showTestStats();
    }
    
  } catch (error) {
    colorLog('red', `\n💥 测试执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  colorLog('red', `\n💥 未捕获的异常: ${error.message}`);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  colorLog('red', `\n💥 未处理的Promise拒绝: ${reason}`);
  process.exit(1);
});

// 处理中断信号
process.on('SIGINT', () => {
  colorLog('yellow', '\n\n⏹️  测试被用户中断');
  process.exit(0);
});

process.on('SIGTERM', () => {
  colorLog('yellow', '\n\n⏹️  测试被终止');
  process.exit(0);
});

// 运行主函数
main().catch(error => {
  colorLog('red', `\n💥 执行失败: ${error.message}`);
  process.exit(1);
});