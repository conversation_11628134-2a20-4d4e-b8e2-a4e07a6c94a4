#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 输出格式：JSON格式，便于程序解析
output_json() {
    echo "{"
    
    # 1. 系统版本信息
    echo "  \"system\": {"
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        echo "    \"os\": \"$NAME $VERSION\","
        echo "    \"pretty_name\": \"$PRETTY_NAME\","
    elif [ -f /etc/redhat-release ]; then
        os_info=$(cat /etc/redhat-release)
        echo "    \"os\": \"$os_info\","
        echo "    \"pretty_name\": \"$os_info\","
    elif [ -f /etc/debian_version ]; then
        os_info="Debian $(cat /etc/debian_version)"
        echo "    \"os\": \"$os_info\","
        echo "    \"pretty_name\": \"$os_info\","
    else
        os_info="$(uname -s) $(uname -r)"
        echo "    \"os\": \"$os_info\","
        echo "    \"pretty_name\": \"$os_info\","
    fi
    
    echo "    \"kernel\": \"$(uname -r)\","
    echo "    \"architecture\": \"$(uname -m)\","
    echo "    \"hostname\": \"$(hostname)\""
    echo "  },"
    
    # 2. CPU信息
    echo "  \"cpu\": {"
    cpu_cores=$(nproc)
    cpu_model=$(cat /proc/cpuinfo | grep 'model name' | head -1 | cut -d':' -f2 | sed 's/^[ \t]*//' | sed 's/[ \t]*$//')
    cpu_freq=$(cat /proc/cpuinfo | grep 'cpu MHz' | head -1 | cut -d':' -f2 | sed 's/^[ \t]*//' | awk '{printf "%.0f", $1}')
    
    # CPU使用率
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}' | sed 's/us,//')
    if [ -z "$cpu_usage" ]; then
        cpu_usage=$(grep 'cpu ' /proc/stat | awk '{usage=($2+$4)*100/($2+$4+$5)} END {printf "%.1f", usage}')
    fi
    
    load_avg=$(uptime | awk -F'load average:' '{print $2}' | sed 's/^[ \t]*//')
    
    echo "    \"cores\": $cpu_cores,"
    echo "    \"model\": \"$cpu_model\","
    echo "    \"frequency\": \"$cpu_freq\","
    echo "    \"usage\": \"$cpu_usage\","
    echo "    \"load_average\": \"$load_avg\""
    echo "  },"
    
    # 3. 内存信息
    echo "  \"memory\": {"
    mem_info=$(free -h | grep '^Mem:')
    mem_total=$(echo $mem_info | awk '{print $2}')
    mem_used=$(echo $mem_info | awk '{print $3}')
    mem_free=$(echo $mem_info | awk '{print $4}')
    mem_available=$(echo $mem_info | awk '{print $7}')
    mem_used_percent=$(free | grep '^Mem:' | awk '{printf "%.1f", ($3/$2)*100}')
    
    # 内存总量（GB）
    mem_total_gb=$(free -g | awk 'NR==2{printf "%.0f", $2}')
    
    echo "    \"total\": \"$mem_total\","
    echo "    \"total_gb\": \"$mem_total_gb\","
    echo "    \"used\": \"$mem_used\","
    echo "    \"free\": \"$mem_free\","
    echo "    \"available\": \"$mem_available\","
    echo "    \"used_percent\": \"$mem_used_percent\""
    echo "  },"
    
    # 4. 硬盘信息
    echo "  \"storage\": {"
    # 根分区信息
    root_info=$(df -h / | tail -1)
    disk_total=$(echo $root_info | awk '{print $2}')
    disk_used=$(echo $root_info | awk '{print $3}')
    disk_available=$(echo $root_info | awk '{print $4}')
    disk_used_percent=$(echo $root_info | awk '{print $5}')
    
    # 硬盘类型检测
    disk_type="Unknown"
    main_disk=$(df / | tail -1 | awk '{print $1}' | sed 's/[0-9]*$//')
    if [ -n "$main_disk" ]; then
        disk_name=$(basename $main_disk)
        if [ -f "/sys/block/$disk_name/queue/rotational" ]; then
            rotational=$(cat /sys/block/$disk_name/queue/rotational)
            if [ "$rotational" = "0" ]; then
                disk_type="SSD"
            else
                disk_type="HDD"
            fi
        fi
    fi
    
    echo "    \"total\": \"$disk_total\","
    echo "    \"used\": \"$disk_used\","
    echo "    \"available\": \"$disk_available\","
    echo "    \"used_percent\": \"$disk_used_percent\","
    echo "    \"type\": \"$disk_type\""
    echo "  },"
    
    # 5. 网络信息
    echo "  \"network\": {"
    # 获取主要网络接口
    main_interface=$(ip route | grep default | awk '{print $5}' | head -1)
    if [ -z "$main_interface" ]; then
        main_interface=$(ls /sys/class/net/ | grep -E '^(eth|ens|enp|wlan)' | head -1)
    fi
    
    if [ -n "$main_interface" ]; then
        rx_bytes=$(cat /sys/class/net/${main_interface}/statistics/rx_bytes 2>/dev/null || echo "0")
        tx_bytes=$(cat /sys/class/net/${main_interface}/statistics/tx_bytes 2>/dev/null || echo "0")
        rx_human=$(numfmt --to=iec-i --suffix=B $rx_bytes 2>/dev/null || echo "${rx_bytes} B")
        tx_human=$(numfmt --to=iec-i --suffix=B $tx_bytes 2>/dev/null || echo "${tx_bytes} B")
        
        echo "    \"main_interface\": \"$main_interface\","
        echo "    \"rx_bytes\": \"$rx_bytes\","
        echo "    \"tx_bytes\": \"$tx_bytes\","
        echo "    \"rx_human\": \"$rx_human\","
        echo "    \"tx_human\": \"$tx_human\""
    else
        echo "    \"main_interface\": \"unknown\","
        echo "    \"rx_bytes\": \"0\","
        echo "    \"tx_bytes\": \"0\","
        echo "    \"rx_human\": \"0 B\","
        echo "    \"tx_human\": \"0 B\""
    fi
    echo "  },"
    
    # 6. 时间戳
    echo "  \"timestamp\": \"$(date '+%Y-%m-%d %H:%M:%S')\""
    echo "}"
}

# 根据参数决定输出格式
if [ "$1" = "--json" ]; then
    output_json
else
    # 原有的彩色输出格式
    echo -e "${WHITE}========================================${NC}"
    echo -e "${WHITE}        系统信息监控报告${NC}"
    echo -e "${WHITE}========================================${NC}"
    echo

    # 1. 系统版本信息
    echo -e "${CYAN}📋 系统版本信息:${NC}"
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        echo -e "  操作系统: ${GREEN}$NAME $VERSION${NC}"
    elif [ -f /etc/redhat-release ]; then
        echo -e "  操作系统: ${GREEN}$(cat /etc/redhat-release)${NC}"
    elif [ -f /etc/debian_version ]; then
        echo -e "  操作系统: ${GREEN}Debian $(cat /etc/debian_version)${NC}"
    else
        echo -e "  操作系统: ${GREEN}$(uname -s) $(uname -r)${NC}"
    fi

    echo -e "  内核版本: ${GREEN}$(uname -r)${NC}"
    echo -e "  架构: ${GREEN}$(uname -m)${NC}"
    echo -e "  主机名: ${GREEN}$(hostname)${NC}"
    echo

    # 2. CPU使用率
    echo -e "${YELLOW}🔥 CPU使用情况:${NC}"
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
    if [ -z "$cpu_usage" ]; then
        # 备用方法
        cpu_usage=$(grep 'cpu ' /proc/stat | awk '{usage=($2+$4)*100/($2+$4+$5)} END {print usage}')
    fi

    # CPU核心数
    cpu_cores=$(nproc)
    load_avg=$(uptime | awk -F'load average:' '{print $2}')

    echo -e "  CPU核心数: ${GREEN}${cpu_cores}${NC}"
    echo -e "  CPU使用率: ${GREEN}${cpu_usage}%${NC}"
    echo -e "  负载平均值:${GREEN}${load_avg}${NC}"
    echo

    # 3. 内存使用情况
    echo -e "${BLUE}💾 内存使用情况:${NC}"
    mem_info=$(free -h | grep '^Mem:')
    mem_total=$(echo $mem_info | awk '{print $2}')
    mem_used=$(echo $mem_info | awk '{print $3}')
    mem_free=$(echo $mem_info | awk '{print $4}')
    mem_available=$(echo $mem_info | awk '{print $7}')

    # 计算内存使用百分比
    mem_used_percent=$(free | grep '^Mem:' | awk '{printf "%.1f", ($3/$2)*100}')

    echo -e "  总内存: ${GREEN}${mem_total}${NC}"
    echo -e "  已使用: ${GREEN}${mem_used} (${mem_used_percent}%)${NC}"
    echo -e "  空闲: ${GREEN}${mem_free}${NC}"
    echo -e "  可用: ${GREEN}${mem_available}${NC}"
    echo

    # 4. 硬盘使用情况
    echo -e "${PURPLE}💽 硬盘使用情况:${NC}"
    echo -e "  ${WHITE}文件系统        总大小  已用  可用 使用率 挂载点${NC}"
    df -h | grep -E '^/dev/' | while read line; do
        filesystem=$(echo $line | awk '{print $1}')
        size=$(echo $line | awk '{print $2}')
        used=$(echo $line | awk '{print $3}')
        avail=$(echo $line | awk '{print $4}')
        use_percent=$(echo $line | awk '{print $5}')
        mount=$(echo $line | awk '{print $6}')
        
        # 根据使用率设置颜色
        if [[ ${use_percent%?} -gt 80 ]]; then
            color=$RED
        elif [[ ${use_percent%?} -gt 60 ]]; then
            color=$YELLOW
        else
            color=$GREEN
        fi
        
        printf "  %-15s %6s %5s %5s ${color}%6s${NC} %s\n" "$filesystem" "$size" "$used" "$avail" "$use_percent" "$mount"
    done
    echo

    # 5. 网络流量统计
    echo -e "${CYAN}🌐 网络流量统计:${NC}"

    # 获取主要网络接口
    main_interface=$(ip route | grep default | awk '{print $5}' | head -1)
    if [ -z "$main_interface" ]; then
        main_interface=$(ls /sys/class/net/ | grep -E '^(eth|ens|enp|wlan)' | head -1)
    fi

    if [ -n "$main_interface" ]; then
        echo -e "  主网络接口: ${GREEN}${main_interface}${NC}"
        
        # 读取网络统计信息
        rx_bytes=$(cat /sys/class/net/${main_interface}/statistics/rx_bytes 2>/dev/null || echo "0")
        tx_bytes=$(cat /sys/class/net/${main_interface}/statistics/tx_bytes 2>/dev/null || echo "0")
        
        # 转换为人类可读格式
        rx_human=$(numfmt --to=iec-i --suffix=B $rx_bytes 2>/dev/null || echo "${rx_bytes} B")
        tx_human=$(numfmt --to=iec-i --suffix=B $tx_bytes 2>/dev/null || echo "${tx_bytes} B")
        
        echo -e "  总下载流量: ${GREEN}${rx_human}${NC}"
        echo -e "  总上传流量: ${GREEN}${tx_human}${NC}"
    else
        echo -e "  ${RED}无法获取网络接口信息${NC}"
    fi

    # 显示所有网络接口的流量
    echo -e "\n  所有网络接口流量统计:"
    echo -e "  ${WHITE}接口名称    下载流量      上传流量${NC}"
    for interface in $(ls /sys/class/net/ | grep -v lo); do
        if [ -f "/sys/class/net/${interface}/statistics/rx_bytes" ]; then
            rx_bytes=$(cat /sys/class/net/${interface}/statistics/rx_bytes)
            tx_bytes=$(cat /sys/class/net/${interface}/statistics/tx_bytes)
            rx_human=$(numfmt --to=iec-i --suffix=B $rx_bytes 2>/dev/null || echo "${rx_bytes} B")
            tx_human=$(numfmt --to=iec-i --suffix=B $tx_bytes 2>/dev/null || echo "${tx_bytes} B")
            printf "  %-10s %12s %12s\n" "$interface" "$rx_human" "$tx_human"
        fi
    done

    echo
    echo -e "${WHITE}========================================${NC}"
    echo -e "报告生成时间: ${GREEN}$(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo -e "${WHITE}========================================${NC}"
fi
