#!/usr/bin/env node

/**
 * 权限系统性能测试运行脚本
 * 执行各种性能测试并生成详细报告
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const { performance } = require('perf_hooks');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 性能测试配置
const PERFORMANCE_CONFIG = {
  basic: {
    name: '基础性能测试',
    tests: [
      'permission-check',
      'cache-performance',
      'middleware-performance'
    ],
    timeout: 30000
  },
  stress: {
    name: '压力测试',
    tests: [
      'concurrent-users',
      'high-load',
      'memory-usage'
    ],
    timeout: 60000
  },
  all: {
    name: '完整性能测试',
    tests: [
      'permission-check',
      'cache-performance', 
      'middleware-performance',
      'concurrent-users',
      'high-load',
      'memory-usage',
      'regression-test'
    ],
    timeout: 120000
  }
};

// 性能基准
const PERFORMANCE_BENCHMARKS = {
  permissionCheckTime: 50,      // ms
  cacheHitRate: 0.95,          // 95%
  middlewareResponseTime: 50,   // ms
  concurrentUsers: 100,         // 用户数
  memoryPerUser: 1024,         // bytes
  errorRate: 0.01              // 1%
};

class PerformanceTestRunner {
  constructor() {
    this.results = {
      startTime: new Date(),
      endTime: null,
      totalDuration: 0,
      tests: {},
      summary: {
        passed: 0,
        failed: 0,
        total: 0
      },
      benchmarks: PERFORMANCE_BENCHMARKS
    };
  }

  // 运行Jest性能测试
  async runJestPerformanceTests() {
    colorLog('🧪 运行Jest性能测试...', 'blue');
    
    try {
      const command = 'npx jest --config=jest.performance.config.js --verbose';
      colorLog(`执行命令: ${command}`, 'cyan');
      
      const output = execSync(command, { 
        stdio: 'pipe', 
        cwd: path.join(__dirname, '..'),
        encoding: 'utf8'
      });
      
      colorLog('✅ Jest性能测试完成', 'green');
      return { success: true, output };
    } catch (error) {
      colorLog('❌ Jest性能测试失败', 'red');
      return { success: false, error: error.message, output: error.stdout };
    }
  }

  // 运行自定义权限检查性能测试
  async runPermissionCheckTest() {
    colorLog('⚡ 运行权限检查性能测试...', 'blue');
    
    const testStart = performance.now();
    const iterations = 1000;
    const results = [];
    
    try {
      // 模拟权限检查
      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        // 模拟权限验证逻辑
        await new Promise(resolve => {
          setTimeout(() => {
            // 模拟数据库查询和缓存访问
            const mockDelay = Math.random() * 10; // 0-10ms随机延迟
            setTimeout(resolve, mockDelay);
          }, 1);
        });
        
        const end = performance.now();
        results.push(end - start);
      }
      
      const testEnd = performance.now();
      const totalTime = testEnd - testStart;
      const avgTime = results.reduce((sum, time) => sum + time, 0) / results.length;
      const maxTime = Math.max(...results);
      const minTime = Math.min(...results);
      
      const passed = avgTime <= PERFORMANCE_BENCHMARKS.permissionCheckTime;
      
      this.results.tests.permissionCheck = {
        name: '权限检查性能',
        passed,
        totalTime,
        avgTime,
        maxTime,
        minTime,
        iterations,
        benchmark: PERFORMANCE_BENCHMARKS.permissionCheckTime,
        details: `平均时间: ${avgTime.toFixed(2)}ms, 最大时间: ${maxTime.toFixed(2)}ms`
      };
      
      if (passed) {
        colorLog(`✅ 权限检查性能测试通过 (平均 ${avgTime.toFixed(2)}ms)`, 'green');
      } else {
        colorLog(`❌ 权限检查性能测试失败 (平均 ${avgTime.toFixed(2)}ms > ${PERFORMANCE_BENCHMARKS.permissionCheckTime}ms)`, 'red');
      }
      
      return passed;
    } catch (error) {
      colorLog(`❌ 权限检查性能测试出错: ${error.message}`, 'red');
      this.results.tests.permissionCheck = {
        name: '权限检查性能',
        passed: false,
        error: error.message
      };
      return false;
    }
  }

  // 运行缓存性能测试
  async runCachePerformanceTest() {
    colorLog('💾 运行缓存性能测试...', 'blue');
    
    try {
      const cacheSize = 1000;
      const testIterations = 10000;
      const cache = new Map();
      
      // 预填充缓存
      for (let i = 0; i < cacheSize; i++) {
        cache.set(`user:${i}`, {
          userId: i,
          permissions: ['user.read.own', 'server.read.own']
        });
      }
      
      let cacheHits = 0;
      let cacheMisses = 0;
      const accessTimes = [];
      
      const testStart = performance.now();
      
      for (let i = 0; i < testIterations; i++) {
        const userId = Math.floor(Math.random() * (cacheSize * 1.2)); // 20%概率缓存未命中
        const key = `user:${userId}`;
        
        const start = performance.now();
        
        if (cache.has(key)) {
          cache.get(key);
          cacheHits++;
        } else {
          // 模拟数据库查询
          await new Promise(resolve => setTimeout(resolve, 5));
          cache.set(key, { userId, permissions: ['user.read.own'] });
          cacheMisses++;
        }
        
        const end = performance.now();
        accessTimes.push(end - start);
      }
      
      const testEnd = performance.now();
      const totalTime = testEnd - testStart;
      const hitRate = cacheHits / testIterations;
      const avgAccessTime = accessTimes.reduce((sum, time) => sum + time, 0) / accessTimes.length;
      
      const hitRatePassed = hitRate >= PERFORMANCE_BENCHMARKS.cacheHitRate;
      const accessTimePassed = avgAccessTime <= 5; // 5ms阈值
      const passed = hitRatePassed && accessTimePassed;
      
      this.results.tests.cachePerformance = {
        name: '缓存性能',
        passed,
        totalTime,
        hitRate,
        avgAccessTime,
        cacheHits,
        cacheMisses,
        testIterations,
        benchmark: {
          hitRate: PERFORMANCE_BENCHMARKS.cacheHitRate,
          accessTime: 5
        },
        details: `命中率: ${(hitRate * 100).toFixed(2)}%, 平均访问时间: ${avgAccessTime.toFixed(2)}ms`
      };
      
      if (passed) {
        colorLog(`✅ 缓存性能测试通过 (命中率: ${(hitRate * 100).toFixed(2)}%)`, 'green');
      } else {
        colorLog(`❌ 缓存性能测试失败`, 'red');
        if (!hitRatePassed) {
          colorLog(`   命中率不足: ${(hitRate * 100).toFixed(2)}% < ${(PERFORMANCE_BENCHMARKS.cacheHitRate * 100)}%`, 'red');
        }
        if (!accessTimePassed) {
          colorLog(`   访问时间过长: ${avgAccessTime.toFixed(2)}ms > 5ms`, 'red');
        }
      }
      
      return passed;
    } catch (error) {
      colorLog(`❌ 缓存性能测试出错: ${error.message}`, 'red');
      this.results.tests.cachePerformance = {
        name: '缓存性能',
        passed: false,
        error: error.message
      };
      return false;
    }
  }

  // 运行并发用户测试
  async runConcurrentUsersTest() {
    colorLog('👥 运行并发用户测试...', 'blue');
    
    try {
      const concurrentUsers = 200;
      const operationsPerUser = 10;
      
      const testStart = performance.now();
      
      const userPromises = Array.from({ length: concurrentUsers }, async (_, userId) => {
        const userOperations = [];
        
        for (let i = 0; i < operationsPerUser; i++) {
          userOperations.push(
            new Promise(resolve => {
              // 模拟权限检查操作
              const delay = Math.random() * 20; // 0-20ms随机延迟
              setTimeout(resolve, delay);
            })
          );
        }
        
        return Promise.all(userOperations);
      });
      
      await Promise.all(userPromises);
      
      const testEnd = performance.now();
      const totalTime = testEnd - testStart;
      const totalOperations = concurrentUsers * operationsPerUser;
      const operationsPerSecond = totalOperations / (totalTime / 1000);
      
      const passed = operationsPerSecond >= 100; // 至少100操作/秒
      
      this.results.tests.concurrentUsers = {
        name: '并发用户测试',
        passed,
        totalTime,
        concurrentUsers,
        operationsPerUser,
        totalOperations,
        operationsPerSecond,
        benchmark: 100,
        details: `${concurrentUsers}并发用户, ${operationsPerSecond.toFixed(0)}操作/秒`
      };
      
      if (passed) {
        colorLog(`✅ 并发用户测试通过 (${operationsPerSecond.toFixed(0)}操作/秒)`, 'green');
      } else {
        colorLog(`❌ 并发用户测试失败 (${operationsPerSecond.toFixed(0)}操作/秒 < 100操作/秒)`, 'red');
      }
      
      return passed;
    } catch (error) {
      colorLog(`❌ 并发用户测试出错: ${error.message}`, 'red');
      this.results.tests.concurrentUsers = {
        name: '并发用户测试',
        passed: false,
        error: error.message
      };
      return false;
    }
  }

  // 运行内存使用测试
  async runMemoryUsageTest() {
    colorLog('🧠 运行内存使用测试...', 'blue');
    
    try {
      const initialMemory = process.memoryUsage();
      
      // 创建大量权限数据模拟内存使用
      const userCount = 1000;
      const permissionData = [];
      
      for (let i = 0; i < userCount; i++) {
        permissionData.push({
          userId: i,
          role: i % 3 === 0 ? 'admin' : 'user',
          permissions: [
            'user.read.own', 'server.read.own', 'website.read.own',
            'user.create', 'server.create', 'website.create'
          ],
          cache: new Map()
        });
      }
      
      // 模拟缓存使用
      permissionData.forEach(user => {
        user.cache.set('lastAccess', Date.now());
        user.cache.set('permissions', user.permissions);
      });
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryPerUser = memoryIncrease / userCount;
      
      const passed = memoryPerUser <= PERFORMANCE_BENCHMARKS.memoryPerUser;
      
      this.results.tests.memoryUsage = {
        name: '内存使用测试',
        passed,
        userCount,
        totalMemoryIncrease: memoryIncrease,
        memoryPerUser,
        benchmark: PERFORMANCE_BENCHMARKS.memoryPerUser,
        initialMemory: initialMemory.heapUsed,
        finalMemory: finalMemory.heapUsed,
        details: `${userCount}用户使用 ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB, 平均 ${memoryPerUser.toFixed(0)}字节/用户`
      };
      
      if (passed) {
        colorLog(`✅ 内存使用测试通过 (${memoryPerUser.toFixed(0)}字节/用户)`, 'green');
      } else {
        colorLog(`❌ 内存使用测试失败 (${memoryPerUser.toFixed(0)}字节/用户 > ${PERFORMANCE_BENCHMARKS.memoryPerUser}字节/用户)`, 'red');
      }
      
      // 清理内存
      permissionData.length = 0;
      
      return passed;
    } catch (error) {
      colorLog(`❌ 内存使用测试出错: ${error.message}`, 'red');
      this.results.tests.memoryUsage = {
        name: '内存使用测试',
        passed: false,
        error: error.message
      };
      return false;
    }
  }

  // 运行指定类型的测试
  async runTestSuite(testType = 'basic') {
    const config = PERFORMANCE_CONFIG[testType];
    if (!config) {
      throw new Error(`未知的测试类型: ${testType}`);
    }
    
    colorLog(`🚀 开始${config.name}...`, 'bright');
    colorLog('='.repeat(60), 'cyan');
    
    const testMethods = {
      'permission-check': () => this.runPermissionCheckTest(),
      'cache-performance': () => this.runCachePerformanceTest(),
      'middleware-performance': () => this.runJestPerformanceTests(),
      'concurrent-users': () => this.runConcurrentUsersTest(),
      'high-load': () => this.runConcurrentUsersTest(),
      'memory-usage': () => this.runMemoryUsageTest(),
      'regression-test': () => this.runJestPerformanceTests()
    };
    
    let passedTests = 0;
    let totalTests = config.tests.length;
    
    for (const testName of config.tests) {
      const testMethod = testMethods[testName];
      if (testMethod) {
        const passed = await testMethod();
        if (passed) {
          passedTests++;
        }
      } else {
        colorLog(`⚠️  未知测试: ${testName}`, 'yellow');
      }
    }
    
    this.results.summary = {
      passed: passedTests,
      failed: totalTests - passedTests,
      total: totalTests
    };
    
    return passedTests === totalTests;
  }

  // 生成性能报告
  generateReport(generateFile = false) {
    this.results.endTime = new Date();
    this.results.totalDuration = this.results.endTime - this.results.startTime;
    
    colorLog('\n📊 性能测试报告', 'bright');
    colorLog('='.repeat(60), 'cyan');
    
    colorLog(`测试时间: ${this.results.startTime.toLocaleString()} - ${this.results.endTime.toLocaleString()}`, 'blue');
    colorLog(`总耗时: ${(this.results.totalDuration / 1000).toFixed(2)}秒`, 'blue');
    colorLog(`测试结果: ${this.results.summary.passed}/${this.results.summary.total} 通过`, 
      this.results.summary.failed === 0 ? 'green' : 'yellow');
    
    colorLog('\n详细结果:', 'yellow');
    for (const [testName, testResult] of Object.entries(this.results.tests)) {
      const status = testResult.passed ? '✅ 通过' : '❌ 失败';
      const color = testResult.passed ? 'green' : 'red';
      colorLog(`  ${status} ${testResult.name}`, color);
      if (testResult.details) {
        colorLog(`     ${testResult.details}`, 'cyan');
      }
      if (testResult.error) {
        colorLog(`     错误: ${testResult.error}`, 'red');
      }
    }
    
    if (generateFile) {
      const reportPath = path.join(__dirname, '..', 'performance-report.json');
      fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
      colorLog(`\n📄 详细报告已保存: ${reportPath}`, 'cyan');
    }
    
    return this.results;
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const testType = args[0] || 'basic';
  const generateReport = args.includes('--report');
  
  if (args.includes('--help') || args.includes('-h')) {
    colorLog('权限系统性能测试运行器', 'bright');
    colorLog('');
    colorLog('用法: node run-performance-tests.js [测试类型] [选项]', 'cyan');
    colorLog('');
    colorLog('测试类型:', 'yellow');
    colorLog('  basic      基础性能测试 (默认)', 'cyan');
    colorLog('  stress     压力测试', 'cyan');
    colorLog('  all        完整性能测试', 'cyan');
    colorLog('');
    colorLog('选项:', 'yellow');
    colorLog('  --report   生成详细报告文件', 'cyan');
    colorLog('  --help, -h 显示帮助信息', 'cyan');
    colorLog('');
    return;
  }
  
  if (!PERFORMANCE_CONFIG[testType]) {
    colorLog(`❌ 未知的测试类型: ${testType}`, 'red');
    colorLog('可用类型: basic, stress, all', 'yellow');
    process.exit(1);
  }
  
  const runner = new PerformanceTestRunner();
  
  try {
    const allPassed = await runner.runTestSuite(testType);
    const report = runner.generateReport(generateReport);
    
    colorLog('\n' + '='.repeat(60), 'cyan');
    
    if (allPassed) {
      colorLog('🎉 所有性能测试通过！权限系统性能符合要求', 'green');
      process.exit(0);
    } else {
      colorLog('⚠️  部分性能测试未通过，请优化相关功能', 'yellow');
      process.exit(1);
    }
  } catch (error) {
    colorLog(`❌ 性能测试运行出错: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    colorLog(`❌ 程序异常退出: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { PerformanceTestRunner, PERFORMANCE_BENCHMARKS };