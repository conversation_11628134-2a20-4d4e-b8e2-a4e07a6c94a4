#!/usr/bin/env node

/**
 * 系统维护脚本
 * 用于定期维护系统，清理数据，优化性能
 */

const mysql = require('mysql2/promise');
const WebsiteStatusOptimizer = require('../services/WebsiteStatusOptimizer');

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'sitemanager',
  password: 'sitemanager123',
  database: 'sitemanager',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

class SystemMaintenance {
  constructor() {
    this.db = null;
    this.optimizer = null;
  }

  async init() {
    try {
      this.db = await mysql.createConnection(dbConfig);
      this.optimizer = new WebsiteStatusOptimizer(this.db);
      console.log('✅ 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error);
      process.exit(1);
    }
  }

  async runMaintenance() {
    console.log('🔧 开始系统维护...');
    console.log(`⏰ 维护时间: ${new Date().toISOString()}`);
    
    try {
      // 1. 重置异常网站状态
      console.log('\n📋 步骤1: 重置异常网站状态');
      const resetResult = await this.optimizer.resetAbnormalWebsiteStatus();
      
      // 2. 清理过期记录
      console.log('\n📋 步骤2: 清理过期记录');
      const cleanupCount = await this.optimizer.cleanupExpiredRecords();
      
      // 3. 优化数据库表
      console.log('\n📋 步骤3: 优化数据库表');
      await this.optimizeDatabaseTables();
      
      // 4. 生成系统报告
      console.log('\n📋 步骤4: 生成系统报告');
      const report = await this.generateSystemReport();
      
      console.log('\n✅ 系统维护完成!');
      console.log('📊 维护报告:');
      console.log(`   - 重置异常网站: ${resetResult.resetCount} 个`);
      console.log(`   - 清理过期记录: ${cleanupCount} 条`);
      console.log(`   - 健康网站: ${report.healthy_websites} 个`);
      console.log(`   - 警告网站: ${report.warning_websites} 个`);
      console.log(`   - 严重异常网站: ${report.critical_websites} 个`);
      console.log(`   - 平均响应时间: ${Math.round(report.avg_response_time || 0)}ms`);
      
    } catch (error) {
      console.error('❌ 系统维护失败:', error);
      throw error;
    }
  }

  async optimizeDatabaseTables() {
    try {
      const tables = [
        'websites',
        'website_status_stats',
        'servers',
        'notification_configs'
      ];

      for (const table of tables) {
        console.log(`   优化表: ${table}`);
        await this.db.execute(`OPTIMIZE TABLE ${table}`);
      }

      console.log('✅ 数据库表优化完成');
    } catch (error) {
      console.error('❌ 数据库表优化失败:', error);
      throw error;
    }
  }

  async generateSystemReport() {
    try {
      const stats = await this.optimizer.getWebsiteStatusStats();
      
      // 获取最近24小时的检测统计
      const [recentStats] = await this.db.execute(`
        SELECT 
          COUNT(*) as total_checks,
          SUM(CASE WHEN current_status_code BETWEEN 200 AND 399 THEN 1 ELSE 0 END) as successful_checks,
          AVG(current_response_time) as avg_response_time
        FROM website_status_stats 
        WHERE updated_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
      `);

      return {
        ...stats,
        recent_checks: recentStats[0]
      };
    } catch (error) {
      console.error('❌ 生成系统报告失败:', error);
      throw error;
    }
  }

  async close() {
    if (this.db) {
      await this.db.end();
      console.log('✅ 数据库连接已关闭');
    }
  }
}

// 主函数
async function main() {
  const maintenance = new SystemMaintenance();
  
  try {
    await maintenance.init();
    await maintenance.runMaintenance();
  } catch (error) {
    console.error('❌ 维护脚本执行失败:', error);
    process.exit(1);
  } finally {
    await maintenance.close();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = SystemMaintenance;