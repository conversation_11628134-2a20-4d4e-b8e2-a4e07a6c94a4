#!/usr/bin/env node

/**
 * 数据清理脚本
 * 清理过期的检测记录、通知日志等数据
 * 保持数据库性能和存储空间优化
 */

const mysql = require('mysql2/promise');

class CleanupOldRecordsScript {
  constructor() {
    this.db = null;
    this.config = {
      // 数据保留策略（天数）
      retention: {
        statusChecks: 30,        // 状态检测记录保留30天
        notificationLogs: 90,    // 通知日志保留90天
        systemLogs: 7,           // 系统日志保留7天
        backupFiles: 30          // 备份文件保留30天
      },
      
      // 批处理配置
      batchSize: 1000,           // 每批删除1000条记录
      maxBatches: 100,           // 最大批次数
      batchDelay: 100            // 批次间延迟(ms)
    };
    
    this.stats = {
      statusChecksDeleted: 0,
      notificationLogsDeleted: 0,
      systemLogsDeleted: 0,
      backupFilesDeleted: 0,
      totalDeleted: 0,
      errors: 0
    };
  }

  /**
   * 初始化
   */
  async initialize() {
    try {
      console.log('🔧 初始化数据清理脚本...');
      
      // 创建数据库连接
      this.db = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'sitemanager',
        charset: 'utf8mb4'
      });
      
      console.log('✅ 数据库连接成功');
      
      // 从数据库加载配置
      await this.loadConfigFromDatabase();
      
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 从数据库加载配置
   */
  async loadConfigFromDatabase() {
    try {
      const [configs] = await this.db.execute(`
        SELECT config_key, config_value, config_type 
        FROM monitor_system_configs 
        WHERE config_key LIKE 'cleanup_%' OR config_key LIKE 'retention_%'
      `);
      
      configs.forEach(config => {
        const keys = config.config_key.split('_');
        let value = config.config_value;
        
        if (config.config_type === 'number') {
          value = parseInt(value);
        }
        
        if (keys[0] === 'retention' && this.config.retention[keys[1]]) {
          this.config.retention[keys[1]] = value;
        } else if (keys[0] === 'cleanup' && this.config[keys[1]]) {
          this.config[keys[1]] = value;
        }
      });
      
      console.log('✅ 清理配置加载完成');
      
    } catch (error) {
      console.warn('⚠️  从数据库加载清理配置失败，使用默认配置:', error.message);
    }
  }

  /**
   * 执行数据清理
   */
  async run() {
    try {
      console.log('🧹 开始执行数据清理...');
      console.log('📋 清理策略:', this.config.retention);
      
      const startTime = Date.now();
      
      // 1. 清理过期的状态检测记录
      await this.cleanupStatusChecks();
      
      // 2. 清理过期的通知日志
      await this.cleanupNotificationLogs();
      
      // 3. 清理系统日志
      await this.cleanupSystemLogs();
      
      // 4. 清理备份文件
      await this.cleanupBackupFiles();
      
      // 5. 优化数据库表
      await this.optimizeTables();
      
      const duration = Date.now() - startTime;
      console.log(`✅ 数据清理完成，耗时: ${duration}ms`);
      
      // 输出清理统计
      this.printCleanupStats();
      
      // 记录清理结果
      await this.recordCleanupResult();
      
    } catch (error) {
      console.error('❌ 数据清理失败:', error);
      throw error;
    }
  }

  /**
   * 清理过期的状态检测记录
   */
  async cleanupStatusChecks() {
    try {
      console.log(`🗄️  清理 ${this.config.retention.statusChecks} 天前的状态检测记录...`);
      
      // 先统计要删除的记录数
      const [countResult] = await this.db.execute(`
        SELECT COUNT(*) as count 
        FROM website_status_checks 
        WHERE check_time < DATE_SUB(NOW(), INTERVAL ? DAY)
      `, [this.config.retention.statusChecks]);
      
      const totalRecords = countResult[0].count;
      
      if (totalRecords === 0) {
        console.log('ℹ️  没有需要清理的状态检测记录');
        return;
      }
      
      console.log(`📊 找到 ${totalRecords} 条过期记录，开始批量删除...`);
      
      // 批量删除
      let deletedCount = 0;
      let batchCount = 0;
      
      while (deletedCount < totalRecords && batchCount < this.config.maxBatches) {
        const [result] = await this.db.execute(`
          DELETE FROM website_status_checks 
          WHERE check_time < DATE_SUB(NOW(), INTERVAL ? DAY)
          LIMIT ?
        `, [this.config.retention.statusChecks, this.config.batchSize]);
        
        const batchDeleted = result.affectedRows;
        deletedCount += batchDeleted;
        batchCount++;
        
        console.log(`  批次 ${batchCount}: 删除 ${batchDeleted} 条记录`);
        
        if (batchDeleted === 0) break;
        
        // 批次间延迟
        if (batchDeleted === this.config.batchSize) {
          await this.sleep(this.config.batchDelay);
        }
      }
      
      this.stats.statusChecksDeleted = deletedCount;
      console.log(`✅ 状态检测记录清理完成: ${deletedCount} 条`);
      
    } catch (error) {
      console.error('❌ 清理状态检测记录失败:', error);
      this.stats.errors++;
    }
  }

  /**
   * 清理过期的通知日志
   */
  async cleanupNotificationLogs() {
    try {
      console.log(`📧 清理 ${this.config.retention.notificationLogs} 天前的通知日志...`);
      
      // 先统计要删除的记录数
      const [countResult] = await this.db.execute(`
        SELECT COUNT(*) as count 
        FROM notification_logs 
        WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
      `, [this.config.retention.notificationLogs]);
      
      const totalRecords = countResult[0].count;
      
      if (totalRecords === 0) {
        console.log('ℹ️  没有需要清理的通知日志');
        return;
      }
      
      console.log(`📊 找到 ${totalRecords} 条过期通知日志，开始批量删除...`);
      
      // 批量删除
      let deletedCount = 0;
      let batchCount = 0;
      
      while (deletedCount < totalRecords && batchCount < this.config.maxBatches) {
        const [result] = await this.db.execute(`
          DELETE FROM notification_logs 
          WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
          LIMIT ?
        `, [this.config.retention.notificationLogs, this.config.batchSize]);
        
        const batchDeleted = result.affectedRows;
        deletedCount += batchDeleted;
        batchCount++;
        
        console.log(`  批次 ${batchCount}: 删除 ${batchDeleted} 条记录`);
        
        if (batchDeleted === 0) break;
        
        // 批次间延迟
        if (batchDeleted === this.config.batchSize) {
          await this.sleep(this.config.batchDelay);
        }
      }
      
      this.stats.notificationLogsDeleted = deletedCount;
      console.log(`✅ 通知日志清理完成: ${deletedCount} 条`);
      
    } catch (error) {
      console.error('❌ 清理通知日志失败:', error);
      this.stats.errors++;
    }
  }

  /**
   * 清理系统日志
   */
  async cleanupSystemLogs() {
    try {
      console.log(`📝 清理 ${this.config.retention.systemLogs} 天前的系统日志...`);
      
      // 清理系统配置中的临时数据
      const [result] = await this.db.execute(`
        DELETE FROM monitor_system_configs 
        WHERE config_key LIKE 'temp_%' 
          AND updated_at < DATE_SUB(NOW(), INTERVAL ? DAY)
      `, [this.config.retention.systemLogs]);
      
      this.stats.systemLogsDeleted = result.affectedRows;
      console.log(`✅ 系统日志清理完成: ${result.affectedRows} 条`);
      
    } catch (error) {
      console.error('❌ 清理系统日志失败:', error);
      this.stats.errors++;
    }
  }

  /**
   * 清理备份文件
   */
  async cleanupBackupFiles() {
    try {
      console.log(`💾 清理 ${this.config.retention.backupFiles} 天前的备份文件...`);
      
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);
      
      // 清理数据库备份目录中的旧文件
      const backupDirs = [
        '/root/sitemanager/backend/database/backups',
        '/var/backups/sitemanager',
        '/tmp/sitemanager-backups'
      ];
      
      let totalDeleted = 0;
      
      for (const dir of backupDirs) {
        try {
          // 查找并删除过期的备份文件
          const { stdout } = await execAsync(`find ${dir} -name "*.sql" -mtime +${this.config.retention.backupFiles} -type f 2>/dev/null | wc -l`);
          const fileCount = parseInt(stdout.trim());
          
          if (fileCount > 0) {
            await execAsync(`find ${dir} -name "*.sql" -mtime +${this.config.retention.backupFiles} -type f -delete 2>/dev/null`);
            console.log(`  删除 ${dir} 中的 ${fileCount} 个备份文件`);
            totalDeleted += fileCount;
          }
        } catch (error) {
          // 忽略目录不存在的错误
          if (!error.message.includes('No such file or directory')) {
            console.warn(`⚠️  清理备份目录 ${dir} 失败:`, error.message);
          }
        }
      }
      
      this.stats.backupFilesDeleted = totalDeleted;
      console.log(`✅ 备份文件清理完成: ${totalDeleted} 个文件`);
      
    } catch (error) {
      console.error('❌ 清理备份文件失败:', error);
      this.stats.errors++;
    }
  }

  /**
   * 优化数据库表
   */
  async optimizeTables() {
    try {
      console.log('🔧 优化数据库表...');
      
      const tables = [
        'website_status_checks',
        'notification_logs',
        'websites',
        'monitor_system_configs'
      ];
      
      for (const table of tables) {
        try {
          console.log(`  优化表: ${table}`);
          await this.db.execute(`OPTIMIZE TABLE ${table}`);
        } catch (error) {
          console.warn(`⚠️  优化表 ${table} 失败:`, error.message);
        }
      }
      
      console.log('✅ 数据库表优化完成');
      
    } catch (error) {
      console.error('❌ 数据库表优化失败:', error);
      this.stats.errors++;
    }
  }

  /**
   * 记录清理结果
   */
  async recordCleanupResult() {
    try {
      const cleanupResult = {
        timestamp: new Date().toISOString(),
        stats: this.stats,
        config: this.config.retention
      };
      
      await this.db.execute(`
        INSERT INTO monitor_system_configs (config_key, config_value, config_type, description)
        VALUES ('last_cleanup_result', ?, 'json', '最后一次数据清理结果')
        ON DUPLICATE KEY UPDATE 
          config_value = VALUES(config_value),
          updated_at = NOW()
      `, [JSON.stringify(cleanupResult)]);
      
      console.log('✅ 清理结果已记录');
      
    } catch (error) {
      console.warn('⚠️  记录清理结果失败:', error.message);
    }
  }

  /**
   * 输出清理统计
   */
  printCleanupStats() {
    console.log('\n🧹 数据清理统计:');
    console.log(`  状态检测记录: ${this.stats.statusChecksDeleted} 条`);
    console.log(`  通知日志: ${this.stats.notificationLogsDeleted} 条`);
    console.log(`  系统日志: ${this.stats.systemLogsDeleted} 条`);
    console.log(`  备份文件: ${this.stats.backupFilesDeleted} 个`);
    
    this.stats.totalDeleted = this.stats.statusChecksDeleted + 
                              this.stats.notificationLogsDeleted + 
                              this.stats.systemLogsDeleted + 
                              this.stats.backupFilesDeleted;
    
    console.log(`  总计删除: ${this.stats.totalDeleted} 项`);
    
    if (this.stats.errors > 0) {
      console.log(`  错误数: ${this.stats.errors}`);
    }
  }

  /**
   * 工具方法
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.db) {
        await this.db.end();
        console.log('✅ 数据库连接已关闭');
      }
    } catch (error) {
      console.error('⚠️  清理资源失败:', error);
    }
  }

  /**
   * 主执行方法
   */
  async execute() {
    try {
      await this.initialize();
      await this.run();
    } catch (error) {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// 检查是否在CLI模式下运行
if (require.main === module) {
  const script = new CleanupOldRecordsScript();
  
  // 处理命令行参数
  if (process.argv.includes('--help')) {
    console.log(`
数据清理脚本

用法: node cleanup-old-records.js [选项]

选项:
  --dry-run   仅显示将要删除的记录数，不实际删除
  --help      显示帮助信息

示例:
  node cleanup-old-records.js
  node cleanup-old-records.js --dry-run
    `);
    process.exit(0);
  }
  
  // 处理进程信号
  process.on('SIGINT', async () => {
    console.log('\n🛑 接收到中断信号，正在清理...');
    await script.cleanup();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    console.log('\n🛑 接收到终止信号，正在清理...');
    await script.cleanup();
    process.exit(0);
  });
  
  // 执行脚本
  script.execute()
    .then(() => {
      console.log('🎉 数据清理脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 数据清理脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = CleanupOldRecordsScript;
