#!/usr/bin/env node

/**
 * 状态异常通知脚本
 * 参考PHP系统的status_notice.php
 * 检测异常网站并发送邮件通知
 */

const mysql = require('mysql2/promise');
const EnhancedNotificationService = require('../services/EnhancedNotificationService');

class StatusNotificationScript {
  constructor() {
    this.db = null;
    this.notificationService = null;
    this.config = {
      // 参考PHP系统配置
      alarmThreshold: 600,        // 10分钟异常触发报警
      notificationInterval: 1800, // 30分钟重复通知间隔
      minFailures: 5,             // 最少连续失败次数
      batchSize: 50               // 批处理大小
    };
  }

  /**
   * 初始化
   */
  async initialize() {
    try {
      console.log('🔧 初始化状态通知脚本...');
      
      // 创建数据库连接
      this.db = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'sitemanager',
        charset: 'utf8mb4'
      });
      
      console.log('✅ 数据库连接成功');
      
      // 初始化通知服务
      this.notificationService = new EnhancedNotificationService(this.db);
      await this.notificationService.initialize();
      
      console.log('✅ 通知服务初始化成功');
      
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行状态通知检查
   */
  async run() {
    try {
      console.log('🚀 开始执行状态异常通知检查...');
      console.log(`配置: 异常阈值=${this.config.alarmThreshold}秒, 通知间隔=${this.config.notificationInterval}秒`);
      
      const startTime = Date.now();
      
      // 发送状态异常通知
      await this.notificationService.sendStatusNotification();
      
      const duration = Date.now() - startTime;
      console.log(`✅ 状态异常通知检查完成，耗时: ${duration}ms`);
      
      // 输出统计信息
      const stats = this.notificationService.getStats();
      console.log('📊 通知统计:', {
        totalSent: stats.totalSent,
        successCount: stats.successCount,
        failureCount: stats.failureCount,
        successRate: stats.successRate
      });
      
    } catch (error) {
      console.error('❌ 状态通知检查失败:', error);
      throw error;
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.db) {
        await this.db.end();
        console.log('✅ 数据库连接已关闭');
      }
    } catch (error) {
      console.error('⚠️  清理资源失败:', error);
    }
  }

  /**
   * 主执行方法
   */
  async execute() {
    try {
      await this.initialize();
      await this.run();
    } catch (error) {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// 检查是否在CLI模式下运行
if (require.main === module) {
  const script = new StatusNotificationScript();
  
  // 处理进程信号
  process.on('SIGINT', async () => {
    console.log('\n🛑 接收到中断信号，正在清理...');
    await script.cleanup();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    console.log('\n🛑 接收到终止信号，正在清理...');
    await script.cleanup();
    process.exit(0);
  });
  
  // 执行脚本
  script.execute()
    .then(() => {
      console.log('🎉 状态通知脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 状态通知脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = StatusNotificationScript;
