#!/usr/bin/env node

/**
 * 权限数据初始化和标准化脚本
 * 功能：
 * 1. 标准化现有权限代码格式 (模块.资源.操作)
 * 2. 初始化默认角色权限配置
 * 3. 创建权限数据验证和修复功能
 * 4. 确保权限数据的一致性和完整性
 */

const mysql = require('mysql2/promise');
const crypto = require('crypto');

// 数据库配置
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'sitemanager',
  password: process.env.DB_PASSWORD || 'sitemanager123',
  database: process.env.DB_NAME || 'sitemanager',
  charset: 'utf8mb4'
};

// 标准权限定义 - 按照模块.资源.操作的格式
const STANDARD_PERMISSIONS = {
  // 用户管理模块
  user: {
    list: {
      view: { name: '查看用户列表', description: '查看系统中所有用户的列表' }
    },
    user: {
      create: { name: '创建用户', description: '创建新的系统用户' },
      view: { name: '查看用户详情', description: '查看用户的详细信息' },
      edit: { name: '编辑用户', description: '修改用户的基本信息' },
      delete: { name: '删除用户', description: '删除系统用户' }
    },
    role: {
      assign: { name: '分配角色', description: '为用户分配或修改角色' }
    },
    permission: {
      manage: { name: '管理用户权限', description: '管理用户的自定义权限' }
    }
  },

  // 网站管理模块
  site: {
    list: {
      view: { name: '查看网站列表', description: '查看系统中所有网站的列表' }
    },
    website: {
      create: { name: '创建网站', description: '添加新的网站记录' },
      view: { name: '查看网站详情', description: '查看网站的详细信息' },
      edit: { name: '编辑网站', description: '修改网站的基本信息' },
      delete: { name: '删除网站', description: '删除网站记录' },
      monitor: { name: '网站监控', description: '执行网站状态检测和监控' }
    },
    attachment: {
      upload: { name: '上传附件', description: '为网站上传附件文件' },
      download: { name: '下载附件', description: '下载网站相关附件' },
      delete: { name: '删除附件', description: '删除网站附件' }
    }
  },

  // 服务器管理模块
  server: {
    list: {
      view: { name: '查看服务器列表', description: '查看系统中所有服务器的列表' }
    },
    server: {
      create: { name: '创建服务器', description: '添加新的服务器记录' },
      view: { name: '查看服务器详情', description: '查看服务器的详细信息' },
      edit: { name: '编辑服务器', description: '修改服务器的基本信息' },
      delete: { name: '删除服务器', description: '删除服务器记录' },
      monitor: { name: '服务器监控', description: '查看服务器监控数据和状态' }
    }
  },

  // 项目管理模块
  project: {
    list: {
      view: { name: '查看项目列表', description: '查看系统中所有项目的列表' }
    },
    project: {
      create: { name: '创建项目', description: '创建新的项目记录' },
      view: { name: '查看项目详情', description: '查看项目的详细信息' },
      edit: { name: '编辑项目', description: '修改项目的基本信息' },
      delete: { name: '删除项目', description: '删除项目记录' }
    }
  },

  // 客户管理模块
  customer: {
    list: {
      view: { name: '查看客户列表', description: '查看系统中所有客户的列表' }
    },
    customer: {
      create: { name: '创建客户', description: '添加新的客户记录' },
      view: { name: '查看客户详情', description: '查看客户的详细信息' },
      edit: { name: '编辑客户', description: '修改客户的基本信息' },
      delete: { name: '删除客户', description: '删除客户记录' }
    }
  },

  // 系统管理模块
  system: {
    settings: {
      view: { name: '查看系统设置', description: '查看系统配置和设置' },
      edit: { name: '修改系统设置', description: '修改系统配置和设置' }
    },
    backup: {
      create: { name: '创建系统备份', description: '执行系统数据备份' },
      restore: { name: '恢复系统备份', description: '从备份恢复系统数据' }
    },
    audit: {
      view: { name: '查看审计日志', description: '查看系统操作审计日志' }
    },
    monitor: {
      view: { name: '查看系统监控', description: '查看系统运行状态和监控数据' }
    }
  }
};

// 角色权限配置
const ROLE_PERMISSIONS = {
  super_admin: 'all', // 超级管理员拥有所有权限
  admin: [
    // 用户管理权限（除了删除用户）
    'user.list.view', 'user.user.create', 'user.user.view', 'user.user.edit', 'user.role.assign', 'user.permission.manage',
    
    // 网站管理权限（全部）
    'site.list.view', 'site.website.create', 'site.website.view', 'site.website.edit', 'site.website.delete', 'site.website.monitor',
    'site.attachment.upload', 'site.attachment.download', 'site.attachment.delete',
    
    // 服务器管理权限（全部）
    'server.list.view', 'server.server.create', 'server.server.view', 'server.server.edit', 'server.server.delete', 'server.server.monitor',
    
    // 项目管理权限（全部）
    'project.list.view', 'project.project.create', 'project.project.view', 'project.project.edit', 'project.project.delete',
    
    // 客户管理权限（全部）
    'customer.list.view', 'customer.customer.create', 'customer.customer.view', 'customer.customer.edit', 'customer.customer.delete',
    
    // 系统管理权限（除了系统备份）
    'system.settings.view', 'system.audit.view', 'system.monitor.view'
  ],
  user: [
    // 基本查看权限
    'user.list.view', 'user.user.view',
    'site.list.view', 'site.website.view', 'site.website.monitor',
    'server.list.view', 'server.server.view', 'server.server.monitor',
    'project.list.view', 'project.project.view',
    'customer.list.view', 'customer.customer.view',
    'system.settings.view'
  ]
};

class PermissionDataInitializer {
  constructor() {
    this.db = null;
  }

  async connect() {
    try {
      this.db = await mysql.createConnection(DB_CONFIG);
      console.log('✅ 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.db) {
      await this.db.end();
      console.log('✅ 数据库连接已关闭');
    }
  }

  /**
   * 生成标准化的权限代码
   */
  generatePermissionCode(module, resource, action) {
    return `${module}.${resource}.${action}`;
  }

  /**
   * 获取所有标准权限列表
   */
  getAllStandardPermissions() {
    const permissions = [];
    
    for (const [module, resources] of Object.entries(STANDARD_PERMISSIONS)) {
      for (const [resource, actions] of Object.entries(resources)) {
        for (const [action, config] of Object.entries(actions)) {
          const code = this.generatePermissionCode(module, resource, action);
          permissions.push({
            code,
            name: config.name,
            description: config.description,
            module,
            resource,
            action
          });
        }
      }
    }
    
    return permissions;
  }

  /**
   * 初始化权限数据
   */
  async initializePermissions() {
    console.log('🔄 开始初始化权限数据...');
    
    const standardPermissions = this.getAllStandardPermissions();
    
    for (const permission of standardPermissions) {
      try {
        // 检查权限是否已存在
        const [existing] = await this.db.execute(
          'SELECT id FROM permissions WHERE code = ?',
          [permission.code]
        );
        
        if (existing.length === 0) {
          // 插入新权限
          await this.db.execute(
            `INSERT INTO permissions (name, code, description, module, resource, action, created_at) 
             VALUES (?, ?, ?, ?, ?, ?, NOW())`,
            [permission.name, permission.code, permission.description, permission.module, permission.resource, permission.action]
          );
          console.log(`✅ 添加权限: ${permission.code} - ${permission.name}`);
        } else {
          // 更新现有权限
          await this.db.execute(
            `UPDATE permissions 
             SET name = ?, description = ?, module = ?, resource = ?, action = ?, updated_at = NOW()
             WHERE code = ?`,
            [permission.name, permission.description, permission.module, permission.resource, permission.action, permission.code]
          );
          console.log(`🔄 更新权限: ${permission.code} - ${permission.name}`);
        }
      } catch (error) {
        console.error(`❌ 处理权限失败 ${permission.code}:`, error.message);
      }
    }
    
    console.log('✅ 权限数据初始化完成');
  }

  /**
   * 初始化角色权限配置
   */
  async initializeRolePermissions() {
    console.log('🔄 开始初始化角色权限配置...');
    
    for (const [role, permissions] of Object.entries(ROLE_PERMISSIONS)) {
      try {
        // 清除现有角色权限
        await this.db.execute('DELETE FROM role_permissions WHERE role = ?', [role]);
        
        let permissionCodes = [];
        
        if (permissions === 'all') {
          // 超级管理员获取所有权限
          const [allPermissions] = await this.db.execute('SELECT code FROM permissions');
          permissionCodes = allPermissions.map(p => p.code);
        } else {
          permissionCodes = permissions;
        }
        
        // 插入角色权限
        for (const permissionCode of permissionCodes) {
          // 验证权限是否存在
          const [permissionExists] = await this.db.execute(
            'SELECT id FROM permissions WHERE code = ?',
            [permissionCode]
          );
          
          if (permissionExists.length > 0) {
            await this.db.execute(
              'INSERT INTO role_permissions (role, permission_code, created_at) VALUES (?, ?, NOW())',
              [role, permissionCode]
            );
          } else {
            console.warn(`⚠️  权限不存在: ${permissionCode}`);
          }
        }
        
        console.log(`✅ 角色 ${role} 权限配置完成，共 ${permissionCodes.length} 个权限`);
      } catch (error) {
        console.error(`❌ 配置角色权限失败 ${role}:`, error.message);
      }
    }
    
    console.log('✅ 角色权限配置初始化完成');
  }

  /**
   * 验证权限数据完整性
   */
  async validatePermissionData() {
    console.log('🔄 开始验证权限数据完整性...');
    
    const issues = [];
    
    // 检查权限表
    const [permissions] = await this.db.execute('SELECT * FROM permissions');
    console.log(`📊 权限表记录数: ${permissions.length}`);
    
    // 检查角色权限表
    const [rolePermissions] = await this.db.execute('SELECT * FROM role_permissions');
    console.log(`📊 角色权限表记录数: ${rolePermissions.length}`);
    
    // 检查是否有无效的权限引用
    const [invalidRolePermissions] = await this.db.execute(`
      SELECT rp.role, rp.permission_code 
      FROM role_permissions rp 
      LEFT JOIN permissions p ON rp.permission_code = p.code 
      WHERE p.code IS NULL
    `);
    
    if (invalidRolePermissions.length > 0) {
      issues.push(`发现 ${invalidRolePermissions.length} 个无效的角色权限引用`);
      invalidRolePermissions.forEach(item => {
        console.warn(`⚠️  无效权限引用: 角色 ${item.role} -> 权限 ${item.permission_code}`);
      });
    }
    
    // 检查每个角色的权限数量
    const [roleStats] = await this.db.execute(`
      SELECT role, COUNT(*) as permission_count 
      FROM role_permissions 
      GROUP BY role
    `);
    
    console.log('📊 角色权限统计:');
    roleStats.forEach(stat => {
      console.log(`   ${stat.role}: ${stat.permission_count} 个权限`);
    });
    
    if (issues.length === 0) {
      console.log('✅ 权限数据验证通过，没有发现问题');
    } else {
      console.log('⚠️  权限数据验证发现以下问题:');
      issues.forEach(issue => console.log(`   - ${issue}`));
    }
    
    return issues;
  }

  /**
   * 修复权限数据问题
   */
  async repairPermissionData() {
    console.log('🔄 开始修复权限数据问题...');
    
    // 删除无效的角色权限引用
    const [result] = await this.db.execute(`
      DELETE rp FROM role_permissions rp 
      LEFT JOIN permissions p ON rp.permission_code = p.code 
      WHERE p.code IS NULL
    `);
    
    if (result.affectedRows > 0) {
      console.log(`✅ 删除了 ${result.affectedRows} 个无效的角色权限引用`);
    }
    
    // 清理权限缓存
    await this.db.execute('DELETE FROM permission_cache WHERE expires_at < NOW()');
    console.log('✅ 清理了过期的权限缓存');
    
    console.log('✅ 权限数据修复完成');
  }

  /**
   * 创建权限配置备份
   */
  async createPermissionBackup() {
    console.log('🔄 创建权限配置备份...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupData = {
      timestamp,
      permissions: [],
      rolePermissions: []
    };
    
    // 备份权限数据
    const [permissions] = await this.db.execute('SELECT * FROM permissions');
    backupData.permissions = permissions;
    
    // 备份角色权限数据
    const [rolePermissions] = await this.db.execute('SELECT * FROM role_permissions');
    backupData.rolePermissions = rolePermissions;
    
    // 保存到权限配置表
    await this.db.execute(
      'INSERT INTO permission_configs (config_key, config_value, description, created_at) VALUES (?, ?, ?, NOW())',
      [`backup_${timestamp}`, JSON.stringify(backupData), '权限配置备份']
    );
    
    console.log(`✅ 权限配置备份已创建: backup_${timestamp}`);
    return `backup_${timestamp}`;
  }

  /**
   * 生成权限报告
   */
  async generatePermissionReport() {
    console.log('📊 生成权限系统报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {},
      details: {}
    };
    
    // 权限统计
    const [permissionStats] = await this.db.execute(`
      SELECT 
        module,
        COUNT(*) as total_permissions,
        COUNT(DISTINCT resource) as total_resources,
        COUNT(DISTINCT action) as total_actions
      FROM permissions 
      GROUP BY module
    `);
    
    report.summary.permissionsByModule = permissionStats;
    
    // 角色统计
    const [roleStats] = await this.db.execute(`
      SELECT 
        role,
        COUNT(*) as permission_count
      FROM role_permissions 
      GROUP BY role
    `);
    
    report.summary.permissionsByRole = roleStats;
    
    // 用户统计
    const [userStats] = await this.db.execute(`
      SELECT 
        role,
        COUNT(*) as user_count
      FROM users 
      GROUP BY role
    `);
    
    report.summary.usersByRole = userStats;
    
    console.log('📊 权限系统报告:');
    console.log('   模块权限分布:');
    permissionStats.forEach(stat => {
      console.log(`     ${stat.module}: ${stat.total_permissions} 个权限 (${stat.total_resources} 资源, ${stat.total_actions} 操作)`);
    });
    
    console.log('   角色权限分布:');
    roleStats.forEach(stat => {
      console.log(`     ${stat.role}: ${stat.permission_count} 个权限`);
    });
    
    console.log('   用户角色分布:');
    userStats.forEach(stat => {
      console.log(`     ${stat.role}: ${stat.user_count} 个用户`);
    });
    
    return report;
  }

  /**
   * 主执行函数
   */
  async run() {
    try {
      await this.connect();
      
      console.log('🚀 开始权限数据初始化和标准化...');
      
      // 1. 创建备份
      await this.createPermissionBackup();
      
      // 2. 初始化权限数据
      await this.initializePermissions();
      
      // 3. 初始化角色权限配置
      await this.initializeRolePermissions();
      
      // 4. 验证数据完整性
      const issues = await this.validatePermissionData();
      
      // 5. 修复发现的问题
      if (issues.length > 0) {
        await this.repairPermissionData();
        await this.validatePermissionData(); // 再次验证
      }
      
      // 6. 生成报告
      await this.generatePermissionReport();
      
      console.log('✅ 权限数据初始化和标准化完成！');
      
    } catch (error) {
      console.error('❌ 权限数据初始化失败:', error);
      throw error;
    } finally {
      await this.disconnect();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const initializer = new PermissionDataInitializer();
  initializer.run().catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = PermissionDataInitializer;