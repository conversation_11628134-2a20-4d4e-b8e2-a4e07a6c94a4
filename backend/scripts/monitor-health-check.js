#!/usr/bin/env node

/**
 * 监控健康检查脚本
 * 检查监控服务状态，如果停止则重启
 * 确保监控系统持续运行
 */

const mysql = require('mysql2/promise');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

class MonitorHealthCheckScript {
  constructor() {
    this.db = null;
    this.config = {
      serviceName: 'sitemanager-monitoring',
      maxRestartAttempts: 3,
      restartDelay: 5000,
      healthCheckTimeout: 10000
    };
    
    this.stats = {
      checkTime: new Date(),
      serviceStatus: 'unknown',
      databaseStatus: 'unknown',
      restartAttempts: 0,
      lastRestartTime: null
    };
  }

  /**
   * 初始化
   */
  async initialize() {
    try {
      console.log('🔧 初始化监控健康检查脚本...');
      
      // 创建数据库连接
      this.db = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'sitemanager',
        charset: 'utf8mb4'
      });
      
      console.log('✅ 数据库连接成功');
      
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行健康检查
   */
  async run() {
    try {
      console.log('🏥 开始监控健康检查...');
      
      const startTime = Date.now();
      
      // 1. 检查数据库连接
      const dbHealth = await this.checkDatabase();
      this.stats.databaseStatus = dbHealth.healthy ? 'healthy' : 'unhealthy';
      
      // 2. 检查监控服务状态
      const serviceHealth = await this.checkMonitoringService();
      this.stats.serviceStatus = serviceHealth.healthy ? 'healthy' : 'unhealthy';
      
      // 3. 检查系统资源
      const systemHealth = await this.checkSystemResources();
      
      // 4. 记录健康检查结果
      await this.recordHealthCheck({
        database: dbHealth,
        service: serviceHealth,
        system: systemHealth
      });
      
      // 5. 如果服务不健康，尝试修复
      if (!serviceHealth.healthy) {
        await this.attemptServiceRecovery();
      }
      
      const duration = Date.now() - startTime;
      console.log(`✅ 监控健康检查完成，耗时: ${duration}ms`);
      
      // 输出健康状态
      this.printHealthStatus({
        database: dbHealth,
        service: serviceHealth,
        system: systemHealth
      });
      
    } catch (error) {
      console.error('❌ 监控健康检查失败:', error);
      throw error;
    }
  }

  /**
   * 检查数据库连接
   */
  async checkDatabase() {
    try {
      console.log('🗄️  检查数据库连接...');
      
      // 执行简单查询测试连接
      await this.db.execute('SELECT 1');
      
      // 检查关键表是否存在
      const [tables] = await this.db.execute(`
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = ? 
          AND TABLE_NAME IN ('websites', 'website_status_checks', 'monitor_system_configs')
      `, [process.env.DB_NAME || 'sitemanager']);
      
      const requiredTables = ['websites', 'website_status_checks', 'monitor_system_configs'];
      const existingTables = tables.map(t => t.TABLE_NAME);
      const missingTables = requiredTables.filter(t => !existingTables.includes(t));
      
      if (missingTables.length > 0) {
        return {
          healthy: false,
          error: `缺少必需的表: ${missingTables.join(', ')}`,
          details: { missingTables }
        };
      }
      
      // 检查最近的监控活动
      const [recentChecks] = await this.db.execute(`
        SELECT COUNT(*) as count 
        FROM website_status_checks 
        WHERE check_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
      `);
      
      return {
        healthy: true,
        message: '数据库连接正常',
        details: {
          tablesExist: existingTables.length,
          recentChecks: recentChecks[0].count
        }
      };
      
    } catch (error) {
      return {
        healthy: false,
        error: error.message
      };
    }
  }

  /**
   * 检查监控服务状态
   */
  async checkMonitoringService() {
    try {
      console.log('🔍 检查监控服务状态...');
      
      // 检查进程是否运行
      const processCheck = await this.checkServiceProcess();
      
      // 检查服务响应
      const responseCheck = await this.checkServiceResponse();
      
      // 检查最近的监控活动
      const activityCheck = await this.checkMonitoringActivity();
      
      const healthy = processCheck.healthy && responseCheck.healthy && activityCheck.healthy;
      
      return {
        healthy,
        message: healthy ? '监控服务运行正常' : '监控服务存在问题',
        details: {
          process: processCheck,
          response: responseCheck,
          activity: activityCheck
        }
      };
      
    } catch (error) {
      return {
        healthy: false,
        error: error.message
      };
    }
  }

  /**
   * 检查服务进程
   */
  async checkServiceProcess() {
    try {
      // 检查Node.js进程
      const { stdout } = await execAsync('pgrep -f "node.*monitoring"');
      const processes = stdout.trim().split('\n').filter(pid => pid);
      
      return {
        healthy: processes.length > 0,
        message: `找到 ${processes.length} 个监控进程`,
        details: { processes }
      };
      
    } catch (error) {
      return {
        healthy: false,
        error: '未找到监控进程',
        details: { error: error.message }
      };
    }
  }

  /**
   * 检查服务响应
   */
  async checkServiceResponse() {
    try {
      // 这里可以添加HTTP健康检查端点的调用
      // 暂时通过检查日志文件来判断服务是否活跃
      
      const { stdout } = await execAsync('find /var/log/sitemanager -name "*.log" -mmin -10 2>/dev/null | wc -l');
      const recentLogs = parseInt(stdout.trim());
      
      return {
        healthy: recentLogs > 0,
        message: recentLogs > 0 ? '服务响应正常' : '服务可能无响应',
        details: { recentLogs }
      };
      
    } catch (error) {
      return {
        healthy: false,
        error: error.message
      };
    }
  }

  /**
   * 检查监控活动
   */
  async checkMonitoringActivity() {
    try {
      // 检查最近的监控检查记录
      const [recentChecks] = await this.db.execute(`
        SELECT 
          COUNT(*) as total_checks,
          COUNT(DISTINCT website_id) as websites_checked,
          MAX(check_time) as last_check_time
        FROM website_status_checks 
        WHERE check_time > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
      `);
      
      const activity = recentChecks[0];
      const healthy = activity.total_checks > 0;
      
      return {
        healthy,
        message: healthy ? '监控活动正常' : '监控活动异常',
        details: {
          totalChecks: activity.total_checks,
          websitesChecked: activity.websites_checked,
          lastCheckTime: activity.last_check_time
        }
      };
      
    } catch (error) {
      return {
        healthy: false,
        error: error.message
      };
    }
  }

  /**
   * 检查系统资源
   */
  async checkSystemResources() {
    try {
      console.log('💻 检查系统资源...');
      
      // 检查内存使用情况
      const { stdout: memInfo } = await execAsync('free -m');
      const memLines = memInfo.split('\n');
      const memData = memLines[1].split(/\s+/);
      const totalMem = parseInt(memData[1]);
      const usedMem = parseInt(memData[2]);
      const memUsage = (usedMem / totalMem * 100).toFixed(2);
      
      // 检查磁盘使用情况
      const { stdout: diskInfo } = await execAsync('df -h / | tail -1');
      const diskData = diskInfo.split(/\s+/);
      const diskUsage = parseFloat(diskData[4].replace('%', ''));
      
      // 检查CPU负载
      const { stdout: loadInfo } = await execAsync('uptime');
      const loadMatch = loadInfo.match(/load average: ([\d.]+)/);
      const loadAverage = loadMatch ? parseFloat(loadMatch[1]) : 0;
      
      const healthy = memUsage < 90 && diskUsage < 90 && loadAverage < 5;
      
      return {
        healthy,
        message: healthy ? '系统资源正常' : '系统资源紧张',
        details: {
          memory: {
            total: totalMem,
            used: usedMem,
            usage: `${memUsage}%`
          },
          disk: {
            usage: `${diskUsage}%`
          },
          load: {
            average: loadAverage
          }
        }
      };
      
    } catch (error) {
      return {
        healthy: false,
        error: error.message
      };
    }
  }

  /**
   * 尝试服务恢复
   */
  async attemptServiceRecovery() {
    try {
      console.log('🔧 尝试恢复监控服务...');
      
      if (this.stats.restartAttempts >= this.config.maxRestartAttempts) {
        console.log('⚠️  已达到最大重启尝试次数，跳过重启');
        return;
      }
      
      this.stats.restartAttempts++;
      this.stats.lastRestartTime = new Date();
      
      // 尝试重启监控服务
      console.log(`🔄 第 ${this.stats.restartAttempts} 次尝试重启监控服务...`);
      
      // 这里可以添加具体的服务重启逻辑
      // 例如：systemctl restart sitemanager-monitoring
      // 或者：pm2 restart monitoring-service
      
      try {
        await execAsync('systemctl restart sitemanager-monitoring');
        console.log('✅ 监控服务重启成功');
      } catch (error) {
        console.log('⚠️  systemctl重启失败，尝试其他方式...');
        
        // 尝试使用pm2重启
        try {
          await execAsync('pm2 restart monitoring-service');
          console.log('✅ 使用pm2重启监控服务成功');
        } catch (pm2Error) {
          console.log('⚠️  pm2重启也失败，尝试直接启动...');
          
          // 尝试直接启动监控脚本
          try {
            const scriptPath = '/root/sitemanager/backend/scripts/start-monitoring.js';
            execAsync(`nohup node ${scriptPath} > /var/log/sitemanager/monitoring.log 2>&1 &`);
            console.log('✅ 直接启动监控脚本');
          } catch (directError) {
            console.error('❌ 所有重启方式都失败:', directError.message);
          }
        }
      }
      
      // 等待服务启动
      await this.sleep(this.config.restartDelay);
      
      // 再次检查服务状态
      const serviceHealth = await this.checkMonitoringService();
      if (serviceHealth.healthy) {
        console.log('🎉 监控服务恢复成功');
        this.stats.restartAttempts = 0; // 重置重启计数
      } else {
        console.log('❌ 监控服务恢复失败');
      }
      
    } catch (error) {
      console.error('❌ 服务恢复失败:', error);
    }
  }

  /**
   * 记录健康检查结果
   */
  async recordHealthCheck(results) {
    try {
      // 将健康检查结果记录到数据库
      await this.db.execute(`
        INSERT INTO monitor_system_configs (config_key, config_value, config_type, description)
        VALUES ('last_health_check', ?, 'json', '最后一次健康检查结果')
        ON DUPLICATE KEY UPDATE 
          config_value = VALUES(config_value),
          updated_at = NOW()
      `, [JSON.stringify({
        timestamp: new Date().toISOString(),
        results,
        stats: this.stats
      })]);
      
    } catch (error) {
      console.warn('⚠️  记录健康检查结果失败:', error.message);
    }
  }

  /**
   * 输出健康状态
   */
  printHealthStatus(results) {
    console.log('\n🏥 监控系统健康状态:');
    console.log(`  数据库: ${results.database.healthy ? '✅ 正常' : '❌ 异常'}`);
    console.log(`  监控服务: ${results.service.healthy ? '✅ 正常' : '❌ 异常'}`);
    console.log(`  系统资源: ${results.system.healthy ? '✅ 正常' : '⚠️  紧张'}`);
    
    if (this.stats.restartAttempts > 0) {
      console.log(`  重启尝试: ${this.stats.restartAttempts}/${this.config.maxRestartAttempts}`);
      if (this.stats.lastRestartTime) {
        console.log(`  最后重启: ${this.stats.lastRestartTime.toLocaleString('zh-CN')}`);
      }
    }
    
    // 输出详细信息
    if (!results.database.healthy) {
      console.log(`    数据库错误: ${results.database.error}`);
    }
    
    if (!results.service.healthy) {
      console.log(`    服务错误: ${results.service.error || '服务状态异常'}`);
    }
    
    if (!results.system.healthy && results.system.details) {
      const details = results.system.details;
      console.log(`    系统状态: 内存${details.memory?.usage} 磁盘${details.disk?.usage} 负载${details.load?.average}`);
    }
  }

  /**
   * 工具方法
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.db) {
        await this.db.end();
        console.log('✅ 数据库连接已关闭');
      }
    } catch (error) {
      console.error('⚠️  清理资源失败:', error);
    }
  }

  /**
   * 主执行方法
   */
  async execute() {
    try {
      await this.initialize();
      await this.run();
    } catch (error) {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// 检查是否在CLI模式下运行
if (require.main === module) {
  const script = new MonitorHealthCheckScript();
  
  // 处理进程信号
  process.on('SIGINT', async () => {
    console.log('\n🛑 接收到中断信号，正在清理...');
    await script.cleanup();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    console.log('\n🛑 接收到终止信号，正在清理...');
    await script.cleanup();
    process.exit(0);
  });
  
  // 执行脚本
  script.execute()
    .then(() => {
      console.log('🎉 监控健康检查脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 监控健康检查脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = MonitorHealthCheckScript;
