#!/usr/bin/env node

/**
 * 部署验证脚本
 * 验证监控系统部署是否正确，检查所有组件是否正常工作
 */

const mysql = require('mysql2/promise');
const { exec } = require('child_process');
const { promisify } = require('util');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');

const execAsync = promisify(exec);

class DeploymentVerification {
  constructor() {
    this.db = null;
    this.checks = {
      environment: [],
      database: [],
      files: [],
      services: [],
      api: [],
      permissions: []
    };
    
    this.config = {
      baseUrl: process.env.API_BASE_URL || 'http://localhost:3001',
      dbConfig: {
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'sitemanager'
      }
    };
  }

  /**
   * 记录检查结果
   */
  recordCheck(category, name, passed, details = null, suggestion = null) {
    const result = {
      name,
      passed,
      details,
      suggestion,
      timestamp: new Date().toISOString()
    };
    
    this.checks[category].push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`  ${status} ${name}`);
    
    if (!passed && suggestion) {
      console.log(`    💡 建议: ${suggestion}`);
    }
    
    if (details) {
      console.log(`    📝 详情: ${details}`);
    }
  }

  /**
   * 检查环境配置
   */
  async checkEnvironment() {
    console.log('\n🌍 检查环境配置...');
    
    // 检查Node.js版本
    try {
      const { stdout } = await execAsync('node --version');
      const version = stdout.trim();
      const majorVersion = parseInt(version.replace('v', '').split('.')[0]);
      
      if (majorVersion >= 16) {
        this.recordCheck('environment', 'Node.js版本', true, version);
      } else {
        this.recordCheck('environment', 'Node.js版本', false, version, '建议使用Node.js 16或更高版本');
      }
    } catch (error) {
      this.recordCheck('environment', 'Node.js版本', false, error.message, '请安装Node.js');
    }
    
    // 检查npm版本
    try {
      const { stdout } = await execAsync('npm --version');
      this.recordCheck('environment', 'npm版本', true, stdout.trim());
    } catch (error) {
      this.recordCheck('environment', 'npm版本', false, error.message, '请安装npm');
    }
    
    // 检查环境变量
    const requiredEnvVars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length === 0) {
      this.recordCheck('environment', '环境变量配置', true, '所有必需的环境变量已设置');
    } else {
      this.recordCheck('environment', '环境变量配置', false, 
        `缺少环境变量: ${missingVars.join(', ')}`, 
        '请设置所有必需的环境变量');
    }
    
    // 检查系统资源
    try {
      const { stdout: memInfo } = await execAsync('free -m');
      const memLines = memInfo.split('\n');
      const memData = memLines[1].split(/\s+/);
      const totalMem = parseInt(memData[1]);
      
      if (totalMem >= 1024) {
        this.recordCheck('environment', '系统内存', true, `${totalMem}MB`);
      } else {
        this.recordCheck('environment', '系统内存', false, `${totalMem}MB`, '建议至少1GB内存');
      }
    } catch (error) {
      this.recordCheck('environment', '系统内存', false, error.message);
    }
  }

  /**
   * 检查数据库连接和结构
   */
  async checkDatabase() {
    console.log('\n🗄️  检查数据库...');
    
    // 检查数据库连接
    try {
      this.db = await mysql.createConnection(this.config.dbConfig);
      await this.db.execute('SELECT 1');
      this.recordCheck('database', '数据库连接', true, '连接成功');
    } catch (error) {
      this.recordCheck('database', '数据库连接', false, error.message, '检查数据库配置和服务状态');
      return;
    }
    
    // 检查数据库版本
    try {
      const [rows] = await this.db.execute('SELECT VERSION() as version');
      const version = rows[0].version;
      this.recordCheck('database', 'MySQL版本', true, version);
    } catch (error) {
      this.recordCheck('database', 'MySQL版本', false, error.message);
    }
    
    // 检查必需的表
    const requiredTables = [
      'websites', 'website_status_checks', 'website_permissions',
      'website_monitor_configs', 'notification_logs', 'monitor_system_configs'
    ];
    
    for (const table of requiredTables) {
      try {
        const [rows] = await this.db.execute(
          'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = ? AND table_name = ?',
          [this.config.dbConfig.database, table]
        );
        
        if (rows[0].count > 0) {
          this.recordCheck('database', `表 ${table}`, true, '表存在');
        } else {
          this.recordCheck('database', `表 ${table}`, false, '表不存在', '运行数据库迁移脚本');
        }
      } catch (error) {
        this.recordCheck('database', `表 ${table}`, false, error.message);
      }
    }
    
    // 检查存储过程
    const requiredProcedures = ['GetSitesToCheck', 'UpdateWebsiteStatusStats', 'GetFailedSitesForNotification'];
    
    for (const proc of requiredProcedures) {
      try {
        const [rows] = await this.db.execute(
          'SELECT COUNT(*) as count FROM information_schema.routines WHERE routine_schema = ? AND routine_name = ?',
          [this.config.dbConfig.database, proc]
        );
        
        if (rows[0].count > 0) {
          this.recordCheck('database', `存储过程 ${proc}`, true, '存储过程存在');
        } else {
          this.recordCheck('database', `存储过程 ${proc}`, false, '存储过程不存在', '运行数据库迁移脚本');
        }
      } catch (error) {
        this.recordCheck('database', `存储过程 ${proc}`, false, error.message);
      }
    }
  }

  /**
   * 检查文件和目录
   */
  async checkFiles() {
    console.log('\n📁 检查文件和目录...');
    
    const requiredFiles = [
      'backend/src/app.js',
      'backend/src/routes/monitor-api.ts',
      'backend/src/controllers/monitor-api.ts',
      'backend/services/EnhancedMonitoringService.js',
      'backend/services/EnhancedNotificationService.js',
      'backend/services/MonitoringServiceManager.js',
      'backend/scripts/status-notification.js',
      'backend/scripts/renewal-notification.js',
      'backend/scripts/update-server-info.js',
      'backend/scripts/monitor-health-check.js',
      'backend/scripts/cleanup-old-records.js',
      'backend/scripts/script-manager.js'
    ];
    
    for (const file of requiredFiles) {
      try {
        const filePath = path.join(process.cwd(), '..', file);
        await fs.access(filePath);
        this.recordCheck('files', `文件 ${file}`, true, '文件存在');
      } catch (error) {
        this.recordCheck('files', `文件 ${file}`, false, '文件不存在', '检查文件路径或重新部署');
      }
    }
    
    // 检查日志目录
    const logDirs = ['/var/log/sitemanager', './logs'];
    
    for (const dir of logDirs) {
      try {
        await fs.access(dir);
        this.recordCheck('files', `日志目录 ${dir}`, true, '目录存在');
      } catch (error) {
        this.recordCheck('files', `日志目录 ${dir}`, false, '目录不存在', `创建目录: mkdir -p ${dir}`);
      }
    }
    
    // 检查配置文件
    const configFiles = [
      'backend/.env',
      'backend/package.json',
      'frontend/package.json'
    ];
    
    for (const file of configFiles) {
      try {
        const filePath = path.join(process.cwd(), '..', file);
        await fs.access(filePath);
        this.recordCheck('files', `配置文件 ${file}`, true, '文件存在');
      } catch (error) {
        this.recordCheck('files', `配置文件 ${file}`, false, '文件不存在', '检查配置文件');
      }
    }
  }

  /**
   * 检查服务状态
   */
  async checkServices() {
    console.log('\n🔧 检查服务状态...');
    
    // 检查MySQL服务
    try {
      await execAsync('systemctl is-active mysql');
      this.recordCheck('services', 'MySQL服务', true, '服务运行中');
    } catch (error) {
      try {
        await execAsync('systemctl is-active mysqld');
        this.recordCheck('services', 'MySQL服务', true, '服务运行中');
      } catch (error2) {
        this.recordCheck('services', 'MySQL服务', false, '服务未运行', 'systemctl start mysql');
      }
    }
    
    // 检查Nginx服务（如果使用）
    try {
      await execAsync('systemctl is-active nginx');
      this.recordCheck('services', 'Nginx服务', true, '服务运行中');
    } catch (error) {
      this.recordCheck('services', 'Nginx服务', false, '服务未运行或未安装', '如需要请启动Nginx服务');
    }
    
    // 检查端口占用
    const ports = [3001, 3000]; // 后端和前端端口
    
    for (const port of ports) {
      try {
        const { stdout } = await execAsync(`netstat -tlnp | grep :${port}`);
        if (stdout.trim()) {
          this.recordCheck('services', `端口 ${port}`, true, '端口被占用');
        } else {
          this.recordCheck('services', `端口 ${port}`, false, '端口未被占用', `启动相应服务监听端口${port}`);
        }
      } catch (error) {
        this.recordCheck('services', `端口 ${port}`, false, '无法检查端口状态');
      }
    }
  }

  /**
   * 检查API接口
   */
  async checkAPI() {
    console.log('\n📡 检查API接口...');
    
    // 检查API服务可达性
    try {
      const response = await axios.get(`${this.config.baseUrl}/api/health`, {
        timeout: 5000
      });
      
      if (response.status === 200) {
        this.recordCheck('api', 'API服务可达性', true, '服务响应正常');
      } else {
        this.recordCheck('api', 'API服务可达性', false, `HTTP状态码: ${response.status}`);
      }
    } catch (error) {
      this.recordCheck('api', 'API服务可达性', false, error.message, '检查API服务是否启动');
    }
    
    // 检查监控API端点
    const monitorEndpoints = [
      '/api/v1/monitor-api/sites-to-check',
      '/api/v1/monitor-api/stats',
      '/api/v1/monitor-api/config'
    ];
    
    for (const endpoint of monitorEndpoints) {
      try {
        const response = await axios.get(`${this.config.baseUrl}${endpoint}`, {
          timeout: 5000,
          validateStatus: () => true // 接受所有状态码
        });
        
        if (response.status === 401) {
          this.recordCheck('api', `API端点 ${endpoint}`, true, '需要认证（正常）');
        } else if (response.status === 200) {
          this.recordCheck('api', `API端点 ${endpoint}`, true, '响应正常');
        } else {
          this.recordCheck('api', `API端点 ${endpoint}`, false, `HTTP状态码: ${response.status}`);
        }
      } catch (error) {
        this.recordCheck('api', `API端点 ${endpoint}`, false, error.message);
      }
    }
  }

  /**
   * 检查文件权限
   */
  async checkPermissions() {
    console.log('\n🔐 检查文件权限...');
    
    const criticalPaths = [
      { path: '/var/log/sitemanager', type: 'directory', required: 'write' },
      { path: './logs', type: 'directory', required: 'write' },
      { path: 'backend/scripts', type: 'directory', required: 'execute' }
    ];
    
    for (const item of criticalPaths) {
      try {
        const stats = await fs.stat(item.path);
        
        if (item.type === 'directory' && stats.isDirectory()) {
          // 检查写权限
          if (item.required === 'write') {
            try {
              const testFile = path.join(item.path, '.write-test');
              await fs.writeFile(testFile, 'test');
              await fs.unlink(testFile);
              this.recordCheck('permissions', `${item.path} 写权限`, true, '权限正常');
            } catch (error) {
              this.recordCheck('permissions', `${item.path} 写权限`, false, '无写权限', `chmod 755 ${item.path}`);
            }
          }
        } else {
          this.recordCheck('permissions', `${item.path} 类型`, false, `期望${item.type}，实际${stats.isDirectory() ? 'directory' : 'file'}`);
        }
      } catch (error) {
        this.recordCheck('permissions', `${item.path} 访问`, false, error.message, `检查路径是否存在: ${item.path}`);
      }
    }
  }

  /**
   * 生成验证报告
   */
  generateReport() {
    console.log('\n📊 部署验证报告');
    console.log('='.repeat(60));
    
    let totalPassed = 0;
    let totalFailed = 0;
    let criticalIssues = 0;
    
    const categoryNames = {
      environment: '环境配置',
      database: '数据库',
      files: '文件系统',
      services: '系统服务',
      api: 'API接口',
      permissions: '文件权限'
    };
    
    Object.entries(this.checks).forEach(([category, checks]) => {
      const passed = checks.filter(c => c.passed).length;
      const failed = checks.filter(c => !c.passed).length;
      
      console.log(`\n${categoryNames[category]}:`);
      console.log(`  通过: ${passed}`);
      console.log(`  失败: ${failed}`);
      
      if (failed > 0) {
        console.log('  失败项目:');
        checks.filter(c => !c.passed).forEach(check => {
          console.log(`    ❌ ${check.name}`);
          if (check.suggestion) {
            console.log(`       💡 ${check.suggestion}`);
          }
        });
        
        // 关键问题计数
        if (['environment', 'database', 'services'].includes(category)) {
          criticalIssues += failed;
        }
      }
      
      totalPassed += passed;
      totalFailed += failed;
    });
    
    console.log('\n' + '='.repeat(60));
    console.log('总体结果:');
    console.log(`  通过: ${totalPassed}`);
    console.log(`  失败: ${totalFailed}`);
    console.log(`  关键问题: ${criticalIssues}`);
    console.log(`  成功率: ${totalPassed + totalFailed > 0 ? Math.round(totalPassed / (totalPassed + totalFailed) * 100) : 0}%`);
    
    if (totalFailed === 0) {
      console.log('\n🎉 部署验证通过！系统可以正常运行。');
    } else if (criticalIssues === 0) {
      console.log('\n⚠️  发现一些非关键问题，系统可以运行但建议修复。');
    } else {
      console.log('\n❌ 发现关键问题，请修复后再启动系统。');
    }
    
    return {
      passed: totalPassed,
      failed: totalFailed,
      critical: criticalIssues,
      canRun: criticalIssues === 0,
      details: this.checks
    };
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.db) {
        await this.db.end();
      }
    } catch (error) {
      console.error('清理资源失败:', error);
    }
  }

  /**
   * 运行所有验证
   */
  async runVerification() {
    console.log('🚀 开始部署验证');
    console.log('='.repeat(60));
    
    try {
      await this.checkEnvironment();
      await this.checkDatabase();
      await this.checkFiles();
      await this.checkServices();
      await this.checkAPI();
      await this.checkPermissions();
      
      const report = this.generateReport();
      
      return report;
      
    } catch (error) {
      console.error('💥 验证过程失败:', error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }
}

// 主程序入口
if (require.main === module) {
  const verification = new DeploymentVerification();
  
  verification.runVerification()
    .then((report) => {
      console.log('\n✅ 部署验证完成');
      process.exit(report.canRun ? 0 : 1);
    })
    .catch((error) => {
      console.error('💥 部署验证失败:', error);
      process.exit(1);
    });
}

module.exports = DeploymentVerification;
