#!/usr/bin/env node

/**
 * 权限系统数据迁移和兼容性测试脚本
 * 功能：
 * 1. 创建数据迁移脚本，保证现有用户权限不丢失
 * 2. 编写迁移前后数据一致性验证
 * 3. 创建回滚脚本以防迁移失败
 * 4. 在测试环境验证迁移流程
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// 数据库配置
const DB_CONFIG = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'sitemanager',
  password: process.env.DB_PASSWORD || 'sitemanager123',
  database: process.env.DB_NAME || 'sitemanager',
  charset: 'utf8mb4'
};

class PermissionMigrationTester {
  constructor() {
    this.db = null;
    this.backupData = {};
    this.migrationId = `migration_${Date.now()}`;
    this.backupDir = path.join(__dirname, '../backups');
    this.reportDir = path.join(__dirname, '../reports');
  }

  async connect() {
    try {
      this.db = await mysql.createConnection(DB_CONFIG);
      console.log('✅ 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      throw error;
    }
  }

  async disconnect() {
    if (this.db) {
      await this.db.end();
      console.log('✅ 数据库连接已关闭');
    }
  }

  /**
   * 创建备份和报告目录
   */
  async createDirectories() {
    try {
      await fs.mkdir(this.backupDir, { recursive: true });
      await fs.mkdir(this.reportDir, { recursive: true });
      console.log('✅ 备份和报告目录创建成功');
    } catch (error) {
      console.error('❌ 创建目录失败:', error.message);
      throw error;
    }
  }

  /**
   * 备份现有权限数据
   */
  async backupExistingData() {
    console.log('🔄 开始备份现有权限数据...');
    
    try {
      // 备份用户表
      const [users] = await this.db.execute('SELECT * FROM users');
      this.backupData.users = users;
      
      // 备份权限表（如果存在）
      try {
        const [permissions] = await this.db.execute('SELECT * FROM permissions');
        this.backupData.permissions = permissions;
      } catch (error) {
        console.log('⚠️  权限表不存在，将在迁移中创建');
        this.backupData.permissions = [];
      }
      
      // 备份角色权限表（如果存在）
      try {
        const [rolePermissions] = await this.db.execute('SELECT * FROM role_permissions');
        this.backupData.rolePermissions = rolePermissions;
      } catch (error) {
        console.log('⚠️  角色权限表不存在，将在迁移中创建');
        this.backupData.rolePermissions = [];
      }
      
      // 备份用户自定义权限表（如果存在）
      try {
        const [userCustomPermissions] = await this.db.execute('SELECT * FROM user_custom_permissions');
        this.backupData.userCustomPermissions = userCustomPermissions;
      } catch (error) {
        console.log('⚠️  用户自定义权限表不存在，将在迁移中创建');
        this.backupData.userCustomPermissions = [];
      }

      // 保存备份数据到文件
      const backupFile = path.join(this.backupDir, `pre_migration_${this.migrationId}.json`);
      await fs.writeFile(backupFile, JSON.stringify(this.backupData, null, 2));
      
      console.log(`✅ 数据备份完成: ${backupFile}`);
      console.log(`   - 用户数据: ${this.backupData.users.length} 条`);
      console.log(`   - 权限数据: ${this.backupData.permissions.length} 条`);
      console.log(`   - 角色权限数据: ${this.backupData.rolePermissions.length} 条`);
      console.log(`   - 用户自定义权限数据: ${this.backupData.userCustomPermissions.length} 条`);
      
    } catch (error) {
      console.error('❌ 备份数据失败:', error.message);
      throw error;
    }
  }

  /**
   * 执行数据库迁移
   */
  async executeMigration() {
    console.log('🔄 开始执行数据库迁移...');
    
    try {
      // 读取迁移脚本
      const migrationScript = path.join(__dirname, '../database/migrations/008_enhance_permission_system.sql');
      const migrationSQL = await fs.readFile(migrationScript, 'utf8');
      
      // 分割SQL语句（简单的分割，实际可能需要更复杂的解析）
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      console.log(`📝 准备执行 ${statements.length} 个SQL语句`);
      
      // 开始事务
      await this.db.execute('START TRANSACTION');
      
      try {
        let executedCount = 0;
        for (const statement of statements) {
          if (statement.trim()) {
            try {
              await this.db.execute(statement);
              executedCount++;
            } catch (error) {
              // 某些语句可能因为表已存在等原因失败，这是正常的
              if (!error.message.includes('already exists') && 
                  !error.message.includes('Duplicate column') &&
                  !error.message.includes('Duplicate key')) {
                console.warn(`⚠️  SQL语句执行警告: ${error.message}`);
                console.warn(`   语句: ${statement.substring(0, 100)}...`);
              }
            }
          }
        }
        
        // 提交事务
        await this.db.execute('COMMIT');
        console.log(`✅ 迁移执行完成，成功执行 ${executedCount} 个语句`);
        
      } catch (error) {
        // 回滚事务
        await this.db.execute('ROLLBACK');
        throw error;
      }
      
    } catch (error) {
      console.error('❌ 迁移执行失败:', error.message);
      throw error;
    }
  }

  /**
   * 验证迁移后的数据完整性
   */
  async validateMigration() {
    console.log('🔄 开始验证迁移后的数据完整性...');
    
    const validationResults = {
      success: true,
      issues: [],
      statistics: {}
    };

    try {
      // 验证用户数据完整性
      const [currentUsers] = await this.db.execute('SELECT * FROM users');
      if (currentUsers.length !== this.backupData.users.length) {
        validationResults.issues.push({
          type: 'data_loss',
          table: 'users',
          message: `用户数据数量不匹配: 备份 ${this.backupData.users.length}, 当前 ${currentUsers.length}`
        });
        validationResults.success = false;
      }

      // 验证用户角色数据
      for (const backupUser of this.backupData.users) {
        const currentUser = currentUsers.find(u => u.id === backupUser.id);
        if (!currentUser) {
          validationResults.issues.push({
            type: 'missing_user',
            userId: backupUser.id,
            message: `用户 ${backupUser.username} (ID: ${backupUser.id}) 在迁移后丢失`
          });
          validationResults.success = false;
        } else if (currentUser.role !== backupUser.role) {
          validationResults.issues.push({
            type: 'role_change',
            userId: backupUser.id,
            message: `用户 ${backupUser.username} 角色发生变化: ${backupUser.role} -> ${currentUser.role}`
          });
        }
      }

      // 验证新表结构
      const requiredTables = [
        'permissions',
        'role_permissions', 
        'user_custom_permissions',
        'audit_logs',
        'permission_cache',
        'role_templates',
        'permission_configs'
      ];

      for (const tableName of requiredTables) {
        try {
          const [tableInfo] = await this.db.execute(`DESCRIBE ${tableName}`);
          validationResults.statistics[tableName] = {
            exists: true,
            columns: tableInfo.length
          };
          console.log(`✅ 表 ${tableName} 存在，包含 ${tableInfo.length} 个字段`);
        } catch (error) {
          validationResults.issues.push({
            type: 'missing_table',
            table: tableName,
            message: `必需的表 ${tableName} 不存在`
          });
          validationResults.success = false;
        }
      }

      // 验证权限数据
      try {
        const [permissions] = await this.db.execute('SELECT COUNT(*) as count FROM permissions');
        const [rolePermissions] = await this.db.execute('SELECT COUNT(*) as count FROM role_permissions');
        
        validationResults.statistics.permissions = permissions[0].count;
        validationResults.statistics.rolePermissions = rolePermissions[0].count;
        
        console.log(`📊 权限数据统计:`);
        console.log(`   - 权限总数: ${permissions[0].count}`);
        console.log(`   - 角色权限总数: ${rolePermissions[0].count}`);
        
        if (permissions[0].count === 0) {
          validationResults.issues.push({
            type: 'no_permissions',
            message: '权限表为空，可能需要运行权限初始化脚本'
          });
        }
        
      } catch (error) {
        validationResults.issues.push({
          type: 'permission_validation_error',
          message: `权限数据验证失败: ${error.message}`
        });
        validationResults.success = false;
      }

      // 验证索引
      const requiredIndexes = [
        { table: 'users', index: 'idx_role' },
        { table: 'users', index: 'idx_username' },
        { table: 'permissions', index: 'idx_module' },
        { table: 'audit_logs', index: 'idx_user_id' },
        { table: 'audit_logs', index: 'idx_created_at' }
      ];

      for (const { table, index } of requiredIndexes) {
        try {
          const [indexes] = await this.db.execute(`SHOW INDEX FROM ${table} WHERE Key_name = ?`, [index]);
          if (indexes.length === 0) {
            validationResults.issues.push({
              type: 'missing_index',
              table,
              index,
              message: `表 ${table} 缺少索引 ${index}`
            });
          }
        } catch (error) {
          // 表不存在的情况已经在上面检查过了
        }
      }

      console.log(`${validationResults.success ? '✅' : '❌'} 数据完整性验证完成`);
      if (validationResults.issues.length > 0) {
        console.log(`⚠️  发现 ${validationResults.issues.length} 个问题:`);
        validationResults.issues.forEach((issue, index) => {
          console.log(`   ${index + 1}. [${issue.type}] ${issue.message}`);
        });
      }

      return validationResults;

    } catch (error) {
      console.error('❌ 数据完整性验证失败:', error.message);
      validationResults.success = false;
      validationResults.issues.push({
        type: 'validation_error',
        message: error.message
      });
      return validationResults;
    }
  }

  /**
   * 创建回滚脚本
   */
  async createRollbackScript() {
    console.log('🔄 创建回滚脚本...');
    
    try {
      const rollbackScript = `#!/usr/bin/env node

/**
 * 权限系统迁移回滚脚本
 * 迁移ID: ${this.migrationId}
 * 创建时间: ${new Date().toISOString()}
 */

const mysql = require('mysql2/promise');
const fs = require('fs').promises;
const path = require('path');

const DB_CONFIG = ${JSON.stringify(DB_CONFIG, null, 2)};

async function rollback() {
  let db = null;
  
  try {
    console.log('🔄 开始回滚迁移 ${this.migrationId}...');
    
    // 连接数据库
    db = await mysql.createConnection(DB_CONFIG);
    console.log('✅ 数据库连接成功');
    
    // 读取备份数据
    const backupFile = path.join(__dirname, '../backups/pre_migration_${this.migrationId}.json');
    const backupData = JSON.parse(await fs.readFile(backupFile, 'utf8'));
    
    // 开始事务
    await db.execute('START TRANSACTION');
    
    try {
      // 删除新创建的表（如果它们在备份中不存在）
      const tablesToDrop = [
        'permission_configs',
        'role_templates', 
        'permission_cache',
        'audit_logs'
      ];
      
      for (const table of tablesToDrop) {
        try {
          await db.execute(\`DROP TABLE IF EXISTS \${table}\`);
          console.log(\`✅ 删除表 \${table}\`);
        } catch (error) {
          console.warn(\`⚠️  删除表 \${table} 失败: \${error.message}\`);
        }
      }
      
      // 恢复用户数据（如果有变化）
      if (backupData.users && backupData.users.length > 0) {
        // 这里可以添加更复杂的用户数据恢复逻辑
        console.log('✅ 用户数据无需恢复');
      }
      
      // 提交事务
      await db.execute('COMMIT');
      console.log('✅ 回滚完成');
      
    } catch (error) {
      await db.execute('ROLLBACK');
      throw error;
    }
    
  } catch (error) {
    console.error('❌ 回滚失败:', error.message);
    process.exit(1);
  } finally {
    if (db) {
      await db.end();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  rollback().catch(error => {
    console.error('回滚脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = rollback;
`;

      const rollbackFile = path.join(__dirname, `rollback_${this.migrationId}.js`);
      await fs.writeFile(rollbackFile, rollbackScript);
      
      console.log(`✅ 回滚脚本创建完成: ${rollbackFile}`);
      
    } catch (error) {
      console.error('❌ 创建回滚脚本失败:', error.message);
      throw error;
    }
  }

  /**
   * 生成迁移报告
   */
  async generateMigrationReport(validationResults) {
    console.log('🔄 生成迁移报告...');
    
    try {
      const report = {
        migrationId: this.migrationId,
        timestamp: new Date().toISOString(),
        database: DB_CONFIG.database,
        
        // 迁移前数据统计
        preMigrationStats: {
          users: this.backupData.users.length,
          permissions: this.backupData.permissions.length,
          rolePermissions: this.backupData.rolePermissions.length,
          userCustomPermissions: this.backupData.userCustomPermissions.length
        },
        
        // 迁移后数据统计
        postMigrationStats: validationResults.statistics,
        
        // 验证结果
        validation: {
          success: validationResults.success,
          issueCount: validationResults.issues.length,
          issues: validationResults.issues
        },
        
        // 迁移摘要
        summary: {
          status: validationResults.success ? 'SUCCESS' : 'FAILED',
          dataIntegrity: validationResults.success ? 'PRESERVED' : 'COMPROMISED',
          backupLocation: path.join(this.backupDir, `pre_migration_${this.migrationId}.json`),
          rollbackScript: path.join(__dirname, `rollback_${this.migrationId}.js`)
        },
        
        // 建议
        recommendations: this.generateRecommendations(validationResults)
      };

      const reportFile = path.join(this.reportDir, `migration_report_${this.migrationId}.json`);
      await fs.writeFile(reportFile, JSON.stringify(report, null, 2));
      
      console.log(`✅ 迁移报告生成完成: ${reportFile}`);
      
      // 打印摘要
      console.log('\n📊 迁移摘要:');
      console.log(`   状态: ${report.summary.status}`);
      console.log(`   数据完整性: ${report.summary.dataIntegrity}`);
      console.log(`   问题数量: ${report.validation.issueCount}`);
      console.log(`   备份位置: ${report.summary.backupLocation}`);
      console.log(`   回滚脚本: ${report.summary.rollbackScript}`);
      
      if (report.recommendations.length > 0) {
        console.log('\n💡 建议:');
        report.recommendations.forEach((rec, index) => {
          console.log(`   ${index + 1}. ${rec}`);
        });
      }
      
      return report;
      
    } catch (error) {
      console.error('❌ 生成迁移报告失败:', error.message);
      throw error;
    }
  }

  /**
   * 生成建议
   */
  generateRecommendations(validationResults) {
    const recommendations = [];
    
    if (!validationResults.success) {
      recommendations.push('迁移存在问题，建议在生产环境部署前解决所有问题');
    }
    
    if (validationResults.statistics.permissions === 0) {
      recommendations.push('权限表为空，建议运行权限初始化脚本: node backend/scripts/permission-data-init.js');
    }
    
    const hasDataLoss = validationResults.issues.some(issue => issue.type === 'data_loss');
    if (hasDataLoss) {
      recommendations.push('检测到数据丢失，强烈建议使用回滚脚本恢复数据');
    }
    
    const hasMissingTables = validationResults.issues.some(issue => issue.type === 'missing_table');
    if (hasMissingTables) {
      recommendations.push('存在缺失的表，建议重新运行迁移脚本');
    }
    
    const hasMissingIndexes = validationResults.issues.some(issue => issue.type === 'missing_index');
    if (hasMissingIndexes) {
      recommendations.push('存在缺失的索引，可能影响查询性能，建议手动创建');
    }
    
    if (validationResults.success && validationResults.issues.length === 0) {
      recommendations.push('迁移成功完成，可以继续下一步的权限系统集成');
      recommendations.push('建议运行权限系统性能测试以验证系统性能');
    }
    
    return recommendations;
  }

  /**
   * 测试权限系统基本功能
   */
  async testBasicPermissionFunctions() {
    console.log('🔄 测试权限系统基本功能...');
    
    const testResults = {
      success: true,
      tests: []
    };

    try {
      // 测试1: 权限查询
      try {
        const [permissions] = await this.db.execute('SELECT * FROM permissions LIMIT 5');
        testResults.tests.push({
          name: '权限查询测试',
          status: 'PASS',
          message: `成功查询到 ${permissions.length} 个权限`
        });
      } catch (error) {
        testResults.tests.push({
          name: '权限查询测试',
          status: 'FAIL',
          message: error.message
        });
        testResults.success = false;
      }

      // 测试2: 角色权限查询
      try {
        const [rolePermissions] = await this.db.execute('SELECT * FROM role_permissions LIMIT 5');
        testResults.tests.push({
          name: '角色权限查询测试',
          status: 'PASS',
          message: `成功查询到 ${rolePermissions.length} 个角色权限`
        });
      } catch (error) {
        testResults.tests.push({
          name: '角色权限查询测试',
          status: 'FAIL',
          message: error.message
        });
        testResults.success = false;
      }

      // 测试3: 审计日志插入
      try {
        await this.db.execute(`
          INSERT INTO audit_logs (user_id, action, resource, result, details, created_at)
          VALUES (1, 'test_action', 'test_resource', 'success', '{"test": true}', NOW())
        `);
        testResults.tests.push({
          name: '审计日志插入测试',
          status: 'PASS',
          message: '成功插入测试审计日志'
        });
        
        // 清理测试数据
        await this.db.execute('DELETE FROM audit_logs WHERE action = "test_action"');
      } catch (error) {
        testResults.tests.push({
          name: '审计日志插入测试',
          status: 'FAIL',
          message: error.message
        });
        testResults.success = false;
      }

      // 测试4: 权限缓存表操作
      try {
        await this.db.execute(`
          INSERT INTO permission_cache (user_id, permissions_hash, permissions_data, expires_at)
          VALUES (1, 'test_hash', '{"test": true}', DATE_ADD(NOW(), INTERVAL 1 HOUR))
        `);
        testResults.tests.push({
          name: '权限缓存测试',
          status: 'PASS',
          message: '成功操作权限缓存表'
        });
        
        // 清理测试数据
        await this.db.execute('DELETE FROM permission_cache WHERE permissions_hash = "test_hash"');
      } catch (error) {
        testResults.tests.push({
          name: '权限缓存测试',
          status: 'FAIL',
          message: error.message
        });
        testResults.success = false;
      }

      console.log(`${testResults.success ? '✅' : '❌'} 基本功能测试完成`);
      testResults.tests.forEach(test => {
        const icon = test.status === 'PASS' ? '✅' : '❌';
        console.log(`   ${icon} ${test.name}: ${test.message}`);
      });

      return testResults;

    } catch (error) {
      console.error('❌ 基本功能测试失败:', error.message);
      testResults.success = false;
      return testResults;
    }
  }

  /**
   * 主执行函数
   */
  async run() {
    try {
      console.log('🚀 开始权限系统数据迁移和兼容性测试...');
      console.log(`📝 迁移ID: ${this.migrationId}`);
      
      // 1. 连接数据库
      await this.connect();
      
      // 2. 创建必要目录
      await this.createDirectories();
      
      // 3. 备份现有数据
      await this.backupExistingData();
      
      // 4. 执行数据库迁移
      await this.executeMigration();
      
      // 5. 验证迁移结果
      const validationResults = await this.validateMigration();
      
      // 6. 测试基本功能
      const testResults = await this.testBasicPermissionFunctions();
      
      // 7. 创建回滚脚本
      await this.createRollbackScript();
      
      // 8. 生成迁移报告
      const report = await this.generateMigrationReport({
        ...validationResults,
        functionTests: testResults
      });
      
      console.log('\n🎉 权限系统数据迁移和兼容性测试完成！');
      
      if (validationResults.success && testResults.success) {
        console.log('✅ 迁移成功，系统可以继续下一步集成');
        return { success: true, report };
      } else {
        console.log('⚠️  迁移存在问题，请查看报告并解决问题后重试');
        return { success: false, report };
      }
      
    } catch (error) {
      console.error('❌ 迁移测试失败:', error);
      throw error;
    } finally {
      await this.disconnect();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const tester = new PermissionMigrationTester();
  tester.run().catch(error => {
    console.error('迁移测试脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = PermissionMigrationTester;