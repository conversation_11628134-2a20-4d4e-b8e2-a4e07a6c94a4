#!/usr/bin/env node

const axios = require('axios');
const PermissionService = require('../services/PermissionService');
const PermissionConfigService = require('../services/PermissionConfigService');
const PermissionCacheService = require('../services/PermissionCacheService');
const AuditService = require('../services/AuditService');
const db = require('../utils/db');

/**
 * 权限系统集成测试脚本
 * 验证权限系统各组件之间的集成和端到端功能
 */

class SystemIntegrationTest {
  constructor() {
    this.baseURL = process.env.TEST_BASE_URL || 'http://localhost:3001';
    this.testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      testDetails: []
    };
    
    this.testUser = {
      id: 999,
      username: 'integration_test_user',
      role: 'admin'
    };
  }

  /**
   * 运行完整的集成测试
   */
  async runIntegrationTests() {
    console.log('🧪 开始权限系统集成测试');
    console.log(`测试基础URL: ${this.baseURL}`);
    
    try {
      // 准备测试环境
      await this.prepareTestEnvironment();
      
      // 运行各项集成测试
      await this.testDatabaseIntegration();
      await this.testServiceIntegration();
      await this.testAPIIntegration();
      await this.testCacheIntegration();
      await this.testAuditIntegration();
      await this.testMonitoringIntegration();
      await this.testEndToEndWorkflow();
      
      // 清理测试环境
      await this.cleanupTestEnvironment();
      
      // 生成测试报告
      const report = this.generateTestReport();
      console.log('\n📋 集成测试报告:');
      console.log(report);
      
      return report;
      
    } catch (error) {
      console.error('❌ 集成测试失败:', error);
      await this.cleanupTestEnvironment();
      throw error;
    }
  }

  /**
   * 准备测试环境
   */
  async prepareTestEnvironment() {
    console.log('🔧 准备测试环境');
    
    try {
      // 创建测试用户
      await this.createTestUser();
      
      // 初始化测试数据
      await this.initializeTestData();
      
      console.log('✅ 测试环境准备完成');
    } catch (error) {
      throw new Error(`准备测试环境失败: ${error.message}`);
    }
  }

  /**
   * 创建测试用户
   */
  async createTestUser() {
    try {
      const [existing] = await db.execute(
        'SELECT id FROM users WHERE id = ?',
        [this.testUser.id]
      );
      
      if (existing.length === 0) {
        await db.execute(`
          INSERT INTO users (id, username, password, role, created_at) 
          VALUES (?, ?, ?, ?, NOW())
        `, [
          this.testUser.id,
          this.testUser.username,
          'test_password_hash',
          this.testUser.role
        ]);
      }
      
      console.log('✅ 测试用户创建完成');
    } catch (error) {
      console.warn('⚠️  创建测试用户警告:', error.message);
    }
  }

  /**
   * 初始化测试数据
   */
  async initializeTestData() {
    try {
      // 确保基础权限数据存在
      const testPermissions = [
        'test.read',
        'test.write',
        'test.delete'
      ];
      
      for (const permission of testPermissions) {
        await db.execute(`
          INSERT IGNORE INTO permissions (code, name, description, category) 
          VALUES (?, ?, ?, ?)
        `, [permission, `测试权限${permission}`, '集成测试用权限', 'test']);
      }
      
      console.log('✅ 测试数据初始化完成');
    } catch (error) {
      console.warn('⚠️  初始化测试数据警告:', error.message);
    }
  }

  /**
   * 测试数据库集成
   */
  async testDatabaseIntegration() {
    console.log('🗄️  测试数据库集成');
    
    await this.runTest('数据库连接测试', async () => {
      await db.execute('SELECT 1');
    });
    
    await this.runTest('权限表查询测试', async () => {
      const [roles] = await db.execute('SELECT COUNT(*) as count FROM roles');
      const [permissions] = await db.execute('SELECT COUNT(*) as count FROM permissions');
      
      if (roles[0].count === 0 || permissions[0].count === 0) {
        throw new Error('权限基础数据不完整');
      }
    });
    
    await this.runTest('审计日志表测试', async () => {
      await db.execute('SELECT COUNT(*) as count FROM audit_logs LIMIT 1');
    });
    
    console.log('✅ 数据库集成测试完成');
  }

  /**
   * 测试服务集成
   */
  async testServiceIntegration() {
    console.log('🔧 测试服务集成');
    
    await this.runTest('权限服务初始化测试', async () => {
      const permissionService = new PermissionService();
      const hasPermission = await permissionService.hasPermission(this.testUser.id, 'test.read');
      // 不验证结果，只验证服务能正常工作
    });
    
    await this.runTest('权限配置服务测试', async () => {
      const config = PermissionConfigService.getConfig();
      if (!config || !config.roles || !config.permissions) {
        throw new Error('权限配置服务未正确初始化');
      }
    });
    
    await this.runTest('缓存服务测试', async () => {
      await PermissionCacheService.setUserPermissions(this.testUser.id, {
        userId: this.testUser.id,
        role: this.testUser.role,
        effectivePermissions: ['test.read']
      });
      
      const cached = await PermissionCacheService.getUserPermissions(this.testUser.id);
      if (!cached || cached.userId !== this.testUser.id) {
        throw new Error('缓存服务未正常工作');
      }
    });
    
    await this.runTest('审计服务测试', async () => {
      const auditService = new AuditService();
      await auditService.logPermissionAccess(
        this.testUser.id,
        'test.read',
        'granted',
        { ip: '127.0.0.1', userAgent: 'integration-test' }
      );
    });
    
    console.log('✅ 服务集成测试完成');
  }

  /**
   * 测试API集成
   */
  async testAPIIntegration() {
    console.log('🌐 测试API集成');
    
    await this.runTest('健康检查API测试', async () => {
      const response = await axios.get(`${this.baseURL}/api/health/permission-system`);
      if (response.status !== 200 || !response.data.success) {
        throw new Error('健康检查API响应异常');
      }
    });
    
    await this.runTest('监控API测试', async () => {
      try {
        const response = await axios.get(`${this.baseURL}/api/monitoring/overview`);
        // API可能需要认证，所以401也是正常的
        if (response.status !== 200 && response.status !== 401) {
          throw new Error('监控API响应异常');
        }
      } catch (error) {
        if (error.response && error.response.status === 401) {
          // 401是正常的，说明API存在且需要认证
          return;
        }
        throw error;
      }
    });
    
    console.log('✅ API集成测试完成');
  }

  /**
   * 测试缓存集成
   */
  async testCacheIntegration() {
    console.log('💾 测试缓存集成');
    
    await this.runTest('权限缓存一致性测试', async () => {
      const permissionService = new PermissionService();
      
      // 第一次查询（应该从数据库获取）
      const result1 = await permissionService.getUserPermissions(this.testUser.id);
      
      // 第二次查询（应该从缓存获取）
      const result2 = await permissionService.getUserPermissions(this.testUser.id);
      
      if (JSON.stringify(result1) !== JSON.stringify(result2)) {
        throw new Error('缓存数据与数据库数据不一致');
      }
    });
    
    await this.runTest('缓存失效测试', async () => {
      // 清除特定用户缓存
      await PermissionCacheService.clearUserPermissions(this.testUser.id);
      
      // 验证缓存已清除
      const cached = await PermissionCacheService.getUserPermissions(this.testUser.id);
      if (cached !== null) {
        throw new Error('缓存清除失败');
      }
    });
    
    console.log('✅ 缓存集成测试完成');
  }

  /**
   * 测试审计集成
   */
  async testAuditIntegration() {
    console.log('📝 测试审计集成');
    
    await this.runTest('权限访问审计测试', async () => {
      const auditService = new AuditService();
      const beforeCount = await this.getAuditLogCount();
      
      // 记录审计日志
      await auditService.logPermissionAccess(
        this.testUser.id,
        'test.integration',
        'granted',
        { ip: '127.0.0.1', userAgent: 'integration-test' }
      );
      
      const afterCount = await this.getAuditLogCount();
      if (afterCount <= beforeCount) {
        throw new Error('审计日志未正确记录');
      }
    });
    
    await this.runTest('权限变更审计测试', async () => {
      const auditService = new AuditService();
      const beforeCount = await this.getAuditLogCount();
      
      // 记录权限变更日志
      await auditService.logPermissionChange(
        1, // admin_id
        this.testUser.id,
        {
          type: 'permission_update',
          oldPermissions: ['test.read'],
          newPermissions: ['test.read', 'test.write']
        }
      );
      
      const afterCount = await this.getAuditLogCount();
      if (afterCount <= beforeCount) {
        throw new Error('权限变更审计日志未正确记录');
      }
    });
    
    console.log('✅ 审计集成测试完成');
  }

  /**
   * 获取审计日志数量
   */
  async getAuditLogCount() {
    const [result] = await db.execute('SELECT COUNT(*) as count FROM audit_logs');
    return result[0].count;
  }

  /**
   * 测试监控集成
   */
  async testMonitoringIntegration() {
    console.log('📊 测试监控集成');
    
    await this.runTest('监控指标记录测试', async () => {
      const PermissionMonitoringService = require('../services/PermissionMonitoringService');
      
      // 记录一些监控指标
      PermissionMonitoringService.recordPermissionCheck(25, true, 'test.read', this.testUser.id);
      PermissionMonitoringService.recordPermissionCheck(35, true, 'test.write', this.testUser.id);
      
      // 获取监控指标
      const metrics = PermissionMonitoringService.getMetrics();
      if (metrics.permissionChecks.total === 0) {
        throw new Error('监控指标未正确记录');
      }
    });
    
    console.log('✅ 监控集成测试完成');
  }

  /**
   * 测试端到端工作流
   */
  async testEndToEndWorkflow() {
    console.log('🔄 测试端到端工作流');
    
    await this.runTest('完整权限验证流程测试', async () => {
      const permissionService = new PermissionService();
      
      // 1. 获取用户权限
      const userPermissions = await permissionService.getUserPermissions(this.testUser.id);
      if (!userPermissions) {
        throw new Error('无法获取用户权限');
      }
      
      // 2. 检查权限
      const hasPermission = await permissionService.hasPermission(this.testUser.id, 'test.read');
      
      // 3. 验证缓存
      const cachedPermissions = await PermissionCacheService.getUserPermissions(this.testUser.id);
      if (!cachedPermissions) {
        throw new Error('权限未正确缓存');
      }
      
      // 4. 验证审计日志
      const auditService = new AuditService();
      await auditService.logPermissionAccess(
        this.testUser.id,
        'test.read',
        hasPermission ? 'granted' : 'denied',
        { ip: '127.0.0.1', userAgent: 'integration-test' }
      );
    });
    
    await this.runTest('权限更新流程测试', async () => {
      const permissionService = new PermissionService();
      
      // 1. 更新用户权限
      await permissionService.updateUserPermissions(
        this.testUser.id,
        [{ code: 'test.write', granted: true }],
        1 // admin_id
      );
      
      // 2. 验证缓存已清除
      await PermissionCacheService.clearUserPermissions(this.testUser.id);
      
      // 3. 重新获取权限验证更新
      const updatedPermissions = await permissionService.getUserPermissions(this.testUser.id);
      if (!updatedPermissions) {
        throw new Error('权限更新后无法获取用户权限');
      }
    });
    
    console.log('✅ 端到端工作流测试完成');
  }

  /**
   * 清理测试环境
   */
  async cleanupTestEnvironment() {
    console.log('🧹 清理测试环境');
    
    try {
      // 删除测试用户
      await db.execute('DELETE FROM users WHERE id = ?', [this.testUser.id]);
      
      // 清除测试用户的缓存
      await PermissionCacheService.clearUserPermissions(this.testUser.id);
      
      // 删除测试权限数据
      await db.execute('DELETE FROM permissions WHERE category = ?', ['test']);
      
      console.log('✅ 测试环境清理完成');
    } catch (error) {
      console.warn('⚠️  清理测试环境警告:', error.message);
    }
  }

  /**
   * 运行单个测试
   */
  async runTest(testName, testFunction) {
    this.testResults.totalTests++;
    
    try {
      console.log(`  🧪 ${testName}`);
      await testFunction();
      this.testResults.passedTests++;
      this.testResults.testDetails.push({
        name: testName,
        status: 'PASSED',
        error: null
      });
      console.log(`  ✅ ${testName} - 通过`);
    } catch (error) {
      this.testResults.failedTests++;
      this.testResults.testDetails.push({
        name: testName,
        status: 'FAILED',
        error: error.message
      });
      console.log(`  ❌ ${testName} - 失败: ${error.message}`);
    }
  }

  /**
   * 生成测试报告
   */
  generateTestReport() {
    const results = this.testResults;
    const successRate = ((results.passedTests / results.totalTests) * 100).toFixed(2);
    
    const report = {
      summary: {
        totalTests: results.totalTests,
        passedTests: results.passedTests,
        failedTests: results.failedTests,
        successRate: successRate + '%'
      },
      details: results.testDetails,
      timestamp: new Date().toISOString()
    };
    
    return report;
  }
}

// 命令行接口
async function main() {
  const integrationTest = new SystemIntegrationTest();
  
  try {
    const report = await integrationTest.runIntegrationTests();
    
    // 保存测试报告
    const fs = require('fs');
    const reportPath = `integration_test_report_${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 测试报告已保存到: ${reportPath}`);
    
    // 如果有失败的测试，退出码为1
    if (report.summary.failedTests > 0) {
      process.exit(1);
    }
    
  } catch (error) {
    console.error('集成测试失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = SystemIntegrationTest;