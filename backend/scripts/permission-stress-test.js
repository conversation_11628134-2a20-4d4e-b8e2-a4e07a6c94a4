l#!/usr/bin/env node

const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const PermissionService = require('../services/PermissionService');
const PermissionCacheService = require('../services/PermissionCacheService');
const PermissionMonitoringService = require('../services/PermissionMonitoringService');

/**
 * 权限系统压力测试脚本
 * 测试权限验证在高并发情况下的性能表现
 */

class PermissionStressTest {
  constructor() {
    this.testConfig = {
      concurrentUsers: 1000,
      testDuration: 60000, // 60秒
      requestsPerSecond: 100,
      workerCount: 4,
      testScenarios: [
        'permission_check',
        'role_check',
        'cache_performance',
        'mixed_operations'
      ]
    };
    
    this.testResults = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: Infinity,
      responseTimes: [],
      errorTypes: {},
      throughput: 0,
      cacheHitRate: 0
    };
  }

  /**
   * 运行完整的压力测试
   */
  async runStressTest(scenario = 'all') {
    console.log('🚀 开始权限系统压力测试');
    console.log('测试配置:', this.testConfig);
    
    const startTime = Date.now();
    
    try {
      if (scenario === 'all') {
        // 运行所有测试场景
        for (const testScenario of this.testConfig.testScenarios) {
          console.log(`\n📊 运行测试场景: ${testScenario}`);
          await this.runTestScenario(testScenario);
        }
      } else {
        // 运行指定测试场景
        await this.runTestScenario(scenario);
      }
      
      const endTime = Date.now();
      const totalDuration = endTime - startTime;
      
      // 生成测试报告
      const report = this.generateTestReport(totalDuration);
      console.log('\n📋 压力测试报告:');
      console.log(report);
      
      return report;
      
    } catch (error) {
      console.error('❌ 压力测试失败:', error);
      throw error;
    }
  }

  /**
   * 运行测试场景
   */
  async runTestScenario(scenario) {
    const workers = [];
    const workerPromises = [];
    
    // 重置测试结果
    this.resetTestResults();
    
    // 创建工作线程
    for (let i = 0; i < this.testConfig.workerCount; i++) {
      const worker = new Worker(__filename, {
        workerData: {
          scenario,
          workerId: i,
          config: this.testConfig
        }
      });
      
      workers.push(worker);
      
      const workerPromise = new Promise((resolve, reject) => {
        worker.on('message', (result) => {
          this.aggregateResults(result);
        });
        
        worker.on('error', reject);
        worker.on('exit', (code) => {
          if (code !== 0) {
            reject(new Error(`Worker stopped with exit code ${code}`));
          } else {
            resolve();
          }
        });
      });
      
      workerPromises.push(workerPromise);
    }
    
    // 等待所有工作线程完成
    await Promise.all(workerPromises);
    
    // 清理工作线程
    workers.forEach(worker => worker.terminate());
    
    console.log(`✅ 测试场景 ${scenario} 完成`);
  }

  /**
   * 聚合测试结果
   */
  aggregateResults(workerResult) {
    this.testResults.totalRequests += workerResult.totalRequests;
    this.testResults.successfulRequests += workerResult.successfulRequests;
    this.testResults.failedRequests += workerResult.failedRequests;
    this.testResults.responseTimes.push(...workerResult.responseTimes);
    
    // 聚合错误类型
    for (const [errorType, count] of Object.entries(workerResult.errorTypes)) {
      this.testResults.errorTypes[errorType] = 
        (this.testResults.errorTypes[errorType] || 0) + count;
    }
  }

  /**
   * 重置测试结果
   */
  resetTestResults() {
    this.testResults = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      maxResponseTime: 0,
      minResponseTime: Infinity,
      responseTimes: [],
      errorTypes: {},
      throughput: 0,
      cacheHitRate: 0
    };
  }

  /**
   * 生成测试报告
   */
  generateTestReport(totalDuration) {
    const results = this.testResults;
    
    // 计算统计数据
    if (results.responseTimes.length > 0) {
      results.averageResponseTime = 
        results.responseTimes.reduce((a, b) => a + b, 0) / results.responseTimes.length;
      results.maxResponseTime = Math.max(...results.responseTimes);
      results.minResponseTime = Math.min(...results.responseTimes);
    }
    
    results.throughput = (results.totalRequests / totalDuration) * 1000; // 每秒请求数
    
    // 获取缓存统计
    try {
      const cacheStats = PermissionCacheService.getCacheStats();
      results.cacheHitRate = cacheStats.hitRate;
    } catch (error) {
      console.warn('获取缓存统计失败:', error.message);
    }
    
    const report = {
      summary: {
        totalRequests: results.totalRequests,
        successfulRequests: results.successfulRequests,
        failedRequests: results.failedRequests,
        successRate: ((results.successfulRequests / results.totalRequests) * 100).toFixed(2) + '%',
        testDuration: totalDuration + 'ms'
      },
      performance: {
        averageResponseTime: results.averageResponseTime.toFixed(2) + 'ms',
        maxResponseTime: results.maxResponseTime + 'ms',
        minResponseTime: results.minResponseTime + 'ms',
        throughput: results.throughput.toFixed(2) + ' req/s'
      },
      cache: {
        hitRate: (results.cacheHitRate * 100).toFixed(2) + '%'
      },
      errors: results.errorTypes,
      percentiles: this.calculatePercentiles(results.responseTimes)
    };
    
    return report;
  }

  /**
   * 计算响应时间百分位数
   */
  calculatePercentiles(responseTimes) {
    if (responseTimes.length === 0) return {};
    
    const sorted = responseTimes.sort((a, b) => a - b);
    const length = sorted.length;
    
    return {
      p50: sorted[Math.floor(length * 0.5)] + 'ms',
      p90: sorted[Math.floor(length * 0.9)] + 'ms',
      p95: sorted[Math.floor(length * 0.95)] + 'ms',
      p99: sorted[Math.floor(length * 0.99)] + 'ms'
    };
  }
}

/**
 * 工作线程执行函数
 */
async function runWorker(workerData) {
  const { scenario, workerId, config } = workerData;
  
  const workerResults = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    responseTimes: [],
    errorTypes: {}
  };
  
  const permissionService = new PermissionService();
  const startTime = Date.now();
  const endTime = startTime + config.testDuration;
  
  console.log(`Worker ${workerId} 开始执行场景: ${scenario}`);
  
  while (Date.now() < endTime) {
    try {
      const requestStartTime = Date.now();
      
      // 根据场景执行不同的操作
      switch (scenario) {
        case 'permission_check':
          await testPermissionCheck(permissionService);
          break;
        case 'role_check':
          await testRoleCheck(permissionService);
          break;
        case 'cache_performance':
          await testCachePerformance(permissionService);
          break;
        case 'mixed_operations':
          await testMixedOperations(permissionService);
          break;
        default:
          throw new Error(`未知的测试场景: ${scenario}`);
      }
      
      const responseTime = Date.now() - requestStartTime;
      workerResults.responseTimes.push(responseTime);
      workerResults.successfulRequests++;
      
    } catch (error) {
      workerResults.failedRequests++;
      const errorType = error.constructor.name;
      workerResults.errorTypes[errorType] = 
        (workerResults.errorTypes[errorType] || 0) + 1;
    }
    
    workerResults.totalRequests++;
    
    // 控制请求频率
    await sleep(1000 / config.requestsPerSecond);
  }
  
  console.log(`Worker ${workerId} 完成，处理了 ${workerResults.totalRequests} 个请求`);
  
  // 发送结果到主线程
  parentPort.postMessage(workerResults);
}

/**
 * 测试权限检查
 */
async function testPermissionCheck(permissionService) {
  const userId = Math.floor(Math.random() * 100) + 1;
  const permissions = [
    'user.read.own',
    'user.update.own',
    'server.read.own',
    'website.read.own',
    'system.config.read'
  ];
  
  const permission = permissions[Math.floor(Math.random() * permissions.length)];
  await permissionService.hasPermission(userId, permission);
}

/**
 * 测试角色检查
 */
async function testRoleCheck(permissionService) {
  const userId = Math.floor(Math.random() * 100) + 1;
  const roles = ['admin', 'user', 'viewer'];
  
  const role = roles[Math.floor(Math.random() * roles.length)];
  await permissionService.hasRole(userId, role);
}

/**
 * 测试缓存性能
 */
async function testCachePerformance(permissionService) {
  // 重复访问相同的权限以测试缓存
  const userId = Math.floor(Math.random() * 10) + 1; // 限制用户ID范围以提高缓存命中
  const permission = 'user.read.own';
  
  await permissionService.hasPermission(userId, permission);
}

/**
 * 测试混合操作
 */
async function testMixedOperations(permissionService) {
  const operations = [
    () => testPermissionCheck(permissionService),
    () => testRoleCheck(permissionService),
    () => testCachePerformance(permissionService)
  ];
  
  const operation = operations[Math.floor(Math.random() * operations.length)];
  await operation();
}

/**
 * 睡眠函数
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 主线程和工作线程的分支处理
if (isMainThread) {
  // 主线程 - 命令行接口
  async function main() {
    const stressTest = new PermissionStressTest();
    const args = process.argv.slice(2);
    const scenario = args[0] || 'all';
    
    try {
      const report = await stressTest.runStressTest(scenario);
      
      // 保存测试报告
      const fs = require('fs');
      const reportPath = `stress_test_report_${Date.now()}.json`;
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`\n📄 测试报告已保存到: ${reportPath}`);
      
    } catch (error) {
      console.error('压力测试失败:', error.message);
      process.exit(1);
    }
  }
  
  if (require.main === module) {
    main();
  }
  
  module.exports = PermissionStressTest;
  
} else {
  // 工作线程
  runWorker(workerData);
}