#!/usr/bin/env node

/**
 * 脚本管理器
 * 统一管理所有定时任务脚本，提供脚本执行、状态监控、日志管理等功能
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs').promises;

class ScriptManager {
  constructor() {
    this.scriptsDir = __dirname;
    this.logDir = '/var/log/sitemanager';
    
    // 可用的脚本配置
    this.scripts = {
      'status-notification': {
        file: 'status-notification.js',
        description: '状态异常通知脚本',
        schedule: '*/10 * * * *', // 每10分钟
        enabled: true,
        timeout: 300000 // 5分钟超时
      },
      'renewal-notification': {
        file: 'renewal-notification.js',
        description: '续费提醒通知脚本',
        schedule: '0 9 * * *', // 每天上午9点
        enabled: true,
        timeout: 180000 // 3分钟超时
      },
      'update-server-info': {
        file: 'update-server-info.js',
        description: '服务器信息更新脚本',
        schedule: '0 * * * *', // 每小时
        enabled: true,
        timeout: 600000 // 10分钟超时
      },
      'monitor-health-check': {
        file: 'monitor-health-check.js',
        description: '监控健康检查脚本',
        schedule: '*/5 * * * *', // 每5分钟
        enabled: true,
        timeout: 120000 // 2分钟超时
      },
      'cleanup-old-records': {
        file: 'cleanup-old-records.js',
        description: '数据清理脚本',
        schedule: '0 2 * * *', // 每天凌晨2点
        enabled: true,
        timeout: 1800000 // 30分钟超时
      }
    };
    
    this.runningScripts = new Map();
  }

  /**
   * 初始化脚本管理器
   */
  async initialize() {
    try {
      console.log('🔧 初始化脚本管理器...');
      
      // 创建日志目录
      await this.ensureLogDirectory();
      
      // 验证脚本文件
      await this.validateScripts();
      
      console.log('✅ 脚本管理器初始化完成');
      
    } catch (error) {
      console.error('❌ 脚本管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 确保日志目录存在
   */
  async ensureLogDirectory() {
    try {
      await fs.mkdir(this.logDir, { recursive: true });
      console.log(`✅ 日志目录已创建: ${this.logDir}`);
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }

  /**
   * 验证脚本文件
   */
  async validateScripts() {
    console.log('🔍 验证脚本文件...');
    
    for (const [name, config] of Object.entries(this.scripts)) {
      const scriptPath = path.join(this.scriptsDir, config.file);
      
      try {
        await fs.access(scriptPath);
        console.log(`  ✅ ${name}: ${config.file}`);
      } catch (error) {
        console.log(`  ❌ ${name}: ${config.file} (文件不存在)`);
        config.enabled = false;
      }
    }
  }

  /**
   * 执行指定脚本
   */
  async runScript(scriptName, args = []) {
    try {
      const config = this.scripts[scriptName];
      if (!config) {
        throw new Error(`未知的脚本: ${scriptName}`);
      }
      
      if (!config.enabled) {
        throw new Error(`脚本已禁用: ${scriptName}`);
      }
      
      if (this.runningScripts.has(scriptName)) {
        throw new Error(`脚本正在运行中: ${scriptName}`);
      }
      
      console.log(`🚀 执行脚本: ${scriptName}`);
      
      const scriptPath = path.join(this.scriptsDir, config.file);
      const logFile = path.join(this.logDir, `${scriptName}.log`);
      
      // 创建子进程
      const child = spawn('node', [scriptPath, ...args], {
        stdio: ['ignore', 'pipe', 'pipe'],
        cwd: this.scriptsDir,
        env: { ...process.env }
      });
      
      // 记录运行信息
      const runInfo = {
        pid: child.pid,
        startTime: new Date(),
        config,
        logFile
      };
      
      this.runningScripts.set(scriptName, runInfo);
      
      // 设置超时
      const timeout = setTimeout(() => {
        console.log(`⏰ 脚本执行超时，强制终止: ${scriptName}`);
        child.kill('SIGTERM');
      }, config.timeout);
      
      // 处理输出
      const logStream = await fs.open(logFile, 'a');
      
      child.stdout.on('data', async (data) => {
        const output = data.toString();
        console.log(`[${scriptName}] ${output.trim()}`);
        await logStream.write(`${new Date().toISOString()} [STDOUT] ${output}`);
      });
      
      child.stderr.on('data', async (data) => {
        const output = data.toString();
        console.error(`[${scriptName}] ${output.trim()}`);
        await logStream.write(`${new Date().toISOString()} [STDERR] ${output}`);
      });
      
      // 等待脚本完成
      return new Promise((resolve, reject) => {
        child.on('close', async (code) => {
          clearTimeout(timeout);
          await logStream.close();
          
          const endTime = new Date();
          const duration = endTime - runInfo.startTime;
          
          this.runningScripts.delete(scriptName);
          
          if (code === 0) {
            console.log(`✅ 脚本执行成功: ${scriptName} (耗时: ${duration}ms)`);
            resolve({ success: true, code, duration });
          } else {
            console.error(`❌ 脚本执行失败: ${scriptName} (退出码: ${code})`);
            reject(new Error(`脚本执行失败，退出码: ${code}`));
          }
        });
        
        child.on('error', async (error) => {
          clearTimeout(timeout);
          await logStream.close();
          this.runningScripts.delete(scriptName);
          
          console.error(`💥 脚本执行异常: ${scriptName}`, error);
          reject(error);
        });
      });
      
    } catch (error) {
      console.error(`❌ 执行脚本失败 ${scriptName}:`, error);
      throw error;
    }
  }

  /**
   * 停止指定脚本
   */
  async stopScript(scriptName) {
    try {
      const runInfo = this.runningScripts.get(scriptName);
      if (!runInfo) {
        console.log(`⚠️  脚本未在运行: ${scriptName}`);
        return;
      }
      
      console.log(`🛑 停止脚本: ${scriptName} (PID: ${runInfo.pid})`);
      
      // 发送终止信号
      process.kill(runInfo.pid, 'SIGTERM');
      
      // 等待一段时间后强制杀死
      setTimeout(() => {
        if (this.runningScripts.has(scriptName)) {
          console.log(`💀 强制终止脚本: ${scriptName}`);
          process.kill(runInfo.pid, 'SIGKILL');
          this.runningScripts.delete(scriptName);
        }
      }, 10000);
      
    } catch (error) {
      console.error(`❌ 停止脚本失败 ${scriptName}:`, error);
      throw error;
    }
  }

  /**
   * 获取脚本状态
   */
  getScriptStatus(scriptName = null) {
    if (scriptName) {
      const config = this.scripts[scriptName];
      const runInfo = this.runningScripts.get(scriptName);
      
      return {
        name: scriptName,
        config,
        running: !!runInfo,
        runInfo: runInfo ? {
          pid: runInfo.pid,
          startTime: runInfo.startTime,
          duration: Date.now() - runInfo.startTime
        } : null
      };
    }
    
    // 返回所有脚本状态
    const status = {};
    
    for (const [name, config] of Object.entries(this.scripts)) {
      const runInfo = this.runningScripts.get(name);
      
      status[name] = {
        config,
        running: !!runInfo,
        runInfo: runInfo ? {
          pid: runInfo.pid,
          startTime: runInfo.startTime,
          duration: Date.now() - runInfo.startTime
        } : null
      };
    }
    
    return status;
  }

  /**
   * 获取脚本日志
   */
  async getScriptLog(scriptName, lines = 50) {
    try {
      const logFile = path.join(this.logDir, `${scriptName}.log`);
      
      return new Promise((resolve, reject) => {
        exec(`tail -n ${lines} ${logFile}`, (error, stdout, stderr) => {
          if (error) {
            reject(error);
          } else {
            resolve(stdout);
          }
        });
      });
      
    } catch (error) {
      console.error(`❌ 获取脚本日志失败 ${scriptName}:`, error);
      throw error;
    }
  }

  /**
   * 生成Crontab配置
   */
  generateCrontabConfig() {
    console.log('📋 生成Crontab配置...');
    
    let crontab = '# SiteManager 脚本管理器生成的Crontab配置\n';
    crontab += `# 生成时间: ${new Date().toISOString()}\n\n`;
    
    for (const [name, config] of Object.entries(this.scripts)) {
      if (config.enabled && config.schedule) {
        const scriptPath = path.join(this.scriptsDir, config.file);
        const logFile = path.join(this.logDir, `${name}.log`);
        
        crontab += `# ${config.description}\n`;
        crontab += `${config.schedule} cd ${this.scriptsDir} && /usr/bin/node ${scriptPath} >> ${logFile} 2>&1\n\n`;
      }
    }
    
    return crontab;
  }

  /**
   * 安装Crontab配置
   */
  async installCrontab() {
    try {
      console.log('📦 安装Crontab配置...');
      
      const crontabConfig = this.generateCrontabConfig();
      const tempFile = '/tmp/sitemanager-crontab.txt';
      
      // 写入临时文件
      await fs.writeFile(tempFile, crontabConfig);
      
      // 安装crontab
      return new Promise((resolve, reject) => {
        exec(`crontab ${tempFile}`, (error, stdout, stderr) => {
          if (error) {
            reject(error);
          } else {
            console.log('✅ Crontab配置安装成功');
            resolve(stdout);
          }
        });
      });
      
    } catch (error) {
      console.error('❌ 安装Crontab配置失败:', error);
      throw error;
    }
  }

  /**
   * 显示帮助信息
   */
  showHelp() {
    console.log(`
SiteManager 脚本管理器

用法: node script-manager.js <命令> [选项]

命令:
  list                    列出所有可用脚本
  status [脚本名]         显示脚本运行状态
  run <脚本名> [参数]     执行指定脚本
  stop <脚本名>           停止指定脚本
  log <脚本名> [行数]     查看脚本日志
  crontab                 生成Crontab配置
  install-crontab         安装Crontab配置
  help                    显示帮助信息

可用脚本:
${Object.entries(this.scripts).map(([name, config]) => 
  `  ${name.padEnd(20)} - ${config.description} (${config.enabled ? '启用' : '禁用'})`
).join('\n')}

示例:
  node script-manager.js list
  node script-manager.js run status-notification
  node script-manager.js status
  node script-manager.js log status-notification 100
  node script-manager.js install-crontab
    `);
  }

  /**
   * 主执行方法
   */
  async execute() {
    try {
      await this.initialize();
      
      const command = process.argv[2];
      const args = process.argv.slice(3);
      
      switch (command) {
        case 'list':
          console.log('📋 可用脚本列表:');
          Object.entries(this.scripts).forEach(([name, config]) => {
            const status = config.enabled ? '✅ 启用' : '❌ 禁用';
            console.log(`  ${name.padEnd(20)} - ${config.description} (${status})`);
          });
          break;
          
        case 'status':
          const scriptName = args[0];
          const status = this.getScriptStatus(scriptName);
          
          if (scriptName) {
            console.log(`📊 脚本状态: ${scriptName}`);
            console.log(`  启用: ${status.config.enabled ? '是' : '否'}`);
            console.log(`  运行中: ${status.running ? '是' : '否'}`);
            if (status.runInfo) {
              console.log(`  PID: ${status.runInfo.pid}`);
              console.log(`  开始时间: ${status.runInfo.startTime.toLocaleString('zh-CN')}`);
              console.log(`  运行时长: ${Math.round(status.runInfo.duration / 1000)}秒`);
            }
          } else {
            console.log('📊 所有脚本状态:');
            Object.entries(status).forEach(([name, info]) => {
              const runStatus = info.running ? '🟢 运行中' : '⚪ 停止';
              console.log(`  ${name.padEnd(20)} - ${runStatus}`);
            });
          }
          break;
          
        case 'run':
          const targetScript = args[0];
          const scriptArgs = args.slice(1);
          
          if (!targetScript) {
            console.error('❌ 请指定要执行的脚本名');
            process.exit(1);
          }
          
          await this.runScript(targetScript, scriptArgs);
          break;
          
        case 'stop':
          const stopScript = args[0];
          
          if (!stopScript) {
            console.error('❌ 请指定要停止的脚本名');
            process.exit(1);
          }
          
          await this.stopScript(stopScript);
          break;
          
        case 'log':
          const logScript = args[0];
          const logLines = parseInt(args[1]) || 50;
          
          if (!logScript) {
            console.error('❌ 请指定要查看日志的脚本名');
            process.exit(1);
          }
          
          const log = await this.getScriptLog(logScript, logLines);
          console.log(`📝 ${logScript} 最近 ${logLines} 行日志:`);
          console.log(log);
          break;
          
        case 'crontab':
          const crontabConfig = this.generateCrontabConfig();
          console.log('📋 Crontab配置:');
          console.log(crontabConfig);
          break;
          
        case 'install-crontab':
          await this.installCrontab();
          break;
          
        case 'help':
        default:
          this.showHelp();
          break;
      }
      
    } catch (error) {
      console.error('💥 脚本管理器执行失败:', error);
      process.exit(1);
    }
  }
}

// 检查是否在CLI模式下运行
if (require.main === module) {
  const manager = new ScriptManager();
  
  // 处理进程信号
  process.on('SIGINT', () => {
    console.log('\n🛑 接收到中断信号，正在停止所有脚本...');
    
    // 停止所有运行中的脚本
    for (const scriptName of manager.runningScripts.keys()) {
      manager.stopScript(scriptName).catch(console.error);
    }
    
    setTimeout(() => process.exit(0), 5000);
  });
  
  // 执行脚本管理器
  manager.execute()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 脚本管理器执行失败:', error);
      process.exit(1);
    });
}

module.exports = ScriptManager;
