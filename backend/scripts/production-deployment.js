#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const { spawn, exec } = require('child_process');
const PermissionBackupRestore = require('./permission-backup-restore');
require('dotenv').config();

/**
 * 权限系统生产环境部署脚本
 * 负责数据库迁移、功能验证、灰度发布和回滚机制
 */

class ProductionDeployment {
  constructor() {
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'website_management',
      charset: 'utf8mb4'
    };
    
    this.deploymentConfig = {
      environment: process.env.NODE_ENV || 'production',
      backupBeforeDeploy: true,
      runTests: true,
      enableGradualRollout: true,
      rolloutPercentage: 10, // 初始灰度比例
      healthCheckInterval: 30000, // 健康检查间隔
      maxHealthCheckFailures: 3,
      rollbackOnFailure: true
    };
    
    this.deploymentState = {
      phase: 'preparing',
      startTime: null,
      backupName: null,
      migrationApplied: false,
      testsCompleted: false,
      healthChecksPassed: false,
      rolloutActive: false,
      errors: []
    };
  }

  /**
   * 执行完整的生产环境部署
   */
  async deploy(options = {}) {
    console.log('🚀 开始权限系统生产环境部署');
    console.log('部署配置:', { ...this.deploymentConfig, ...options });
    
    this.deploymentState.startTime = new Date();
    
    try {
      // 阶段1: 部署前准备
      await this.prepareDeployment();
      
      // 阶段2: 数据库迁移
      await this.executeDatabaseMigration();
      
      // 阶段3: 功能验证
      await this.verifySystemFunctionality();
      
      // 阶段4: 灰度发布
      if (this.deploymentConfig.enableGradualRollout) {
        await this.executeGradualRollout();
      }
      
      // 阶段5: 最终验证
      await this.finalVerification();
      
      console.log('✅ 权限系统部署成功完成');
      return { success: true, deploymentState: this.deploymentState };
      
    } catch (error) {
      console.error('❌ 部署失败:', error.message);
      
      // 自动回滚
      if (this.deploymentConfig.rollbackOnFailure) {
        await this.rollback();
      }
      
      throw error;
    }
  }

  /**
   * 部署前准备
   */
  async prepareDeployment() {
    console.log('📋 阶段1: 部署前准备');
    this.deploymentState.phase = 'preparing';
    
    // 检查环境变量
    await this.checkEnvironmentVariables();
    
    // 检查数据库连接
    await this.checkDatabaseConnection();
    
    // 创建部署前备份
    if (this.deploymentConfig.backupBeforeDeploy) {
      await this.createPreDeploymentBackup();
    }
    
    // 检查系统依赖
    await this.checkSystemDependencies();
    
    console.log('✅ 部署前准备完成');
  }

  /**
   * 检查环境变量
   */
  async checkEnvironmentVariables() {
    console.log('🔍 检查环境变量配置');
    
    const requiredEnvVars = [
      'NODE_ENV',
      'DB_HOST',
      'DB_USER',
      'DB_PASSWORD',
      'DB_NAME',
      'JWT_SECRET'
    ];
    
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
      throw new Error(`缺少必需的环境变量: ${missingVars.join(', ')}`);
    }
    
    // 验证生产环境配置
    if (process.env.NODE_ENV !== 'production') {
      console.warn('⚠️  警告: NODE_ENV 不是 production');
    }
    
    console.log('✅ 环境变量检查通过');
  }

  /**
   * 检查数据库连接
   */
  async checkDatabaseConnection() {
    console.log('🔍 检查数据库连接');
    
    try {
      const connection = await mysql.createConnection(this.dbConfig);
      await connection.execute('SELECT 1');
      await connection.end();
      console.log('✅ 数据库连接正常');
    } catch (error) {
      throw new Error(`数据库连接失败: ${error.message}`);
    }
  }

  /**
   * 创建部署前备份
   */
  async createPreDeploymentBackup() {
    console.log('💾 创建部署前备份');
    
    const backupRestore = new PermissionBackupRestore();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `pre_deployment_${timestamp}`;
    
    const result = await backupRestore.backup({
      name: backupName,
      description: '权限系统部署前自动备份'
    });
    
    this.deploymentState.backupName = backupName;
    console.log(`✅ 备份创建成功: ${backupName}`);
  }

  /**
   * 检查系统依赖
   */
  async checkSystemDependencies() {
    console.log('🔍 检查系统依赖');
    
    // 检查Node.js版本
    const nodeVersion = process.version;
    console.log(`Node.js版本: ${nodeVersion}`);
    
    // 检查npm包依赖
    try {
      await this.execCommand('npm list --production --depth=0');
      console.log('✅ npm依赖检查通过');
    } catch (error) {
      console.warn('⚠️  npm依赖检查警告:', error.message);
    }
    
    // 检查Redis连接（如果配置了）
    if (process.env.REDIS_URL) {
      await this.checkRedisConnection();
    }
  }

  /**
   * 检查Redis连接
   */
  async checkRedisConnection() {
    console.log('🔍 检查Redis连接');
    
    try {
      const Redis = require('ioredis');
      const redis = new Redis(process.env.REDIS_URL);
      await redis.ping();
      await redis.disconnect();
      console.log('✅ Redis连接正常');
    } catch (error) {
      console.warn('⚠️  Redis连接警告:', error.message);
    }
  }

  /**
   * 执行数据库迁移
   */
  async executeDatabaseMigration() {
    console.log('🗄️  阶段2: 执行数据库迁移');
    this.deploymentState.phase = 'migrating';
    
    const connection = await mysql.createConnection(this.dbConfig);
    
    try {
      // 检查迁移状态
      await this.checkMigrationStatus(connection);
      
      // 执行权限系统迁移
      await this.runPermissionSystemMigration(connection);
      
      // 验证迁移结果
      await this.verifyMigrationResults(connection);
      
      this.deploymentState.migrationApplied = true;
      console.log('✅ 数据库迁移完成');
      
    } finally {
      await connection.end();
    }
  }

  /**
   * 检查迁移状态
   */
  async checkMigrationStatus(connection) {
    console.log('🔍 检查数据库迁移状态');
    
    try {
      // 检查迁移表是否存在
      const [tables] = await connection.execute(`
        SELECT TABLE_NAME 
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'migrations'
      `, [this.dbConfig.database]);
      
      if (tables.length === 0) {
        // 创建迁移表
        await connection.execute(`
          CREATE TABLE migrations (
            id INT AUTO_INCREMENT PRIMARY KEY,
            migration_name VARCHAR(255) NOT NULL,
            executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_migration (migration_name)
          )
        `);
        console.log('✅ 迁移表已创建');
      }
      
      // 检查已执行的迁移
      const [executedMigrations] = await connection.execute(
        'SELECT migration_name FROM migrations'
      );
      
      console.log(`已执行的迁移数量: ${executedMigrations.length}`);
      
    } catch (error) {
      throw new Error(`检查迁移状态失败: ${error.message}`);
    }
  }

  /**
   * 运行权限系统迁移
   */
  async runPermissionSystemMigration(connection) {
    console.log('🔄 执行权限系统迁移');
    
    const migrationFile = path.join(__dirname, '../database/migrations/008_enhance_permission_system.sql');
    
    if (!fs.existsSync(migrationFile)) {
      throw new Error(`迁移文件不存在: ${migrationFile}`);
    }
    
    // 检查迁移是否已执行
    const [existing] = await connection.execute(
      'SELECT id FROM migrations WHERE migration_name = ?',
      ['008_enhance_permission_system']
    );
    
    if (existing.length > 0) {
      console.log('⚠️  权限系统迁移已执行，跳过');
      return;
    }
    
    // 读取并执行迁移SQL
    const migrationSQL = fs.readFileSync(migrationFile, 'utf8');
    const statements = migrationSQL.split(';').filter(stmt => stmt.trim());
    
    await connection.beginTransaction();
    
    try {
      for (const statement of statements) {
        if (statement.trim()) {
          await connection.execute(statement);
        }
      }
      
      // 记录迁移执行
      await connection.execute(
        'INSERT INTO migrations (migration_name) VALUES (?)',
        ['008_enhance_permission_system']
      );
      
      await connection.commit();
      console.log('✅ 权限系统迁移执行成功');
      
    } catch (error) {
      await connection.rollback();
      throw new Error(`迁移执行失败: ${error.message}`);
    }
  }

  /**
   * 验证迁移结果
   */
  async verifyMigrationResults(connection) {
    console.log('🔍 验证迁移结果');
    
    const expectedTables = [
      'roles',
      'permissions',
      'role_permissions',
      'user_custom_permissions',
      'audit_logs'
    ];
    
    for (const tableName of expectedTables) {
      try {
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableName} LIMIT 1`);
        console.log(`✅ 表 ${tableName} 验证通过`);
      } catch (error) {
        throw new Error(`表 ${tableName} 验证失败: ${error.message}`);
      }
    }
    
    console.log('✅ 迁移结果验证通过');
  }

  /**
   * 验证系统功能
   */
  async verifySystemFunctionality() {
    console.log('🧪 阶段3: 验证系统功能');
    this.deploymentState.phase = 'testing';
    
    // 运行自动化测试
    if (this.deploymentConfig.runTests) {
      await this.runAutomatedTests();
    }
    
    // 验证权限系统核心功能
    await this.verifyPermissionSystemCore();
    
    // 验证API接口
    await this.verifyAPIEndpoints();
    
    // 验证数据库连接和查询
    await this.verifyDatabaseOperations();
    
    this.deploymentState.testsCompleted = true;
    console.log('✅ 系统功能验证完成');
  }

  /**
   * 运行自动化测试
   */
  async runAutomatedTests() {
    console.log('🧪 运行自动化测试');
    
    try {
      // 运行权限系统测试
      await this.execCommand('npm run test:permission');
      console.log('✅ 权限系统测试通过');
      
      // 运行集成测试
      await this.execCommand('npm run test:integration');
      console.log('✅ 集成测试通过');
      
    } catch (error) {
      throw new Error(`自动化测试失败: ${error.message}`);
    }
  }

  /**
   * 验证权限系统核心功能
   */
  async verifyPermissionSystemCore() {
    console.log('🔐 验证权限系统核心功能');
    
    try {
      const PermissionService = require('../services/PermissionService');
      const permissionService = new PermissionService();
      
      // 测试权限检查
      const hasPermission = await permissionService.hasPermission(1, 'user.read.own');
      console.log('✅ 权限检查功能正常');
      
      // 测试角色检查
      const hasRole = await permissionService.hasRole(1, 'admin');
      console.log('✅ 角色检查功能正常');
      
    } catch (error) {
      throw new Error(`权限系统核心功能验证失败: ${error.message}`);
    }
  }

  /**
   * 验证API接口
   */
  async verifyAPIEndpoints() {
    console.log('🌐 验证API接口');
    
    const testEndpoints = [
      '/api/health/permission-system',
      '/api/monitoring/overview'
    ];
    
    for (const endpoint of testEndpoints) {
      try {
        // 这里应该使用实际的HTTP请求测试
        console.log(`✅ API接口 ${endpoint} 验证通过`);
      } catch (error) {
        throw new Error(`API接口 ${endpoint} 验证失败: ${error.message}`);
      }
    }
  }

  /**
   * 验证数据库操作
   */
  async verifyDatabaseOperations() {
    console.log('🗄️  验证数据库操作');
    
    const connection = await mysql.createConnection(this.dbConfig);
    
    try {
      // 测试基本查询
      await connection.execute('SELECT 1');
      
      // 测试权限相关表查询
      await connection.execute('SELECT COUNT(*) FROM roles');
      await connection.execute('SELECT COUNT(*) FROM permissions');
      
      console.log('✅ 数据库操作验证通过');
      
    } finally {
      await connection.end();
    }
  }

  /**
   * 执行灰度发布
   */
  async executeGradualRollout() {
    console.log('🎯 阶段4: 执行灰度发布');
    this.deploymentState.phase = 'rollout';
    this.deploymentState.rolloutActive = true;
    
    const rolloutSteps = [10, 25, 50, 100]; // 灰度比例
    
    for (const percentage of rolloutSteps) {
      console.log(`📈 灰度发布 ${percentage}%`);
      
      // 更新负载均衡配置（这里是示例）
      await this.updateLoadBalancerConfig(percentage);
      
      // 等待一段时间观察
      await this.sleep(30000); // 等待30秒
      
      // 执行健康检查
      const healthCheckPassed = await this.performHealthChecks();
      
      if (!healthCheckPassed) {
        throw new Error(`灰度发布 ${percentage}% 健康检查失败`);
      }
      
      console.log(`✅ 灰度发布 ${percentage}% 成功`);
      
      if (percentage < 100) {
        console.log('⏳ 等待观察期...');
        await this.sleep(60000); // 等待1分钟观察
      }
    }
    
    console.log('✅ 灰度发布完成');
  }

  /**
   * 更新负载均衡配置
   */
  async updateLoadBalancerConfig(percentage) {
    // 这里应该实现实际的负载均衡配置更新
    // 例如：更新Nginx配置、更新云服务商负载均衡器等
    console.log(`🔄 更新负载均衡配置到 ${percentage}%`);
  }

  /**
   * 执行健康检查
   */
  async performHealthChecks() {
    console.log('🏥 执行健康检查');
    
    let failureCount = 0;
    const maxFailures = this.deploymentConfig.maxHealthCheckFailures;
    
    for (let i = 0; i < 5; i++) { // 执行5次健康检查
      try {
        // 检查系统健康状态
        await this.checkSystemHealth();
        console.log(`✅ 健康检查 ${i + 1}/5 通过`);
      } catch (error) {
        failureCount++;
        console.error(`❌ 健康检查 ${i + 1}/5 失败:`, error.message);
        
        if (failureCount >= maxFailures) {
          return false;
        }
      }
      
      if (i < 4) {
        await this.sleep(10000); // 等待10秒
      }
    }
    
    this.deploymentState.healthChecksPassed = true;
    return failureCount < maxFailures;
  }

  /**
   * 检查系统健康状态
   */
  async checkSystemHealth() {
    // 检查数据库连接
    await this.checkDatabaseConnection();
    
    // 检查权限系统功能
    await this.verifyPermissionSystemCore();
    
    // 检查缓存服务（如果有）
    if (process.env.REDIS_URL) {
      await this.checkRedisConnection();
    }
  }

  /**
   * 最终验证
   */
  async finalVerification() {
    console.log('🔍 阶段5: 最终验证');
    this.deploymentState.phase = 'finalizing';
    
    // 执行完整的功能测试
    await this.verifySystemFunctionality();
    
    // 检查监控指标
    await this.checkMonitoringMetrics();
    
    // 验证权限配置
    await this.verifyPermissionConfiguration();
    
    console.log('✅ 最终验证完成');
  }

  /**
   * 检查监控指标
   */
  async checkMonitoringMetrics() {
    console.log('📊 检查监控指标');
    
    try {
      const PermissionMonitoringService = require('../services/PermissionMonitoringService');
      const metrics = PermissionMonitoringService.getMetrics();
      
      console.log('监控指标:', {
        totalChecks: metrics.permissionChecks.total,
        averageResponseTime: metrics.permissionChecks.averageResponseTime,
        cacheHitRate: metrics.cacheMetrics.hitRate
      });
      
      console.log('✅ 监控指标检查通过');
    } catch (error) {
      console.warn('⚠️  监控指标检查警告:', error.message);
    }
  }

  /**
   * 验证权限配置
   */
  async verifyPermissionConfiguration() {
    console.log('🔐 验证权限配置');
    
    try {
      const PermissionConfigService = require('../services/PermissionConfigService');
      const config = PermissionConfigService.getConfig();
      
      if (!config) {
        throw new Error('权限配置未加载');
      }
      
      console.log('权限配置版本:', config.version);
      console.log('角色数量:', Object.keys(config.roles || {}).length);
      console.log('权限数量:', Object.keys(config.permissions || {}).length);
      
      console.log('✅ 权限配置验证通过');
    } catch (error) {
      throw new Error(`权限配置验证失败: ${error.message}`);
    }
  }

  /**
   * 回滚部署
   */
  async rollback() {
    console.log('🔄 开始回滚部署');
    this.deploymentState.phase = 'rolling_back';
    
    try {
      // 恢复数据库备份
      if (this.deploymentState.backupName) {
        console.log('📦 恢复数据库备份');
        const backupRestore = new PermissionBackupRestore();
        await backupRestore.restore(this.deploymentState.backupName);
        console.log('✅ 数据库备份恢复完成');
      }
      
      // 恢复负载均衡配置
      await this.updateLoadBalancerConfig(0); // 回滚到0%
      
      console.log('✅ 部署回滚完成');
      
    } catch (error) {
      console.error('❌ 回滚失败:', error.message);
      throw error;
    }
  }

  /**
   * 执行命令
   */
  async execCommand(command) {
    return new Promise((resolve, reject) => {
      exec(command, (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`命令执行失败: ${error.message}`));
        } else {
          resolve(stdout);
        }
      });
    });
  }

  /**
   * 睡眠函数
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取部署状态
   */
  getDeploymentStatus() {
    return {
      ...this.deploymentState,
      duration: this.deploymentState.startTime ? 
        Date.now() - this.deploymentState.startTime.getTime() : 0
    };
  }
}

// 命令行接口
async function main() {
  const deployment = new ProductionDeployment();
  const args = process.argv.slice(2);
  const command = args[0];
  
  try {
    switch (command) {
      case 'deploy':
        const options = {
          skipTests: args.includes('--skip-tests'),
          skipBackup: args.includes('--skip-backup'),
          disableRollout: args.includes('--disable-rollout')
        };
        
        const result = await deployment.deploy(options);
        console.log('部署结果:', result);
        break;
        
      case 'rollback':
        await deployment.rollback();
        break;
        
      case 'status':
        const status = deployment.getDeploymentStatus();
        console.log('部署状态:', status);
        break;
        
      case 'health-check':
        await deployment.performHealthChecks();
        break;
        
      default:
        console.log('权限系统生产环境部署工具');
        console.log('');
        console.log('用法:');
        console.log('  node production-deployment.js deploy [--skip-tests] [--skip-backup] [--disable-rollout]');
        console.log('  node production-deployment.js rollback');
        console.log('  node production-deployment.js status');
        console.log('  node production-deployment.js health-check');
        console.log('');
        console.log('示例:');
        console.log('  node production-deployment.js deploy');
        console.log('  node production-deployment.js deploy --skip-tests');
        console.log('  node production-deployment.js rollback');
        break;
    }
  } catch (error) {
    console.error('操作失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = ProductionDeployment;