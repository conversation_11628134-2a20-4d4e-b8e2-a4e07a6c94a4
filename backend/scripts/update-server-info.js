#!/usr/bin/env node

/**
 * 服务器信息更新脚本
 * 参考PHP系统的update_site_server_ip.php
 * 更新网站的服务器IP、CDN状态、地理位置等信息
 */

const mysql = require('mysql2/promise');
const dns = require('dns').promises;
const axios = require('axios');

class ServerInfoUpdateScript {
  constructor() {
    this.db = null;
    this.config = {
      batchSize: 50,              // 批处理大小
      timeout: 10000,             // 请求超时时间
      maxRetries: 3,              // 最大重试次数
      geoApiUrl: 'http://ip-api.com/json/', // IP地理位置查询API
      concurrency: 10             // 并发处理数
    };
    
    this.stats = {
      totalProcessed: 0,
      successCount: 0,
      failureCount: 0,
      ipUpdated: 0,
      cdnDetected: 0,
      locationUpdated: 0
    };
  }

  /**
   * 初始化
   */
  async initialize() {
    try {
      console.log('🔧 初始化服务器信息更新脚本...');
      
      // 创建数据库连接
      this.db = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'sitemanager',
        charset: 'utf8mb4'
      });
      
      console.log('✅ 数据库连接成功');
      
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行服务器信息更新
   */
  async run() {
    try {
      console.log('🚀 开始更新服务器信息...');
      
      const startTime = Date.now();
      
      // 获取需要更新的网站列表
      const websites = await this.getWebsitesToUpdate();
      
      if (websites.length === 0) {
        console.log('ℹ️  没有需要更新的网站');
        return;
      }
      
      console.log(`📊 找到 ${websites.length} 个网站需要更新服务器信息`);
      
      // 分批处理
      const batches = this.createBatches(websites, this.config.batchSize);
      
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        console.log(`处理批次 ${i + 1}/${batches.length} (${batch.length} 个网站)`);
        
        await this.processBatch(batch);
        
        // 批次间短暂延迟
        if (i < batches.length - 1) {
          await this.sleep(1000);
        }
      }
      
      const duration = Date.now() - startTime;
      console.log(`✅ 服务器信息更新完成，耗时: ${duration}ms`);
      
      // 输出统计信息
      this.printStats();
      
    } catch (error) {
      console.error('❌ 服务器信息更新失败:', error);
      throw error;
    }
  }

  /**
   * 获取需要更新的网站列表
   */
  async getWebsitesToUpdate() {
    try {
      // 查询活跃网站，优先更新长时间未更新的网站
      const [websites] = await this.db.execute(`
        SELECT 
          id, site_name, domain, site_url,
          server_ip, cdn_type, location,
          last_check_time, updated_at
        FROM websites 
        WHERE status = 'active' 
          AND site_url IS NOT NULL 
          AND site_url != ''
          AND (
            server_ip IS NULL 
            OR cdn_type IS NULL 
            OR location IS NULL
            OR updated_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
          )
        ORDER BY 
          CASE 
            WHEN server_ip IS NULL THEN 1
            WHEN cdn_type IS NULL THEN 2
            WHEN location IS NULL THEN 3
            ELSE 4
          END,
          updated_at ASC
        LIMIT ?
      `, [this.config.batchSize * 10]); // 获取更多数据以便批处理
      
      return websites;
      
    } catch (error) {
      console.error('❌ 获取网站列表失败:', error);
      throw error;
    }
  }

  /**
   * 处理批次
   */
  async processBatch(websites) {
    try {
      // 创建并发任务
      const tasks = websites.map(website => this.updateWebsiteInfo(website));
      
      // 限制并发数
      const results = await this.limitConcurrency(tasks, this.config.concurrency);
      
      // 统计结果
      results.forEach(result => {
        this.stats.totalProcessed++;
        if (result.success) {
          this.stats.successCount++;
          if (result.ipUpdated) this.stats.ipUpdated++;
          if (result.cdnDetected) this.stats.cdnDetected++;
          if (result.locationUpdated) this.stats.locationUpdated++;
        } else {
          this.stats.failureCount++;
        }
      });
      
    } catch (error) {
      console.error('❌ 批次处理失败:', error);
    }
  }

  /**
   * 更新单个网站信息
   */
  async updateWebsiteInfo(website) {
    try {
      const result = {
        websiteId: website.id,
        success: false,
        ipUpdated: false,
        cdnDetected: false,
        locationUpdated: false,
        error: null
      };
      
      console.log(`🔍 更新网站信息: ${website.domain}`);
      
      // 1. DNS解析获取IP地址
      const ipInfo = await this.resolveIP(website.domain);
      if (ipInfo.success && ipInfo.ip !== website.server_ip) {
        result.ipUpdated = true;
      }
      
      // 2. HTTP请求检测CDN
      const cdnInfo = await this.detectCDN(website.site_url);
      if (cdnInfo.success && cdnInfo.cdn !== website.cdn_type) {
        result.cdnDetected = true;
      }
      
      // 3. 获取IP地理位置信息
      let locationInfo = { success: false, location: null };
      if (ipInfo.success && ipInfo.ip) {
        locationInfo = await this.getIPLocation(ipInfo.ip);
        if (locationInfo.success && locationInfo.location !== website.location) {
          result.locationUpdated = true;
        }
      }
      
      // 4. 更新数据库
      if (result.ipUpdated || result.cdnDetected || result.locationUpdated) {
        await this.updateDatabase(website.id, {
          serverIp: ipInfo.ip,
          cdnType: cdnInfo.cdn,
          location: locationInfo.location
        });
      }
      
      result.success = true;
      return result;
      
    } catch (error) {
      console.error(`❌ 更新网站信息失败 ${website.domain}:`, error.message);
      return {
        websiteId: website.id,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * DNS解析获取IP地址
   */
  async resolveIP(domain) {
    try {
      const addresses = await dns.resolve4(domain);
      return {
        success: true,
        ip: addresses[0],
        addresses
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检测CDN（参考PHP系统逻辑）
   */
  async detectCDN(url) {
    try {
      const response = await axios.head(url, {
        timeout: this.config.timeout,
        validateStatus: () => true // 接受所有状态码
      });
      
      const headers = response.headers;
      let cdn = 'no';
      
      // 参考PHP系统的CDN检测逻辑
      if (headers['x-qc-cache']) {
        cdn = 'Quic.cloud';
      } else if (headers['cf-cache-status'] || headers['cf-ray']) {
        cdn = 'Cloudflare';
      } else if (headers['x-swift-cachetime']) {
        cdn = '阿里云';
      } else if (headers['eo-cache-status']) {
        cdn = '腾讯云';
      } else if (headers['x-cache'] && headers['x-cache'].includes('HIT')) {
        cdn = 'Generic CDN';
      } else if (headers['server'] && headers['server'].toLowerCase().includes('nginx')) {
        cdn = 'Nginx';
      } else if (headers['server'] && headers['server'].toLowerCase().includes('apache')) {
        cdn = 'Apache';
      }
      
      return {
        success: true,
        cdn,
        headers
      };
      
    } catch (error) {
      return {
        success: false,
        cdn: 'no',
        error: error.message
      };
    }
  }

  /**
   * 获取IP地理位置信息
   */
  async getIPLocation(ip) {
    try {
      // 跳过私有IP地址
      if (this.isPrivateIP(ip)) {
        return {
          success: true,
          location: '内网IP'
        };
      }
      
      const response = await axios.get(`${this.config.geoApiUrl}${ip}`, {
        timeout: this.config.timeout,
        params: {
          lang: 'zh-CN'
        }
      });
      
      if (response.data.status === 'success') {
        const location = `${response.data.country || ''}${response.data.regionName || ''}${response.data.city || ''}`;
        return {
          success: true,
          location: location || '未知',
          data: response.data
        };
      } else {
        return {
          success: false,
          error: response.data.message || '地理位置查询失败'
        };
      }
      
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 判断是否为私有IP地址
   */
  isPrivateIP(ip) {
    const parts = ip.split('.').map(Number);
    
    // 10.0.0.0/8
    if (parts[0] === 10) return true;
    
    // **********/12
    if (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) return true;
    
    // ***********/16
    if (parts[0] === 192 && parts[1] === 168) return true;
    
    // *********/8 (localhost)
    if (parts[0] === 127) return true;
    
    return false;
  }

  /**
   * 更新数据库
   */
  async updateDatabase(websiteId, info) {
    try {
      const updates = [];
      const values = [];
      
      if (info.serverIp) {
        updates.push('server_ip = ?');
        values.push(info.serverIp);
      }
      
      if (info.cdnType) {
        updates.push('cdn_type = ?');
        values.push(info.cdnType);
      }
      
      if (info.location) {
        updates.push('location = ?');
        values.push(info.location);
      }
      
      if (updates.length > 0) {
        updates.push('updated_at = NOW()');
        values.push(websiteId);
        
        await this.db.execute(
          `UPDATE websites SET ${updates.join(', ')} WHERE id = ?`,
          values
        );
      }
      
    } catch (error) {
      console.error(`❌ 更新数据库失败 (ID: ${websiteId}):`, error);
      throw error;
    }
  }

  /**
   * 限制并发数
   */
  async limitConcurrency(tasks, limit) {
    const results = [];
    
    for (let i = 0; i < tasks.length; i += limit) {
      const batch = tasks.slice(i, i + limit);
      const batchResults = await Promise.allSettled(batch);
      
      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            error: result.reason.message
          });
        }
      });
    }
    
    return results;
  }

  /**
   * 创建批次
   */
  createBatches(array, batchSize) {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 输出统计信息
   */
  printStats() {
    console.log('\n📊 服务器信息更新统计:');
    console.log(`  总处理数: ${this.stats.totalProcessed}`);
    console.log(`  成功数: ${this.stats.successCount}`);
    console.log(`  失败数: ${this.stats.failureCount}`);
    console.log(`  IP更新数: ${this.stats.ipUpdated}`);
    console.log(`  CDN检测数: ${this.stats.cdnDetected}`);
    console.log(`  位置更新数: ${this.stats.locationUpdated}`);
    
    if (this.stats.totalProcessed > 0) {
      const successRate = (this.stats.successCount / this.stats.totalProcessed * 100).toFixed(2);
      console.log(`  成功率: ${successRate}%`);
    }
  }

  /**
   * 工具方法
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.db) {
        await this.db.end();
        console.log('✅ 数据库连接已关闭');
      }
    } catch (error) {
      console.error('⚠️  清理资源失败:', error);
    }
  }

  /**
   * 主执行方法
   */
  async execute() {
    try {
      await this.initialize();
      await this.run();
    } catch (error) {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// 检查是否在CLI模式下运行
if (require.main === module) {
  const script = new ServerInfoUpdateScript();
  
  // 处理进程信号
  process.on('SIGINT', async () => {
    console.log('\n🛑 接收到中断信号，正在清理...');
    await script.cleanup();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    console.log('\n🛑 接收到终止信号，正在清理...');
    await script.cleanup();
    process.exit(0);
  });
  
  // 执行脚本
  script.execute()
    .then(() => {
      console.log('🎉 服务器信息更新脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 服务器信息更新脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = ServerInfoUpdateScript;
