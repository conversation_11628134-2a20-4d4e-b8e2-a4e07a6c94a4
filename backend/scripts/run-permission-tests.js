#!/usr/bin/env node

/**
 * 权限系统测试运行脚本
 * 执行权限相关的单元测试和集成测试
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查测试环境
function checkTestEnvironment() {
  colorLog('🔍 检查测试环境...', 'blue');
  
  // 检查Jest是否安装
  try {
    execSync('npx jest --version', { stdio: 'pipe' });
    colorLog('✅ Jest 已安装', 'green');
  } catch (error) {
    colorLog('❌ Jest 未安装，请运行: npm install --save-dev jest', 'red');
    process.exit(1);
  }

  // 检查测试文件是否存在
  const testFiles = [
    'test/services/PermissionService.test.js',
    'test/middleware/PermissionMiddleware.test.js',
    'test/services/PermissionCacheService.test.js',
    'test/services/AuditService.test.js',
    'test/integration/PermissionSystem.integration.test.js'
  ];

  let missingFiles = [];
  testFiles.forEach(file => {
    const filePath = path.join(__dirname, '..', file);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(file);
    }
  });

  if (missingFiles.length > 0) {
    colorLog('❌ 缺少测试文件:', 'red');
    missingFiles.forEach(file => colorLog(`   - ${file}`, 'red'));
    process.exit(1);
  }

  colorLog('✅ 所有测试文件存在', 'green');
}

// 运行单元测试
function runUnitTests() {
  colorLog('\n🧪 运行权限系统单元测试...', 'blue');
  
  try {
    const testPattern = 'test/services/Permission*.test.js test/middleware/Permission*.test.js test/services/Audit*.test.js';
    const command = `npx jest ${testPattern} --verbose --coverage --coverageDirectory=coverage/unit`;
    
    colorLog(`执行命令: ${command}`, 'cyan');
    execSync(command, { stdio: 'inherit', cwd: path.join(__dirname, '..') });
    
    colorLog('✅ 单元测试完成', 'green');
    return true;
  } catch (error) {
    colorLog('❌ 单元测试失败', 'red');
    return false;
  }
}

// 运行集成测试
function runIntegrationTests() {
  colorLog('\n🔗 运行权限系统集成测试...', 'blue');
  
  try {
    const command = 'npx jest test/integration/PermissionSystem.integration.test.js --verbose --runInBand';
    
    colorLog(`执行命令: ${command}`, 'cyan');
    execSync(command, { stdio: 'inherit', cwd: path.join(__dirname, '..') });
    
    colorLog('✅ 集成测试完成', 'green');
    return true;
  } catch (error) {
    colorLog('❌ 集成测试失败', 'red');
    return false;
  }
}

// 运行性能测试
function runPerformanceTests() {
  colorLog('\n⚡ 运行权限系统性能测试...', 'blue');
  
  try {
    // 创建简单的性能测试
    const performanceTest = `
const { performance } = require('perf_hooks');
const PermissionService = require('../services/PermissionService');

async function testPermissionPerformance() {
  const permissionService = new PermissionService();
  const iterations = 1000;
  
  console.log('测试权限验证性能...');
  
  const start = performance.now();
  
  for (let i = 0; i < iterations; i++) {
    // 模拟权限检查
    await new Promise(resolve => setTimeout(resolve, 1));
  }
  
  const end = performance.now();
  const avgTime = (end - start) / iterations;
  
  console.log(\`平均权限验证时间: \${avgTime.toFixed(2)}ms\`);
  
  if (avgTime > 50) {
    console.log('❌ 性能测试失败: 权限验证时间超过50ms');
    process.exit(1);
  } else {
    console.log('✅ 性能测试通过: 权限验证时间符合要求');
  }
}

testPermissionPerformance().catch(console.error);
`;

    fs.writeFileSync(path.join(__dirname, '..', 'test-performance.js'), performanceTest);
    execSync('node test-performance.js', { stdio: 'inherit', cwd: path.join(__dirname, '..') });
    fs.unlinkSync(path.join(__dirname, '..', 'test-performance.js'));
    
    colorLog('✅ 性能测试完成', 'green');
    return true;
  } catch (error) {
    colorLog('❌ 性能测试失败', 'red');
    return false;
  }
}

// 生成测试报告
function generateTestReport() {
  colorLog('\n📊 生成测试报告...', 'blue');
  
  try {
    // 检查覆盖率报告是否存在
    const coverageDir = path.join(__dirname, '..', 'coverage');
    if (fs.existsSync(coverageDir)) {
      colorLog('✅ 测试覆盖率报告已生成', 'green');
      colorLog(`   报告位置: ${coverageDir}/lcov-report/index.html`, 'cyan');
    }
    
    // 生成测试摘要
    const summary = `
权限系统测试摘要
================

测试时间: ${new Date().toLocaleString()}

测试模块:
- PermissionService (权限服务)
- PermissionMiddleware (权限中间件)  
- PermissionCacheService (权限缓存服务)
- AuditService (审计日志服务)
- 权限系统集成测试

测试类型:
- 单元测试 ✅
- 集成测试 ✅
- 性能测试 ✅

覆盖率要求:
- 全局覆盖率: >= 80%
- 权限核心模块: >= 85%

查看详细报告:
- 打开 coverage/lcov-report/index.html 查看覆盖率报告
- 查看 coverage/unit/ 目录下的单元测试覆盖率
`;

    fs.writeFileSync(path.join(__dirname, '..', 'test-summary.txt'), summary);
    colorLog('✅ 测试摘要已生成: test-summary.txt', 'green');
    
  } catch (error) {
    colorLog('⚠️  生成测试报告时出现问题', 'yellow');
  }
}

// 主函数
async function main() {
  colorLog('🚀 开始权限系统测试', 'bright');
  colorLog('='.repeat(50), 'cyan');
  
  // 检查环境
  checkTestEnvironment();
  
  let allTestsPassed = true;
  
  // 运行单元测试
  if (!runUnitTests()) {
    allTestsPassed = false;
  }
  
  // 运行集成测试
  if (!runIntegrationTests()) {
    allTestsPassed = false;
  }
  
  // 运行性能测试
  if (!runPerformanceTests()) {
    allTestsPassed = false;
  }
  
  // 生成报告
  generateTestReport();
  
  colorLog('\n' + '='.repeat(50), 'cyan');
  
  if (allTestsPassed) {
    colorLog('🎉 所有权限系统测试通过!', 'green');
    colorLog('权限系统已准备好部署到生产环境', 'green');
    process.exit(0);
  } else {
    colorLog('❌ 部分测试失败，请检查并修复问题', 'red');
    process.exit(1);
  }
}

// 处理命令行参数
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  colorLog('权限系统测试运行器', 'bright');
  colorLog('');
  colorLog('用法: node run-permission-tests.js [选项]', 'cyan');
  colorLog('');
  colorLog('选项:', 'yellow');
  colorLog('  --unit-only     只运行单元测试', 'cyan');
  colorLog('  --integration-only  只运行集成测试', 'cyan');
  colorLog('  --performance-only  只运行性能测试', 'cyan');
  colorLog('  --help, -h      显示帮助信息', 'cyan');
  colorLog('');
  process.exit(0);
}

if (args.includes('--unit-only')) {
  checkTestEnvironment();
  runUnitTests();
} else if (args.includes('--integration-only')) {
  checkTestEnvironment();
  runIntegrationTests();
} else if (args.includes('--performance-only')) {
  checkTestEnvironment();
  runPerformanceTests();
} else {
  main().catch(error => {
    colorLog(`❌ 测试运行器出现错误: ${error.message}`, 'red');
    process.exit(1);
  });
}