#!/usr/bin/env node

/**
 * 续费提醒通知脚本
 * 参考PHP系统的renewal_notice.php
 * 检测即将到期的网站并发送续费提醒
 */

const mysql = require('mysql2/promise');
const EnhancedNotificationService = require('../services/EnhancedNotificationService');

class RenewalNotificationScript {
  constructor() {
    this.db = null;
    this.notificationService = null;
    this.config = {
      // 参考PHP系统配置
      warningDays: 20,            // 20天内到期提醒
      batchSize: 50,              // 批处理大小
      includeInactive: false      // 是否包含非活跃网站
    };
  }

  /**
   * 初始化
   */
  async initialize() {
    try {
      console.log('🔧 初始化续费提醒脚本...');
      
      // 创建数据库连接
      this.db = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'sitemanager',
        charset: 'utf8mb4'
      });
      
      console.log('✅ 数据库连接成功');
      
      // 初始化通知服务
      this.notificationService = new EnhancedNotificationService(this.db);
      await this.notificationService.initialize();
      
      console.log('✅ 通知服务初始化成功');
      
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行续费提醒检查
   */
  async run() {
    try {
      console.log('🚀 开始执行续费提醒检查...');
      console.log(`配置: 提醒天数=${this.config.warningDays}天`);
      
      const startTime = Date.now();
      
      // 发送续费提醒通知
      await this.notificationService.sendRenewalNotification();
      
      // 发送SSL证书到期提醒
      await this.notificationService.sendSSLExpiryNotification();
      
      const duration = Date.now() - startTime;
      console.log(`✅ 续费提醒检查完成，耗时: ${duration}ms`);
      
      // 输出统计信息
      const stats = this.notificationService.getStats();
      console.log('📊 通知统计:', {
        totalSent: stats.totalSent,
        successCount: stats.successCount,
        failureCount: stats.failureCount,
        successRate: stats.successRate
      });
      
    } catch (error) {
      console.error('❌ 续费提醒检查失败:', error);
      throw error;
    }
  }

  /**
   * 生成续费报告
   */
  async generateRenewalReport() {
    try {
      console.log('📊 生成续费报告...');
      
      // 查询即将到期的网站
      const [expiringSites] = await this.db.execute(`
        SELECT 
          w.id, w.site_name, w.domain, w.site_url,
          w.expire_date, w.renewal_fee, w.project_amount,
          DATEDIFF(w.expire_date, CURDATE()) as days_until_expiry,
          p.name as platform_name,
          s.name as server_name
        FROM websites w
        LEFT JOIN platforms p ON w.platform_id = p.id
        LEFT JOIN servers s ON w.server_id = s.id
        WHERE w.status = 'active'
          AND w.expire_date IS NOT NULL
          AND w.expire_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
        ORDER BY w.expire_date ASC
      `, [this.config.warningDays]);
      
      // 查询SSL证书即将到期的网站
      const [sslExpiringSites] = await this.db.execute(`
        SELECT 
          w.id, w.site_name, w.domain, w.site_url,
          w.ssl_expire_date, w.ssl_status, w.ssl_issuer,
          DATEDIFF(w.ssl_expire_date, CURDATE()) as days_until_expiry
        FROM websites w
        WHERE w.status = 'active'
          AND w.ssl_check = 1
          AND w.ssl_expire_date IS NOT NULL
          AND w.ssl_expire_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ORDER BY w.ssl_expire_date ASC
      `);
      
      const report = {
        generatedAt: new Date().toISOString(),
        summary: {
          totalExpiring: expiringSites.length,
          totalSSLExpiring: sslExpiringSites.length,
          warningDays: this.config.warningDays
        },
        expiringSites: expiringSites.map(site => ({
          id: site.id,
          name: site.site_name,
          domain: site.domain,
          expireDate: site.expire_date,
          daysUntilExpiry: site.days_until_expiry,
          renewalFee: site.renewal_fee,
          platform: site.platform_name,
          server: site.server_name
        })),
        sslExpiringSites: sslExpiringSites.map(site => ({
          id: site.id,
          name: site.site_name,
          domain: site.domain,
          sslExpireDate: site.ssl_expire_date,
          daysUntilExpiry: site.days_until_expiry,
          issuer: site.ssl_issuer
        }))
      };
      
      console.log('📋 续费报告摘要:');
      console.log(`  - 即将到期网站: ${report.summary.totalExpiring} 个`);
      console.log(`  - SSL证书即将到期: ${report.summary.totalSSLExpiring} 个`);
      
      return report;
      
    } catch (error) {
      console.error('❌ 生成续费报告失败:', error);
      throw error;
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.db) {
        await this.db.end();
        console.log('✅ 数据库连接已关闭');
      }
    } catch (error) {
      console.error('⚠️  清理资源失败:', error);
    }
  }

  /**
   * 主执行方法
   */
  async execute() {
    try {
      await this.initialize();
      await this.run();
      
      // 生成报告（可选）
      if (process.argv.includes('--report')) {
        const report = await this.generateRenewalReport();
        console.log('\n📊 详细报告:');
        console.log(JSON.stringify(report, null, 2));
      }
      
    } catch (error) {
      console.error('💥 脚本执行失败:', error);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// 检查是否在CLI模式下运行
if (require.main === module) {
  const script = new RenewalNotificationScript();
  
  // 处理命令行参数
  if (process.argv.includes('--help')) {
    console.log(`
续费提醒通知脚本

用法: node renewal-notification.js [选项]

选项:
  --report    生成详细的续费报告
  --help      显示帮助信息

示例:
  node renewal-notification.js
  node renewal-notification.js --report
    `);
    process.exit(0);
  }
  
  // 处理进程信号
  process.on('SIGINT', async () => {
    console.log('\n🛑 接收到中断信号，正在清理...');
    await script.cleanup();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    console.log('\n🛑 接收到终止信号，正在清理...');
    await script.cleanup();
    process.exit(0);
  });
  
  // 执行脚本
  script.execute()
    .then(() => {
      console.log('🎉 续费提醒脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 续费提醒脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = RenewalNotificationScript;
