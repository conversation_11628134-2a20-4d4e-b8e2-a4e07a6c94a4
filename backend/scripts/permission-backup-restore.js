#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

/**
 * 权限系统备份和恢复脚本
 * 用于备份和恢复权限相关的数据库数据和配置文件
 */

class PermissionBackupRestore {
  constructor() {
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'website_management',
      charset: 'utf8mb4'
    };
    
    this.backupDir = path.join(__dirname, '../backups');
    this.configDir = path.join(__dirname, '../config');
    
    // 确保备份目录存在
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }
  }

  /**
   * 创建数据库连接
   */
  async createConnection() {
    try {
      const connection = await mysql.createConnection(this.dbConfig);
      return connection;
    } catch (error) {
      console.error('数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 备份权限系统数据
   */
  async backup(options = {}) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = options.name || `permission_backup_${timestamp}`;
    const backupPath = path.join(this.backupDir, backupName);
    
    console.log(`开始备份权限系统数据到: ${backupPath}`);
    
    try {
      // 创建备份目录
      if (!fs.existsSync(backupPath)) {
        fs.mkdirSync(backupPath, { recursive: true });
      }
      
      // 备份数据库数据
      await this.backupDatabase(backupPath);
      
      // 备份配置文件
      await this.backupConfigs(backupPath);
      
      // 创建备份元数据
      const metadata = {
        backupName,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        description: options.description || '权限系统数据备份',
        tables: [
          'users',
          'roles', 
          'permissions',
          'role_permissions',
          'user_custom_permissions',
          'audit_logs'
        ],
        configs: [
          'permissions.json',
          'permissions.production.json'
        ]
      };
      
      fs.writeFileSync(
        path.join(backupPath, 'metadata.json'),
        JSON.stringify(metadata, null, 2)
      );
      
      console.log(`权限系统备份完成: ${backupName}`);
      return { success: true, backupPath, metadata };
      
    } catch (error) {
      console.error('备份失败:', error);
      throw error;
    }
  }

  /**
   * 备份数据库数据
   */
  async backupDatabase(backupPath) {
    const connection = await this.createConnection();
    
    try {
      const tables = [
        'users',
        'roles',
        'permissions', 
        'role_permissions',
        'user_custom_permissions',
        'audit_logs'
      ];
      
      for (const table of tables) {
        console.log(`备份表: ${table}`);
        
        try {
          // 获取表结构
          const [createTableResult] = await connection.execute(`SHOW CREATE TABLE ${table}`);
          const createTableSQL = createTableResult[0]['Create Table'];
          
          // 获取表数据
          const [rows] = await connection.execute(`SELECT * FROM ${table}`);
          
          // 生成备份SQL
          let backupSQL = `-- 表结构: ${table}\n`;
          backupSQL += `DROP TABLE IF EXISTS ${table};\n`;
          backupSQL += createTableSQL + ';\n\n';
          
          if (rows.length > 0) {
            backupSQL += `-- 表数据: ${table}\n`;
            backupSQL += `INSERT INTO ${table} VALUES\n`;
            
            const values = rows.map(row => {
              const escapedValues = Object.values(row).map(value => {
                if (value === null) return 'NULL';
                if (typeof value === 'string') {
                  return `'${value.replace(/'/g, "''")}'`;
                }
                if (value instanceof Date) {
                  return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
                }
                return value;
              });
              return `(${escapedValues.join(', ')})`;
            });
            
            backupSQL += values.join(',\n') + ';\n\n';
          }
          
          // 写入备份文件
          fs.writeFileSync(
            path.join(backupPath, `${table}.sql`),
            backupSQL
          );
          
        } catch (error) {
          if (error.code === 'ER_NO_SUCH_TABLE') {
            console.warn(`表 ${table} 不存在，跳过备份`);
          } else {
            throw error;
          }
        }
      }
      
    } finally {
      await connection.end();
    }
  }

  /**
   * 备份配置文件
   */
  async backupConfigs(backupPath) {
    const configFiles = [
      'permissions.json',
      'permissions.production.json'
    ];
    
    const configBackupPath = path.join(backupPath, 'configs');
    if (!fs.existsSync(configBackupPath)) {
      fs.mkdirSync(configBackupPath);
    }
    
    for (const configFile of configFiles) {
      const sourcePath = path.join(this.configDir, configFile);
      const targetPath = path.join(configBackupPath, configFile);
      
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`配置文件已备份: ${configFile}`);
      } else {
        console.warn(`配置文件不存在: ${configFile}`);
      }
    }
  }

  /**
   * 恢复权限系统数据
   */
  async restore(backupName, options = {}) {
    const backupPath = path.join(this.backupDir, backupName);
    
    if (!fs.existsSync(backupPath)) {
      throw new Error(`备份不存在: ${backupName}`);
    }
    
    console.log(`开始恢复权限系统数据从: ${backupPath}`);
    
    try {
      // 读取备份元数据
      const metadataPath = path.join(backupPath, 'metadata.json');
      if (!fs.existsSync(metadataPath)) {
        throw new Error('备份元数据文件不存在');
      }
      
      const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      console.log(`恢复备份: ${metadata.backupName} (${metadata.timestamp})`);
      
      // 恢复数据库数据
      if (!options.configOnly) {
        await this.restoreDatabase(backupPath, metadata.tables);
      }
      
      // 恢复配置文件
      if (!options.dataOnly) {
        await this.restoreConfigs(backupPath);
      }
      
      console.log('权限系统数据恢复完成');
      return { success: true, metadata };
      
    } catch (error) {
      console.error('恢复失败:', error);
      throw error;
    }
  }

  /**
   * 恢复数据库数据
   */
  async restoreDatabase(backupPath, tables) {
    const connection = await this.createConnection();
    
    try {
      // 禁用外键检查
      await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
      
      for (const table of tables) {
        const sqlFile = path.join(backupPath, `${table}.sql`);
        
        if (fs.existsSync(sqlFile)) {
          console.log(`恢复表: ${table}`);
          
          const sql = fs.readFileSync(sqlFile, 'utf8');
          const statements = sql.split(';').filter(stmt => stmt.trim());
          
          for (const statement of statements) {
            if (statement.trim()) {
              await connection.execute(statement);
            }
          }
        } else {
          console.warn(`表备份文件不存在: ${table}.sql`);
        }
      }
      
      // 重新启用外键检查
      await connection.execute('SET FOREIGN_KEY_CHECKS = 1');
      
    } finally {
      await connection.end();
    }
  }

  /**
   * 恢复配置文件
   */
  async restoreConfigs(backupPath) {
    const configBackupPath = path.join(backupPath, 'configs');
    
    if (!fs.existsSync(configBackupPath)) {
      console.warn('配置备份目录不存在');
      return;
    }
    
    const configFiles = fs.readdirSync(configBackupPath);
    
    for (const configFile of configFiles) {
      const sourcePath = path.join(configBackupPath, configFile);
      const targetPath = path.join(this.configDir, configFile);
      
      // 备份现有配置文件
      if (fs.existsSync(targetPath)) {
        const backupConfigPath = `${targetPath}.backup.${Date.now()}`;
        fs.copyFileSync(targetPath, backupConfigPath);
        console.log(`现有配置文件已备份: ${backupConfigPath}`);
      }
      
      fs.copyFileSync(sourcePath, targetPath);
      console.log(`配置文件已恢复: ${configFile}`);
    }
  }

  /**
   * 列出所有备份
   */
  listBackups() {
    if (!fs.existsSync(this.backupDir)) {
      return [];
    }
    
    const backups = [];
    const items = fs.readdirSync(this.backupDir);
    
    for (const item of items) {
      const itemPath = path.join(this.backupDir, item);
      const metadataPath = path.join(itemPath, 'metadata.json');
      
      if (fs.statSync(itemPath).isDirectory() && fs.existsSync(metadataPath)) {
        try {
          const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
          backups.push({
            name: item,
            ...metadata,
            size: this.getDirectorySize(itemPath)
          });
        } catch (error) {
          console.warn(`读取备份元数据失败: ${item}`);
        }
      }
    }
    
    return backups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }

  /**
   * 删除备份
   */
  deleteBackup(backupName) {
    const backupPath = path.join(this.backupDir, backupName);
    
    if (!fs.existsSync(backupPath)) {
      throw new Error(`备份不存在: ${backupName}`);
    }
    
    this.removeDirectory(backupPath);
    console.log(`备份已删除: ${backupName}`);
  }

  /**
   * 获取目录大小
   */
  getDirectorySize(dirPath) {
    let size = 0;
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        size += this.getDirectorySize(itemPath);
      } else {
        size += stats.size;
      }
    }
    
    return size;
  }

  /**
   * 递归删除目录
   */
  removeDirectory(dirPath) {
    if (fs.existsSync(dirPath)) {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        
        if (fs.statSync(itemPath).isDirectory()) {
          this.removeDirectory(itemPath);
        } else {
          fs.unlinkSync(itemPath);
        }
      }
      
      fs.rmdirSync(dirPath);
    }
  }
}

// 命令行接口
async function main() {
  const backupRestore = new PermissionBackupRestore();
  const args = process.argv.slice(2);
  const command = args[0];
  
  try {
    switch (command) {
      case 'backup':
        const backupOptions = {
          name: args[1],
          description: args[2]
        };
        const backupResult = await backupRestore.backup(backupOptions);
        console.log('备份成功:', backupResult.metadata.backupName);
        break;
        
      case 'restore':
        if (!args[1]) {
          console.error('请指定要恢复的备份名称');
          process.exit(1);
        }
        const restoreOptions = {
          configOnly: args.includes('--config-only'),
          dataOnly: args.includes('--data-only')
        };
        await backupRestore.restore(args[1], restoreOptions);
        console.log('恢复成功');
        break;
        
      case 'list':
        const backups = backupRestore.listBackups();
        console.log('\n可用的备份:');
        console.log('名称\t\t\t时间\t\t\t大小\t描述');
        console.log('-'.repeat(80));
        for (const backup of backups) {
          const size = (backup.size / 1024).toFixed(2) + ' KB';
          console.log(`${backup.name}\t${backup.timestamp}\t${size}\t${backup.description || ''}`);
        }
        break;
        
      case 'delete':
        if (!args[1]) {
          console.error('请指定要删除的备份名称');
          process.exit(1);
        }
        backupRestore.deleteBackup(args[1]);
        console.log('删除成功');
        break;
        
      default:
        console.log('权限系统备份恢复工具');
        console.log('');
        console.log('用法:');
        console.log('  node permission-backup-restore.js backup [名称] [描述]');
        console.log('  node permission-backup-restore.js restore <备份名称> [--config-only|--data-only]');
        console.log('  node permission-backup-restore.js list');
        console.log('  node permission-backup-restore.js delete <备份名称>');
        console.log('');
        console.log('示例:');
        console.log('  node permission-backup-restore.js backup "pre_upgrade" "升级前备份"');
        console.log('  node permission-backup-restore.js restore permission_backup_2025-01-16');
        console.log('  node permission-backup-restore.js list');
        break;
    }
  } catch (error) {
    console.error('操作失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = PermissionBackupRestore;