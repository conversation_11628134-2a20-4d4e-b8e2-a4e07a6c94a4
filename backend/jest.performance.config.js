/**
 * Jest 性能测试配置文件
 * 专门用于权限系统性能测试
 */

module.exports = {
  // 继承基础配置
  ...require('./jest.config.js'),

  // 性能测试特定配置
  displayName: 'Performance Tests',
  
  // 只运行性能测试
  testMatch: [
    '**/test/performance/**/*.test.js'
  ],

  // 性能测试需要更长的超时时间
  testTimeout: 60000, // 60秒

  // 性能测试不需要覆盖率报告
  collectCoverage: false,

  // 性能测试应该串行运行以获得准确的性能数据
  maxConcurrency: 1,
  maxWorkers: 1,

  // 详细输出性能数据
  verbose: true,

  // 性能测试专用的设置文件
  setupFilesAfterEnv: [
    '<rootDir>/test/setup.js',
    '<rootDir>/test/performance/performance-setup.js'
  ],

  // 性能测试报告器
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: './performance-report',
        filename: 'performance-report.html',
        expand: true,
        pageTitle: '权限系统性能测试报告'
      }
    ]
  ],

  // 全局变量
  globals: {
    'process.env.NODE_ENV': 'performance-test',
    'process.env.PERFORMANCE_TEST': 'true'
  }
};