{"name": "sitemanager-backend", "version": "1.0.0", "description": "WordPress站点管理后台系统 - 后端API", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec \"ts-node -r tsconfig-paths/register\" src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=test/services --testPathPattern=test/middleware", "test:integration": "jest --testPathPattern=test/integration", "test:permission": "jest --testPathPattern=Permission", "test:permission:all": "node scripts/run-permission-tests.js", "test:permission:unit": "node scripts/run-permission-tests.js --unit-only", "test:permission:integration": "node scripts/run-permission-tests.js --integration-only", "test:permission:performance": "node scripts/run-permission-tests.js --performance-only", "test:performance": "jest --config=jest.performance.config.js", "test:performance:basic": "node scripts/run-performance-tests.js basic", "test:performance:stress": "node scripts/run-performance-tests.js stress", "test:performance:all": "node scripts/run-performance-tests.js all --report", "test:stress": "node scripts/permission-stress-test.js", "test:integration:system": "node scripts/system-integration-test.js", "deploy:production": "node scripts/production-deployment.js deploy", "deploy:rollback": "node scripts/production-deployment.js rollback", "deploy:status": "node scripts/production-deployment.js status", "backup:create": "node scripts/permission-backup-restore.js backup", "backup:restore": "node scripts/permission-backup-restore.js restore", "backup:list": "node scripts/permission-backup-restore.js list", "test:ci": "jest --coverage --watchAll=false --ci", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "migrate": "node dist/database/migrate.js", "seed": "node dist/database/seed.js"}, "keywords": ["wordpress", "site-management", "nodejs", "express", "typescript", "mysql"], "author": "Site Manager Team", "license": "MIT", "dependencies": {"@types/archiver": "^6.0.3", "@types/bcrypt": "^5.0.2", "archiver": "^6.0.1", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "chokidar": "^4.0.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "csv-writer": "^1.6.0", "dns": "^0.2.2", "dotenv": "^16.3.1", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "node-cron": "^3.0.3", "node-fetch": "^2.7.0", "node-ssh": "^13.2.1", "nodemailer": "^6.10.1", "redis": "^4.6.10", "request": "^2.88.2", "ssh2": "^1.16.0", "whois-json": "^2.0.4", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.13", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "babel-jest": "^29.7.0", "eslint": "^8.54.0", "jest": "^29.7.0", "jest-html-reporters": "^3.1.7", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}