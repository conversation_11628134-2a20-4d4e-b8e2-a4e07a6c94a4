#!/usr/bin/env node

/**
 * 权限系统测试快速验证脚本
 * 用于验证测试环境和基本功能
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 权限系统测试环境验证');
console.log('='.repeat(50));

// 检查测试文件
const testFiles = [
  'test/services/PermissionService.test.js',
  'test/services/RoleService.test.js', 
  'test/services/PermissionCacheService.test.js',
  'test/middleware/PermissionMiddleware.test.js',
  'test/integration/PermissionSystem.integration.test.js'
];

console.log('\n📁 检查测试文件:');
let allFilesExist = true;

testFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  const exists = fs.existsSync(filePath);
  const status = exists ? '✅' : '❌';
  console.log(`  ${status} ${file}`);
  
  if (!exists) {
    allFilesExist = false;
  }
});

// 检查配置文件
const configFiles = [
  'jest.config.js',
  'test/setup.js',
  'package.json'
];

console.log('\n⚙️  检查配置文件:');
configFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  const exists = fs.existsSync(filePath);
  const status = exists ? '✅' : '❌';
  console.log(`  ${status} ${file}`);
  
  if (!exists) {
    allFilesExist = false;
  }
});

// 检查源文件
const sourceFiles = [
  'services/PermissionService.js',
  'services/RoleService.js',
  'services/PermissionCacheService.js',
  'middleware/PermissionMiddleware.js',
  'services/AuditService.js'
];

console.log('\n📦 检查源文件:');
sourceFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  const exists = fs.existsSync(filePath);
  const status = exists ? '✅' : '❌';
  console.log(`  ${status} ${file}`);
  
  if (!exists) {
    allFilesExist = false;
  }
});

// 检查package.json中的测试脚本
console.log('\n📋 检查测试脚本:');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  const scripts = packageJson.scripts || {};
  
  const requiredScripts = [
    'test',
    'test:unit',
    'test:integration', 
    'test:coverage',
    'test:permission'
  ];
  
  requiredScripts.forEach(script => {
    const exists = scripts[script];
    const status = exists ? '✅' : '❌';
    console.log(`  ${status} ${script}: ${exists || '未定义'}`);
  });
  
} catch (error) {
  console.log('  ❌ 无法读取package.json');
  allFilesExist = false;
}

// 检查依赖包
console.log('\n📚 检查测试依赖:');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  const devDeps = packageJson.devDependencies || {};
  
  const requiredDeps = [
    'jest',
    '@types/jest',
    'babel-jest'
  ];
  
  requiredDeps.forEach(dep => {
    const exists = devDeps[dep];
    const status = exists ? '✅' : '❌';
    console.log(`  ${status} ${dep}: ${exists || '未安装'}`);
  });
  
} catch (error) {
  console.log('  ❌ 无法检查依赖包');
}

// 总结
console.log('\n' + '='.repeat(50));
if (allFilesExist) {
  console.log('🎉 测试环境验证通过！');
  console.log('\n可以运行以下命令开始测试:');
  console.log('  npm test                    # 运行所有测试');
  console.log('  npm run test:unit           # 运行单元测试');
  console.log('  npm run test:integration    # 运行集成测试');
  console.log('  npm run test:coverage       # 运行覆盖率测试');
  console.log('  npm run test:permission     # 运行权限系统测试');
  console.log('  node scripts/run-tests.js   # 使用测试运行器');
} else {
  console.log('❌ 测试环境验证失败！');
  console.log('请确保所有必需的文件都已创建。');
  process.exit(1);
}

console.log('');