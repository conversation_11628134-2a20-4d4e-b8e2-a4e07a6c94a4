console.log('开始测试导入...');

try {
  const { authenticateToken } = require('./auth');
  console.log('✅ 认证模块导入成功');
  console.log('authenticateToken类型:', typeof authenticateToken);
  
  // 测试在Express中使用
  const express = require('express');
  const app = express();
  
  console.log('✅ Express创建成功');
  
  // 测试路由定义
  app.get('/test', authenticateToken, (req, res) => {
    res.json({ message: 'test' });
  });
  
  console.log('✅ 路由定义成功');
  console.log('测试完成，没有发现问题');
  
} catch (error) {
  console.error('❌ 测试失败:', error.message);
  console.error('错误堆栈:', error.stack);
}
