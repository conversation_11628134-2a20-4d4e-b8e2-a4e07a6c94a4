/**
 * 站点监控服务器
 * 独立运行的监控服务，负责站点存活检测和通知
 */

const mysql = require('mysql2/promise');
const EnhancedSiteMonitor = require('./services/EnhancedSiteMonitor');
const NotificationService = require('./services/NotificationService');

class MonitoringServer {
  constructor() {
    this.db = null;
    this.monitor = null;
    this.notificationService = null;
    this.isRunning = false;
  }

  /**
   * 启动监控服务器
   */
  async start() {
    try {
      console.log('🚀 启动站点监控服务器...');
      
      // 初始化数据库连接
      await this.initDatabase();
      
      // 初始化通知服务
      await this.initNotificationService();
      
      // 初始化监控服务
      await this.initMonitoringService();

      // 设置优雅关闭
      this.setupGracefulShutdown();

      // 启动API服务器
      await this.startAPIServer();

      // 启动监控（放在最后，因为这是一个持续运行的服务）
      await this.monitor.start();

      this.isRunning = true;
      console.log('✅ 监控服务器启动成功！');
      
    } catch (error) {
      console.error('❌ 监控服务器启动失败:', error);
      process.exit(1);
    }
  }

  /**
   * 初始化数据库连接
   */
  async initDatabase() {
    console.log('📊 初始化数据库连接...');
    
    this.db = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'sitemanager123',
      database: process.env.DB_NAME || 'sitemanager',
      charset: 'utf8mb4',
      timezone: '+08:00'
    });
    
    // 测试连接
    await this.db.execute('SELECT 1');
    console.log('✅ 数据库连接成功');
  }

  /**
   * 初始化通知服务
   */
  async initNotificationService() {
    console.log('📤 初始化通知服务...');
    
    // 从数据库加载通知配置
    const notificationConfig = await this.loadNotificationConfig();
    
    this.notificationService = new NotificationService(notificationConfig);
    console.log('✅ 通知服务初始化完成');
  }

  /**
   * 初始化监控服务
   */
  async initMonitoringService() {
    console.log('🔍 初始化增强版监控服务...');

    this.monitor = new EnhancedSiteMonitor(this.db, this.notificationService);
    
    // 监听监控事件
    this.monitor.on('site_down', (message) => {
      console.log(`🚨 站点故障: ${message.site.domain} - ${message.error.message}`);
    });
    
    this.monitor.on('site_up', (message) => {
      console.log(`✅ 站点恢复: ${message.site.domain}`);
    });
    
    console.log('✅ 监控服务初始化完成');
  }

  /**
   * 从数据库加载通知配置
   */
  async loadNotificationConfig() {
    try {
      const [rows] = await this.db.execute(`
        SELECT type, config 
        FROM notification_config 
        WHERE enabled = 1
      `);
      
      const config = {};
      
      for (const row of rows) {
        config[row.type] = {
          enabled: true,
          ...JSON.parse(row.config)
        };
      }
      
      return config;
    } catch (error) {
      console.warn('⚠️  加载通知配置失败，使用默认配置:', error.message);
      return this.getDefaultNotificationConfig();
    }
  }

  /**
   * 获取默认通知配置
   */
  getDefaultNotificationConfig() {
    return {
      email: {
        enabled: process.env.EMAIL_ENABLED === 'true',
        host: process.env.EMAIL_HOST || 'smtp.qq.com',
        port: parseInt(process.env.EMAIL_PORT) || 587,
        secure: process.env.EMAIL_SECURE === 'true',
        user: process.env.EMAIL_USER || '',
        pass: process.env.EMAIL_PASS || '',
        from: process.env.EMAIL_FROM || '',
        to: (process.env.EMAIL_TO || '').split(',').filter(Boolean)
      },
      wechat: {
        enabled: process.env.WECHAT_ENABLED === 'true',
        webhookUrl: process.env.WECHAT_WEBHOOK_URL || '',
        mentionedList: (process.env.WECHAT_MENTIONED || '').split(',').filter(Boolean)
      },
      dingtalk: {
        enabled: process.env.DINGTALK_ENABLED === 'true',
        webhookUrl: process.env.DINGTALK_WEBHOOK_URL || '',
        secret: process.env.DINGTALK_SECRET || '',
        atMobiles: (process.env.DINGTALK_AT_MOBILES || '').split(',').filter(Boolean),
        isAtAll: process.env.DINGTALK_AT_ALL === 'true'
      },
      webhook: {
        enabled: process.env.WEBHOOK_ENABLED === 'true',
        urls: (process.env.WEBHOOK_URLS || '').split(',').filter(Boolean),
        headers: {}
      }
    };
  }

  /**
   * 启动API服务器（用于监控状态查询）
   */
  async startAPIServer() {
    try {
      console.log('📡 启动监控API服务器...');

      const express = require('express');
      const cors = require('cors');

      const app = express();
      app.use(cors());
      app.use(express.json());
    
    // 监控状态接口
    app.get('/status', (req, res) => {
      res.json({
        success: true,
        data: {
          isRunning: this.isRunning,
          stats: this.monitor.getStats(),
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          timestamp: new Date()
        }
      });
    });
    
    // 手动触发检测接口
    app.post('/trigger-check', async (req, res) => {
      try {
        if (!this.isRunning) {
          return res.status(503).json({
            success: false,
            message: '监控服务未运行'
          });
        }
        
        // 触发一次检测周期
        this.monitor.runCheckCycle();
        
        res.json({
          success: true,
          message: '检测已触发'
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message: error.message
        });
      }
    });
    
    // 测试通知接口
    app.post('/test-notification', async (req, res) => {
      try {
        const { type = 'all' } = req.body;
        
        await this.notificationService.testNotification(type);
        
        res.json({
          success: true,
          message: '测试通知已发送'
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message: error.message
        });
      }
    });
    
    // 获取监控统计
    app.get('/stats', async (req, res) => {
      try {
        // 获取状态检测表统计
        const [statusStats] = await this.db.execute(`
          SELECT
            COUNT(*) as total_sites,
            SUM(CASE WHEN error_count = 0 THEN 1 ELSE 0 END) as healthy_sites,
            SUM(CASE WHEN error_count >= 3 THEN 1 ELSE 0 END) as failed_sites,
            SUM(CASE WHEN error_count > 0 AND error_count < 3 THEN 1 ELSE 0 END) as warning_sites,
            AVG(response_time) as avg_response_time
          FROM status_check
        `);

        // 获取故障站点详情
        const [failedSites] = await this.db.execute(`
          SELECT * FROM v_failed_sites LIMIT 10
        `);

        res.json({
          success: true,
          data: {
            status: statusStats[0] || {},
            failedSites: failedSites,
            monitor: this.monitor.getStats()
          }
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message: error.message
        });
      }
    });

    // 获取状态检测详情
    app.get('/status-check', async (req, res) => {
      try {
        const { page = 1, limit = 20, status = 'all' } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = '';
        if (status === 'failed') {
          whereClause = 'WHERE sc.error_count >= 3';
        } else if (status === 'warning') {
          whereClause = 'WHERE sc.error_count > 0 AND sc.error_count < 3';
        } else if (status === 'healthy') {
          whereClause = 'WHERE sc.error_count = 0';
        }

        const [sites] = await this.db.execute(`
          SELECT
            sc.platform_id,
            w.site_name,
            w.domain,
            w.site_url,
            sc.status_code,
            sc.error_count,
            sc.response_time,
            sc.last_check_time,
            sc.last_success_time,
            sc.error_message,
            s.name as server_name
          FROM status_check sc
          JOIN websites w ON sc.platform_id = w.id
          LEFT JOIN servers s ON w.server_id = s.id
          ${whereClause}
          ORDER BY sc.error_count DESC, sc.last_check_time DESC
          LIMIT ? OFFSET ?
        `, [parseInt(limit), parseInt(offset)]);

        const [countResult] = await this.db.execute(`
          SELECT COUNT(*) as total
          FROM status_check sc
          JOIN websites w ON sc.platform_id = w.id
          ${whereClause}
        `);

        res.json({
          success: true,
          data: {
            sites,
            pagination: {
              page: parseInt(page),
              limit: parseInt(limit),
              total: countResult[0].total,
              totalPages: Math.ceil(countResult[0].total / limit)
            }
          }
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          message: error.message
        });
      }
    });
    
      const port = process.env.MONITOR_PORT || 3002;
      app.listen(port, () => {
        console.log(`📡 监控API服务器启动成功: http://localhost:${port}`);
      });

    } catch (error) {
      console.error('❌ API服务器启动失败:', error);
    }
  }

  /**
   * 设置优雅关闭
   */
  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      console.log(`\n🛑 收到 ${signal} 信号，开始优雅关闭...`);
      
      this.isRunning = false;
      
      if (this.monitor) {
        await this.monitor.stop();
      }
      
      if (this.db) {
        await this.db.end();
      }
      
      console.log('✅ 监控服务器已安全关闭');
      process.exit(0);
    };
    
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon
  }
}

// 启动监控服务器
if (require.main === module) {
  const server = new MonitoringServer();
  server.start().catch(error => {
    console.error('启动失败:', error);
    process.exit(1);
  });
}

module.exports = MonitoringServer;
