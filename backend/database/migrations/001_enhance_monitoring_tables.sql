-- =====================================================
-- 网站监控系统数据库增强迁移脚本
-- 参考PHP监控系统，安全地增强现有表结构
-- 版本: 1.0.0
-- 创建时间: 2024-12-19
-- =====================================================

-- 设置字符集和时区
SET NAMES utf8mb4;
SET time_zone = '+08:00';

-- =====================================================
-- 1. 安全增强 websites 表 (参考 zendkee_website)
-- =====================================================

-- 检查并添加监控相关字段
ALTER TABLE websites 
ADD COLUMN IF NOT EXISTS tries INT DEFAULT 0 COMMENT '尝试次数（用于排序优先级）',
ADD COLUMN IF NOT EXISTS status_check TINYINT(1) DEFAULT 1 COMMENT '是否启用状态检查（1=启用，0=禁用）',
ADD COLUMN IF NOT EXISTS ssl_check TINYINT(1) DEFAULT 1 COMMENT '是否启用SSL检查（1=启用，0=禁用）',
ADD COLUMN IF NOT EXISTS next_notice DATETIME NULL COMMENT '下次通知时间（用于控制通知频率）',
ADD COLUMN IF NOT EXISTS last_error_check DATETIME NULL COMMENT '最近错误检查时间',
ADD COLUMN IF NOT EXISTS cdn_type VARCHAR(50) DEFAULT 'no' COMMENT 'CDN类型（Cloudflare/阿里云/腾讯云/no）',
ADD COLUMN IF NOT EXISTS server_ip VARCHAR(45) NULL COMMENT '解析的服务器IP地址',
ADD COLUMN IF NOT EXISTS location VARCHAR(100) NULL COMMENT '服务器地理位置',
ADD COLUMN IF NOT EXISTS consecutive_failures INT DEFAULT 0 COMMENT '连续失败次数',
ADD COLUMN IF NOT EXISTS last_failure_time DATETIME NULL COMMENT '最后失败时间',
ADD COLUMN IF NOT EXISTS notification_sent TINYINT(1) DEFAULT 0 COMMENT '是否已发送通知',
ADD COLUMN IF NOT EXISTS last_notification_time DATETIME NULL COMMENT '最后通知时间';

-- 添加索引以优化查询性能
ALTER TABLE websites 
ADD INDEX IF NOT EXISTS idx_status_check (status_check),
ADD INDEX IF NOT EXISTS idx_ssl_check (ssl_check),
ADD INDEX IF NOT EXISTS idx_tries (tries),
ADD INDEX IF NOT EXISTS idx_last_check_time (last_check_time),
ADD INDEX IF NOT EXISTS idx_next_notice (next_notice),
ADD INDEX IF NOT EXISTS idx_consecutive_failures (consecutive_failures),
ADD INDEX IF NOT EXISTS idx_notification_sent (notification_sent);

-- =====================================================
-- 2. 增强 website_status_checks 表 (参考 zendkee_website_checklog)
-- =====================================================

-- 检查表是否存在，不存在则创建
CREATE TABLE IF NOT EXISTS website_status_checks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL COMMENT '网站ID',
    check_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '检测时间',
    status_code INT DEFAULT 0 COMMENT 'HTTP状态码',
    response_time INT DEFAULT 0 COMMENT '响应时间(ms)',
    is_accessible BOOLEAN DEFAULT FALSE COMMENT '是否可访问',
    error_message TEXT COMMENT '错误信息',
    check_type ENUM('scheduled', 'manual', 'batch', 'api') DEFAULT 'scheduled' COMMENT '检测类型',
    
    -- 新增PHP系统的字段
    cdn_detected VARCHAR(50) NULL COMMENT '检测到的CDN类型',
    server_ip VARCHAR(45) NULL COMMENT '解析的服务器IP',
    redirect_count INT DEFAULT 0 COMMENT '重定向次数',
    ssl_days_remaining INT NULL COMMENT 'SSL证书剩余天数',
    ssl_issuer VARCHAR(255) NULL COMMENT 'SSL证书颁发者',
    ssl_valid_from DATETIME NULL COMMENT 'SSL证书生效时间',
    ssl_valid_to DATETIME NULL COMMENT 'SSL证书到期时间',
    dns_resolution_time INT DEFAULT 0 COMMENT 'DNS解析时间(ms)',
    connect_time INT DEFAULT 0 COMMENT '连接时间(ms)',
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引
    INDEX idx_website_id (website_id),
    INDEX idx_check_time (check_time),
    INDEX idx_website_check_time (website_id, check_time),
    INDEX idx_status_code (status_code),
    INDEX idx_check_type (check_type),
    INDEX idx_is_accessible (is_accessible),
    
    -- 外键约束（如果websites表存在）
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站状态检测记录表';

-- 如果表已存在，则添加新字段
ALTER TABLE website_status_checks 
ADD COLUMN IF NOT EXISTS cdn_detected VARCHAR(50) NULL COMMENT '检测到的CDN类型',
ADD COLUMN IF NOT EXISTS server_ip VARCHAR(45) NULL COMMENT '解析的服务器IP',
ADD COLUMN IF NOT EXISTS redirect_count INT DEFAULT 0 COMMENT '重定向次数',
ADD COLUMN IF NOT EXISTS ssl_days_remaining INT NULL COMMENT 'SSL证书剩余天数',
ADD COLUMN IF NOT EXISTS ssl_issuer VARCHAR(255) NULL COMMENT 'SSL证书颁发者',
ADD COLUMN IF NOT EXISTS ssl_valid_from DATETIME NULL COMMENT 'SSL证书生效时间',
ADD COLUMN IF NOT EXISTS ssl_valid_to DATETIME NULL COMMENT 'SSL证书到期时间',
ADD COLUMN IF NOT EXISTS dns_resolution_time INT DEFAULT 0 COMMENT 'DNS解析时间(ms)',
ADD COLUMN IF NOT EXISTS connect_time INT DEFAULT 0 COMMENT '连接时间(ms)';

-- 添加新索引
ALTER TABLE website_status_checks 
ADD INDEX IF NOT EXISTS idx_cdn_detected (cdn_detected),
ADD INDEX IF NOT EXISTS idx_ssl_days_remaining (ssl_days_remaining);

-- =====================================================
-- 3. 创建网站权限表 (参考 zendkee_website_permission)
-- =====================================================

CREATE TABLE IF NOT EXISTS website_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL COMMENT '网站ID',
    user_id INT NOT NULL COMMENT '用户ID',
    permission_type ENUM('view', 'edit', 'admin') DEFAULT 'view' COMMENT '权限类型',
    granted_by INT NULL COMMENT '授权人ID',
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at DATETIME NULL COMMENT '权限到期时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    notes TEXT COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 唯一约束：一个用户对一个网站只能有一种权限
    UNIQUE KEY uk_website_user (website_id, user_id),
    
    -- 索引
    INDEX idx_website_id (website_id),
    INDEX idx_user_id (user_id),
    INDEX idx_permission_type (permission_type),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at),
    
    -- 外键约束
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站权限管理表';

-- =====================================================
-- 4. 创建监控配置表
-- =====================================================

CREATE TABLE IF NOT EXISTS website_monitor_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL COMMENT '网站ID',
    
    -- HTTP检测配置
    http_method ENUM('GET', 'POST', 'HEAD') DEFAULT 'GET' COMMENT 'HTTP请求方法',
    status_codes VARCHAR(100) DEFAULT '200-299,301,302' COMMENT '正常状态码范围',
    connect_timeout INT DEFAULT 10 COMMENT '连接超时时间(秒)',
    retries INT DEFAULT 1 COMMENT '重试次数',
    retry_interval INT DEFAULT 30 COMMENT '重试间隔(秒)',
    
    -- 检测开关
    enable_http_check TINYINT(1) DEFAULT 1 COMMENT '启用HTTP检测',
    enable_ssl_check TINYINT(1) DEFAULT 1 COMMENT '启用SSL检测',
    enable_keyword_check TINYINT(1) DEFAULT 0 COMMENT '启用关键词检测',
    
    -- SSL配置
    notify_cert_expiry TINYINT(1) DEFAULT 1 COMMENT '证书到期通知',
    cert_expiry_days INT DEFAULT 30 COMMENT '证书到期提醒天数',
    ignore_tls TINYINT(1) DEFAULT 0 COMMENT '忽略TLS错误',
    
    -- 关键词检测
    keyword VARCHAR(255) NULL COMMENT '检测关键词',
    
    -- 请求配置
    request_headers TEXT COMMENT '自定义请求头(JSON格式)',
    request_body TEXT COMMENT '请求体内容',
    max_redirects INT DEFAULT 10 COMMENT '最大重定向次数',
    
    -- 检测间隔配置
    check_interval INT DEFAULT 300 COMMENT '检测间隔(秒)',
    
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 唯一约束
    UNIQUE KEY uk_website_id (website_id),
    
    -- 索引
    INDEX idx_enable_http_check (enable_http_check),
    INDEX idx_enable_ssl_check (enable_ssl_check),
    INDEX idx_check_interval (check_interval),
    
    -- 外键约束
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站监控配置表';

-- =====================================================
-- 5. 创建通知历史表
-- =====================================================

CREATE TABLE IF NOT EXISTS notification_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL COMMENT '网站ID',
    notification_type ENUM('email', 'feishu', 'wechat', 'sms', 'webhook') NOT NULL COMMENT '通知类型',
    trigger_reason ENUM('site_down', 'site_up', 'ssl_expiry', 'renewal_reminder') NOT NULL COMMENT '触发原因',
    message TEXT NOT NULL COMMENT '通知消息内容',
    status ENUM('pending', 'sent', 'failed') DEFAULT 'pending' COMMENT '发送状态',
    response_data TEXT COMMENT '响应数据',
    error_message TEXT COMMENT '错误信息',
    sent_at DATETIME NULL COMMENT '发送时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_website_id (website_id),
    INDEX idx_notification_type (notification_type),
    INDEX idx_trigger_reason (trigger_reason),
    INDEX idx_status (status),
    INDEX idx_sent_at (sent_at),
    INDEX idx_created_at (created_at),
    
    -- 外键约束
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知历史记录表';

-- =====================================================
-- 6. 创建系统配置表
-- =====================================================

CREATE TABLE IF NOT EXISTS monitor_system_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 唯一约束
    UNIQUE KEY uk_config_key (config_key),
    
    -- 索引
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='监控系统配置表';

-- =====================================================
-- 7. 插入默认配置数据
-- =====================================================

-- 插入系统默认配置
INSERT IGNORE INTO monitor_system_configs (config_key, config_value, config_type, description) VALUES
('check_interval', '60', 'number', '默认检测间隔(秒)'),
('alarm_threshold', '600', 'number', '报警阈值(秒) - 异常持续时间'),
('notification_interval', '1800', 'number', '通知间隔(秒) - 重复通知间隔'),
('batch_size', '20', 'number', '批处理大小 - 每次检测网站数量'),
('ssl_warning_days', '30', 'number', 'SSL证书预警天数'),
('renewal_warning_days', '20', 'number', '续费预警天数'),
('max_retries', '3', 'number', '最大重试次数'),
('timeout', '10', 'number', '请求超时时间(秒)'),
('enable_notifications', 'true', 'boolean', '是否启用通知'),
('smtp_config', '{}', 'json', 'SMTP邮件配置');

-- =====================================================
-- 8. 创建存储过程和函数
-- =====================================================

-- 删除已存在的存储过程
DROP PROCEDURE IF EXISTS GetSitesToCheck;
DROP PROCEDURE IF EXISTS UpdateWebsiteStatusStats;
DROP PROCEDURE IF EXISTS GetFailedSitesForNotification;

-- 创建获取待检测网站的存储过程（参考PHP系统逻辑）
DELIMITER //
CREATE PROCEDURE GetSitesToCheck(
    IN p_limit INT DEFAULT 20,
    IN p_check_interval INT DEFAULT 60
)
BEGIN
    SELECT 
        w.id,
        w.site_name,
        w.domain,
        w.site_url,
        w.platform_id,
        w.server_id,
        w.status_check,
        w.ssl_check,
        w.tries,
        w.last_check_time,
        w.consecutive_failures,
        COALESCE(w.tries, 0) as priority_tries,
        CASE 
            WHEN w.last_check_time IS NULL THEN 1
            WHEN w.last_check_time < DATE_SUB(NOW(), INTERVAL p_check_interval SECOND) THEN 1
            ELSE 0
        END as should_check
    FROM websites w
    WHERE w.status = 'active'
        AND w.status_check = 1
        AND w.site_url IS NOT NULL
        AND w.site_url != ''
        AND (
            w.last_check_time IS NULL OR
            w.last_check_time < DATE_SUB(NOW(), INTERVAL p_check_interval SECOND)
        )
    ORDER BY w.tries ASC, w.last_check_time ASC
    LIMIT p_limit;
END //
DELIMITER ;

-- 创建更新网站状态统计的存储过程
DELIMITER //
CREATE PROCEDURE UpdateWebsiteStatusStats(
    IN p_website_id INT,
    IN p_status_code INT,
    IN p_response_time INT,
    IN p_is_accessible BOOLEAN,
    IN p_error_message TEXT,
    IN p_cdn_detected VARCHAR(50),
    IN p_server_ip VARCHAR(45),
    IN p_ssl_days_remaining INT,
    IN p_ssl_issuer VARCHAR(255)
)
BEGIN
    DECLARE v_current_failures INT DEFAULT 0;
    
    -- 插入检测记录
    INSERT INTO website_status_checks (
        website_id, check_time, status_code, response_time, is_accessible,
        error_message, cdn_detected, server_ip, ssl_days_remaining, ssl_issuer
    ) VALUES (
        p_website_id, NOW(), p_status_code, p_response_time, p_is_accessible,
        p_error_message, p_cdn_detected, p_server_ip, p_ssl_days_remaining, p_ssl_issuer
    );
    
    -- 更新网站表的状态信息
    UPDATE websites SET
        access_status_code = p_status_code,
        response_time = p_response_time,
        last_check_time = NOW(),
        tries = tries + 1,
        consecutive_failures = CASE 
            WHEN p_is_accessible THEN 0 
            ELSE consecutive_failures + 1 
        END,
        last_failure_time = CASE 
            WHEN p_is_accessible THEN last_failure_time 
            ELSE NOW() 
        END,
        last_error_check = CASE 
            WHEN p_is_accessible THEN NULL 
            ELSE NOW() 
        END,
        cdn_type = COALESCE(p_cdn_detected, cdn_type),
        server_ip = COALESCE(p_server_ip, server_ip),
        -- 重置通知状态（如果网站恢复正常）
        notification_sent = CASE 
            WHEN p_is_accessible THEN 0 
            ELSE notification_sent 
        END
    WHERE id = p_website_id;
    
    -- 获取更新后的连续失败次数
    SELECT consecutive_failures INTO v_current_failures 
    FROM websites 
    WHERE id = p_website_id;
    
    -- 返回更新结果
    SELECT 
        p_website_id as website_id,
        p_is_accessible as is_accessible,
        v_current_failures as consecutive_failures,
        p_status_code as status_code;
END //
DELIMITER ;

-- 创建获取需要通知的故障网站的存储过程
DELIMITER //
CREATE PROCEDURE GetFailedSitesForNotification(
    IN p_failure_threshold INT DEFAULT 5,
    IN p_notification_interval INT DEFAULT 1800
)
BEGIN
    SELECT 
        w.id,
        w.site_name,
        w.domain,
        w.site_url,
        w.consecutive_failures,
        w.last_failure_time,
        w.last_error_check,
        w.access_status_code,
        w.notification_sent,
        w.last_notification_time,
        w.next_notice,
        TIMESTAMPDIFF(MINUTE, w.last_error_check, NOW()) as error_duration_minutes
    FROM websites w
    WHERE w.status = 'active'
        AND w.status_check = 1
        AND w.consecutive_failures >= p_failure_threshold
        AND (
            w.notification_sent = 0 OR
            w.next_notice IS NULL OR
            w.next_notice <= NOW()
        )
        AND w.last_error_check IS NOT NULL
        AND w.last_error_check < DATE_SUB(NOW(), INTERVAL 600 SECOND) -- 至少异常10分钟
    ORDER BY w.consecutive_failures DESC, w.last_error_check ASC;
END //
DELIMITER ;

-- =====================================================
-- 9. 创建视图
-- =====================================================

-- 创建网站监控状态视图
CREATE OR REPLACE VIEW website_monitor_status AS
SELECT
    w.id,
    w.site_name,
    w.domain,
    w.site_url,
    w.status,
    w.status_check,
    w.ssl_check,
    w.consecutive_failures,
    w.last_check_time,
    w.last_failure_time,
    w.access_status_code,
    w.response_time,
    w.cdn_type,
    w.server_ip,
    w.notification_sent,
    w.last_notification_time,
    w.next_notice,
    CASE
        WHEN w.consecutive_failures = 0 THEN '正常'
        WHEN w.consecutive_failures >= 5 THEN CONCAT('异常 (', w.consecutive_failures, '次)')
        ELSE CONCAT('警告 (', w.consecutive_failures, '次)')
    END as status_display,
    CASE
        WHEN w.consecutive_failures = 0 THEN 'success'
        WHEN w.consecutive_failures >= 5 THEN 'error'
        ELSE 'warning'
    END as status_type,
    CASE
        WHEN w.last_error_check IS NOT NULL
        THEN TIMESTAMPDIFF(MINUTE, w.last_error_check, NOW())
        ELSE 0
    END as error_duration_minutes
FROM websites w
WHERE w.status = 'active';

-- 迁移完成标记
INSERT IGNORE INTO monitor_system_configs (config_key, config_value, config_type, description) 
VALUES ('migration_001_completed', 'true', 'boolean', '监控增强迁移001已完成');

-- =====================================================
-- 迁移脚本执行完成
-- =====================================================
