-- 创建性能指标表
-- 用于存储系统性能监控数据

-- 性能指标主表
CREATE TABLE IF NOT EXISTS performance_metrics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- 监控检测指标
    total_checks INT DEFAULT 0 COMMENT '总检测次数',
    successful_checks INT DEFAULT 0 COMMENT '成功检测次数',
    failed_checks INT DEFAULT 0 COMMENT '失败检测次数',
    avg_response_time INT DEFAULT 0 COMMENT '平均响应时间(ms)',
    max_response_time INT DEFAULT 0 COMMENT '最大响应时间(ms)',
    min_response_time INT DEFAULT 0 COMMENT '最小响应时间(ms)',
    checks_per_second DECIMAL(10,2) DEFAULT 0 COMMENT '每秒检测数',
    
    -- 系统资源指标
    cpu_usage DECIMAL(5,2) DEFAULT 0 COMMENT 'CPU使用率(%)',
    memory_usage DECIMAL(5,2) DEFAULT 0 COMMENT '内存使用率(%)',
    disk_usage DECIMAL(5,2) DEFAULT 0 COMMENT '磁盘使用率(%)',
    network_connections INT DEFAULT 0 COMMENT '网络连接数',
    system_uptime BIGINT DEFAULT 0 COMMENT '系统运行时间(秒)',
    
    -- 数据库性能指标
    db_connection_count INT DEFAULT 0 COMMENT '数据库连接数',
    db_query_count INT DEFAULT 0 COMMENT '数据库查询次数',
    db_avg_query_time INT DEFAULT 0 COMMENT '平均查询时间(ms)',
    db_slow_queries INT DEFAULT 0 COMMENT '慢查询次数',
    db_connection_pool_usage DECIMAL(5,2) DEFAULT 0 COMMENT '连接池使用率(%)',
    
    -- 通知性能指标
    total_notifications INT DEFAULT 0 COMMENT '总通知数',
    successful_notifications INT DEFAULT 0 COMMENT '成功通知数',
    failed_notifications INT DEFAULT 0 COMMENT '失败通知数',
    avg_notification_time INT DEFAULT 0 COMMENT '平均通知处理时间(ms)',
    notification_queue_size INT DEFAULT 0 COMMENT '通知队列大小',
    
    -- 错误统计
    total_errors INT DEFAULT 0 COMMENT '总错误数',
    error_rate DECIMAL(5,2) DEFAULT 0 COMMENT '错误率(%)',
    
    -- 完整指标数据(JSON格式)
    metrics_data JSON COMMENT '完整指标数据',
    
    -- 索引
    INDEX idx_timestamp (timestamp),
    INDEX idx_cpu_usage (cpu_usage),
    INDEX idx_memory_usage (memory_usage),
    INDEX idx_error_rate (error_rate)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能指标表';

-- 性能指标历史表（用于长期存储）
CREATE TABLE IF NOT EXISTS performance_metrics_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL COMMENT '日期',
    hour TINYINT NOT NULL COMMENT '小时(0-23)',
    
    -- 汇总统计
    total_checks INT DEFAULT 0 COMMENT '总检测次数',
    successful_checks INT DEFAULT 0 COMMENT '成功检测次数',
    failed_checks INT DEFAULT 0 COMMENT '失败检测次数',
    avg_response_time INT DEFAULT 0 COMMENT '平均响应时间(ms)',
    max_response_time INT DEFAULT 0 COMMENT '最大响应时间(ms)',
    min_response_time INT DEFAULT 0 COMMENT '最小响应时间(ms)',
    
    avg_cpu_usage DECIMAL(5,2) DEFAULT 0 COMMENT '平均CPU使用率(%)',
    max_cpu_usage DECIMAL(5,2) DEFAULT 0 COMMENT '最大CPU使用率(%)',
    avg_memory_usage DECIMAL(5,2) DEFAULT 0 COMMENT '平均内存使用率(%)',
    max_memory_usage DECIMAL(5,2) DEFAULT 0 COMMENT '最大内存使用率(%)',
    
    total_notifications INT DEFAULT 0 COMMENT '总通知数',
    successful_notifications INT DEFAULT 0 COMMENT '成功通知数',
    failed_notifications INT DEFAULT 0 COMMENT '失败通知数',
    
    total_errors INT DEFAULT 0 COMMENT '总错误数',
    avg_error_rate DECIMAL(5,2) DEFAULT 0 COMMENT '平均错误率(%)',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 唯一索引
    UNIQUE KEY uk_date_hour (date, hour),
    INDEX idx_date (date),
    INDEX idx_avg_response_time (avg_response_time),
    INDEX idx_avg_error_rate (avg_error_rate)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能指标历史表';

-- 性能报警记录表
CREATE TABLE IF NOT EXISTS performance_alerts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    alert_type VARCHAR(50) NOT NULL COMMENT '报警类型',
    alert_level ENUM('info', 'warning', 'error', 'critical') DEFAULT 'warning' COMMENT '报警级别',
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    current_value DECIMAL(10,2) NOT NULL COMMENT '当前值',
    threshold_value DECIMAL(10,2) NOT NULL COMMENT '阈值',
    message TEXT COMMENT '报警消息',
    
    -- 报警状态
    status ENUM('active', 'resolved', 'suppressed') DEFAULT 'active' COMMENT '报警状态',
    resolved_at TIMESTAMP NULL COMMENT '解决时间',
    resolved_by VARCHAR(100) COMMENT '解决人',
    resolution_note TEXT COMMENT '解决说明',
    
    -- 通知状态
    notification_sent BOOLEAN DEFAULT FALSE COMMENT '是否已发送通知',
    notification_sent_at TIMESTAMP NULL COMMENT '通知发送时间',
    notification_recipients TEXT COMMENT '通知接收人',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_alert_type (alert_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_metric_name (metric_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能报警记录表';

-- 系统配置优化建议表
CREATE TABLE IF NOT EXISTS performance_recommendations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    category VARCHAR(50) NOT NULL COMMENT '建议类别',
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium' COMMENT '优先级',
    title VARCHAR(200) NOT NULL COMMENT '建议标题',
    description TEXT COMMENT '建议描述',
    
    -- 触发条件
    trigger_metric VARCHAR(100) COMMENT '触发指标',
    trigger_value DECIMAL(10,2) COMMENT '触发值',
    trigger_condition VARCHAR(20) COMMENT '触发条件(>, <, =, etc.)',
    
    -- 建议内容
    recommendation TEXT NOT NULL COMMENT '具体建议',
    expected_improvement TEXT COMMENT '预期改善效果',
    implementation_difficulty ENUM('easy', 'medium', 'hard') DEFAULT 'medium' COMMENT '实施难度',
    
    -- 状态跟踪
    status ENUM('pending', 'in_progress', 'completed', 'dismissed') DEFAULT 'pending' COMMENT '状态',
    implemented_at TIMESTAMP NULL COMMENT '实施时间',
    implemented_by VARCHAR(100) COMMENT '实施人',
    implementation_notes TEXT COMMENT '实施说明',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category (category),
    INDEX idx_priority (priority),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能优化建议表';

-- 创建存储过程：汇总性能指标到历史表
DELIMITER //

CREATE PROCEDURE AggregatePerformanceMetrics(IN target_date DATE, IN target_hour TINYINT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 删除已存在的记录
    DELETE FROM performance_metrics_history 
    WHERE date = target_date AND hour = target_hour;
    
    -- 插入汇总数据
    INSERT INTO performance_metrics_history (
        date, hour,
        total_checks, successful_checks, failed_checks,
        avg_response_time, max_response_time, min_response_time,
        avg_cpu_usage, max_cpu_usage,
        avg_memory_usage, max_memory_usage,
        total_notifications, successful_notifications, failed_notifications,
        total_errors, avg_error_rate
    )
    SELECT 
        target_date,
        target_hour,
        SUM(total_checks),
        SUM(successful_checks),
        SUM(failed_checks),
        AVG(avg_response_time),
        MAX(max_response_time),
        MIN(CASE WHEN min_response_time > 0 THEN min_response_time ELSE NULL END),
        AVG(cpu_usage),
        MAX(cpu_usage),
        AVG(memory_usage),
        MAX(memory_usage),
        SUM(total_notifications),
        SUM(successful_notifications),
        SUM(failed_notifications),
        SUM(total_errors),
        AVG(error_rate)
    FROM performance_metrics
    WHERE DATE(timestamp) = target_date 
      AND HOUR(timestamp) = target_hour;
    
    COMMIT;
END //

DELIMITER ;

-- 创建存储过程：清理旧的性能指标数据
DELIMITER //

CREATE PROCEDURE CleanupPerformanceMetrics(IN retention_days INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 清理详细指标数据（保留指定天数）
    DELETE FROM performance_metrics 
    WHERE timestamp < DATE_SUB(NOW(), INTERVAL retention_days DAY);
    
    -- 清理历史汇总数据（保留更长时间，比如90天）
    DELETE FROM performance_metrics_history 
    WHERE date < DATE_SUB(CURDATE(), INTERVAL (retention_days * 3) DAY);
    
    -- 清理已解决的旧报警记录（保留30天）
    DELETE FROM performance_alerts 
    WHERE status = 'resolved' 
      AND resolved_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 清理已完成的旧建议记录（保留60天）
    DELETE FROM performance_recommendations 
    WHERE status IN ('completed', 'dismissed')
      AND updated_at < DATE_SUB(NOW(), INTERVAL 60 DAY);
    
    COMMIT;
END //

DELIMITER ;

-- 创建视图：性能概览
CREATE OR REPLACE VIEW performance_overview AS
SELECT 
    DATE(timestamp) as date,
    HOUR(timestamp) as hour,
    AVG(cpu_usage) as avg_cpu_usage,
    MAX(cpu_usage) as max_cpu_usage,
    AVG(memory_usage) as avg_memory_usage,
    MAX(memory_usage) as max_memory_usage,
    AVG(avg_response_time) as avg_response_time,
    MAX(max_response_time) as max_response_time,
    SUM(total_checks) as total_checks,
    SUM(successful_checks) as successful_checks,
    SUM(failed_checks) as failed_checks,
    AVG(error_rate) as avg_error_rate,
    COUNT(*) as metric_count
FROM performance_metrics
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
GROUP BY DATE(timestamp), HOUR(timestamp)
ORDER BY date DESC, hour DESC;

-- 创建视图：当前性能状态
CREATE OR REPLACE VIEW current_performance_status AS
SELECT 
    cpu_usage,
    memory_usage,
    avg_response_time,
    checks_per_second,
    error_rate,
    total_checks,
    successful_checks,
    failed_checks,
    total_notifications,
    successful_notifications,
    failed_notifications,
    timestamp
FROM performance_metrics
ORDER BY timestamp DESC
LIMIT 1;

-- 插入初始性能配置
INSERT IGNORE INTO monitor_system_configs (config_key, config_value, description, category) VALUES
('performance_monitoring_enabled', 'true', '是否启用性能监控', 'performance'),
('performance_metrics_interval', '60', '性能指标收集间隔(秒)', 'performance'),
('performance_history_retention', '60', '性能历史数据保留数量', 'performance'),
('performance_alert_cpu_threshold', '80', 'CPU使用率报警阈值(%)', 'performance'),
('performance_alert_memory_threshold', '85', '内存使用率报警阈值(%)', 'performance'),
('performance_alert_response_time_threshold', '5000', '响应时间报警阈值(ms)', 'performance'),
('performance_alert_error_rate_threshold', '10', '错误率报警阈值(%)', 'performance'),
('performance_alert_db_query_time_threshold', '1000', '数据库查询时间报警阈值(ms)', 'performance'),
('performance_cleanup_retention_days', '7', '性能指标数据保留天数', 'performance'),
('performance_aggregation_enabled', 'true', '是否启用性能指标汇总', 'performance');
