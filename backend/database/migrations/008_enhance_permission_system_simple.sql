-- 权限系统增强迁移脚本（简化版）
-- 版本: 008
-- 描述: 增强权限系统，添加审计日志、权限缓存等功能

-- 1. 创建审计日志表
CREATE TABLE IF NOT EXISTS audit_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id INT COMMENT '操作用户ID',
  action VARCHAR(100) NOT NULL COMMENT '操作类型',
  resource VARCHAR(100) COMMENT '资源类型',
  resource_id VARCHAR(100) COMMENT '资源ID',
  result ENUM('granted', 'denied', 'success', 'failure') NOT NULL COMMENT '操作结果',
  details JSON COMMENT '详细信息',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_action (action),
  INDEX idx_resource (resource),
  INDEX idx_result (result),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';

-- 2. 创建权限缓存表
CREATE TABLE IF NOT EXISTS permission_cache (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  permissions_hash VARCHAR(64) NOT NULL COMMENT '权限数据哈希值',
  permissions_data JSON NOT NULL COMMENT '权限数据',
  expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_user_id (user_id),
  INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限缓存表';

-- 3. 创建角色模板表
CREATE TABLE IF NOT EXISTS role_templates (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '模板名称',
  description TEXT COMMENT '模板描述',
  permissions JSON NOT NULL COMMENT '权限配置',
  is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统模板',
  created_by INT COMMENT '创建者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_is_system (is_system)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色模板表';

-- 4. 创建权限配置表
CREATE TABLE IF NOT EXISTS permission_configs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
  config_value JSON NOT NULL COMMENT '配置值',
  description TEXT COMMENT '配置描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限配置表';

-- 5. 插入默认权限数据（如果不存在）
INSERT IGNORE INTO permissions (name, code, description, module) VALUES
-- 用户管理权限
('查看用户列表', 'user.list.view', '查看用户列表的权限', 'user'),
('创建用户', 'user.create', '创建新用户的权限', 'user'),
('编辑用户', 'user.edit', '编辑用户信息的权限', 'user'),
('删除用户', 'user.delete', '删除用户的权限', 'user'),
('查看用户详情', 'user.view', '查看用户详细信息的权限', 'user'),

-- 网站管理权限
('查看网站列表', 'site.list.view', '查看网站列表的权限', 'site'),
('创建网站', 'site.create', '创建新网站的权限', 'site'),
('编辑网站', 'site.edit', '编辑网站信息的权限', 'site'),
('删除网站', 'site.delete', '删除网站的权限', 'site'),
('查看网站详情', 'site.view', '查看网站详细信息的权限', 'site'),
('网站状态检测', 'site.monitor', '执行网站状态检测的权限', 'site'),

-- 服务器管理权限
('查看服务器列表', 'server.list.view', '查看服务器列表的权限', 'server'),
('创建服务器', 'server.create', '创建新服务器的权限', 'server'),
('编辑服务器', 'server.edit', '编辑服务器信息的权限', 'server'),
('删除服务器', 'server.delete', '删除服务器的权限', 'server'),
('查看服务器详情', 'server.view', '查看服务器详细信息的权限', 'server'),
('服务器监控', 'server.monitor', '查看服务器监控数据的权限', 'server'),

-- 系统管理权限
('系统设置', 'system.settings.edit', '修改系统设置的权限', 'system'),
('查看系统设置', 'system.settings.view', '查看系统设置的权限', 'system'),
('系统备份', 'system.backup', '执行系统备份的权限', 'system'),
('查看审计日志', 'system.audit.view', '查看审计日志的权限', 'system'),
('权限管理', 'system.permission.manage', '管理用户权限的权限', 'system');

-- 6. 插入默认角色权限配置（如果不存在）
INSERT IGNORE INTO role_permissions (role, permission_code) VALUES
-- 超级管理员权限（所有权限）
('super_admin', 'user.list.view'),
('super_admin', 'user.create'),
('super_admin', 'user.edit'),
('super_admin', 'user.delete'),
('super_admin', 'user.view'),
('super_admin', 'site.list.view'),
('super_admin', 'site.create'),
('super_admin', 'site.edit'),
('super_admin', 'site.delete'),
('super_admin', 'site.view'),
('super_admin', 'site.monitor'),
('super_admin', 'server.list.view'),
('super_admin', 'server.create'),
('super_admin', 'server.edit'),
('super_admin', 'server.delete'),
('super_admin', 'server.view'),
('super_admin', 'server.monitor'),
('super_admin', 'system.settings.edit'),
('super_admin', 'system.settings.view'),
('super_admin', 'system.backup'),
('super_admin', 'system.audit.view'),
('super_admin', 'system.permission.manage'),

-- 管理员权限（除了用户删除和系统备份）
('admin', 'user.list.view'),
('admin', 'user.create'),
('admin', 'user.edit'),
('admin', 'user.view'),
('admin', 'site.list.view'),
('admin', 'site.create'),
('admin', 'site.edit'),
('admin', 'site.delete'),
('admin', 'site.view'),
('admin', 'site.monitor'),
('admin', 'server.list.view'),
('admin', 'server.create'),
('admin', 'server.edit'),
('admin', 'server.delete'),
('admin', 'server.view'),
('admin', 'server.monitor'),
('admin', 'system.settings.view'),
('admin', 'system.audit.view'),

-- 普通用户权限（只读权限）
('user', 'user.list.view'),
('user', 'user.view'),
('user', 'site.list.view'),
('user', 'site.view'),
('user', 'server.list.view'),
('user', 'server.view'),
('user', 'system.settings.view');

-- 7. 插入默认权限配置
INSERT IGNORE INTO permission_configs (config_key, config_value, description) VALUES
('cache_ttl', '"300"', '权限缓存过期时间（秒）'),
('audit_retention_days', '"90"', '审计日志保留天数'),
('max_login_attempts', '"5"', '最大登录尝试次数'),
('session_timeout', '"3600"', '会话超时时间（秒）'),
('require_2fa_permissions', '["user.delete", "system.backup", "system.permission.manage"]', '需要二次验证的权限列表');

-- 8. 迁移完成标记
INSERT IGNORE INTO permission_configs (config_key, config_value, description) VALUES
('migration_version', '"008"', '数据库迁移版本号');

-- 显示迁移结果
SELECT 'Permission System Migration Completed' as status,
       (SELECT COUNT(*) FROM permissions) as total_permissions,
       (SELECT COUNT(*) FROM role_permissions) as total_role_permissions,
       (SELECT COUNT(DISTINCT role) FROM role_permissions) as total_roles;