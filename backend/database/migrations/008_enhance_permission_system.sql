-- 权限系统增强迁移脚本
-- 版本: 008
-- 描述: 增强权限系统，添加审计日志、权限缓存、角色模板等功能

-- 1. 增强permissions表，添加资源和操作字段
-- 检查并添加resource字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE table_name = 'permissions' AND column_name = 'resource' AND table_schema = DATABASE()) = 0,
    'ALTER TABLE permissions ADD COLUMN resource VARCHAR(100) COMMENT "资源类型" AFTER module',
    'SELECT "resource column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加action字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE table_name = 'permissions' AND column_name = 'action' AND table_schema = DATABASE()) = 0,
    'ALTER TABLE permissions ADD COLUMN action VARCHAR(50) COMMENT "操作类型" AFTER resource',
    'SELECT "action column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引
ALTER TABLE permissions ADD INDEX idx_module (module);
ALTER TABLE permissions ADD INDEX idx_resource (resource);
ALTER TABLE permissions ADD INDEX idx_action (action);

-- 2. 增强user_custom_permissions表，添加授权状态和创建者
-- 检查并添加granted字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE table_name = 'user_custom_permissions' AND column_name = 'granted' AND table_schema = DATABASE()) = 0,
    'ALTER TABLE user_custom_permissions ADD COLUMN granted BOOLEAN DEFAULT TRUE COMMENT "是否授予权限" AFTER permission_code',
    'SELECT "granted column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加created_by字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE table_name = 'user_custom_permissions' AND column_name = 'created_by' AND table_schema = DATABASE()) = 0,
    'ALTER TABLE user_custom_permissions ADD COLUMN created_by INT COMMENT "授权人ID" AFTER granted',
    'SELECT "created_by column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加updated_at字段
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE table_name = 'user_custom_permissions' AND column_name = 'updated_at' AND table_schema = DATABASE()) = 0,
    'ALTER TABLE user_custom_permissions ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at',
    'SELECT "updated_at column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加外键约束（如果不存在）
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE table_name = 'user_custom_permissions' AND constraint_name = 'fk_user_custom_permissions_created_by' AND table_schema = DATABASE()) = 0,
    'ALTER TABLE user_custom_permissions ADD FOREIGN KEY fk_user_custom_permissions_created_by (created_by) REFERENCES users(id) ON DELETE SET NULL',
    'SELECT "foreign key already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加唯一键约束（如果不存在）
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
    WHERE table_name = 'user_custom_permissions' AND constraint_name = 'uk_user_permission' AND table_schema = DATABASE()) = 0,
    'ALTER TABLE user_custom_permissions ADD UNIQUE KEY uk_user_permission (user_id, permission_code)',
    'SELECT "unique key already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 创建审计日志表
CREATE TABLE IF NOT EXISTS audit_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id INT COMMENT '操作用户ID',
  action VARCHAR(100) NOT NULL COMMENT '操作类型',
  resource VARCHAR(100) COMMENT '资源类型',
  resource_id VARCHAR(100) COMMENT '资源ID',
  result ENUM('granted', 'denied', 'success', 'failure') NOT NULL COMMENT '操作结果',
  details JSON COMMENT '详细信息',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_action (action),
  INDEX idx_resource (resource),
  INDEX idx_result (result),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审计日志表';

-- 4. 创建权限缓存表（用于缓存用户的有效权限）
CREATE TABLE IF NOT EXISTS permission_cache (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  permissions_hash VARCHAR(64) NOT NULL COMMENT '权限数据哈希值',
  permissions_data JSON NOT NULL COMMENT '权限数据',
  expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_user_id (user_id),
  INDEX idx_expires_at (expires_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限缓存表';

-- 5. 创建角色模板表
CREATE TABLE IF NOT EXISTS role_templates (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '模板名称',
  description TEXT COMMENT '模板描述',
  permissions JSON NOT NULL COMMENT '权限配置',
  is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统模板',
  created_by INT COMMENT '创建者ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_name (name),
  INDEX idx_is_system (is_system),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色模板表';

-- 6. 增强role_permissions表，添加更多角色支持
ALTER TABLE role_permissions 
MODIFY COLUMN role ENUM('super_admin', 'admin', 'manager', 'operator', 'user') NOT NULL;

-- 7. 创建权限配置表（用于存储系统权限配置）
CREATE TABLE IF NOT EXISTS permission_configs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
  config_value JSON NOT NULL COMMENT '配置值',
  description TEXT COMMENT '配置描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限配置表';

-- 8. 插入默认权限数据
INSERT IGNORE INTO permissions (name, code, description, module, resource, action) VALUES
-- 用户管理权限
('查看用户列表', 'user.list.view', '查看用户列表的权限', 'user', 'list', 'view'),
('创建用户', 'user.create', '创建新用户的权限', 'user', 'user', 'create'),
('编辑用户', 'user.edit', '编辑用户信息的权限', 'user', 'user', 'edit'),
('删除用户', 'user.delete', '删除用户的权限', 'user', 'user', 'delete'),
('查看用户详情', 'user.view', '查看用户详细信息的权限', 'user', 'user', 'view'),

-- 网站管理权限
('查看网站列表', 'site.list.view', '查看网站列表的权限', 'site', 'list', 'view'),
('创建网站', 'site.create', '创建新网站的权限', 'site', 'website', 'create'),
('编辑网站', 'site.edit', '编辑网站信息的权限', 'site', 'website', 'edit'),
('删除网站', 'site.delete', '删除网站的权限', 'site', 'website', 'delete'),
('查看网站详情', 'site.view', '查看网站详细信息的权限', 'site', 'website', 'view'),
('网站状态检测', 'site.monitor', '执行网站状态检测的权限', 'site', 'website', 'monitor'),

-- 服务器管理权限
('查看服务器列表', 'server.list.view', '查看服务器列表的权限', 'server', 'list', 'view'),
('创建服务器', 'server.create', '创建新服务器的权限', 'server', 'server', 'create'),
('编辑服务器', 'server.edit', '编辑服务器信息的权限', 'server', 'server', 'edit'),
('删除服务器', 'server.delete', '删除服务器的权限', 'server', 'server', 'delete'),
('查看服务器详情', 'server.view', '查看服务器详细信息的权限', 'server', 'server', 'view'),
('服务器监控', 'server.monitor', '查看服务器监控数据的权限', 'server', 'server', 'monitor'),

-- 系统管理权限
('系统设置', 'system.settings.edit', '修改系统设置的权限', 'system', 'settings', 'edit'),
('查看系统设置', 'system.settings.view', '查看系统设置的权限', 'system', 'settings', 'view'),
('系统备份', 'system.backup', '执行系统备份的权限', 'system', 'system', 'backup'),
('查看审计日志', 'system.audit.view', '查看审计日志的权限', 'system', 'audit', 'view'),
('权限管理', 'system.permission.manage', '管理用户权限的权限', 'system', 'permission', 'manage');

-- 9. 插入默认角色权限配置
INSERT IGNORE INTO role_permissions (role, permission_code) VALUES
-- 超级管理员权限（所有权限）
('super_admin', 'user.list.view'),
('super_admin', 'user.create'),
('super_admin', 'user.edit'),
('super_admin', 'user.delete'),
('super_admin', 'user.view'),
('super_admin', 'site.list.view'),
('super_admin', 'site.create'),
('super_admin', 'site.edit'),
('super_admin', 'site.delete'),
('super_admin', 'site.view'),
('super_admin', 'site.monitor'),
('super_admin', 'server.list.view'),
('super_admin', 'server.create'),
('super_admin', 'server.edit'),
('super_admin', 'server.delete'),
('super_admin', 'server.view'),
('super_admin', 'server.monitor'),
('super_admin', 'system.settings.edit'),
('super_admin', 'system.settings.view'),
('super_admin', 'system.backup'),
('super_admin', 'system.audit.view'),
('super_admin', 'system.permission.manage'),

-- 管理员权限（除了用户删除和系统备份）
('admin', 'user.list.view'),
('admin', 'user.create'),
('admin', 'user.edit'),
('admin', 'user.view'),
('admin', 'site.list.view'),
('admin', 'site.create'),
('admin', 'site.edit'),
('admin', 'site.delete'),
('admin', 'site.view'),
('admin', 'site.monitor'),
('admin', 'server.list.view'),
('admin', 'server.create'),
('admin', 'server.edit'),
('admin', 'server.delete'),
('admin', 'server.view'),
('admin', 'server.monitor'),
('admin', 'system.settings.view'),
('admin', 'system.audit.view'),

-- 普通用户权限（只读权限）
('user', 'user.list.view'),
('user', 'user.view'),
('user', 'site.list.view'),
('user', 'site.view'),
('user', 'server.list.view'),
('user', 'server.view'),
('user', 'system.settings.view');

-- 10. 插入默认权限配置
INSERT IGNORE INTO permission_configs (config_key, config_value, description) VALUES
('cache_ttl', '300', '权限缓存过期时间（秒）'),
('audit_retention_days', '90', '审计日志保留天数'),
('max_login_attempts', '5', '最大登录尝试次数'),
('session_timeout', '3600', '会话超时时间（秒）'),
('require_2fa_permissions', '["user.delete", "system.backup", "system.permission.manage"]', '需要二次验证的权限列表');

-- 11. 创建权限系统相关的视图
CREATE OR REPLACE VIEW v_user_effective_permissions AS
SELECT 
    u.id as user_id,
    u.username,
    u.role,
    p.code as permission_code,
    p.name as permission_name,
    p.module,
    p.resource,
    p.action,
    CASE 
        WHEN ucp.granted IS NOT NULL THEN 'custom'
        ELSE 'role'
    END as permission_source,
    COALESCE(ucp.granted, TRUE) as granted
FROM users u
LEFT JOIN role_permissions rp ON u.role = rp.role
LEFT JOIN permissions p ON rp.permission_code = p.code
LEFT JOIN user_custom_permissions ucp ON u.id = ucp.user_id AND p.code = ucp.permission_code
WHERE p.code IS NOT NULL
UNION
SELECT 
    u.id as user_id,
    u.username,
    u.role,
    ucp.permission_code,
    p.name as permission_name,
    p.module,
    p.resource,
    p.action,
    'custom' as permission_source,
    ucp.granted
FROM users u
JOIN user_custom_permissions ucp ON u.id = ucp.user_id
JOIN permissions p ON ucp.permission_code = p.code
WHERE ucp.permission_code NOT IN (
    SELECT rp.permission_code 
    FROM role_permissions rp 
    WHERE rp.role = u.role
);

-- 12. 创建权限统计视图
CREATE OR REPLACE VIEW v_permission_stats AS
SELECT 
    p.module,
    COUNT(*) as total_permissions,
    COUNT(DISTINCT rp.role) as roles_using,
    COUNT(DISTINCT ucp.user_id) as users_with_custom
FROM permissions p
LEFT JOIN role_permissions rp ON p.code = rp.permission_code
LEFT JOIN user_custom_permissions ucp ON p.code = ucp.permission_code
GROUP BY p.module;

-- 13. 添加必要的索引优化
-- 检查并添加role索引
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE table_name = 'users' AND index_name = 'idx_role' AND table_schema = DATABASE()) = 0,
    'ALTER TABLE users ADD INDEX idx_role (role)',
    'SELECT "idx_role already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加username索引
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
    WHERE table_name = 'users' AND index_name = 'idx_username' AND table_schema = DATABASE()) = 0,
    'ALTER TABLE users ADD INDEX idx_username (username)',
    'SELECT "idx_username already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 14. 清理权限缓存（如果存在旧数据）
DELETE FROM permission_cache WHERE expires_at < NOW();

-- 迁移完成标记
INSERT IGNORE INTO permission_configs (config_key, config_value, description) VALUES
('migration_version', '008', '数据库迁移版本号'),
('migration_date', CONCAT('"', NOW(), '"'), '最后迁移时间');

-- 显示迁移结果
SELECT 'Permission System Migration Completed' as status,
       (SELECT COUNT(*) FROM permissions) as total_permissions,
       (SELECT COUNT(*) FROM role_permissions) as total_role_permissions,
       (SELECT COUNT(DISTINCT role) FROM role_permissions) as total_roles;