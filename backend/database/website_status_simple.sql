-- 网站状态检测记录表（简化版本）
CREATE TABLE IF NOT EXISTS website_status_checks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL,
    check_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status_code INT DEFAULT 0,
    response_time INT DEFAULT 0,
    is_accessible BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    check_type ENUM('scheduled', 'manual', 'batch') DEFAULT 'scheduled',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_website_id (website_id),
    INDEX idx_check_time (check_time),
    INDEX idx_website_check_time (website_id, check_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站状态检测记录表';

-- 网站状态统计表（简化版本）
CREATE TABLE IF NOT EXISTS website_status_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL UNIQUE,
    consecutive_failures INT DEFAULT 0 COMMENT '连续失败次数',
    total_checks INT DEFAULT 0 COMMENT '总检测次数',
    success_checks INT DEFAULT 0 COMMENT '成功检测次数',
    last_success_time DATETIME NULL COMMENT '最后成功时间',
    last_failure_time DATETIME NULL COMMENT '最后失败时间',
    first_failure_time DATETIME NULL COMMENT '首次失败时间',
    current_status_code INT DEFAULT 0 COMMENT '当前状态码',
    current_response_time INT DEFAULT 0 COMMENT '当前响应时间',
    notification_sent BOOLEAN DEFAULT FALSE COMMENT '是否已发送通知',
    notification_count INT DEFAULT 0 COMMENT '通知发送次数',
    last_notification_time DATETIME NULL COMMENT '最后通知时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_website_id (website_id),
    INDEX idx_consecutive_failures (consecutive_failures),
    INDEX idx_last_failure_time (last_failure_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站状态统计表';

-- 初始化现有网站的状态统计
INSERT IGNORE INTO website_status_stats (website_id, consecutive_failures, total_checks, success_checks)
SELECT 
    id,
    COALESCE(consecutive_failures, 0),
    0,
    0
FROM websites 
WHERE status = 'active';
