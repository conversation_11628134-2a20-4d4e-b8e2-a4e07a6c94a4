-- 网站权限管理表（对应PHP文档中的zendkee_website_permission）
CREATE TABLE IF NOT EXISTS website_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL COMMENT '网站ID',
    user_id INT NOT NULL COMMENT '用户ID',
    permission_type ENUM('view', 'edit', 'admin') DEFAULT 'view' COMMENT '权限类型',
    granted_by INT NULL COMMENT '授权人ID',
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at DATETIME NULL COMMENT '权限到期时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    notes TEXT NULL COMMENT '备注',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 索引
    INDEX idx_website_id (website_id),
    INDEX idx_user_id (user_id),
    INDEX idx_permission_type (permission_type),
    INDEX idx_is_active (is_active),
    INDEX idx_website_user (website_id, user_id),
    
    -- 外键约束
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- 唯一约束：同一用户对同一网站只能有一种权限
    UNIQUE KEY uk_website_user (website_id, user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站权限管理表';

-- 用户角色表
CREATE TABLE IF NOT EXISTS user_roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(50) NOT NULL UNIQUE COMMENT '角色名称',
    role_description TEXT COMMENT '角色描述',
    permissions JSON COMMENT '权限配置',
    is_system_role BOOLEAN DEFAULT FALSE COMMENT '是否系统角色',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS user_role_assignments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    role_id INT NOT NULL COMMENT '角色ID',
    assigned_by INT NULL COMMENT '分配人ID',
    assigned_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    expires_at DATETIME NULL COMMENT '角色到期时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    INDEX idx_is_active (is_active),
    
    -- 外键约束
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    
    -- 唯一约束：同一用户不能重复分配同一角色
    UNIQUE KEY uk_user_role (user_id, role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色分配表';

-- 插入默认角色
INSERT IGNORE INTO user_roles (role_name, role_description, permissions, is_system_role) VALUES
('admin', '系统管理员', '{"websites": ["view", "edit", "delete", "manage_permissions"], "users": ["view", "edit", "delete"], "system": ["config", "logs"]}', TRUE),
('manager', '网站管理员', '{"websites": ["view", "edit", "manage_permissions"], "users": ["view"]}', TRUE),
('operator', '网站操作员', '{"websites": ["view", "edit"]}', TRUE),
('viewer', '只读用户', '{"websites": ["view"]}', TRUE);

-- 创建权限检查存储过程
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CheckUserWebsitePermission(
    IN p_user_id INT,
    IN p_website_id INT,
    IN p_required_permission VARCHAR(20)
)
BEGIN
    DECLARE permission_count INT DEFAULT 0;
    DECLARE role_permission_count INT DEFAULT 0;
    
    -- 检查直接权限
    SELECT COUNT(*) INTO permission_count
    FROM website_permissions wp
    WHERE wp.user_id = p_user_id
        AND wp.website_id = p_website_id
        AND wp.permission_type = p_required_permission
        AND wp.is_active = TRUE
        AND (wp.expires_at IS NULL OR wp.expires_at > NOW());
    
    -- 检查角色权限
    SELECT COUNT(*) INTO role_permission_count
    FROM user_role_assignments ura
    JOIN user_roles ur ON ura.role_id = ur.id
    WHERE ura.user_id = p_user_id
        AND ura.is_active = TRUE
        AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
        AND JSON_CONTAINS(ur.permissions, CONCAT('"', p_required_permission, '"'), '$.websites');
    
    -- 返回结果
    SELECT 
        CASE 
            WHEN permission_count > 0 OR role_permission_count > 0 THEN 1
            ELSE 0
        END as has_permission;
END //
DELIMITER ;

-- 创建获取用户可访问网站列表的存储过程
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS GetUserAccessibleWebsites(
    IN p_user_id INT,
    IN p_permission_type VARCHAR(20)
)
BEGIN
    -- 通过直接权限或角色权限获取可访问的网站
    SELECT DISTINCT
        w.id,
        w.site_name,
        w.domain,
        w.site_url,
        w.status,
        wp.permission_type as direct_permission,
        GROUP_CONCAT(DISTINCT ur.role_name) as roles
    FROM websites w
    LEFT JOIN website_permissions wp ON w.id = wp.website_id 
        AND wp.user_id = p_user_id 
        AND wp.is_active = TRUE
        AND (wp.expires_at IS NULL OR wp.expires_at > NOW())
    LEFT JOIN user_role_assignments ura ON ura.user_id = p_user_id
        AND ura.is_active = TRUE
        AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
    LEFT JOIN user_roles ur ON ura.role_id = ur.id
        AND JSON_CONTAINS(ur.permissions, CONCAT('"', p_permission_type, '"'), '$.websites')
    WHERE w.status = 'active'
        AND (
            wp.permission_type = p_permission_type OR
            ur.id IS NOT NULL
        )
    GROUP BY w.id, w.site_name, w.domain, w.site_url, w.status, wp.permission_type
    ORDER BY w.site_name;
END //
DELIMITER ;

-- 添加网站表的IP解析相关字段（兼容旧版MySQL）
ALTER TABLE websites
ADD COLUMN server_ip VARCHAR(45) COMMENT '服务器IP地址',
ADD COLUMN server_location VARCHAR(255) COMMENT '服务器地理位置',
ADD COLUMN server_isp VARCHAR(255) COMMENT '服务器ISP',
ADD COLUMN cdn_type VARCHAR(50) DEFAULT 'no' COMMENT 'CDN类型',
ADD COLUMN ip_resolved_at DATETIME COMMENT 'IP解析时间';

-- 添加索引（忽略已存在的错误）
ALTER TABLE websites ADD INDEX idx_server_ip (server_ip);
ALTER TABLE websites ADD INDEX idx_cdn_type (cdn_type);

-- IP地理位置数据表（用于离线查询）
CREATE TABLE IF NOT EXISTS ip_location_ranges (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ip_start BIGINT UNSIGNED NOT NULL COMMENT '起始IP（数字格式）',
    ip_end BIGINT UNSIGNED NOT NULL COMMENT '结束IP（数字格式）',
    country VARCHAR(100) COMMENT '国家',
    region VARCHAR(100) COMMENT '省份/地区',
    city VARCHAR(100) COMMENT '城市',
    isp VARCHAR(255) COMMENT 'ISP提供商',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_ip_range (ip_start, ip_end),
    INDEX idx_country (country)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IP地理位置范围表';
