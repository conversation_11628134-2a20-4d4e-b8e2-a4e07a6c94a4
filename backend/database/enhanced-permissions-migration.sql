-- 增强版权限系统数据库迁移脚本
-- 创建时间: 2025-01-28
-- 描述: 创建支持页面级、模块级、资源级权限控制的数据库表结构

-- 1. 页面权限定义表
CREATE TABLE IF NOT EXISTS page_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    permission_code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限代码',
    permission_name VARCHAR(200) NOT NULL COMMENT '权限名称',
    description TEXT COMMENT '权限描述',
    route_path VARCHAR(200) COMMENT '对应路由路径',
    parent_code VARCHAR(100) COMMENT '父权限代码',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    attributes TEXT COMMENT '权限属性列表，逗号分隔，支持通配符*和拒绝模式!',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    INDEX idx_permission_code (permission_code),
    INDEX idx_parent_code (parent_code),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='页面权限定义表';

-- 2. 用户页面权限表
CREATE TABLE IF NOT EXISTS user_page_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    permission_code VARCHAR(100) NOT NULL COMMENT '权限代码',
    granted BOOLEAN DEFAULT TRUE COMMENT '是否授予权限',
    granted_by INT COMMENT '授权人ID',
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at DATETIME COMMENT '过期时间',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_permission (user_id, permission_code),
    INDEX idx_user_id (user_id),
    INDEX idx_permission_code (permission_code),
    INDEX idx_granted (granted),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户页面权限表';

-- 3. 增强版网站权限表
CREATE TABLE IF NOT EXISTS enhanced_website_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    website_id INT NOT NULL COMMENT '网站ID',
    access_granted BOOLEAN DEFAULT TRUE COMMENT '是否可访问',
    edit_granted BOOLEAN DEFAULT FALSE COMMENT '是否可编辑',
    view_credentials BOOLEAN DEFAULT FALSE COMMENT '是否可查看密码',
    manage_ssl BOOLEAN DEFAULT FALSE COMMENT '是否可管理SSL',
    manage_backup BOOLEAN DEFAULT FALSE COMMENT '是否可管理备份',
    granted_by INT COMMENT '授权人ID',
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at DATETIME COMMENT '过期时间',
    notes TEXT COMMENT '备注',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_website (user_id, website_id),
    INDEX idx_user_id (user_id),
    INDEX idx_website_id (website_id),
    INDEX idx_is_active (is_active),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='增强版网站权限表';

-- 4. 增强版服务器权限表
CREATE TABLE IF NOT EXISTS enhanced_server_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    server_id INT NOT NULL COMMENT '服务器ID',
    access_granted BOOLEAN DEFAULT TRUE COMMENT '是否可访问',
    edit_granted BOOLEAN DEFAULT FALSE COMMENT '是否可编辑',
    connect_granted BOOLEAN DEFAULT FALSE COMMENT '是否可SSH连接',
    view_credentials BOOLEAN DEFAULT FALSE COMMENT '是否可查看连接密码',
    granted_by INT COMMENT '授权人ID',
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
    expires_at DATETIME COMMENT '过期时间',
    notes TEXT COMMENT '备注',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_server (user_id, server_id),
    INDEX idx_user_id (user_id),
    INDEX idx_server_id (server_id),
    INDEX idx_is_active (is_active),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='增强版服务器权限表';

-- 插入默认页面权限数据
INSERT IGNORE INTO page_permissions (permission_code, permission_name, description, route_path, sort_order, attributes) VALUES
-- 主要页面权限
('dashboard', '仪表盘', '访问系统仪表盘', '/dashboard', 1, '*'),
('website.basic', '网站管理-基础管理', '访问网站基础管理功能', '/websites/basic', 2, '*'),
('website.advanced', '网站管理-高级管理', '访问网站高级管理功能', '/websites/advanced', 3, '*'),
('server.list', '服务器管理-服务器列表', '访问服务器列表页面', '/servers/list', 4, '*'),
('server.inventory', '服务器管理-台账管理', '访问服务器台账管理', '/servers/inventory', 5, '*'),
('user.management', '用户管理', '访问用户管理页面', '/users', 6, '*'),
('system.settings', '系统设置', '访问系统设置页面', '/settings', 7, '*'),

-- 网站管理子权限
('website.basic.view', '查看网站基础信息', '查看网站基础信息', NULL, 21, 'name,url,status,!password'),
('website.basic.create', '创建网站', '创建新网站', NULL, 22, 'name,url,description'),
('website.basic.edit', '编辑网站基础信息', '编辑网站基础信息', NULL, 23, 'name,url,description,!password'),
('website.basic.delete', '删除网站', '删除网站', NULL, 24, 'id,name'),
('website.basic.monitor', '监控网站状态', '监控网站运行状态', NULL, 25, 'status,uptime,response_time'),

('website.advanced.view', '查看网站高级信息', '查看网站高级配置信息', NULL, 31, '*'),
('website.advanced.ssl', 'SSL证书管理', '管理网站SSL证书', NULL, 32, 'ssl_cert,ssl_key,ssl_status'),
('website.advanced.backup', '备份管理', '管理网站备份', NULL, 33, 'backup_config,backup_history'),
('website.advanced.security', '安全配置', '配置网站安全设置', NULL, 34, 'security_config,firewall_rules'),
('website.advanced.performance', '性能优化', '网站性能优化设置', NULL, 35, 'cache_config,cdn_config'),

-- 服务器管理子权限
('server.list.view', '查看服务器列表', '查看服务器列表信息', NULL, 41, 'name,ip_address,status,!ssh_password'),
('server.list.create', '创建服务器', '创建新服务器记录', NULL, 42, 'name,ip_address,description'),
('server.list.edit', '编辑服务器信息', '编辑服务器基础信息', NULL, 43, 'name,ip_address,description,!ssh_password'),
('server.list.delete', '删除服务器', '删除服务器记录', NULL, 44, 'id,name'),
('server.list.connect', 'SSH连接服务器', '通过SSH连接服务器', NULL, 45, 'ssh_host,ssh_port,ssh_username'),

('server.inventory.view', '查看服务器台账', '查看服务器台账信息', NULL, 51, '*,!sensitive_data'),
('server.inventory.export', '导出台账数据', '导出服务器台账数据', NULL, 52, '*,!passwords,!keys'),
('server.inventory.import', '导入台账数据', '导入服务器台账数据', NULL, 53, 'basic_info,hardware_info'),
('server.inventory.audit', '审计台账变更', '审计服务器台账变更记录', NULL, 54, 'audit_logs,change_history'),

-- 用户管理子权限
('user.view', '查看用户信息', '查看用户基础信息', NULL, 61, 'username,email,role,status,!password'),
('user.create', '创建用户', '创建新用户账户', NULL, 62, 'username,email,role'),
('user.edit', '编辑用户信息', '编辑用户基础信息', NULL, 63, 'username,email,role,status,!password'),
('user.delete', '删除用户', '删除用户账户', NULL, 64, 'id,username'),
('user.permission.manage', '管理用户权限', '管理用户权限分配', NULL, 65, '*'),

-- 系统设置子权限
('system.settings.view', '查看系统设置', '查看系统配置信息', NULL, 71, '*,!database_password,!api_keys'),
('system.settings.edit', '编辑系统设置', '修改系统配置', NULL, 72, 'basic_config,email_config,!security_keys'),
('system.logs.view', '查看系统日志', '查看系统运行日志', NULL, 73, 'log_content,log_level,timestamp'),
('system.backup.manage', '系统备份管理', '管理系统备份', NULL, 74, 'backup_config,backup_schedule');

-- 设置权限层级关系（更新parent_code）
UPDATE page_permissions SET parent_code = 'website.basic' WHERE permission_code LIKE 'website.basic.%';
UPDATE page_permissions SET parent_code = 'website.advanced' WHERE permission_code LIKE 'website.advanced.%';
UPDATE page_permissions SET parent_code = 'server.list' WHERE permission_code LIKE 'server.list.%';
UPDATE page_permissions SET parent_code = 'server.inventory' WHERE permission_code LIKE 'server.inventory.%';
UPDATE page_permissions SET parent_code = 'user.management' WHERE permission_code LIKE 'user.%' AND permission_code != 'user.management';
UPDATE page_permissions SET parent_code = 'system.settings' WHERE permission_code LIKE 'system.%';

-- 为超级管理员创建默认页面权限（所有权限）
INSERT IGNORE INTO user_page_permissions (user_id, permission_code, granted, granted_by)
SELECT 
    u.id as user_id,
    pp.permission_code,
    TRUE as granted,
    u.id as granted_by
FROM users u
CROSS JOIN page_permissions pp
WHERE u.role = 'super_admin' AND pp.is_active = TRUE;

-- 为管理员创建默认页面权限（除了高级管理功能）
INSERT IGNORE INTO user_page_permissions (user_id, permission_code, granted, granted_by)
SELECT 
    u.id as user_id,
    pp.permission_code,
    TRUE as granted,
    u.id as granted_by
FROM users u
CROSS JOIN page_permissions pp
WHERE u.role = 'admin' 
    AND pp.is_active = TRUE
    AND pp.permission_code NOT IN (
        'website.advanced',
        'website.advanced.view',
        'website.advanced.ssl',
        'website.advanced.backup',
        'website.advanced.security',
        'website.advanced.performance',
        'server.inventory',
        'server.inventory.view',
        'server.inventory.export',
        'server.inventory.import',
        'server.inventory.audit',
        'user.delete',
        'user.permission.manage',
        'system.settings.edit',
        'system.backup.manage'
    );

-- 为普通用户创建默认页面权限（只读权限）
INSERT IGNORE INTO user_page_permissions (user_id, permission_code, granted, granted_by)
SELECT 
    u.id as user_id,
    pp.permission_code,
    TRUE as granted,
    u.id as granted_by
FROM users u
CROSS JOIN page_permissions pp
WHERE u.role = 'user' 
    AND pp.is_active = TRUE
    AND pp.permission_code IN (
        'dashboard',
        'website.basic',
        'website.basic.view',
        'website.basic.monitor',
        'server.list',
        'server.list.view',
        'user.management',
        'user.view',
        'system.settings',
        'system.settings.view'
    );

-- 创建权限检查存储过程
DELIMITER //

-- 检查用户页面权限
CREATE PROCEDURE IF NOT EXISTS CheckUserPagePermission(
    IN p_user_id INT,
    IN p_permission_code VARCHAR(100)
)
BEGIN
    DECLARE v_user_role VARCHAR(50);
    DECLARE v_has_permission BOOLEAN DEFAULT FALSE;
    
    -- 获取用户角色
    SELECT role INTO v_user_role FROM users WHERE id = p_user_id;
    
    -- 超级管理员拥有所有权限
    IF v_user_role = 'super_admin' THEN
        SET v_has_permission = TRUE;
    ELSE
        -- 检查用户是否有该页面权限
        SELECT COUNT(*) > 0 INTO v_has_permission
        FROM user_page_permissions upp
        WHERE upp.user_id = p_user_id 
            AND upp.permission_code = p_permission_code
            AND upp.granted = TRUE
            AND (upp.expires_at IS NULL OR upp.expires_at > NOW());
    END IF;
    
    SELECT v_has_permission as has_permission;
END //

-- 检查用户网站权限
CREATE PROCEDURE IF NOT EXISTS CheckUserWebsitePermission(
    IN p_user_id INT,
    IN p_website_id INT,
    IN p_action VARCHAR(50)
)
BEGIN
    DECLARE v_user_role VARCHAR(50);
    DECLARE v_has_permission BOOLEAN DEFAULT FALSE;
    
    -- 获取用户角色
    SELECT role INTO v_user_role FROM users WHERE id = p_user_id;
    
    -- 超级管理员拥有所有权限
    IF v_user_role = 'super_admin' THEN
        SET v_has_permission = TRUE;
    ELSE
        -- 检查用户是否有该网站的权限
        SELECT 
            CASE p_action
                WHEN 'access' THEN ewp.access_granted
                WHEN 'edit' THEN ewp.edit_granted
                WHEN 'view_credentials' THEN ewp.view_credentials
                WHEN 'manage_ssl' THEN ewp.manage_ssl
                WHEN 'manage_backup' THEN ewp.manage_backup
                ELSE FALSE
            END INTO v_has_permission
        FROM enhanced_website_permissions ewp
        WHERE ewp.user_id = p_user_id 
            AND ewp.website_id = p_website_id
            AND ewp.is_active = TRUE
            AND (ewp.expires_at IS NULL OR ewp.expires_at > NOW());
    END IF;
    
    SELECT v_has_permission as has_permission;
END //

DELIMITER ;

-- 5. 权限变更日志表
CREATE TABLE IF NOT EXISTS permission_change_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '被修改权限的用户ID',
    changed_by INT NOT NULL COMMENT '修改权限的用户ID',
    change_type VARCHAR(50) NOT NULL COMMENT '变更类型',
    details JSON COMMENT '变更详情',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',

    INDEX idx_user_id (user_id),
    INDEX idx_changed_by (changed_by),
    INDEX idx_change_type (change_type),
    INDEX idx_created_at (created_at),

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限变更日志表';

-- 创建索引优化查询性能
-- 注意：如果索引已存在会报错，但不影响功能
CREATE INDEX idx_user_page_permissions_lookup ON user_page_permissions(user_id, permission_code, granted);
CREATE INDEX idx_enhanced_website_permissions_lookup ON enhanced_website_permissions(user_id, website_id, is_active);
CREATE INDEX idx_enhanced_server_permissions_lookup ON enhanced_server_permissions(user_id, server_id, is_active);
