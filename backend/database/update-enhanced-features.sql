-- 增强网站管理功能数据库更新脚本
-- 添加性能评分、SSL状态、域名状态、密码管理等功能

-- 创建网站密码管理表
CREATE TABLE IF NOT EXISTS website_credentials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL COMMENT '网站ID',
    account_type ENUM('admin', 'user', 'ftp', 'database', 'hosting', 'other') NOT NULL DEFAULT 'admin' COMMENT '账号类型',
    username VARCHAR(255) NOT NULL COMMENT '用户名',
    password TEXT NOT NULL COMMENT '密码(加密存储)',
    email VARCHAR(255) COMMENT '邮箱',
    url VARCHAR(500) COMMENT '登录URL',
    description TEXT COMMENT '描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    INDEX idx_website_id (website_id),
    INDEX idx_account_type (account_type),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站密码管理表';

-- 创建网站监控日志表
CREATE TABLE IF NOT EXISTS website_monitor_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL COMMENT '网站ID',
    check_type ENUM('access', 'ssl', 'domain', 'performance', 'security') NOT NULL COMMENT '检查类型',
    status ENUM('success', 'warning', 'error') NOT NULL COMMENT '检查状态',
    response_time INT COMMENT '响应时间(ms)',
    status_code INT COMMENT 'HTTP状态码',
    error_message TEXT COMMENT '错误信息',
    check_data JSON COMMENT '检查详细数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    INDEX idx_website_id (website_id),
    INDEX idx_check_type (check_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站监控日志表';

-- 创建通知配置表
CREATE TABLE IF NOT EXISTS notification_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type ENUM('feishu', 'email', 'sms', 'webhook') NOT NULL COMMENT '通知类型',
    name VARCHAR(100) NOT NULL COMMENT '配置名称',
    config JSON NOT NULL COMMENT '配置信息',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_type (type),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知配置表';

-- 创建通知记录表
CREATE TABLE IF NOT EXISTS notification_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL COMMENT '网站ID',
    notification_type ENUM('feishu', 'email', 'sms', 'webhook') NOT NULL COMMENT '通知类型',
    trigger_reason ENUM('consecutive_failures', 'ssl_expiry', 'domain_expiry', 'manual') NOT NULL COMMENT '触发原因',
    message TEXT NOT NULL COMMENT '通知内容',
    status ENUM('pending', 'sent', 'failed') DEFAULT 'pending' COMMENT '发送状态',
    response_data JSON COMMENT '响应数据',
    error_message TEXT COMMENT '错误信息',
    sent_at TIMESTAMP NULL COMMENT '发送时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    INDEX idx_website_id (website_id),
    INDEX idx_notification_type (notification_type),
    INDEX idx_trigger_reason (trigger_reason),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知记录表';

-- 检查并添加新字段到websites表

-- 异常状态统计和通知相关字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND column_name = 'consecutive_failures') = 0,
    'ALTER TABLE websites ADD COLUMN consecutive_failures INT DEFAULT 0 COMMENT ''连续失败次数'' AFTER access_status',
    'SELECT ''consecutive_failures column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND column_name = 'last_failure_time') = 0,
    'ALTER TABLE websites ADD COLUMN last_failure_time TIMESTAMP NULL COMMENT ''最后失败时间'' AFTER consecutive_failures',
    'SELECT ''last_failure_time column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND column_name = 'notification_sent') = 0,
    'ALTER TABLE websites ADD COLUMN notification_sent BOOLEAN DEFAULT FALSE COMMENT ''是否已发送通知'' AFTER last_failure_time',
    'SELECT ''notification_sent column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND column_name = 'last_notification_time') = 0,
    'ALTER TABLE websites ADD COLUMN last_notification_time TIMESTAMP NULL COMMENT ''最后通知时间'' AFTER notification_sent',
    'SELECT ''last_notification_time column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- SSL相关字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'ssl_status') = 0,
    'ALTER TABLE websites ADD COLUMN ssl_status ENUM(''valid'', ''expired'', ''expiring_soon'', ''invalid'', ''unknown'') DEFAULT ''unknown'' COMMENT ''SSL状态'' AFTER ssl_expire_date',
    'SELECT ''ssl_status column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'ssl_issuer') = 0,
    'ALTER TABLE websites ADD COLUMN ssl_issuer VARCHAR(255) COMMENT ''SSL证书颁发者'' AFTER ssl_status',
    'SELECT ''ssl_issuer column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'ssl_last_check') = 0,
    'ALTER TABLE websites ADD COLUMN ssl_last_check TIMESTAMP COMMENT ''SSL最后检查时间'' AFTER ssl_issuer',
    'SELECT ''ssl_last_check column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 域名相关字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'domain_status') = 0,
    'ALTER TABLE websites ADD COLUMN domain_status ENUM(''active'', ''expired'', ''expiring_soon'', ''suspended'', ''unknown'') DEFAULT ''unknown'' COMMENT ''域名状态'' AFTER domain_expire_date',
    'SELECT ''domain_status column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'domain_registrar') = 0,
    'ALTER TABLE websites ADD COLUMN domain_registrar VARCHAR(255) COMMENT ''域名注册商'' AFTER domain_status',
    'SELECT ''domain_registrar column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'domain_last_check') = 0,
    'ALTER TABLE websites ADD COLUMN domain_last_check TIMESTAMP COMMENT ''域名最后检查时间'' AFTER domain_registrar',
    'SELECT ''domain_last_check column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 访问状态相关字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'access_status') = 0,
    'ALTER TABLE websites ADD COLUMN access_status ENUM(''online'', ''offline'', ''error'', ''unknown'') DEFAULT ''unknown'' COMMENT ''访问状态'' AFTER access_status_code',
    'SELECT ''access_status column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 性能评分相关字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'performance_score') = 0,
    'ALTER TABLE websites ADD COLUMN performance_score INT COMMENT ''性能评分(0-100)'' AFTER last_check_time',
    'SELECT ''performance_score column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'mobile_score') = 0,
    'ALTER TABLE websites ADD COLUMN mobile_score INT COMMENT ''移动端评分(0-100)'' AFTER performance_score',
    'SELECT ''mobile_score column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'desktop_score') = 0,
    'ALTER TABLE websites ADD COLUMN desktop_score INT COMMENT ''桌面端评分(0-100)'' AFTER mobile_score',
    'SELECT ''desktop_score column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'page_load_time') = 0,
    'ALTER TABLE websites ADD COLUMN page_load_time DECIMAL(5,2) COMMENT ''页面加载时间(秒)'' AFTER desktop_score',
    'SELECT ''page_load_time column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'first_contentful_paint') = 0,
    'ALTER TABLE websites ADD COLUMN first_contentful_paint DECIMAL(5,2) COMMENT ''首次内容绘制时间(秒)'' AFTER page_load_time',
    'SELECT ''first_contentful_paint column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'largest_contentful_paint') = 0,
    'ALTER TABLE websites ADD COLUMN largest_contentful_paint DECIMAL(5,2) COMMENT ''最大内容绘制时间(秒)'' AFTER first_contentful_paint',
    'SELECT ''largest_contentful_paint column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'cumulative_layout_shift') = 0,
    'ALTER TABLE websites ADD COLUMN cumulative_layout_shift DECIMAL(5,3) COMMENT ''累积布局偏移'' AFTER largest_contentful_paint',
    'SELECT ''cumulative_layout_shift column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'performance_last_check') = 0,
    'ALTER TABLE websites ADD COLUMN performance_last_check TIMESTAMP COMMENT ''性能最后检查时间'' AFTER cumulative_layout_shift',
    'SELECT ''performance_last_check column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 安全扫描相关字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'security_score') = 0,
    'ALTER TABLE websites ADD COLUMN security_score INT COMMENT ''安全评分(0-100)'' AFTER performance_last_check',
    'SELECT ''security_score column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'vulnerabilities_critical') = 0,
    'ALTER TABLE websites ADD COLUMN vulnerabilities_critical INT DEFAULT 0 COMMENT ''严重漏洞数量'' AFTER security_score',
    'SELECT ''vulnerabilities_critical column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'vulnerabilities_high') = 0,
    'ALTER TABLE websites ADD COLUMN vulnerabilities_high INT DEFAULT 0 COMMENT ''高危漏洞数量'' AFTER vulnerabilities_critical',
    'SELECT ''vulnerabilities_high column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'vulnerabilities_medium') = 0,
    'ALTER TABLE websites ADD COLUMN vulnerabilities_medium INT DEFAULT 0 COMMENT ''中危漏洞数量'' AFTER vulnerabilities_high',
    'SELECT ''vulnerabilities_medium column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'vulnerabilities_low') = 0,
    'ALTER TABLE websites ADD COLUMN vulnerabilities_low INT DEFAULT 0 COMMENT ''低危漏洞数量'' AFTER vulnerabilities_medium',
    'SELECT ''vulnerabilities_low column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'websites' 
     AND table_schema = 'sitemanager' 
     AND column_name = 'security_last_scan') = 0,
    'ALTER TABLE websites ADD COLUMN security_last_scan TIMESTAMP COMMENT ''安全最后扫描时间'' AFTER vulnerabilities_low',
    'SELECT ''security_last_scan column already exists'''
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（忽略错误如果索引已存在）
SET @sql = 'CREATE INDEX idx_ssl_status ON websites(ssl_status)';
SET @ignore_error = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND index_name = 'idx_ssl_status') = 0,
    @sql,
    'SELECT ''idx_ssl_status already exists'''
));
PREPARE stmt FROM @ignore_error;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'CREATE INDEX idx_domain_status ON websites(domain_status)';
SET @ignore_error = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND index_name = 'idx_domain_status') = 0,
    @sql,
    'SELECT ''idx_domain_status already exists'''
));
PREPARE stmt FROM @ignore_error;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'CREATE INDEX idx_access_status ON websites(access_status)';
SET @ignore_error = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND index_name = 'idx_access_status') = 0,
    @sql,
    'SELECT ''idx_access_status already exists'''
));
PREPARE stmt FROM @ignore_error;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'CREATE INDEX idx_performance_score ON websites(performance_score)';
SET @ignore_error = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND index_name = 'idx_performance_score') = 0,
    @sql,
    'SELECT ''idx_performance_score already exists'''
));
PREPARE stmt FROM @ignore_error;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 插入一些测试数据
INSERT IGNORE INTO website_credentials (website_id, account_type, username, password, email, url, description) VALUES
(1, 'admin', 'admin', 'encrypted_password_123', '<EMAIL>', 'https://example.com/wp-admin', 'WordPress管理员账号'),
(1, 'user', 'editor', 'encrypted_password_456', '<EMAIL>', 'https://example.com/wp-admin', 'WordPress编辑账号');

SELECT 'Enhanced website management features database update completed!' as message;
