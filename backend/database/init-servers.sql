-- 服务器管理表
CREATE TABLE IF NOT EXISTS servers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '服务器名称',
    ip_address VARCHAR(45) NOT NULL COMMENT 'IP地址',
    location VARCHAR(100) NOT NULL COMMENT '机房位置',
    provider VARCHAR(50) NOT NULL COMMENT '服务商',
    department VARCHAR(100) COMMENT '所属部门',

    -- 配置信息
    cpu VARCHAR(100) COMMENT 'CPU配置',
    memory VARCHAR(50) COMMENT '内存配置',
    storage VARCHAR(100) COMMENT '存储配置',
    bandwidth VARCHAR(50) COMMENT '带宽配置',
    os VARCHAR(100) COMMENT '操作系统',
    
    -- 到期和费用
    expire_date DATE COMMENT '到期时间',
    renewal_fee DECIMAL(10,2) COMMENT '续费金额',
    
    -- 状态和监控
    status ENUM('active', 'inactive', 'maintenance', 'expired') DEFAULT 'active' COMMENT '状态',
    monitoring_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用监控',
    
    -- 告警阈值
    cpu_threshold INT DEFAULT 80 COMMENT 'CPU告警阈值',
    memory_threshold INT DEFAULT 85 COMMENT '内存告警阈值',
    disk_threshold INT DEFAULT 90 COMMENT '磁盘告警阈值',
    network_threshold INT DEFAULT 80 COMMENT '网络告警阈值',
    
    -- SSH连接配置
    ssh_port INT DEFAULT 22 COMMENT 'SSH端口',
    ssh_username VARCHAR(50) COMMENT 'SSH用户名',
    ssh_auth_type ENUM('password', 'key') DEFAULT 'password' COMMENT 'SSH认证方式',
    ssh_password TEXT COMMENT 'SSH密码(加密存储)',
    ssh_private_key TEXT COMMENT 'SSH私钥内容',
    ssh_key_passphrase TEXT COMMENT 'SSH密钥密码',
    
    -- 备注和时间
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_ip_address (ip_address),
    INDEX idx_status (status),
    INDEX idx_provider (provider),
    INDEX idx_expire_date (expire_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务器管理表';

-- 服务器负载信息表
CREATE TABLE IF NOT EXISTS server_load_info (
    id INT AUTO_INCREMENT PRIMARY KEY,
    server_id INT NOT NULL COMMENT '服务器ID',
    cpu_usage DECIMAL(5,2) DEFAULT 0 COMMENT 'CPU使用率',
    memory_usage DECIMAL(5,2) DEFAULT 0 COMMENT '内存使用率',
    disk_usage DECIMAL(5,2) DEFAULT 0 COMMENT '磁盘使用率',
    network_in DECIMAL(10,2) DEFAULT 0 COMMENT '网络入流量 MB/s',
    network_out DECIMAL(10,2) DEFAULT 0 COMMENT '网络出流量 MB/s',
    uptime BIGINT DEFAULT 0 COMMENT '运行时间(秒)',
    load_average_1 DECIMAL(5,2) DEFAULT 0 COMMENT '1分钟负载平均值',
    load_average_5 DECIMAL(5,2) DEFAULT 0 COMMENT '5分钟负载平均值',
    load_average_15 DECIMAL(5,2) DEFAULT 0 COMMENT '15分钟负载平均值',
    processes INT DEFAULT 0 COMMENT '进程数',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE CASCADE,
    INDEX idx_server_id (server_id),
    INDEX idx_last_updated (last_updated)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务器负载信息表';

-- 插入示例数据
INSERT INTO servers (
    name, ip_address, location, provider, department, cpu, memory, storage, bandwidth, os,
    expire_date, renewal_fee, status, monitoring_enabled,
    cpu_threshold, memory_threshold, disk_threshold, network_threshold,
    ssh_port, ssh_username, ssh_auth_type, ssh_password,
    notes
) VALUES
(
    'Web-Server-01', '*************', '北京-阿里云', '阿里云', '技术部',
    '4核 Intel Xeon', '8GB DDR4', '100GB SSD', '10Mbps', 'Ubuntu 20.04 LTS',
    '2024-12-31', 3600.00, 'active', TRUE,
    80, 85, 90, 80,
    22, 'root', 'password', 'demo123456',
    '主要Web服务器，运行WordPress站点'
),
(
    'DB-Server-01', '*************', '上海-腾讯云', '腾讯云', '技术部',
    '8核 Intel Xeon', '16GB DDR4', '500GB SSD', '20Mbps', 'CentOS 8',
    '2024-08-15', 7200.00, 'active', TRUE,
    75, 80, 85, 75,
    22, 'root', 'password', 'demo123456',
    '数据库服务器，MySQL主库'
),
(
    'Cache-Server-01', '*************', '深圳-华为云', '华为云', '运维部',
    '2核 Intel Xeon', '4GB DDR4', '50GB SSD', '5Mbps', 'Ubuntu 18.04 LTS',
    '2024-07-20', 1800.00, 'active', TRUE,
    70, 80, 85, 75,
    22, 'root', 'password', 'demo123456',
    'Redis缓存服务器'
),
(
    'Backup-Server-01', '192.168.1.103', '广州-腾讯云', '腾讯云', '运维部',
    '2核 Intel Xeon', '8GB DDR4', '1TB HDD', '10Mbps', 'CentOS 7',
    '2024-06-30', 2400.00, 'maintenance', FALSE,
    60, 75, 95, 70,
    22, 'root', 'password', 'demo123456',
    '备份服务器，定期维护中'
);

-- 插入负载信息示例数据
INSERT INTO server_load_info (
    server_id, cpu_usage, memory_usage, disk_usage, network_in, network_out,
    uptime, load_average_1, load_average_5, load_average_15, processes
) VALUES
(1, 45.0, 68.0, 35.0, 2.5, 1.8, 2592000, 0.8, 0.9, 1.1, 156),
(2, 25.0, 72.0, 58.0, 1.2, 0.8, 1728000, 0.5, 0.6, 0.7, 89),
(3, 15.0, 45.0, 25.0, 0.8, 0.5, 1296000, 0.2, 0.3, 0.4, 45),
(4, 5.0, 20.0, 75.0, 0.2, 0.1, 864000, 0.1, 0.1, 0.2, 32);

-- SSH配置表
CREATE TABLE IF NOT EXISTS ssh_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '配置名称',
    username VARCHAR(50) NOT NULL COMMENT 'SSH用户名',
    auth_type ENUM('password', 'key') NOT NULL DEFAULT 'password' COMMENT '认证方式',
    password TEXT COMMENT 'SSH密码(加密存储)',
    private_key TEXT COMMENT 'SSH私钥内容',
    key_passphrase TEXT COMMENT 'SSH密钥密码',
    port INT DEFAULT 22 COMMENT 'SSH端口',
    description TEXT COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_name (name),
    INDEX idx_auth_type (auth_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='SSH配置表';

-- 平台表
CREATE TABLE IF NOT EXISTS platforms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '平台名称',
    description VARCHAR(200) COMMENT '平台描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_name (name),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='平台表';

-- 网站表
CREATE TABLE IF NOT EXISTS websites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    site_name VARCHAR(100) NOT NULL COMMENT '站点名称',
    domain VARCHAR(100) NOT NULL COMMENT '域名',
    site_url VARCHAR(255) NOT NULL COMMENT '站点URL',
    platform_id INT NOT NULL COMMENT '平台ID',
    server_id INT COMMENT '服务器ID',
    industry VARCHAR(50) COMMENT '所属行业',
    status ENUM('active', 'inactive', 'suspended', 'expired') DEFAULT 'active' COMMENT '状态',
    online_date DATE COMMENT '上线时间',
    expire_date DATE COMMENT '到期时间',
    project_amount DECIMAL(10,2) COMMENT '项目金额',
    renewal_fee DECIMAL(10,2) COMMENT '续费金额',

    -- SSL相关字段
    ssl_expire_date DATE COMMENT 'SSL到期时间',
    ssl_status ENUM('valid', 'expired', 'expiring_soon', 'invalid', 'unknown') DEFAULT 'unknown' COMMENT 'SSL状态',
    ssl_issuer VARCHAR(255) COMMENT 'SSL证书颁发者',
    ssl_last_check TIMESTAMP COMMENT 'SSL最后检查时间',

    -- 域名相关字段
    domain_expire_date DATE COMMENT '域名到期时间',
    domain_status ENUM('active', 'expired', 'expiring_soon', 'suspended', 'unknown') DEFAULT 'unknown' COMMENT '域名状态',
    domain_registrar VARCHAR(255) COMMENT '域名注册商',
    domain_last_check TIMESTAMP COMMENT '域名最后检查时间',

    -- 访问状态相关字段
    access_status_code INT DEFAULT 200 COMMENT '访问状态码',
    access_status ENUM('online', 'offline', 'error', 'unknown') DEFAULT 'unknown' COMMENT '访问状态',
    response_time INT COMMENT '响应时间(ms)',
    last_check_time TIMESTAMP COMMENT '最后检查时间',

    -- 性能评分相关字段
    performance_score INT COMMENT '性能评分(0-100)',
    mobile_score INT COMMENT '移动端评分(0-100)',
    desktop_score INT COMMENT '桌面端评分(0-100)',
    page_load_time DECIMAL(5,2) COMMENT '页面加载时间(秒)',
    first_contentful_paint DECIMAL(5,2) COMMENT '首次内容绘制时间(秒)',
    largest_contentful_paint DECIMAL(5,2) COMMENT '最大内容绘制时间(秒)',
    cumulative_layout_shift DECIMAL(5,3) COMMENT '累积布局偏移',
    performance_last_check TIMESTAMP COMMENT '性能最后检查时间',

    -- 安全扫描相关字段
    security_score INT COMMENT '安全评分(0-100)',
    vulnerabilities_critical INT DEFAULT 0 COMMENT '严重漏洞数量',
    vulnerabilities_high INT DEFAULT 0 COMMENT '高危漏洞数量',
    vulnerabilities_medium INT DEFAULT 0 COMMENT '中危漏洞数量',
    vulnerabilities_low INT DEFAULT 0 COMMENT '低危漏洞数量',
    security_last_scan TIMESTAMP COMMENT '安全最后扫描时间',

    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (platform_id) REFERENCES platforms(id) ON DELETE RESTRICT,
    FOREIGN KEY (server_id) REFERENCES servers(id) ON DELETE SET NULL,
    INDEX idx_site_name (site_name),
    INDEX idx_domain (domain),
    INDEX idx_status (status),
    INDEX idx_platform_id (platform_id),
    INDEX idx_server_id (server_id),
    INDEX idx_expire_date (expire_date),
    INDEX idx_industry (industry),
    INDEX idx_ssl_status (ssl_status),
    INDEX idx_domain_status (domain_status),
    INDEX idx_access_status (access_status),
    INDEX idx_performance_score (performance_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站表';

-- 网站密码管理表
CREATE TABLE IF NOT EXISTS website_credentials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL COMMENT '网站ID',
    account_type ENUM('admin', 'user', 'ftp', 'database', 'hosting', 'other') NOT NULL DEFAULT 'admin' COMMENT '账号类型',
    username VARCHAR(255) NOT NULL COMMENT '用户名',
    password TEXT NOT NULL COMMENT '密码(加密存储)',
    email VARCHAR(255) COMMENT '邮箱',
    url VARCHAR(500) COMMENT '登录URL',
    description TEXT COMMENT '描述',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    INDEX idx_website_id (website_id),
    INDEX idx_account_type (account_type),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站密码管理表';

-- 网站监控日志表
CREATE TABLE IF NOT EXISTS website_monitor_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL COMMENT '网站ID',
    check_type ENUM('access', 'ssl', 'domain', 'performance', 'security') NOT NULL COMMENT '检查类型',
    status ENUM('success', 'warning', 'error') NOT NULL COMMENT '检查状态',
    response_time INT COMMENT '响应时间(ms)',
    status_code INT COMMENT 'HTTP状态码',
    error_message TEXT COMMENT '错误信息',
    check_data JSON COMMENT '检查详细数据',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    INDEX idx_website_id (website_id),
    INDEX idx_check_type (check_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站监控日志表';

-- 网站附件表
CREATE TABLE IF NOT EXISTS website_attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL COMMENT '网站ID',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
    file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    file_type VARCHAR(100) NOT NULL COMMENT '文件类型',
    mime_type VARCHAR(100) NOT NULL COMMENT 'MIME类型',
    category ENUM('image','pdf','excel','word','other') NOT NULL DEFAULT 'other' COMMENT '文件分类',
    description TEXT COMMENT '文件描述',
    uploaded_by INT COMMENT '上传者ID',
    is_preview_available BOOLEAN DEFAULT FALSE COMMENT '是否支持预览',
    thumbnail_path VARCHAR(500) COMMENT '缩略图路径',
    download_count INT DEFAULT 0 COMMENT '下载次数',
    last_accessed TIMESTAMP NULL COMMENT '最后访问时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE,
    INDEX idx_website_id (website_id),
    INDEX idx_category (category),
    INDEX idx_file_type (file_type),
    INDEX idx_uploaded_by (uploaded_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站附件表';

-- 插入平台数据
INSERT INTO platforms (name, description, is_active) VALUES
('WordPress', 'WordPress CMS', TRUE),
('WooCommerce', 'WordPress电商平台', TRUE),
('Shopify', 'Shopify电商平台', TRUE),
('Laravel', 'Laravel PHP框架', TRUE),
('Next.js', 'Next.js React框架', TRUE),
('Vue.js', 'Vue.js前端框架', TRUE),
('Static', '静态网站', TRUE);

-- 插入示例网站数据
INSERT INTO websites (
    site_name, domain, site_url, platform_id, server_id, industry, status,
    online_date, expire_date, project_amount, renewal_fee,
    ssl_expire_date, domain_expire_date, access_status_code, response_time,
    last_check_time, notes
) VALUES
(
    '企业官网', 'github.com', 'https://github.com', 1, 1, '制造业', 'active',
    '2024-01-20', '2024-12-31', 15000.00, 3600.00,
    '2024-12-15', '2024-12-31', 200, 180,
    NOW(), '企业主站，运行正常'
),
(
    '电商平台', 'www.microsoft.com', 'https://www.microsoft.com', 2, 2, '电子商务', 'inactive',
    NULL, '2024-12-31', 28000.00, 4800.00,
    '2024-08-31', '2024-12-31', 503, NULL,
    NOW(), '电商平台开发中'
);
