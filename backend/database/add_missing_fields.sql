-- 添加缺少的字段以支持新的监控逻辑

-- 检查并添加缺少的字段
-- 添加状态检查控制字段（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND column_name = 'status_check') > 0,
    'SELECT "status_check already exists" as message',
    'ALTER TABLE websites ADD COLUMN status_check TINYINT(1) DEFAULT 1 COMMENT "是否启用状态检查（1=启用，0=禁用）"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND column_name = 'ssl_check') > 0,
    'SELECT "ssl_check already exists" as message',
    'ALTER TABLE websites ADD COLUMN ssl_check TINYINT(1) DEFAULT 1 COMMENT "是否启用SSL检查（1=启用，0=禁用）"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND column_name = 'tries') > 0,
    'SELECT "tries already exists" as message',
    'ALTER TABLE websites ADD COLUMN tries INT DEFAULT 0 COMMENT "尝试次数（用于排序）"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND column_name = 'last_error_check') > 0,
    'SELECT "last_error_check already exists" as message',
    'ALTER TABLE websites ADD COLUMN last_error_check TIMESTAMP NULL COMMENT "最近错误检查时间"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'websites'
     AND table_schema = 'sitemanager'
     AND column_name = 'next_notice') > 0,
    'SELECT "next_notice already exists" as message',
    'ALTER TABLE websites ADD COLUMN next_notice TIMESTAMP NULL COMMENT "下次通知时间"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引
ALTER TABLE websites 
ADD INDEX idx_status_check (status_check),
ADD INDEX idx_ssl_check (ssl_check),
ADD INDEX idx_tries (tries),
ADD INDEX idx_last_error_check (last_error_check),
ADD INDEX idx_next_notice (next_notice);

-- 更新现有数据，启用所有活跃网站的检查
UPDATE websites 
SET status_check = 1, ssl_check = 1, tries = 0, consecutive_failures = 0
WHERE status = 'active';

-- 显示更新结果
SELECT 
    COUNT(*) as total_websites,
    SUM(CASE WHEN status_check = 1 THEN 1 ELSE 0 END) as status_check_enabled,
    SUM(CASE WHEN ssl_check = 1 THEN 1 ELSE 0 END) as ssl_check_enabled
FROM websites 
WHERE status = 'active';
