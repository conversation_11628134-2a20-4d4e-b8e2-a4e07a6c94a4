-- 网站状态检测记录表
CREATE TABLE IF NOT EXISTS website_status_checks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL,
    check_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    status_code INT DEFAULT 0,
    response_time INT DEFAULT 0,
    is_accessible BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    check_type ENUM('scheduled', 'manual', 'batch') DEFAULT 'scheduled',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_website_id (website_id),
    INDEX idx_check_time (check_time),
    INDEX idx_website_check_time (website_id, check_time),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站状态检测记录表';

-- 网站状态统计表
CREATE TABLE IF NOT EXISTS website_status_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL UNIQUE,
    consecutive_failures INT DEFAULT 0 COMMENT '连续失败次数',
    total_checks INT DEFAULT 0 COMMENT '总检测次数',
    success_checks INT DEFAULT 0 COMMENT '成功检测次数',
    last_success_time DATETIME NULL COMMENT '最后成功时间',
    last_failure_time DATETIME NULL COMMENT '最后失败时间',
    first_failure_time DATETIME NULL COMMENT '首次失败时间',
    current_status_code INT DEFAULT 0 COMMENT '当前状态码',
    current_response_time INT DEFAULT 0 COMMENT '当前响应时间',
    notification_sent BOOLEAN DEFAULT FALSE COMMENT '是否已发送通知',
    notification_count INT DEFAULT 0 COMMENT '通知发送次数',
    last_notification_time DATETIME NULL COMMENT '最后通知时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_website_id (website_id),
    INDEX idx_consecutive_failures (consecutive_failures),
    INDEX idx_last_failure_time (last_failure_time),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站状态统计表';

-- 添加网站表的新字段（如果不存在）
-- 注意：MySQL不支持IF NOT EXISTS语法，需要分别执行
-- ALTER TABLE websites ADD COLUMN server_id INT NULL COMMENT '所在服务器ID';
-- ALTER TABLE websites ADD COLUMN server_name VARCHAR(255) NULL COMMENT '所在服务器名称';
-- ALTER TABLE websites ADD INDEX idx_server_id (server_id);

-- 更新现有网站的服务器信息（可选，根据实际情况调整）
-- UPDATE websites w 
-- LEFT JOIN servers s ON w.server_ip = s.ip_address 
-- SET w.server_id = s.id, w.server_name = s.server_name 
-- WHERE w.server_ip IS NOT NULL AND s.id IS NOT NULL;

-- 创建视图：网站状态详情
-- 注意：暂时注释掉server_name字段，因为可能不存在
CREATE OR REPLACE VIEW website_status_details AS
SELECT
    w.id,
    w.site_name,
    w.domain,
    w.site_url,
    w.server_id,
    -- w.server_name,
    s.server_name as actual_server_name,
    s.ip_address as server_ip,
    COALESCE(ws.consecutive_failures, 0) as consecutive_failures,
    COALESCE(ws.total_checks, 0) as total_checks,
    COALESCE(ws.success_checks, 0) as success_checks,
    ws.last_success_time,
    ws.last_failure_time,
    ws.first_failure_time,
    COALESCE(ws.current_status_code, 0) as current_status_code,
    COALESCE(ws.current_response_time, 0) as current_response_time,
    COALESCE(ws.notification_sent, FALSE) as notification_sent,
    COALESCE(ws.notification_count, 0) as notification_count,
    ws.last_notification_time,
    CASE
        WHEN COALESCE(ws.consecutive_failures, 0) = 0 THEN '正常'
        WHEN COALESCE(ws.consecutive_failures, 0) >= 5 THEN CONCAT('异常 (', ws.consecutive_failures, '次)')
        ELSE CONCAT('警告 (', ws.consecutive_failures, '次)')
    END as status_display,
    CASE
        WHEN COALESCE(ws.consecutive_failures, 0) = 0 THEN 'success'
        WHEN COALESCE(ws.consecutive_failures, 0) >= 5 THEN 'error'
        ELSE 'warning'
    END as status_type,
    CASE
        WHEN ws.last_failure_time IS NOT NULL AND ws.first_failure_time IS NOT NULL
        THEN TIMESTAMPDIFF(MINUTE, ws.first_failure_time, COALESCE(ws.last_success_time, NOW()))
        ELSE 0
    END as error_duration_minutes,
    CASE
        WHEN COALESCE(ws.total_checks, 0) > 0
        THEN ROUND((COALESCE(ws.success_checks, 0) / ws.total_checks) * 100, 2)
        ELSE 0
    END as success_rate
FROM websites w
LEFT JOIN website_status_stats ws ON w.id = ws.website_id
LEFT JOIN servers s ON w.server_id = s.id
WHERE w.status = 'active';

-- 初始化现有网站的状态统计
INSERT IGNORE INTO website_status_stats (website_id, consecutive_failures, total_checks, success_checks)
SELECT 
    id,
    COALESCE(consecutive_failures, 0),
    0,
    0
FROM websites 
WHERE status = 'active';

-- 创建存储过程：更新网站状态统计
DELIMITER //
CREATE OR REPLACE PROCEDURE UpdateWebsiteStatusStats(
    IN p_website_id INT,
    IN p_status_code INT,
    IN p_response_time INT,
    IN p_is_accessible BOOLEAN,
    IN p_error_message TEXT
)
BEGIN
    DECLARE v_consecutive_failures INT DEFAULT 0;
    DECLARE v_notification_threshold INT DEFAULT 5;
    
    -- 插入检测记录
    INSERT INTO website_status_checks (
        website_id, status_code, response_time, is_accessible, error_message, check_type
    ) VALUES (
        p_website_id, p_status_code, p_response_time, p_is_accessible, p_error_message, 'scheduled'
    );
    
    -- 更新或插入状态统计
    INSERT INTO website_status_stats (
        website_id, consecutive_failures, total_checks, success_checks,
        last_success_time, last_failure_time, first_failure_time,
        current_status_code, current_response_time
    ) VALUES (
        p_website_id, 
        CASE WHEN p_is_accessible THEN 0 ELSE 1 END,
        1,
        CASE WHEN p_is_accessible THEN 1 ELSE 0 END,
        CASE WHEN p_is_accessible THEN NOW() ELSE NULL END,
        CASE WHEN p_is_accessible THEN NULL ELSE NOW() END,
        CASE WHEN p_is_accessible THEN NULL ELSE NOW() END,
        p_status_code,
        p_response_time
    ) ON DUPLICATE KEY UPDATE
        total_checks = total_checks + 1,
        success_checks = success_checks + CASE WHEN p_is_accessible THEN 1 ELSE 0 END,
        consecutive_failures = CASE 
            WHEN p_is_accessible THEN 0 
            ELSE consecutive_failures + 1 
        END,
        last_success_time = CASE 
            WHEN p_is_accessible THEN NOW() 
            ELSE last_success_time 
        END,
        last_failure_time = CASE 
            WHEN p_is_accessible THEN last_failure_time 
            ELSE NOW() 
        END,
        first_failure_time = CASE 
            WHEN p_is_accessible THEN NULL
            WHEN consecutive_failures = 0 THEN NOW()
            ELSE first_failure_time
        END,
        current_status_code = p_status_code,
        current_response_time = p_response_time,
        notification_sent = CASE 
            WHEN p_is_accessible THEN FALSE 
            ELSE notification_sent 
        END;
    
    -- 同步到websites表
    UPDATE websites SET
        access_status_code = p_status_code,
        response_time = p_response_time,
        last_check_time = NOW(),
        consecutive_failures = (
            SELECT consecutive_failures 
            FROM website_status_stats 
            WHERE website_id = p_website_id
        ),
        last_failure_time = CASE 
            WHEN p_is_accessible THEN NULL
            ELSE NOW()
        END
    WHERE id = p_website_id;
    
END //
DELIMITER ;

-- 创建清理旧记录的存储过程
DELIMITER //
CREATE OR REPLACE PROCEDURE CleanOldStatusChecks()
BEGIN
    -- 删除30天前的检测记录，但保留每天的最后一条记录
    DELETE wsc1 FROM website_status_checks wsc1
    WHERE wsc1.check_time < DATE_SUB(NOW(), INTERVAL 30 DAY)
    AND EXISTS (
        SELECT 1 FROM website_status_checks wsc2 
        WHERE wsc2.website_id = wsc1.website_id 
        AND DATE(wsc2.check_time) = DATE(wsc1.check_time)
        AND wsc2.check_time > wsc1.check_time
    );
    
    SELECT ROW_COUNT() as deleted_records;
END //
DELIMITER ;

-- 创建获取需要通知的网站列表的存储过程
DELIMITER //
CREATE OR REPLACE PROCEDURE GetWebsitesNeedingNotification()
BEGIN
    SELECT 
        w.id,
        w.site_name,
        w.site_url,
        w.server_name,
        ws.consecutive_failures,
        ws.first_failure_time,
        ws.last_failure_time,
        ws.current_status_code,
        TIMESTAMPDIFF(MINUTE, ws.first_failure_time, NOW()) as error_duration_minutes,
        CONCAT(
            TIMESTAMPDIFF(HOUR, ws.first_failure_time, NOW()), '小时',
            MOD(TIMESTAMPDIFF(MINUTE, ws.first_failure_time, NOW()), 60), '分钟'
        ) as error_duration_display
    FROM websites w
    INNER JOIN website_status_stats ws ON w.id = ws.website_id
    WHERE w.status = 'active'
    AND ws.consecutive_failures >= 5
    AND ws.notification_sent = FALSE;
END //
DELIMITER ;
