#!/usr/bin/env node

/**
 * 测试网站状态修复脚本
 * 用于验证状态码200但被标记为error的网站问题修复
 */

const mysql = require('mysql2/promise');

async function testStatusFix() {
  let connection;
  
  try {
    // 连接数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '123456',
      database: 'sitemanager'
    });

    console.log('🔍 检查状态码200但被标记为error的网站...');

    // 查询状态码200但access_status为error的网站
    const [problemSites] = await connection.execute(`
      SELECT 
        w.id, w.site_name, w.access_status, w.access_status_code, w.consecutive_failures,
        ws.consecutive_failures as stats_consecutive_failures,
        ws.notification_sent, ws.last_notification_time
      FROM websites w
      LEFT JOIN website_status_stats ws ON w.id = ws.website_id
      WHERE w.access_status_code = 200 
      AND w.access_status = 'error'
      LIMIT 10
    `);

    if (problemSites.length > 0) {
      console.log(`\n❌ 发现 ${problemSites.length} 个问题网站（状态码200但标记为error）:`);
      problemSites.forEach(site => {
        console.log(`  - ID: ${site.id}, 名称: ${site.site_name}`);
        console.log(`    websites表连续失败: ${site.consecutive_failures}, stats表连续失败: ${site.stats_consecutive_failures}`);
      });

      console.log('\n🔧 修复这些网站的状态...');
      
      // 修复状态码200的网站状态
      for (const site of problemSites) {
        await connection.execute(`
          UPDATE websites 
          SET access_status = 'online', consecutive_failures = 0
          WHERE id = ? AND access_status_code = 200
        `, [site.id]);

        await connection.execute(`
          UPDATE website_status_stats 
          SET consecutive_failures = 0, notification_sent = FALSE, first_failure_time = NULL
          WHERE website_id = ? 
        `, [site.id]);

        console.log(`  ✅ 修复网站 ${site.id}: ${site.site_name}`);
      }
    } else {
      console.log('✅ 没有发现状态码200但被标记为error的网站');
    }

    console.log('\n🔍 测试飞书通知查询逻辑...');
    
    // 测试新的飞书通知查询逻辑
    const [abnormalSites] = await connection.execute(`
      SELECT
        w.id,
        w.site_name,
        w.access_status,
        w.access_status_code,
        ws.consecutive_failures
      FROM websites w
      LEFT JOIN website_status_stats ws ON w.id = ws.website_id
      WHERE w.status = 'active'
      AND ws.consecutive_failures >= 5
      AND (
        -- 真正的异常：状态码不是2xx或3xx
        (w.access_status_code < 200 OR w.access_status_code >= 400) OR
        -- 或者状态为offline（通常状态码为0）
        w.access_status = 'offline'
      )
      LIMIT 10
    `);

    if (abnormalSites.length > 0) {
      console.log(`\n🚨 发现 ${abnormalSites.length} 个真正异常的网站:`);
      abnormalSites.forEach(site => {
        console.log(`  - ID: ${site.id}, 名称: ${site.site_name}, 状态码: ${site.access_status_code}, 连续失败: ${site.consecutive_failures}`);
      });
    } else {
      console.log('✅ 没有发现真正异常的网站（符合新的通知条件）');
    }

    console.log('\n🎯 修复完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
testStatusFix().catch(console.error);
