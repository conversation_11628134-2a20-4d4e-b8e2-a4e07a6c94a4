/**
 * 测试定时任务的检查逻辑
 */

const mysql = require('mysql2/promise');
const WebsiteStatusService = require('./services/website-status-service');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'sitemanager123',
  database: process.env.DB_NAME || 'sitemanager',
  charset: 'utf8mb4'
};

async function testScheduledCheck() {
  console.log('🧪 测试定时任务的检查逻辑...\n');

  try {
    // 1. 连接数据库
    const db = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 2. 初始化WebsiteStatusService
    const websiteStatusService = new WebsiteStatusService(db);
    console.log('✅ WebsiteStatusService初始化成功');

    // 3. 获取测试网站
    const [websites] = await db.execute(
      'SELECT id, site_name, domain, site_url FROM websites WHERE domain = ? AND status = "active"',
      ['la.jsj-silica.com']
    );

    if (websites.length === 0) {
      console.log('❌ 没有找到测试网站 la.jsj-silica.com');
      return;
    }

    const testWebsite = websites[0];
    console.log(`🎯 找到测试网站: ${testWebsite.site_name} (${testWebsite.domain})`);

    // 4. 测试单个网站检查
    console.log('\n📡 测试单个网站检查...');
    const checkResults = await websiteStatusService.performBatchCheck([testWebsite]);
    
    if (checkResults.length > 0) {
      const result = checkResults[0];
      console.log('✅ 检查结果:');
      console.log(`   网站ID: ${result.websiteId}`);
      console.log(`   网站名: ${result.siteName}`);
      console.log(`   URL: ${result.url}`);
      console.log(`   状态码: ${result.statusCode}`);
      console.log(`   是否可访问: ${result.isAccessible}`);
      console.log(`   响应时间: ${result.responseTime}ms`);
      console.log(`   状态: ${result.status}`);
      console.log(`   消息: ${result.message}`);
      
      // 5. 测试状态同步到数据库
      console.log('\n💾 测试状态同步到数据库...');
      await websiteStatusService.syncStatusToWebsitesTable(checkResults);
      
      // 6. 验证数据库更新
      console.log('\n🔍 验证数据库更新...');
      const [updatedWebsites] = await db.execute(
        'SELECT access_status_code, access_status, response_time, last_check_time FROM websites WHERE id = ?',
        [testWebsite.id]
      );
      
      if (updatedWebsites.length > 0) {
        const updated = updatedWebsites[0];
        console.log('✅ 数据库更新结果:');
        console.log(`   access_status_code: ${updated.access_status_code}`);
        console.log(`   access_status: ${updated.access_status}`);
        console.log(`   response_time: ${updated.response_time}`);
        console.log(`   last_check_time: ${updated.last_check_time}`);
        
        // 检查是否使用了coolmonitor逻辑
        if (result.isAccessible && updated.access_status === 'online') {
          console.log('🎉 SUCCESS: 定时任务正在使用coolmonitor增强检查器！');
          console.log(`   检查器检测结果: isAccessible=${result.isAccessible}, statusCode=${result.statusCode}`);
          console.log(`   数据库更新结果: access_status=${updated.access_status}, access_status_code=${updated.access_status_code}`);
        } else if (!result.isAccessible && updated.access_status === 'error') {
          console.log('⚠️  WARNING: 检查器认为网站不可访问');
          console.log(`   检查器检测结果: isAccessible=${result.isAccessible}, statusCode=${result.statusCode}`);
          console.log(`   数据库更新结果: access_status=${updated.access_status}, access_status_code=${updated.access_status_code}`);
        } else {
          console.log('❌ ERROR: 检查器结果与数据库状态不一致');
          console.log(`   检查器检测结果: isAccessible=${result.isAccessible}, statusCode=${result.statusCode}`);
          console.log(`   数据库更新结果: access_status=${updated.access_status}, access_status_code=${updated.access_status_code}`);
        }
      }
    } else {
      console.log('❌ 没有检查结果');
    }

    await db.end();
    console.log('\n🎯 测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testScheduledCheck().catch(console.error);
}

module.exports = { testScheduledCheck };
