/**
 * 直接测试增强版HTTP检查器的核心功能
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// 监控状态常量
const MONITOR_STATUS = {
  DOWN: 0,
  UP: 1,
  PENDING: 2
};

/**
 * 检查HTTP状态码是否符合预期
 */
function checkStatusCode(statusCode, expectedStatusCodes = '200-299') {
  const statusParts = expectedStatusCodes.split(',');
  
  for (const part of statusParts) {
    const trimmedPart = part.trim();
    
    if (trimmedPart.includes('-')) {
      const [min, max] = trimmedPart.split('-').map(s => parseInt(s));
      if (statusCode >= min && statusCode <= max) {
        return true;
      }
    } else if (parseInt(trimmedPart) === statusCode) {
      return true;
    }
  }
  
  return false;
}

/**
 * 执行HTTP请求
 */
async function performHttpRequest(url, timeout = 10) {
  return new Promise((resolve, reject) => {
    try {
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: 'GET',
        timeout: timeout * 1000,
        headers: {
          'User-Agent': 'SiteManager-DirectTest/1.0',
          'Accept': '*/*',
          'Connection': 'close'
        }
      };

      if (urlObj.protocol === 'https:') {
        options.rejectUnauthorized = false;
      }

      console.log(`📡 发起请求到: ${url}`);
      
      const req = client.request(options, (res) => {
        console.log(`📥 收到响应，状态码: ${res.statusCode}`);

        // 消费响应数据
        res.on('data', (chunk) => {
          // 忽略数据，只是为了触发end事件
        });

        res.on('end', () => {
          console.log(`✅ 响应完成`);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers
          });
        });
      });

      req.on('error', (error) => {
        console.log(`❌ 请求错误: ${error.message}`);
        reject(new Error(`网络错误: ${error.message}`));
      });

      req.on('timeout', () => {
        console.log(`⏰ 请求超时`);
        req.destroy();
        reject(new Error('连接超时'));
      });

      req.end();
    } catch (error) {
      console.log(`❌ 请求异常: ${error.message}`);
      reject(new Error(`请求失败: ${error.message}`));
    }
  });
}

/**
 * HTTP检查
 */
async function checkHttp(url, statusCodes = '200-299') {
  const startTime = Date.now();
  
  try {
    console.log(`🚀 开始检查: ${url}`);
    
    const result = await performHttpRequest(url, 10);
    const responseTime = Date.now() - startTime;
    
    const isStatusValid = checkStatusCode(result.statusCode, statusCodes);
    
    if (isStatusValid) {
      return {
        status: MONITOR_STATUS.UP,
        message: `状态码: ${result.statusCode}`,
        ping: responseTime
      };
    } else {
      return {
        status: MONITOR_STATUS.DOWN,
        message: `状态码不符合预期: ${result.statusCode}`,
        ping: responseTime
      };
    }
  } catch (error) {
    return {
      status: MONITOR_STATUS.DOWN,
      message: error.message,
      ping: Date.now() - startTime
    };
  }
}

async function testDirect() {
  console.log('🔍 开始直接测试...');

  const testUrls = [
    'https://www.baidu.com',
    'https://www.qq.com',
    'https://github.com'
  ];

  for (const url of testUrls) {
    console.log(`\n📋 测试: ${url}`);
    
    try {
      const result = await checkHttp(url);
      
      console.log(`✅ 检查结果:`);
      console.log(`   状态: ${result.status === MONITOR_STATUS.UP ? '✅ 正常' : '❌ 异常'}`);
      console.log(`   消息: ${result.message}`);
      console.log(`   响应时间: ${result.ping}ms`);
      
    } catch (error) {
      console.log(`❌ 检查失败: ${error.message}`);
    }
    
    console.log('─'.repeat(50));
  }

  console.log('\n🎉 直接测试完成!');
}

testDirect().catch(console.error);
