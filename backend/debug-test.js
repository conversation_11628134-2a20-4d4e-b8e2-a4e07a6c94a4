/**
 * 调试测试HTTP检查器
 */

const https = require('https');

async function debugTest() {
  console.log('🔍 开始调试测试...');

  // 测试原生HTTPS请求
  console.log('📡 测试原生HTTPS请求...');
  
  const options = {
    hostname: 'www.baidu.com',
    port: 443,
    path: '/',
    method: 'GET',
    timeout: 10000,
    headers: {
      'User-Agent': 'SiteManager-Test/1.0'
    }
  };

  const startTime = Date.now();

  const req = https.request(options, (res) => {
    console.log(`✅ 状态码: ${res.statusCode}`);
    console.log(`⏱️ 响应时间: ${Date.now() - startTime}ms`);
    
    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log(`📄 响应长度: ${data.length} 字符`);
      console.log('🎉 原生请求测试完成!');
      
      // 现在测试我们的检查器
      testOurChecker();
    });
  });

  req.on('error', (error) => {
    console.error('❌ 原生请求失败:', error.message);
    testOurChecker();
  });

  req.on('timeout', () => {
    console.log('⏰ 原生请求超时');
    req.destroy();
    testOurChecker();
  });

  req.end();
}

async function testOurChecker() {
  console.log('\n🚀 测试我们的检查器...');
  
  try {
    const { EnhancedHttpChecker, MONITOR_STATUS } = require('./services/enhanced-http-checker');
    const checker = new EnhancedHttpChecker();

    console.log('📡 执行HTTP检查...');
    const result = await checker.performHttpRequest({
      url: 'https://www.baidu.com',
      httpMethod: 'GET',
      connectTimeout: 10
    });

    console.log('✅ 检查器结果:');
    console.log(`   状态码: ${result.statusCode}`);
    console.log(`   响应体长度: ${result.responseBody ? result.responseBody.length : 'null'}`);

  } catch (error) {
    console.error('❌ 检查器失败:', error.message);
    console.error('   错误堆栈:', error.stack);
  }

  console.log('🎉 调试测试完成!');
}

debugTest();
