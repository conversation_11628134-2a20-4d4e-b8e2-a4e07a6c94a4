/**
 * 权限验证中间件
 * 功能：
 * 1. 实现 requirePermission() 中间件函数
 * 2. 实现 requireRole() 中间件函数
 * 3. 实现 requireOwnership() 资源所有权验证
 * 4. 添加权限验证失败的标准化错误响应
 */

const PermissionService = require('../services/PermissionService');
const AuditService = require('../services/AuditService');
const PermissionMonitoringService = require('../services/PermissionMonitoringService');

class PermissionMiddleware {
  constructor(database) {
    this.db = database;
    this.permissionService = new PermissionService(database);
    this.auditService = new AuditService(database);
  }

  /**
   * 权限检查中间件
   * @param {string|string[]} requiredPermissions - 必需的权限
   * @param {Object} options - 选项配置
   * @returns {Function} Express中间件函数
   */
  requirePermission(requiredPermissions, options = {}) {
    const {
      requireAll = false,        // 是否需要所有权限
      skipAudit = false,         // 是否跳过审计日志
      customErrorMessage = null, // 自定义错误消息
      allowSuperAdmin = true     // 是否允许超级管理员绕过检查
    } = options;

    return async (req, res, next) => {
      try {
        // 检查用户是否已认证
        if (!req.user || !req.user.id) {
          return this.sendUnauthorizedResponse(res, '用户未认证');
        }

        const userId = req.user.id;
        const permissions = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];

        // 记录权限检查开始时间（用于性能监控）
        const startTime = Date.now();

        // 执行权限检查
        const hasPermission = await this.permissionService.hasPermission(userId, permissions, requireAll);

        // 记录性能指标
        const checkDuration = Date.now() - startTime;
        
        // 记录监控指标
        PermissionMonitoringService.recordPermissionCheck(checkDuration, hasPermission, permissions.join(','), userId);

        if (hasPermission) {
          // 权限验证通过
          if (!skipAudit) {
            await this.auditService.logPermissionAccess(
              userId,
              'permission_check',
              permissions.join(','),
              'granted',
              {
                requiredPermissions: permissions,
                requireAll,
                checkDuration,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                endpoint: `${req.method} ${req.path}`
              }
            );
          }

          // 添加权限信息到请求对象
          req.userPermissions = await this.permissionService.getUserPermissions(userId);
          
          return next();
        } else {
          // 权限验证失败
          if (!skipAudit) {
            await this.auditService.logPermissionAccess(
              userId,
              'permission_check',
              permissions.join(','),
              'denied',
              {
                requiredPermissions: permissions,
                requireAll,
                checkDuration,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                endpoint: `${req.method} ${req.path}`,
                userRole: req.user.role
              }
            );
          }

          const errorMessage = customErrorMessage || this.generatePermissionErrorMessage(permissions, requireAll);
          return this.sendForbiddenResponse(res, errorMessage, {
            required: permissions,
            missing: await this.getMissingPermissions(userId, permissions),
            userPermissions: (await this.permissionService.getUserPermissions(userId)).effectivePermissions
          });
        }

      } catch (error) {
        console.error('权限检查中间件错误:', error);
        
        // 记录错误审计日志
        if (!skipAudit && req.user?.id) {
          await this.auditService.logPermissionAccess(
            req.user.id,
            'permission_check',
            Array.isArray(requiredPermissions) ? requiredPermissions.join(',') : requiredPermissions,
            'error',
            {
              error: error.message,
              ip: req.ip,
              userAgent: req.get('User-Agent'),
              endpoint: `${req.method} ${req.path}`
            }
          );
        }

        return this.sendInternalErrorResponse(res, '权限验证失败');
      }
    };
  }

  /**
   * 角色检查中间件
   * @param {string|string[]} requiredRoles - 必需的角色
   * @param {Object} options - 选项配置
   * @returns {Function} Express中间件函数
   */
  requireRole(requiredRoles, options = {}) {
    const {
      skipAudit = false,
      customErrorMessage = null
    } = options;

    return async (req, res, next) => {
      try {
        // 检查用户是否已认证
        if (!req.user || !req.user.id) {
          return this.sendUnauthorizedResponse(res, '用户未认证');
        }

        const userId = req.user.id;
        const roles = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];

        // 记录角色检查开始时间
        const startTime = Date.now();

        // 执行角色检查
        const hasRole = await this.permissionService.hasRole(userId, roles);

        // 记录性能指标
        const checkDuration = Date.now() - startTime;

        if (hasRole) {
          // 角色验证通过
          if (!skipAudit) {
            await this.auditService.logPermissionAccess(
              userId,
              'role_check',
              roles.join(','),
              'granted',
              {
                requiredRoles: roles,
                userRole: req.user.role,
                checkDuration,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                endpoint: `${req.method} ${req.path}`
              }
            );
          }

          return next();
        } else {
          // 角色验证失败
          if (!skipAudit) {
            await this.auditService.logPermissionAccess(
              userId,
              'role_check',
              roles.join(','),
              'denied',
              {
                requiredRoles: roles,
                userRole: req.user.role,
                checkDuration,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                endpoint: `${req.method} ${req.path}`
              }
            );
          }

          const errorMessage = customErrorMessage || `需要以下角色之一: ${roles.join(', ')}`;
          return this.sendForbiddenResponse(res, errorMessage, {
            required: roles,
            current: req.user.role
          });
        }

      } catch (error) {
        console.error('角色检查中间件错误:', error);
        return this.sendInternalErrorResponse(res, '角色验证失败');
      }
    };
  }

  /**
   * 资源所有权检查中间件
   * @param {string} resourceType - 资源类型
   * @param {Object} options - 选项配置
   * @returns {Function} Express中间件函数
   */
  requireOwnership(resourceType, options = {}) {
    const {
      resourceIdParam = 'id',    // 资源ID参数名
      ownerField = 'user_id',    // 所有者字段名
      allowAdmin = true,         // 是否允许管理员访问
      skipAudit = false,
      customErrorMessage = null
    } = options;

    return async (req, res, next) => {
      try {
        // 检查用户是否已认证
        if (!req.user || !req.user.id) {
          return this.sendUnauthorizedResponse(res, '用户未认证');
        }

        const userId = req.user.id;
        const resourceId = req.params[resourceIdParam];

        if (!resourceId) {
          return this.sendBadRequestResponse(res, `缺少资源ID参数: ${resourceIdParam}`);
        }

        // 管理员权限检查
        if (allowAdmin && (req.user.role === 'admin' || req.user.role === 'super_admin')) {
          if (!skipAudit) {
            await this.auditService.logPermissionAccess(
              userId,
              'ownership_check',
              `${resourceType}:${resourceId}`,
              'granted',
              {
                reason: 'admin_access',
                resourceType,
                resourceId,
                userRole: req.user.role,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                endpoint: `${req.method} ${req.path}`
              }
            );
          }
          return next();
        }

        // 检查资源所有权
        const isOwner = await this.checkResourceOwnership(resourceType, resourceId, userId, ownerField);

        if (isOwner) {
          // 所有权验证通过
          if (!skipAudit) {
            await this.auditService.logPermissionAccess(
              userId,
              'ownership_check',
              `${resourceType}:${resourceId}`,
              'granted',
              {
                reason: 'owner_access',
                resourceType,
                resourceId,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                endpoint: `${req.method} ${req.path}`
              }
            );
          }

          return next();
        } else {
          // 所有权验证失败
          if (!skipAudit) {
            await this.auditService.logPermissionAccess(
              userId,
              'ownership_check',
              `${resourceType}:${resourceId}`,
              'denied',
              {
                reason: 'not_owner',
                resourceType,
                resourceId,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                endpoint: `${req.method} ${req.path}`
              }
            );
          }

          const errorMessage = customErrorMessage || '您没有权限访问此资源';
          return this.sendForbiddenResponse(res, errorMessage, {
            resourceType,
            resourceId,
            reason: 'ownership_required'
          });
        }

      } catch (error) {
        console.error('资源所有权检查中间件错误:', error);
        return this.sendInternalErrorResponse(res, '资源所有权验证失败');
      }
    };
  }

  /**
   * 组合权限检查中间件
   * @param {Object} requirements - 权限要求
   * @returns {Function} Express中间件函数
   */
  requireCombined(requirements) {
    const {
      permissions = [],
      roles = [],
      ownership = null,
      logic = 'AND'  // 'AND' 或 'OR'
    } = requirements;

    return async (req, res, next) => {
      try {
        if (!req.user || !req.user.id) {
          return this.sendUnauthorizedResponse(res, '用户未认证');
        }

        const userId = req.user.id;
        const checks = [];

        // 权限检查
        if (permissions.length > 0) {
          const hasPermissions = await this.permissionService.hasPermission(userId, permissions, logic === 'AND');
          checks.push(hasPermissions);
        }

        // 角色检查
        if (roles.length > 0) {
          const hasRole = await this.permissionService.hasRole(userId, roles);
          checks.push(hasRole);
        }

        // 所有权检查
        if (ownership) {
          const { resourceType, resourceIdParam = 'id', ownerField = 'user_id' } = ownership;
          const resourceId = req.params[resourceIdParam];
          
          if (resourceId) {
            const isOwner = await this.checkResourceOwnership(resourceType, resourceId, userId, ownerField);
            checks.push(isOwner);
          }
        }

        // 根据逻辑判断结果
        const passed = logic === 'AND' ? checks.every(check => check) : checks.some(check => check);

        if (passed) {
          return next();
        } else {
          return this.sendForbiddenResponse(res, '权限不足', {
            required: requirements,
            logic
          });
        }

      } catch (error) {
        console.error('组合权限检查中间件错误:', error);
        return this.sendInternalErrorResponse(res, '权限验证失败');
      }
    };
  }

  /**
   * 检查资源所有权
   * @param {string} resourceType - 资源类型
   * @param {string} resourceId - 资源ID
   * @param {number} userId - 用户ID
   * @param {string} ownerField - 所有者字段名
   * @returns {Promise<boolean>} 是否拥有资源
   */
  async checkResourceOwnership(resourceType, resourceId, userId, ownerField) {
    try {
      // 根据资源类型构建表名
      const tableName = this.getTableNameByResourceType(resourceType);
      
      const [result] = await this.db.execute(
        `SELECT ${ownerField} FROM ${tableName} WHERE id = ?`,
        [resourceId]
      );

      if (result.length === 0) {
        return false; // 资源不存在
      }

      return result[0][ownerField] === userId;

    } catch (error) {
      console.error(`检查资源所有权失败 (${resourceType}:${resourceId}):`, error.message);
      return false;
    }
  }

  /**
   * 根据资源类型获取表名
   * @param {string} resourceType - 资源类型
   * @returns {string} 表名
   */
  getTableNameByResourceType(resourceType) {
    const tableMapping = {
      'website': 'websites',
      'server': 'servers',
      'project': 'projects',
      'customer': 'customers',
      'user': 'users'
    };

    return tableMapping[resourceType] || resourceType;
  }

  /**
   * 获取用户缺失的权限
   * @param {number} userId - 用户ID
   * @param {string[]} requiredPermissions - 必需权限
   * @returns {Promise<string[]>} 缺失的权限
   */
  async getMissingPermissions(userId, requiredPermissions) {
    try {
      const userPermissions = await this.permissionService.getUserPermissions(userId);
      const effectivePermissions = userPermissions.effectivePermissions || [];
      
      return requiredPermissions.filter(permission => !effectivePermissions.includes(permission));
    } catch (error) {
      console.error('获取缺失权限失败:', error.message);
      return requiredPermissions;
    }
  }

  /**
   * 生成权限错误消息
   * @param {string[]} permissions - 权限列表
   * @param {boolean} requireAll - 是否需要所有权限
   * @returns {string} 错误消息
   */
  generatePermissionErrorMessage(permissions, requireAll) {
    if (permissions.length === 1) {
      return `需要权限: ${permissions[0]}`;
    }

    if (requireAll) {
      return `需要以下所有权限: ${permissions.join(', ')}`;
    } else {
      return `需要以下权限之一: ${permissions.join(', ')}`;
    }
  }

  /**
   * 发送未认证响应
   */
  sendUnauthorizedResponse(res, message) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'UNAUTHORIZED',
        message: message || '用户未认证',
        details: {
          requiresAuthentication: true
        }
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 发送权限不足响应
   */
  sendForbiddenResponse(res, message, details = {}) {
    return res.status(403).json({
      success: false,
      error: {
        code: 'INSUFFICIENT_PERMISSIONS',
        message: message || '权限不足，无法执行此操作',
        details
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 发送请求错误响应
   */
  sendBadRequestResponse(res, message) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'BAD_REQUEST',
        message: message || '请求参数错误'
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 发送内部错误响应
   */
  sendInternalErrorResponse(res, message) {
    return res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: message || '服务器内部错误'
      },
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 创建权限检查装饰器
   * @param {string|string[]} permissions - 权限
   * @param {Object} options - 选项
   * @returns {Function} 装饰器函数
   */
  static createPermissionDecorator(permissions, options = {}) {
    return function(target, propertyKey, descriptor) {
      const originalMethod = descriptor.value;
      
      descriptor.value = async function(req, res, next) {
        const middleware = new PermissionMiddleware(this.db);
        const permissionCheck = middleware.requirePermission(permissions, options);
        
        return permissionCheck(req, res, () => {
          return originalMethod.call(this, req, res, next);
        });
      };
      
      return descriptor;
    };
  }
}

module.exports = PermissionMiddleware;