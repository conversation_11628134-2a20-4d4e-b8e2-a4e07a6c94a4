/**
 * 权限中间件集成工具
 * 功能：
 * 1. 加载权限配置文件
 * 2. 自动为API路由添加权限验证
 * 3. 实现权限配置热重载功能
 * 4. 添加权限验证性能监控
 */

const fs = require('fs').promises;
const path = require('path');
const PermissionMiddleware = require('./PermissionMiddleware');

class PermissionIntegration {
  constructor(app, database) {
    this.app = app;
    this.db = database;
    this.permissionMiddleware = new PermissionMiddleware(database);
    this.config = null;
    this.configPath = path.join(__dirname, '../config/permissions.json');
    this.lastConfigLoad = null;
    
    // 性能监控指标
    this.metrics = {
      totalChecks: 0,
      successfulChecks: 0,
      failedChecks: 0,
      averageResponseTime: 0,
      lastResetTime: Date.now()
    };
  }

  /**
   * 初始化权限集成
   */
  async initialize() {
    try {
      // 加载权限配置
      await this.loadPermissionConfig();
      
      // 设置配置文件监听（热重载）
      this.setupConfigWatcher();
      
      // 注册权限验证中间件
      this.registerPermissionMiddleware();
      
      // 启动性能监控
      this.startPerformanceMonitoring();
      
      console.log('✅ 权限中间件集成初始化完成');
      
    } catch (error) {
      console.error('❌ 权限中间件集成初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 加载权限配置文件
   */
  async loadPermissionConfig() {
    try {
      const configData = await fs.readFile(this.configPath, 'utf8');
      this.config = JSON.parse(configData);
      this.lastConfigLoad = Date.now();
      
      console.log('✅ 权限配置文件加载成功');
      console.log(`   - API权限规则: ${Object.keys(this.config.apiPermissions || {}).length} 条`);
      console.log(`   - 角色权限配置: ${Object.keys(this.config.rolePermissions || {}).length} 个角色`);
      console.log(`   - 资源所有权配置: ${Object.keys(this.config.ownershipResources || {}).length} 种资源`);
      
    } catch (error) {
      console.error('❌ 加载权限配置文件失败:', error.message);
      throw error;
    }
  }

  /**
   * 设置配置文件监听器（热重载）
   */
  setupConfigWatcher() {
    const chokidar = require('chokidar');
    
    const watcher = chokidar.watch(this.configPath, {
      persistent: true,
      ignoreInitial: true
    });

    watcher.on('change', async () => {
      try {
        console.log('🔄 检测到权限配置文件变更，重新加载...');
        await this.loadPermissionConfig();
        console.log('✅ 权限配置热重载完成');
      } catch (error) {
        console.error('❌ 权限配置热重载失败:', error.message);
      }
    });

    console.log('👁️  权限配置文件监听器已启动');
  }

  /**
   * 注册权限验证中间件
   */
  registerPermissionMiddleware() {
    // 为所有需要权限验证的路由添加中间件
    this.app.use('/api/v1/*', (req, res, next) => {
      this.handlePermissionCheck(req, res, next);
    });

    console.log('✅ 权限验证中间件已注册');
  }

  /**
   * 处理权限检查
   */
  async handlePermissionCheck(req, res, next) {
    const startTime = Date.now();
    
    try {
      // 跳过认证相关的API
      if (this.shouldSkipPermissionCheck(req)) {
        return next();
      }

      // 检查用户是否已认证
      if (!req.user || !req.user.id) {
        this.recordMetrics(false, Date.now() - startTime);
        return this.permissionMiddleware.sendUnauthorizedResponse(res, '用户未认证');
      }

      // 获取API权限要求
      const requiredPermissions = this.getRequiredPermissions(req);
      
      if (!requiredPermissions || requiredPermissions.length === 0) {
        // 没有配置权限要求，允许通过
        return next();
      }

      // 执行权限检查
      const hasPermission = await this.permissionMiddleware.permissionService.hasPermission(
        req.user.id, 
        requiredPermissions
      );

      if (hasPermission) {
        this.recordMetrics(true, Date.now() - startTime);
        return next();
      } else {
        this.recordMetrics(false, Date.now() - startTime);
        
        // 记录审计日志
        await this.permissionMiddleware.auditService.logPermissionAccess(
          req.user.id,
          'api_access',
          `${req.method} ${req.path}`,
          'denied',
          {
            requiredPermissions,
            ip: req.ip,
            userAgent: req.get('User-Agent')
          }
        );

        return this.permissionMiddleware.sendForbiddenResponse(
          res, 
          `需要以下权限: ${requiredPermissions.join(', ')}`,
          {
            required: requiredPermissions,
            endpoint: `${req.method} ${req.path}`
          }
        );
      }

    } catch (error) {
      this.recordMetrics(false, Date.now() - startTime);
      console.error('权限检查处理失败:', error.message);
      return this.permissionMiddleware.sendInternalErrorResponse(res, '权限验证失败');
    }
  }

  /**
   * 判断是否应该跳过权限检查
   */
  shouldSkipPermissionCheck(req) {
    const skipPaths = [
      '/api/v1/auth/login',
      '/api/v1/auth/logout', 
      '/api/v1/auth/verify',
      '/api/v1/auth/permissions'
    ];

    const path = req.path;
    return skipPaths.some(skipPath => path === skipPath);
  }

  /**
   * 获取API所需权限
   */
  getRequiredPermissions(req) {
    if (!this.config || !this.config.apiPermissions) {
      return [];
    }

    const method = req.method;
    const path = req.path;
    const routeKey = `${method} ${path}`;

    // 精确匹配
    if (this.config.apiPermissions[routeKey]) {
      return this.config.apiPermissions[routeKey];
    }

    // 模式匹配（支持参数路由）
    for (const [pattern, permissions] of Object.entries(this.config.apiPermissions)) {
      if (this.matchRoute(pattern, routeKey)) {
        return permissions;
      }
    }

    return [];
  }

  /**
   * 路由模式匹配
   */
  matchRoute(pattern, route) {
    // 将路由参数 :id 转换为正则表达式
    const regexPattern = pattern
      .replace(/:[^/]+/g, '[^/]+')  // :id -> [^/]+
      .replace(/\*/g, '.*');        // * -> .*

    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(route);
  }

  /**
   * 记录性能指标
   */
  recordMetrics(success, responseTime) {
    this.metrics.totalChecks++;
    
    if (success) {
      this.metrics.successfulChecks++;
    } else {
      this.metrics.failedChecks++;
    }

    // 计算平均响应时间
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime * (this.metrics.totalChecks - 1) + responseTime) / 
      this.metrics.totalChecks;
  }

  /**
   * 启动性能监控
   */
  startPerformanceMonitoring() {
    setInterval(() => {
      this.logPerformanceMetrics();
    }, 60000); // 每分钟记录一次

    console.log('📊 权限验证性能监控已启动');
  }

  /**
   * 记录性能指标日志
   */
  logPerformanceMetrics() {
    const successRate = this.metrics.totalChecks > 0 
      ? (this.metrics.successfulChecks / this.metrics.totalChecks * 100).toFixed(2)
      : 0;

    console.log('📊 权限验证性能指标:');
    console.log(`   总检查次数: ${this.metrics.totalChecks}`);
    console.log(`   成功率: ${successRate}%`);
    console.log(`   平均响应时间: ${this.metrics.averageResponseTime.toFixed(2)}ms`);

    // 如果平均响应时间超过50ms，发出警告
    if (this.metrics.averageResponseTime > 50) {
      console.warn(`⚠️  权限验证响应时间过长: ${this.metrics.averageResponseTime.toFixed(2)}ms`);
    }
  }

  /**
   * 获取性能指标
   */
  getMetrics() {
    const successRate = this.metrics.totalChecks > 0 
      ? (this.metrics.successfulChecks / this.metrics.totalChecks * 100).toFixed(2)
      : 0;

    return {
      ...this.metrics,
      successRate: `${successRate}%`,
      uptime: Date.now() - this.metrics.lastResetTime
    };
  }

  /**
   * 重置性能指标
   */
  resetMetrics() {
    this.metrics = {
      totalChecks: 0,
      successfulChecks: 0,
      failedChecks: 0,
      averageResponseTime: 0,
      lastResetTime: Date.now()
    };

    console.log('📊 权限验证性能指标已重置');
  }

  /**
   * 创建特定权限的中间件
   */
  createPermissionMiddleware(permissions, options = {}) {
    return this.permissionMiddleware.requirePermission(permissions, options);
  }

  /**
   * 创建角色检查中间件
   */
  createRoleMiddleware(roles, options = {}) {
    return this.permissionMiddleware.requireRole(roles, options);
  }

  /**
   * 创建资源所有权检查中间件
   */
  createOwnershipMiddleware(resourceType, options = {}) {
    const resourceConfig = this.config?.ownershipResources?.[resourceType];
    if (resourceConfig) {
      options = { ...resourceConfig, ...options };
    }
    
    return this.permissionMiddleware.requireOwnership(resourceType, options);
  }

  /**
   * 获取当前配置
   */
  getConfig() {
    return this.config;
  }

  /**
   * 更新配置
   */
  async updateConfig(newConfig) {
    try {
      this.config = { ...this.config, ...newConfig };
      
      // 保存到文件
      await fs.writeFile(this.configPath, JSON.stringify(this.config, null, 2));
      
      console.log('✅ 权限配置已更新');
      
    } catch (error) {
      console.error('❌ 更新权限配置失败:', error.message);
      throw error;
    }
  }

  /**
   * 验证配置文件格式
   */
  validateConfig(config) {
    const requiredFields = ['apiPermissions', 'rolePermissions'];
    
    for (const field of requiredFields) {
      if (!config[field]) {
        throw new Error(`权限配置缺少必需字段: ${field}`);
      }
    }

    // 验证API权限配置格式
    for (const [route, permissions] of Object.entries(config.apiPermissions)) {
      if (!Array.isArray(permissions)) {
        throw new Error(`API权限配置格式错误: ${route} 的权限必须是数组`);
      }
    }

    // 验证角色权限配置格式
    for (const [role, permissions] of Object.entries(config.rolePermissions)) {
      if (permissions !== 'all' && !Array.isArray(permissions)) {
        throw new Error(`角色权限配置格式错误: ${role} 的权限必须是数组或 'all'`);
      }
    }

    return true;
  }
}

module.exports = PermissionIntegration;