/**
 * 缓存中间件 - 自动处理API响应缓存
 * 用于减少重复的数据库查询，提升API响应速度
 */

const cacheService = require('../services/CacheService');

/**
 * 创建缓存中间件
 * @param {object} options 缓存选项
 * @param {number} options.ttl 缓存过期时间（秒），默认300秒
 * @param {string} options.keyPrefix 缓存键前缀
 * @param {function} options.keyGenerator 自定义键生成函数
 * @param {function} options.shouldCache 判断是否应该缓存的函数
 * @returns {function} Express中间件函数
 */
function createCacheMiddleware(options = {}) {
    const {
        ttl = 300, // 默认5分钟
        keyPrefix = 'api',
        keyGenerator = null,
        shouldCache = null
    } = options;

    return async (req, res, next) => {
        // 只缓存GET请求
        if (req.method !== 'GET') {
            return next();
        }

        // 检查是否应该缓存
        if (shouldCache && !shouldCache(req)) {
            return next();
        }

        try {
            // 生成缓存键
            let cacheKey;
            if (keyGenerator) {
                cacheKey = keyGenerator(req);
            } else {
                // 默认键生成策略
                const pathKey = req.path.replace(/\//g, ':');
                const queryKey = Object.keys(req.query).length > 0 
                    ? ':' + JSON.stringify(req.query) 
                    : '';
                cacheKey = cacheService.constructor.generateKey(keyPrefix, pathKey + queryKey);
            }

            // 尝试从缓存获取数据
            const cachedData = await cacheService.get(cacheKey);
            if (cachedData) {
                console.log(`🎯 API缓存命中: ${req.path}`);
                return res.json(cachedData);
            }

            // 缓存未命中，继续处理请求
            console.log(`❌ API缓存未命中: ${req.path}`);

            // 重写res.json方法以拦截响应数据
            const originalJson = res.json;
            res.json = function(data) {
                // 只缓存成功的响应
                if (res.statusCode >= 200 && res.statusCode < 300) {
                    // 异步设置缓存，不阻塞响应
                    setImmediate(async () => {
                        try {
                            await cacheService.set(cacheKey, data, ttl);
                            console.log(`✅ API响应已缓存: ${req.path} (TTL: ${ttl}s)`);
                        } catch (error) {
                            console.error(`❌ API响应缓存失败: ${req.path}`, error.message);
                        }
                    });
                }

                // 调用原始的json方法
                return originalJson.call(this, data);
            };

            next();
        } catch (error) {
            console.error('❌ 缓存中间件错误:', error.message);
            // 缓存错误不应该影响正常请求
            next();
        }
    };
}

/**
 * 网站列表缓存中间件
 * 专门用于网站管理相关的API缓存
 */
const websiteCacheMiddleware = createCacheMiddleware({
    ttl: 300, // 5分钟
    keyPrefix: 'websites',
    keyGenerator: (req) => {
        const { page = 1, limit = 50, search = '', status = '', platform = '' } = req.query;
        return cacheService.constructor.generateKey(
            'websites', 
            'list', 
            page, 
            limit, 
            search, 
            status, 
            platform
        );
    },
    shouldCache: (req) => {
        // 只缓存列表查询，不缓存详情查询
        return req.path.includes('/websites') && !req.path.match(/\/websites\/\d+$/);
    }
});

/**
 * 服务器列表缓存中间件
 */
const serverCacheMiddleware = createCacheMiddleware({
    ttl: 600, // 10分钟
    keyPrefix: 'servers',
    keyGenerator: (req) => {
        const { page = 1, limit = 50, search = '', status = '' } = req.query;
        return cacheService.constructor.generateKey(
            'servers', 
            'list', 
            page, 
            limit, 
            search, 
            status
        );
    },
    shouldCache: (req) => {
        return req.path.includes('/servers') && !req.path.match(/\/servers\/\d+$/);
    }
});

/**
 * 统计数据缓存中间件
 */
const statsCacheMiddleware = createCacheMiddleware({
    ttl: 900, // 15分钟
    keyPrefix: 'stats',
    keyGenerator: (req) => {
        return cacheService.constructor.generateKey('stats', req.path.replace(/\//g, ':'));
    },
    shouldCache: (req) => {
        return req.path.includes('/stats') || req.path.includes('/dashboard');
    }
});

/**
 * 用户权限缓存中间件
 */
const permissionCacheMiddleware = createCacheMiddleware({
    ttl: 1800, // 30分钟
    keyPrefix: 'permissions',
    keyGenerator: (req) => {
        const userId = req.user?.id || 'anonymous';
        return cacheService.constructor.generateKey('permissions', userId, req.path.replace(/\//g, ':'));
    },
    shouldCache: (req) => {
        return req.path.includes('/permissions') || req.path.includes('/users');
    }
});

/**
 * 清除相关缓存的辅助函数
 */
const clearCache = {
    /**
     * 清除网站相关缓存
     */
    websites: async () => {
        await cacheService.delPattern('websites:*');
        await cacheService.delPattern('stats:*'); // 统计数据也需要更新
        console.log('🧹 已清除网站相关缓存');
    },

    /**
     * 清除服务器相关缓存
     */
    servers: async () => {
        await cacheService.delPattern('servers:*');
        await cacheService.delPattern('stats:*');
        console.log('🧹 已清除服务器相关缓存');
    },

    /**
     * 清除统计数据缓存
     */
    stats: async () => {
        await cacheService.delPattern('stats:*');
        console.log('🧹 已清除统计数据缓存');
    },

    /**
     * 清除用户权限缓存
     * @param {number} userId 用户ID，不传则清除所有用户权限缓存
     */
    permissions: async (userId = null) => {
        const pattern = userId ? `permissions:${userId}:*` : 'permissions:*';
        await cacheService.delPattern(pattern);
        console.log(`🧹 已清除用户权限缓存${userId ? ` (用户ID: ${userId})` : ''}`);
    },

    /**
     * 清除所有API缓存
     */
    all: async () => {
        await cacheService.delPattern('api:*');
        await cacheService.delPattern('websites:*');
        await cacheService.delPattern('servers:*');
        await cacheService.delPattern('stats:*');
        await cacheService.delPattern('permissions:*');
        console.log('🧹 已清除所有API缓存');
    }
};

module.exports = {
    createCacheMiddleware,
    websiteCacheMiddleware,
    serverCacheMiddleware,
    statsCacheMiddleware,
    permissionCacheMiddleware,
    clearCache
};