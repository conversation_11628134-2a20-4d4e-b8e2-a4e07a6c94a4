/**
 * 权限中间件集成示例
 * 展示如何在 simple-server.js 中集成权限验证
 */

const PermissionMiddleware = require('./PermissionMiddleware');
const AuditService = require('../services/AuditService');

/**
 * 初始化权限中间件
 * @param {Object} app - Express应用实例
 * @param {Object} db - 数据库连接
 */
function initializePermissionMiddleware(app, db) {
  const permissionMiddleware = new PermissionMiddleware(db);
  const auditService = new AuditService(db);

  // 创建便捷的权限检查函数
  const requirePermission = (permissions, options = {}) => {
    return permissionMiddleware.requirePermission(permissions, options);
  };

  const requireRole = (roles, options = {}) => {
    return permissionMiddleware.requireRole(roles, options);
  };

  const requireOwnership = (resourceType, options = {}) => {
    return permissionMiddleware.requireOwnership(resourceType, options);
  };

  // 返回中间件函数，供路由使用
  return {
    requirePermission,
    requireRole,
    requireOwnership,
    auditService,
    permissionMiddleware
  };
}

/**
 * 应用权限中间件到现有路由的示例
 */
function applyPermissionMiddlewareExample(app, db) {
  const { requirePermission, requireRole } = initializePermissionMiddleware(app, db);

  // 用户管理路由权限
  app.get('/api/v1/users', requirePermission(['user.list.view']), (req, res) => {
    // 原有的路由处理逻辑
  });

  app.post('/api/v1/users', requirePermission(['user.user.create']), (req, res) => {
    // 原有的路由处理逻辑
  });

  app.put('/api/v1/users/:id', requirePermission(['user.user.edit']), (req, res) => {
    // 原有的路由处理逻辑
  });

  app.delete('/api/v1/users/:id', requirePermission(['user.user.delete']), (req, res) => {
    // 原有的路由处理逻辑
  });

  // 网站管理路由权限
  app.get('/api/v1/websites', requirePermission(['site.list.view']), (req, res) => {
    // 原有的路由处理逻辑
  });

  app.post('/api/v1/websites', requirePermission(['site.website.create']), (req, res) => {
    // 原有的路由处理逻辑
  });

  // 服务器管理路由权限
  app.get('/api/v1/servers', requirePermission(['server.list.view']), (req, res) => {
    // 原有的路由处理逻辑
  });

  // 系统管理路由权限（需要管理员角色）
  app.get('/api/v1/system/info', requireRole(['admin', 'super_admin']), (req, res) => {
    // 原有的路由处理逻辑
  });

  app.put('/api/v1/system/settings', requirePermission(['system.settings.edit']), (req, res) => {
    // 原有的路由处理逻辑
  });

  // 权限管理路由（需要权限管理权限）
  app.get('/api/v1/permissions', requirePermission(['system.permission.manage']), (req, res) => {
    // 原有的路由处理逻辑
  });

  console.log('✅ 权限中间件已应用到所有路由');
}

/**
 * 创建权限检查装饰器函数
 */
function createPermissionDecorator(db) {
  const permissionMiddleware = new PermissionMiddleware(db);

  return {
    /**
     * 权限检查装饰器
     * @param {string|string[]} permissions - 所需权限
     * @param {Object} options - 选项
     */
    checkPermission: (permissions, options = {}) => {
      return (req, res, next) => {
        const middleware = permissionMiddleware.requirePermission(permissions, options);
        return middleware(req, res, next);
      };
    },

    /**
     * 角色检查装饰器
     * @param {string|string[]} roles - 所需角色
     * @param {Object} options - 选项
     */
    checkRole: (roles, options = {}) => {
      return (req, res, next) => {
        const middleware = permissionMiddleware.requireRole(roles, options);
        return middleware(req, res, next);
      };
    },

    /**
     * 资源所有权检查装饰器
     * @param {string} resourceType - 资源类型
     * @param {Object} options - 选项
     */
    checkOwnership: (resourceType, options = {}) => {
      return (req, res, next) => {
        const middleware = permissionMiddleware.requireOwnership(resourceType, options);
        return middleware(req, res, next);
      };
    }
  };
}

/**
 * 批量应用权限中间件的工具函数
 */
function batchApplyPermissions(app, db, routePermissions) {
  const { requirePermission, requireRole, requireOwnership } = initializePermissionMiddleware(app, db);

  for (const [route, config] of Object.entries(routePermissions)) {
    const [method, path] = route.split(' ');
    
    let middleware;
    
    if (config.permissions) {
      middleware = requirePermission(config.permissions, config.options || {});
    } else if (config.roles) {
      middleware = requireRole(config.roles, config.options || {});
    } else if (config.ownership) {
      middleware = requireOwnership(config.ownership.resourceType, config.ownership.options || {});
    }

    if (middleware) {
      // 这里需要根据实际的路由注册方式来应用中间件
      console.log(`应用权限中间件到路由: ${route}`);
    }
  }
}

/**
 * 权限验证性能监控
 */
function setupPermissionMonitoring(app, db) {
  const metrics = {
    totalChecks: 0,
    successfulChecks: 0,
    failedChecks: 0,
    averageResponseTime: 0
  };

  // 添加性能监控中间件
  app.use('/api/v1/*', (req, res, next) => {
    const startTime = Date.now();
    
    // 监听响应结束事件
    res.on('finish', () => {
      const responseTime = Date.now() - startTime;
      metrics.totalChecks++;
      
      if (res.statusCode < 400) {
        metrics.successfulChecks++;
      } else {
        metrics.failedChecks++;
      }
      
      // 更新平均响应时间
      metrics.averageResponseTime = 
        (metrics.averageResponseTime * (metrics.totalChecks - 1) + responseTime) / 
        metrics.totalChecks;
    });
    
    next();
  });

  // 定期输出性能指标
  setInterval(() => {
    if (metrics.totalChecks > 0) {
      const successRate = (metrics.successfulChecks / metrics.totalChecks * 100).toFixed(2);
      console.log(`📊 权限验证性能: 成功率 ${successRate}%, 平均响应时间 ${metrics.averageResponseTime.toFixed(2)}ms`);
    }
  }, 60000); // 每分钟输出一次

  return metrics;
}

module.exports = {
  initializePermissionMiddleware,
  applyPermissionMiddlewareExample,
  createPermissionDecorator,
  batchApplyPermissions,
  setupPermissionMonitoring
};