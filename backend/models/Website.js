const SearchUtils = require('../utils/SearchUtils');

class Website {
  constructor(db) {
    this.db = db;
  }

  // 从设置中获取平台信息
  async getPlatformInfo(platformId) {
    try {
      // 如果没有platformId，返回null
      if (!platformId) {
        return null;
      }

      // 根据platform_id字段获取平台信息
      const [platformRows] = await this.db.execute(
        'SELECT id, name, platform_id FROM platforms WHERE platform_id = ?',
        [platformId]
      );

      if (platformRows.length > 0) {
        const platform = platformRows[0];
        return {
          id: platform.platform_id, // 使用platform_id作为前端的id
          name: platform.name,
          description: `${platform.name} 平台`
        };
      }

      return null;
    } catch (error) {
      console.error('获取平台信息失败:', error);
      return null;
    }
  }

  // 获取所有网站列表（带分页和筛选）
  async getAllWebsites(page = 1, limit = 50, filters = {}) {
    try {
      // 确保参数是数字类型
      const pageNum = parseInt(page) || 1;
      let limitNum = parseInt(limit) || 50;

      // 支持显示全部（当limit >= 10000时）
      if (limitNum >= 10000) {
        limitNum = 999999; // 设置一个很大的值来获取所有数据
      }

      const offset = (pageNum - 1) * limitNum;

      console.log(`获取网站列表: page=${pageNum}, limit=${limitNum}, offset=${offset}, filters:`, filters);

      // 构建WHERE条件
      let whereConditions = [];
      let queryParams = [];

      // 搜索条件 - 使用SearchUtils增强的模糊搜索
      if (filters.search && filters.search.trim()) {
        const searchFields = [
          'w.site_name',
          'w.site_url',
          'w.domain',
          'w.notes',
          'p.name',
          's.name',
          's.location',
          's.provider'
        ];

        const searchResult = SearchUtils.buildIntelligentSearchCondition(
          filters.search,
          searchFields,
          {
            multiKeyword: true,
            keywordLogic: 'AND',
            fuzzyMatch: true,
            caseSensitive: false,
            enableChineseSegmentation: true,
            enableRegionMapping: true
          }
        );

        if (searchResult.condition) {
          whereConditions.push(searchResult.condition);
          queryParams.push(...searchResult.params);
        }
      }

      // 状态筛选
      if (filters.status && filters.status.trim()) {
        whereConditions.push('w.status = ?');
        queryParams.push(filters.status);
      }

      // 行业筛选
      if (filters.industry && filters.industry.trim()) {
        whereConditions.push('w.industry = ?');
        queryParams.push(filters.industry);
      }

      // 平台筛选
      if (filters.platform && filters.platform.trim()) {
        whereConditions.push('p.name = ?');
        queryParams.push(filters.platform);
      }

      // 构建WHERE子句
      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 构建排序
      const sortBy = filters.sortBy || 'created_at';
      const sortOrder = filters.sortOrder === 'asc' ? 'ASC' : 'DESC';
      const sortField = this.getSortField(sortBy);

      // 主查询
      const query = `
        SELECT
          w.*,
          s.name as server_name,
          s.ip_address as server_ip,
          s.location as server_location,
          s.provider as server_provider,
          s.status as server_status,
          p.name as platform_name
        FROM websites w
        LEFT JOIN servers s ON w.server_id = s.id
        LEFT JOIN platforms p ON w.platform_id = p.platform_id
        ${whereClause}
        ORDER BY ${sortField} ${sortOrder}
        LIMIT ${limitNum} OFFSET ${offset}
      `;

      const [rows] = await this.db.execute(query, queryParams);
      console.log(`查询到 ${rows.length} 条网站记录`);

      // 查询总数（使用相同的筛选条件）
      const countQuery = `
        SELECT COUNT(*) as total
        FROM websites w
        LEFT JOIN servers s ON w.server_id = s.id
        LEFT JOIN platforms p ON w.platform_id = p.platform_id
        ${whereClause}
      `;
      const [countRows] = await this.db.execute(countQuery, queryParams);
      const total = countRows[0].total;
      console.log(`筛选后网站总数: ${total}`);

      // 格式化数据
      const websites = await Promise.all(rows.map(row => this.formatWebsiteData(row)));

      return {
        websites,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum)
        }
      };
    } catch (error) {
      console.error('获取网站列表失败:', error);
      throw error;
    }
  }

  // 根据ID获取单个网站
  async getWebsiteById(id) {
    try {
      const query = `
        SELECT
          w.*,
          s.name as server_name,
          s.ip_address as server_ip,
          s.location as server_location,
          s.provider as server_provider,
          s.status as server_status
        FROM websites w
        LEFT JOIN servers s ON w.server_id = s.id
        WHERE w.id = ?
      `;
      
      const [rows] = await this.db.execute(query, [id]);
      
      if (rows.length === 0) {
        return null;
      }
      
      return this.formatWebsiteData(rows[0]);
    } catch (error) {
      console.error('获取网站详情失败:', error);
      throw error;
    }
  }

  // 创建新网站
  async createWebsite(websiteData) {
    try {
      const {
        siteName, domain, siteUrl, platformId, serverId, industry, status = 'active',
        onlineDate, expireDate, projectAmount, renewalFee, sslExpireDate,
        domainExpireDate, notes, hasOnboard = false, siteId
      } = websiteData;

      // 如果没有提供siteId，则获取下一个site_id（最大值+1）
      let finalSiteId = siteId;
      if (!finalSiteId) {
        const [maxSiteIdRows] = await this.db.execute(
          'SELECT COALESCE(MAX(site_id), 0) + 1 as next_site_id FROM websites'
        );
        finalSiteId = maxSiteIdRows[0].next_site_id;
      }

      // 注意：站点名称允许重复，系统通过ID进行唯一识别

      const query = `
        INSERT INTO websites (
          site_id, site_name, domain, site_url, platform_id, server_id, industry, status,
          online_date, expire_date, project_amount, renewal_fee, ssl_expire_date,
          domain_expire_date, notes, has_onboard
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const values = [
        finalSiteId,
        siteName,
        domain || siteUrl.replace(/^https?:\/\//, '').replace(/\/.*$/, ''),
        siteUrl,
        platformId,
        serverId || null,
        industry || null,
        status,
        onlineDate || null,
        expireDate || null,
        projectAmount || null,
        renewalFee || null,
        sslExpireDate || null,
        domainExpireDate || null,
        notes || null,
        hasOnboard
      ];

      const [result] = await this.db.execute(query, values);
      const insertId = result.insertId;

      // 返回创建的网站信息
      return await this.getWebsiteById(insertId);
    } catch (error) {
      console.error('创建网站失败:', error);
      throw error;
    }
  }

  // 更新网站
  async updateWebsite(id, websiteData) {
    try {
      const {
        siteName, domain, siteUrl, platformId, serverId, industry, status,
        onlineDate, expireDate, projectAmount, renewalFee, sslExpireDate,
        domainExpireDate, accessStatusCode, responseTime, notes, hasOnboard,
        // 新增字段
        sslStatus, sslIssuer, domainStatus, domainRegistrar, accessStatus,
        performanceScore, mobileScore, desktopScore, pageLoadTime,
        firstContentfulPaint, largestContentfulPaint, cumulativeLayoutShift,
        securityScore, vulnerabilitiesCritical, vulnerabilitiesHigh,
        vulnerabilitiesMedium, vulnerabilitiesLow
      } = websiteData;

      // 检查网站是否存在
      const existing = await this.getWebsiteById(id);
      if (!existing) {
        return null;
      }

      // 注意：站点名称允许重复，系统通过ID进行唯一识别

      const query = `
        UPDATE websites SET
          site_name = COALESCE(?, site_name),
          domain = COALESCE(?, domain),
          site_url = COALESCE(?, site_url),
          platform_id = COALESCE(?, platform_id),
          server_id = ?,
          industry = ?,
          status = COALESCE(?, status),
          online_date = ?,
          expire_date = ?,
          project_amount = ?,
          renewal_fee = ?,
          ssl_expire_date = ?,
          ssl_status = COALESCE(?, ssl_status),
          ssl_issuer = ?,
          domain_expire_date = ?,
          domain_status = COALESCE(?, domain_status),
          domain_registrar = ?,
          access_status_code = COALESCE(?, access_status_code),
          access_status = COALESCE(?, access_status),
          response_time = ?,
          performance_score = ?,
          mobile_score = ?,
          desktop_score = ?,
          page_load_time = ?,
          first_contentful_paint = ?,
          largest_contentful_paint = ?,
          cumulative_layout_shift = ?,
          security_score = ?,
          vulnerabilities_critical = ?,
          vulnerabilities_high = ?,
          vulnerabilities_medium = ?,
          vulnerabilities_low = ?,
          last_check_time = CASE WHEN ? IS NOT NULL OR ? IS NOT NULL THEN NOW() ELSE last_check_time END,
          notes = ?,
          has_onboard = COALESCE(?, has_onboard),
          updated_at = NOW()
        WHERE id = ?
      `;

      const values = [
        siteName || null,
        domain || null,
        siteUrl || null,
        platformId || null,
        serverId || null,
        industry || null,
        status || null,
        onlineDate || null,
        expireDate || null,
        projectAmount || null,
        renewalFee || null,
        sslExpireDate || null,
        sslStatus || null,
        sslIssuer || null,
        domainExpireDate || null,
        domainStatus || null,
        domainRegistrar || null,
        accessStatusCode || null,
        accessStatus || null,
        responseTime || null,
        performanceScore || null,
        mobileScore || null,
        desktopScore || null,
        pageLoadTime || null,
        firstContentfulPaint || null,
        largestContentfulPaint || null,
        cumulativeLayoutShift || null,
        securityScore || null,
        vulnerabilitiesCritical || null,
        vulnerabilitiesHigh || null,
        vulnerabilitiesMedium || null,
        vulnerabilitiesLow || null,
        accessStatusCode || null, // 用于判断是否更新 last_check_time
        responseTime || null,     // 用于判断是否更新 last_check_time
        notes || null,
        hasOnboard !== undefined ? hasOnboard : null,
        id
      ];

      await this.db.execute(query, values);

      // 返回更新后的网站信息
      return await this.getWebsiteById(id);
    } catch (error) {
      console.error('更新网站失败:', error);
      throw error;
    }
  }

  // 删除网站
  async deleteWebsite(id) {
    try {
      // 检查网站是否存在
      const existing = await this.getWebsiteById(id);
      if (!existing) {
        return false;
      }

      // 获取该网站的所有附件
      const [attachmentRows] = await this.db.execute(
        'SELECT file_path FROM website_attachments WHERE website_id = ?',
        [id]
      );

      // 删除物理文件
      const fs = require('fs');
      const path = require('path');

      for (const attachment of attachmentRows) {
        try {
          if (fs.existsSync(attachment.file_path)) {
            fs.unlinkSync(attachment.file_path);
            console.log(`删除附件文件: ${attachment.file_path}`);
          }
        } catch (fileError) {
          console.warn(`删除附件文件失败: ${attachment.file_path}`, fileError.message);
          // 继续删除其他文件，不因单个文件删除失败而中断
        }
      }

      // 删除网站记录（外键约束会自动删除相关的附件记录、密码记录等）
      await this.db.execute('DELETE FROM websites WHERE id = ?', [id]);

      console.log(`网站删除成功: ID ${id}, 清理了 ${attachmentRows.length} 个附件文件`);
      return true;
    } catch (error) {
      console.error('删除网站失败:', error);
      throw error;
    }
  }

  // 获取网站统计信息
  async getWebsiteStats() {
    try {
      // 总数统计
      const [totalRows] = await this.db.execute('SELECT COUNT(*) as total FROM websites');
      const total = totalRows[0].total;

      // 按状态统计
      const [statusRows] = await this.db.execute(`
        SELECT status, COUNT(*) as count 
        FROM websites 
        GROUP BY status
      `);

      // 按平台统计 - 基于设置中的平台类型
      let platformRows = [];

      try {
        // 获取设置中的平台类型
        const [settingRows] = await this.db.execute(
          'SELECT setting_value FROM system_settings WHERE setting_key = ?',
          ['platforms']
        );

        let settingPlatforms = [];
        if (settingRows.length > 0 && settingRows[0].setting_value) {
          try {
            settingPlatforms = JSON.parse(settingRows[0].setting_value);
          } catch (error) {
            console.error('解析设置中的平台数据失败:', error);
          }
        }

        // 为每个设置中的平台统计网站数量
        for (const platformName of settingPlatforms) {
          const [countRows] = await this.db.execute(`
            SELECT COUNT(w.id) as count
            FROM websites w
            JOIN platforms p ON w.platform_id = p.platform_id
            WHERE p.name = ?
          `, [platformName]);

          const count = countRows[0].count;
          if (count > 0) {
            platformRows.push({ name: platformName, count });
          }
        }

        // 按数量降序排列
        platformRows.sort((a, b) => b.count - a.count);

        // 如果没有平台数据，添加未分配统计
        if (platformRows.length === 0) {
          const [unassignedRows] = await this.db.execute(`
            SELECT COUNT(*) as count FROM websites WHERE platform_id IS NULL
          `);
          if (unassignedRows[0].count > 0) {
            platformRows.push({ name: '未分配', count: unassignedRows[0].count });
          }
        }
      } catch (error) {
        console.error('获取平台统计失败:', error);
        // 如果出错，回退到原来的查询方式
        const [fallbackRows] = await this.db.execute(`
          SELECT
            COALESCE(p.name, '未分类') as name,
            COUNT(w.id) as count
          FROM platforms p
          LEFT JOIN websites w ON p.platform_id = w.platform_id
          GROUP BY p.id, p.name
          HAVING count > 0
          ORDER BY count DESC
        `);
        platformRows = fallbackRows;
      }

      // 按服务器统计
      const [serverRows] = await this.db.execute(`
        SELECT 
          COALESCE(s.name, '未分配') as name,
          COUNT(w.id) as count
        FROM websites w
        LEFT JOIN servers s ON w.server_id = s.id
        GROUP BY s.id, s.name
        ORDER BY count DESC
      `);

      // 即将到期的网站
      const [expiringRows] = await this.db.execute(`
        SELECT COUNT(*) as count
        FROM websites
        WHERE expire_date IS NOT NULL 
        AND expire_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
      `);

      return {
        total,
        byStatus: statusRows,
        byPlatform: platformRows,
        byServer: serverRows,
        expiringSoon: expiringRows[0].count
      };
    } catch (error) {
      console.error('获取网站统计失败:', error);
      throw error;
    }
  }

  // 格式化网站数据
  async formatWebsiteData(row) {
    // 获取平台信息
    const platform = await this.getPlatformInfo(row.platform_id);

    return {
      id: row.id,
      siteId: row.site_id,
      siteName: row.site_name,
      domain: row.domain,
      siteUrl: row.site_url,
      platform: platform,
      server: row.server_id ? {
        id: row.server_id,
        name: row.server_name,
        ipAddress: row.server_ip,
        location: row.server_location,
        provider: row.server_provider,
        status: row.server_status
      } : null,
      industry: row.industry,
      status: row.status,
      onlineDate: row.online_date,
      expireDate: row.expire_date,
      projectAmount: row.project_amount,
      renewalFee: row.renewal_fee,

      // SSL相关信息
      sslExpireDate: row.ssl_expire_date,
      sslStatus: row.ssl_status,
      sslIssuer: row.ssl_issuer,
      sslSubject: row.ssl_subject,
      sslValidFrom: row.ssl_valid_from,
      sslSerialNumber: row.ssl_serial_number,
      sslDaysUntilExpiry: row.ssl_days_until_expiry,
      sslLastCheck: row.ssl_last_check,

      // 为前端提供完整的SSL信息对象
      sslInfo: row.ssl_status ? {
        status: row.ssl_status,
        issuer: row.ssl_issuer,
        subject: row.ssl_subject,
        validFrom: row.ssl_valid_from,
        validTo: row.ssl_expire_date,
        expiryDate: row.ssl_expire_date, // 兼容前端的expiryDate字段
        daysUntilExpiry: row.ssl_days_until_expiry,
        serialNumber: row.ssl_serial_number,
        lastChecked: row.ssl_last_check,
        isValid: row.ssl_status === 'valid'
      } : null,

      // 域名相关信息
      domainExpireDate: row.domain_expire_date,
      domainStatus: row.domain_status,
      domainRegistrar: row.domain_registrar,
      domainLastCheck: row.domain_last_check,

      // 为前端提供完整的域名信息对象
      domainInfo: row.domain_status ? {
        registrar: row.domain_registrar || '未知',
        registrationDate: '未知', // 需要从WHOIS获取
        expirationDate: row.domain_expire_date || '未知',
        daysUntilExpiry: row.domain_expire_date ?
          Math.floor((new Date(row.domain_expire_date) - new Date()) / (1000 * 60 * 60 * 24)) : null,
        nameServers: [], // 需要从DNS查询获取
        status: row.domain_status,
        isExpired: row.domain_status === 'expired',
        lastChecked: row.domain_last_check || new Date().toISOString()
      } : null,

      // 访问状态信息
      accessStatusCode: row.access_status_code,
      accessStatus: row.access_status,
      responseTime: row.response_time,
      lastCheckTime: row.last_check_time,
      consecutiveFailures: row.consecutive_failures || 0,
      lastFailureTime: row.last_failure_time,
      notificationSent: row.notification_sent || false,
      lastNotificationTime: row.last_notification_time,

      // 性能评分信息
      performanceScore: row.performance_score,
      mobileScore: row.mobile_score,
      desktopScore: row.desktop_score,
      pageLoadTime: row.page_load_time,
      firstContentfulPaint: row.first_contentful_paint,
      largestContentfulPaint: row.largest_contentful_paint,
      cumulativeLayoutShift: row.cumulative_layout_shift,
      performanceLastCheck: row.performance_last_check,

      // 安全扫描信息
      securityScore: row.security_score,
      vulnerabilitiesCritical: row.vulnerabilities_critical,
      vulnerabilitiesHigh: row.vulnerabilities_high,
      vulnerabilitiesMedium: row.vulnerabilities_medium,
      vulnerabilitiesLow: row.vulnerabilities_low,
      securityLastScan: row.security_last_scan,

      notes: row.notes,
      hasOnboard: row.has_onboard || false,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  // 获取排序字段映射
  getSortField(sortBy) {
    const fieldMap = {
      'siteId': 'w.site_id',
      'siteName': 'w.site_name',
      'domain': 'w.domain',
      'status': 'w.status',
      'onlineDate': 'w.online_date',
      'expireDate': 'w.expire_date',
      'projectAmount': 'w.project_amount',
      'renewalFee': 'w.renewal_fee',
      'created_at': 'w.created_at',
      'updated_at': 'w.updated_at'
    };

    return fieldMap[sortBy] || 'w.site_id';
  }
}

module.exports = Website;
