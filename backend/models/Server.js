const mysql = require('mysql2/promise');

class ServerModel {
  constructor(db) {
    this.db = db;
  }

  // 获取所有服务器
  async getAllServers(page = 1, limit = 10, filters = {}) {
    try {
      let whereClause = 'WHERE 1=1';
      const params = [];

      // 添加筛选条件
      if (filters.status) {
        whereClause += ' AND s.status = ?';
        params.push(filters.status);
      }
      if (filters.provider) {
        whereClause += ' AND s.provider = ?';
        params.push(filters.provider);
      }
      if (filters.search) {
        whereClause += ' AND (s.name LIKE ? OR s.ip_address LIKE ? OR s.location LIKE ? OR s.department LIKE ? OR s.notes LIKE ?)';
        const searchTerm = `%${filters.search}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
      }

      // 计算总数
      const [countResult] = await this.db.execute(
        `SELECT COUNT(*) as total FROM servers s ${whereClause}`,
        params
      );
      const total = countResult[0].total;

      // 获取分页数据 - 先简化查询，不使用分页
      console.log('SQL查询参数:', {
        whereClause,
        params
      });

      const [rows] = await this.db.execute(
        `SELECT
          s.*,
          sli.cpu_usage, sli.memory_usage, sli.disk_usage,
          sli.network_in, sli.network_out, sli.uptime,
          sli.load_average_1, sli.load_average_5, sli.load_average_15,
          sli.processes, sli.last_updated as load_last_updated
        FROM servers s
        LEFT JOIN server_load_info sli ON s.id = sli.server_id
        ${whereClause}
        ORDER BY s.created_at DESC`,
        params
      );

      return {
        servers: rows.map(this.formatServerData),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('获取服务器列表失败:', error);
      throw error;
    }
  }

  // 根据ID获取服务器
  async getServerById(id) {
    try {
      const [rows] = await this.db.execute(
        `SELECT 
          s.*,
          sli.cpu_usage, sli.memory_usage, sli.disk_usage,
          sli.network_in, sli.network_out, sli.uptime,
          sli.load_average_1, sli.load_average_5, sli.load_average_15,
          sli.processes, sli.last_updated as load_last_updated
        FROM servers s
        LEFT JOIN server_load_info sli ON s.id = sli.server_id
        WHERE s.id = ?`,
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      return this.formatServerData(rows[0]);
    } catch (error) {
      console.error('获取服务器详情失败:', error);
      throw error;
    }
  }

  // 创建服务器
  async createServer(serverData) {
    const connection = await this.db.getConnection();
    try {
      await connection.beginTransaction();

      // 插入服务器基本信息
      const [result] = await connection.execute(
        `INSERT INTO servers (
          name, ip_address, location, provider, department, instance_id, type, region, uptime,
          cpu, memory, storage, bandwidth, os,
          expire_date, renewal_fee, status, monitoring_enabled,
          cpu_threshold, memory_threshold, disk_threshold, network_threshold,
          ssh_port, ssh_username, ssh_auth_type, ssh_password, ssh_private_key, ssh_key_passphrase,
          notes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          serverData.name || null,
          serverData.ipAddress || null,
          serverData.location || null,
          serverData.provider || null,
          serverData.department || null,
          serverData.instanceId || null,
          serverData.type || 'cloud',
          serverData.region || null,
          serverData.uptime || null,
          serverData.specifications?.cpu || null,
          serverData.specifications?.memory || null,
          serverData.specifications?.storage || null,
          serverData.specifications?.bandwidth || null,
          serverData.specifications?.os || null,
          serverData.expireDate || null,
          serverData.renewalFee || null,
          serverData.status || 'active',
          serverData.monitoringEnabled !== undefined ? serverData.monitoringEnabled : true,
          serverData.alertThresholds?.cpuUsage || 80,
          serverData.alertThresholds?.memoryUsage || 85,
          serverData.alertThresholds?.diskUsage || 90,
          serverData.alertThresholds?.networkUsage || 80,
          serverData.sshPort || 22,
          serverData.sshUsername || null,
          serverData.sshAuthType || 'password',
          serverData.sshPassword || null,
          serverData.sshPrivateKey || null,
          serverData.sshKeyPassphrase || null,
          serverData.notes || null
        ]
      );

      const serverId = result.insertId;

      // 创建初始负载信息记录
      await connection.execute(
        `INSERT INTO server_load_info (server_id) VALUES (?)`,
        [serverId]
      );

      await connection.commit();
      return await this.getServerById(serverId);
    } catch (error) {
      await connection.rollback();
      console.error('创建服务器失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // 更新服务器
  async updateServer(id, serverData) {
    const connection = await this.db.getConnection();
    try {
      await connection.beginTransaction();

      const [result] = await connection.execute(
        `UPDATE servers SET
          name = ?, ip_address = ?, location = ?, provider = ?, department = ?,
          instance_id = ?, type = ?, region = ?, uptime = ?,
          cpu = ?, memory = ?, storage = ?, bandwidth = ?, os = ?,
          expire_date = ?, renewal_fee = ?, status = ?, monitoring_enabled = ?,
          cpu_threshold = ?, memory_threshold = ?, disk_threshold = ?, network_threshold = ?,
          ssh_port = ?, ssh_username = ?, ssh_auth_type = ?, ssh_password = ?,
          ssh_private_key = ?, ssh_key_passphrase = ?, notes = ?,
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?`,
        [
          serverData.name || null,
          serverData.ipAddress || null,
          serverData.location || null,
          serverData.provider || null,
          serverData.department || null,
          serverData.instanceId || null,
          serverData.type || 'cloud',
          serverData.region || null,
          serverData.uptime || null,
          serverData.specifications?.cpu || null,
          serverData.specifications?.memory || null,
          serverData.specifications?.storage || null,
          serverData.specifications?.bandwidth || null,
          serverData.specifications?.os || null,
          serverData.expireDate || null,
          serverData.renewalFee || null,
          serverData.status || 'active',
          serverData.monitoringEnabled !== undefined ? serverData.monitoringEnabled : true,
          serverData.alertThresholds?.cpuUsage || 80,
          serverData.alertThresholds?.memoryUsage || 85,
          serverData.alertThresholds?.diskUsage || 90,
          serverData.alertThresholds?.networkUsage || 80,
          serverData.sshPort || 22,
          serverData.sshUsername || null,
          serverData.sshAuthType || 'password',
          serverData.sshPassword || null,
          serverData.sshPrivateKey || null,
          serverData.sshKeyPassphrase || null,
          serverData.notes || null,
          id
        ]
      );

      await connection.commit();

      if (result.affectedRows === 0) {
        return null;
      }

      return await this.getServerById(id);
    } catch (error) {
      await connection.rollback();
      console.error('更新服务器失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // 删除服务器
  async deleteServer(id) {
    try {
      const [result] = await this.db.execute(
        'DELETE FROM servers WHERE id = ?',
        [id]
      );

      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除服务器失败:', error);
      throw error;
    }
  }

  // 更新服务器到期日期
  async updateServerExpireDate(id, expireDate) {
    try {
      const [result] = await this.db.execute(
        'UPDATE servers SET expire_date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [expireDate, id]
      );

      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新服务器到期日期失败:', error);
      throw error;
    }
  }

  // 获取服务器选项（用于下拉选择）
  async getServerOptions() {
    try {
      const [rows] = await this.db.execute(
        'SELECT id, name, ip_address, location, provider, status FROM servers ORDER BY name'
      );

      return rows.map(row => ({
        id: row.id,
        name: row.name,
        ipAddress: row.ip_address,
        location: row.location,
        provider: row.provider,
        status: row.status
      }));
    } catch (error) {
      console.error('获取服务器选项失败:', error);
      throw error;
    }
  }

  // 批量导入服务器（支持更新）
  async batchImportServers(serversData) {
    const connection = await this.db.getConnection();
    try {
      await connection.beginTransaction();

      const results = [];
      for (const serverData of serversData) {
        try {
          let serverId = null;
          let isUpdate = false;

          // 检查IP地址是否已存在
          if (serverData.ipAddress) {
            const [existing] = await connection.execute(
              'SELECT id FROM servers WHERE ip_address = ?',
              [serverData.ipAddress]
            );

            if (existing.length > 0) {
              // IP地址存在，执行更新
              serverId = existing[0].id;
              isUpdate = true;
            }
          } else {
            // 没有IP地址，跳过这条记录
            results.push({
              success: false,
              data: serverData,
              error: 'IP地址不能为空'
            });
            continue;
          }

          if (isUpdate) {
            // 更新现有服务器 - 只更新非空字段
            const updateFields = [];
            const updateValues = [];

            // 构建动态更新语句，只更新提供的非空字段
            const addUpdateField = (field, dbField, value) => {
              if (value !== undefined && value !== null && value !== '') {
                updateFields.push(`${dbField} = ?`);
                updateValues.push(value);
              }
            };

            // 基本信息字段
            addUpdateField('name', 'name', serverData.name);
            addUpdateField('ipAddress', 'ip_address', serverData.ipAddress);
            addUpdateField('location', 'location', serverData.location);
            addUpdateField('provider', 'provider', serverData.provider);
            addUpdateField('department', 'department', serverData.department);

            // 新增字段
            addUpdateField('instanceId', 'instance_id', serverData.instanceId);
            addUpdateField('type', 'type', serverData.type);
            addUpdateField('region', 'region', serverData.region);
            addUpdateField('uptime', 'uptime', serverData.uptime);

            // 规格配置
            addUpdateField('cpu', 'cpu', serverData.cpu);
            addUpdateField('memory', 'memory', serverData.memory);
            addUpdateField('storage', 'storage', serverData.storage);
            addUpdateField('bandwidth', 'bandwidth', serverData.bandwidth);
            addUpdateField('os', 'os', serverData.os);

            // 其他字段（排除SSH配置，保护现有SSH设置）
            addUpdateField('expireDate', 'expire_date', serverData.expireDate);
            addUpdateField('renewalFee', 'renewal_fee', serverData.renewalFee);
            addUpdateField('status', 'status', serverData.status);
            addUpdateField('notes', 'notes', serverData.notes);

            // SSH配置字段在Excel导入时不更新，保护现有配置
            // addUpdateField('sshPort', 'ssh_port', serverData.sshPort);
            // addUpdateField('sshUsername', 'ssh_username', serverData.sshUsername);
            // addUpdateField('sshAuthType', 'ssh_auth_type', serverData.sshAuthType);
            // addUpdateField('sshPassword', 'ssh_password', serverData.sshPassword);
            // addUpdateField('sshPrivateKey', 'ssh_private_key', serverData.sshPrivateKey);
            // addUpdateField('sshKeyPassphrase', 'ssh_key_passphrase', serverData.sshKeyPassphrase);

            // 监控相关字段
            if (serverData.monitoringEnabled !== undefined) {
              updateFields.push('monitoring_enabled = ?');
              updateValues.push(serverData.monitoringEnabled);
            }
            addUpdateField('cpuThreshold', 'cpu_threshold', serverData.cpuThreshold);
            addUpdateField('memoryThreshold', 'memory_threshold', serverData.memoryThreshold);
            addUpdateField('diskThreshold', 'disk_threshold', serverData.diskThreshold);
            addUpdateField('networkThreshold', 'network_threshold', serverData.networkThreshold);

            // 如果有字段需要更新
            if (updateFields.length > 0) {
              updateFields.push('updated_at = CURRENT_TIMESTAMP');
              updateValues.push(serverId);

              const updateSQL = `UPDATE servers SET ${updateFields.join(', ')} WHERE id = ?`;

              await connection.execute(updateSQL, updateValues);

              console.log(`更新服务器 ID ${serverId}: 更新了 ${updateFields.length - 1} 个字段`);
            } else {
              console.log(`服务器 ID ${serverId}: 没有字段需要更新`);
            }

            results.push({
              success: true,
              action: 'updated',
              data: { id: serverId, ...serverData }
            });
          } else {
            // 插入新服务器 - 确保必填字段有值

            // 检查必填字段（IP地址已在前面验证过）
            if (!serverData.name) {
              throw new Error('服务器名称不能为空');
            }
            if (!serverData.location) {
              throw new Error('机房位置不能为空');
            }
            if (!serverData.provider) {
              throw new Error('服务商不能为空');
            }

            const [result] = await connection.execute(
              `INSERT INTO servers (
                name, ip_address, location, provider, department, instance_id, type, region, uptime,
                cpu, memory, storage, bandwidth, os,
                expire_date, renewal_fee, status, monitoring_enabled,
                cpu_threshold, memory_threshold, disk_threshold, network_threshold,
                ssh_port, ssh_username, ssh_auth_type, ssh_password, ssh_private_key, ssh_key_passphrase,
                notes
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              [
                serverData.name,
                serverData.ipAddress,
                serverData.location,
                serverData.provider,
                serverData.department || null,
                serverData.instanceId || null,
                serverData.type || 'cloud',
                serverData.region || null,
                serverData.uptime || null,
                serverData.cpu || null,
                serverData.memory || null,
                serverData.storage || null,
                serverData.bandwidth || null,
                serverData.os || null,
                serverData.expireDate || null,
                serverData.renewalFee || null,
                serverData.status || 'active',
                serverData.monitoringEnabled !== false,
                serverData.cpuThreshold || 80,
                serverData.memoryThreshold || 85,
                serverData.diskThreshold || 90,
                serverData.networkThreshold || 80,
                serverData.sshPort || 22,
                serverData.sshUsername || null,
                serverData.sshAuthType || 'password',
                serverData.sshPassword || null,
                serverData.sshPrivateKey || null,
                serverData.sshKeyPassphrase || null,
                serverData.notes || null
              ]
            );

            serverId = result.insertId;

            // 创建负载信息记录
            await connection.execute(
              'INSERT INTO server_load_info (server_id) VALUES (?)',
              [serverId]
            );

            results.push({
              success: true,
              action: 'inserted',
              data: { id: serverId, ...serverData }
            });
          }
        } catch (error) {
          console.error(`导入服务器失败 (IP: ${serverData.ipAddress}, 名称: ${serverData.name}):`, error);
          results.push({
            success: false,
            data: serverData,
            error: error.message
          });
        }
      }

      await connection.commit();
      return results;
    } catch (error) {
      await connection.rollback();
      console.error('批量导入服务器失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  // 格式化服务器数据
  formatServerData(row) {
    return {
      id: row.id,
      name: row.name,
      ipAddress: row.ip_address,
      location: row.location,
      provider: row.provider,
      department: row.department,

      // 新增字段
      instanceId: row.instance_id,
      type: row.type,
      region: row.region,
      uptime: row.uptime,

      specifications: {
        cpu: row.cpu,
        memory: row.memory,
        storage: row.storage,
        bandwidth: row.bandwidth,
        os: row.os
      },
      expireDate: row.expire_date,
      renewalFee: row.renewal_fee,
      status: row.status,
      monitoringEnabled: row.monitoring_enabled,
      alertThresholds: {
        cpuUsage: row.cpu_threshold,
        memoryUsage: row.memory_threshold,
        diskUsage: row.disk_threshold,
        networkUsage: row.network_threshold
      },
      sshPort: row.ssh_port,
      sshUsername: row.ssh_username,
      sshAuthType: row.ssh_auth_type,
      sshPassword: row.ssh_password,
      sshPrivateKey: row.ssh_private_key,
      sshKeyPassphrase: row.ssh_key_passphrase,
      loadInfo: row.cpu_usage !== null ? {
        cpuUsage: parseFloat(row.cpu_usage),
        memoryUsage: parseFloat(row.memory_usage),
        diskUsage: parseFloat(row.disk_usage),
        networkIn: parseFloat(row.network_in),
        networkOut: parseFloat(row.network_out),
        uptime: row.uptime,
        loadAverage: [
          parseFloat(row.load_average_1),
          parseFloat(row.load_average_5),
          parseFloat(row.load_average_15)
        ],
        processes: row.processes,
        lastUpdated: row.load_last_updated
      } : null,
      notes: row.notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  // 检测服务器配置信息
  async detectServerConfig(serverData) {
    try {
      // 这个方法主要用于调用SSH检测函数
      // 实际的SSH检测逻辑在simple-server.js中的detectServerConfigViaSSH函数
      // 这里可以添加数据库记录逻辑，比如保存检测历史等

      console.log(`开始检测服务器配置: ${serverData.name || serverData.ipAddress}`);

      // 可以在这里添加检测前的验证逻辑
      if (!serverData.ipAddress || !serverData.sshUsername) {
        throw new Error('缺少必要的连接信息');
      }

      // 返回成功标识，实际检测在API层进行
      return {
        success: true,
        message: '配置检测请求已接收'
      };
    } catch (error) {
      console.error('检测服务器配置失败:', error);
      throw error;
    }
  }

  // 更新服务器配置信息（从检测结果自动更新）
  async updateServerConfigFromDetection(id, configInfo) {
    const connection = await this.db.getConnection();
    try {
      await connection.beginTransaction();

      // 更新服务器的硬件配置信息
      const updateData = {};

      if (configInfo.systemInfo) {
        if (configInfo.systemInfo.cpu) {
          updateData.cpu = configInfo.systemInfo.cpu;
        }
        if (configInfo.systemInfo.memory) {
          updateData.memory = configInfo.systemInfo.memory;
        }
        if (configInfo.systemInfo.storage) {
          updateData.storage = configInfo.systemInfo.storage;
        }
        if (configInfo.systemInfo.os) {
          updateData.os = configInfo.systemInfo.os;
        }
      }

      // 如果有配置信息需要更新
      if (Object.keys(updateData).length > 0) {
        const setClause = Object.keys(updateData).map(key => `${key} = ?`).join(', ');
        const values = Object.values(updateData);
        values.push(id);

        await connection.execute(
          `UPDATE servers SET ${setClause}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
          values
        );

        console.log(`服务器配置信息已更新: ID ${id}`);
      }

      await connection.commit();
      return await this.getServerById(id);
    } catch (error) {
      await connection.rollback();
      console.error('更新服务器配置信息失败:', error);
      throw error;
    } finally {
      connection.release();
    }
  }
}

module.exports = ServerModel;
