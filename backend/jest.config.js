/**
 * Jest 配置文件
 * 权限系统单元测试和集成测试配置
 */

module.exports = {
  // 测试环境
  testEnvironment: 'node',

  // 测试文件匹配模式
  testMatch: [
    '**/test/**/*.test.js',
    '**/test/**/*.spec.js'
  ],

  // 覆盖率收集配置
  collectCoverage: true,
  collectCoverageFrom: [
    'services/**/*.js',
    'middleware/**/*.js',
    '!**/node_modules/**',
    '!**/test/**',
    '!**/coverage/**'
  ],

  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov'
  ],

  // 覆盖率输出目录
  coverageDirectory: 'coverage',

  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './services/PermissionService.js': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    },
    './services/RoleService.js': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    },
    './services/PermissionCacheService.js': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './middleware/PermissionMiddleware.js': {
      branches: 85,
      functions: 85,
      lines: 85,
      statements: 85
    }
  },

  // 测试设置文件
  setupFilesAfterEnv: [
    '<rootDir>/test/setup.js'
  ],

  // 忽略的文件和目录
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/build/',
    '/coverage/'
  ],

  // 全局变量
  globals: {
    'process.env.NODE_ENV': 'test'
  },

  // 测试超时时间（毫秒）
  testTimeout: 10000,

  // 详细输出
  verbose: true,

  // 清除模拟
  clearMocks: true,
  restoreMocks: true,

  // 错误时停止
  bail: false,

  // 最大并发数
  maxConcurrency: 5,

  // 测试结果处理器
  reporters: [
    'default',
    [
      'jest-html-reporters',
      {
        publicPath: './coverage/html-report',
        filename: 'report.html',
        expand: true
      }
    ]
  ],

  // 模拟配置
  moduleFileExtensions: ['js', 'json'],

  // 测试序列化器
  snapshotSerializers: [],

  // 监听模式配置
  watchman: true,
  watchPathIgnorePatterns: [
    '/node_modules/',
    '/coverage/',
    '/logs/'
  ]
};