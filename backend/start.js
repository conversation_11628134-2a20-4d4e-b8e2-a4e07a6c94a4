// 设置模块路径别名
const Module = require('module');
const path = require('path');

const originalResolveFilename = Module._resolveFilename;
Module._resolveFilename = function (request, parent, isMain) {
  if (request.startsWith('@/')) {
    const newRequest = path.join(__dirname, 'dist', request.slice(2));
    return originalResolveFilename.call(this, newRequest, parent, isMain);
  }
  return originalResolveFilename.call(this, request, parent, isMain);
};

// 启动应用
require('./dist/index.js');
