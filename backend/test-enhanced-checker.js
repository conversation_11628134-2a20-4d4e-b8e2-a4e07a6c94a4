/**
 * 增强版HTTP检查器测试脚本
 * 用于验证coolmonitor风格的监控功能
 */

const { Enhanced<PERSON>ttp<PERSON><PERSON><PERSON>, MONITOR_STATUS } = require('./services/enhanced-http-checker');

async function testEnhancedChecker() {
  console.log('🚀 开始测试增强版HTTP检查器...\n');

  const checker = new EnhancedHttpChecker();

  // 测试用例
  const testCases = [
    {
      name: '基础HTTP检查 - 百度',
      config: {
        url: 'https://www.baidu.com',
        httpMethod: 'GET',
        statusCodes: '200-299',
        connectTimeout: 10,
        notifyCertExpiry: true
      }
    },
    {
      name: 'HTTP检查 - 自定义状态码',
      config: {
        url: 'https://httpstat.us/301',
        httpMethod: 'GET',
        statusCodes: '301,302',
        connectTimeout: 10
      }
    },
    {
      name: 'HTTPS证书检查 - GitHub',
      config: {
        url: 'https://github.com',
        notifyCertExpiry: true,
        connectTimeout: 10
      }
    },
    {
      name: '关键词检查 - 百度搜索',
      config: {
        url: 'https://www.baidu.com',
        keyword: '百度一下,搜索',
        httpMethod: 'GET',
        statusCodes: '200-299',
        connectTimeout: 10
      }
    },
    {
      name: '重试机制测试 - 不存在的域名',
      config: {
        url: 'https://this-domain-does-not-exist-12345.com',
        httpMethod: 'GET',
        statusCodes: '200-299',
        connectTimeout: 5,
        retries: 2,
        retryInterval: 1
      }
    },
    {
      name: 'SSL证书单独检查 - 腾讯',
      config: {
        url: 'https://www.qq.com',
        connectTimeout: 10
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`📋 测试: ${testCase.name}`);
    console.log(`🔗 URL: ${testCase.config.url}`);
    
    const startTime = Date.now();
    
    try {
      let result;
      
      // 根据测试类型选择不同的检查方法
      if (testCase.name.includes('关键词检查')) {
        result = await checker.checkKeyword(testCase.config);
      } else if (testCase.name.includes('SSL证书单独检查')) {
        result = await checker.checkHttpsCertificate(testCase.config);
      } else {
        result = await checker.checkHttp(testCase.config);
      }
      
      const duration = Date.now() - startTime;
      
      console.log(`✅ 检查结果:`);
      console.log(`   状态: ${result.status === MONITOR_STATUS.UP ? '✅ 正常' : '❌ 异常'}`);
      console.log(`   消息: ${result.message}`);
      console.log(`   响应时间: ${result.ping}ms`);
      console.log(`   总耗时: ${duration}ms`);
      
      if (result.certificateDaysRemaining !== undefined) {
        console.log(`   SSL证书剩余天数: ${result.certificateDaysRemaining}天`);
      }
      
    } catch (error) {
      console.log(`❌ 检查失败: ${error.message}`);
    }
    
    console.log('─'.repeat(60));
  }

  // 测试状态码检查函数
  console.log('\n🔍 测试状态码检查函数:');
  const { checkStatusCode } = require('./services/enhanced-http-checker');
  
  const statusTests = [
    { code: 200, expected: '200-299', result: checkStatusCode(200, '200-299') },
    { code: 301, expected: '200-299', result: checkStatusCode(301, '200-299') },
    { code: 301, expected: '301,302', result: checkStatusCode(301, '301,302') },
    { code: 404, expected: '200-299,404', result: checkStatusCode(404, '200-299,404') },
    { code: 500, expected: '200-299', result: checkStatusCode(500, '200-299') }
  ];
  
  statusTests.forEach(test => {
    console.log(`   状态码 ${test.code} vs 期望 "${test.expected}": ${test.result ? '✅' : '❌'}`);
  });

  console.log('\n🎉 增强版HTTP检查器测试完成!');
}

// 运行测试
if (require.main === module) {
  testEnhancedChecker().catch(console.error);
}

module.exports = { testEnhancedChecker };
