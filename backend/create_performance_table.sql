-- 创建性能指标表
CREATE TABLE IF NOT EXISTS performance_metrics (
  id INT AUTO_INCREMENT PRIMARY KEY,
  timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  total_checks INT DEFAULT 0 COMMENT '总检测次数',
  successful_checks INT DEFAULT 0 COMMENT '成功检测次数',
  failed_checks INT DEFAULT 0 COMMENT '失败检测次数',
  avg_response_time DECIMAL(10,3) DEFAULT 0 COMMENT '平均响应时间(秒)',
  checks_per_second DECIMAL(10,3) DEFAULT 0 COMMENT '每秒检测次数',
  cpu_usage DECIMAL(5,2) DEFAULT 0 COMMENT 'CPU使用率(%)',
  memory_usage DECIMAL(5,2) DEFAULT 0 COMMENT '内存使用率(%)',
  total_notifications INT DEFAULT 0 COMMENT '总通知次数',
  successful_notifications INT DEFAULT 0 COMMENT '成功通知次数',
  error_rate DECIMAL(5,2) DEFAULT 0 COMMENT '错误率(%)',
  metrics_data JSON COMMENT '详细指标数据',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_timestamp (timestamp),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='性能监控指标表';

-- 创建监控系统配置表
CREATE TABLE IF NOT EXISTS monitor_system_configs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
  config_value TEXT COMMENT '配置值',
  config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
  description TEXT COMMENT '配置描述',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_config_key (config_key),
  INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='监控系统配置表';
