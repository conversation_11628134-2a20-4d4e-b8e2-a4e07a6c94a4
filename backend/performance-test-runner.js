#!/usr/bin/env node

/**
 * 权限系统性能测试验证脚本
 * 用于验证性能测试环境和快速执行基础性能测试
 */

const fs = require('fs');
const path = require('path');

console.log('⚡ 权限系统性能测试环境验证');
console.log('='.repeat(60));

// 检查性能测试文件
const performanceTestFiles = [
  'test/performance/PermissionPerformance.test.js',
  'test/performance/performance-setup.js',
  'jest.performance.config.js',
  'scripts/run-performance-tests.js'
];

console.log('\n📁 检查性能测试文件:');
let allFilesExist = true;

performanceTestFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  const exists = fs.existsSync(filePath);
  const status = exists ? '✅' : '❌';
  console.log(`  ${status} ${file}`);
  
  if (!exists) {
    allFilesExist = false;
  }
});

// 检查性能测试脚本
console.log('\n📋 检查性能测试脚本:');
try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  const scripts = packageJson.scripts || {};
  
  const performanceScripts = [
    'test:performance',
    'test:performance:basic',
    'test:performance:stress',
    'test:performance:all'
  ];
  
  performanceScripts.forEach(script => {
    const exists = scripts[script];
    const status = exists ? '✅' : '❌';
    console.log(`  ${status} ${script}: ${exists || '未定义'}`);
  });
  
} catch (error) {
  console.log('  ❌ 无法读取package.json');
  allFilesExist = false;
}

// 检查Node.js性能相关功能
console.log('\n🔧 检查Node.js性能功能:');

// 检查高精度时间
try {
  const start = process.hrtime.bigint();
  const end = process.hrtime.bigint();
  const duration = Number(end - start);
  console.log('  ✅ process.hrtime.bigint() 可用');
} catch (error) {
  console.log('  ❌ process.hrtime.bigint() 不可用');
}

// 检查内存使用监控
try {
  const memUsage = process.memoryUsage();
  console.log(`  ✅ process.memoryUsage() 可用 (堆使用: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB)`);
} catch (error) {
  console.log('  ❌ process.memoryUsage() 不可用');
}

// 检查CPU使用监控
try {
  const cpuUsage = process.cpuUsage();
  console.log('  ✅ process.cpuUsage() 可用');
} catch (error) {
  console.log('  ❌ process.cpuUsage() 不可用');
}

// 检查垃圾回收
if (global.gc) {
  console.log('  ✅ 垃圾回收已启用 (--expose-gc)');
} else {
  console.log('  ⚠️  垃圾回收未启用 (建议使用 --expose-gc 启动)');
}

// 性能测试配置验证
console.log('\n⚙️  性能测试配置:');

const performanceConfig = {
  '单个权限验证阈值': '< 50ms',
  '批量权限验证阈值': '< 100ms', 
  '缓存命中阈值': '< 5ms',
  '中间件响应阈值': '< 50ms',
  '并发吞吐量阈值': '> 100 req/s',
  '缓存命中率目标': '> 95%',
  '成功率目标': '> 95%'
};

Object.entries(performanceConfig).forEach(([metric, threshold]) => {
  console.log(`  📊 ${metric}: ${threshold}`);
});

// 系统资源检查
console.log('\n💻 系统资源:');
const os = require('os');

console.log(`  CPU: ${os.cpus().length} 核 ${os.cpus()[0].model}`);
console.log(`  内存: ${Math.round(os.totalmem() / 1024 / 1024 / 1024)} GB 总计, ${Math.round(os.freemem() / 1024 / 1024 / 1024)} GB 可用`);
console.log(`  平台: ${os.platform()} ${os.arch()}`);
console.log(`  Node.js: ${process.version}`);

// 性能测试建议
console.log('\n💡 性能测试建议:');
console.log('  1. 在性能测试前关闭其他占用资源的应用');
console.log('  2. 使用 --expose-gc 启动Node.js以启用垃圾回收控制');
console.log('  3. 多次运行测试以获得稳定的结果');
console.log('  4. 在生产环境类似的硬件上运行测试');
console.log('  5. 监控系统资源使用情况');

// 总结
console.log('\n' + '='.repeat(60));
if (allFilesExist) {
  console.log('🎉 性能测试环境验证通过！');
  console.log('\n可以运行以下命令开始性能测试:');
  console.log('  npm run test:performance           # 运行所有性能测试');
  console.log('  npm run test:performance:basic     # 运行基础性能测试');
  console.log('  npm run test:performance:stress    # 运行压力测试');
  console.log('  npm run test:performance:all       # 运行完整性能测试并生成报告');
  console.log('  node scripts/run-performance-tests.js --help  # 查看详细选项');
  
  console.log('\n高级用法:');
  console.log('  node --expose-gc scripts/run-performance-tests.js all --gc --report');
  console.log('  node scripts/run-performance-tests.js stress --iterations=200 --concurrency=20');
  
} else {
  console.log('❌ 性能测试环境验证失败！');
  console.log('请确保所有必需的文件都已创建。');
  process.exit(1);
}

console.log('');