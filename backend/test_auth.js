const jwt = require('jsonwebtoken');

// JWT配置
const JWT_SECRET = 'test-secret';

// 认证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '访问被拒绝：需要认证令牌'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({
        success: false,
        message: '无效的认证令牌'
      });
    }
    req.user = user;
    next();
  });
};

console.log('认证中间件定义成功');
console.log('authenticateToken:', typeof authenticateToken);
