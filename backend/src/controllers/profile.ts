import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import bcrypt from 'bcrypt';

// 用户资料接口
interface UserProfile {
  id: number;
  username: string;
  email: string;
  realName: string;
  phone: string;
  avatar: string;
  role: string;
  department: string;
  position: string;
  bio: string;
  preferences: {
    language: string;
    timezone: string;
    theme: string;
    emailNotifications: boolean;
    smsNotifications: boolean;
    desktopNotifications: boolean;
  };
  security: {
    twoFactorEnabled: boolean;
    lastPasswordChange: string;
    loginSessions: Array<{
      id: string;
      ip: string;
      location: string;
      device: string;
      lastActive: string;
      current: boolean;
    }>;
  };
  activityLog: Array<{
    id: number;
    action: string;
    description: string;
    ip: string;
    timestamp: string;
    status: 'success' | 'warning' | 'error';
  }>;
}

// 模拟用户数据
const mockUserProfile: UserProfile = {
  id: 1,
  username: 'admin',
  email: '<EMAIL>',
  realName: '系统管理员',
  phone: '13800138000',
  avatar: '',
  role: 'admin',
  department: '技术部',
  position: '系统管理员',
  bio: '负责系统维护和管理工作',
  preferences: {
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    theme: 'light',
    emailNotifications: true,
    smsNotifications: false,
    desktopNotifications: true
  },
  security: {
    twoFactorEnabled: false,
    lastPasswordChange: '2024-05-15',
    loginSessions: [
      {
        id: '1',
        ip: '*************',
        location: '北京市',
        device: 'Chrome on Windows',
        lastActive: '2024-06-16 15:30:00',
        current: true
      },
      {
        id: '2',
        ip: '*************',
        location: '上海市',
        device: 'Safari on macOS',
        lastActive: '2024-06-15 09:20:00',
        current: false
      }
    ]
  },
  activityLog: [
    {
      id: 1,
      action: '登录系统',
      description: '用户成功登录系统',
      ip: '*************',
      timestamp: '2024-06-16 15:30:00',
      status: 'success'
    },
    {
      id: 2,
      action: '修改网站信息',
      description: '更新了阿里巴巴官网的SSL证书信息',
      ip: '*************',
      timestamp: '2024-06-16 14:25:00',
      status: 'success'
    },
    {
      id: 3,
      action: '删除用户',
      description: '删除了用户 test001',
      ip: '*************',
      timestamp: '2024-06-16 11:15:00',
      status: 'warning'
    }
  ]
};

// 获取用户资料
export const getProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    logger.info(`获取用户资料: ${userId}`);

    // 在实际应用中，这里应该从数据库获取用户资料
    const profile = mockUserProfile;

    res.json({
      success: true,
      data: profile
    });

  } catch (error) {
    logger.error('获取用户资料失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户资料失败'
    });
  }
};

// 更新用户资料
export const updateProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const { realName, email, phone, department, position, bio } = req.body;
    logger.info(`更新用户资料: ${userId}`);

    // 在实际应用中，这里应该验证数据并更新数据库
    // 这里只是模拟更新成功

    res.json({
      success: true,
      message: '个人资料更新成功'
    });

  } catch (error) {
    logger.error('更新用户资料失败:', error);
    res.status(500).json({
      success: false,
      message: '更新用户资料失败'
    });
  }
};

// 修改密码
export const changePassword = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const { currentPassword, newPassword } = req.body;
    logger.info(`修改密码: ${userId}`);

    // 在实际应用中，这里应该：
    // 1. 验证当前密码
    // 2. 加密新密码
    // 3. 更新数据库
    // 4. 记录操作日志

    // 模拟密码验证和更新
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    res.json({
      success: true,
      message: '密码修改成功'
    });

  } catch (error) {
    logger.error('修改密码失败:', error);
    res.status(500).json({
      success: false,
      message: '修改密码失败'
    });
  }
};

// 更新偏好设置
export const updatePreferences = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const preferences = req.body;
    logger.info(`更新偏好设置: ${userId}`);

    // 在实际应用中，这里应该更新数据库中的用户偏好设置
    // 这里只是模拟更新成功

    res.json({
      success: true,
      message: '偏好设置更新成功'
    });

  } catch (error) {
    logger.error('更新偏好设置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新偏好设置失败'
    });
  }
};

// 上传头像
export const uploadAvatar = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    logger.info(`上传头像: ${userId}`);

    // 在实际应用中，这里应该处理文件上传并更新数据库
    const avatarUrl = '/uploads/avatars/default.jpg';

    res.json({
      success: true,
      data: { avatarUrl },
      message: '头像上传成功'
    });

  } catch (error) {
    logger.error('上传头像失败:', error);
    res.status(500).json({
      success: false,
      message: '上传头像失败'
    });
  }
};

// 获取活动日志
export const getActivityLog = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const { page = 1, pageSize = 10 } = req.query;
    logger.info(`获取活动日志: ${userId}`);

    // 在实际应用中，这里应该从数据库分页获取活动日志
    const activityLog = mockUserProfile.activityLog;

    res.json({
      success: true,
      data: {
        list: activityLog,
        total: activityLog.length,
        page: parseInt(page as string),
        pageSize: parseInt(pageSize as string)
      }
    });

  } catch (error) {
    logger.error('获取活动日志失败:', error);
    res.status(500).json({
      success: false,
      message: '获取活动日志失败'
    });
  }
};

// 获取登录会话
export const getLoginSessions = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    logger.info(`获取登录会话: ${userId}`);

    // 在实际应用中，这里应该从数据库获取用户的登录会话
    const sessions = mockUserProfile.security.loginSessions;

    res.json({
      success: true,
      data: sessions
    });

  } catch (error) {
    logger.error('获取登录会话失败:', error);
    res.status(500).json({
      success: false,
      message: '获取登录会话失败'
    });
  }
};

// 终止登录会话
export const terminateSession = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const { sessionId } = req.params;
    logger.info(`终止登录会话: ${userId}, sessionId: ${sessionId}`);

    // 在实际应用中，这里应该从数据库删除指定的会话
    // 这里只是模拟终止成功

    res.json({
      success: true,
      message: '会话终止成功'
    });

  } catch (error) {
    logger.error('终止登录会话失败:', error);
    res.status(500).json({
      success: false,
      message: '终止登录会话失败'
    });
  }
};

// 启用/禁用双因子认证
export const toggleTwoFactor = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    const { enabled } = req.body;
    logger.info(`${enabled ? '启用' : '禁用'}双因子认证: ${userId}`);

    // 在实际应用中，这里应该处理双因子认证的启用/禁用逻辑
    // 这里只是模拟操作成功

    res.json({
      success: true,
      message: `双因子认证${enabled ? '启用' : '禁用'}成功`
    });

  } catch (error) {
    logger.error('切换双因子认证失败:', error);
    res.status(500).json({
      success: false,
      message: '切换双因子认证失败'
    });
  }
};
