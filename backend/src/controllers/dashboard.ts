import { Request, Response } from 'express';
import { pool } from '@/config/database';
import { getCache, setCache, CacheKeys } from '@/services/cache';
import { logger } from '@/utils/logger';

/**
 * 获取仪表盘统计数据
 */
export const getDashboardStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const cacheKey = 'dashboard:stats';

    // 尝试从缓存获取
    const cachedStats = await getCache(cacheKey);
    if (cachedStats) {
      logger.debug('仪表盘统计缓存命中');
      res.json({
        ...cachedStats,
        cached: true
      });
      return;
    }

    // 并行查询各种统计数据
    const [
      websiteStats,
      projectStats,
      serverStats,
      domainStats,
      customerStats,
      recentActivities
    ] = await Promise.all([
      getWebsiteStats(),
      getProjectStats(),
      getServerStats(),
      getDomainStats(),
      getCustomerStats(),
      getRecentActivities()
    ]);

    const stats = {
      // 网站统计
      totalWebsites: websiteStats.total,
      activeWebsites: websiteStats.active,
      inactiveWebsites: websiteStats.inactive,
      expiringSoon: websiteStats.expiringSoon,
      
      // 项目统计
      totalProjects: projectStats.total,
      onlineProjects: projectStats.online,
      developmentProjects: projectStats.development,
      testingProjects: projectStats.testing,
      
      // 服务器统计
      totalServers: serverStats.total,
      activeServers: serverStats.active,
      maintenanceServers: serverStats.maintenance,
      
      // 域名统计
      totalDomains: domainStats.total,
      activeDomains: domainStats.active,
      expiredDomains: domainStats.expired,
      
      // 客户统计
      totalCustomers: customerStats.total,
      activeCustomers: customerStats.active,
      
      // 最近活动
      recentActivities: recentActivities.slice(0, 10)
    };

    const response = {
      success: true,
      message: '获取仪表盘统计成功',
      data: stats,
      timestamp: new Date().toISOString()
    };

    // 缓存结果（缓存5分钟）
    await setCache(cacheKey, response, 300);

    res.json({
      ...response,
      cached: false
    });

  } catch (error) {
    logger.error('获取仪表盘统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取仪表盘统计失败',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 获取网站统计
 */
async function getWebsiteStats() {
  const [rows] = await pool.execute(`
    SELECT 
      COUNT(*) as total,
      SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
      SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive,
      SUM(CASE WHEN expire_date <= DATE_ADD(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as expiringSoon
    FROM websites
  `);
  return (rows as any[])[0];
}

/**
 * 获取项目统计
 */
async function getProjectStats() {
  const [rows] = await pool.execute(`
    SELECT 
      COUNT(*) as total,
      SUM(CASE WHEN online_status = 'online' THEN 1 ELSE 0 END) as online,
      SUM(CASE WHEN online_status = 'development' THEN 1 ELSE 0 END) as development,
      SUM(CASE WHEN online_status = 'testing' THEN 1 ELSE 0 END) as testing
    FROM projects
  `);
  return (rows as any[])[0];
}

/**
 * 获取服务器统计
 */
async function getServerStats() {
  const [rows] = await pool.execute(`
    SELECT 
      COUNT(*) as total,
      SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
      SUM(CASE WHEN status = 'maintenance' THEN 1 ELSE 0 END) as maintenance
    FROM servers
  `);
  return (rows as any[])[0];
}

/**
 * 获取域名统计
 */
async function getDomainStats() {
  const [rows] = await pool.execute(`
    SELECT 
      COUNT(*) as total,
      SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active,
      SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired
    FROM domains
  `);
  return (rows as any[])[0];
}

/**
 * 获取客户统计
 */
async function getCustomerStats() {
  const [rows] = await pool.execute(`
    SELECT 
      COUNT(*) as total,
      SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active
    FROM customers
  `);
  return (rows as any[])[0];
}

/**
 * 获取最近活动
 */
async function getRecentActivities() {
  // 这里可以从日志表或活动表获取，暂时返回模拟数据
  return [
    {
      id: 1,
      type: 'website',
      title: '网站 example.com 上线成功',
      description: '网站已成功部署并开始运行',
      time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      status: 'success'
    },
    {
      id: 2,
      type: 'project',
      title: '项目 "企业官网" 进入测试阶段',
      description: '开发完成，开始进行功能测试',
      time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
      status: 'info'
    },
    {
      id: 3,
      type: 'server',
      title: '服务器 Server-01 CPU使用率过高',
      description: 'CPU使用率达到85%，建议检查',
      time: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
      status: 'warning'
    }
  ];
}

/**
 * 获取图表数据
 */
export const getDashboardCharts = async (req: Request, res: Response): Promise<void> => {
  try {
    const { type, period = '7d' } = req.query;
    const cacheKey = `dashboard:charts:${type}:${period}`;

    // 尝试从缓存获取
    const cachedCharts = await getCache(cacheKey);
    if (cachedCharts) {
      logger.debug(`图表数据缓存命中: ${type}`);
      res.json({
        ...cachedCharts,
        cached: true
      });
      return;
    }

    let chartData;
    switch (type) {
      case 'website-status':
        chartData = await getWebsiteStatusChart();
        break;
      case 'project-progress':
        chartData = await getProjectProgressChart();
        break;
      case 'monthly-trend':
        chartData = await getMonthlyTrendChart(period as string);
        break;
      case 'platform-distribution':
        chartData = await getPlatformDistributionChart();
        break;
      default:
        chartData = await getWebsiteStatusChart();
    }

    const response = {
      success: true,
      message: '获取图表数据成功',
      data: chartData,
      timestamp: new Date().toISOString()
    };

    // 缓存结果（缓存10分钟）
    await setCache(cacheKey, response, 600);

    res.json({
      ...response,
      cached: false
    });

  } catch (error) {
    logger.error('获取图表数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取图表数据失败',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 获取网站状态分布图表数据
 */
async function getWebsiteStatusChart() {
  const [rows] = await pool.execute(`
    SELECT status, COUNT(*) as count
    FROM websites
    GROUP BY status
  `);
  
  return (rows as any[]).map(row => ({
    name: getStatusName(row.status),
    value: row.count,
    status: row.status
  }));
}

/**
 * 获取项目进度图表数据
 */
async function getProjectProgressChart() {
  const [rows] = await pool.execute(`
    SELECT online_status, COUNT(*) as count
    FROM projects
    GROUP BY online_status
  `);
  
  return (rows as any[]).map(row => ({
    name: getProjectStatusName(row.online_status),
    value: row.count,
    status: row.online_status
  }));
}

/**
 * 获取月度趋势图表数据
 */
async function getMonthlyTrendChart(period: string) {
  const days = period === '30d' ? 30 : 7;
  
  const [rows] = await pool.execute(`
    SELECT 
      DATE(created_at) as date,
      COUNT(*) as websites,
      (SELECT COUNT(*) FROM projects WHERE DATE(created_at) = DATE(w.created_at)) as projects
    FROM websites w
    WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
    GROUP BY DATE(created_at)
    ORDER BY date
  `, [days]);
  
  return rows;
}

/**
 * 获取平台分布图表数据
 */
async function getPlatformDistributionChart() {
  const [rows] = await pool.execute(`
    SELECT p.name, COUNT(w.id) as count
    FROM platforms p
    LEFT JOIN websites w ON p.id = w.platform_id
    GROUP BY p.id, p.name
    HAVING count > 0
    ORDER BY count DESC
  `);
  
  return rows;
}

// 辅助函数
function getStatusName(status: string): string {
  const statusMap: { [key: string]: string } = {
    'active': '正常',
    'inactive': '停用',
    'suspended': '暂停',
    'expired': '过期'
  };
  return statusMap[status] || status;
}

function getProjectStatusName(status: string): string {
  const statusMap: { [key: string]: string } = {
    'planning': '规划中',
    'development': '开发中',
    'testing': '测试中',
    'online': '已上线',
    'suspended': '已暂停'
  };
  return statusMap[status] || status;
}
