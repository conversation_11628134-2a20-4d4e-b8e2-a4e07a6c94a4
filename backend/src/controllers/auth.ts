import { Request, Response } from 'express';
import { query, queryOne, insert, update } from '@/config/database';
import { 
  hashPassword, 
  comparePassword, 
  generateToken, 
  generateRefreshToken,
  verifyRefreshToken,
  blacklistToken,
  extractTokenFromHeader
} from '@/utils/auth';
import { User, CreateUserRequest, ApiResponse } from '@/types';
import { logger } from '@/utils/logger';

// 用户登录
export const login = async (req: Request, res: Response): Promise<void> => {
  try {
    const { username, password } = req.body;

    // 查找用户
    const user = await queryOne<User>(
      'SELECT * FROM users WHERE (username = ? OR email = ?) AND status = "active"',
      [username, username]
    );

    if (!user) {
      res.status(401).json({
        success: false,
        message: '用户名或密码错误',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 验证密码
    const isPasswordValid = await comparePassword(password, user.password!);
    if (!isPasswordValid) {
      res.status(401).json({
        success: false,
        message: '用户名或密码错误',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 更新最后登录时间
    await update(
      'UPDATE users SET last_login = NOW() WHERE id = ?',
      [user.id]
    );

    // 生成令牌
    const token = generateToken(user);
    const refreshToken = generateRefreshToken(user);

    // 移除密码字段
    const { password: _, ...userWithoutPassword } = user;

    logger.info('用户登录成功', { userId: user.id, username: user.username });

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: userWithoutPassword,
        token,
        refreshToken
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('登录失败', error);
    res.status(500).json({
      success: false,
      message: '登录服务错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 用户注册（仅超级管理员可用）
export const register = async (req: Request, res: Response): Promise<void> => {
  try {
    const { username, email, password, realName, role, phone, department }: CreateUserRequest = req.body;

    // 检查用户名是否已存在
    const existingUser = await queryOne(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );

    if (existingUser) {
      res.status(409).json({
        success: false,
        message: '用户名或邮箱已存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 创建用户
    const userId = await insert(
      `INSERT INTO users (username, email, password, real_name, role, phone, department) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [username, email, hashedPassword, realName, role || 'user', phone, department]
    );

    logger.info('用户注册成功', { userId, username, createdBy: req.user?.id });

    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: { userId },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('用户注册失败', error);
    res.status(500).json({
      success: false,
      message: '用户注册服务错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 刷新令牌
export const refreshToken = async (req: Request, res: Response): Promise<void> => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      res.status(400).json({
        success: false,
        message: '刷新令牌不能为空',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 验证刷新令牌
    const payload = verifyRefreshToken(refreshToken);
    if (!payload) {
      res.status(401).json({
        success: false,
        message: '无效的刷新令牌',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 获取用户信息
    const user = await queryOne<User>(
      'SELECT * FROM users WHERE id = ? AND status = "active"',
      [payload.userId]
    );

    if (!user) {
      res.status(401).json({
        success: false,
        message: '用户不存在或已被禁用',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 生成新的令牌
    const newToken = generateToken(user);
    const newRefreshToken = generateRefreshToken(user);

    // 移除密码字段
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      message: '令牌刷新成功',
      data: {
        user: userWithoutPassword,
        token: newToken,
        refreshToken: newRefreshToken
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('令牌刷新失败', error);
    res.status(500).json({
      success: false,
      message: '令牌刷新服务错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 用户登出
export const logout = async (req: Request, res: Response): Promise<void> => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (token) {
      // 将令牌加入黑名单
      blacklistToken(token);
    }

    logger.info('用户登出', { userId: req.user?.id });

    res.json({
      success: true,
      message: '登出成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('登出失败', error);
    res.status(500).json({
      success: false,
      message: '登出服务错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 获取当前用户信息
export const getCurrentUser = async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: '用户未认证',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 获取最新的用户信息
    const user = await queryOne<User>(
      'SELECT id, username, email, real_name, role, status, avatar, phone, department, last_login, created_at, updated_at FROM users WHERE id = ?',
      [req.user.id]
    );

    if (!user) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.json({
      success: true,
      message: '获取用户信息成功',
      data: user,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('获取用户信息失败', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息服务错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 修改密码
export const changePassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!req.user) {
      res.status(401).json({
        success: false,
        message: '用户未认证',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 获取用户当前密码
    const user = await queryOne<User>(
      'SELECT password FROM users WHERE id = ?',
      [req.user.id]
    );

    if (!user) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 验证当前密码
    const isCurrentPasswordValid = await comparePassword(currentPassword, user.password!);
    if (!isCurrentPasswordValid) {
      res.status(400).json({
        success: false,
        message: '当前密码错误',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 加密新密码
    const hashedNewPassword = await hashPassword(newPassword);

    // 更新密码
    await update(
      'UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?',
      [hashedNewPassword, req.user.id]
    );

    logger.info('用户修改密码', { userId: req.user.id });

    res.json({
      success: true,
      message: '密码修改成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('修改密码失败', error);
    res.status(500).json({
      success: false,
      message: '修改密码服务错误',
      timestamp: new Date().toISOString()
    });
  }
};
