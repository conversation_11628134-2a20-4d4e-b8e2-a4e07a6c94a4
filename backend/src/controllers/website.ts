import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { pool } from '@/config/database';
import { getCache, setCache, deleteCache, CacheKeys } from '@/services/cache';
import { logger } from '@/utils/logger';
import { hashPassword } from '@/utils/auth';
import { ApiResponse, Website, WebsiteAccount } from '@/types';

// 获取网站列表
export const getWebsites = async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: '参数验证失败',
        errors: errors.array()
      });
      return;
    }

    const {
      page = 1,
      limit = 10,
      search = '',
      status = '',
      platform = '',
      server = ''
    } = req.query;

    // 生成缓存键
    const filters = { search, status, platform, server };
    const cacheKey = CacheKeys.websiteList(Number(page), Number(limit), filters);

    // 尝试从缓存获取
    const cachedResult = await getCache(cacheKey);
    if (cachedResult) {
      logger.debug('网站列表缓存命中');
      res.json({
        ...cachedResult,
        cached: true
      });
      return;
    }

    const offset = (Number(page) - 1) * Number(limit);
    
    // 构建查询条件
    let whereConditions = ['1=1'];
    const queryParams: any[] = [];

    if (search) {
      whereConditions.push('(w.domain LIKE ? OR w.site_url LIKE ? OR w.notes LIKE ?)');
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }

    if (status) {
      whereConditions.push('w.status = ?');
      queryParams.push(status);
    }

    if (platform) {
      whereConditions.push('p.name = ?');
      queryParams.push(platform);
    }

    if (server) {
      whereConditions.push('s.name = ?');
      queryParams.push(server);
    }

    const whereClause = whereConditions.join(' AND ');

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM websites w
      LEFT JOIN platforms p ON w.platform_id = p.platform_id
      LEFT JOIN servers s ON w.server_id = s.id
      WHERE ${whereClause}
    `;

    const [countResult] = await pool.execute(countQuery, queryParams);
    const total = (countResult as any[])[0].total;

    // 查询数据
    const dataQuery = `
      SELECT
        w.*,
        p.name as platform_name,
        p.description as platform_description,
        s.name as server_name,
        s.ip_address as server_ip,
        s.location as server_location,
        s.provider as server_provider,
        proj.project_name,
        proj.online_status as project_online_status,
        proj.online_date as project_online_date,
        proj.planned_online_date as project_planned_online_date,
        proj.customer_id,
        c.name as customer_name,
        c.company as customer_company
      FROM websites w
      LEFT JOIN platforms p ON w.platform_id = p.platform_id
      LEFT JOIN servers s ON w.server_id = s.id
      LEFT JOIN projects proj ON w.project_id = proj.id
      LEFT JOIN customers c ON proj.customer_id = c.id
      WHERE ${whereClause}
      ORDER BY w.created_at DESC
      LIMIT ? OFFSET ?
    `;

    queryParams.push(Number(limit), offset);
    const [rows] = await pool.execute(dataQuery, queryParams);

    // 格式化数据
    const websites = (rows as any[]).map(row => ({
      id: row.id,
      projectId: row.project_id,
      project: row.project_name ? {
        id: row.project_id,
        customerId: row.customer_id || 0,
        projectName: row.project_name,
        projectType: 'website',
        onlineStatus: row.project_online_status,
        onlineDate: row.project_online_date,
        plannedOnlineDate: row.project_planned_online_date,
        createdAt: new Date(),
        updatedAt: new Date(),
        customer: row.customer_name ? {
          id: row.customer_id || 0,
          name: row.customer_name,
          company: row.customer_company,
          status: 'confirmed' as const,
          createdAt: new Date(),
          updatedAt: new Date()
        } : undefined
      } : undefined,
      platformId: row.platform_id,
      platform: {
        id: row.platform_id,
        name: row.platform_name,
        description: row.platform_description,
        isActive: true,
        createdAt: new Date()
      },
      serverId: row.server_id,
      server: row.server_id ? {
        id: row.server_id,
        name: row.server_name,
        ipAddress: row.server_ip,
        location: row.server_location,
        provider: row.server_provider,
        status: 'active' as const,
        createdAt: new Date(),
        updatedAt: new Date()
      } : undefined,
      siteUrl: row.site_url,
      domain: row.domain,
      onlineDate: row.online_date,
      expireDate: row.expire_date,
      renewalFee: row.renewal_fee,
      accessStatusCode: row.access_status_code,
      sslExpireDate: row.ssl_expire_date,
      domainExpireDate: row.domain_expire_date,
      lastCheckTime: row.last_check_time,
      status: row.status,
      notes: row.notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));

    const response: ApiResponse<{
      websites: Website[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }> = {
      success: true,
      message: '获取网站列表成功',
      data: {
        websites,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          totalPages: Math.ceil(total / Number(limit))
        }
      },
      timestamp: new Date().toISOString()
    };

    // 缓存结果（缓存1分钟）
    await setCache(cacheKey, response, 60);

    res.json({
      ...response,
      cached: false
    });
    logger.info('获取网站列表成功', { 
      userId: (req as any).user?.userId,
      total,
      page,
      limit 
    });

  } catch (error) {
    logger.error('获取网站列表失败', error);
    res.status(500).json({
      success: false,
      message: '获取网站列表失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 获取单个网站详情
export const getWebsiteById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // 生成缓存键
    const cacheKey = CacheKeys.website(Number(id));

    // 尝试从缓存获取
    const cachedWebsite = await getCache(cacheKey);
    if (cachedWebsite) {
      logger.debug(`网站详情缓存命中: ${id}`);
      res.json({
        ...cachedWebsite,
        cached: true
      });
      return;
    }

    const query = `
      SELECT
        w.*,
        p.name as platform_name,
        p.description as platform_description,
        s.name as server_name,
        s.ip_address as server_ip,
        s.location as server_location,
        s.provider as server_provider,
        proj.project_name,
        proj.online_status as project_online_status,
        proj.online_date as project_online_date,
        proj.planned_online_date as project_planned_online_date,
        proj.customer_id,
        c.name as customer_name,
        c.company as customer_company
      FROM websites w
      LEFT JOIN platforms p ON w.platform_id = p.platform_id
      LEFT JOIN servers s ON w.server_id = s.id
      LEFT JOIN projects proj ON w.project_id = proj.id
      LEFT JOIN customers c ON proj.customer_id = c.id
      WHERE w.id = ?
    `;

    const [rows] = await pool.execute(query, [id]);
    const websites = rows as any[];

    if (websites.length === 0) {
      res.status(404).json({
        success: false,
        message: '网站不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const row = websites[0];
    const website = {
      id: row.id,
      projectId: row.project_id,
      project: row.project_name ? {
        id: row.project_id,
        customerId: row.customer_id || 0,
        projectName: row.project_name,
        projectType: 'website',
        onlineStatus: row.project_online_status,
        onlineDate: row.project_online_date,
        plannedOnlineDate: row.project_planned_online_date,
        createdAt: new Date(),
        updatedAt: new Date(),
        customer: row.customer_name ? {
          id: row.customer_id || 0,
          name: row.customer_name,
          company: row.customer_company,
          status: 'confirmed' as const,
          createdAt: new Date(),
          updatedAt: new Date()
        } : undefined
      } : undefined,
      platformId: row.platform_id,
      platform: {
        id: row.platform_id,
        name: row.platform_name,
        description: row.platform_description,
        isActive: true,
        createdAt: new Date()
      },
      serverId: row.server_id,
      server: row.server_id ? {
        id: row.server_id,
        name: row.server_name,
        ipAddress: row.server_ip,
        location: row.server_location,
        provider: row.server_provider,
        status: 'active' as const,
        createdAt: new Date(),
        updatedAt: new Date()
      } : undefined,
      siteUrl: row.site_url,
      domain: row.domain,
      onlineDate: row.online_date,
      expireDate: row.expire_date,
      renewalFee: row.renewal_fee,
      accessStatusCode: row.access_status_code,
      sslExpireDate: row.ssl_expire_date,
      domainExpireDate: row.domain_expire_date,
      lastCheckTime: row.last_check_time,
      status: row.status,
      notes: row.notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };

    const response: ApiResponse<Website> = {
      success: true,
      message: '获取网站详情成功',
      data: website,
      timestamp: new Date().toISOString()
    };

    // 缓存结果（缓存5分钟）
    await setCache(cacheKey, response, 300);

    res.json({
      ...response,
      cached: false
    });
    logger.info('获取网站详情成功', { 
      userId: (req as any).user?.userId,
      websiteId: id 
    });

  } catch (error) {
    logger.error('获取网站详情失败', error);
    res.status(500).json({
      success: false,
      message: '获取网站详情失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 创建网站
export const createWebsite = async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: '参数验证失败',
        errors: errors.array()
      });
      return;
    }

    const {
      projectId,
      platformId,
      serverId,
      siteName,
      siteUrl,
      domain,
      onlineDate,
      expireDate,
      projectAmount,
      renewalFee,
      sslExpireDate,
      domainExpireDate,
      hasOnboard,
      siteId,
      status = 'active',
      notes
    } = req.body;

    const query = `
      INSERT INTO websites (
        project_id, platform_id, server_id, site_name, site_url, domain,
        online_date, expire_date, project_amount, renewal_fee, ssl_expire_date,
        domain_expire_date, has_onboard, site_id, status, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      projectId || null,
      platformId,
      serverId || null,
      siteName,
      siteUrl,
      domain,
      onlineDate || null,
      expireDate || null,
      projectAmount || null,
      renewalFee || null,
      sslExpireDate || null,
      domainExpireDate || null,
      hasOnboard ? 1 : 0,
      siteId || null,
      status,
      notes || null
    ];

    const [result] = await pool.execute(query, values);
    const insertId = (result as any).insertId;

    // 清除相关缓存
    await deleteCache('websites:*');

    // 获取创建的网站信息
    const [rows] = await pool.execute(`
      SELECT
        w.*,
        p.name as platform_name,
        p.description as platform_description,
        s.name as server_name,
        s.ip_address as server_ip
      FROM websites w
      LEFT JOIN platforms p ON w.platform_id = p.platform_id
      LEFT JOIN servers s ON w.server_id = s.id
      WHERE w.id = ?
    `, [insertId]);

    const website = (rows as any[])[0];

    const response: ApiResponse<Website> = {
      success: true,
      message: '创建网站成功',
      data: {
        id: website.id,
        projectId: website.project_id,
        platformId: website.platform_id,
        platform: {
          id: website.platform_id,
          name: website.platform_name,
          description: website.platform_description,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        serverId: website.server_id,
        server: website.server_id ? {
          id: website.server_id,
          name: website.server_name,
          ipAddress: website.server_ip
        } : undefined,
        siteUrl: website.site_url,
        domain: website.domain,
        onlineDate: website.online_date,
        expireDate: website.expire_date,
        projectAmount: website.project_amount,
        renewalFee: website.renewal_fee,
        accessStatusCode: website.access_status_code,
        sslExpireDate: website.ssl_expire_date,
        domainExpireDate: website.domain_expire_date,
        lastCheckTime: website.last_check_time,
        status: website.status,
        notes: website.notes,
        createdAt: website.created_at,
        updatedAt: website.updated_at
      } as Website,
      timestamp: new Date().toISOString()
    };

    res.status(201).json(response);
    logger.info('创建网站成功', {
      userId: (req as any).user?.userId,
      websiteId: insertId,
      domain
    });

  } catch (error) {
    logger.error('创建网站失败', error);
    res.status(500).json({
      success: false,
      message: '创建网站失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 更新网站
export const updateWebsite = async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: '参数验证失败',
        errors: errors.array()
      });
      return;
    }

    const { id } = req.params;
    const {
      projectId,
      platformId,
      serverId,
      siteName,
      siteUrl,
      domain,
      onlineDate,
      expireDate,
      projectAmount,
      renewalFee,
      sslExpireDate,
      domainExpireDate,
      hasOnboard,
      siteId,
      status,
      notes
    } = req.body;

    // 检查网站是否存在
    const [existingRows] = await pool.execute('SELECT id FROM websites WHERE id = ?', [id]);
    if ((existingRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '网站不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const query = `
      UPDATE websites SET
        project_id = ?,
        platform_id = ?,
        server_id = ?,
        site_name = ?,
        site_url = ?,
        domain = ?,
        online_date = ?,
        expire_date = ?,
        project_amount = ?,
        renewal_fee = ?,
        ssl_expire_date = ?,
        domain_expire_date = ?,
        has_onboard = ?,
        site_id = ?,
        status = ?,
        notes = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const values = [
      projectId || null,
      platformId,
      serverId || null,
      siteName,
      siteUrl,
      domain,
      onlineDate || null,
      expireDate || null,
      projectAmount || null,
      renewalFee || null,
      sslExpireDate || null,
      domainExpireDate || null,
      hasOnboard ? 1 : 0,
      siteId || null,
      status,
      notes || null,
      id
    ];

    await pool.execute(query, values);

    // 清除相关缓存
    await deleteCache(CacheKeys.website(Number(id)));
    await deleteCache('websites:*');

    // 获取更新后的网站信息
    const [rows] = await pool.execute(`
      SELECT
        w.*,
        p.name as platform_name,
        p.description as platform_description,
        s.name as server_name,
        s.ip_address as server_ip,
        s.location as server_location,
        s.provider as server_provider,
        proj.project_name,
        proj.customer_id,
        c.name as customer_name,
        c.company as customer_company
      FROM websites w
      LEFT JOIN platforms p ON w.platform_id = p.platform_id
      LEFT JOIN servers s ON w.server_id = s.id
      LEFT JOIN projects proj ON w.project_id = proj.id
      LEFT JOIN customers c ON proj.customer_id = c.id
      WHERE w.id = ?
    `, [id]);

    const row = (rows as any[])[0];
    const website = {
      id: row.id,
      projectId: row.project_id,
      project: row.project_name ? {
        id: row.project_id,
        customerId: row.customer_id || 0,
        projectName: row.project_name,
        projectType: 'website',
        onlineStatus: row.project_online_status || 'planning',
        createdAt: new Date(),
        updatedAt: new Date(),
        customer: row.customer_name ? {
          id: row.customer_id || 0,
          name: row.customer_name,
          company: row.customer_company,
          status: 'confirmed' as const,
          createdAt: new Date(),
          updatedAt: new Date()
        } : undefined
      } : undefined,
      platformId: row.platform_id,
      platform: {
        id: row.platform_id,
        name: row.platform_name,
        description: row.platform_description,
        isActive: true,
        createdAt: new Date()
      },
      serverId: row.server_id,
      server: row.server_id ? {
        id: row.server_id,
        name: row.server_name,
        ipAddress: row.server_ip,
        location: row.server_location,
        provider: row.server_provider,
        status: 'active' as const,
        createdAt: new Date(),
        updatedAt: new Date()
      } : undefined,
      siteUrl: row.site_url,
      domain: row.domain,
      onlineDate: row.online_date,
      expireDate: row.expire_date,
      renewalFee: row.renewal_fee,
      accessStatusCode: row.access_status_code,
      sslExpireDate: row.ssl_expire_date,
      domainExpireDate: row.domain_expire_date,
      lastCheckTime: row.last_check_time,
      status: row.status,
      notes: row.notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };

    const response: ApiResponse<Website> = {
      success: true,
      message: '更新网站成功',
      data: website,
      timestamp: new Date().toISOString()
    };

    res.json(response);
    logger.info('更新网站成功', {
      userId: (req as any).user?.userId,
      websiteId: id,
      domain
    });

  } catch (error) {
    logger.error('更新网站失败', error);
    res.status(500).json({
      success: false,
      message: '更新网站失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 删除网站
export const deleteWebsite = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // 检查网站是否存在
    const [existingRows] = await pool.execute('SELECT id, domain FROM websites WHERE id = ?', [id]);
    if ((existingRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '网站不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const website = (existingRows as any[])[0];

    // 删除网站（注意：这里可能需要先删除相关的网站账号等关联数据）
    await pool.execute('DELETE FROM websites WHERE id = ?', [id]);

    // 清除相关缓存
    await deleteCache(CacheKeys.website(Number(id)));
    await deleteCache('websites:*');

    const response: ApiResponse = {
      success: true,
      message: '删除网站成功',
      timestamp: new Date().toISOString()
    };

    res.json(response);
    logger.info('删除网站成功', {
      userId: (req as any).user?.userId,
      websiteId: id,
      domain: website.domain
    });

  } catch (error) {
    logger.error('删除网站失败', error);
    res.status(500).json({
      success: false,
      message: '删除网站失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 获取平台列表（用于下拉选择）
export const getPlatforms = async (req: Request, res: Response): Promise<void> => {
  try {
    const cacheKey = 'platforms:active';

    // 尝试从缓存获取
    const cachedPlatforms = await getCache(cacheKey);
    if (cachedPlatforms) {
      logger.debug('平台列表缓存命中');
      res.json({
        ...cachedPlatforms,
        cached: true
      });
      return;
    }

    const [rows] = await pool.execute('SELECT * FROM platforms WHERE is_active = 1 ORDER BY name');

    const response: ApiResponse = {
      success: true,
      message: '获取平台列表成功',
      data: rows,
      timestamp: new Date().toISOString()
    };

    // 缓存结果（缓存1小时）
    await setCache(cacheKey, response, 3600);

    res.json({
      ...response,
      cached: false
    });

  } catch (error) {
    logger.error('获取平台列表失败', error);
    res.status(500).json({
      success: false,
      message: '获取平台列表失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 获取服务器列表（用于下拉选择）
export const getServers = async (req: Request, res: Response): Promise<void> => {
  try {
    const cacheKey = 'servers:active';

    // 尝试从缓存获取
    const cachedServers = await getCache(cacheKey);
    if (cachedServers) {
      logger.debug('服务器列表缓存命中');
      res.json({
        ...cachedServers,
        cached: true
      });
      return;
    }

    const [rows] = await pool.execute('SELECT id, name, ip_address, location FROM servers WHERE status = "active" ORDER BY name');

    const response: ApiResponse = {
      success: true,
      message: '获取服务器列表成功',
      data: rows,
      timestamp: new Date().toISOString()
    };

    // 缓存结果（缓存5分钟）
    await setCache(cacheKey, response, 300);

    res.json({
      ...response,
      cached: false
    });

  } catch (error) {
    logger.error('获取服务器列表失败', error);
    res.status(500).json({
      success: false,
      message: '获取服务器列表失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 批量操作网站
 */
export const batchUpdateWebsites = async (req: Request, res: Response): Promise<void> => {
  try {
    const { action, websiteIds, data } = req.body;

    if (!Array.isArray(websiteIds) || websiteIds.length === 0) {
      res.status(400).json({
        success: false,
        message: '请选择要操作的网站',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const connection = await pool.getConnection();
    await connection.beginTransaction();

    try {
      let affectedRows = 0;
      const placeholders = websiteIds.map(() => '?').join(',');

      switch (action) {
        case 'updateStatus':
          const [statusResult] = await connection.execute(
            `UPDATE websites SET status = ?, updated_at = NOW() WHERE id IN (${placeholders})`,
            [data.status, ...websiteIds]
          );
          affectedRows = (statusResult as any).affectedRows;
          break;

        case 'updateServer':
          const [serverResult] = await connection.execute(
            `UPDATE websites SET server_id = ?, updated_at = NOW() WHERE id IN (${placeholders})`,
            [data.serverId, ...websiteIds]
          );
          affectedRows = (serverResult as any).affectedRows;
          break;

        case 'updateExpireDate':
          const [expireResult] = await connection.execute(
            `UPDATE websites SET expire_date = ?, updated_at = NOW() WHERE id IN (${placeholders})`,
            [data.expireDate, ...websiteIds]
          );
          affectedRows = (expireResult as any).affectedRows;
          break;

        case 'delete':
          const [deleteResult] = await connection.execute(
            `DELETE FROM websites WHERE id IN (${placeholders})`,
            websiteIds
          );
          affectedRows = (deleteResult as any).affectedRows;
          break;

        default:
          throw new Error('不支持的操作类型');
      }

      await connection.commit();

      // 清除相关缓存
      await deleteCache('websites:*');

      logger.info('批量操作网站成功', {
        action,
        websiteIds,
        affectedRows,
        userId: (req as any).user?.userId
      });

      res.json({
        success: true,
        message: `成功${getActionName(action)} ${affectedRows} 个网站`,
        data: { affectedRows },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    logger.error('批量操作网站失败:', error);
    res.status(500).json({
      success: false,
      message: '批量操作失败',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 检查网站可访问性
 */
export const checkWebsiteAccess = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // 获取网站信息
    const [rows] = await pool.execute('SELECT site_url FROM websites WHERE id = ?', [id]);
    if ((rows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '网站不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const website = (rows as any[])[0];
    const startTime = Date.now();

    try {
      // 真实网站访问检查
      const axios = require('axios');
      const response = await axios.get(website.site_url, {
        timeout: 10000,
        validateStatus: () => true // 接受所有状态码
      });

      const statusCode = response.status;
      const responseTime = Date.now() - startTime;
      const isAccessible = statusCode >= 200 && statusCode < 400;

      // 更新网站访问状态
      await pool.execute(
        'UPDATE websites SET access_status_code = ?, last_check_time = NOW() WHERE id = ?',
        [statusCode, id]
      );

      // 清除相关缓存
      await deleteCache(`website:${id}`);
      await deleteCache('websites:*');

      res.json({
        success: true,
        message: '网站可访问性检查完成',
        data: {
          accessible: isAccessible,
          statusCode,
          responseTime: Math.round(responseTime),
          lastCheckTime: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      // 网站无法访问
      await pool.execute(
        'UPDATE websites SET access_status_code = ?, last_check_time = NOW() WHERE id = ?',
        [0, id]
      );

      res.json({
        success: true,
        message: '网站可访问性检查完成',
        data: {
          accessible: false,
          statusCode: 0,
          responseTime: Date.now() - startTime,
          lastCheckTime: new Date().toISOString(),
          error: 'Connection failed'
        },
        timestamp: new Date().toISOString()
      });
    }

  } catch (error) {
    logger.error('检查网站可访问性失败:', error);
    res.status(500).json({
      success: false,
      message: '检查网站可访问性失败',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 获取网站统计信息
 */
export const getWebsiteStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const cacheKey = 'website:stats';

    // 尝试从缓存获取
    const cachedStats = await getCache(cacheKey);
    if (cachedStats) {
      logger.debug('网站统计缓存命中');
      res.json({
        ...cachedStats,
        cached: true
      });
      return;
    }

    // 并行查询各种统计数据
    const [
      totalResult,
      statusResult,
      platformResult,
      serverResult,
      expiringResult
    ] = await Promise.all([
      pool.execute('SELECT COUNT(*) as total FROM websites'),
      pool.execute(`
        SELECT status, COUNT(*) as count
        FROM websites
        GROUP BY status
      `),
      pool.execute(`
        SELECT p.name, COUNT(w.id) as count
        FROM platforms p
        LEFT JOIN websites w ON p.platform_id = w.platform_id
        GROUP BY p.id, p.name
        ORDER BY count DESC
      `),
      pool.execute(`
        SELECT s.name, COUNT(w.id) as count
        FROM servers s
        LEFT JOIN websites w ON s.id = w.server_id
        GROUP BY s.id, s.name
        ORDER BY count DESC
      `),
      pool.execute(`
        SELECT COUNT(*) as count
        FROM websites
        WHERE expire_date <= DATE_ADD(NOW(), INTERVAL 30 DAY)
        AND expire_date > NOW()
      `)
    ]);

    const stats = {
      total: (totalResult[0] as any[])[0].total,
      byStatus: statusResult[0] as any[],
      byPlatform: platformResult[0] as any[],
      byServer: serverResult[0] as any[],
      expiringSoon: (expiringResult[0] as any[])[0].count
    };

    const response = {
      success: true,
      message: '获取网站统计成功',
      data: stats,
      timestamp: new Date().toISOString()
    };

    // 缓存结果（缓存5分钟）
    await setCache(cacheKey, response, 300);

    res.json({
      ...response,
      cached: false
    });

  } catch (error) {
    logger.error('获取网站统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取网站统计失败',
      timestamp: new Date().toISOString()
    });
  }
};

// 辅助函数
function getActionName(action: string): string {
  const actionMap: { [key: string]: string } = {
    'updateStatus': '更新状态',
    'updateServer': '更新服务器',
    'updateExpireDate': '更新到期时间',
    'delete': '删除'
  };
  return actionMap[action] || action;
}

// ==================== 网站账号管理 API ====================

// 获取网站账号列表
export const getWebsiteAccounts = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // 检查网站是否存在
    const [websiteRows] = await pool.execute('SELECT id, domain FROM websites WHERE id = ?', [id]);
    if ((websiteRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '网站不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 查询网站账号
    const query = `
      SELECT
        id, website_id, account_type, username, password, email, role,
        login_url, is_active, notes, created_at, updated_at
      FROM website_accounts
      WHERE website_id = ?
      ORDER BY created_at DESC
    `;

    const [rows] = await pool.execute(query, [id]);
    const accounts = (rows as any[]).map(row => ({
      id: row.id,
      websiteId: row.website_id,
      accountType: row.account_type,
      username: row.username,
      password: row.password, // 注意：这里返回加密的密码，前端需要解密显示
      email: row.email,
      role: row.role,
      loginUrl: row.login_url,
      isActive: Boolean(row.is_active),
      notes: row.notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));

    const response: ApiResponse<WebsiteAccount[]> = {
      success: true,
      message: '获取网站账号列表成功',
      data: accounts,
      timestamp: new Date().toISOString()
    };

    res.json(response);
    logger.info('获取网站账号列表成功', {
      userId: (req as any).user?.userId,
      websiteId: id,
      accountCount: accounts.length
    });

  } catch (error) {
    logger.error('获取网站账号列表失败', error);
    res.status(500).json({
      success: false,
      message: '获取网站账号列表失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 创建网站账号
export const createWebsiteAccount = async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: '参数验证失败',
        errors: errors.array()
      });
      return;
    }

    const { id } = req.params;
    const {
      accountType,
      username,
      password,
      email,
      role,
      loginUrl,
      isActive = true,
      notes
    } = req.body;

    // 检查网站是否存在
    const [websiteRows] = await pool.execute('SELECT id, domain FROM websites WHERE id = ?', [id]);
    if ((websiteRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '网站不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 创建账号
    const query = `
      INSERT INTO website_accounts (
        website_id, account_type, username, password, email, role,
        login_url, is_active, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      id,
      accountType,
      username,
      hashedPassword,
      email || null,
      role || null,
      loginUrl || null,
      isActive ? 1 : 0,
      notes || null
    ];

    const [result] = await pool.execute(query, values);
    const insertId = (result as any).insertId;

    // 获取创建的账号信息
    const [rows] = await pool.execute(
      'SELECT * FROM website_accounts WHERE id = ?',
      [insertId]
    );

    const account = (rows as any[])[0];
    const responseData = {
      id: account.id,
      websiteId: account.website_id,
      accountType: account.account_type,
      username: account.username,
      password: account.password, // 返回加密的密码
      email: account.email,
      role: account.role,
      loginUrl: account.login_url,
      isActive: Boolean(account.is_active),
      notes: account.notes,
      createdAt: account.created_at,
      updatedAt: account.updated_at
    };

    const response: ApiResponse<WebsiteAccount> = {
      success: true,
      message: '创建网站账号成功',
      data: responseData,
      timestamp: new Date().toISOString()
    };

    res.status(201).json(response);
    logger.info('创建网站账号成功', {
      userId: (req as any).user?.userId,
      websiteId: id,
      accountId: insertId,
      accountType,
      username
    });

  } catch (error) {
    logger.error('创建网站账号失败', error);
    res.status(500).json({
      success: false,
      message: '创建网站账号失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 更新网站账号
export const updateWebsiteAccount = async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: '参数验证失败',
        errors: errors.array()
      });
      return;
    }

    const { id, accountId } = req.params;
    const {
      accountType,
      username,
      password,
      email,
      role,
      loginUrl,
      isActive,
      notes
    } = req.body;

    // 检查网站是否存在
    const [websiteRows] = await pool.execute('SELECT id FROM websites WHERE id = ?', [id]);
    if ((websiteRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '网站不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 检查账号是否存在
    const [accountRows] = await pool.execute(
      'SELECT id FROM website_accounts WHERE id = ? AND website_id = ?',
      [accountId, id]
    );
    if ((accountRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '网站账号不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 构建更新字段
    const updateFields = [];
    const updateValues = [];

    if (accountType !== undefined) {
      updateFields.push('account_type = ?');
      updateValues.push(accountType);
    }
    if (username !== undefined) {
      updateFields.push('username = ?');
      updateValues.push(username);
    }
    if (password !== undefined) {
      // 如果提供了新密码，需要加密
      const hashedPassword = await hashPassword(password);
      updateFields.push('password = ?');
      updateValues.push(hashedPassword);
    }
    if (email !== undefined) {
      updateFields.push('email = ?');
      updateValues.push(email);
    }
    if (role !== undefined) {
      updateFields.push('role = ?');
      updateValues.push(role);
    }
    if (loginUrl !== undefined) {
      updateFields.push('login_url = ?');
      updateValues.push(loginUrl);
    }
    if (isActive !== undefined) {
      updateFields.push('is_active = ?');
      updateValues.push(isActive ? 1 : 0);
    }
    if (notes !== undefined) {
      updateFields.push('notes = ?');
      updateValues.push(notes);
    }

    if (updateFields.length === 0) {
      res.status(400).json({
        success: false,
        message: '没有提供要更新的字段',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 添加更新时间
    updateFields.push('updated_at = NOW()');
    updateValues.push(accountId);

    const query = `UPDATE website_accounts SET ${updateFields.join(', ')} WHERE id = ?`;
    await pool.execute(query, updateValues);

    // 获取更新后的账号信息
    const [rows] = await pool.execute(
      'SELECT * FROM website_accounts WHERE id = ?',
      [accountId]
    );

    const account = (rows as any[])[0];
    const responseData = {
      id: account.id,
      websiteId: account.website_id,
      accountType: account.account_type,
      username: account.username,
      password: account.password, // 返回加密的密码
      email: account.email,
      role: account.role,
      loginUrl: account.login_url,
      isActive: Boolean(account.is_active),
      notes: account.notes,
      createdAt: account.created_at,
      updatedAt: account.updated_at
    };

    const response: ApiResponse<WebsiteAccount> = {
      success: true,
      message: '更新网站账号成功',
      data: responseData,
      timestamp: new Date().toISOString()
    };

    res.json(response);
    logger.info('更新网站账号成功', {
      userId: (req as any).user?.userId,
      websiteId: id,
      accountId,
      username
    });

  } catch (error) {
    logger.error('更新网站账号失败', error);
    res.status(500).json({
      success: false,
      message: '更新网站账号失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 删除网站账号
export const deleteWebsiteAccount = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id, accountId } = req.params;

    // 检查网站是否存在
    const [websiteRows] = await pool.execute('SELECT id FROM websites WHERE id = ?', [id]);
    if ((websiteRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '网站不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 检查账号是否存在
    const [accountRows] = await pool.execute(
      'SELECT id, username FROM website_accounts WHERE id = ? AND website_id = ?',
      [accountId, id]
    );
    if ((accountRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '网站账号不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    const account = (accountRows as any[])[0];

    // 删除账号
    await pool.execute('DELETE FROM website_accounts WHERE id = ?', [accountId]);

    const response: ApiResponse = {
      success: true,
      message: '删除网站账号成功',
      timestamp: new Date().toISOString()
    };

    res.json(response);
    logger.info('删除网站账号成功', {
      userId: (req as any).user?.userId,
      websiteId: id,
      accountId,
      username: account.username
    });

  } catch (error) {
    logger.error('删除网站账号失败', error);
    res.status(500).json({
      success: false,
      message: '删除网站账号失败',
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString()
    });
  }
};
