import { Request, Response } from 'express';
import { query, queryOne, insert, update, remove, paginate } from '@/config/database';
import { hashPassword } from '@/utils/auth';
import { User, CreateUserRequest, UpdateUserRequest, QueryParams } from '@/types';
import { logger } from '@/utils/logger';

// 获取用户列表
export const getUsers = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      page = 1,
      pageSize = 10,
      search,
      role,
      status,
      sortBy = 'created_at',
      sortOrder = 'desc'
    }: QueryParams = req.query;

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    // 搜索条件
    if (search) {
      whereClause += ' AND (username LIKE ? OR email LIKE ? OR real_name LIKE ?)';
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }

    // 角色筛选
    if (role) {
      whereClause += ' AND role = ?';
      params.push(role);
    }

    // 状态筛选
    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }

    // 排序
    const allowedSortFields = ['id', 'username', 'email', 'real_name', 'role', 'status', 'created_at', 'updated_at'];
    const sortField = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder === 'asc' ? 'ASC' : 'DESC';

    const sql = `
      SELECT id, username, email, real_name, role, status, avatar, phone, department, last_login, created_at, updated_at
      FROM users 
      ${whereClause}
      ORDER BY ${sortField} ${order}
    `;

    const countSql = `SELECT COUNT(*) as total FROM users ${whereClause}`;

    const result = await paginate<User>(
      sql,
      countSql,
      params,
      Number(page),
      Number(pageSize)
    );

    res.json({
      success: true,
      message: '获取用户列表成功',
      data: result.data,
      pagination: {
        total: result.total,
        page: result.page,
        pageSize: result.pageSize,
        totalPages: result.totalPages
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('获取用户列表失败', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表服务错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 获取单个用户
export const getUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const user = await queryOne<User>(
      'SELECT id, username, email, real_name, role, status, avatar, phone, department, last_login, created_at, updated_at FROM users WHERE id = ?',
      [id]
    );

    if (!user) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    res.json({
      success: true,
      message: '获取用户信息成功',
      data: user,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('获取用户信息失败', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息服务错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 创建用户
export const createUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const { username, email, password, realName, role, phone, department }: CreateUserRequest = req.body;

    // 检查用户名和邮箱是否已存在
    const existingUser = await queryOne(
      'SELECT id FROM users WHERE username = ? OR email = ?',
      [username, email]
    );

    if (existingUser) {
      res.status(409).json({
        success: false,
        message: '用户名或邮箱已存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 创建用户
    const userId = await insert(
      `INSERT INTO users (username, email, password, real_name, role, phone, department) 
       VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [username, email, hashedPassword, realName, role || 'user', phone, department]
    );

    logger.info('创建用户成功', { userId, username, createdBy: req.user?.id });

    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: { id: userId },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('创建用户失败', error);
    res.status(500).json({
      success: false,
      message: '创建用户服务错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 更新用户
export const updateUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { realName, email, phone, department, status, role }: UpdateUserRequest = req.body;

    // 检查用户是否存在
    const existingUser = await queryOne('SELECT id FROM users WHERE id = ?', [id]);
    if (!existingUser) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 如果更新邮箱，检查是否已被其他用户使用
    if (email) {
      const emailExists = await queryOne(
        'SELECT id FROM users WHERE email = ? AND id != ?',
        [email, id]
      );
      if (emailExists) {
        res.status(409).json({
          success: false,
          message: '邮箱已被其他用户使用',
          timestamp: new Date().toISOString()
        });
        return;
      }
    }

    // 构建更新字段
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (realName !== undefined) {
      updateFields.push('real_name = ?');
      updateValues.push(realName);
    }
    if (email !== undefined) {
      updateFields.push('email = ?');
      updateValues.push(email);
    }
    if (phone !== undefined) {
      updateFields.push('phone = ?');
      updateValues.push(phone);
    }
    if (department !== undefined) {
      updateFields.push('department = ?');
      updateValues.push(department);
    }
    if (status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }
    if (role !== undefined) {
      updateFields.push('role = ?');
      updateValues.push(role);
    }

    if (updateFields.length === 0) {
      res.status(400).json({
        success: false,
        message: '没有提供要更新的字段',
        timestamp: new Date().toISOString()
      });
      return;
    }

    updateFields.push('updated_at = NOW()');
    updateValues.push(id);

    const sql = `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`;
    const affectedRows = await update(sql, updateValues);

    if (affectedRows === 0) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    logger.info('更新用户成功', { userId: id, updatedBy: req.user?.id });

    res.json({
      success: true,
      message: '用户更新成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('更新用户失败', error);
    res.status(500).json({
      success: false,
      message: '更新用户服务错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 删除用户
export const deleteUser = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    // 检查是否为当前用户
    if (req.user?.id === parseInt(id)) {
      res.status(400).json({
        success: false,
        message: '不能删除自己的账户',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 检查用户是否存在
    const user = await queryOne('SELECT username FROM users WHERE id = ?', [id]);
    if (!user) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 删除用户（软删除，更改状态为inactive）
    const affectedRows = await update(
      'UPDATE users SET status = "inactive", updated_at = NOW() WHERE id = ?',
      [id]
    );

    if (affectedRows === 0) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    logger.info('删除用户成功', { userId: id, username: user.username, deletedBy: req.user?.id });

    res.json({
      success: true,
      message: '用户删除成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('删除用户失败', error);
    res.status(500).json({
      success: false,
      message: '删除用户服务错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 重置用户密码
export const resetUserPassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { newPassword } = req.body;

    // 检查用户是否存在
    const user = await queryOne('SELECT username FROM users WHERE id = ?', [id]);
    if (!user) {
      res.status(404).json({
        success: false,
        message: '用户不存在',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 加密新密码
    const hashedPassword = await hashPassword(newPassword);

    // 更新密码
    await update(
      'UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?',
      [hashedPassword, id]
    );

    logger.info('重置用户密码成功', { userId: id, username: user.username, resetBy: req.user?.id });

    res.json({
      success: true,
      message: '密码重置成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('重置用户密码失败', error);
    res.status(500).json({
      success: false,
      message: '重置密码服务错误',
      timestamp: new Date().toISOString()
    });
  }
};
