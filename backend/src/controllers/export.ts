import { Request, Response } from 'express';
import { query, paginate } from '@/config/database';
import { exportTable, sendFileDownload, ExportFormat } from '@/utils/export';
import { logger } from '@/utils/logger';
import { User, Customer, Project, Website, Server, Domain } from '@/types';

// 导出用户列表
export const exportUsers = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      search,
      role,
      status,
      format = 'excel'
    } = req.query;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (search) {
      whereClause += ' AND (username LIKE ? OR email LIKE ? OR real_name LIKE ?)';
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }

    if (role) {
      whereClause += ' AND role = ?';
      params.push(role);
    }

    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }

    const sql = `
      SELECT id, username, email, real_name, role, status, phone, department, last_login, created_at, updated_at
      FROM users 
      ${whereClause}
      ORDER BY created_at DESC
    `;

    const users = await query<User>(sql, params);

    // 定义导出列
    const headers = [
      { key: 'id', label: 'ID', width: 10 },
      { key: 'username', label: '用户名', width: 15 },
      { key: 'email', label: '邮箱', width: 25 },
      { key: 'real_name', label: '真实姓名', width: 15 },
      { key: 'role', label: '角色', width: 12 },
      { key: 'status', label: '状态', width: 10 },
      { key: 'phone', label: '手机号', width: 15 },
      { key: 'department', label: '部门', width: 15 },
      { key: 'last_login', label: '最后登录', width: 20 },
      { key: 'created_at', label: '创建时间', width: 20 },
      { key: 'updated_at', label: '更新时间', width: 20 }
    ];

    // 处理数据
    const processedData = users.map(user => ({
      ...user,
      role: getRoleText(user.role),
      status: getStatusText(user.status),
      last_login: user.lastLogin ? new Date(user.lastLogin).toLocaleString('zh-CN') : '从未登录',
      created_at: new Date(user.createdAt).toLocaleString('zh-CN'),
      updated_at: new Date(user.updatedAt).toLocaleString('zh-CN')
    }));

    const filePath = await exportTable({
      filename: '用户列表',
      sheetName: '用户数据',
      headers,
      data: processedData,
      format: format as ExportFormat
    });

    sendFileDownload(res, filePath);
    logger.info('用户列表导出成功', { userId: req.user?.id, recordCount: users.length, format });

  } catch (error) {
    logger.error('导出用户列表失败', error);
    res.status(500).json({
      success: false,
      message: '导出失败',
      timestamp: new Date().toISOString()
    });
  }
};

// 导出客户列表
export const exportCustomers = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      search,
      status,
      industry,
      format = 'excel'
    } = req.query;

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (search) {
      whereClause += ' AND (name LIKE ? OR company LIKE ? OR phone LIKE ? OR email LIKE ?)';
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern, searchPattern);
    }

    if (status) {
      whereClause += ' AND status = ?';
      params.push(status);
    }

    if (industry) {
      whereClause += ' AND industry = ?';
      params.push(industry);
    }

    const sql = `
      SELECT c.*, u.real_name as sales_person_name
      FROM customers c
      LEFT JOIN users u ON c.sales_person_id = u.id
      ${whereClause}
      ORDER BY c.created_at DESC
    `;

    const customers = await query<Customer>(sql, params);

    const headers = [
      { key: 'id', label: 'ID', width: 10 },
      { key: 'name', label: '客户姓名', width: 15 },
      { key: 'company', label: '公司名称', width: 25 },
      { key: 'contact_person', label: '联系人', width: 15 },
      { key: 'phone', label: '手机号', width: 15 },
      { key: 'email', label: '邮箱', width: 25 },
      { key: 'address', label: '地址', width: 30 },
      { key: 'industry', label: '行业', width: 15 },
      { key: 'source', label: '来源', width: 15 },
      { key: 'status', label: '状态', width: 12 },
      { key: 'sales_person_name', label: '销售负责人', width: 15 },
      { key: 'notes', label: '备注', width: 30 },
      { key: 'created_at', label: '创建时间', width: 20 }
    ];

    const processedData = customers.map(customer => ({
      ...customer,
      status: getCustomerStatusText(customer.status),
      created_at: new Date(customer.createdAt).toLocaleString('zh-CN')
    }));

    const filePath = await exportTable({
      filename: '客户列表',
      sheetName: '客户数据',
      headers,
      data: processedData,
      format: format as ExportFormat
    });

    sendFileDownload(res, filePath);
    logger.info('客户列表导出成功', { userId: req.user?.id, recordCount: customers.length, format });

  } catch (error) {
    logger.error('导出客户列表失败', error);
    res.status(500).json({
      success: false,
      message: '导出失败',
      timestamp: new Date().toISOString()
    });
  }
};

// 导出项目列表
export const exportProjects = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      search,
      onlineStatus,
      projectType,
      format = 'excel'
    } = req.query;

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (search) {
      whereClause += ' AND (p.project_name LIKE ? OR p.contract_number LIKE ? OR c.company LIKE ?)';
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern, searchPattern);
    }

    if (onlineStatus) {
      whereClause += ' AND p.online_status = ?';
      params.push(onlineStatus);
    }

    if (projectType) {
      whereClause += ' AND p.project_type = ?';
      params.push(projectType);
    }

    const sql = `
      SELECT p.*, c.name as customer_name, c.company as customer_company,
             pm.real_name as project_manager_name, sp.real_name as sales_person_name
      FROM projects p
      LEFT JOIN customers c ON p.customer_id = c.id
      LEFT JOIN users pm ON p.project_manager_id = pm.id
      LEFT JOIN users sp ON p.sales_person_id = sp.id
      ${whereClause}
      ORDER BY p.created_at DESC
    `;

    const projects = await query<Project>(sql, params);

    const headers = [
      { key: 'id', label: 'ID', width: 10 },
      { key: 'project_name', label: '项目名称', width: 25 },
      { key: 'customer_company', label: '客户公司', width: 25 },
      { key: 'project_type', label: '项目类型', width: 15 },
      { key: 'service_fee', label: '服务费用', width: 15 },
      { key: 'contract_number', label: '合同编号', width: 20 },
      { key: 'contract_signed_date', label: '签订日期', width: 15 },
      { key: 'online_status', label: '项目状态', width: 12 },
      { key: 'planned_online_date', label: '计划上线', width: 15 },
      { key: 'online_date', label: '实际上线', width: 15 },
      { key: 'project_manager_name', label: '项目经理', width: 15 },
      { key: 'sales_person_name', label: '销售负责人', width: 15 },
      { key: 'preview_link', label: '预览链接', width: 30 },
      { key: 'notes', label: '备注', width: 30 },
      { key: 'created_at', label: '创建时间', width: 20 }
    ];

    const processedData = projects.map(project => ({
      ...project,
      service_fee: project.serviceFee ? `¥${project.serviceFee.toLocaleString()}` : '',
      online_status: getProjectStatusText(project.onlineStatus),
      created_at: new Date(project.createdAt).toLocaleString('zh-CN')
    }));

    const filePath = await exportTable({
      filename: '项目列表',
      sheetName: '项目数据',
      headers,
      data: processedData,
      format: format as ExportFormat
    });

    sendFileDownload(res, filePath);
    logger.info('项目列表导出成功', { userId: req.user?.id, recordCount: projects.length, format });

  } catch (error) {
    logger.error('导出项目列表失败', error);
    res.status(500).json({
      success: false,
      message: '导出失败',
      timestamp: new Date().toISOString()
    });
  }
};

// 导出网站列表
export const exportWebsites = async (req: Request, res: Response): Promise<void> => {
  try {
    const {
      search,
      status,
      platform,
      format = 'excel'
    } = req.query;

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (search) {
      whereClause += ' AND (w.domain LIKE ? OR w.site_url LIKE ?)';
      const searchPattern = `%${search}%`;
      params.push(searchPattern, searchPattern);
    }

    if (status) {
      whereClause += ' AND w.status = ?';
      params.push(status);
    }

    if (platform) {
      whereClause += ' AND p.name = ?';
      params.push(platform);
    }

    const sql = `
      SELECT w.*, p.name as platform_name, s.name as server_name, s.ip_address,
             pr.project_name, c.company as customer_company
      FROM websites w
      LEFT JOIN platforms p ON w.platform_id = p.platform_id
      LEFT JOIN servers s ON w.server_id = s.id
      LEFT JOIN projects pr ON w.project_id = pr.id
      LEFT JOIN customers c ON pr.customer_id = c.id
      ${whereClause}
      ORDER BY w.created_at DESC
    `;

    const websites = await query<Website>(sql, params);

    const headers = [
      { key: 'id', label: 'ID', width: 10 },
      { key: 'domain', label: '域名', width: 25 },
      { key: 'site_url', label: '网站URL', width: 30 },
      { key: 'platform_name', label: '平台类型', width: 15 },
      { key: 'server_name', label: '服务器', width: 15 },
      { key: 'ip_address', label: 'IP地址', width: 15 },
      { key: 'customer_company', label: '客户公司', width: 25 },
      { key: 'project_name', label: '关联项目', width: 25 },
      { key: 'status', label: '网站状态', width: 12 },
      { key: 'access_status_code', label: '访问状态', width: 12 },
      { key: 'online_date', label: '上线日期', width: 15 },
      { key: 'expire_date', label: '到期日期', width: 15 },
      { key: 'ssl_expire_date', label: 'SSL到期', width: 15 },
      { key: 'domain_expire_date', label: '域名到期', width: 15 },
      { key: 'renewal_fee', label: '续费金额', width: 15 },
      { key: 'last_check_time', label: '最后检查', width: 20 },
      { key: 'notes', label: '备注', width: 30 },
      { key: 'created_at', label: '创建时间', width: 20 }
    ];

    const processedData = websites.map(website => ({
      ...website,
      status: getWebsiteStatusText(website.status),
      access_status_code: website.accessStatusCode || '未检测',
      renewal_fee: website.renewalFee ? `¥${website.renewalFee.toLocaleString()}` : '',
      last_check_time: website.lastCheckTime ? new Date(website.lastCheckTime).toLocaleString('zh-CN') : '未检查',
      created_at: new Date(website.createdAt).toLocaleString('zh-CN')
    }));

    const filePath = await exportTable({
      filename: '网站列表',
      sheetName: '网站数据',
      headers,
      data: processedData,
      format: format as ExportFormat
    });

    sendFileDownload(res, filePath);
    logger.info('网站列表导出成功', { userId: req.user?.id, recordCount: websites.length, format });

  } catch (error) {
    logger.error('导出网站列表失败', error);
    res.status(500).json({
      success: false,
      message: '导出失败',
      timestamp: new Date().toISOString()
    });
  }
};

// 辅助函数：获取角色文本
const getRoleText = (role: string): string => {
  const roleMap: { [key: string]: string } = {
    'super_admin': '超级管理员',
    'admin': '管理员',
    'user': '普通用户'
  };
  return roleMap[role] || role;
};

// 辅助函数：获取状态文本
const getStatusText = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    'active': '正常',
    'inactive': '禁用',
    'suspended': '暂停'
  };
  return statusMap[status] || status;
};

// 辅助函数：获取客户状态文本
const getCustomerStatusText = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    'potential': '潜在客户',
    'confirmed': '已确认',
    'lost': '已流失'
  };
  return statusMap[status] || status;
};

// 辅助函数：获取项目状态文本
const getProjectStatusText = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    'planning': '规划中',
    'development': '开发中',
    'testing': '测试中',
    'online': '已上线',
    'suspended': '已暂停'
  };
  return statusMap[status] || status;
};

// 辅助函数：获取网站状态文本
const getWebsiteStatusText = (status: string): string => {
  const statusMap: { [key: string]: string } = {
    'active': '正常',
    'inactive': '停用',
    'suspended': '暂停',
    'expired': '过期'
  };
  return statusMap[status] || status;
};
