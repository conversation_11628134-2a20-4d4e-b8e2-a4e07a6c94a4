import { Request, Response } from 'express';
import { pool } from '@/config/database';
import { getCache, setCache, deleteCache, CacheKeys } from '@/services/cache';
import { logger } from '@/utils/logger';

/**
 * 更新项目上线状态
 */
export const updateProjectOnlineStatus = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { onlineStatus, onlineDate } = req.body;

    // 开始事务
    const connection = await pool.getConnection();
    await connection.beginTransaction();

    try {
      // 更新项目状态
      await connection.execute(
        'UPDATE projects SET online_status = ?, online_date = ?, updated_at = NOW() WHERE id = ?',
        [onlineStatus, onlineDate, id]
      );

      // 如果项目状态变为已上线，同时更新关联的网站上线时间
      if (onlineStatus === 'online' && onlineDate) {
        await connection.execute(`
          UPDATE websites 
          SET online_date = ?, updated_at = NOW() 
          WHERE project_id = ? AND online_date IS NULL
        `, [onlineDate, id]);

        logger.info('项目上线，自动更新关联网站上线时间', {
          projectId: id,
          onlineDate,
          userId: (req as any).user?.userId
        });
      }

      await connection.commit();

      // 清除相关缓存
      await deleteCache(`project:${id}`);
      await deleteCache('projects:*');
      await deleteCache('websites:*');

      res.json({
        success: true,
        message: '项目状态更新成功',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    logger.error('更新项目状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新项目状态失败',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 获取项目关联的网站列表
 */
export const getProjectWebsites = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const cacheKey = `project:${id}:websites`;

    // 尝试从缓存获取
    const cachedWebsites = await getCache(cacheKey);
    if (cachedWebsites) {
      logger.debug(`项目网站列表缓存命中: ${id}`);
      res.json({
        ...cachedWebsites,
        cached: true
      });
      return;
    }

    const query = `
      SELECT
        w.*,
        p.name as platform_name,
        s.name as server_name,
        s.ip_address as server_ip
      FROM websites w
      LEFT JOIN platforms p ON w.platform_id = p.platform_id
      LEFT JOIN servers s ON w.server_id = s.id
      WHERE w.project_id = ?
      ORDER BY w.created_at DESC
    `;

    const [rows] = await pool.execute(query, [id]);
    const websites = (rows as any[]).map(row => ({
      id: row.id,
      projectId: row.project_id,
      platformId: row.platform_id,
      platform: {
        id: row.platform_id,
        name: row.platform_name
      },
      serverId: row.server_id,
      server: row.server_id ? {
        id: row.server_id,
        name: row.server_name,
        ipAddress: row.server_ip
      } : undefined,
      siteUrl: row.site_url,
      domain: row.domain,
      onlineDate: row.online_date,
      expireDate: row.expire_date,
      renewalFee: row.renewal_fee,
      accessStatusCode: row.access_status_code,
      sslExpireDate: row.ssl_expire_date,
      domainExpireDate: row.domain_expire_date,
      lastCheckTime: row.last_check_time,
      status: row.status,
      notes: row.notes,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    }));

    const response = {
      success: true,
      message: '获取项目网站列表成功',
      data: websites,
      timestamp: new Date().toISOString()
    };

    // 缓存结果（缓存5分钟）
    await setCache(cacheKey, response, 300);

    res.json({
      ...response,
      cached: false
    });

  } catch (error) {
    logger.error('获取项目网站列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取项目网站列表失败',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 批量更新网站上线时间
 */
export const batchUpdateWebsiteOnlineDate = async (req: Request, res: Response): Promise<void> => {
  try {
    const { projectId, onlineDate, websiteIds } = req.body;

    if (!projectId || !onlineDate || !Array.isArray(websiteIds) || websiteIds.length === 0) {
      res.status(400).json({
        success: false,
        message: '参数错误：需要提供项目ID、上线时间和网站ID列表',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 构建批量更新SQL
    const placeholders = websiteIds.map(() => '?').join(',');
    const query = `
      UPDATE websites 
      SET online_date = ?, updated_at = NOW() 
      WHERE id IN (${placeholders}) AND project_id = ?
    `;

    const params = [onlineDate, ...websiteIds, projectId];
    const [result] = await pool.execute(query, params);
    const affectedRows = (result as any).affectedRows;

    // 清除相关缓存
    await deleteCache(`project:${projectId}:websites`);
    await deleteCache('websites:*');

    logger.info('批量更新网站上线时间成功', {
      projectId,
      onlineDate,
      websiteIds,
      affectedRows,
      userId: (req as any).user?.userId
    });

    res.json({
      success: true,
      message: `成功更新 ${affectedRows} 个网站的上线时间`,
      data: {
        affectedRows,
        websiteIds,
        onlineDate
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('批量更新网站上线时间失败:', error);
    res.status(500).json({
      success: false,
      message: '批量更新网站上线时间失败',
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 获取项目上线统计
 */
export const getProjectOnlineStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const cacheKey = 'project:online:stats';

    // 尝试从缓存获取
    const cachedStats = await getCache(cacheKey);
    if (cachedStats) {
      logger.debug('项目上线统计缓存命中');
      res.json({
        ...cachedStats,
        cached: true
      });
      return;
    }

    const query = `
      SELECT 
        p.online_status,
        COUNT(*) as count,
        COUNT(w.id) as website_count,
        SUM(CASE WHEN w.online_date IS NOT NULL THEN 1 ELSE 0 END) as online_website_count
      FROM projects p
      LEFT JOIN websites w ON p.id = w.project_id
      GROUP BY p.online_status
    `;

    const [rows] = await pool.execute(query);
    const stats = rows as any[];

    const response = {
      success: true,
      message: '获取项目上线统计成功',
      data: {
        byStatus: stats,
        summary: {
          totalProjects: stats.reduce((sum, item) => sum + item.count, 0),
          totalWebsites: stats.reduce((sum, item) => sum + item.website_count, 0),
          onlineWebsites: stats.reduce((sum, item) => sum + item.online_website_count, 0)
        }
      },
      timestamp: new Date().toISOString()
    };

    // 缓存结果（缓存10分钟）
    await setCache(cacheKey, response, 600);

    res.json({
      ...response,
      cached: false
    });

  } catch (error) {
    logger.error('获取项目上线统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取项目上线统计失败',
      timestamp: new Date().toISOString()
    });
  }
};
