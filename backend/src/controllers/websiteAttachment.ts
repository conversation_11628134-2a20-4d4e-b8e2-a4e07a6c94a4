import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import path from 'path';
import fs from 'fs';
import { pool } from '../config/database';
import { logger } from '../utils/logger';
import { ApiResponse } from '../types';

// 获取网站附件列表
export const getWebsiteAttachments = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id: websiteId } = req.params;
    const { category, page = 1, limit = 20 } = req.query;

    let query = `
      SELECT 
        wa.*,
        u.username as uploaded_by_name
      FROM website_attachments wa
      LEFT JOIN users u ON wa.uploaded_by = u.id
      WHERE wa.website_id = ?
    `;
    const queryParams: any[] = [websiteId];

    // 按分类筛选
    if (category && category !== 'all') {
      query += ' AND wa.category = ?';
      queryParams.push(category);
    }

    query += ' ORDER BY wa.created_at DESC';

    // 分页
    const offset = (Number(page) - 1) * Number(limit);
    query += ' LIMIT ? OFFSET ?';
    queryParams.push(Number(limit), offset);

    const [rows] = await pool.execute(query, queryParams);

    // 转换字段名为驼峰格式
    const attachments = (rows as any[]).map(attachment => ({
      id: attachment.id,
      websiteId: attachment.website_id,
      fileName: attachment.file_name,
      originalName: attachment.original_name,
      filePath: attachment.file_path,
      fileSize: attachment.file_size,
      fileType: attachment.file_type,
      mimeType: attachment.mime_type,
      category: attachment.category,
      description: attachment.description,
      uploadedBy: attachment.uploaded_by,
      uploadedByName: attachment.uploaded_by_name,
      isPreviewAvailable: Boolean(attachment.is_preview_available),
      thumbnailPath: attachment.thumbnail_path,
      downloadCount: attachment.download_count,
      lastAccessed: attachment.last_accessed,
      createdAt: attachment.created_at,
      updatedAt: attachment.updated_at
    }));

    // 获取总数
    let countQuery = 'SELECT COUNT(*) as total FROM website_attachments WHERE website_id = ?';
    const countParams: any[] = [websiteId];
    if (category && category !== 'all') {
      countQuery += ' AND category = ?';
      countParams.push(category);
    }
    const [countRows] = await pool.execute(countQuery, countParams);
    const total = (countRows as any[])[0].total;

    const response: ApiResponse = {
      success: true,
      message: '获取附件列表成功',
      data: {
        attachments,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      },
      timestamp: new Date().toISOString()
    };

    res.json(response);
  } catch (error) {
    logger.error('获取网站附件列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取附件列表失败',
      timestamp: new Date().toISOString()
    });
  }
};

// 上传网站附件
export const uploadWebsiteAttachment = async (req: Request, res: Response): Promise<void> => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({
        success: false,
        message: '参数验证失败',
        errors: errors.array()
      });
      return;
    }

    const { id: websiteId } = req.params;
    const { description } = req.body;
    const fileInfo = req.body.fileInfo;
    const userId = req.user?.id || 1; // 默认用户ID

    if (!fileInfo) {
      res.status(400).json({
        success: false,
        message: '没有上传文件'
      });
      return;
    }

    // 检查网站是否存在
    const [websiteRows] = await pool.execute('SELECT id FROM websites WHERE id = ?', [websiteId]);
    if ((websiteRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '网站不存在'
      });
      return;
    }

    // 插入附件记录
    const insertQuery = `
      INSERT INTO website_attachments (
        website_id, file_name, original_name, file_path, file_size,
        file_type, mime_type, category, description, uploaded_by, is_preview_available
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const [result] = await pool.execute(insertQuery, [
      websiteId,
      fileInfo.fileName,
      fileInfo.originalName,
      fileInfo.filePath,
      fileInfo.fileSize,
      fileInfo.fileType,
      fileInfo.mimeType,
      fileInfo.category,
      description || null,
      userId,
      fileInfo.isPreviewAvailable
    ]);

    const attachmentId = (result as any).insertId;

    // 获取刚插入的附件信息
    const [attachmentRows] = await pool.execute(`
      SELECT
        wa.*,
        u.username as uploaded_by_name
      FROM website_attachments wa
      LEFT JOIN users u ON wa.uploaded_by = u.id
      WHERE wa.id = ?
    `, [attachmentId]);

    const attachment = (attachmentRows as any[])[0];

    // 转换字段名为驼峰格式
    const formattedAttachment = {
      id: attachment.id,
      websiteId: attachment.website_id,
      fileName: attachment.file_name,
      originalName: attachment.original_name,
      filePath: attachment.file_path,
      fileSize: attachment.file_size,
      fileType: attachment.file_type,
      mimeType: attachment.mime_type,
      category: attachment.category,
      description: attachment.description,
      uploadedBy: attachment.uploaded_by,
      uploadedByName: attachment.uploaded_by_name,
      isPreviewAvailable: Boolean(attachment.is_preview_available),
      thumbnailPath: attachment.thumbnail_path,
      downloadCount: attachment.download_count,
      lastAccessed: attachment.last_accessed,
      createdAt: attachment.created_at,
      updatedAt: attachment.updated_at
    };

    const response: ApiResponse = {
      success: true,
      message: '文件上传成功',
      data: formattedAttachment,
      timestamp: new Date().toISOString()
    };

    res.json(response);
  } catch (error) {
    logger.error('上传网站附件失败:', error);
    res.status(500).json({
      success: false,
      message: '文件上传失败',
      timestamp: new Date().toISOString()
    });
  }
};

// 删除网站附件
export const deleteWebsiteAttachment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id: websiteId, attachmentId } = req.params;

    // 获取附件信息
    const [attachmentRows] = await pool.execute(
      'SELECT * FROM website_attachments WHERE id = ? AND website_id = ?',
      [attachmentId, websiteId]
    );

    if ((attachmentRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '附件不存在'
      });
      return;
    }

    const attachment = (attachmentRows as any[])[0];

    // 删除物理文件
    if (fs.existsSync(attachment.file_path)) {
      fs.unlinkSync(attachment.file_path);
    }

    // 删除缩略图（如果存在）
    if (attachment.thumbnail_path && fs.existsSync(attachment.thumbnail_path)) {
      fs.unlinkSync(attachment.thumbnail_path);
    }

    // 删除数据库记录
    await pool.execute('DELETE FROM website_attachments WHERE id = ?', [attachmentId]);

    res.json({
      success: true,
      message: '附件删除成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('删除网站附件失败:', error);
    res.status(500).json({
      success: false,
      message: '删除附件失败',
      timestamp: new Date().toISOString()
    });
  }
};

// 下载网站附件
export const downloadWebsiteAttachment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id: websiteId, attachmentId } = req.params;

    // 获取附件信息
    const [attachmentRows] = await pool.execute(
      'SELECT * FROM website_attachments WHERE id = ? AND website_id = ?',
      [attachmentId, websiteId]
    );

    if ((attachmentRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '附件不存在'
      });
      return;
    }

    const attachment = (attachmentRows as any[])[0];

    // 检查文件是否存在
    if (!fs.existsSync(attachment.file_path)) {
      res.status(404).json({
        success: false,
        message: '文件不存在'
      });
      return;
    }

    // 更新下载次数和最后访问时间
    await pool.execute(
      'UPDATE website_attachments SET download_count = download_count + 1, last_accessed = NOW() WHERE id = ?',
      [attachmentId]
    );

    // 设置响应头
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(attachment.original_name)}"`);
    res.setHeader('Content-Type', attachment.mime_type);

    // 发送文件
    res.sendFile(path.resolve(attachment.file_path));
  } catch (error) {
    logger.error('下载网站附件失败:', error);
    res.status(500).json({
      success: false,
      message: '下载附件失败',
      timestamp: new Date().toISOString()
    });
  }
};

// 预览网站附件
export const previewWebsiteAttachment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id: websiteId, attachmentId } = req.params;

    // 获取附件信息
    const [attachmentRows] = await pool.execute(
      'SELECT * FROM website_attachments WHERE id = ? AND website_id = ?',
      [attachmentId, websiteId]
    );

    if ((attachmentRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '附件不存在'
      });
      return;
    }

    const attachment = (attachmentRows as any[])[0];

    // 检查是否支持预览
    if (!attachment.is_preview_available) {
      res.status(400).json({
        success: false,
        message: '该文件类型不支持预览'
      });
      return;
    }

    // 检查文件是否存在
    if (!fs.existsSync(attachment.file_path)) {
      res.status(404).json({
        success: false,
        message: '文件不存在'
      });
      return;
    }

    // 更新最后访问时间
    await pool.execute(
      'UPDATE website_attachments SET last_accessed = NOW() WHERE id = ?',
      [attachmentId]
    );

    // 设置响应头
    res.setHeader('Content-Type', attachment.mime_type);
    res.setHeader('Content-Disposition', `inline; filename="${encodeURIComponent(attachment.original_name)}"`);

    // 发送文件
    res.sendFile(path.resolve(attachment.file_path));
  } catch (error) {
    logger.error('预览网站附件失败:', error);
    res.status(500).json({
      success: false,
      message: '预览附件失败',
      timestamp: new Date().toISOString()
    });
  }
};

// 更新附件信息
export const updateWebsiteAttachment = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id: websiteId, attachmentId } = req.params;
    const { description } = req.body;

    // 检查附件是否存在
    const [attachmentRows] = await pool.execute(
      'SELECT id FROM website_attachments WHERE id = ? AND website_id = ?',
      [attachmentId, websiteId]
    );

    if ((attachmentRows as any[]).length === 0) {
      res.status(404).json({
        success: false,
        message: '附件不存在'
      });
      return;
    }

    // 更新附件描述
    await pool.execute(
      'UPDATE website_attachments SET description = ? WHERE id = ?',
      [description || null, attachmentId]
    );

    res.json({
      success: true,
      message: '附件信息更新成功',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('更新网站附件失败:', error);
    res.status(500).json({
      success: false,
      message: '更新附件信息失败',
      timestamp: new Date().toISOString()
    });
  }
};
