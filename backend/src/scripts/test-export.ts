import { exportTable, exportMultipleTables, ExportFormat } from '../utils/export';
import { logger } from '../utils/logger';

// 测试数据
const testUsers = [
  {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    realName: '系统管理员',
    role: 'super_admin',
    status: 'active',
    phone: '13800138000',
    department: '技术部',
    lastLogin: '2024-01-22T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-22T10:30:00Z'
  },
  {
    id: 2,
    username: 'manager',
    email: '<EMAIL>',
    realName: '项目经理',
    role: 'admin',
    status: 'active',
    phone: '13800138001',
    department: '项目部',
    lastLogin: '2024-01-22T09:15:00Z',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-22T09:15:00Z'
  }
];

const testCustomers = [
  {
    id: 1,
    name: '张三',
    company: '北京科技有限公司',
    contactPerson: '张三',
    phone: '13800138000',
    email: 'zhang<PERSON>@example.com',
    address: '北京市朝阳区xxx街道',
    industry: '科技',
    source: '官网咨询',
    status: 'confirmed',
    salesPersonName: '销售A',
    notes: '重要客户',
    createdAt: '2024-01-01T00:00:00Z'
  }
];

// 测试单表导出
async function testSingleExport() {
  console.log('开始测试单表导出...');
  
  try {
    // 测试Excel导出
    const excelPath = await exportTable({
      filename: '测试用户列表',
      sheetName: '用户数据',
      headers: [
        { key: 'id', label: 'ID', width: 10 },
        { key: 'username', label: '用户名', width: 15 },
        { key: 'email', label: '邮箱', width: 25 },
        { key: 'realName', label: '真实姓名', width: 15 },
        { key: 'role', label: '角色', width: 12 },
        { key: 'status', label: '状态', width: 10 },
        { key: 'phone', label: '手机号', width: 15 },
        { key: 'department', label: '部门', width: 15 },
        { key: 'lastLogin', label: '最后登录', width: 20 },
        { key: 'createdAt', label: '创建时间', width: 20 }
      ],
      data: testUsers,
      format: 'excel'
    });
    console.log('Excel导出成功:', excelPath);

    // 测试CSV导出
    const csvPath = await exportTable({
      filename: '测试用户列表',
      headers: [
        { key: 'id', label: 'ID' },
        { key: 'username', label: '用户名' },
        { key: 'email', label: '邮箱' },
        { key: 'realName', label: '真实姓名' },
        { key: 'role', label: '角色' },
        { key: 'status', label: '状态' }
      ],
      data: testUsers,
      format: 'csv'
    });
    console.log('CSV导出成功:', csvPath);

    // 测试JSON导出
    const jsonPath = await exportTable({
      filename: '测试用户列表',
      headers: [],
      data: testUsers,
      format: 'json'
    });
    console.log('JSON导出成功:', jsonPath);

  } catch (error) {
    console.error('单表导出测试失败:', error);
  }
}

// 测试多表导出
async function testMultipleExport() {
  console.log('开始测试多表导出...');
  
  try {
    const excelPath = await exportMultipleTables({
      filename: '测试多表数据',
      sheets: [
        {
          name: '用户数据',
          headers: [
            { key: 'id', label: 'ID', width: 10 },
            { key: 'username', label: '用户名', width: 15 },
            { key: 'email', label: '邮箱', width: 25 },
            { key: 'realName', label: '真实姓名', width: 15 },
            { key: 'role', label: '角色', width: 12 }
          ],
          data: testUsers
        },
        {
          name: '客户数据',
          headers: [
            { key: 'id', label: 'ID', width: 10 },
            { key: 'name', label: '客户姓名', width: 15 },
            { key: 'company', label: '公司名称', width: 25 },
            { key: 'phone', label: '手机号', width: 15 },
            { key: 'email', label: '邮箱', width: 25 }
          ],
          data: testCustomers
        }
      ],
      format: 'excel'
    });
    console.log('多表Excel导出成功:', excelPath);

  } catch (error) {
    console.error('多表导出测试失败:', error);
  }
}

// 运行测试
async function runTests() {
  console.log('开始导出功能测试...\n');
  
  await testSingleExport();
  console.log('');
  await testMultipleExport();
  
  console.log('\n导出功能测试完成！');
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}

export { runTests };
