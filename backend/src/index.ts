import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import path from 'path';

import { config, validateConfig } from '@/config';
import { testConnection } from '@/config/database';
import { initRedis, closeRedis } from '@/services/cache';
import { logger } from '@/utils/logger';
import {
  errorHandler,
  notFoundHandler,
  corsOptions,
  securityHeaders,
  healthCheck,
  apiRateLimit
} from '@/middleware/common';
import {
  performanceMonitor,
  cacheControl,
  enhancedRateLimit,
  memoryMonitor
} from '@/middleware/performance';
import routes from '@/routes';

// 验证配置（开发环境跳过）
try {
  if (process.env.NODE_ENV === 'production') {
    validateConfig();
    logger.info('配置验证通过');
  } else {
    logger.info('开发环境跳过配置验证');
  }
} catch (error) {
  logger.error('配置验证失败', error);
  process.exit(1);
}

// 创建Express应用
const app = express();

// 信任代理（用于获取真实IP）
app.set('trust proxy', 1);

// 性能监控中间件
app.use(performanceMonitor);
app.use(memoryMonitor);

// 基础中间件
app.use(helmet()); // 安全头
app.use(securityHeaders); // 自定义安全头
app.use(compression()); // 响应压缩
app.use(cors(corsOptions)); // CORS配置

// 请求日志
if (config.app.env === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', {
    stream: {
      write: (message: string) => {
        logger.info(message.trim());
      }
    }
  }));
}

// 请求解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// 健康检查
app.get('/health', healthCheck);

// 静态资源缓存控制
app.use('/uploads', cacheControl(86400)); // 24小时缓存

// API限流（增强版）
app.use(config.app.apiPrefix, enhancedRateLimit(15 * 60 * 1000, 1000)); // 15分钟1000次

// API路由
app.use(config.app.apiPrefix, routes);

// 404处理
app.use(notFoundHandler);

// 错误处理
app.use(errorHandler);

// 启动服务器
const startServer = async () => {
  try {
    // 初始化缓存
    await initRedis();

    // 测试数据库连接（可选）
    try {
      const dbConnected = await testConnection();
      if (dbConnected) {
        logger.info('数据库连接成功');
      } else {
        logger.warn('数据库连接失败，但继续启动服务器');
      }
    } catch (error) {
      logger.warn('数据库连接测试失败，但继续启动服务器:', error);
    }

    // 启动HTTP服务器
    const server = app.listen(config.app.port, () => {
      logger.info(`服务器启动成功`, {
        port: config.app.port,
        env: config.app.env,
        apiPrefix: config.app.apiPrefix,
        redis: 'connected',
        database: 'connected'
      });
    });

    // 优雅关闭处理
    const gracefulShutdown = (signal: string) => {
      logger.info(`收到 ${signal} 信号，开始优雅关闭服务器`);

      server.close(async (err) => {
        if (err) {
          logger.error('服务器关闭时发生错误', err);
          process.exit(1);
        }

        // 关闭Redis连接
        await closeRedis();

        logger.info('服务器已优雅关闭');
        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        logger.error('强制关闭服务器');
        process.exit(1);
      }, 10000);
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // 未捕获异常处理
    process.on('uncaughtException', (error) => {
      logger.error('未捕获的异常', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('未处理的Promise拒绝', { reason, promise });
      process.exit(1);
    });

  } catch (error) {
    logger.error('服务器启动失败', error);
    process.exit(1);
  }
};

// 启动应用
startServer();

export default app;
