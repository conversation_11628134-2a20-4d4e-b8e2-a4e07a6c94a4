import winston from 'winston';
import path from 'path';
import { config } from '@/config';

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    // 添加堆栈信息
    if (stack) {
      log += `\n${stack}`;
    }
    
    // 添加元数据
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// 控制台格式
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, stack }) => {
    let log = `${timestamp} ${level}: ${message}`;
    if (stack) {
      log += `\n${stack}`;
    }
    return log;
  })
);

// 创建日志目录
const logsDir = path.join(process.cwd(), 'logs');

// 日志传输器配置
const transports: winston.transport[] = [
  // 控制台输出
  new winston.transports.Console({
    format: consoleFormat,
    level: config.app.env === 'development' ? 'debug' : 'info'
  })
];

// 生产环境添加文件日志
if (config.app.env === 'production') {
  transports.push(
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    // 组合日志文件
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      format: logFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  );
}

// 创建logger实例
export const logger = winston.createLogger({
  level: config.app.env === 'development' ? 'debug' : 'info',
  format: logFormat,
  transports,
  // 处理未捕获的异常
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log'),
      format: logFormat
    })
  ],
  // 处理未处理的Promise拒绝
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log'),
      format: logFormat
    })
  ]
});

// 开发环境下的额外配置
if (config.app.env === 'development') {
  logger.debug('日志系统初始化完成 - 开发模式');
}

// 导出日志方法
export const logInfo = (message: string, meta?: any) => {
  logger.info(message, meta);
};

export const logError = (message: string, error?: Error | any, meta?: any) => {
  logger.error(message, { error: error?.message || error, stack: error?.stack, ...meta });
};

export const logWarn = (message: string, meta?: any) => {
  logger.warn(message, meta);
};

export const logDebug = (message: string, meta?: any) => {
  logger.debug(message, meta);
};

// 请求日志中间件
export const requestLogger = (req: any, res: any, next: any) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const { method, originalUrl, ip } = req;
    const { statusCode } = res;
    
    const logData = {
      method,
      url: originalUrl,
      statusCode,
      duration: `${duration}ms`,
      ip,
      userAgent: req.get('User-Agent')
    };
    
    if (statusCode >= 400) {
      logger.warn(`HTTP ${statusCode} - ${method} ${originalUrl}`, logData);
    } else {
      logger.info(`HTTP ${statusCode} - ${method} ${originalUrl}`, logData);
    }
  });
  
  next();
};

// 数据库操作日志
export const logDatabaseOperation = (operation: string, table: string, data?: any) => {
  logger.debug(`数据库操作: ${operation}`, {
    table,
    data: data ? JSON.stringify(data) : undefined
  });
};

// API调用日志
export const logApiCall = (api: string, method: string, url: string, response?: any) => {
  logger.info(`外部API调用: ${api}`, {
    method,
    url,
    response: response ? JSON.stringify(response) : undefined
  });
};

// 监控日志
export const logMonitor = (type: string, resource: string, status: string, data?: any) => {
  logger.info(`监控检查: ${type}`, {
    resource,
    status,
    data
  });
};
