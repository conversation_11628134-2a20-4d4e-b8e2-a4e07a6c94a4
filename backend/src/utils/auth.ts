import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { config } from '@/config';

// 密码加密
export const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
};

// 密码验证
export const comparePassword = async (password: string, hashedPassword: string): Promise<boolean> => {
  return bcrypt.compare(password, hashedPassword);
};

// 生成访问令牌
export const generateToken = (payload: object): string => {
  return jwt.sign(payload, config.jwt.secret as string, {
    expiresIn: config.jwt.expiresIn
  });
};

// 生成刷新令牌
export const generateRefreshToken = (payload: object): string => {
  return jwt.sign(payload, config.jwt.secret as string, {
    expiresIn: config.jwt.refreshExpiresIn
  });
};

// 验证令牌
export const verifyToken = (token: string): any => {
  return jwt.verify(token, config.jwt.secret as string);
};

// 验证刷新令牌
export const verifyRefreshToken = (token: string): any => {
  return jwt.verify(token, config.jwt.secret as string);
};

// 从请求头中提取令牌
export const extractTokenFromHeader = (authHeader: string | undefined): string | null => {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.substring(7);
};





// 检查用户权限
export const checkPermission = (
  userRole: string,
  requiredRole: string | string[]
): boolean => {
  const roleHierarchy = {
    'super_admin': 3,
    'admin': 2,
    'user': 1
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
  
  if (Array.isArray(requiredRole)) {
    return requiredRole.some(role => {
      const requiredLevel = roleHierarchy[role as keyof typeof roleHierarchy] || 0;
      return userLevel >= requiredLevel;
    });
  }
  
  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
  return userLevel >= requiredLevel;
};

// 检查资源权限
export const checkResourcePermission = (
  userRole: string,
  resourcePermission: string,
  requiredPermission: 'read' | 'write' | 'admin'
): boolean => {
  // 超级管理员拥有所有权限
  if (userRole === 'super_admin') {
    return true;
  }

  const permissionLevels = {
    'read': 1,
    'write': 2,
    'admin': 3
  };

  const userPermissionLevel = permissionLevels[resourcePermission as keyof typeof permissionLevels] || 0;
  const requiredLevel = permissionLevels[requiredPermission];

  return userPermissionLevel >= requiredLevel;
};

// 生成随机密码
export const generateRandomPassword = (length: number = 12): string => {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
  let password = '';
  
  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  
  return password;
};

// 验证密码强度
export const validatePasswordStrength = (password: string): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('密码长度至少8位');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('密码必须包含小写字母');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('密码必须包含大写字母');
  }
  
  if (!/\d/.test(password)) {
    errors.push('密码必须包含数字');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('密码必须包含特殊字符');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// 生成API密钥
export const generateApiKey = (): string => {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2);
  return `sk_${timestamp}_${random}`;
};

// 令牌黑名单管理（简单实现，生产环境建议使用Redis）
const tokenBlacklist = new Set<string>();

// 将令牌加入黑名单
export const blacklistToken = (token: string): void => {
  tokenBlacklist.add(token);
  
  // 定期清理过期的令牌（简单实现）
  setTimeout(() => {
    tokenBlacklist.delete(token);
  }, 7 * 24 * 60 * 60 * 1000); // 7天后清理
};

// 检查令牌是否在黑名单中
export const isTokenBlacklisted = (token: string): boolean => {
  return tokenBlacklist.has(token);
};
