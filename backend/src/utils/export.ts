import * as XLSX from 'xlsx';
import { createObjectCsvWriter } from 'csv-writer';
import archiver from 'archiver';
import fs from 'fs';
import path from 'path';
import { Response } from 'express';
import { logger } from './logger';

// 导出格式类型
export type ExportFormat = 'excel' | 'csv' | 'json';

// 导出配置接口
export interface ExportConfig {
  filename: string;
  sheetName?: string;
  headers: Array<{
    key: string;
    label: string;
    width?: number;
  }>;
  data: any[];
  format: ExportFormat;
}

// 导出多个表格的配置
export interface MultiExportConfig {
  filename: string;
  sheets: Array<{
    name: string;
    headers: Array<{
      key: string;
      label: string;
      width?: number;
    }>;
    data: any[];
  }>;
  format: ExportFormat;
}

/**
 * 导出单个表格
 */
export const exportTable = async (config: ExportConfig): Promise<string> => {
  const { filename, sheetName = 'Sheet1', headers, data, format } = config;
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const fullFilename = `${filename}_${timestamp}`;
  
  try {
    switch (format) {
      case 'excel':
        return await exportToExcel(fullFilename, sheetName, headers, data);
      case 'csv':
        return await exportToCSV(fullFilename, headers, data);
      case 'json':
        return await exportToJSON(fullFilename, data);
      default:
        throw new Error(`不支持的导出格式: ${format}`);
    }
  } catch (error) {
    logger.error('导出表格失败', error);
    throw error;
  }
};

/**
 * 导出多个表格到一个Excel文件
 */
export const exportMultipleTables = async (config: MultiExportConfig): Promise<string> => {
  const { filename, sheets, format } = config;
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const fullFilename = `${filename}_${timestamp}`;
  
  try {
    if (format === 'excel') {
      return await exportMultipleToExcel(fullFilename, sheets);
    } else {
      // 对于非Excel格式，创建压缩包
      return await exportMultipleToArchive(fullFilename, sheets, format);
    }
  } catch (error) {
    logger.error('导出多个表格失败', error);
    throw error;
  }
};

/**
 * 导出到Excel格式
 */
const exportToExcel = async (
  filename: string,
  sheetName: string,
  headers: Array<{ key: string; label: string; width?: number }>,
  data: any[]
): Promise<string> => {
  const workbook = XLSX.utils.book_new();
  
  // 创建表头
  const headerRow = headers.map(h => h.label);
  
  // 创建数据行
  const dataRows = data.map(row => 
    headers.map(header => {
      const value = getNestedValue(row, header.key);
      return formatCellValue(value);
    })
  );
  
  // 合并表头和数据
  const worksheetData = [headerRow, ...dataRows];
  
  // 创建工作表
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
  
  // 设置列宽
  const colWidths = headers.map(header => ({
    wch: header.width || 15
  }));
  worksheet['!cols'] = colWidths;
  
  // 设置表头样式
  const headerRange = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
  for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
    const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
    if (!worksheet[cellAddress]) continue;
    
    worksheet[cellAddress].s = {
      font: { bold: true },
      fill: { fgColor: { rgb: 'E6F3FF' } },
      alignment: { horizontal: 'center' }
    };
  }
  
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
  
  const filePath = path.join(process.cwd(), 'uploads', 'exports', `${filename}.xlsx`);
  await ensureDirectoryExists(path.dirname(filePath));
  
  XLSX.writeFile(workbook, filePath);
  
  logger.info('Excel文件导出成功', { filename, filePath });
  return filePath;
};

/**
 * 导出多个表格到Excel
 */
const exportMultipleToExcel = async (
  filename: string,
  sheets: Array<{
    name: string;
    headers: Array<{ key: string; label: string; width?: number }>;
    data: any[];
  }>
): Promise<string> => {
  const workbook = XLSX.utils.book_new();
  
  for (const sheet of sheets) {
    const { name, headers, data } = sheet;
    
    // 创建表头
    const headerRow = headers.map(h => h.label);
    
    // 创建数据行
    const dataRows = data.map(row => 
      headers.map(header => {
        const value = getNestedValue(row, header.key);
        return formatCellValue(value);
      })
    );
    
    // 合并表头和数据
    const worksheetData = [headerRow, ...dataRows];
    
    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);
    
    // 设置列宽
    const colWidths = headers.map(header => ({
      wch: header.width || 15
    }));
    worksheet['!cols'] = colWidths;
    
    XLSX.utils.book_append_sheet(workbook, worksheet, name);
  }
  
  const filePath = path.join(process.cwd(), 'uploads', 'exports', `${filename}.xlsx`);
  await ensureDirectoryExists(path.dirname(filePath));
  
  XLSX.writeFile(workbook, filePath);
  
  logger.info('多表格Excel文件导出成功', { filename, filePath, sheetsCount: sheets.length });
  return filePath;
};

/**
 * 导出到CSV格式
 */
const exportToCSV = async (
  filename: string,
  headers: Array<{ key: string; label: string }>,
  data: any[]
): Promise<string> => {
  const filePath = path.join(process.cwd(), 'uploads', 'exports', `${filename}.csv`);
  await ensureDirectoryExists(path.dirname(filePath));
  
  const csvWriter = createObjectCsvWriter({
    path: filePath,
    header: headers.map(h => ({ id: h.key, title: h.label })),
    encoding: 'utf8'
  });
  
  // 处理数据，展平嵌套对象
  const processedData = data.map(row => {
    const processedRow: any = {};
    headers.forEach(header => {
      processedRow[header.key] = formatCellValue(getNestedValue(row, header.key));
    });
    return processedRow;
  });
  
  await csvWriter.writeRecords(processedData);
  
  logger.info('CSV文件导出成功', { filename, filePath });
  return filePath;
};

/**
 * 导出到JSON格式
 */
const exportToJSON = async (filename: string, data: any[]): Promise<string> => {
  const filePath = path.join(process.cwd(), 'uploads', 'exports', `${filename}.json`);
  await ensureDirectoryExists(path.dirname(filePath));
  
  const jsonData = {
    exportTime: new Date().toISOString(),
    totalRecords: data.length,
    data: data
  };
  
  fs.writeFileSync(filePath, JSON.stringify(jsonData, null, 2), 'utf8');
  
  logger.info('JSON文件导出成功', { filename, filePath });
  return filePath;
};

/**
 * 导出多个文件到压缩包
 */
const exportMultipleToArchive = async (
  filename: string,
  sheets: Array<{
    name: string;
    headers: Array<{ key: string; label: string }>;
    data: any[];
  }>,
  format: ExportFormat
): Promise<string> => {
  const archivePath = path.join(process.cwd(), 'uploads', 'exports', `${filename}.zip`);
  await ensureDirectoryExists(path.dirname(archivePath));
  
  const output = fs.createWriteStream(archivePath);
  const archive = archiver('zip', { zlib: { level: 9 } });
  
  archive.pipe(output);
  
  for (const sheet of sheets) {
    let filePath: string;
    
    if (format === 'csv') {
      filePath = await exportToCSV(`${sheet.name}_temp`, sheet.headers, sheet.data);
      archive.file(filePath, { name: `${sheet.name}.csv` });
    } else if (format === 'json') {
      filePath = await exportToJSON(`${sheet.name}_temp`, sheet.data);
      archive.file(filePath, { name: `${sheet.name}.json` });
    }
  }
  
  await archive.finalize();
  
  // 清理临时文件
  for (const sheet of sheets) {
    const tempPath = path.join(process.cwd(), 'uploads', 'exports', `${sheet.name}_temp.*`);
    // 这里可以添加清理逻辑
  }
  
  logger.info('压缩包导出成功', { filename, archivePath, sheetsCount: sheets.length });
  return archivePath;
};

/**
 * 获取嵌套对象的值
 */
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : '';
  }, obj);
};

/**
 * 格式化单元格值
 */
const formatCellValue = (value: any): string => {
  if (value === null || value === undefined) {
    return '';
  }
  
  if (typeof value === 'object') {
    if (value instanceof Date) {
      return value.toLocaleString('zh-CN');
    }
    return JSON.stringify(value);
  }
  
  return String(value);
};

/**
 * 确保目录存在
 */
const ensureDirectoryExists = async (dirPath: string): Promise<void> => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
};

/**
 * 发送文件下载响应
 */
export const sendFileDownload = (res: Response, filePath: string, originalFilename?: string): void => {
  const filename = originalFilename || path.basename(filePath);
  const encodedFilename = encodeURIComponent(filename);
  
  res.setHeader('Content-Disposition', `attachment; filename="${encodedFilename}"; filename*=UTF-8''${encodedFilename}`);
  res.setHeader('Content-Type', 'application/octet-stream');
  
  const fileStream = fs.createReadStream(filePath);
  fileStream.pipe(res);
  
  // 文件发送完成后删除临时文件
  fileStream.on('end', () => {
    setTimeout(() => {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        logger.info('临时导出文件已删除', { filePath });
      }
    }, 5000); // 5秒后删除
  });
};

/**
 * 清理过期的导出文件
 */
export const cleanupExpiredExports = (): void => {
  const exportsDir = path.join(process.cwd(), 'uploads', 'exports');
  
  if (!fs.existsSync(exportsDir)) {
    return;
  }
  
  const files = fs.readdirSync(exportsDir);
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24小时
  
  files.forEach(file => {
    const filePath = path.join(exportsDir, file);
    const stats = fs.statSync(filePath);
    
    if (now - stats.mtime.getTime() > maxAge) {
      fs.unlinkSync(filePath);
      logger.info('过期导出文件已删除', { filePath });
    }
  });
};
