import { Router } from 'express';
import {
  getProfile,
  updateProfile,
  changePassword,
  updatePreferences,
  uploadAvatar,
  getActivityLog,
  getLoginSessions,
  terminateSession,
  toggleTwoFactor
} from '../controllers/profile';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// 所有个人资料路由都需要认证
router.use(authenticateToken);

// 获取用户资料
router.get('/', getProfile);

// 更新用户资料
router.put('/', updateProfile);

// 修改密码
router.post('/password', changePassword);

// 更新偏好设置
router.put('/preferences', updatePreferences);

// 上传头像
router.post('/avatar', uploadAvatar);

// 获取活动日志
router.get('/activity', getActivityLog);

// 获取登录会话
router.get('/sessions', getLoginSessions);

// 终止登录会话
router.delete('/sessions/:sessionId', terminateSession);

// 启用/禁用双因子认证
router.post('/two-factor', toggleTwoFactor);

export default router;
