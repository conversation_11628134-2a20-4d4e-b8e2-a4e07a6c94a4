import { Router } from 'express';
import { body } from 'express-validator';
import {
  login,
  register,
  refreshToken,
  logout,
  getCurrentUser,
  changePassword
} from '@/controllers/auth';
import { authenticate, authorize } from '@/middleware/auth';
import { validateRequest, loginRateLimit } from '@/middleware/common';
import { validatePasswordStrength } from '@/utils/auth';

const router = Router();

// 登录验证规则
const loginValidation = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间'),
  body('password')
    .notEmpty()
    .withMessage('密码不能为空')
    .isLength({ min: 6 })
    .withMessage('密码长度至少6个字符')
];

// 注册验证规则
const registerValidation = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  body('email')
    .isEmail()
    .withMessage('邮箱格式不正确')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 8 })
    .withMessage('密码长度至少8个字符')
    .custom((value) => {
      const validation = validatePasswordStrength(value);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }
      return true;
    }),
  body('realName')
    .optional()
    .isLength({ max: 50 })
    .withMessage('真实姓名长度不能超过50个字符'),
  body('role')
    .optional()
    .isIn(['admin', 'user'])
    .withMessage('角色必须是admin或user'),
  body('phone')
    .optional()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('手机号格式不正确'),
  body('department')
    .optional()
    .isLength({ max: 50 })
    .withMessage('部门名称长度不能超过50个字符')
];

// 刷新令牌验证规则
const refreshTokenValidation = [
  body('refreshToken')
    .notEmpty()
    .withMessage('刷新令牌不能为空')
];

// 修改密码验证规则
const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('当前密码不能为空'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('新密码长度至少8个字符')
    .custom((value) => {
      const validation = validatePasswordStrength(value);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }
      return true;
    })
    .custom((value, { req }) => {
      if (value === req.body.currentPassword) {
        throw new Error('新密码不能与当前密码相同');
      }
      return true;
    })
];

// 路由定义

/**
 * @route   POST /api/v1/auth/login
 * @desc    用户登录
 * @access  Public
 */
router.post('/login', 
  loginRateLimit,
  loginValidation,
  validateRequest,
  login
);

/**
 * @route   POST /api/v1/auth/register
 * @desc    用户注册（仅超级管理员）
 * @access  Private (Super Admin)
 */
router.post('/register',
  authenticate,
  authorize('super_admin'),
  registerValidation,
  validateRequest,
  register
);

/**
 * @route   POST /api/v1/auth/refresh
 * @desc    刷新访问令牌
 * @access  Public
 */
router.post('/refresh',
  refreshTokenValidation,
  validateRequest,
  refreshToken
);

/**
 * @route   POST /api/v1/auth/logout
 * @desc    用户登出
 * @access  Private
 */
router.post('/logout',
  authenticate,
  logout
);

/**
 * @route   GET /api/v1/auth/me
 * @desc    获取当前用户信息
 * @access  Private
 */
router.get('/me',
  authenticate,
  getCurrentUser
);

/**
 * @route   PUT /api/v1/auth/change-password
 * @desc    修改密码
 * @access  Private
 */
router.put('/change-password',
  authenticate,
  changePasswordValidation,
  validateRequest,
  changePassword
);

export default router;
