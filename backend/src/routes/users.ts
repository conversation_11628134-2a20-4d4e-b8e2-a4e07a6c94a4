import { Router } from 'express';
import { body, param, query } from 'express-validator';
import {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  resetUserPassword
} from '@/controllers/users';
import { authenticate, authorize } from '@/middleware/auth';
import { validateRequest } from '@/middleware/common';
import { validatePasswordStrength } from '@/utils/auth';

const router = Router();

// 查询参数验证
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是大于0的整数'),
  query('pageSize')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  query('search')
    .optional()
    .isLength({ max: 100 })
    .withMessage('搜索关键词长度不能超过100个字符'),
  query('role')
    .optional()
    .isIn(['super_admin', 'admin', 'user'])
    .withMessage('角色必须是super_admin、admin或user'),
  query('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended'])
    .withMessage('状态必须是active、inactive或suspended'),
  query('sortBy')
    .optional()
    .isIn(['id', 'username', 'email', 'real_name', 'role', 'status', 'created_at', 'updated_at'])
    .withMessage('排序字段无效'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序方向必须是asc或desc')
];

// ID参数验证
const idValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('用户ID必须是大于0的整数')
];

// 创建用户验证规则
const createUserValidation = [
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空')
    .isLength({ min: 3, max: 50 })
    .withMessage('用户名长度必须在3-50个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  body('email')
    .isEmail()
    .withMessage('邮箱格式不正确')
    .normalizeEmail(),
  body('password')
    .isLength({ min: 8 })
    .withMessage('密码长度至少8个字符')
    .custom((value) => {
      const validation = validatePasswordStrength(value);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }
      return true;
    }),
  body('realName')
    .optional()
    .isLength({ max: 50 })
    .withMessage('真实姓名长度不能超过50个字符'),
  body('role')
    .optional()
    .isIn(['admin', 'user'])
    .withMessage('角色必须是admin或user'),
  body('phone')
    .optional()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('手机号格式不正确'),
  body('department')
    .optional()
    .isLength({ max: 50 })
    .withMessage('部门名称长度不能超过50个字符')
];

// 更新用户验证规则
const updateUserValidation = [
  body('realName')
    .optional()
    .isLength({ max: 50 })
    .withMessage('真实姓名长度不能超过50个字符'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('邮箱格式不正确')
    .normalizeEmail(),
  body('phone')
    .optional()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('手机号格式不正确'),
  body('department')
    .optional()
    .isLength({ max: 50 })
    .withMessage('部门名称长度不能超过50个字符'),
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended'])
    .withMessage('状态必须是active、inactive或suspended'),
  body('role')
    .optional()
    .isIn(['admin', 'user'])
    .withMessage('角色必须是admin或user')
];

// 重置密码验证规则
const resetPasswordValidation = [
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('新密码长度至少8个字符')
    .custom((value) => {
      const validation = validatePasswordStrength(value);
      if (!validation.isValid) {
        throw new Error(validation.errors.join(', '));
      }
      return true;
    })
];

// 路由定义

/**
 * @route   GET /api/v1/users
 * @desc    获取用户列表
 * @access  Private (Admin+)
 */
router.get('/',
  authenticate,
  authorize(['admin', 'super_admin']),
  queryValidation,
  validateRequest,
  getUsers
);

/**
 * @route   GET /api/v1/users/:id
 * @desc    获取单个用户信息
 * @access  Private (Admin+)
 */
router.get('/:id',
  authenticate,
  authorize(['admin', 'super_admin']),
  idValidation,
  validateRequest,
  getUser
);

/**
 * @route   POST /api/v1/users
 * @desc    创建新用户
 * @access  Private (Admin+)
 */
router.post('/',
  authenticate,
  authorize(['admin', 'super_admin']),
  createUserValidation,
  validateRequest,
  createUser
);

/**
 * @route   PUT /api/v1/users/:id
 * @desc    更新用户信息
 * @access  Private (Admin+)
 */
router.put('/:id',
  authenticate,
  authorize(['admin', 'super_admin']),
  idValidation,
  updateUserValidation,
  validateRequest,
  updateUser
);

/**
 * @route   DELETE /api/v1/users/:id
 * @desc    删除用户（软删除）
 * @access  Private (Super Admin)
 */
router.delete('/:id',
  authenticate,
  authorize('super_admin'),
  idValidation,
  validateRequest,
  deleteUser
);

/**
 * @route   PUT /api/v1/users/:id/reset-password
 * @desc    重置用户密码
 * @access  Private (Super Admin)
 */
router.put('/:id/reset-password',
  authenticate,
  authorize('super_admin'),
  idValidation,
  resetPasswordValidation,
  validateRequest,
  resetUserPassword
);

export default router;
