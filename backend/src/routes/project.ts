import { Router } from 'express';
import { body, param } from 'express-validator';
import {
  updateProjectOnlineStatus,
  getProjectWebsites,
  batchUpdateWebsiteOnlineDate,
  getProjectOnlineStats
} from '@/controllers/project';
import { authenticate, authorize } from '@/middleware/auth';
import { validateRequest } from '@/middleware/common';
import { cacheControl } from '@/middleware/performance';

const router = Router();

// 验证规则
const updateOnlineStatusValidation = [
  param('id').isInt({ min: 1 }).withMessage('项目ID必须是正整数'),
  body('onlineStatus')
    .isIn(['planning', 'development', 'testing', 'online', 'suspended'])
    .withMessage('上线状态值无效'),
  body('onlineDate')
    .optional()
    .isISO8601()
    .withMessage('上线时间格式无效')
];

const batchUpdateValidation = [
  body('projectId').isInt({ min: 1 }).withMessage('项目ID必须是正整数'),
  body('onlineDate').isISO8601().withMessage('上线时间格式无效'),
  body('websiteIds')
    .isArray({ min: 1 })
    .withMessage('网站ID列表不能为空')
    .custom((value) => {
      if (!Array.isArray(value) || !value.every(id => Number.isInteger(id) && id > 0)) {
        throw new Error('网站ID列表格式无效');
      }
      return true;
    })
];

/**
 * @route   PUT /api/v1/projects/:id/online-status
 * @desc    更新项目上线状态（自动联动网站上线时间）
 * @access  Private
 */
router.put('/:id/online-status',
  authenticate,
  authorize(['admin', 'manager']),
  updateOnlineStatusValidation,
  validateRequest,
  updateProjectOnlineStatus
);

/**
 * @route   GET /api/v1/projects/:id/websites
 * @desc    获取项目关联的网站列表
 * @access  Private
 */
router.get('/:id/websites',
  authenticate,
  param('id').isInt({ min: 1 }).withMessage('项目ID必须是正整数'),
  validateRequest,
  cacheControl(300), // 缓存5分钟
  getProjectWebsites
);

/**
 * @route   POST /api/v1/projects/batch-update-website-online-date
 * @desc    批量更新网站上线时间
 * @access  Private
 */
router.post('/batch-update-website-online-date',
  authenticate,
  authorize(['admin', 'manager']),
  batchUpdateValidation,
  validateRequest,
  batchUpdateWebsiteOnlineDate
);

/**
 * @route   GET /api/v1/projects/online-stats
 * @desc    获取项目上线统计
 * @access  Private
 */
router.get('/online-stats',
  authenticate,
  cacheControl(600), // 缓存10分钟
  getProjectOnlineStats
);

export default router;
