import { Router } from 'express';
import { query } from 'express-validator';
import {
  getDashboardStats,
  getDashboardCharts
} from '@/controllers/dashboard';
import { authenticate } from '@/middleware/auth';
import { validateRequest } from '@/middleware/common';
import { cacheControl } from '@/middleware/performance';

const router = Router();

/**
 * @route   GET /api/v1/dashboard/stats
 * @desc    获取仪表盘统计数据
 * @access  Private
 */
router.get('/stats',
  authenticate,
  cacheControl(300), // 缓存5分钟
  getDashboardStats
);

/**
 * @route   GET /api/v1/dashboard/charts
 * @desc    获取仪表盘图表数据
 * @access  Private
 */
router.get('/charts',
  authenticate,
  [
    query('type')
      .optional()
      .isIn(['website-status', 'project-progress', 'monthly-trend', 'platform-distribution'])
      .withMessage('图表类型无效'),
    query('period')
      .optional()
      .isIn(['7d', '30d', '90d'])
      .withMessage('时间周期无效')
  ],
  validateRequest,
  cacheControl(600), // 缓存10分钟
  getDashboardCharts
);

export default router;
