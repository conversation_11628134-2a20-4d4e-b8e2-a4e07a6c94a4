const express = require('express');
const mysql = require('mysql2/promise');
const router = express.Router();

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  user: 'sitemanager',
  password: 'sitemanager123',
  database: 'sitemanager',
  charset: 'utf8mb4'
};

// 获取数据库连接
async function getDbConnection() {
  return await mysql.createConnection(dbConfig);
}

// 测试飞书通知
router.post('/test-feishu', async (req, res) => {
  try {
    const { webhookUrl, botName = '网站监控机器人' } = req.body;

    if (!webhookUrl) {
      return res.status(400).json({
        success: false,
        message: '缺少飞书机器人Webhook URL'
      });
    }

    // 构建测试消息
    const testMessage = `🤖 ${botName} 测试通知

📍 测试信息：
• 发送时间：${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}
• 系统状态：正常运行
• 通知功能：配置成功

✅ 如果您收到此消息，说明飞书机器人配置正确！

🔧 接下来系统将在以下情况发送通知：
• 网站连续失败5次以上
• SSL证书即将到期
• 域名即将到期
• 服务器异常告警`;

    // 发送飞书通知
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        msg_type: 'text',
        content: {
          text: testMessage
        }
      }),
    });

    const result = await response.json();

    if (response.ok && result.code === 0) {
      res.json({
        success: true,
        message: '飞书测试通知发送成功',
        data: result
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.msg || '飞书通知发送失败',
        error: result
      });
    }
  } catch (error) {
    console.error('发送飞书测试通知失败:', error);
    res.status(500).json({
      success: false,
      message: '发送飞书测试通知失败',
      error: error.message
    });
  }
});

// 保存通知配置（通用）
router.post('/config', async (req, res) => {
  const connection = await getDbConnection();
  try {
    const { type, name, config, enabled = true } = req.body;

    if (!type || !name || !config) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：type, name, config'
      });
    }

    // 检查是否已存在同类型同名称的配置
    const [existing] = await connection.execute(
      'SELECT id FROM notification_configs WHERE type = ? AND name = ?',
      [type, name]
    );

    let result;
    if (existing.length > 0) {
      // 更新现有配置
      [result] = await connection.execute(
        'UPDATE notification_configs SET config = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [JSON.stringify(config), enabled, existing[0].id]
      );
    } else {
      // 创建新配置
      [result] = await connection.execute(
        'INSERT INTO notification_configs (type, name, config, is_active) VALUES (?, ?, ?, ?)',
        [type, name, JSON.stringify(config), enabled]
      );
    }

    res.json({
      success: true,
      message: '通知配置保存成功',
      data: {
        id: existing.length > 0 ? existing[0].id : result.insertId,
        type,
        name,
        config,
        enabled
      }
    });
  } catch (error) {
    console.error('保存通知配置失败:', error);
    res.status(500).json({
      success: false,
      message: '保存通知配置失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

// 保存飞书配置（兼容旧接口）
router.post('/feishu-config', async (req, res) => {
  const connection = await getDbConnection();
  try {
    const { webhookUrl, botName, notificationThreshold, enabled } = req.body;

    if (!webhookUrl) {
      return res.status(400).json({
        success: false,
        message: '缺少飞书机器人Webhook URL'
      });
    }

    const config = {
      webhookUrl,
      botName: botName || '网站监控机器人',
      notificationThreshold: notificationThreshold || 5
    };

    // 检查是否已存在飞书配置
    const [existing] = await connection.execute(
      'SELECT id FROM notification_configs WHERE type = ? AND name = ?',
      ['feishu', '默认飞书配置']
    );

    let result;
    if (existing.length > 0) {
      // 更新现有配置
      [result] = await connection.execute(
        'UPDATE notification_configs SET config = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [JSON.stringify(config), enabled, existing[0].id]
      );
    } else {
      // 创建新配置
      [result] = await connection.execute(
        'INSERT INTO notification_configs (type, name, config, is_active) VALUES (?, ?, ?, ?)',
        ['feishu', '默认飞书配置', JSON.stringify(config), enabled]
      );
    }

    res.json({
      success: true,
      message: '飞书配置保存成功',
      data: {
        webhookUrl,
        botName,
        notificationThreshold,
        enabled
      }
    });
  } catch (error) {
    console.error('保存飞书配置失败:', error);
    res.status(500).json({
      success: false,
      message: '保存飞书配置失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

// 获取通知配置列表
router.get('/configs', async (req, res) => {
  const connection = await getDbConnection();
  try {
    const { type } = req.query;

    let query = 'SELECT * FROM notification_configs';
    let params = [];

    if (type) {
      query += ' WHERE type = ?';
      params.push(type);
    }

    query += ' ORDER BY created_at DESC';

    const [configs] = await connection.execute(query, params);

    // 解析JSON配置
    const parsedConfigs = configs.map(config => ({
      ...config,
      config: JSON.parse(config.config),
      is_active: Boolean(config.is_active)
    }));

    res.json({
      success: true,
      data: parsedConfigs
    });
  } catch (error) {
    console.error('获取通知配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取通知配置失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

// 获取飞书配置（兼容旧接口）
router.get('/feishu-config', async (req, res) => {
  const connection = await getDbConnection();
  try {
    const [configs] = await connection.execute(
      'SELECT * FROM notification_configs WHERE type = ? ORDER BY created_at DESC LIMIT 1',
      ['feishu']
    );

    if (configs.length > 0) {
      const config = JSON.parse(configs[0].config);
      res.json({
        success: true,
        data: {
          webhookUrl: config.webhookUrl || '',
          botName: config.botName || '网站监控机器人',
          notificationThreshold: config.notificationThreshold || 5,
          enabled: Boolean(configs[0].is_active)
        }
      });
    } else {
      res.json({
        success: true,
        data: {
          webhookUrl: '',
          botName: '网站监控机器人',
          notificationThreshold: 5,
          enabled: false
        }
      });
    }
  } catch (error) {
    console.error('获取飞书配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取飞书配置失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

// 删除通知配置
router.delete('/config/:id', async (req, res) => {
  const connection = await getDbConnection();
  try {
    const { id } = req.params;

    const [result] = await connection.execute(
      'DELETE FROM notification_configs WHERE id = ?',
      [id]
    );

    if (result.affectedRows > 0) {
      res.json({
        success: true,
        message: '通知配置删除成功'
      });
    } else {
      res.status(404).json({
        success: false,
        message: '通知配置不存在'
      });
    }
  } catch (error) {
    console.error('删除通知配置失败:', error);
    res.status(500).json({
      success: false,
      message: '删除通知配置失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

// 获取通知日志
router.get('/logs', async (req, res) => {
  const connection = await getDbConnection();
  try {
    const {
      page = 1,
      limit = 20,
      type,
      status,
      website_id,
      start_date,
      end_date
    } = req.query;

    let whereConditions = [];
    let params = [];

    if (type) {
      whereConditions.push('nl.notification_type = ?');
      params.push(type);
    }

    if (status) {
      whereConditions.push('nl.status = ?');
      params.push(status);
    }

    if (website_id) {
      whereConditions.push('nl.website_id = ?');
      params.push(website_id);
    }

    if (start_date) {
      whereConditions.push('nl.created_at >= ?');
      params.push(start_date);
    }

    if (end_date) {
      whereConditions.push('nl.created_at <= ?');
      params.push(end_date);
    }

    const whereClause = whereConditions.length > 0 ?
      'WHERE ' + whereConditions.join(' AND ') : '';

    // 获取总数
    const [countResult] = await connection.execute(
      `SELECT COUNT(*) as total FROM notification_logs nl ${whereClause}`,
      params
    );

    // 获取日志列表
    const offset = (page - 1) * limit;
    const [logs] = await connection.execute(
      `SELECT
        nl.*,
        w.site_name,
        w.url
      FROM notification_logs nl
      LEFT JOIN websites w ON nl.website_id = w.id
      ${whereClause}
      ORDER BY nl.created_at DESC
      LIMIT ? OFFSET ?`,
      [...params, parseInt(limit), offset]
    );

    // 解析JSON字段
    const parsedLogs = logs.map(log => ({
      ...log,
      response_data: log.response_data ? JSON.parse(log.response_data) : null
    }));

    res.json({
      success: true,
      data: {
        logs: parsedLogs,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: countResult[0].total,
          pages: Math.ceil(countResult[0].total / limit)
        }
      }
    });
  } catch (error) {
    console.error('获取通知日志失败:', error);
    res.status(500).json({
      success: false,
      message: '获取通知日志失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

// 获取通知统计
router.get('/stats', async (req, res) => {
  const connection = await getDbConnection();
  try {
    const { days = 30 } = req.query;

    // 获取总体统计
    const [totalStats] = await connection.execute(
      `SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending
      FROM notification_logs
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)`,
      [days]
    );

    // 获取按类型统计
    const [typeStats] = await connection.execute(
      `SELECT
        notification_type,
        COUNT(*) as total,
        SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
      FROM notification_logs
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY notification_type`,
      [days]
    );

    // 获取按日期统计
    const [dailyStats] = await connection.execute(
      `SELECT
        DATE(created_at) as date,
        COUNT(*) as total,
        SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
      FROM notification_logs
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC`,
      [days]
    );

    const stats = totalStats[0];
    const successRate = stats.total > 0 ?
      ((stats.sent / stats.total) * 100).toFixed(2) : 0;

    res.json({
      success: true,
      data: {
        total: stats.total,
        sent: stats.sent,
        failed: stats.failed,
        pending: stats.pending,
        successRate: parseFloat(successRate),
        typeStats,
        dailyStats
      }
    });
  } catch (error) {
    console.error('获取通知统计失败:', error);
    res.status(500).json({
      success: false,
      message: '获取通知统计失败',
      error: error.message
    });
  } finally {
    await connection.end();
  }
});

module.exports = router;
