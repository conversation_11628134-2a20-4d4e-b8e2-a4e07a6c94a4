import { Router } from 'express';
import authRoutes from './auth';
import userRoutes from './users';
import exportRoutes from './export';
import websiteRoutes from './website';
import projectRoutes from './project';
import dashboardRoutes from './dashboard';
import monitorRoutes from './monitor';
import settingsRoutes from './settings';
import profileRoutes from './profile';
// import customerRoutes from './customers';
// import serverRoutes from './servers';
// import domainRoutes from './domains';
// import permissionRoutes from './permissions';
// import notificationRoutes from './notifications';

const router = Router();

// API版本信息
router.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'WordPress站点管理系统 API',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: {
      auth: '/auth',
      users: '/users',
      export: '/export',
      customers: '/customers',
      projects: '/projects',
      websites: '/websites',
      servers: '/servers',
      domains: '/domains',
      permissions: '/permissions',
      monitor: '/monitor',
      settings: '/settings',
      profile: '/profile',
      notifications: '/notifications'
    }
  });
});

// 路由注册
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/export', exportRoutes);
router.use('/websites', websiteRoutes);
router.use('/projects', projectRoutes);
router.use('/dashboard', dashboardRoutes);
router.use('/monitor', monitorRoutes);
router.use('/settings', settingsRoutes);
router.use('/profile', profileRoutes);
// router.use('/customers', customerRoutes);
// router.use('/servers', serverRoutes);
// router.use('/domains', domainRoutes);
// router.use('/permissions', permissionRoutes);
// router.use('/notifications', notificationRoutes);

export default router;
