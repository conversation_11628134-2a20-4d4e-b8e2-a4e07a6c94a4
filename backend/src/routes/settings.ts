import { Router } from 'express';
import {
  getSettings,
  updateSettings,
  getSettingsByCategory,
  testEmailConfig,
  exportConfig,
  importConfig,
  resetSettings
} from '../controllers/settings';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// 所有设置路由都需要认证
router.use(authenticateToken);

// 获取所有设置
router.get('/', getSettings);

// 获取特定分类的设置
router.get('/:category', getSettingsByCategory);

// 更新设置
router.put('/', updateSettings);

// 重置设置
router.post('/:category/reset', resetSettings);

// 测试邮件配置
router.post('/notification/test-email', testEmailConfig);

// 导出配置
router.get('/export/config', exportConfig);

// 导入配置
router.post('/import/config', importConfig);

export default router;
