import { Router } from 'express';
import { param, body } from 'express-validator';
import {
  getWebsiteAttachments,
  uploadWebsiteAttachment,
  deleteWebsiteAttachment,
  downloadWebsiteAttachment,
  previewWebsiteAttachment,
  updateWebsiteAttachment
} from '../controllers/websiteAttachment';
import { authenticate, authorize } from '../middleware/auth';
import { validateRequest } from '../middleware/common';
import { uploadSingle, processFileInfo, handleUploadError } from '../middleware/upload';

const router = Router();

// 参数验证
const websiteIdValidation = [
  param('id').isInt({ min: 1 }).withMessage('网站ID必须是正整数')
];

const attachmentIdValidation = [
  param('attachmentId').isInt({ min: 1 }).withMessage('附件ID必须是正整数')
];

const uploadValidation = [
  body('description').optional().isString().isLength({ max: 500 }).withMessage('描述长度不能超过500字符')
];

const updateValidation = [
  body('description').optional().isString().isLength({ max: 500 }).withMessage('描述长度不能超过500字符')
];

/**
 * @route   GET /api/v1/websites/:id/attachments
 * @desc    获取网站附件列表
 * @access  Private
 */
router.get('/:id/attachments',
  authenticate,
  websiteIdValidation,
  validateRequest,
  getWebsiteAttachments
);

/**
 * @route   POST /api/v1/websites/:id/attachments
 * @desc    上传网站附件
 * @access  Private (需要写权限)
 */
router.post('/:id/attachments',
  authenticate,
  authorize(['admin', 'super_admin']),
  websiteIdValidation,
  uploadSingle,
  processFileInfo,
  uploadValidation,
  validateRequest,
  uploadWebsiteAttachment,
  handleUploadError
);

/**
 * @route   DELETE /api/v1/websites/:id/attachments/:attachmentId
 * @desc    删除网站附件
 * @access  Private (需要管理员权限)
 */
router.delete('/:id/attachments/:attachmentId',
  authenticate,
  authorize(['admin', 'super_admin']),
  websiteIdValidation,
  attachmentIdValidation,
  validateRequest,
  deleteWebsiteAttachment
);

/**
 * @route   GET /api/v1/websites/:id/attachments/:attachmentId/download
 * @desc    下载网站附件
 * @access  Private
 */
router.get('/:id/attachments/:attachmentId/download',
  authenticate,
  websiteIdValidation,
  attachmentIdValidation,
  validateRequest,
  downloadWebsiteAttachment
);

/**
 * @route   GET /api/v1/websites/:id/attachments/:attachmentId/preview
 * @desc    预览网站附件
 * @access  Private
 */
router.get('/:id/attachments/:attachmentId/preview',
  authenticate,
  websiteIdValidation,
  attachmentIdValidation,
  validateRequest,
  previewWebsiteAttachment
);

/**
 * @route   PUT /api/v1/websites/:id/attachments/:attachmentId
 * @desc    更新附件信息
 * @access  Private (需要写权限)
 */
router.put('/:id/attachments/:attachmentId',
  authenticate,
  authorize(['admin', 'super_admin']),
  websiteIdValidation,
  attachmentIdValidation,
  updateValidation,
  validateRequest,
  updateWebsiteAttachment
);

export default router;
