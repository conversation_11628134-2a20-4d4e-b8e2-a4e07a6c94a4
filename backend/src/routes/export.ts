import { Router, Request, Response } from 'express';
import { query } from 'express-validator';
import {
  exportUsers,
  exportCustomers,
  exportProjects,
  exportWebsites
} from '@/controllers/export';
import { authenticate, authorize } from '@/middleware/auth';
import { validateRequest } from '@/middleware/common';

const router = Router();

// 导出格式验证
const exportFormatValidation = [
  query('format')
    .optional()
    .isIn(['excel', 'csv', 'json'])
    .withMessage('导出格式必须是excel、csv或json')
];

// 通用查询参数验证
const commonQueryValidation = [
  query('search')
    .optional()
    .isLength({ max: 100 })
    .withMessage('搜索关键词长度不能超过100个字符'),
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是大于0的整数'),
  query('pageSize')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('每页数量必须是1-1000之间的整数')
];

/**
 * @route   GET /api/v1/export/users
 * @desc    导出用户列表
 * @access  Private (Admin+)
 */
router.get('/users',
  authenticate,
  authorize(['admin', 'super_admin']),
  [
    ...exportFormatValidation,
    ...commonQueryValidation,
    query('role')
      .optional()
      .isIn(['super_admin', 'admin', 'user'])
      .withMessage('角色必须是super_admin、admin或user'),
    query('status')
      .optional()
      .isIn(['active', 'inactive', 'suspended'])
      .withMessage('状态必须是active、inactive或suspended')
  ],
  validateRequest,
  exportUsers
);

/**
 * @route   GET /api/v1/export/customers
 * @desc    导出客户列表
 * @access  Private
 */
router.get('/customers',
  authenticate,
  [
    ...exportFormatValidation,
    ...commonQueryValidation,
    query('status')
      .optional()
      .isIn(['potential', 'confirmed', 'lost'])
      .withMessage('状态必须是potential、confirmed或lost'),
    query('industry')
      .optional()
      .isLength({ max: 50 })
      .withMessage('行业名称长度不能超过50个字符')
  ],
  validateRequest,
  exportCustomers
);

/**
 * @route   GET /api/v1/export/projects
 * @desc    导出项目列表
 * @access  Private
 */
router.get('/projects',
  authenticate,
  [
    ...exportFormatValidation,
    ...commonQueryValidation,
    query('onlineStatus')
      .optional()
      .isIn(['planning', 'development', 'testing', 'online', 'suspended'])
      .withMessage('项目状态无效'),
    query('projectType')
      .optional()
      .isLength({ max: 50 })
      .withMessage('项目类型长度不能超过50个字符')
  ],
  validateRequest,
  exportProjects
);

/**
 * @route   GET /api/v1/export/websites
 * @desc    导出网站列表
 * @access  Private
 */
router.get('/websites',
  authenticate,
  [
    ...exportFormatValidation,
    ...commonQueryValidation,
    query('status')
      .optional()
      .isIn(['active', 'inactive', 'suspended', 'expired'])
      .withMessage('状态必须是active、inactive、suspended或expired'),
    query('platform')
      .optional()
      .isLength({ max: 50 })
      .withMessage('平台名称长度不能超过50个字符')
  ],
  validateRequest,
  exportWebsites
);

/**
 * @route   GET /api/v1/export/servers
 * @desc    导出服务器列表
 * @access  Private (Admin+)
 */
router.get('/servers',
  authenticate,
  authorize(['admin', 'super_admin']),
  [
    ...exportFormatValidation,
    ...commonQueryValidation,
    query('status')
      .optional()
      .isIn(['active', 'inactive', 'maintenance'])
      .withMessage('状态必须是active、inactive或maintenance')
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    // 服务器导出功能待实现
    res.status(501).json({
      success: false,
      message: '服务器导出功能开发中',
      timestamp: new Date().toISOString()
    });
  }
);

/**
 * @route   GET /api/v1/export/domains
 * @desc    导出域名列表
 * @access  Private
 */
router.get('/domains',
  authenticate,
  [
    ...exportFormatValidation,
    ...commonQueryValidation,
    query('status')
      .optional()
      .isIn(['active', 'expired', 'suspended', 'transferred'])
      .withMessage('状态必须是active、expired、suspended或transferred')
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    // 域名导出功能待实现
    res.status(501).json({
      success: false,
      message: '域名导出功能开发中',
      timestamp: new Date().toISOString()
    });
  }
);

/**
 * @route   GET /api/v1/export/all
 * @desc    导出所有数据（多表格Excel）
 * @access  Private (Super Admin)
 */
router.get('/all',
  authenticate,
  authorize('super_admin'),
  [
    query('format')
      .optional()
      .isIn(['excel'])
      .withMessage('全量导出仅支持Excel格式'),
    query('includeUsers')
      .optional()
      .isBoolean()
      .withMessage('includeUsers必须是布尔值'),
    query('includeCustomers')
      .optional()
      .isBoolean()
      .withMessage('includeCustomers必须是布尔值'),
    query('includeProjects')
      .optional()
      .isBoolean()
      .withMessage('includeProjects必须是布尔值'),
    query('includeWebsites')
      .optional()
      .isBoolean()
      .withMessage('includeWebsites必须是布尔值')
  ],
  validateRequest,
  async (req: Request, res: Response) => {
    // 全量导出功能待实现
    res.status(501).json({
      success: false,
      message: '全量导出功能开发中',
      timestamp: new Date().toISOString()
    });
  }
);

export default router;
