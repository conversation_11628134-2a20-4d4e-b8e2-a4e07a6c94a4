import { Router } from 'express';
import {
  getMonitorData,
  checkWebsiteStatus,
  resolveAlert,
  getSystemMetrics
} from '../controllers/monitor';
import { authenticateToken } from '../middleware/auth';

const router = Router();

// 所有监控路由都需要认证
router.use(authenticateToken);

// 获取监控数据
router.get('/data', getMonitorData);

// 获取系统性能指标
router.get('/metrics', getSystemMetrics);

// 检查网站状态
router.post('/website/:websiteId/check', checkWebsiteStatus);

// 处理告警
router.post('/alert/:alertId/resolve', resolveAlert);

export default router;
