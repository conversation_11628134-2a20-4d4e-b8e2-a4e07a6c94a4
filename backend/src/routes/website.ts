import { Router } from 'express';
import { body, param, query } from 'express-validator';
import {
  getWebsites,
  getWebsiteById,
  createWebsite,
  updateWebsite,
  deleteWebsite,
  getPlatforms,
  getServers,
  batchUpdateWebsites,
  checkWebsiteAccess,
  getWebsiteStats,
  getWebsiteAccounts,
  createWebsiteAccount,
  updateWebsiteAccount,
  deleteWebsiteAccount
} from '@/controllers/website';
import { authenticate, authorize } from '@/middleware/auth';
import { validateRequest } from '@/middleware/common';
import { cacheControl } from '@/middleware/performance';
import websiteAttachmentRoutes from './websiteAttachment';

const router = Router();

// 通用验证规则
const websiteValidation = [
  body('platformId')
    .isInt({ min: 1 })
    .withMessage('平台ID必须是正整数'),
  body('siteUrl')
    .isURL()
    .withMessage('网站URL格式不正确'),
  body('domain')
    .isLength({ min: 1, max: 100 })
    .withMessage('域名长度必须在1-100字符之间')
    .matches(/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/)
    .withMessage('域名格式不正确'),
  body('projectId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('项目ID必须是正整数'),
  body('serverId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('服务器ID必须是正整数'),
  body('onlineDate')
    .optional()
    .isISO8601()
    .withMessage('上线日期格式不正确'),
  body('expireDate')
    .optional()
    .isISO8601()
    .withMessage('到期日期格式不正确'),
  body('projectAmount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('项目金额必须是非负数'),
  body('renewalFee')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('续费金额必须是非负数'),
  body('sslExpireDate')
    .optional()
    .isISO8601()
    .withMessage('SSL到期日期格式不正确'),
  body('domainExpireDate')
    .optional()
    .isISO8601()
    .withMessage('域名到期日期格式不正确'),
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended', 'expired'])
    .withMessage('状态必须是active、inactive、suspended或expired'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('备注长度不能超过1000字符')
];

// 查询参数验证
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  query('search')
    .optional()
    .isLength({ max: 100 })
    .withMessage('搜索关键词长度不能超过100字符'),
  query('status')
    .optional()
    .isIn(['active', 'inactive', 'suspended', 'expired'])
    .withMessage('状态筛选值无效'),
  query('platform')
    .optional()
    .isLength({ max: 50 })
    .withMessage('平台筛选值长度不能超过50字符'),
  query('server')
    .optional()
    .isLength({ max: 50 })
    .withMessage('服务器筛选值长度不能超过50字符')
];

// ID参数验证
const idValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID必须是正整数')
];

/**
 * @route   GET /api/v1/websites
 * @desc    获取网站列表
 * @access  Private
 */
router.get('/',
  authenticate,
  cacheControl(60), // 缓存1分钟
  queryValidation,
  validateRequest,
  getWebsites
);

/**
 * @route   GET /api/v1/websites/:id
 * @desc    获取单个网站详情
 * @access  Private
 */
router.get('/:id',
  authenticate,
  idValidation,
  validateRequest,
  getWebsiteById
);

/**
 * @route   POST /api/v1/websites
 * @desc    创建新网站
 * @access  Private (需要写权限)
 */
router.post('/',
  authenticate,
  authorize(['admin', 'super_admin']),
  websiteValidation,
  validateRequest,
  createWebsite
);

/**
 * @route   PUT /api/v1/websites/:id
 * @desc    更新网站信息
 * @access  Private (需要写权限)
 */
router.put('/:id',
  authenticate,
  authorize(['admin', 'super_admin']),
  idValidation,
  websiteValidation,
  validateRequest,
  updateWebsite
);

/**
 * @route   DELETE /api/v1/websites/:id
 * @desc    删除网站
 * @access  Private (需要管理员权限)
 */
router.delete('/:id',
  authenticate,
  authorize(['admin', 'super_admin']),
  idValidation,
  validateRequest,
  deleteWebsite
);

/**
 * @route   GET /api/v1/websites/options/platforms
 * @desc    获取平台选项列表
 * @access  Private
 */
router.get('/options/platforms',
  authenticate,
  cacheControl(3600), // 缓存1小时
  getPlatforms
);

/**
 * @route   GET /api/v1/websites/options/servers
 * @desc    获取服务器选项列表
 * @access  Private
 */
router.get('/options/servers',
  authenticate,
  cacheControl(300), // 缓存5分钟
  getServers
);

/**
 * @route   POST /api/v1/websites/batch
 * @desc    批量操作网站
 * @access  Private (需要写权限)
 */
router.post('/batch',
  authenticate,
  authorize(['admin', 'super_admin']),
  [
    body('action')
      .isIn(['updateStatus', 'updateServer', 'updateExpireDate', 'delete'])
      .withMessage('操作类型无效'),
    body('websiteIds')
      .isArray({ min: 1 })
      .withMessage('请选择要操作的网站'),
    body('data')
      .optional()
      .isObject()
      .withMessage('操作数据格式无效')
  ],
  validateRequest,
  batchUpdateWebsites
);

/**
 * @route   POST /api/v1/websites/:id/check-access
 * @desc    检查网站可访问性
 * @access  Private
 */
router.post('/:id/check-access',
  authenticate,
  idValidation,
  validateRequest,
  checkWebsiteAccess
);

/**
 * @route   GET /api/v1/websites/stats
 * @desc    获取网站统计信息
 * @access  Private
 */
router.get('/stats',
  authenticate,
  cacheControl(300), // 缓存5分钟
  getWebsiteStats
);

// 集成网站附件路由
router.use('/', websiteAttachmentRoutes);

// ==================== 网站账号管理路由 ====================

/**
 * @route   GET /api/v1/websites/:id/accounts
 * @desc    获取网站账号列表
 * @access  Private
 */
router.get('/:id/accounts',
  authenticate,
  param('id').isInt({ min: 1 }).withMessage('网站ID必须是正整数'),
  validateRequest,
  getWebsiteAccounts
);

/**
 * @route   POST /api/v1/websites/:id/accounts
 * @desc    创建网站账号
 * @access  Private (需要写权限)
 */
router.post('/:id/accounts',
  authenticate,
  authorize(['admin', 'super_admin']),
  [
    param('id').isInt({ min: 1 }).withMessage('网站ID必须是正整数'),
    body('accountType')
      .notEmpty()
      .withMessage('账户类型不能为空')
      .isIn(['admin', 'user', 'ftp', 'database', 'hosting', 'domain', 'ssl', 'email', 'analytics', 'other'])
      .withMessage('账户类型无效'),
    body('username')
      .notEmpty()
      .withMessage('用户名不能为空')
      .isLength({ min: 2, max: 100 })
      .withMessage('用户名长度必须在2-100个字符之间'),
    body('password')
      .notEmpty()
      .withMessage('密码不能为空')
      .isLength({ min: 6 })
      .withMessage('密码长度至少6个字符'),
    body('email')
      .optional()
      .isEmail()
      .withMessage('邮箱格式无效'),
    body('role')
      .optional()
      .isLength({ max: 50 })
      .withMessage('角色长度不能超过50个字符'),
    body('loginUrl')
      .optional()
      .isURL()
      .withMessage('登录URL格式无效'),
    body('isActive')
      .optional()
      .isBoolean()
      .withMessage('是否启用必须是布尔值'),
    body('notes')
      .optional()
      .isLength({ max: 500 })
      .withMessage('备注长度不能超过500个字符')
  ],
  validateRequest,
  createWebsiteAccount
);

/**
 * @route   PUT /api/v1/websites/:id/accounts/:accountId
 * @desc    更新网站账号
 * @access  Private (需要写权限)
 */
router.put('/:id/accounts/:accountId',
  authenticate,
  authorize(['admin', 'super_admin']),
  [
    param('id').isInt({ min: 1 }).withMessage('网站ID必须是正整数'),
    param('accountId').isInt({ min: 1 }).withMessage('账号ID必须是正整数'),
    body('accountType')
      .optional()
      .isIn(['admin', 'user', 'ftp', 'database', 'hosting', 'domain', 'ssl', 'email', 'analytics', 'other'])
      .withMessage('账户类型无效'),
    body('username')
      .optional()
      .isLength({ min: 2, max: 100 })
      .withMessage('用户名长度必须在2-100个字符之间'),
    body('password')
      .optional()
      .isLength({ min: 6 })
      .withMessage('密码长度至少6个字符'),
    body('email')
      .optional()
      .isEmail()
      .withMessage('邮箱格式无效'),
    body('role')
      .optional()
      .isLength({ max: 50 })
      .withMessage('角色长度不能超过50个字符'),
    body('loginUrl')
      .optional()
      .isURL()
      .withMessage('登录URL格式无效'),
    body('isActive')
      .optional()
      .isBoolean()
      .withMessage('是否启用必须是布尔值'),
    body('notes')
      .optional()
      .isLength({ max: 500 })
      .withMessage('备注长度不能超过500个字符')
  ],
  validateRequest,
  updateWebsiteAccount
);

/**
 * @route   DELETE /api/v1/websites/:id/accounts/:accountId
 * @desc    删除网站账号
 * @access  Private (需要管理员权限)
 */
router.delete('/:id/accounts/:accountId',
  authenticate,
  authorize(['admin', 'super_admin']),
  [
    param('id').isInt({ min: 1 }).withMessage('网站ID必须是正整数'),
    param('accountId').isInt({ min: 1 }).withMessage('账号ID必须是正整数')
  ],
  validateRequest,
  deleteWebsiteAccount
);

export default router;
