import mysql from 'mysql2/promise';
import { config } from './index';
import { logger } from '@/utils/logger';

// 数据库连接池配置 - 优化性能
const poolConfig = {
  host: config.database.host,
  port: config.database.port,
  user: config.database.user,
  password: config.database.password,
  database: config.database.name,
  waitForConnections: true,
  connectionLimit: 20, // 增加连接数
  queueLimit: 0,
  charset: 'utf8mb4',
  timezone: '+08:00',
  // 性能优化配置
  supportBigNumbers: true,
  bigNumberStrings: true,
  dateStrings: false,
  debug: false,
  multipleStatements: false
};

// 创建连接池
export const pool = mysql.createPool(poolConfig);

// 数据库连接测试
export const testConnection = async (): Promise<boolean> => {
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    logger.info('数据库连接成功');
    return true;
  } catch (error) {
    logger.error('数据库连接失败:', error);
    return false;
  }
};

// 执行查询的通用方法
export const query = async <T = any>(sql: string, params?: any[]): Promise<T[]> => {
  try {
    const [rows] = await pool.execute(sql, params);
    return rows as T[];
  } catch (error) {
    logger.error('数据库查询错误:', { sql, params, error });
    throw error;
  }
};

// 执行单条查询
export const queryOne = async <T = any>(sql: string, params?: any[]): Promise<T | null> => {
  const results = await query<T>(sql, params);
  return results.length > 0 ? results[0] : null;
};

// 执行插入操作
export const insert = async (sql: string, params?: any[]): Promise<number> => {
  try {
    const [result] = await pool.execute(sql, params);
    return (result as any).insertId;
  } catch (error) {
    logger.error('数据库插入错误:', { sql, params, error });
    throw error;
  }
};

// 执行更新操作
export const update = async (sql: string, params?: any[]): Promise<number> => {
  try {
    const [result] = await pool.execute(sql, params);
    return (result as any).affectedRows;
  } catch (error) {
    logger.error('数据库更新错误:', { sql, params, error });
    throw error;
  }
};

// 执行删除操作
export const remove = async (sql: string, params?: any[]): Promise<number> => {
  try {
    const [result] = await pool.execute(sql, params);
    return (result as any).affectedRows;
  } catch (error) {
    logger.error('数据库删除错误:', { sql, params, error });
    throw error;
  }
};

// 事务处理
export const transaction = async <T>(
  callback: (connection: mysql.PoolConnection) => Promise<T>
): Promise<T> => {
  const connection = await pool.getConnection();
  try {
    await connection.beginTransaction();
    const result = await callback(connection);
    await connection.commit();
    return result;
  } catch (error) {
    await connection.rollback();
    throw error;
  } finally {
    connection.release();
  }
};

// 分页查询
export interface PaginationResult<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export const paginate = async <T = any>(
  sql: string,
  countSql: string,
  params: any[] = [],
  page: number = 1,
  pageSize: number = 10
): Promise<PaginationResult<T>> => {
  const offset = (page - 1) * pageSize;
  const paginatedSql = `${sql} LIMIT ${pageSize} OFFSET ${offset}`;
  
  const [data, countResult] = await Promise.all([
    query<T>(paginatedSql, params),
    queryOne<{ total: number }>(countSql, params)
  ]);
  
  const total = countResult?.total || 0;
  const totalPages = Math.ceil(total / pageSize);
  
  return {
    data,
    total,
    page,
    pageSize,
    totalPages
  };
};
