import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../../.env') });

// 配置接口定义
interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  name: string;
}

interface JWTConfig {
  secret: string;
  expiresIn: string;
  refreshExpiresIn: string;
}

interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
}

interface SMTPConfig {
  host: string;
  port: number;
  secure: boolean;
  user: string;
  pass: string;
}

interface AppConfig {
  port: number;
  env: string;
  frontendUrl: string;
  apiPrefix: string;
  uploadPath: string;
  maxFileSize: number;
}

interface ExternalAPIConfig {
  baiduApiKey: string;
  sslLabsApi: string;
}

interface MonitorConfig {
  interval: number;
  alertEmail: string;
  retryTimes: number;
  timeout: number;
}

export interface Config {
  app: AppConfig;
  database: DatabaseConfig;
  jwt: JWTConfig;
  redis: RedisConfig;
  smtp: SMTPConfig;
  externalApi: ExternalAPIConfig;
  monitor: MonitorConfig;
}

// 获取环境变量值，支持默认值
const getEnv = (key: string, defaultValue?: string): string => {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`环境变量 ${key} 未设置`);
  }
  return value;
};

// 获取数字类型环境变量
const getEnvNumber = (key: string, defaultValue?: number): number => {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`环境变量 ${key} 未设置`);
  }
  const num = parseInt(value, 10);
  if (isNaN(num)) {
    throw new Error(`环境变量 ${key} 必须是数字`);
  }
  return num;
};

// 获取布尔类型环境变量
const getEnvBoolean = (key: string, defaultValue?: boolean): boolean => {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`环境变量 ${key} 未设置`);
  }
  return value.toLowerCase() === 'true';
};

// 导出配置对象
export const config: Config = {
  app: {
    port: getEnvNumber('PORT', 3001),
    env: getEnv('NODE_ENV', 'development'),
    frontendUrl: getEnv('FRONTEND_URL', 'http://localhost:3000'),
    apiPrefix: getEnv('API_PREFIX', '/api/v1'),
    uploadPath: getEnv('UPLOAD_PATH', 'uploads'),
    maxFileSize: getEnvNumber('MAX_FILE_SIZE', 10 * 1024 * 1024) // 10MB
  },
  database: {
    host: getEnv('DB_HOST', 'localhost'),
    port: getEnvNumber('DB_PORT', 3306),
    user: getEnv('DB_USER', 'root'),
    password: getEnv('DB_PASSWORD'),
    name: getEnv('DB_NAME', 'sitemanager')
  },
  jwt: {
    secret: getEnv('JWT_SECRET'),
    expiresIn: getEnv('JWT_EXPIRES_IN', '7d'),
    refreshExpiresIn: getEnv('JWT_REFRESH_EXPIRES_IN', '30d')
  },
  redis: {
    host: getEnv('REDIS_HOST', 'localhost'),
    port: getEnvNumber('REDIS_PORT', 6379),
    password: process.env.REDIS_PASSWORD || undefined,
    db: getEnvNumber('REDIS_DB', 0)
  },
  smtp: {
    host: getEnv('SMTP_HOST', 'smtp.gmail.com'),
    port: getEnvNumber('SMTP_PORT', 587),
    secure: getEnvBoolean('SMTP_SECURE', false),
    user: getEnv('SMTP_USER', ''),
    pass: getEnv('SMTP_PASS', '')
  },
  externalApi: {
    baiduApiKey: getEnv('BAIDU_API_KEY', ''),
    sslLabsApi: getEnv('SSL_LABS_API', 'https://api.ssllabs.com/api/v3/')
  },
  monitor: {
    interval: getEnvNumber('MONITOR_INTERVAL', 300000), // 5分钟
    alertEmail: getEnv('ALERT_EMAIL', ''),
    retryTimes: getEnvNumber('RETRY_TIMES', 3),
    timeout: getEnvNumber('TIMEOUT', 30000) // 30秒
  }
};

// 验证必需的配置
export const validateConfig = (): void => {
  const requiredConfigs = [
    'database.password',
    'jwt.secret'
  ];

  for (const configPath of requiredConfigs) {
    const keys = configPath.split('.');
    let value: any = config;
    
    for (const key of keys) {
      value = value[key];
    }
    
    if (!value) {
      throw new Error(`必需的配置项 ${configPath} 未设置`);
    }
  }
};

// 开发环境配置
export const isDevelopment = config.app.env === 'development';
export const isProduction = config.app.env === 'production';
export const isTest = config.app.env === 'test';
