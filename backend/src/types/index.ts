// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  password?: string;
  realName?: string;
  role: 'super_admin' | 'admin' | 'user';
  status: 'active' | 'inactive' | 'suspended';
  avatar?: string;
  phone?: string;
  department?: string;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  realName?: string;
  role?: 'admin' | 'user';
  phone?: string;
  department?: string;
}

export interface UpdateUserRequest {
  realName?: string;
  email?: string;
  phone?: string;
  department?: string;
  status?: 'active' | 'inactive' | 'suspended';
  role?: 'admin' | 'user';
}

// 客户相关类型
export interface Customer {
  id: number;
  name: string;
  company?: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  address?: string;
  industry?: string;
  source?: string;
  status: 'potential' | 'confirmed' | 'lost';
  salesPersonId?: number;
  salesPerson?: User;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateCustomerRequest {
  name: string;
  company?: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
  address?: string;
  industry?: string;
  source?: string;
  salesPersonId?: number;
  notes?: string;
}

// 项目相关类型
export interface Project {
  id: number;
  customerId: number;
  customer?: Customer;
  projectName: string;
  projectType: string;
  serviceFee?: number;
  projectManagerId?: number;
  projectManager?: User;
  salesPersonId?: number;
  salesPerson?: User;
  contractNumber?: string;
  contractSignedDate?: Date;
  onlineStatus: 'planning' | 'development' | 'testing' | 'online' | 'suspended';
  onlineDate?: Date;
  plannedOnlineDate?: Date;
  infoCollectionForm?: string;
  previewLink?: string;
  progressSheet?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateProjectRequest {
  customerId: number;
  projectName: string;
  projectType: string;
  serviceFee?: number;
  projectManagerId?: number;
  salesPersonId?: number;
  contractNumber?: string;
  contractSignedDate?: string;
  plannedOnlineDate?: string;
  infoCollectionForm?: string;
  previewLink?: string;
  progressSheet?: string;
  notes?: string;
}

// 平台相关类型
export interface Platform {
  id: number;
  name: string;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
}

// 服务器相关类型
export interface Server {
  id: number;
  name: string;
  ipAddress: string;
  location?: string;
  provider?: string;
  cpuCores?: number;
  memoryGb?: number;
  storageGb?: number;
  bandwidthMbps?: number;
  os?: string;
  expireDate?: Date;
  renewalFee?: number;
  status: 'active' | 'inactive' | 'maintenance';
  sshPort?: number;
  sshUsername?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 网站相关类型
export interface Website {
  id: number;
  projectId?: number;
  project?: Project;
  platformId: number;
  platform?: Platform;
  serverId?: number;
  server?: Server;
  siteUrl: string;
  domain: string;
  onlineDate?: Date;
  expireDate?: Date;
  renewalFee?: number;
  accessStatusCode?: number;
  sslExpireDate?: Date;
  domainExpireDate?: Date;
  lastCheckTime?: Date;
  status: 'active' | 'inactive' | 'suspended' | 'expired';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 网站账号类型
export interface WebsiteAccount {
  id: number;
  websiteId: number;
  accountType: string;
  username: string;
  password: string;
  email?: string;
  role?: string;
  loginUrl?: string;
  isActive: boolean;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 域名相关类型
export interface Domain {
  id: number;
  domainName: string;
  registrar?: string;
  registerDate?: Date;
  expireDate?: Date;
  renewalFee?: number;
  autoRenewal: boolean;
  dnsProvider?: string;
  status: 'active' | 'expired' | 'suspended' | 'transferred';
  whoisInfo?: any;
  lastCheckTime?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 权限相关类型
export interface UserPermission {
  id: number;
  userId: number;
  resourceType: 'website' | 'server' | 'domain' | 'project' | 'customer';
  resourceId: number;
  permission: 'read' | 'write' | 'admin';
  grantedBy?: number;
  grantedAt: Date;
  expiresAt?: Date;
}

// 监控日志类型
export interface MonitorLog {
  id: number;
  resourceType: 'website' | 'server' | 'domain' | 'ssl';
  resourceId: number;
  checkType: string;
  status: 'success' | 'warning' | 'error';
  responseTime?: number;
  statusCode?: number;
  errorMessage?: string;
  checkData?: any;
  createdAt: Date;
}

// 通知类型
export interface Notification {
  id: number;
  userId?: number;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  content: string;
  isRead: boolean;
  resourceType?: string;
  resourceId?: number;
  createdAt: Date;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface PaginatedResponse<T = any> {
  success: boolean;
  message: string;
  data: T[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  timestamp: string;
}

// 查询参数类型
export interface QueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  role?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
}

// JWT载荷类型
export interface JWTPayload {
  userId: number;
  username: string;
  role: string;
  iat: number;
  exp: number;
}

// 请求扩展类型
declare global {
  namespace Express {
    interface Request {
      user?: User;
      file?: any;
      files?: any;
    }
  }
}
