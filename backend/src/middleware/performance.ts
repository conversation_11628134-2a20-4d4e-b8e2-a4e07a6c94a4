import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';
import { setCache, getCache } from '@/services/cache';

// 性能监控中间件
export const performanceMonitor = (req: Request, res: Response, next: NextFunction): void => {
  const startTime = Date.now();
  const startMemory = process.memoryUsage();

  // 监听响应完成事件
  res.on('finish', () => {
    const endTime = Date.now();
    const endMemory = process.memoryUsage();
    const duration = endTime - startTime;
    
    // 计算内存使用变化
    const memoryDiff = {
      rss: endMemory.rss - startMemory.rss,
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
      external: endMemory.external - startMemory.external,
    };

    // 记录性能数据
    const performanceData = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration,
      memoryDiff,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      timestamp: new Date().toISOString(),
    };

    // 慢请求警告（超过1秒）
    if (duration > 1000) {
      logger.warn('慢请求检测', performanceData);
    } else if (duration > 500) {
      logger.info('中等耗时请求', performanceData);
    } else {
      logger.debug('请求性能', performanceData);
    }

    // 异步保存性能统计数据
    savePerformanceStats(performanceData).catch(error => {
      logger.error('保存性能统计失败:', error);
    });
  });

  next();
};

// 保存性能统计数据
const savePerformanceStats = async (data: any): Promise<void> => {
  try {
    const today = new Date().toISOString().split('T')[0];
    const hour = new Date().getHours();
    
    // 按小时统计
    const hourlyKey = `perf:hourly:${today}:${hour}`;
    const hourlyStats = await getCache(hourlyKey) || {
      totalRequests: 0,
      totalDuration: 0,
      avgDuration: 0,
      slowRequests: 0,
      errorRequests: 0,
      requests: []
    };

    hourlyStats.totalRequests++;
    hourlyStats.totalDuration += data.duration;
    hourlyStats.avgDuration = hourlyStats.totalDuration / hourlyStats.totalRequests;
    
    if (data.duration > 1000) {
      hourlyStats.slowRequests++;
    }
    
    if (data.statusCode >= 400) {
      hourlyStats.errorRequests++;
    }

    // 只保留最近100个请求的详细信息
    hourlyStats.requests.push(data);
    if (hourlyStats.requests.length > 100) {
      hourlyStats.requests = hourlyStats.requests.slice(-100);
    }

    // 缓存1小时
    await setCache(hourlyKey, hourlyStats, 3600);

    // 按天统计
    const dailyKey = `perf:daily:${today}`;
    const dailyStats = await getCache(dailyKey) || {
      totalRequests: 0,
      totalDuration: 0,
      avgDuration: 0,
      slowRequests: 0,
      errorRequests: 0,
      hourlyBreakdown: {}
    };

    dailyStats.totalRequests++;
    dailyStats.totalDuration += data.duration;
    dailyStats.avgDuration = dailyStats.totalDuration / dailyStats.totalRequests;
    
    if (data.duration > 1000) {
      dailyStats.slowRequests++;
    }
    
    if (data.statusCode >= 400) {
      dailyStats.errorRequests++;
    }

    // 按小时分解
    if (!dailyStats.hourlyBreakdown[hour]) {
      dailyStats.hourlyBreakdown[hour] = {
        requests: 0,
        avgDuration: 0,
        slowRequests: 0,
        errorRequests: 0
      };
    }
    
    dailyStats.hourlyBreakdown[hour].requests++;
    dailyStats.hourlyBreakdown[hour].avgDuration = 
      (dailyStats.hourlyBreakdown[hour].avgDuration * (dailyStats.hourlyBreakdown[hour].requests - 1) + data.duration) / 
      dailyStats.hourlyBreakdown[hour].requests;
    
    if (data.duration > 1000) {
      dailyStats.hourlyBreakdown[hour].slowRequests++;
    }
    
    if (data.statusCode >= 400) {
      dailyStats.hourlyBreakdown[hour].errorRequests++;
    }

    // 缓存24小时
    await setCache(dailyKey, dailyStats, 86400);

  } catch (error) {
    logger.error('保存性能统计数据失败:', error);
  }
};

// 获取性能统计数据
export const getPerformanceStats = async (period: 'hourly' | 'daily' = 'daily', date?: string): Promise<any> => {
  try {
    const targetDate = date || new Date().toISOString().split('T')[0];
    
    if (period === 'hourly') {
      const hour = new Date().getHours();
      const key = `perf:hourly:${targetDate}:${hour}`;
      return await getCache(key);
    } else {
      const key = `perf:daily:${targetDate}`;
      return await getCache(key);
    }
  } catch (error) {
    logger.error('获取性能统计失败:', error);
    return null;
  }
};

// 响应压缩中间件
export const compressionMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  // 设置响应压缩头
  if (req.headers['accept-encoding']?.includes('gzip')) {
    res.setHeader('Content-Encoding', 'gzip');
  }
  
  next();
};

// 缓存控制中间件
export const cacheControl = (maxAge: number = 300) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // 只对GET请求设置缓存
    if (req.method === 'GET') {
      res.setHeader('Cache-Control', `public, max-age=${maxAge}`);
      res.setHeader('ETag', `"${Date.now()}"`);
    }
    
    next();
  };
};

// API限流增强版
export const enhancedRateLimit = (windowMs: number = 15 * 60 * 1000, max: number = 100) => {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const key = req.ip || 'unknown';
    const now = Date.now();
    
    // 清理过期记录
    for (const [ip, data] of requests.entries()) {
      if (now > data.resetTime) {
        requests.delete(ip);
      }
    }

    // 获取或创建请求记录
    let requestData = requests.get(key);
    if (!requestData || now > requestData.resetTime) {
      requestData = {
        count: 0,
        resetTime: now + windowMs
      };
      requests.set(key, requestData);
    }

    requestData.count++;

    // 设置响应头
    res.setHeader('X-RateLimit-Limit', max);
    res.setHeader('X-RateLimit-Remaining', Math.max(0, max - requestData.count));
    res.setHeader('X-RateLimit-Reset', new Date(requestData.resetTime).toISOString());

    // 检查是否超过限制
    if (requestData.count > max) {
      logger.warn('API限流触发', {
        ip: key,
        count: requestData.count,
        limit: max,
        url: req.originalUrl
      });

      res.status(429).json({
        success: false,
        message: '请求过于频繁，请稍后再试',
        retryAfter: Math.ceil((requestData.resetTime - now) / 1000),
        timestamp: new Date().toISOString()
      });
      return;
    }

    next();
  };
};

// 内存监控中间件
export const memoryMonitor = (req: Request, res: Response, next: NextFunction): void => {
  const memUsage = process.memoryUsage();
  const memUsageMB = {
    rss: Math.round(memUsage.rss / 1024 / 1024 * 100) / 100,
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024 * 100) / 100,
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024 * 100) / 100,
    external: Math.round(memUsage.external / 1024 / 1024 * 100) / 100,
  };

  // 内存使用过高警告
  if (memUsageMB.heapUsed > 500) { // 500MB
    logger.warn('内存使用过高', {
      memory: memUsageMB,
      url: req.originalUrl,
      method: req.method
    });
  }

  // 添加内存信息到响应头（开发环境）
  if (process.env.NODE_ENV === 'development') {
    res.setHeader('X-Memory-Usage', JSON.stringify(memUsageMB));
  }

  next();
};

// 数据库查询性能监控
export const dbQueryMonitor = (queryType: string, tableName?: string) => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      
      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;
        
        // 记录查询性能
        logger.debug('数据库查询性能', {
          queryType,
          tableName,
          duration,
          method: propertyName,
          args: args.length
        });

        // 慢查询警告
        if (duration > 1000) {
          logger.warn('慢查询检测', {
            queryType,
            tableName,
            duration,
            method: propertyName
          });
        }

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        logger.error('数据库查询错误', {
          queryType,
          tableName,
          duration,
          method: propertyName,
          error
        });
        throw error;
      }
    };
  };
};
