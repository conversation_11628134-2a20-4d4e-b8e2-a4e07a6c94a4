import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import { validationResult } from 'express-validator';
import { logger } from '@/utils/logger';
import { config } from '@/config';

// 错误处理中间件
export const errorHandler = (
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  logger.error('未处理的错误', error, {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });

  // 数据库错误
  if (error.code === 'ER_DUP_ENTRY') {
    res.status(409).json({
      success: false,
      message: '数据已存在',
      timestamp: new Date().toISOString()
    });
    return;
  }

  // JWT错误
  if (error.name === 'JsonWebTokenError') {
    res.status(401).json({
      success: false,
      message: '无效的认证令牌',
      timestamp: new Date().toISOString()
    });
    return;
  }

  if (error.name === 'TokenExpiredError') {
    res.status(401).json({
      success: false,
      message: '认证令牌已过期',
      timestamp: new Date().toISOString()
    });
    return;
  }

  // 验证错误
  if (error.name === 'ValidationError') {
    res.status(400).json({
      success: false,
      message: '数据验证失败',
      error: error.message,
      timestamp: new Date().toISOString()
    });
    return;
  }

  // 默认错误响应
  const statusCode = error.statusCode || error.status || 500;
  const message = config.app.env === 'production' 
    ? '服务器内部错误' 
    : error.message || '未知错误';

  res.status(statusCode).json({
    success: false,
    message,
    ...(config.app.env !== 'production' && { stack: error.stack }),
    timestamp: new Date().toISOString()
  });
};

// 404处理中间件
export const notFoundHandler = (req: Request, res: Response): void => {
  res.status(404).json({
    success: false,
    message: `路由 ${req.originalUrl} 不存在`,
    timestamp: new Date().toISOString()
  });
};

// 验证结果检查中间件
export const validateRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      message: '请求参数验证失败',
      errors: errors.array().map(error => ({
        field: error.type === 'field' ? error.path : undefined,
        message: error.msg,
        value: error.type === 'field' ? error.value : undefined
      })),
      timestamp: new Date().toISOString()
    });
    return;
  }
  
  next();
};

// 通用限流中间件
export const createRateLimit = (windowMs: number, max: number, message?: string) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message: message || '请求过于频繁，请稍后再试',
      timestamp: new Date().toISOString()
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      logger.warn('请求限流触发', {
        ip: req.ip,
        url: req.originalUrl,
        userAgent: req.get('User-Agent')
      });
      
      res.status(429).json({
        success: false,
        message: message || '请求过于频繁，请稍后再试',
        timestamp: new Date().toISOString()
      });
    }
  });
};

// 登录限流
export const loginRateLimit = createRateLimit(
  15 * 60 * 1000, // 15分钟
  5, // 最多5次尝试
  '登录尝试过于频繁，请15分钟后再试'
);

// API通用限流
export const apiRateLimit = createRateLimit(
  60 * 1000, // 1分钟
  100, // 最多100次请求
  'API请求过于频繁，请稍后再试'
);

// 文件上传限流
export const uploadRateLimit = createRateLimit(
  60 * 1000, // 1分钟
  10, // 最多10次上传
  '文件上传过于频繁，请稍后再试'
);

// 请求日志中间件
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  const start = Date.now();
  
  // 记录请求开始
  logger.info(`请求开始: ${req.method} ${req.originalUrl}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id
  });
  
  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logData = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id
    };
    
    if (res.statusCode >= 400) {
      logger.warn(`请求完成: ${req.method} ${req.originalUrl} - ${res.statusCode}`, logData);
    } else {
      logger.info(`请求完成: ${req.method} ${req.originalUrl} - ${res.statusCode}`, logData);
    }
  });
  
  next();
};

// CORS配置中间件
export const corsOptions = {
  origin: (origin: string | undefined, callback: (err: Error | null, allow?: boolean) => void) => {
    // 允许的域名列表
    const allowedOrigins = [
      config.app.frontendUrl,
      'http://localhost:3000',
      'http://localhost:3001',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:3001'
    ];
    
    // 开发环境允许所有域名
    if (config.app.env === 'development') {
      callback(null, true);
      return;
    }
    
    // 检查域名是否在允许列表中
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('CORS策略不允许该域名访问'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count', 'X-Page-Count']
};

// 安全头中间件
export const securityHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // 移除服务器信息
  res.removeHeader('X-Powered-By');
  
  // 设置安全头
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // 开发环境不设置HTTPS相关头
  if (config.app.env === 'production') {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }
  
  next();
};

// 健康检查中间件
export const healthCheck = (req: Request, res: Response): void => {
  res.status(200).json({
    success: true,
    message: '服务运行正常',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version
  });
};
