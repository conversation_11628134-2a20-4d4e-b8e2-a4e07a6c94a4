import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { Request } from 'express';
import crypto from 'crypto';

// 确保上传目录存在
const ensureUploadDir = (dir: string) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

// 文件类型检查
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // 允许的文件类型
  const allowedTypes = {
    // 图片类型
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg', 
    'image/png': 'png',
    'image/gif': 'gif',
    'image/webp': 'webp',
    'image/svg+xml': 'svg',
    
    // PDF文档
    'application/pdf': 'pdf',
    
    // Excel表格
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
    'application/vnd.ms-excel': 'xls',
    'text/csv': 'csv',
    
    // Word文档
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
    'application/msword': 'doc',
    
    // 其他常用格式
    'text/plain': 'txt',
    'application/zip': 'zip',
    'application/x-rar-compressed': 'rar'
  };

  if (allowedTypes[file.mimetype as keyof typeof allowedTypes]) {
    cb(null, true);
  } else {
    cb(new Error(`不支持的文件类型: ${file.mimetype}。支持的类型: 图片(jpg,png,gif,webp,svg)、PDF、Excel(xlsx,xls,csv)、Word(docx,doc)、文本文件等`));
  }
};

// 获取文件分类
const getFileCategory = (mimetype: string): string => {
  if (mimetype.startsWith('image/')) return 'image';
  if (mimetype === 'application/pdf') return 'pdf';
  if (mimetype.includes('spreadsheet') || mimetype.includes('excel') || mimetype === 'text/csv') return 'excel';
  if (mimetype.includes('wordprocessing') || mimetype.includes('msword')) return 'word';
  return 'other';
};

// 是否支持预览
const isPreviewSupported = (mimetype: string): boolean => {
  const previewTypes = [
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
    'application/pdf'
  ];
  return previewTypes.includes(mimetype);
};

// 存储配置
const storage = multer.diskStorage({
  destination: (req: Request, file: Express.Multer.File, cb) => {
    const websiteId = req.params.id || req.body.websiteId || 'temp';
    const uploadDir = path.join(process.cwd(), 'uploads', 'websites', websiteId.toString());
    ensureUploadDir(uploadDir);
    cb(null, uploadDir);
  },
  filename: (req: Request, file: Express.Multer.File, cb) => {
    // 生成唯一文件名
    const timestamp = Date.now();
    const randomString = crypto.randomBytes(6).toString('hex');
    const ext = path.extname(file.originalname);
    const filename = `${timestamp}_${randomString}${ext}`;
    cb(null, filename);
  }
});

// 文件上传配置
export const uploadConfig = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 10 // 最多10个文件
  }
});

// 单文件上传中间件
export const uploadSingle = uploadConfig.single('file');

// 多文件上传中间件
export const uploadMultiple = uploadConfig.array('files', 10);

// 文件信息处理中间件
export const processFileInfo = (req: Request, res: any, next: any) => {
  if (req.file) {
    // 单文件处理
    const file = req.file;
    req.body.fileInfo = {
      fileName: file.filename,
      originalName: file.originalname,
      filePath: file.path,
      fileSize: file.size,
      fileType: path.extname(file.originalname).toLowerCase(),
      mimeType: file.mimetype,
      category: getFileCategory(file.mimetype),
      isPreviewAvailable: isPreviewSupported(file.mimetype)
    };
  } else if (req.files && Array.isArray(req.files)) {
    // 多文件处理
    req.body.filesInfo = req.files.map(file => ({
      fileName: file.filename,
      originalName: file.originalname,
      filePath: file.path,
      fileSize: file.size,
      fileType: path.extname(file.originalname).toLowerCase(),
      mimeType: file.mimetype,
      category: getFileCategory(file.mimetype),
      isPreviewAvailable: isPreviewSupported(file.mimetype)
    }));
  }
  next();
};

// 错误处理中间件
export const handleUploadError = (error: any, req: Request, res: any, next: any) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        success: false,
        message: '文件大小超过限制（最大50MB）',
        error: error.message
      });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        success: false,
        message: '文件数量超过限制（最多10个文件）',
        error: error.message
      });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        success: false,
        message: '意外的文件字段',
        error: error.message
      });
    }
  }
  
  if (error.message.includes('不支持的文件类型')) {
    return res.status(400).json({
      success: false,
      message: error.message
    });
  }

  return res.status(500).json({
    success: false,
    message: '文件上传失败',
    error: error.message
  });
};

// 清理临时文件
export const cleanupTempFiles = (files: Express.Multer.File[]) => {
  files.forEach(file => {
    if (fs.existsSync(file.path)) {
      fs.unlinkSync(file.path);
    }
  });
};

// 生成缩略图（图片文件）
export const generateThumbnail = async (filePath: string, outputPath: string): Promise<boolean> => {
  try {
    // 这里可以使用sharp库生成缩略图
    // 暂时返回false，表示不生成缩略图
    return false;
  } catch (error) {
    console.error('生成缩略图失败:', error);
    return false;
  }
};

export default {
  uploadSingle,
  uploadMultiple,
  processFileInfo,
  handleUploadError,
  cleanupTempFiles,
  generateThumbnail
};
