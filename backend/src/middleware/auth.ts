import { Request, Response, NextFunction } from 'express';
import { 
  extractTokenFromHeader, 
  verifyToken, 
  checkPermission, 
  isTokenBlacklisted 
} from '@/utils/auth';
import { query } from '@/config/database';
import { User } from '@/types';
import { logger } from '@/utils/logger';

// 认证中间件
export const authenticate = async (
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      res.status(401).json({
        success: false,
        message: '未提供认证令牌',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 检查令牌是否在黑名单中
    if (isTokenBlacklisted(token)) {
      res.status(401).json({
        success: false,
        message: '令牌已失效',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 验证令牌
    const payload = verifyToken(token);
    if (!payload) {
      res.status(401).json({
        success: false,
        message: '无效的认证令牌',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 获取用户信息
    const users = await query<User>(
      'SELECT id, username, email, real_name, role, status, avatar, phone, department, last_login FROM users WHERE id = ? AND status = "active"',
      [payload.userId]
    );

    if (users.length === 0) {
      res.status(401).json({
        success: false,
        message: '用户不存在或已被禁用',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // 将用户信息添加到请求对象
    req.user = users[0];
    next();
  } catch (error) {
    logger.error('认证中间件错误', error);
    res.status(500).json({
      success: false,
      message: '认证服务错误',
      timestamp: new Date().toISOString()
    });
  }
};

// 角色权限中间件
export const authorize = (requiredRole: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        message: '用户未认证',
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (!checkPermission(req.user.role, requiredRole)) {
      res.status(403).json({
        success: false,
        message: '权限不足',
        timestamp: new Date().toISOString()
      });
      return;
    }

    next();
  };
};

// 资源权限中间件
export const authorizeResource = (
  resourceType: 'website' | 'server' | 'domain' | 'project' | 'customer',
  requiredPermission: 'read' | 'write' | 'admin' = 'read'
) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          message: '用户未认证',
          timestamp: new Date().toISOString()
        });
        return;
      }

      // 超级管理员拥有所有权限
      if (req.user.role === 'super_admin') {
        next();
        return;
      }

      const resourceId = parseInt(req.params.id);
      if (isNaN(resourceId)) {
        res.status(400).json({
          success: false,
          message: '无效的资源ID',
          timestamp: new Date().toISOString()
        });
        return;
      }

      // 检查用户是否有该资源的权限
      const permissions = await query(
        `SELECT permission FROM user_permissions 
         WHERE user_id = ? AND resource_type = ? AND resource_id = ? 
         AND (expires_at IS NULL OR expires_at > NOW())`,
        [req.user.id, resourceType, resourceId]
      );

      if (permissions.length === 0) {
        res.status(403).json({
          success: false,
          message: '无权访问该资源',
          timestamp: new Date().toISOString()
        });
        return;
      }

      const userPermission = permissions[0].permission;
      const permissionLevels = {
        'read': 1,
        'write': 2,
        'admin': 3
      };

      const userLevel = permissionLevels[userPermission as keyof typeof permissionLevels] || 0;
      const requiredLevel = permissionLevels[requiredPermission];

      if (userLevel < requiredLevel) {
        res.status(403).json({
          success: false,
          message: '权限级别不足',
          timestamp: new Date().toISOString()
        });
        return;
      }

      next();
    } catch (error) {
      logger.error('资源权限检查错误', error);
      res.status(500).json({
        success: false,
        message: '权限检查服务错误',
        timestamp: new Date().toISOString()
      });
    }
  };
};

// 可选认证中间件（用于某些可以匿名访问但需要用户信息的接口）
export const optionalAuthenticate = async (
  req: Request, 
  res: Response, 
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      next();
      return;
    }

    // 检查令牌是否在黑名单中
    if (isTokenBlacklisted(token)) {
      next();
      return;
    }

    // 验证令牌
    const payload = verifyToken(token);
    if (!payload) {
      next();
      return;
    }

    // 获取用户信息
    const users = await query<User>(
      'SELECT id, username, email, real_name, role, status, avatar, phone, department, last_login FROM users WHERE id = ? AND status = "active"',
      [payload.userId]
    );

    if (users.length > 0) {
      req.user = users[0];
    }

    next();
  } catch (error) {
    logger.error('可选认证中间件错误', error);
    next();
  }
};

// 检查用户状态中间件
export const checkUserStatus = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({
      success: false,
      message: '用户未认证',
      timestamp: new Date().toISOString()
    });
    return;
  }

  if (req.user.status !== 'active') {
    res.status(403).json({
      success: false,
      message: '用户账户已被禁用',
      timestamp: new Date().toISOString()
    });
    return;
  }

  next();
};

// 为了兼容性，添加别名
export const authenticateToken = authenticate;
