import * as Redis from 'redis';
import { config } from '@/config';
import { logger } from '@/utils/logger';

// Redis客户端实例
let redisClient: any | null = null;

// 缓存键前缀
const CACHE_PREFIX = 'sitemanager:';

// 默认缓存时间（秒）
const DEFAULT_TTL = 300; // 5分钟

// 初始化Redis连接
export const initRedis = async (): Promise<void> => {
  try {
    // 暂时使用内存缓存替代Redis，避免配置问题
    logger.info('使用内存缓存模式');
    redisClient = new Map(); // 简单的内存缓存
  } catch (error) {
    logger.error('缓存初始化失败:', error);
    redisClient = null;
  }
};

// 关闭缓存连接
export const closeRedis = async (): Promise<void> => {
  if (redisClient) {
    redisClient.clear();
    redisClient = null;
    logger.info('缓存已清理');
  }
};

// 生成缓存键
const getCacheKey = (key: string): string => {
  return `${CACHE_PREFIX}${key}`;
};

// 设置缓存
export const setCache = async (
  key: string,
  value: any,
  ttl: number = DEFAULT_TTL
): Promise<boolean> => {
  if (!redisClient) {
    logger.warn('缓存未连接，跳过缓存设置');
    return false;
  }

  try {
    const cacheKey = getCacheKey(key);
    const cacheData = {
      value,
      timestamp: Date.now(),
      ttl
    };

    redisClient.set(cacheKey, cacheData);
    logger.debug(`缓存设置成功: ${cacheKey}`);
    return true;
  } catch (error) {
    logger.error('设置缓存失败:', { key, error });
    return false;
  }
};

// 获取缓存
export const getCache = async <T = any>(key: string): Promise<T | null> => {
  if (!redisClient) {
    logger.warn('缓存未连接，跳过缓存获取');
    return null;
  }

  try {
    const cacheKey = getCacheKey(key);
    const cacheData = redisClient.get(cacheKey);

    if (cacheData) {
      // 检查是否过期
      if (Date.now() - cacheData.timestamp > cacheData.ttl) {
        redisClient.delete(cacheKey);
        logger.debug(`缓存已过期: ${cacheKey}`);
        return null;
      }

      logger.debug(`缓存命中: ${cacheKey}`);
      return cacheData.value as T;
    }

    logger.debug(`缓存未命中: ${cacheKey}`);
    return null;
  } catch (error) {
    logger.error('获取缓存失败:', { key, error });
    return null;
  }
};

// 删除缓存
export const deleteCache = async (key: string): Promise<boolean> => {
  if (!redisClient) {
    logger.warn('缓存未连接，跳过缓存删除');
    return false;
  }

  try {
    if (key.includes('*')) {
      // 模式匹配删除
      let count = 0;
      for (const cacheKey of redisClient.keys()) {
        if (cacheKey.includes(key.replace('*', ''))) {
          redisClient.delete(cacheKey);
          count++;
        }
      }
      logger.debug(`批量删除缓存: ${key}, 删除数量: ${count}`);
      return count > 0;
    } else {
      const cacheKey = getCacheKey(key);
      const result = redisClient.delete(cacheKey);
      logger.debug(`缓存删除: ${cacheKey}, 结果: ${result}`);
      return result;
    }
  } catch (error) {
    logger.error('删除缓存失败:', { key, error });
    return false;
  }
};

// 批量删除缓存（按模式）
export const deleteCachePattern = async (pattern: string): Promise<number> => {
  return (await deleteCache(pattern)) ? 1 : 0;
};

// 检查缓存是否存在
export const existsCache = async (key: string): Promise<boolean> => {
  if (!redisClient) {
    return false;
  }

  try {
    const cacheKey = getCacheKey(key);
    return redisClient.has(cacheKey);
  } catch (error) {
    logger.error('检查缓存存在性失败:', { key, error });
    return false;
  }
};

// 设置缓存过期时间
export const expireCache = async (key: string, ttl: number): Promise<boolean> => {
  // 内存缓存模式下，重新设置缓存即可
  const value = await getCache(key);
  if (value) {
    return setCache(key, value, ttl);
  }
  return false;
};

// 获取缓存剩余时间
export const getTTL = async (key: string): Promise<number> => {
  if (!redisClient) {
    return -1;
  }

  try {
    const cacheKey = getCacheKey(key);
    const cacheData = redisClient.get(cacheKey);
    if (cacheData) {
      const remaining = cacheData.ttl - (Date.now() - cacheData.timestamp);
      return Math.max(0, Math.floor(remaining / 1000));
    }
    return -1;
  } catch (error) {
    logger.error('获取缓存TTL失败:', { key, error });
    return -1;
  }
};

// 缓存装饰器函数
export const withCache = <T extends any[], R>(
  cacheKey: string | ((...args: T) => string),
  ttl: number = DEFAULT_TTL
) => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    const method = descriptor.value;

    descriptor.value = async function (...args: T): Promise<R> {
      const key = typeof cacheKey === 'function' ? cacheKey(...args) : cacheKey;
      
      // 尝试从缓存获取
      const cachedResult = await getCache<R>(key);
      if (cachedResult !== null) {
        return cachedResult;
      }

      // 执行原方法
      const result = await method.apply(this, args);
      
      // 设置缓存
      await setCache(key, result, ttl);
      
      return result;
    };
  };
};

// 常用缓存键生成器
export const CacheKeys = {
  // 用户相关
  user: (id: number) => `user:${id}`,
  userList: (page: number, limit: number, search?: string) => 
    `users:${page}:${limit}:${search || 'all'}`,
  
  // 网站相关
  website: (id: number) => `website:${id}`,
  websiteList: (page: number, limit: number, filters?: any) => 
    `websites:${page}:${limit}:${JSON.stringify(filters || {})}`,
  
  // 项目相关
  project: (id: number) => `project:${id}`,
  projectList: (page: number, limit: number, filters?: any) => 
    `projects:${page}:${limit}:${JSON.stringify(filters || {})}`,
  
  // 客户相关
  customer: (id: number) => `customer:${id}`,
  customerList: (page: number, limit: number, search?: string) => 
    `customers:${page}:${limit}:${search || 'all'}`,
  
  // 统计数据
  stats: (type: string, period?: string) => 
    `stats:${type}:${period || 'all'}`,
};

// 缓存预热函数
export const warmupCache = async (): Promise<void> => {
  logger.info('开始缓存预热...');

  try {
    // 这里可以添加需要预热的缓存数据
    // 例如：常用的统计数据、配置信息等

    logger.info('缓存预热完成');
  } catch (error) {
    logger.error('缓存预热失败:', error);
  }
};

// 清理过期缓存
export const cleanupExpiredCache = async (): Promise<void> => {
  logger.info('开始清理过期缓存...');

  try {
    if (redisClient) {
      let cleanedCount = 0;
      const now = Date.now();

      for (const [key, data] of redisClient.entries()) {
        if (now - data.timestamp > data.ttl) {
          redisClient.delete(key);
          cleanedCount++;
        }
      }

      logger.info(`清理过期缓存完成，清理数量: ${cleanedCount}`);
    }
  } catch (error) {
    logger.error('清理缓存失败:', error);
  }
};
