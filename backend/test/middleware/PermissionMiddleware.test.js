const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');
const PermissionMiddleware = require('../../middleware/PermissionMiddleware');
const PermissionService = require('../../services/PermissionService');
const AuditService = require('../../services/AuditService');

// 模拟依赖服务
jest.mock('../../services/PermissionService');
jest.mock('../../services/AuditService');

describe('PermissionMiddleware 权限中间件测试', () => {
  let mockReq, mockRes, mockNext;
  let permissionService, auditService;

  beforeEach(() => {
    // 创建模拟的请求、响应和next函数
    mockReq = {
      user: { id: 1, username: 'testuser', role: 'admin' },
      ip: '*************',
      get: jest.fn().mockReturnValue('Mozilla/5.0'),
      params: {},
      body: {},
      query: {}
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis()
    };

    mockNext = jest.fn();

    // 创建服务实例的模拟
    permissionService = new PermissionService();
    auditService = new AuditService();

    // 清除所有模拟调用
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('requirePermission 权限验证中间件', () => {
    test('应该允许拥有权限的用户通过', async () => {
      // 模拟用户拥有所需权限
      permissionService.hasPermission.mockResolvedValue(true);
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      const middleware = PermissionMiddleware.requirePermission('user.create');
      await middleware(mockReq, mockRes, mockNext);

      expect(permissionService.hasPermission).toHaveBeenCalledWith(1, 'user.create');
      expect(auditService.logPermissionAccess).toHaveBeenCalledWith(
        1, 'user.create', 'granted', expect.any(Object)
      );
      expect(mockNext).toHaveBeenCalled();
      expect(mockRes.status).not.toHaveBeenCalled();
    });

    test('应该拒绝没有权限的用户', async () => {
      // 模拟用户没有所需权限
      permissionService.hasPermission.mockResolvedValue(false);
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      const middleware = PermissionMiddleware.requirePermission('user.delete');
      await middleware(mockReq, mockRes, mockNext);

      expect(permissionService.hasPermission).toHaveBeenCalledWith(1, 'user.delete');
      expect(auditService.logPermissionAccess).toHaveBeenCalledWith(
        1, 'user.delete', 'denied', expect.any(Object)
      );
      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法执行此操作',
          details: {
            required: ['user.delete'],
            resource: 'user.delete'
          }
        },
        timestamp: expect.any(String)
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该支持多权限验证', async () => {
      permissionService.hasPermission.mockResolvedValue(true);
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      const middleware = PermissionMiddleware.requirePermission(['user.create', 'user.read.any']);
      await middleware(mockReq, mockRes, mockNext);

      expect(permissionService.hasPermission).toHaveBeenCalledWith(1, ['user.create', 'user.read.any']);
      expect(mockNext).toHaveBeenCalled();
    });

    test('应该处理未认证用户', async () => {
      mockReq.user = null;

      const middleware = PermissionMiddleware.requirePermission('user.create');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(401);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: '请先登录后再访问此资源'
        },
        timestamp: expect.any(String)
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('应该处理权限检查异常', async () => {
      permissionService.hasPermission.mockRejectedValue(new Error('数据库连接失败'));

      const middleware = PermissionMiddleware.requirePermission('user.create');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'PERMISSION_CHECK_ERROR',
          message: '权限验证失败，请稍后重试'
        },
        timestamp: expect.any(String)
      });
    });
  });

  describe('requireRole 角色验证中间件', () => {
    test('应该允许拥有角色的用户通过', async () => {
      permissionService.hasRole.mockResolvedValue(true);
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      const middleware = PermissionMiddleware.requireRole('admin');
      await middleware(mockReq, mockRes, mockNext);

      expect(permissionService.hasRole).toHaveBeenCalledWith(1, 'admin');
      expect(mockNext).toHaveBeenCalled();
    });

    test('应该拒绝没有角色的用户', async () => {
      permissionService.hasRole.mockResolvedValue(false);
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      const middleware = PermissionMiddleware.requireRole('super_admin');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INSUFFICIENT_ROLE',
          message: '用户角色权限不足',
          details: {
            required: ['super_admin'],
            current: 'admin'
          }
        },
        timestamp: expect.any(String)
      });
    });

    test('应该支持多角色验证', async () => {
      permissionService.hasRole.mockResolvedValue(true);

      const middleware = PermissionMiddleware.requireRole(['admin', 'super_admin']);
      await middleware(mockReq, mockRes, mockNext);

      expect(permissionService.hasRole).toHaveBeenCalledWith(1, ['admin', 'super_admin']);
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('requireOwnership 资源所有权验证中间件', () => {
    test('应该允许资源所有者访问', async () => {
      // 模拟资源所有权检查函数
      const mockOwnershipCheck = jest.fn().mockResolvedValue(1); // 返回用户ID
      permissionService.hasPermission.mockResolvedValue(false); // 没有全局权限
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      mockReq.params.id = '123';

      const middleware = PermissionMiddleware.requireOwnership(mockOwnershipCheck, 'server.read');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockOwnershipCheck).toHaveBeenCalledWith(mockReq);
      expect(mockNext).toHaveBeenCalled();
    });

    test('应该拒绝非资源所有者访问', async () => {
      const mockOwnershipCheck = jest.fn().mockResolvedValue(2); // 返回其他用户ID
      permissionService.hasPermission.mockResolvedValue(false);
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      const middleware = PermissionMiddleware.requireOwnership(mockOwnershipCheck, 'server.read');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(403);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法访问此资源'
        },
        timestamp: expect.any(String)
      });
    });

    test('应该允许拥有全局权限的用户访问', async () => {
      const mockOwnershipCheck = jest.fn().mockResolvedValue(2);
      permissionService.hasPermission.mockResolvedValue(true); // 有全局权限
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      const middleware = PermissionMiddleware.requireOwnership(mockOwnershipCheck, 'server.read');
      await middleware(mockReq, mockRes, mockNext);

      expect(permissionService.hasPermission).toHaveBeenCalledWith(1, 'server.read.any');
      expect(mockNext).toHaveBeenCalled();
    });

    test('应该处理资源不存在的情况', async () => {
      const mockOwnershipCheck = jest.fn().mockResolvedValue(null);
      permissionService.hasPermission.mockResolvedValue(false);

      const middleware = PermissionMiddleware.requireOwnership(mockOwnershipCheck, 'server.read');
      await middleware(mockReq, mockRes, mockNext);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(mockRes.json).toHaveBeenCalledWith({
        success: false,
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: '请求的资源不存在'
        },
        timestamp: expect.any(String)
      });
    });
  });

  describe('权限中间件组合使用', () => {
    test('应该支持权限和角色的组合验证', async () => {
      permissionService.hasRole.mockResolvedValue(true);
      permissionService.hasPermission.mockResolvedValue(true);
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      // 模拟组合中间件
      const roleMiddleware = PermissionMiddleware.requireRole('admin');
      const permissionMiddleware = PermissionMiddleware.requirePermission('user.delete');

      // 先执行角色验证
      await roleMiddleware(mockReq, mockRes, mockNext);
      expect(mockNext).toHaveBeenCalledTimes(1);

      // 再执行权限验证
      mockNext.mockClear();
      await permissionMiddleware(mockReq, mockRes, mockNext);
      expect(mockNext).toHaveBeenCalledTimes(1);
    });
  });

  describe('审计日志记录', () => {
    test('应该记录权限验证成功的日志', async () => {
      permissionService.hasPermission.mockResolvedValue(true);
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      const middleware = PermissionMiddleware.requirePermission('user.create');
      await middleware(mockReq, mockRes, mockNext);

      expect(auditService.logPermissionAccess).toHaveBeenCalledWith(
        1,
        'user.create',
        'granted',
        expect.objectContaining({
          ip: '*************',
          userAgent: 'Mozilla/5.0',
          timestamp: expect.any(Date)
        })
      );
    });

    test('应该记录权限验证失败的日志', async () => {
      permissionService.hasPermission.mockResolvedValue(false);
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      const middleware = PermissionMiddleware.requirePermission('user.delete');
      await middleware(mockReq, mockRes, mockNext);

      expect(auditService.logPermissionAccess).toHaveBeenCalledWith(
        1,
        'user.delete',
        'denied',
        expect.objectContaining({
          ip: '*************',
          userAgent: 'Mozilla/5.0',
          timestamp: expect.any(Date)
        })
      );
    });

    test('应该处理审计日志记录失败', async () => {
      permissionService.hasPermission.mockResolvedValue(true);
      auditService.logPermissionAccess = jest.fn().mockRejectedValue(new Error('日志记录失败'));

      const middleware = PermissionMiddleware.requirePermission('user.create');
      
      // 即使日志记录失败，中间件也应该正常工作
      await middleware(mockReq, mockRes, mockNext);
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('性能和缓存', () => {
    test('应该在短时间内完成权限验证', async () => {
      permissionService.hasPermission.mockResolvedValue(true);
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      const startTime = Date.now();
      const middleware = PermissionMiddleware.requirePermission('user.create');
      await middleware(mockReq, mockRes, mockNext);
      const endTime = Date.now();

      // 权限验证应该在50ms内完成
      expect(endTime - startTime).toBeLessThan(50);
    });

    test('应该支持权限验证结果缓存', async () => {
      permissionService.hasPermission.mockResolvedValue(true);
      auditService.logPermissionAccess = jest.fn().mockResolvedValue();

      const middleware = PermissionMiddleware.requirePermission('user.create');
      
      // 第一次调用
      await middleware(mockReq, mockRes, mockNext);
      
      // 第二次调用相同权限
      mockNext.mockClear();
      await middleware(mockReq, mockRes, mockNext);

      // 验证缓存是否生效（具体实现取决于缓存策略）
      expect(mockNext).toHaveBeenCalledTimes(1);
    });
  });
});