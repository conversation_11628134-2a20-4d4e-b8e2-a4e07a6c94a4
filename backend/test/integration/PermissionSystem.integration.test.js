const { describe, test, expect, beforeAll, afterAll, beforeEach, afterEach } = require('@jest/globals');
const request = require('supertest');
const app = require('../../simple-server');
const db = require('../../utils/db');
const jwt = require('jsonwebtoken');

describe('权限系统集成测试', () => {
  let adminToken, userToken, testUserId, testAdminId;
  const JWT_SECRET = process.env.JWT_SECRET || 'test-secret-key';

  beforeAll(async () => {
    // 创建测试用户
    const [adminResult] = await db.execute(`
      INSERT INTO users (username, email, password, role, created_at)
      VALUES (?, ?, ?, ?, NOW())
    `, ['test_admin', '<EMAIL>', 'hashed_password', 'admin']);
    testAdminId = adminResult.insertId;

    const [userResult] = await db.execute(`
      INSERT INTO users (username, email, password, role, created_at)
      VALUES (?, ?, ?, ?, NOW())
    `, ['test_user', '<EMAIL>', 'hashed_password', 'user']);
    testUserId = userResult.insertId;

    // 生成测试JWT令牌
    adminToken = jwt.sign(
      { id: testAdminId, username: 'test_admin', role: 'admin' },
      JWT_SECRET,
      { expiresIn: '1h' }
    );

    userToken = jwt.sign(
      { id: testUserId, username: 'test_user', role: 'user' },
      JWT_SECRET,
      { expiresIn: '1h' }
    );
  });

  afterAll(async () => {
    // 清理测试数据
    await db.execute('DELETE FROM users WHERE id IN (?, ?)', [testAdminId, testUserId]);
    await db.execute('DELETE FROM audit_logs WHERE user_id IN (?, ?)', [testAdminId, testUserId]);
    await db.end();
  });

  beforeEach(async () => {
    // 清理审计日志
    await db.execute('DELETE FROM audit_logs WHERE user_id IN (?, ?)', [testAdminId, testUserId]);
  });

  describe('用户管理API权限控制', () => {
    test('管理员应该能够查看所有用户', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.users)).toBe(true);

      // 验证审计日志记录
      const [auditLogs] = await db.execute(`
        SELECT * FROM audit_logs 
        WHERE user_id = ? AND action = 'permission_access' AND resource = 'user.read.any'
      `, [testAdminId]);
      expect(auditLogs.length).toBe(1);
      expect(auditLogs[0].result).toBe('granted');
    });

    test('普通用户不应该能够查看所有用户', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INSUFFICIENT_PERMISSIONS');

      // 验证审计日志记录
      const [auditLogs] = await db.execute(`
        SELECT * FROM audit_logs 
        WHERE user_id = ? AND action = 'permission_access' AND resource = 'user.read.any'
      `, [testUserId]);
      expect(auditLogs.length).toBe(1);
      expect(auditLogs[0].result).toBe('denied');
    });

    test('用户应该能够查看自己的信息', async () => {
      const response = await request(app)
        .get(`/api/v1/users/${testUserId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.id).toBe(testUserId);
    });

    test('用户不应该能够查看其他用户的信息', async () => {
      const response = await request(app)
        .get(`/api/v1/users/${testAdminId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INSUFFICIENT_PERMISSIONS');
    });

    test('管理员应该能够创建新用户', async () => {
      const newUser = {
        username: 'new_test_user',
        email: '<EMAIL>',
        password: 'password123',
        role: 'user'
      };

      const response = await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newUser)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.username).toBe(newUser.username);

      // 清理创建的用户
      await db.execute('DELETE FROM users WHERE username = ?', [newUser.username]);
    });

    test('普通用户不应该能够创建新用户', async () => {
      const newUser = {
        username: 'unauthorized_user',
        email: '<EMAIL>',
        password: 'password123',
        role: 'user'
      };

      const response = await request(app)
        .post('/api/v1/users')
        .set('Authorization', `Bearer ${userToken}`)
        .send(newUser)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INSUFFICIENT_PERMISSIONS');
    });
  });

  describe('服务器管理API权限控制', () => {
    let testServerId;

    beforeEach(async () => {
      // 创建测试服务器
      const [result] = await db.execute(`
        INSERT INTO servers (name, host, port, username, created_by, created_at)
        VALUES (?, ?, ?, ?, ?, NOW())
      `, ['test_server', '*************', 22, 'root', testUserId]);
      testServerId = result.insertId;

      // 创建资源所有权记录
      await db.execute(`
        INSERT INTO resource_ownership (resource_type, resource_id, user_id, created_at)
        VALUES (?, ?, ?, NOW())
      `, ['server', testServerId.toString(), testUserId]);
    });

    afterEach(async () => {
      // 清理测试服务器
      if (testServerId) {
        await db.execute('DELETE FROM resource_ownership WHERE resource_type = ? AND resource_id = ?', 
          ['server', testServerId.toString()]);
        await db.execute('DELETE FROM servers WHERE id = ?', [testServerId]);
      }
    });

    test('管理员应该能够查看所有服务器', async () => {
      const response = await request(app)
        .get('/api/v1/servers')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.servers)).toBe(true);
    });

    test('用户应该只能查看自己的服务器', async () => {
      const response = await request(app)
        .get('/api/v1/servers')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      const userServers = response.body.data.servers.filter(s => s.created_by === testUserId);
      expect(userServers.length).toBeGreaterThan(0);
    });

    test('用户应该能够访问自己创建的服务器', async () => {
      const response = await request(app)
        .get(`/api/v1/servers/${testServerId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.server.id).toBe(testServerId);
    });

    test('用户应该能够创建新服务器', async () => {
      const newServer = {
        name: 'user_server',
        host: '*************',
        port: 22,
        username: 'ubuntu'
      };

      const response = await request(app)
        .post('/api/v1/servers')
        .set('Authorization', `Bearer ${userToken}`)
        .send(newServer)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.server.name).toBe(newServer.name);

      // 验证资源所有权记录
      const [ownership] = await db.execute(`
        SELECT * FROM resource_ownership 
        WHERE resource_type = 'server' AND resource_id = ? AND user_id = ?
      `, [response.body.data.server.id.toString(), testUserId]);
      expect(ownership.length).toBe(1);

      // 清理创建的服务器
      const serverId = response.body.data.server.id;
      await db.execute('DELETE FROM resource_ownership WHERE resource_type = ? AND resource_id = ?', 
        ['server', serverId.toString()]);
      await db.execute('DELETE FROM servers WHERE id = ?', [serverId]);
    });
  });

  describe('网站管理API权限控制', () => {
    let testWebsiteId;

    beforeEach(async () => {
      // 创建测试网站
      const [result] = await db.execute(`
        INSERT INTO websites (name, url, created_by, created_at)
        VALUES (?, ?, ?, NOW())
      `, ['test_website', 'https://test.example.com', testUserId]);
      testWebsiteId = result.insertId;

      // 创建资源所有权记录
      await db.execute(`
        INSERT INTO resource_ownership (resource_type, resource_id, user_id, created_at)
        VALUES (?, ?, ?, NOW())
      `, ['website', testWebsiteId.toString(), testUserId]);
    });

    afterEach(async () => {
      // 清理测试网站
      if (testWebsiteId) {
        await db.execute('DELETE FROM resource_ownership WHERE resource_type = ? AND resource_id = ?', 
          ['website', testWebsiteId.toString()]);
        await db.execute('DELETE FROM websites WHERE id = ?', [testWebsiteId]);
      }
    });

    test('管理员应该能够查看所有网站', async () => {
      const response = await request(app)
        .get('/api/v1/websites')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.websites)).toBe(true);
    });

    test('用户应该能够创建新网站', async () => {
      const newWebsite = {
        name: 'user_website',
        url: 'https://user.example.com',
        description: '用户创建的测试网站'
      };

      const response = await request(app)
        .post('/api/v1/websites')
        .set('Authorization', `Bearer ${userToken}`)
        .send(newWebsite)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.website.name).toBe(newWebsite.name);

      // 清理创建的网站
      const websiteId = response.body.data.website.id;
      await db.execute('DELETE FROM resource_ownership WHERE resource_type = ? AND resource_id = ?', 
        ['website', websiteId.toString()]);
      await db.execute('DELETE FROM websites WHERE id = ?', [websiteId]);
    });

    test('用户应该能够更新自己的网站', async () => {
      const updateData = {
        name: 'updated_test_website',
        description: '更新后的网站描述'
      };

      const response = await request(app)
        .put(`/api/v1/websites/${testWebsiteId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('用户不应该能够删除其他用户的网站', async () => {
      // 创建另一个用户的网站
      const [result] = await db.execute(`
        INSERT INTO websites (name, url, created_by, created_at)
        VALUES (?, ?, ?, NOW())
      `, ['admin_website', 'https://admin.example.com', testAdminId]);
      const adminWebsiteId = result.insertId;

      await db.execute(`
        INSERT INTO resource_ownership (resource_type, resource_id, user_id, created_at)
        VALUES (?, ?, ?, NOW())
      `, ['website', adminWebsiteId.toString(), testAdminId]);

      const response = await request(app)
        .delete(`/api/v1/websites/${adminWebsiteId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INSUFFICIENT_PERMISSIONS');

      // 清理
      await db.execute('DELETE FROM resource_ownership WHERE resource_type = ? AND resource_id = ?', 
        ['website', adminWebsiteId.toString()]);
      await db.execute('DELETE FROM websites WHERE id = ?', [adminWebsiteId]);
    });
  });

  describe('权限管理API', () => {
    test('管理员应该能够查看权限列表', async () => {
      const response = await request(app)
        .get('/api/v1/auth/permissions')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.permissions)).toBe(true);
    });

    test('普通用户不应该能够查看权限列表', async () => {
      const response = await request(app)
        .get('/api/v1/auth/permissions')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INSUFFICIENT_PERMISSIONS');
    });

    test('管理员应该能够查看用户权限', async () => {
      const response = await request(app)
        .get(`/api/v1/auth/users/${testUserId}/permissions`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.userId).toBe(testUserId);
      expect(Array.isArray(response.body.data.effectivePermissions)).toBe(true);
    });

    test('管理员应该能够更新用户权限', async () => {
      const permissionUpdate = {
        customPermissions: [
          { code: 'server.create', granted: true },
          { code: 'website.delete.any', granted: true }
        ]
      };

      const response = await request(app)
        .put(`/api/v1/auth/users/${testUserId}/permissions`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(permissionUpdate)
        .expect(200);

      expect(response.body.success).toBe(true);

      // 验证权限变更日志
      const [changeLogs] = await db.execute(`
        SELECT * FROM audit_logs 
        WHERE user_id = ? AND action = 'permission_change'
      `, [testAdminId]);
      expect(changeLogs.length).toBe(1);
    });

    test('普通用户不应该能够更新其他用户权限', async () => {
      const permissionUpdate = {
        customPermissions: [
          { code: 'system.backup', granted: true }
        ]
      };

      const response = await request(app)
        .put(`/api/v1/auth/users/${testAdminId}/permissions`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(permissionUpdate)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INSUFFICIENT_PERMISSIONS');
    });
  });

  describe('审计日志API', () => {
    beforeEach(async () => {
      // 创建一些测试审计日志
      await db.execute(`
        INSERT INTO audit_logs (user_id, action, resource, result, ip_address, created_at)
        VALUES 
        (?, 'permission_access', 'user.create', 'granted', '*************', NOW()),
        (?, 'permission_access', 'user.delete', 'denied', '*************', NOW()),
        (?, 'permission_change', 'user_permissions', 'success', '*************', NOW())
      `, [testAdminId, testUserId, testAdminId]);
    });

    test('管理员应该能够查看审计日志', async () => {
      const response = await request(app)
        .get('/api/v1/audit/logs')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.logs)).toBe(true);
      expect(response.body.data.pagination).toBeDefined();
    });

    test('普通用户不应该能够查看审计日志', async () => {
      const response = await request(app)
        .get('/api/v1/audit/logs')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('INSUFFICIENT_PERMISSIONS');
    });

    test('管理员应该能够按用户筛选审计日志', async () => {
      const response = await request(app)
        .get(`/api/v1/audit/logs?userId=${testUserId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      const logs = response.body.data.logs;
      logs.forEach(log => {
        expect(log.user_id).toBe(testUserId);
      });
    });

    test('管理员应该能够获取审计统计', async () => {
      const response = await request(app)
        .get('/api/v1/audit/stats')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.totalEvents).toBeGreaterThan(0);
      expect(response.body.data.permissionAccess).toBeDefined();
      expect(response.body.data.successRate).toBeDefined();
    });
  });

  describe('权限缓存和性能', () => {
    test('权限验证应该在合理时间内完成', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      expect(response.body.success).toBe(true);
      expect(responseTime).toBeLessThan(200); // 应该在200ms内完成
    });

    test('重复权限检查应该利用缓存', async () => {
      // 第一次请求
      const start1 = Date.now();
      await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);
      const time1 = Date.now() - start1;

      // 第二次请求（应该使用缓存）
      const start2 = Date.now();
      await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);
      const time2 = Date.now() - start2;

      // 第二次请求应该更快（使用缓存）
      expect(time2).toBeLessThanOrEqual(time1);
    });
  });

  describe('错误处理和边界情况', () => {
    test('应该处理无效的JWT令牌', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', 'Bearer invalid_token')
        .expect(403);

      expect(response.body.success).toBe(false);
    });

    test('应该处理过期的JWT令牌', async () => {
      const expiredToken = jwt.sign(
        { id: testUserId, username: 'test_user', role: 'user' },
        JWT_SECRET,
        { expiresIn: '-1h' } // 已过期
      );

      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
    });

    test('应该处理缺失的Authorization头', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .expect(401);

      expect(response.body.success).toBe(false);
    });

    test('应该处理不存在的资源访问', async () => {
      const response = await request(app)
        .get('/api/v1/servers/99999')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);

      expect(response.body.success).toBe(false);
    });
  });

  describe('安全性测试', () => {
    test('应该防止权限提升攻击', async () => {
      // 尝试通过修改JWT payload进行权限提升
      const maliciousToken = jwt.sign(
        { id: testUserId, username: 'test_user', role: 'super_admin' }, // 尝试提升为超级管理员
        'wrong_secret', // 使用错误的密钥
        { expiresIn: '1h' }
      );

      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${maliciousToken}`)
        .expect(403);

      expect(response.body.success).toBe(false);
    });

    test('应该记录可疑的权限访问尝试', async () => {
      // 多次尝试访问无权限的资源
      for (let i = 0; i < 5; i++) {
        await request(app)
          .delete(`/api/v1/users/${testAdminId}`)
          .set('Authorization', `Bearer ${userToken}`)
          .expect(403);
      }

      // 验证审计日志记录了这些尝试
      const [suspiciousLogs] = await db.execute(`
        SELECT COUNT(*) as count FROM audit_logs 
        WHERE user_id = ? AND result = 'denied' AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
      `, [testUserId]);

      expect(suspiciousLogs[0].count).toBe(5);
    });

    test('应该防止SQL注入攻击', async () => {
      const maliciousUserId = "1' OR '1'='1";
      
      const response = await request(app)
        .get(`/api/v1/users/${maliciousUserId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });
});