/**
 * 权限系统性能测试设置文件
 * 配置性能测试环境和工具
 */

const { performance } = require('perf_hooks');

// 性能测试工具类
class PerformanceTestUtils {
  constructor() {
    this.measurements = new Map();
    this.thresholds = {
      permissionCheck: 50, // 权限检查应在50ms内完成
      cacheAccess: 5,      // 缓存访问应在5ms内完成
      middlewareProcess: 50, // 中间件处理应在50ms内完成
      auditLogWrite: 10,   // 审计日志写入应在10ms内完成
      batchOperation: 1000 // 批量操作应在1秒内完成
    };
  }

  // 开始性能测量
  startMeasurement(name) {
    this.measurements.set(name, {
      startTime: performance.now(),
      endTime: null,
      duration: null
    });
  }

  // 结束性能测量
  endMeasurement(name) {
    const measurement = this.measurements.get(name);
    if (!measurement) {
      throw new Error(`未找到性能测量: ${name}`);
    }

    measurement.endTime = performance.now();
    measurement.duration = measurement.endTime - measurement.startTime;
    
    return measurement.duration;
  }

  // 获取测量结果
  getMeasurement(name) {
    return this.measurements.get(name);
  }

  // 验证性能阈值
  assertPerformance(name, threshold = null) {
    const measurement = this.measurements.get(name);
    if (!measurement || measurement.duration === null) {
      throw new Error(`性能测量未完成: ${name}`);
    }

    const actualThreshold = threshold || this.thresholds[name] || 100;
    
    if (measurement.duration > actualThreshold) {
      throw new Error(
        `性能测试失败: ${name} 耗时 ${measurement.duration.toFixed(2)}ms，超过阈值 ${actualThreshold}ms`
      );
    }

    return true;
  }

  // 批量性能测试
  async batchPerformanceTest(name, testFunction, iterations = 100) {
    const results = [];
    
    this.startMeasurement(`${name}_batch`);
    
    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      await testFunction(i);
      const end = performance.now();
      results.push(end - start);
    }
    
    this.endMeasurement(`${name}_batch`);
    
    const totalTime = this.getMeasurement(`${name}_batch`).duration;
    const avgTime = results.reduce((sum, time) => sum + time, 0) / results.length;
    const minTime = Math.min(...results);
    const maxTime = Math.max(...results);
    const medianTime = results.sort((a, b) => a - b)[Math.floor(results.length / 2)];
    
    return {
      totalTime,
      avgTime,
      minTime,
      maxTime,
      medianTime,
      iterations,
      results
    };
  }

  // 并发性能测试
  async concurrentPerformanceTest(name, testFunction, concurrency = 10) {
    const promises = [];
    
    this.startMeasurement(`${name}_concurrent`);
    
    for (let i = 0; i < concurrency; i++) {
      promises.push(testFunction(i));
    }
    
    const results = await Promise.all(promises);
    
    this.endMeasurement(`${name}_concurrent`);
    
    return {
      totalTime: this.getMeasurement(`${name}_concurrent`).duration,
      concurrency,
      results
    };
  }

  // 内存使用测试
  measureMemoryUsage(name, testFunction) {
    const initialMemory = process.memoryUsage();
    
    return testFunction().then(result => {
      const finalMemory = process.memoryUsage();
      const memoryDiff = {
        heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
        heapTotal: finalMemory.heapTotal - initialMemory.heapTotal,
        external: finalMemory.external - initialMemory.external,
        rss: finalMemory.rss - initialMemory.rss
      };
      
      return {
        result,
        memoryUsage: memoryDiff,
        initialMemory,
        finalMemory
      };
    });
  }

  // 生成性能报告
  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      measurements: {},
      summary: {
        totalTests: this.measurements.size,
        passedTests: 0,
        failedTests: 0,
        avgDuration: 0
      }
    };

    let totalDuration = 0;
    
    for (const [name, measurement] of this.measurements) {
      if (measurement.duration !== null) {
        const threshold = this.thresholds[name] || 100;
        const passed = measurement.duration <= threshold;
        
        report.measurements[name] = {
          duration: measurement.duration,
          threshold,
          passed,
          status: passed ? 'PASS' : 'FAIL'
        };
        
        if (passed) {
          report.summary.passedTests++;
        } else {
          report.summary.failedTests++;
        }
        
        totalDuration += measurement.duration;
      }
    }
    
    report.summary.avgDuration = totalDuration / this.measurements.size;
    
    return report;
  }

  // 清理测量数据
  clear() {
    this.measurements.clear();
  }
}

// 性能测试断言扩展
expect.extend({
  toBeWithinPerformanceThreshold(received, threshold) {
    const pass = received <= threshold;
    
    if (pass) {
      return {
        message: () => `期望 ${received}ms 不在性能阈值 ${threshold}ms 内`,
        pass: true
      };
    } else {
      return {
        message: () => `期望 ${received}ms 在性能阈值 ${threshold}ms 内，但实际超出了 ${received - threshold}ms`,
        pass: false
      };
    }
  },

  toHaveGoodCacheHitRate(received, expectedRate = 0.95) {
    const pass = received >= expectedRate;
    
    if (pass) {
      return {
        message: () => `期望缓存命中率 ${received} 不达到 ${expectedRate}`,
        pass: true
      };
    } else {
      return {
        message: () => `期望缓存命中率至少为 ${expectedRate}，但实际为 ${received}`,
        pass: false
      };
    }
  },

  toSupportConcurrency(received, expectedConcurrency) {
    const pass = received >= expectedConcurrency;
    
    if (pass) {
      return {
        message: () => `期望并发数 ${received} 不支持 ${expectedConcurrency}`,
        pass: true
      };
    } else {
      return {
        message: () => `期望支持并发数至少为 ${expectedConcurrency}，但实际只支持 ${received}`,
        pass: false
      };
    }
  }
});

// 全局性能测试工具实例
global.performanceUtils = new PerformanceTestUtils();

// 性能测试钩子
beforeEach(() => {
  global.performanceUtils.clear();
});

afterEach(() => {
  // 可以在这里生成每个测试的性能报告
  if (process.env.GENERATE_PERFORMANCE_REPORT === 'true') {
    const report = global.performanceUtils.generateReport();
    console.log('性能测试报告:', JSON.stringify(report, null, 2));
  }
});

// 性能测试辅助函数
global.measurePerformance = async (name, testFunction, threshold = null) => {
  global.performanceUtils.startMeasurement(name);
  const result = await testFunction();
  const duration = global.performanceUtils.endMeasurement(name);
  
  if (threshold) {
    global.performanceUtils.assertPerformance(name, threshold);
  }
  
  return { result, duration };
};

// 批量性能测试辅助函数
global.batchPerformanceTest = async (name, testFunction, iterations = 100) => {
  return global.performanceUtils.batchPerformanceTest(name, testFunction, iterations);
};

// 并发性能测试辅助函数
global.concurrentPerformanceTest = async (name, testFunction, concurrency = 10) => {
  return global.performanceUtils.concurrentPerformanceTest(name, testFunction, concurrency);
};

// 内存使用测试辅助函数
global.measureMemoryUsage = async (name, testFunction) => {
  return global.performanceUtils.measureMemoryUsage(name, testFunction);
};

// 性能基准数据
global.PERFORMANCE_BENCHMARKS = {
  PERMISSION_CHECK_MAX_TIME: 50,      // 权限检查最大时间(ms)
  CACHE_ACCESS_MAX_TIME: 5,           // 缓存访问最大时间(ms)
  MIDDLEWARE_PROCESS_MAX_TIME: 50,    // 中间件处理最大时间(ms)
  AUDIT_LOG_WRITE_MAX_TIME: 10,       // 审计日志写入最大时间(ms)
  BATCH_OPERATION_MAX_TIME: 1000,     // 批量操作最大时间(ms)
  MIN_CACHE_HIT_RATE: 0.95,           // 最小缓存命中率
  MIN_CONCURRENT_USERS: 100,          // 最小并发用户数
  MAX_MEMORY_PER_USER: 1024,          // 每用户最大内存使用(bytes)
  MAX_ERROR_RATE: 0.01                // 最大错误率
};

console.log('权限系统性能测试环境已初始化');