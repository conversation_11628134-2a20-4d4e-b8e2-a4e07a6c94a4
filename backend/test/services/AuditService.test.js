const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');
const AuditService = require('../../services/AuditService');
const db = require('../../utils/db');

// 模拟数据库连接
jest.mock('../../utils/db');

describe('AuditService 审计日志服务测试', () => {
  let auditService;

  beforeEach(() => {
    auditService = new AuditService();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('logPermissionAccess 记录权限访问日志', () => {
    test('应该正确记录权限访问成功日志', async () => {
      const mockContext = {
        ip: '*************',
        userAgent: 'Mozilla/5.0',
        timestamp: new Date()
      };

      db.execute.mockResolvedValue([{ insertId: 1 }]);

      await auditService.logPermissionAccess(1, 'user.create', 'granted', mockContext);

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO audit_logs'),
        expect.arrayContaining([
          1, // user_id
          'permission_access', // action
          'user.create', // resource
          null, // resource_id
          'granted', // result
          expect.any(String), // details (JSON)
          '*************', // ip_address
          'Mozilla/5.0' // user_agent
        ])
      );
    });

    test('应该正确记录权限访问拒绝日志', async () => {
      const mockContext = {
        ip: '********',
        userAgent: 'Chrome/91.0',
        timestamp: new Date(),
        resource: 'user.delete',
        resourceId: '123'
      };

      db.execute.mockResolvedValue([{ insertId: 2 }]);

      await auditService.logPermissionAccess(1, 'user.delete', 'denied', mockContext);

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO audit_logs'),
        expect.arrayContaining([
          1,
          'permission_access',
          'user.delete',
          '123',
          'denied',
          expect.any(String),
          '********',
          'Chrome/91.0'
        ])
      );
    });

    test('应该处理匿名用户的权限访问日志', async () => {
      const mockContext = {
        ip: '*************',
        userAgent: 'Mozilla/5.0',
        timestamp: new Date()
      };

      db.execute.mockResolvedValue([{ insertId: 3 }]);

      await auditService.logPermissionAccess(null, 'public.access', 'granted', mockContext);

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO audit_logs'),
        expect.arrayContaining([
          null, // 匿名用户
          'permission_access',
          'public.access',
          null,
          'granted',
          expect.any(String),
          '*************',
          'Mozilla/5.0'
        ])
      );
    });

    test('应该处理数据库插入失败', async () => {
      const mockContext = { ip: '*************', userAgent: 'Mozilla/5.0' };
      
      db.execute.mockRejectedValue(new Error('数据库连接失败'));

      // 审计日志失败不应该影响主要功能
      await expect(auditService.logPermissionAccess(1, 'user.create', 'granted', mockContext))
        .resolves.not.toThrow();
    });
  });

  describe('logPermissionChange 记录权限变更日志', () => {
    test('应该正确记录权限变更日志', async () => {
      const changes = {
        type: 'permission_update',
        targetUserId: 2,
        oldPermissions: ['user.read.own'],
        newPermissions: ['user.read.own', 'user.create'],
        addedPermissions: ['user.create'],
        removedPermissions: []
      };

      db.execute.mockResolvedValue([{ insertId: 4 }]);

      await auditService.logPermissionChange(1, 2, changes);

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO audit_logs'),
        expect.arrayContaining([
          1, // admin_id
          'permission_change',
          'user_permissions',
          '2', // target_user_id
          'success',
          expect.any(String), // changes as JSON
          null, // ip_address (可能在上下文中提供)
          null // user_agent
        ])
      );

      // 验证详细信息包含变更内容
      const detailsArg = db.execute.mock.calls[0][1][5];
      const details = JSON.parse(detailsArg);
      expect(details).toMatchObject({
        type: 'permission_update',
        targetUserId: 2,
        addedPermissions: ['user.create'],
        removedPermissions: []
      });
    });

    test('应该记录角色变更日志', async () => {
      const changes = {
        type: 'role_change',
        targetUserId: 3,
        oldRole: 'user',
        newRole: 'admin',
        reason: '用户升级为管理员'
      };

      db.execute.mockResolvedValue([{ insertId: 5 }]);

      await auditService.logPermissionChange(1, 3, changes);

      const detailsArg = db.execute.mock.calls[0][1][5];
      const details = JSON.parse(detailsArg);
      expect(details).toMatchObject({
        type: 'role_change',
        oldRole: 'user',
        newRole: 'admin',
        reason: '用户升级为管理员'
      });
    });

    test('应该记录批量权限变更日志', async () => {
      const changes = {
        type: 'bulk_permission_update',
        affectedUsers: [2, 3, 4],
        operation: 'add_permission',
        permission: 'server.create',
        reason: '批量授予服务器创建权限'
      };

      db.execute.mockResolvedValue([{ insertId: 6 }]);

      await auditService.logPermissionChange(1, null, changes);

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO audit_logs'),
        expect.arrayContaining([
          1,
          'permission_change',
          'bulk_permissions',
          null,
          'success',
          expect.any(String),
          null,
          null
        ])
      );
    });
  });

  describe('getAuditLogs 查询审计日志', () => {
    test('应该支持基本的日志查询', async () => {
      const mockLogs = [
        {
          id: 1,
          user_id: 1,
          action: 'permission_access',
          resource: 'user.create',
          result: 'granted',
          created_at: new Date()
        },
        {
          id: 2,
          user_id: 1,
          action: 'permission_access',
          resource: 'user.delete',
          result: 'denied',
          created_at: new Date()
        }
      ];

      const mockCount = [{ total: 2 }];

      db.execute
        .mockResolvedValueOnce([mockCount]) // 总数查询
        .mockResolvedValueOnce([mockLogs]); // 数据查询

      const result = await auditService.getAuditLogs({
        page: 1,
        limit: 10
      });

      expect(result).toEqual({
        logs: mockLogs,
        pagination: {
          page: 1,
          limit: 10,
          total: 2,
          totalPages: 1
        }
      });
    });

    test('应该支持按用户ID筛选', async () => {
      const mockLogs = [
        {
          id: 1,
          user_id: 2,
          action: 'permission_access',
          resource: 'server.create',
          result: 'granted',
          created_at: new Date()
        }
      ];

      db.execute
        .mockResolvedValueOnce([[{ total: 1 }]])
        .mockResolvedValueOnce([mockLogs]);

      const result = await auditService.getAuditLogs({
        userId: 2,
        page: 1,
        limit: 10
      });

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('WHERE user_id = ?'),
        expect.arrayContaining([2])
      );
      expect(result.logs).toEqual(mockLogs);
    });

    test('应该支持按操作类型筛选', async () => {
      db.execute
        .mockResolvedValueOnce([[{ total: 0 }]])
        .mockResolvedValueOnce([[]]);

      await auditService.getAuditLogs({
        action: 'permission_change',
        page: 1,
        limit: 10
      });

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('WHERE action = ?'),
        expect.arrayContaining(['permission_change'])
      );
    });

    test('应该支持按时间范围筛选', async () => {
      const startDate = new Date('2025-01-01');
      const endDate = new Date('2025-01-31');

      db.execute
        .mockResolvedValueOnce([[{ total: 0 }]])
        .mockResolvedValueOnce([[]]);

      await auditService.getAuditLogs({
        startDate,
        endDate,
        page: 1,
        limit: 10
      });

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('WHERE created_at >= ? AND created_at <= ?'),
        expect.arrayContaining([startDate, endDate])
      );
    });

    test('应该支持按结果筛选', async () => {
      db.execute
        .mockResolvedValueOnce([[{ total: 0 }]])
        .mockResolvedValueOnce([[]]);

      await auditService.getAuditLogs({
        result: 'denied',
        page: 1,
        limit: 10
      });

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('WHERE result = ?'),
        expect.arrayContaining(['denied'])
      );
    });

    test('应该支持组合筛选条件', async () => {
      db.execute
        .mockResolvedValueOnce([[{ total: 0 }]])
        .mockResolvedValueOnce([[]]);

      await auditService.getAuditLogs({
        userId: 1,
        action: 'permission_access',
        result: 'denied',
        startDate: new Date('2025-01-01'),
        page: 1,
        limit: 10
      });

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('WHERE user_id = ? AND action = ? AND result = ? AND created_at >= ?'),
        expect.arrayContaining([1, 'permission_access', 'denied', expect.any(Date)])
      );
    });
  });

  describe('getAuditStats 获取审计统计', () => {
    test('应该返回基本统计信息', async () => {
      const mockStats = [
        { action: 'permission_access', result: 'granted', count: 150 },
        { action: 'permission_access', result: 'denied', count: 25 },
        { action: 'permission_change', result: 'success', count: 10 }
      ];

      db.execute.mockResolvedValue([mockStats]);

      const result = await auditService.getAuditStats({
        startDate: new Date('2025-01-01'),
        endDate: new Date('2025-01-31')
      });

      expect(result).toEqual({
        totalEvents: 185,
        permissionAccess: {
          granted: 150,
          denied: 25,
          total: 175
        },
        permissionChanges: 10,
        securityEvents: 25, // denied access attempts
        successRate: expect.closeTo(0.865, 2) // 160/185
      });
    });

    test('应该支持按用户统计', async () => {
      const mockUserStats = [
        { user_id: 1, username: 'admin', total_events: 50, denied_events: 2 },
        { user_id: 2, username: 'user1', total_events: 30, denied_events: 5 }
      ];

      db.execute.mockResolvedValue([mockUserStats]);

      const result = await auditService.getAuditStats({
        groupBy: 'user',
        startDate: new Date('2025-01-01'),
        endDate: new Date('2025-01-31')
      });

      expect(result.userStats).toEqual([
        { userId: 1, username: 'admin', totalEvents: 50, deniedEvents: 2, successRate: 0.96 },
        { userId: 2, username: 'user1', totalEvents: 30, deniedEvents: 5, successRate: 0.83 }
      ]);
    });
  });

  describe('detectAnomalies 异常检测', () => {
    test('应该检测异常的权限访问模式', async () => {
      // 模拟异常访问数据
      const mockAnomalies = [
        {
          user_id: 2,
          username: 'user1',
          denied_count: 15,
          total_count: 20,
          last_attempt: new Date(),
          ip_addresses: ['*************', '********']
        }
      ];

      db.execute.mockResolvedValue([mockAnomalies]);

      const result = await auditService.detectAnomalies({
        timeWindow: '1h', // 1小时内
        deniedThreshold: 10 // 拒绝次数阈值
      });

      expect(result.suspiciousUsers).toEqual([
        {
          userId: 2,
          username: 'user1',
          deniedCount: 15,
          totalCount: 20,
          deniedRate: 0.75,
          lastAttempt: expect.any(Date),
          ipAddresses: ['*************', '********'],
          riskLevel: 'high'
        }
      ]);
    });

    test('应该检测权限提升尝试', async () => {
      const mockEscalationAttempts = [
        {
          user_id: 3,
          username: 'user2',
          attempted_permissions: ['system.backup', 'user.delete.any'],
          attempt_count: 5,
          last_attempt: new Date()
        }
      ];

      db.execute.mockResolvedValue([mockEscalationAttempts]);

      const result = await auditService.detectAnomalies({
        checkEscalation: true
      });

      expect(result.escalationAttempts).toEqual([
        {
          userId: 3,
          username: 'user2',
          attemptedPermissions: ['system.backup', 'user.delete.any'],
          attemptCount: 5,
          lastAttempt: expect.any(Date),
          riskLevel: 'critical'
        }
      ]);
    });
  });

  describe('cleanupOldLogs 清理旧日志', () => {
    test('应该清理指定天数之前的日志', async () => {
      db.execute.mockResolvedValue([{ affectedRows: 100 }]);

      const result = await auditService.cleanupOldLogs(90); // 清理90天前的日志

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM audit_logs WHERE created_at < ?'),
        expect.any(Array)
      );
      expect(result.deletedCount).toBe(100);
    });

    test('应该支持按日志类型清理', async () => {
      db.execute.mockResolvedValue([{ affectedRows: 50 }]);

      const result = await auditService.cleanupOldLogs(30, 'permission_access');

      expect(db.execute).toHaveBeenCalledWith(
        expect.stringContaining('DELETE FROM audit_logs WHERE created_at < ? AND action = ?'),
        expect.any(Array)
      );
      expect(result.deletedCount).toBe(50);
    });
  });

  describe('exportAuditLogs 导出审计日志', () => {
    test('应该支持CSV格式导出', async () => {
      const mockLogs = [
        {
          id: 1,
          user_id: 1,
          action: 'permission_access',
          resource: 'user.create',
          result: 'granted',
          ip_address: '*************',
          created_at: new Date('2025-01-15T10:30:00Z')
        }
      ];

      db.execute.mockResolvedValue([mockLogs]);

      const result = await auditService.exportAuditLogs({
        format: 'csv',
        startDate: new Date('2025-01-01'),
        endDate: new Date('2025-01-31')
      });

      expect(result.format).toBe('csv');
      expect(result.data).toContain('id,user_id,action,resource,result,ip_address,created_at');
      expect(result.data).toContain('1,1,permission_access,user.create,granted,*************');
    });

    test('应该支持JSON格式导出', async () => {
      const mockLogs = [
        {
          id: 1,
          user_id: 1,
          action: 'permission_access',
          resource: 'user.create',
          result: 'granted',
          created_at: new Date()
        }
      ];

      db.execute.mockResolvedValue([mockLogs]);

      const result = await auditService.exportAuditLogs({
        format: 'json',
        userId: 1
      });

      expect(result.format).toBe('json');
      expect(JSON.parse(result.data)).toEqual(mockLogs);
    });
  });

  describe('性能和错误处理', () => {
    test('应该处理大量日志查询的性能', async () => {
      // 模拟大量数据
      const largeMockLogs = Array.from({ length: 1000 }, (_, i) => ({
        id: i + 1,
        user_id: Math.floor(i / 10) + 1,
        action: 'permission_access',
        result: i % 10 === 0 ? 'denied' : 'granted',
        created_at: new Date()
      }));

      db.execute
        .mockResolvedValueOnce([[{ total: 1000 }]])
        .mockResolvedValueOnce([largeMockLogs.slice(0, 50)]); // 分页

      const startTime = Date.now();
      const result = await auditService.getAuditLogs({
        page: 1,
        limit: 50
      });
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
      expect(result.logs.length).toBe(50);
      expect(result.pagination.total).toBe(1000);
    });

    test('应该处理数据库查询错误', async () => {
      db.execute.mockRejectedValue(new Error('数据库查询失败'));

      await expect(auditService.getAuditLogs({ page: 1, limit: 10 }))
        .rejects.toThrow('数据库查询失败');
    });

    test('应该处理无效的查询参数', async () => {
      await expect(auditService.getAuditLogs({
        page: -1,
        limit: 0
      })).rejects.toThrow('无效的分页参数');

      await expect(auditService.getAuditLogs({
        startDate: new Date('invalid'),
        endDate: new Date('2025-01-01')
      })).rejects.toThrow('无效的日期范围');
    });
  });
});