const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');
const PermissionService = require('../../services/PermissionService');

describe('PermissionService 权限服务测试', () => {
  let permissionService;
  let mockDb;

  beforeEach(() => {
    // 创建模拟数据库
    mockDb = {
      execute: jest.fn(),
      beginTransaction: jest.fn(),
      commit: jest.fn(),
      rollback: jest.fn()
    };
    
    permissionService = new PermissionService(mockDb);
    // 清除所有模拟调用
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('getUserPermissions 获取用户权限', () => {
    test('应该正确获取用户的角色权限', async () => {
      // 模拟数据库返回用户信息
      const mockUser = {
        id: 1,
        username: 'testuser',
        role: 'admin'
      };

      const mockRolePermissions = [
        { permission_code: 'user.create' },
        { permission_code: 'user.read.any' },
        { permission_code: 'server.create' }
      ];

      mockDb.execute
        .mockResolvedValueOnce([[mockUser]]) // 获取用户信息
        .mockResolvedValueOnce([mockRolePermissions]) // 获取角色权限
        .mockResolvedValueOnce([]); // 获取自定义权限

      const result = await permissionService.getUserPermissions(1);

      expect(result).toEqual({
        userId: 1,
        role: 'admin',
        rolePermissions: ['user.create', 'user.read.any', 'server.create'],
        customPermissions: [],
        effectivePermissions: ['user.create', 'user.read.any', 'server.create']
      });

      expect(mockDb.execute).toHaveBeenCalledTimes(3);
    });

    test('应该正确合并角色权限和自定义权限', async () => {
      const mockUser = {
        id: 1,
        username: 'testuser',
        role: 'user'
      };

      const mockRolePermissions = [
        { permission_code: 'user.read.own' },
        { permission_code: 'server.read.own' }
      ];

      const mockCustomPermissions = [
        { permission_code: 'user.create', granted: true },
        { permission_code: 'server.read.own', granted: false } // 撤销权限
      ];

      mockDb.execute
        .mockResolvedValueOnce([[mockUser]])
        .mockResolvedValueOnce([mockRolePermissions])
        .mockResolvedValueOnce([mockCustomPermissions]);

      const result = await permissionService.getUserPermissions(1);

      expect(result.effectivePermissions).toContain('user.create');
      expect(result.effectivePermissions).not.toContain('server.read.own');
      expect(result.customPermissions).toEqual([
        { code: 'user.create', granted: true },
        { code: 'server.read.own', granted: false }
      ]);
    });

    test('应该处理不存在的用户', async () => {
      mockDb.execute.mockResolvedValueOnce([[]]);

      await expect(permissionService.getUserPermissions(999))
        .rejects.toThrow('用户不存在');
    });
  });

  describe('hasPermission 权限检查', () => {
    test('应该正确检查用户是否拥有特定权限', async () => {
      // 模拟getUserPermissions返回结果
      jest.spyOn(permissionService, 'getUserPermissions').mockResolvedValue({
        userId: 1,
        role: 'admin',
        effectivePermissions: ['user.create', 'user.read.any', 'server.create']
      });

      const hasPermission = await permissionService.hasPermission(1, 'user.create');
      expect(hasPermission).toBe(true);

      const noPermission = await permissionService.hasPermission(1, 'system.backup');
      expect(noPermission).toBe(false);
    });

    test('应该支持多权限检查', async () => {
      jest.spyOn(permissionService, 'getUserPermissions').mockResolvedValue({
        userId: 1,
        role: 'admin',
        effectivePermissions: ['user.create', 'user.read.any', 'server.create']
      });

      const hasAllPermissions = await permissionService.hasPermission(1, ['user.create', 'server.create']);
      expect(hasAllPermissions).toBe(true);

      const missingPermissions = await permissionService.hasPermission(1, ['user.create', 'system.backup']);
      expect(missingPermissions).toBe(false);
    });
  });

  describe('hasRole 角色检查', () => {
    test('应该正确检查用户角色', async () => {
      const mockUser = { id: 1, role: 'admin' };
      mockDb.execute.mockResolvedValueOnce([[mockUser]]);

      const hasRole = await permissionService.hasRole(1, 'admin');
      expect(hasRole).toBe(true);

      const noRole = await permissionService.hasRole(1, 'super_admin');
      expect(noRole).toBe(false);
    });

    test('应该支持多角色检查', async () => {
      const mockUser = { id: 1, role: 'admin' };
      mockDb.execute.mockResolvedValueOnce([[mockUser]]);

      const hasAnyRole = await permissionService.hasRole(1, ['admin', 'super_admin']);
      expect(hasAnyRole).toBe(true);
    });
  });

  describe('updateUserPermissions 更新用户权限', () => {
    test('应该正确更新用户自定义权限', async () => {
      const permissions = [
        { code: 'user.create', granted: true },
        { code: 'system.backup', granted: false }
      ];

      // 模拟事务操作
      mockDb.beginTransaction = jest.fn().mockResolvedValue();
      mockDb.commit = jest.fn().mockResolvedValue();
      mockDb.rollback = jest.fn().mockResolvedValue();
      
      mockDb.execute
        .mockResolvedValueOnce([[]]) // 删除现有权限
        .mockResolvedValueOnce([{ insertId: 1 }]) // 插入新权限
        .mockResolvedValueOnce([{ insertId: 2 }]); // 插入新权限

      await permissionService.updateUserPermissions(1, permissions, 2);

      expect(mockDb.beginTransaction).toHaveBeenCalled();
      expect(mockDb.commit).toHaveBeenCalled();
      expect(mockDb.execute).toHaveBeenCalledTimes(3);
    });

    test('应该在更新失败时回滚事务', async () => {
      const permissions = [{ code: 'user.create', granted: true }];

      mockDb.beginTransaction = jest.fn().mockResolvedValue();
      mockDb.commit = jest.fn().mockResolvedValue();
      mockDb.rollback = jest.fn().mockResolvedValue();
      
      // 模拟数据库错误
      mockDb.execute.mockRejectedValue(new Error('数据库错误'));

      await expect(permissionService.updateUserPermissions(1, permissions, 2))
        .rejects.toThrow('数据库错误');

      expect(mockDb.rollback).toHaveBeenCalled();
    });
  });

  describe('calculateEffectivePermissions 计算有效权限', () => {
    test('应该正确计算权限继承', async () => {
      const rolePermissions = ['user.read.own', 'server.read.own'];
      const customPermissions = [
        { code: 'user.create', granted: true },
        { code: 'server.read.own', granted: false }
      ];

      const effectivePermissions = permissionService.calculateEffectivePermissions(
        rolePermissions, 
        customPermissions
      );

      expect(effectivePermissions).toContain('user.read.own');
      expect(effectivePermissions).toContain('user.create');
      expect(effectivePermissions).not.toContain('server.read.own');
    });

    test('应该处理权限冲突', async () => {
      const rolePermissions = ['user.create', 'user.read.any'];
      const customPermissions = [
        { code: 'user.create', granted: false }, // 撤销角色权限
        { code: 'system.backup', granted: true } // 添加额外权限
      ];

      const effectivePermissions = permissionService.calculateEffectivePermissions(
        rolePermissions, 
        customPermissions
      );

      expect(effectivePermissions).not.toContain('user.create');
      expect(effectivePermissions).toContain('user.read.any');
      expect(effectivePermissions).toContain('system.backup');
    });
  });

  describe('权限验证边界情况', () => {
    test('应该处理空权限列表', async () => {
      jest.spyOn(permissionService, 'getUserPermissions').mockResolvedValue({
        userId: 1,
        role: 'user',
        effectivePermissions: []
      });

      const hasPermission = await permissionService.hasPermission(1, 'user.create');
      expect(hasPermission).toBe(false);
    });

    test('应该处理无效的权限代码', async () => {
      jest.spyOn(permissionService, 'getUserPermissions').mockResolvedValue({
        userId: 1,
        role: 'user',
        effectivePermissions: ['user.create']
      });

      const hasPermission = await permissionService.hasPermission(1, '');
      expect(hasPermission).toBe(false);

      const hasNullPermission = await permissionService.hasPermission(1, null);
      expect(hasNullPermission).toBe(false);
    });

    test('应该处理超级管理员权限', async () => {
      jest.spyOn(permissionService, 'getUserPermissions').mockResolvedValue({
        userId: 1,
        role: 'super_admin',
        effectivePermissions: ['*'] // 超级管理员拥有所有权限
      });

      const hasAnyPermission = await permissionService.hasPermission(1, 'any.permission');
      expect(hasAnyPermission).toBe(true);
    });
  });
});