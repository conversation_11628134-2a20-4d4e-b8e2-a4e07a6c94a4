/**
 * 角色服务单元测试
 * 测试 RoleService 类的所有主要功能
 */

const RoleService = require('../../services/RoleService');

// 模拟数据库连接
const mockDb = {
  execute: jest.fn(),
  query: jest.fn()
};

describe('RoleService', () => {
  let roleService;

  beforeEach(() => {
    // 重置所有模拟
    jest.clearAllMocks();
    
    // 创建角色服务实例
    roleService = new RoleService(mockDb);
  });

  describe('getRolePermissions', () => {
    const mockPermissions = [
      {
        id: 1,
        name: '查看网站',
        code: 'site.website.view',
        description: '允许查看网站信息',
        module: 'site',
        resource: 'website',
        action: 'view',
        assigned_at: '2024-01-01T10:00:00Z'
      },
      {
        id: 2,
        name: '编辑网站',
        code: 'site.website.edit',
        description: '允许编辑网站信息',
        module: 'site',
        resource: 'website',
        action: 'edit',
        assigned_at: '2024-01-01T10:00:00Z'
      }
    ];

    beforeEach(() => {
      mockDb.execute.mockResolvedValue([mockPermissions]);
    });

    test('应该成功获取角色权限', async () => {
      const result = await roleService.getRolePermissions('admin');

      expect(result).toMatchObject({
        role: 'admin',
        roleInfo: roleService.systemRoles.admin,
        permissions: mockPermissions,
        totalPermissions: 2
      });

      expect(result.permissionsByModule).toHaveProperty('site');
      expect(result.permissionsByModule.site).toHaveLength(2);
    });

    test('应该抛出错误当角色不存在时', async () => {
      await expect(roleService.getRolePermissions('invalid_role'))
        .rejects.toThrow('角色不存在: invalid_role');
    });

    test('应该正确按模块分组权限', async () => {
      const mixedPermissions = [
        { ...mockPermissions[0], module: 'site' },
        { ...mockPermissions[1], module: 'server' }
      ];
      mockDb.execute.mockResolvedValue([mixedPermissions]);

      const result = await roleService.getRolePermissions('admin');

      expect(result.permissionsByModule).toHaveProperty('site');
      expect(result.permissionsByModule).toHaveProperty('server');
      expect(result.permissionsByModule.site).toHaveLength(1);
      expect(result.permissionsByModule.server).toHaveLength(1);
    });
  });

  describe('updateRolePermissions', () => {
    const mockCurrentPermissions = [
      { permission_code: 'site.website.view' },
      { permission_code: 'site.website.edit' }
    ];

    const newPermissions = [
      'site.website.view',
      'site.website.delete',
      'server.server.view'
    ];

    beforeEach(() => {
      // 模拟权限验证成功
      jest.spyOn(roleService, 'validatePermissionCodes')
        .mockResolvedValue({ valid: true, validCodes: newPermissions, invalidCodes: [] });

      mockDb.execute
        .mockResolvedValueOnce() // START TRANSACTION
        .mockResolvedValueOnce([mockCurrentPermissions]) // 获取当前权限
        .mockResolvedValueOnce() // DELETE 删除权限
        .mockResolvedValueOnce() // INSERT 添加权限1
        .mockResolvedValueOnce() // INSERT 添加权限2
        .mockResolvedValueOnce() // 审计日志
        .mockResolvedValueOnce(); // COMMIT

      // 模拟清除缓存
      jest.spyOn(roleService, 'clearRoleUsersCache').mockResolvedValue();
      jest.spyOn(roleService, 'recordRolePermissionChange').mockResolvedValue();
    });

    test('应该成功更新角色权限', async () => {
      const result = await roleService.updateRolePermissions('admin', newPermissions, 1);

      expect(result).toMatchObject({
        role: 'admin',
        addedPermissions: ['site.website.delete', 'server.server.view'],
        removedPermissions: ['site.website.edit'],
        totalPermissions: 3,
        errors: []
      });

      expect(mockDb.execute).toHaveBeenCalledWith('START TRANSACTION');
      expect(mockDb.execute).toHaveBeenCalledWith('COMMIT');
      expect(roleService.clearRoleUsersCache).toHaveBeenCalledWith('admin');
    });

    test('应该抛出错误当角色不存在时', async () => {
      await expect(roleService.updateRolePermissions('invalid_role', newPermissions, 1))
        .rejects.toThrow('角色不存在: invalid_role');
    });

    test('应该抛出错误当权限代码无效时', async () => {
      jest.spyOn(roleService, 'validatePermissionCodes')
        .mockResolvedValue({ 
          valid: false, 
          validCodes: [], 
          invalidCodes: ['invalid.permission'] 
        });

      await expect(roleService.updateRolePermissions('admin', ['invalid.permission'], 1))
        .rejects.toThrow('无效的权限代码: invalid.permission');
    });

    test('应该在出错时回滚事务', async () => {
      mockDb.execute
        .mockResolvedValueOnce() // START TRANSACTION
        .mockResolvedValueOnce([mockCurrentPermissions]) // 获取当前权限
        .mockRejectedValueOnce(new Error('数据库错误')); // DELETE 失败

      await expect(roleService.updateRolePermissions('admin', newPermissions, 1))
        .rejects.toThrow('数据库错误');

      expect(mockDb.execute).toHaveBeenCalledWith('ROLLBACK');
    });

    test('应该处理没有权限变更的情况', async () => {
      const samePermissions = ['site.website.view', 'site.website.edit'];
      mockDb.execute
        .mockResolvedValueOnce() // START TRANSACTION
        .mockResolvedValueOnce([mockCurrentPermissions]) // 获取当前权限
        .mockResolvedValueOnce() // 审计日志
        .mockResolvedValueOnce(); // COMMIT

      const result = await roleService.updateRolePermissions('admin', samePermissions, 1);

      expect(result.addedPermissions).toHaveLength(0);
      expect(result.removedPermissions).toHaveLength(0);
    });
  });

  describe('getRoleTemplates', () => {
    const mockTemplates = [
      {
        id: 1,
        name: '网站管理员模板',
        description: '网站管理相关权限',
        permissions: '["site.website.view", "site.website.edit"]',
        is_system: 1,
        created_by: null,
        created_at: '2024-01-01T10:00:00Z',
        creator_username: null
      },
      {
        id: 2,
        name: '自定义模板',
        description: '用户自定义权限模板',
        permissions: '["server.server.view"]',
        is_system: 0,
        created_by: 1,
        created_at: '2024-01-02T10:00:00Z',
        creator_username: 'admin'
      }
    ];

    beforeEach(() => {
      mockDb.execute.mockResolvedValue([mockTemplates]);
    });

    test('应该获取所有角色模板', async () => {
      const result = await roleService.getRoleTemplates();

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        id: 1,
        name: '网站管理员模板',
        permissions: ['site.website.view', 'site.website.edit']
      });
      expect(result[1]).toMatchObject({
        id: 2,
        name: '自定义模板',
        permissions: ['server.server.view']
      });
    });

    test('应该支持筛选选项', async () => {
      await roleService.getRoleTemplates({ 
        includeSystem: false, 
        createdBy: 1, 
        limit: 10 
      });

      expect(mockDb.execute).toHaveBeenCalledWith(
        expect.stringContaining('WHERE is_system = 0 AND created_by = ?'),
        [1, 10]
      );
    });

    test('应该正确解析权限JSON', async () => {
      const result = await roleService.getRoleTemplates();

      expect(Array.isArray(result[0].permissions)).toBe(true);
      expect(result[0].permissions).toContain('site.website.view');
    });
  });

  describe('createRoleTemplate', () => {
    const templateData = {
      name: '测试模板',
      description: '测试用权限模板',
      permissions: ['site.website.view', 'site.website.edit'],
      isSystem: false
    };

    beforeEach(() => {
      // 模拟模板名称不存在
      mockDb.execute
        .mockResolvedValueOnce([[]]) // 检查模板名称唯一性
        .mockResolvedValueOnce([{ insertId: 1 }]); // 创建模板

      // 模拟权限验证成功
      jest.spyOn(roleService, 'validatePermissionCodes')
        .mockResolvedValue({ 
          valid: true, 
          validCodes: templateData.permissions, 
          invalidCodes: [] 
        });
    });

    test('应该成功创建角色模板', async () => {
      const result = await roleService.createRoleTemplate(templateData, 1);

      expect(result).toMatchObject({
        id: 1,
        name: '测试模板',
        description: '测试用权限模板',
        permissions: ['site.website.view', 'site.website.edit'],
        isSystem: false,
        createdBy: 1
      });

      expect(mockDb.execute).toHaveBeenCalledWith(
        expect.stringContaining('INSERT INTO role_templates'),
        expect.arrayContaining([
          '测试模板',
          '测试用权限模板',
          JSON.stringify(templateData.permissions),
          false,
          1
        ])
      );
    });

    test('应该抛出错误当模板名称已存在时', async () => {
      mockDb.execute.mockResolvedValueOnce([[{ id: 1 }]]); // 模板名称已存在

      await expect(roleService.createRoleTemplate(templateData, 1))
        .rejects.toThrow('模板名称已存在: 测试模板');
    });

    test('应该抛出错误当权限代码无效时', async () => {
      jest.spyOn(roleService, 'validatePermissionCodes')
        .mockResolvedValue({ 
          valid: false, 
          validCodes: [], 
          invalidCodes: ['invalid.permission'] 
        });

      await expect(roleService.createRoleTemplate(templateData, 1))
        .rejects.toThrow('无效的权限代码: invalid.permission');
    });
  });

  describe('applyRoleTemplate', () => {
    const mockTemplate = {
      id: 1,
      name: '网站管理员模板',
      permissions: '["site.website.view", "site.website.edit"]'
    };

    const mockUpdateResult = {
      role: 'admin',
      addedPermissions: ['site.website.view', 'site.website.edit'],
      removedPermissions: [],
      totalPermissions: 2
    };

    beforeEach(() => {
      mockDb.execute.mockResolvedValue([[mockTemplate]]);
      jest.spyOn(roleService, 'updateRolePermissions')
        .mockResolvedValue(mockUpdateResult);
    });

    test('应该成功应用角色模板', async () => {
      const result = await roleService.applyRoleTemplate('admin', 1, 2);

      expect(result).toMatchObject({
        role: 'admin',
        templateName: '网站管理员模板',
        templateId: 1,
        ...mockUpdateResult
      });

      expect(roleService.updateRolePermissions).toHaveBeenCalledWith(
        'admin',
        ['site.website.view', 'site.website.edit'],
        2
      );
    });

    test('应该抛出错误当模板不存在时', async () => {
      mockDb.execute.mockResolvedValue([[]]);

      await expect(roleService.applyRoleTemplate('admin', 999, 2))
        .rejects.toThrow('角色模板不存在: 999');
    });
  });

  describe('batchUpdateRolePermissions', () => {
    const rolePermissions = {
      'admin': ['site.website.view', 'site.website.edit'],
      'user': ['site.website.view']
    };

    beforeEach(() => {
      mockDb.execute
        .mockResolvedValueOnce() // START TRANSACTION
        .mockResolvedValueOnce(); // COMMIT

      jest.spyOn(roleService, 'updateRolePermissions')
        .mockResolvedValueOnce({ role: 'admin', addedPermissions: [], removedPermissions: [] })
        .mockResolvedValueOnce({ role: 'user', addedPermissions: [], removedPermissions: [] });
    });

    test('应该成功批量更新角色权限', async () => {
      const result = await roleService.batchUpdateRolePermissions(rolePermissions, 1);

      expect(result).toMatchObject({
        success: true,
        totalRoles: 2,
        errors: []
      });

      expect(result.results).toHaveProperty('admin');
      expect(result.results).toHaveProperty('user');
    });

    test('应该处理部分失败的情况', async () => {
      jest.spyOn(roleService, 'updateRolePermissions')
        .mockResolvedValueOnce({ role: 'admin', addedPermissions: [], removedPermissions: [] })
        .mockRejectedValueOnce(new Error('权限更新失败'));

      mockDb.execute
        .mockResolvedValueOnce() // START TRANSACTION
        .mockResolvedValueOnce(); // ROLLBACK

      const result = await roleService.batchUpdateRolePermissions(rolePermissions, 1);

      expect(result).toMatchObject({
        success: false,
        totalRoles: 2,
        results: {},
        errors: [expect.stringContaining('部分角色权限更新失败')]
      });
    });
  });

  describe('getRoleUserStatistics', () => {
    const mockUserCount = [{ count: 5 }];
    const mockUsers = [
      {
        id: 1,
        username: 'user1',
        email: '<EMAIL>',
        status: 'active',
        last_login: '2024-01-01T10:00:00Z',
        created_at: '2024-01-01T09:00:00Z'
      }
    ];
    const mockRecentChanges = [
      {
        created_at: '2024-01-01T11:00:00Z',
        details: '{"role": "admin", "operatorId": 1}',
        operator_username: 'superadmin'
      }
    ];

    beforeEach(() => {
      mockDb.execute
        .mockResolvedValueOnce([mockUserCount])
        .mockResolvedValueOnce([mockUsers])
        .mockResolvedValueOnce([mockRecentChanges]);
    });

    test('应该返回角色用户统计信息', async () => {
      const result = await roleService.getRoleUserStatistics('admin');

      expect(result).toMatchObject({
        role: 'admin',
        roleInfo: roleService.systemRoles.admin,
        userCount: 5,
        users: mockUsers
      });

      expect(result.recentChanges).toHaveLength(1);
      expect(result.recentChanges[0].details).toEqual({
        role: 'admin',
        operatorId: 1
      });
    });
  });

  describe('validatePermissionCodes', () => {
    const validPermissions = [
      { code: 'site.website.view' },
      { code: 'site.website.edit' }
    ];

    test('应该验证有效的权限代码', async () => {
      const permissionCodes = ['site.website.view', 'site.website.edit'];
      mockDb.execute.mockResolvedValue([validPermissions]);

      const result = await roleService.validatePermissionCodes(permissionCodes);

      expect(result).toEqual({
        valid: true,
        validCodes: ['site.website.view', 'site.website.edit'],
        invalidCodes: []
      });
    });

    test('应该检测无效的权限代码', async () => {
      const permissionCodes = ['site.website.view', 'invalid.permission'];
      mockDb.execute.mockResolvedValue([[{ code: 'site.website.view' }]]);

      const result = await roleService.validatePermissionCodes(permissionCodes);

      expect(result).toEqual({
        valid: false,
        validCodes: ['site.website.view'],
        invalidCodes: ['invalid.permission']
      });
    });

    test('应该处理非数组输入', async () => {
      const result = await roleService.validatePermissionCodes('not_an_array');

      expect(result).toEqual({
        valid: false,
        invalidCodes: ['权限代码必须是数组']
      });
    });

    test('应该处理数据库错误', async () => {
      mockDb.execute.mockRejectedValue(new Error('数据库错误'));

      const result = await roleService.validatePermissionCodes(['site.website.view']);

      expect(result).toEqual({
        valid: false,
        invalidCodes: ['验证失败']
      });
    });
  });

  describe('clearRoleUsersCache', () => {
    const mockUsers = [{ id: 1 }, { id: 2 }];

    beforeEach(() => {
      mockDb.execute.mockResolvedValue([mockUsers]);
    });

    test('应该清除角色用户的权限缓存', async () => {
      // 模拟 PermissionCacheService
      const mockClearUserPermissions = jest.fn().mockResolvedValue();
      jest.doMock('../../services/PermissionCacheService', () => {
        return jest.fn().mockImplementation(() => ({
          clearUserPermissions: mockClearUserPermissions
        }));
      });

      await roleService.clearRoleUsersCache('admin');

      expect(mockDb.execute).toHaveBeenCalledWith(
        'SELECT id FROM users WHERE role = ?',
        ['admin']
      );
    });
  });

  describe('系统角色管理', () => {
    test('getSystemRoles 应该返回系统角色信息', () => {
      const systemRoles = roleService.getSystemRoles();

      expect(systemRoles).toHaveProperty('super_admin');
      expect(systemRoles).toHaveProperty('admin');
      expect(systemRoles).toHaveProperty('user');
      expect(systemRoles.super_admin.level).toBe(100);
      expect(systemRoles.admin.level).toBe(80);
      expect(systemRoles.user.level).toBe(20);
    });

    test('compareRoleLevel 应该正确比较角色级别', () => {
      expect(roleService.compareRoleLevel('super_admin', 'admin')).toBe(1);
      expect(roleService.compareRoleLevel('admin', 'super_admin')).toBe(-1);
      expect(roleService.compareRoleLevel('admin', 'admin')).toBe(0);
      expect(roleService.compareRoleLevel('invalid_role', 'user')).toBe(-1);
    });

    test('canManageRole 应该正确判断角色管理权限', () => {
      expect(roleService.canManageRole('super_admin', 'admin')).toBe(true);
      expect(roleService.canManageRole('super_admin', 'user')).toBe(true);
      expect(roleService.canManageRole('admin', 'super_admin')).toBe(false);
      expect(roleService.canManageRole('admin', 'user')).toBe(true);
      expect(roleService.canManageRole('user', 'admin')).toBe(false);
    });
  });
});