const { describe, test, expect, beforeEach, afterEach } = require('@jest/globals');
const PermissionCacheService = require('../../services/PermissionCacheService');

// 模拟Redis
const mockRedis = {
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
  keys: jest.fn(),
  pipeline: jest.fn().mockReturnValue({
    del: jest.fn().mockReturnThis(),
    exec: jest.fn().mockResolvedValue([])
  })
};

// 模拟Redis构造函数
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => mockRedis);
});

describe('PermissionCacheService 权限缓存服务测试', () => {
  let cacheService;

  beforeEach(() => {
    cacheService = new PermissionCacheService();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('getUserPermissions 获取用户权限缓存', () => {
    test('应该从本地缓存获取权限数据', async () => {
      const mockPermissions = {
        userId: 1,
        role: 'admin',
        effectivePermissions: ['user.create', 'user.read.any']
      };

      // 设置本地缓存
      cacheService.localCache.set('permissions:1', {
        data: mockPermissions,
        timestamp: Date.now()
      });

      const result = await cacheService.getUserPermissions(1);

      expect(result).toEqual(mockPermissions);
      expect(mockRedis.get).not.toHaveBeenCalled();
    });

    test('应该从Redis缓存获取权限数据', async () => {
      const mockPermissions = {
        userId: 1,
        role: 'admin',
        effectivePermissions: ['user.create', 'user.read.any']
      };

      // 本地缓存未命中，Redis缓存命中
      mockRedis.get.mockResolvedValue(JSON.stringify(mockPermissions));

      const result = await cacheService.getUserPermissions(1);

      expect(result).toEqual(mockPermissions);
      expect(mockRedis.get).toHaveBeenCalledWith('permissions:1');
      
      // 验证本地缓存已更新
      const localCacheEntry = cacheService.localCache.get('permissions:1');
      expect(localCacheEntry.data).toEqual(mockPermissions);
    });

    test('应该在缓存未命中时返回null', async () => {
      // 本地缓存和Redis缓存都未命中
      mockRedis.get.mockResolvedValue(null);

      const result = await cacheService.getUserPermissions(1);

      expect(result).toBeNull();
      expect(mockRedis.get).toHaveBeenCalledWith('permissions:1');
    });

    test('应该处理Redis连接错误', async () => {
      mockRedis.get.mockRejectedValue(new Error('Redis连接失败'));

      const result = await cacheService.getUserPermissions(1);

      expect(result).toBeNull();
      // 应该记录错误日志（如果有日志系统）
    });

    test('应该检查本地缓存过期时间', async () => {
      const expiredData = {
        data: { userId: 1, effectivePermissions: [] },
        timestamp: Date.now() - 400000 // 超过5分钟过期时间
      };

      cacheService.localCache.set('permissions:1', expiredData);
      mockRedis.get.mockResolvedValue(null);

      const result = await cacheService.getUserPermissions(1);

      expect(result).toBeNull();
      expect(mockRedis.get).toHaveBeenCalled();
      // 过期数据应该被清除
      expect(cacheService.localCache.has('permissions:1')).toBe(false);
    });
  });

  describe('setUserPermissions 设置用户权限缓存', () => {
    test('应该同时更新本地缓存和Redis缓存', async () => {
      const mockPermissions = {
        userId: 1,
        role: 'admin',
        effectivePermissions: ['user.create', 'user.read.any']
      };

      mockRedis.set.mockResolvedValue('OK');

      await cacheService.setUserPermissions(1, mockPermissions);

      // 验证Redis缓存更新
      expect(mockRedis.set).toHaveBeenCalledWith(
        'permissions:1',
        JSON.stringify(mockPermissions),
        'EX',
        300 // 5分钟过期时间
      );

      // 验证本地缓存更新
      const localCacheEntry = cacheService.localCache.get('permissions:1');
      expect(localCacheEntry.data).toEqual(mockPermissions);
      expect(localCacheEntry.timestamp).toBeCloseTo(Date.now(), -2);
    });

    test('应该处理Redis设置失败', async () => {
      const mockPermissions = { userId: 1, effectivePermissions: [] };
      mockRedis.set.mockRejectedValue(new Error('Redis设置失败'));

      // 即使Redis失败，也应该更新本地缓存
      await cacheService.setUserPermissions(1, mockPermissions);

      const localCacheEntry = cacheService.localCache.get('permissions:1');
      expect(localCacheEntry.data).toEqual(mockPermissions);
    });
  });

  describe('clearUserPermissions 清除用户权限缓存', () => {
    test('应该清除本地缓存和Redis缓存', async () => {
      // 设置初始缓存
      cacheService.localCache.set('permissions:1', { data: {}, timestamp: Date.now() });
      mockRedis.del.mockResolvedValue(1);

      await cacheService.clearUserPermissions(1);

      expect(mockRedis.del).toHaveBeenCalledWith('permissions:1');
      expect(cacheService.localCache.has('permissions:1')).toBe(false);
    });

    test('应该处理Redis删除失败', async () => {
      cacheService.localCache.set('permissions:1', { data: {}, timestamp: Date.now() });
      mockRedis.del.mockRejectedValue(new Error('Redis删除失败'));

      await cacheService.clearUserPermissions(1);

      // 本地缓存应该被清除
      expect(cacheService.localCache.has('permissions:1')).toBe(false);
    });
  });

  describe('clearRolePermissions 清除角色权限缓存', () => {
    test('应该清除指定角色所有用户的权限缓存', async () => {
      // 模拟Redis返回匹配的键
      mockRedis.keys.mockResolvedValue(['permissions:1', 'permissions:2', 'permissions:3']);
      
      // 模拟获取用户数据以检查角色
      mockRedis.get
        .mockResolvedValueOnce(JSON.stringify({ userId: 1, role: 'admin' }))
        .mockResolvedValueOnce(JSON.stringify({ userId: 2, role: 'user' }))
        .mockResolvedValueOnce(JSON.stringify({ userId: 3, role: 'admin' }));

      const pipeline = mockRedis.pipeline();
      pipeline.exec.mockResolvedValue([]);

      await cacheService.clearRolePermissions('admin');

      expect(mockRedis.keys).toHaveBeenCalledWith('permissions:*');
      expect(pipeline.del).toHaveBeenCalledWith('permissions:1');
      expect(pipeline.del).toHaveBeenCalledWith('permissions:3');
      expect(pipeline.del).not.toHaveBeenCalledWith('permissions:2');
    });

    test('应该清除本地缓存中的角色权限', async () => {
      // 设置本地缓存
      cacheService.localCache.set('permissions:1', {
        data: { userId: 1, role: 'admin' },
        timestamp: Date.now()
      });
      cacheService.localCache.set('permissions:2', {
        data: { userId: 2, role: 'user' },
        timestamp: Date.now()
      });

      mockRedis.keys.mockResolvedValue([]);

      await cacheService.clearRolePermissions('admin');

      expect(cacheService.localCache.has('permissions:1')).toBe(false);
      expect(cacheService.localCache.has('permissions:2')).toBe(true);
    });
  });

  describe('缓存性能和容量管理', () => {
    test('应该限制本地缓存大小', async () => {
      // 设置较小的最大缓存大小进行测试
      cacheService.maxLocalCacheSize = 3;

      // 添加超过限制的缓存项
      for (let i = 1; i <= 5; i++) {
        await cacheService.setUserPermissions(i, { userId: i, effectivePermissions: [] });
      }

      // 验证缓存大小不超过限制
      expect(cacheService.localCache.size).toBeLessThanOrEqual(3);
    });

    test('应该清理过期的本地缓存项', async () => {
      const now = Date.now();
      
      // 添加过期和未过期的缓存项
      cacheService.localCache.set('permissions:1', {
        data: { userId: 1 },
        timestamp: now - 400000 // 过期
      });
      cacheService.localCache.set('permissions:2', {
        data: { userId: 2 },
        timestamp: now - 100000 // 未过期
      });

      await cacheService.cleanupExpiredCache();

      expect(cacheService.localCache.has('permissions:1')).toBe(false);
      expect(cacheService.localCache.has('permissions:2')).toBe(true);
    });

    test('应该提供缓存统计信息', async () => {
      // 设置一些缓存数据
      cacheService.localCache.set('permissions:1', { data: {}, timestamp: Date.now() });
      cacheService.localCache.set('permissions:2', { data: {}, timestamp: Date.now() });

      const stats = await cacheService.getCacheStats();

      expect(stats).toEqual({
        localCacheSize: 2,
        localCacheHits: expect.any(Number),
        localCacheMisses: expect.any(Number),
        redisCacheHits: expect.any(Number),
        redisCacheMisses: expect.any(Number),
        hitRate: expect.any(Number)
      });
    });
  });

  describe('缓存一致性', () => {
    test('应该在权限更新时保持缓存一致性', async () => {
      const oldPermissions = { userId: 1, role: 'user', effectivePermissions: ['user.read.own'] };
      const newPermissions = { userId: 1, role: 'admin', effectivePermissions: ['user.create', 'user.read.any'] };

      // 设置初始缓存
      await cacheService.setUserPermissions(1, oldPermissions);

      // 更新权限
      await cacheService.setUserPermissions(1, newPermissions);

      // 验证缓存已更新
      const cachedPermissions = await cacheService.getUserPermissions(1);
      expect(cachedPermissions).toEqual(newPermissions);
    });

    test('应该处理并发缓存更新', async () => {
      const permissions1 = { userId: 1, role: 'admin', effectivePermissions: ['user.create'] };
      const permissions2 = { userId: 1, role: 'admin', effectivePermissions: ['user.create', 'user.delete'] };

      // 模拟并发更新
      const promise1 = cacheService.setUserPermissions(1, permissions1);
      const promise2 = cacheService.setUserPermissions(1, permissions2);

      await Promise.all([promise1, promise2]);

      // 验证最终状态一致
      const finalPermissions = await cacheService.getUserPermissions(1);
      expect(finalPermissions).toBeDefined();
      expect(finalPermissions.userId).toBe(1);
    });
  });

  describe('错误处理和恢复', () => {
    test('应该在Redis不可用时降级到本地缓存', async () => {
      const mockPermissions = { userId: 1, effectivePermissions: [] };
      
      // 设置本地缓存
      cacheService.localCache.set('permissions:1', {
        data: mockPermissions,
        timestamp: Date.now()
      });

      // 模拟Redis不可用
      mockRedis.get.mockRejectedValue(new Error('Redis不可用'));

      const result = await cacheService.getUserPermissions(1);

      expect(result).toEqual(mockPermissions);
    });

    test('应该在缓存损坏时进行修复', async () => {
      // 模拟损坏的缓存数据
      mockRedis.get.mockResolvedValue('invalid json data');

      const result = await cacheService.getUserPermissions(1);

      expect(result).toBeNull();
      // 应该清除损坏的缓存
      expect(mockRedis.del).toHaveBeenCalledWith('permissions:1');
    });
  });
});