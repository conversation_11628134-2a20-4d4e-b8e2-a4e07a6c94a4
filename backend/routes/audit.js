/**
 * 审计日志API路由
 * 功能：
 * 1. 创建审计日志查询API GET /api/v1/audit/logs
 * 2. 实现日志统计分析API GET /api/v1/audit/stats
 * 3. 添加日志导出API GET /api/v1/audit/export
 * 4. 实现异常检测API GET /api/v1/audit/anomalies
 * 5. 添加日志清理和归档API
 */

const express = require('express');
const router = express.Router();
const AuditService = require('../services/AuditService');
const PermissionMiddleware = require('../middleware/PermissionMiddleware');

// 初始化服务和中间件
let auditService = null;
let permissionMiddleware = null;

// 中间件初始化函数
const initializeServices = (db) => {
  if (!auditService) {
    auditService = new AuditService(db);
  }
  if (!permissionMiddleware) {
    permissionMiddleware = new PermissionMiddleware(db);
  }
};

/**
 * GET /api/v1/audit/logs
 * 获取审计日志列表
 */
router.get('/logs', async (req, res) => {
  try {
    // 初始化服务
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionMiddleware.permissionService.hasPermission(
      req.user.id, 
      'system.audit.view'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法查看审计日志'
        }
      });
    }

    // 解析查询参数
    const {
      page = 1,
      limit = 20,
      userId,
      action,
      resource,
      result,
      startDate,
      endDate,
      sortBy = 'created_at',
      sortOrder = 'DESC'
    } = req.query;

    // 构建筛选条件
    const filters = {
      userId: userId ? parseInt(userId) : undefined,
      action,
      resource,
      result,
      startDate,
      endDate,
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      sortOrder
    };

    // 获取审计日志
    const result = await auditService.getAuditLogs(filters);

    // 记录审计日志访问
    await auditService.logPermissionAccess(
      req.user.id,
      'audit_log_access',
      'audit_logs',
      'success',
      {
        filters,
        resultCount: result.logs.length,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.json({
      success: true,
      data: result,
      message: '审计日志获取成功'
    });

  } catch (error) {
    console.error('获取审计日志失败:', error);
    
    // 记录错误日志
    if (auditService && req.user) {
      await auditService.logPermissionAccess(
        req.user.id,
        'audit_log_access',
        'audit_logs',
        'failure',
        {
          error: error.message,
          ip: req.ip,
          userAgent: req.get('User-Agent')
        }
      );
    }

    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取审计日志失败'
      }
    });
  }
});

/**
 * GET /api/v1/audit/stats
 * 获取审计日志统计信息
 */
router.get('/stats', async (req, res) => {
  try {
    // 初始化服务
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionMiddleware.permissionService.hasPermission(
      req.user.id, 
      'system.audit.view'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法查看审计统计'
        }
      });
    }

    // 解析查询参数
    const {
      startDate,
      endDate,
      userId
    } = req.query;

    const filters = {
      startDate,
      endDate,
      userId: userId ? parseInt(userId) : undefined
    };

    // 获取统计信息
    const statistics = await auditService.getAuditStatistics(filters);

    // 记录统计访问
    await auditService.logPermissionAccess(
      req.user.id,
      'audit_stats_access',
      'audit_statistics',
      'success',
      {
        filters,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.json({
      success: true,
      data: statistics,
      message: '审计统计获取成功'
    });

  } catch (error) {
    console.error('获取审计统计失败:', error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取审计统计失败'
      }
    });
  }
});

/**
 * GET /api/v1/audit/export
 * 导出审计日志
 */
router.get('/export', async (req, res) => {
  try {
    // 初始化服务
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionMiddleware.permissionService.hasPermission(
      req.user.id, 
      'system.audit.view'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法导出审计日志'
        }
      });
    }

    // 解析查询参数
    const {
      format = 'csv',
      userId,
      action,
      resource,
      result,
      startDate,
      endDate,
      limit = 10000
    } = req.query;

    // 构建筛选条件
    const filters = {
      userId: userId ? parseInt(userId) : undefined,
      action,
      resource,
      result,
      startDate,
      endDate,
      limit: parseInt(limit)
    };

    // 导出审计日志
    const exportData = await auditService.exportAuditLogs(filters, format);

    // 设置响应头
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `audit_logs_${timestamp}.${format}`;
    
    res.setHeader('Content-Type', format === 'csv' ? 'text/csv' : 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    // 记录导出操作
    await auditService.logSensitiveOperation(
      req.user.id,
      'audit_log_export',
      'audit_logs',
      'export',
      {
        format,
        filters,
        recordCount: exportData.split('\n').length - 1 // CSV行数估算
      },
      {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.send(exportData);

  } catch (error) {
    console.error('导出审计日志失败:', error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '导出审计日志失败'
      }
    });
  }
});

/**
 * GET /api/v1/audit/anomalies
 * 检测异常访问模式
 */
router.get('/anomalies', async (req, res) => {
  try {
    // 初始化服务
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionMiddleware.permissionService.hasPermission(
      req.user.id, 
      'system.audit.view'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法查看异常检测'
        }
      });
    }

    // 解析查询参数
    const {
      timeWindow = 3600,    // 默认1小时
      maxFailures = 10,     // 默认最大失败次数
      maxRequests = 1000    // 默认最大请求次数
    } = req.query;

    const options = {
      timeWindow: parseInt(timeWindow),
      maxFailures: parseInt(maxFailures),
      maxRequests: parseInt(maxRequests)
    };

    // 检测异常访问
    const anomalies = await auditService.detectAnomalousAccess(options);

    // 记录异常检测访问
    await auditService.logPermissionAccess(
      req.user.id,
      'anomaly_detection',
      'security_analysis',
      'success',
      {
        options,
        anomalyCount: anomalies.length,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.json({
      success: true,
      data: {
        anomalies,
        detectionTime: new Date().toISOString(),
        options
      },
      message: '异常检测完成'
    });

  } catch (error) {
    console.error('异常检测失败:', error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '异常检测失败'
      }
    });
  }
});

/**
 * POST /api/v1/audit/cleanup
 * 清理过期审计日志
 */
router.post('/cleanup', async (req, res) => {
  try {
    // 初始化服务
    initializeServices(req.db);
    
    // 权限检查 - 需要系统管理员权限
    const hasPermission = await permissionMiddleware.permissionService.hasPermission(
      req.user.id, 
      'system.backup.create'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法执行日志清理'
        }
      });
    }

    // 执行清理
    const deletedCount = await auditService.cleanupExpiredLogs();

    // 记录清理操作
    await auditService.logSensitiveOperation(
      req.user.id,
      'audit_log_cleanup',
      'audit_logs',
      'cleanup',
      {
        deletedCount
      },
      {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.json({
      success: true,
      data: {
        deletedCount,
        cleanupTime: new Date().toISOString()
      },
      message: `成功清理 ${deletedCount} 条过期日志`
    });

  } catch (error) {
    console.error('清理审计日志失败:', error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '清理审计日志失败'
      }
    });
  }
});

/**
 * GET /api/v1/audit/user/:userId/history
 * 获取用户操作历史
 */
router.get('/user/:userId/history', async (req, res) => {
  try {
    // 初始化服务
    initializeServices(req.db);
    
    const { userId } = req.params;
    const targetUserId = parseInt(userId);

    // 权限检查 - 管理员或用户本人
    const isAdmin = await permissionMiddleware.permissionService.hasRole(req.user.id, ['admin', 'super_admin']);
    const isSelf = req.user.id === targetUserId;
    
    if (!isAdmin && !isSelf) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，只能查看自己的操作历史'
        }
      });
    }

    // 解析查询参数
    const {
      limit = 50,
      startDate,
      endDate
    } = req.query;

    const filters = {
      limit: parseInt(limit),
      startDate,
      endDate
    };

    // 获取用户历史
    const history = await auditService.getAuditLogs({
      userId: targetUserId,
      ...filters
    });

    // 记录历史查看
    await auditService.logPermissionAccess(
      req.user.id,
      'user_history_access',
      'user_audit_history',
      'success',
      {
        targetUserId,
        filters,
        recordCount: history.logs.length,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.json({
      success: true,
      data: {
        userId: targetUserId,
        logs: history.logs,
        pagination: history.pagination
      },
      message: '用户操作历史获取成功'
    });

  } catch (error) {
    console.error('获取用户操作历史失败:', error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取用户操作历史失败'
      }
    });
  }
});

/**
 * POST /api/v1/audit/reports
 * 创建审计报告
 */
router.post('/reports', async (req, res) => {
  try {
    // 初始化服务
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionMiddleware.permissionService.hasPermission(
      req.user.id, 
      'system.audit.view'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法创建审计报告'
        }
      });
    }

    const {
      reportType = 'summary',
      startDate,
      endDate,
      includeUsers = true,
      includeActions = true,
      includeAnomalies = true
    } = req.body;

    // 生成报告数据
    const reportData = {
      reportType,
      generatedAt: new Date().toISOString(),
      generatedBy: req.user.id,
      period: {
        startDate,
        endDate
      },
      data: {}
    };

    // 获取统计数据
    if (includeActions || includeUsers) {
      const statistics = await auditService.getAuditStatistics({
        startDate,
        endDate
      });
      
      if (includeActions) {
        reportData.data.actionStatistics = statistics.actionStatistics;
      }
      
      if (includeUsers) {
        reportData.data.userStatistics = statistics.userStatistics;
      }
    }

    // 获取异常数据
    if (includeAnomalies) {
      const anomalies = await auditService.detectAnomalousAccess({
        timeWindow: 86400, // 24小时
        maxFailures: 5,
        maxRequests: 500
      });
      reportData.data.anomalies = anomalies;
    }

    // 记录报告生成
    await auditService.logSensitiveOperation(
      req.user.id,
      'audit_report_generation',
      'audit_report',
      'create',
      {
        reportType,
        period: reportData.period,
        includeUsers,
        includeActions,
        includeAnomalies
      },
      {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.json({
      success: true,
      data: reportData,
      message: '审计报告生成成功'
    });

  } catch (error) {
    console.error('创建审计报告失败:', error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '创建审计报告失败'
      }
    });
  }
});

/**
 * GET /api/v1/audit/config
 * 获取审计配置
 */
router.get('/config', async (req, res) => {
  try {
    // 初始化服务
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionMiddleware.permissionService.hasPermission(
      req.user.id, 
      'system.settings.view'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法查看审计配置'
        }
      });
    }

    // 获取审计配置
    const config = auditService.getConfig();

    res.json({
      success: true,
      data: config,
      message: '审计配置获取成功'
    });

  } catch (error) {
    console.error('获取审计配置失败:', error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取审计配置失败'
      }
    });
  }
});

/**
 * PUT /api/v1/audit/config
 * 更新审计配置
 */
router.put('/config', async (req, res) => {
  try {
    // 初始化服务
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionMiddleware.permissionService.hasPermission(
      req.user.id, 
      'system.settings.edit'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法修改审计配置'
        }
      });
    }

    const newConfig = req.body;

    // 更新配置
    auditService.updateConfig(newConfig);

    // 记录配置变更
    await auditService.logSensitiveOperation(
      req.user.id,
      'audit_config_update',
      'audit_config',
      'update',
      {
        oldConfig: auditService.getConfig(),
        newConfig
      },
      {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.json({
      success: true,
      data: auditService.getConfig(),
      message: '审计配置更新成功'
    });

  } catch (error) {
    console.error('更新审计配置失败:', error);
    
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '更新审计配置失败'
      }
    });
  }
});

module.exports = router;