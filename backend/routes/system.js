/**
 * 系统管理路由
 * 包含权限系统健康检查、配置管理等接口
 */

const express = require('express');
const router = express.Router();
const PermissionConfigService = require('../services/PermissionConfigService');
const PermissionCacheService = require('../services/PermissionCacheService');
const PermissionService = require('../services/PermissionService');

// 权限系统健康检查
router.get('/permission-health', async (req, res) => {
  try {
    const health = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      checks: []
    };

    // 数据库连接检查
    try {
      await req.db.execute('SELECT 1');
      health.checks.push({ 
        component: 'database', 
        status: 'ok',
        message: '数据库连接正常'
      });
    } catch (error) {
      health.checks.push({ 
        component: 'database', 
        status: 'error', 
        message: error.message 
      });
      health.status = 'unhealthy';
    }

    // 权限服务检查
    try {
      const permissionService = new PermissionService(req.db);
      // 执行一个简单的权限检查测试
      await permissionService.hasPermission(1, 'test.permission');
      health.checks.push({ 
        component: 'permission_service', 
        status: 'ok',
        message: '权限服务正常'
      });
    } catch (error) {
      health.checks.push({ 
        component: 'permission_service', 
        status: 'error', 
        message: error.message 
      });
      health.status = 'degraded';
    }

    // 缓存服务检查
    try {
      const cacheService = new PermissionCacheService(req.db);
      const metrics = cacheService.getMetrics();
      health.checks.push({ 
        component: 'cache_service', 
        status: 'ok',
        message: '缓存服务正常',
        metrics: {
          hitRate: metrics.overallHitRate,
          size: metrics.memoryCacheSize,
          totalRequests: metrics.totalRequests
        }
      });
    } catch (error) {
      health.checks.push({ 
        component: 'cache_service', 
        status: 'error', 
        message: error.message 
      });
      health.status = 'degraded';
    }

    // 配置服务检查
    try {
      const configService = new PermissionConfigService(req.db);
      const config = configService.getConfiguration();
      const envInfo = configService.getEnvironmentInfo();
      
      health.checks.push({ 
        component: 'config_service', 
        status: 'ok',
        message: '配置服务正常',
        info: {
          version: envInfo.version,
          environment: envInfo.environment,
          lastLoaded: envInfo.lastLoaded
        }
      });
    } catch (error) {
      health.checks.push({ 
        component: 'config_service', 
        status: 'error', 
        message: error.message 
      });
      health.status = 'degraded';
    }

    // 性能指标
    const memoryUsage = process.memoryUsage();
    health.performance = {
      uptime: process.uptime(),
      memory: {
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
        rss: Math.round(memoryUsage.rss / 1024 / 1024)
      },
      cpu: process.cpuUsage()
    };

    res.json({
      success: true,
      data: health
    });

  } catch (error) {
    console.error('权限系统健康检查失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_FAILED',
        message: '健康检查失败',
        details: error.message
      }
    });
  }
});

// 获取权限系统状态
router.get('/permission-status', async (req, res) => {
  try {
    const cacheService = new PermissionCacheService(req.db);
    const configService = new PermissionConfigService(req.db);
    
    const status = {
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      
      // 缓存状态
      cache: {
        ...cacheService.getMetrics(),
        config: cacheService.getConfig()
      },
      
      // 配置状态
      config: configService.getEnvironmentInfo(),
      
      // 数据库统计
      database: await getDatabaseStats(req.db),
      
      // 性能指标
      performance: {
        memory: process.memoryUsage(),
        cpu: process.cpuUsage()
      }
    };

    res.json({
      success: true,
      data: status
    });

  } catch (error) {
    console.error('获取权限系统状态失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'STATUS_FETCH_FAILED',
        message: '获取系统状态失败',
        details: error.message
      }
    });
  }
});

// 清除权限缓存
router.delete('/permission-cache', async (req, res) => {
  try {
    const { userId, role } = req.query;
    const cacheService = new PermissionCacheService(req.db);
    
    let result;
    
    if (userId) {
      // 清除指定用户的缓存
      result = await cacheService.clearUserPermissions(parseInt(userId));
      result = { clearedUsers: [parseInt(userId)], clearedCount: 1 };
    } else if (role) {
      // 清除指定角色的缓存
      result = await cacheService.clearRolePermissions(role);
    } else {
      // 清除所有缓存
      result = await cacheService.clearAllCache();
      result = { message: '所有权限缓存已清除' };
    }

    res.json({
      success: true,
      data: result,
      message: '权限缓存清除成功'
    });

  } catch (error) {
    console.error('清除权限缓存失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CACHE_CLEAR_FAILED',
        message: '清除权限缓存失败',
        details: error.message
      }
    });
  }
});

// 重新加载权限配置
router.post('/reload-permission-config', async (req, res) => {
  try {
    const configService = new PermissionConfigService(req.db);
    const result = await configService.reloadConfiguration();
    
    // 清除相关缓存
    const cacheService = new PermissionCacheService(req.db);
    await cacheService.clearAllCache();

    res.json({
      success: true,
      data: {
        configVersion: configService.getConfigVersion(),
        reloadedAt: new Date().toISOString(),
        changes: result.changes
      },
      message: '权限配置重新加载成功'
    });

  } catch (error) {
    console.error('重新加载权限配置失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CONFIG_RELOAD_FAILED',
        message: '重新加载权限配置失败',
        details: error.message
      }
    });
  }
});

// 获取权限配置
router.get('/permission-config', async (req, res) => {
  try {
    const configService = new PermissionConfigService(req.db);
    const config = configService.getConfiguration();
    const envInfo = configService.getEnvironmentInfo();

    res.json({
      success: true,
      data: {
        config,
        environment: envInfo
      }
    });

  } catch (error) {
    console.error('获取权限配置失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CONFIG_FETCH_FAILED',
        message: '获取权限配置失败',
        details: error.message
      }
    });
  }
});

// 更新权限配置
router.put('/permission-config', async (req, res) => {
  try {
    const { config } = req.body;
    const operatorId = req.user?.id;

    if (!config) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_CONFIG',
          message: '无效的配置数据'
        }
      });
    }

    const configService = new PermissionConfigService(req.db);
    await configService.updateConfiguration(config, operatorId);

    res.json({
      success: true,
      message: '权限配置更新成功'
    });

  } catch (error) {
    console.error('更新权限配置失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CONFIG_UPDATE_FAILED',
        message: '更新权限配置失败',
        details: error.message
      }
    });
  }
});

// 备份权限配置
router.post('/backup-permission-config', async (req, res) => {
  try {
    const configService = new PermissionConfigService(req.db);
    const backupFile = await configService.backupConfiguration();

    res.json({
      success: true,
      data: {
        backupFile: backupFile.split('/').pop(), // 只返回文件名
        timestamp: new Date().toISOString()
      },
      message: '权限配置备份成功'
    });

  } catch (error) {
    console.error('备份权限配置失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'CONFIG_BACKUP_FAILED',
        message: '备份权限配置失败',
        details: error.message
      }
    });
  }
});

// 权限系统诊断
router.get('/permission-diagnosis/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const PermissionDiagnostics = require('../scripts/permission-diagnosis');
    
    const diagnostics = new PermissionDiagnostics(req.db);
    const diagnosis = await diagnostics.diagnoseUserPermissions(parseInt(userId));

    res.json({
      success: true,
      data: diagnosis
    });

  } catch (error) {
    console.error('权限系统诊断失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'DIAGNOSIS_FAILED',
        message: '权限系统诊断失败',
        details: error.message
      }
    });
  }
});

// 权限系统性能指标
router.get('/permission-metrics', async (req, res) => {
  try {
    const cacheService = new PermissionCacheService(req.db);
    const permissionService = new PermissionService(req.db);
    
    // 获取缓存指标
    const cacheMetrics = cacheService.getMetrics();
    
    // 获取权限统计
    const permissionStats = await permissionService.getPermissionStatistics();
    
    // 系统性能指标
    const systemMetrics = {
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage()
    };

    res.json({
      success: true,
      data: {
        cache: cacheMetrics,
        permissions: permissionStats,
        system: systemMetrics,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('获取权限系统性能指标失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'METRICS_FETCH_FAILED',
        message: '获取性能指标失败',
        details: error.message
      }
    });
  }
});

// 辅助函数：获取数据库统计信息
async function getDatabaseStats(db) {
  try {
    const [stats] = await db.execute(`
      SELECT 
        (SELECT COUNT(*) FROM users) as user_count,
        (SELECT COUNT(*) FROM permissions) as permission_count,
        (SELECT COUNT(*) FROM role_permissions) as role_permission_count,
        (SELECT COUNT(*) FROM user_custom_permissions) as custom_permission_count,
        (SELECT COUNT(*) FROM permission_cache) as cached_permission_count,
        (SELECT COUNT(*) FROM audit_logs WHERE action LIKE '%permission%') as permission_audit_count
    `);

    return stats[0];
  } catch (error) {
    return { error: error.message };
  }
}

module.exports = router;