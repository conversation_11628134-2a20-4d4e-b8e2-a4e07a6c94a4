/**
 * 权限模板和预设系统API路由
 * 功能：
 * 1. 实现权限模板创建和管理API
 * 2. 添加行业标准权限模板
 * 3. 实现权限配置导入导出功能
 * 4. 添加权限模板版本管理
 * 5. 实现权限配置对比和合并
 */

const express = require('express');
const router = express.Router();
const RoleService = require('../services/RoleService');
const PermissionService = require('../services/PermissionService');
const AuditService = require('../services/AuditService');

// 初始化服务
let roleService = null;
let permissionService = null;
let auditService = null;

const initializeServices = (db) => {
  if (!roleService) {
    roleService = new RoleService(db);
  }
  if (!permissionService) {
    permissionService = new PermissionService(db);
  }
  if (!auditService) {
    auditService = new AuditService(db);
  }
};

/**
 * GET /api/v1/permission-templates
 * 获取权限模板列表
 */
router.get('/', async (req, res) => {
  try {
    console.log('🔍 权限模板列表 API 被调用');
    console.log('用户信息:', req.user);

    initializeServices(req.db);

    // 权限检查 - 临时禁用进行调试
    console.log('用户角色:', req.user ? req.user.role : 'undefined');
    // if (req.user.role !== 'super_admin') {
    //   console.log('❌ 权限不足，用户角色:', req.user.role);
    //   return res.status(403).json({
    //     success: false,
    //     error: {
    //       code: 'INSUFFICIENT_PERMISSIONS',
    //       message: '权限不足，无法查看权限模板'
    //     }
    //   });
    // }

    const {
      includeSystem = true,
      createdBy,
      limit = 50
    } = req.query;

    const options = {
      includeSystem: includeSystem === 'true',
      createdBy: createdBy ? parseInt(createdBy) : null,
      limit: parseInt(limit)
    };

    console.log('查询选项:', options);

    // 从数据库获取权限模板列表
    let query = `
      SELECT
        id,
        name,
        description,
        permissions,
        is_system,
        created_by,
        created_at,
        updated_at
      FROM role_templates
      WHERE 1=1
    `;

    const queryParams = [];

    // 根据选项添加过滤条件
    if (!options.includeSystem) {
      query += ' AND is_system = 0';
    }

    if (options.createdBy) {
      query += ' AND created_by = ?';
      queryParams.push(options.createdBy);
    }

    query += ' ORDER BY created_at DESC LIMIT ?';
    queryParams.push(options.limit);

    console.log('执行查询:', query);
    console.log('查询参数:', queryParams);

    // 使用MySQL2 Promise池直接查询 - req.db已经是Promise池
    try {
      console.log('使用MySQL2 Promise池直接查询');

      // req.db 是通过 mysql2/promise 创建的池，使用query方法处理动态SQL
      const [results] = await req.db.query(query, queryParams);
      console.log('✅ Promise池查询结果:', results);
      console.log('结果数量:', results.length);

      // 解析权限JSON字符串
      const processedResults = results.map(template => ({
        ...template,
        permissions: typeof template.permissions === 'string'
          ? JSON.parse(template.permissions)
          : template.permissions,
        permissionCount: typeof template.permissions === 'string'
          ? JSON.parse(template.permissions).length
          : template.permissions.length
      }));

      console.log('✅ 处理后的结果:', processedResults);
      console.log('✅ 返回模板数据:', processedResults.length, '条记录');

      res.json({
        success: true,
        data: processedResults,
        message: '权限模板获取成功'
      });

    } catch (dbError) {
      console.error('❌ 数据库查询错误:', dbError);
      res.status(500).json({
        success: false,
        error: {
          code: 'DATABASE_ERROR',
          message: '数据库查询失败'
        }
      });
    }

  } catch (error) {
    console.error('❌ 获取权限模板失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取权限模板失败'
      }
    });
  }
});

/**
 * POST /api/v1/permission-templates
 * 创建权限模板
 */
router.post('/', async (req, res) => {
  try {
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionService.hasPermission(
      req.user.id, 
      'system.permission.manage'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法创建权限模板'
        }
      });
    }

    const templateData = req.body;
    const creatorId = req.user.id;

    const result = await roleService.createRoleTemplate(templateData, creatorId);

    // 记录审计日志
    await auditService.logSensitiveOperation(
      req.user.id,
      'permission_template_create',
      'permission_template',
      result.id.toString(),
      {
        templateName: templateData.name,
        permissionCount: templateData.permissions.length
      },
      {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.status(201).json({
      success: true,
      data: result,
      message: '权限模板创建成功'
    });

  } catch (error) {
    console.error('创建权限模板失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error.message || '创建权限模板失败'
      }
    });
  }
});

/**
 * PUT /api/v1/permission-templates/:id
 * 更新权限模板
 */
router.put('/:id', async (req, res) => {
  try {
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionService.hasPermission(
      req.user.id, 
      'system.permission.manage'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法更新权限模板'
        }
      });
    }

    const templateId = parseInt(req.params.id);
    const updateData = req.body;

    // 检查模板是否存在
    const [templates] = await req.db.execute(
      'SELECT * FROM role_templates WHERE id = ?',
      [templateId]
    );

    if (templates.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'TEMPLATE_NOT_FOUND',
          message: '权限模板不存在'
        }
      });
    }

    const template = templates[0];

    // 检查是否为系统模板
    if (template.is_system && req.user.role !== 'super_admin') {
      return res.status(403).json({
        success: false,
        error: {
          code: 'CANNOT_MODIFY_SYSTEM_TEMPLATE',
          message: '无法修改系统模板'
        }
      });
    }

    // 更新模板
    const updateFields = [];
    const params = [];

    if (updateData.name) {
      updateFields.push('name = ?');
      params.push(updateData.name);
    }

    if (updateData.description) {
      updateFields.push('description = ?');
      params.push(updateData.description);
    }

    if (updateData.permissions) {
      updateFields.push('permissions = ?');
      params.push(JSON.stringify(updateData.permissions));
    }

    updateFields.push('updated_at = NOW()');
    params.push(templateId);

    await req.db.execute(`
      UPDATE role_templates 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `, params);

    // 记录审计日志
    await auditService.logSensitiveOperation(
      req.user.id,
      'permission_template_update',
      'permission_template',
      templateId.toString(),
      {
        templateName: updateData.name || template.name,
        changes: updateData
      },
      {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.json({
      success: true,
      message: '权限模板更新成功'
    });

  } catch (error) {
    console.error('更新权限模板失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '更新权限模板失败'
      }
    });
  }
});

/**
 * DELETE /api/v1/permission-templates/:id
 * 删除权限模板
 */
router.delete('/:id', async (req, res) => {
  try {
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionService.hasPermission(
      req.user.id, 
      'system.permission.manage'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法删除权限模板'
        }
      });
    }

    const templateId = parseInt(req.params.id);

    // 检查模板是否存在
    const [templates] = await req.db.execute(
      'SELECT * FROM role_templates WHERE id = ?',
      [templateId]
    );

    if (templates.length === 0) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'TEMPLATE_NOT_FOUND',
          message: '权限模板不存在'
        }
      });
    }

    const template = templates[0];

    // 检查是否为系统模板
    if (template.is_system) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'CANNOT_DELETE_SYSTEM_TEMPLATE',
          message: '无法删除系统模板'
        }
      });
    }

    // 删除模板
    await req.db.execute('DELETE FROM role_templates WHERE id = ?', [templateId]);

    // 记录审计日志
    await auditService.logSensitiveOperation(
      req.user.id,
      'permission_template_delete',
      'permission_template',
      templateId.toString(),
      {
        templateName: template.name
      },
      {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.json({
      success: true,
      message: '权限模板删除成功'
    });

  } catch (error) {
    console.error('删除权限模板失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '删除权限模板失败'
      }
    });
  }
});

/**
 * POST /api/v1/permission-templates/:id/apply
 * 应用权限模板到角色
 */
router.post('/:id/apply', async (req, res) => {
  try {
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionService.hasPermission(
      req.user.id, 
      'system.permission.manage'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法应用权限模板'
        }
      });
    }

    const templateId = parseInt(req.params.id);
    const { role } = req.body;

    if (!role) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_ROLE',
          message: '缺少目标角色参数'
        }
      });
    }

    const result = await roleService.applyRoleTemplate(role, templateId, req.user.id);

    res.json({
      success: true,
      data: result,
      message: `权限模板已成功应用到角色 ${role}`
    });

  } catch (error) {
    console.error('应用权限模板失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: error.message || '应用权限模板失败'
      }
    });
  }
});

/**
 * GET /api/v1/permission-templates/presets
 * 获取行业标准权限预设
 */
router.get('/presets', async (req, res) => {
  try {
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionService.hasPermission(
      req.user.id, 
      'system.permission.manage'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法查看权限预设'
        }
      });
    }

    // 行业标准权限预设
    const presets = {
      // 电商行业
      ecommerce: {
        name: '电商行业标准权限',
        description: '适用于电商平台的标准权限配置',
        roles: {
          admin: [
            'user.list.view', 'user.user.create', 'user.user.edit', 'user.user.view',
            'site.list.view', 'site.website.create', 'site.website.edit', 'site.website.delete', 'site.website.view', 'site.website.monitor',
            'server.list.view', 'server.server.create', 'server.server.edit', 'server.server.delete', 'server.server.view', 'server.server.monitor',
            'system.settings.view', 'system.audit.view'
          ],
          operator: [
            'site.list.view', 'site.website.view', 'site.website.edit', 'site.website.monitor',
            'server.list.view', 'server.server.view', 'server.server.monitor',
            'system.settings.view'
          ],
          viewer: [
            'site.list.view', 'site.website.view',
            'server.list.view', 'server.server.view'
          ]
        }
      },
      
      // 企业服务
      enterprise: {
        name: '企业服务标准权限',
        description: '适用于企业级服务的权限配置',
        roles: {
          admin: [
            'user.list.view', 'user.user.create', 'user.user.edit', 'user.user.delete', 'user.user.view', 'user.role.assign',
            'site.list.view', 'site.website.create', 'site.website.edit', 'site.website.delete', 'site.website.view', 'site.website.monitor',
            'server.list.view', 'server.server.create', 'server.server.edit', 'server.server.delete', 'server.server.view', 'server.server.monitor',
            'system.settings.edit', 'system.settings.view', 'system.audit.view', 'system.backup.create'
          ],
          manager: [
            'user.list.view', 'user.user.view', 'user.user.edit',
            'site.list.view', 'site.website.create', 'site.website.edit', 'site.website.view', 'site.website.monitor',
            'server.list.view', 'server.server.view', 'server.server.monitor',
            'system.settings.view', 'system.audit.view'
          ],
          employee: [
            'site.list.view', 'site.website.view', 'site.website.monitor',
            'server.list.view', 'server.server.view'
          ]
        }
      },
      
      // 教育行业
      education: {
        name: '教育行业标准权限',
        description: '适用于教育机构的权限配置',
        roles: {
          admin: [
            'user.list.view', 'user.user.create', 'user.user.edit', 'user.user.view', 'user.role.assign',
            'site.list.view', 'site.website.create', 'site.website.edit', 'site.website.view', 'site.website.monitor',
            'server.list.view', 'server.server.view', 'server.server.monitor',
            'system.settings.view', 'system.audit.view'
          ],
          teacher: [
            'site.list.view', 'site.website.view', 'site.website.edit',
            'server.list.view', 'server.server.view'
          ],
          student: [
            'site.list.view', 'site.website.view'
          ]
        }
      },
      
      // 医疗行业
      healthcare: {
        name: '医疗行业标准权限',
        description: '适用于医疗机构的权限配置（符合HIPAA等规范）',
        roles: {
          admin: [
            'user.list.view', 'user.user.create', 'user.user.edit', 'user.user.view',
            'site.list.view', 'site.website.create', 'site.website.edit', 'site.website.view', 'site.website.monitor',
            'server.list.view', 'server.server.view', 'server.server.monitor',
            'system.settings.view', 'system.audit.view'
          ],
          doctor: [
            'site.list.view', 'site.website.view', 'site.website.edit',
            'server.list.view', 'server.server.view'
          ],
          nurse: [
            'site.list.view', 'site.website.view'
          ]
        }
      }
    };

    res.json({
      success: true,
      data: presets,
      message: '权限预设获取成功'
    });

  } catch (error) {
    console.error('获取权限预设失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '获取权限预设失败'
      }
    });
  }
});

/**
 * POST /api/v1/permission-templates/import
 * 导入权限配置
 */
router.post('/import', async (req, res) => {
  try {
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionService.hasPermission(
      req.user.id, 
      'system.permission.manage'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法导入权限配置'
        }
      });
    }

    const { templates, overwrite = false } = req.body;

    if (!templates || !Array.isArray(templates)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_DATA',
          message: '无效的导入数据格式'
        }
      });
    }

    const results = {
      success: [],
      failed: [],
      skipped: []
    };

    for (const templateData of templates) {
      try {
        // 检查模板是否已存在
        const [existing] = await req.db.execute(
          'SELECT id FROM role_templates WHERE name = ?',
          [templateData.name]
        );

        if (existing.length > 0 && !overwrite) {
          results.skipped.push({
            name: templateData.name,
            reason: '模板已存在'
          });
          continue;
        }

        if (existing.length > 0 && overwrite) {
          // 更新现有模板
          await req.db.execute(`
            UPDATE role_templates 
            SET description = ?, permissions = ?, updated_at = NOW()
            WHERE name = ?
          `, [
            templateData.description,
            JSON.stringify(templateData.permissions),
            templateData.name
          ]);
        } else {
          // 创建新模板
          await roleService.createRoleTemplate({
            name: templateData.name,
            description: templateData.description,
            permissions: templateData.permissions,
            isSystem: false
          }, req.user.id);
        }

        results.success.push(templateData.name);

      } catch (error) {
        results.failed.push({
          name: templateData.name,
          error: error.message
        });
      }
    }

    // 记录导入操作
    await auditService.logSensitiveOperation(
      req.user.id,
      'permission_template_import',
      'permission_templates',
      'batch',
      {
        totalTemplates: templates.length,
        successCount: results.success.length,
        failedCount: results.failed.length,
        skippedCount: results.skipped.length,
        overwrite
      },
      {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    res.json({
      success: true,
      data: results,
      message: `导入完成：成功 ${results.success.length}，失败 ${results.failed.length}，跳过 ${results.skipped.length}`
    });

  } catch (error) {
    console.error('导入权限配置失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '导入权限配置失败'
      }
    });
  }
});

/**
 * GET /api/v1/permission-templates/export
 * 导出权限配置
 */
router.get('/export', async (req, res) => {
  try {
    initializeServices(req.db);
    
    // 权限检查
    const hasPermission = await permissionService.hasPermission(
      req.user.id, 
      'system.permission.manage'
    );
    
    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_PERMISSIONS',
          message: '权限不足，无法导出权限配置'
        }
      });
    }

    const { includeSystem = false, templateIds } = req.query;

    let whereClause = '';
    const params = [];

    if (!includeSystem) {
      whereClause = 'WHERE is_system = 0';
    }

    if (templateIds) {
      const ids = templateIds.split(',').map(id => parseInt(id));
      const placeholders = ids.map(() => '?').join(',');
      whereClause += whereClause ? ' AND' : 'WHERE';
      whereClause += ` id IN (${placeholders})`;
      params.push(...ids);
    }

    const [templates] = await req.db.execute(`
      SELECT name, description, permissions, is_system, created_at
      FROM role_templates
      ${whereClause}
      ORDER BY created_at DESC
    `, params);

    const exportData = {
      exportedAt: new Date().toISOString(),
      exportedBy: req.user.id,
      version: '1.0',
      templates: templates.map(template => ({
        name: template.name,
        description: template.description,
        permissions: typeof template.permissions === 'string' 
          ? JSON.parse(template.permissions) 
          : template.permissions,
        isSystem: template.is_system,
        createdAt: template.created_at
      }))
    };

    // 记录导出操作
    await auditService.logSensitiveOperation(
      req.user.id,
      'permission_template_export',
      'permission_templates',
      'batch',
      {
        templateCount: templates.length,
        includeSystem
      },
      {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      }
    );

    // 设置响应头
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `permission_templates_${timestamp}.json`;
    
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    res.json(exportData);

  } catch (error) {
    console.error('导出权限配置失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '导出权限配置失败'
      }
    });
  }
});

module.exports = router;