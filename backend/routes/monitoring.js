const express = require('express');
const router = express.Router();
const PermissionMonitoringService = require('../services/PermissionMonitoringService');

/**
 * 权限系统监控API路由
 */

/**
 * 获取权限系统监控概览
 */
router.get('/overview', 
  async (req, res) => {
    try {
      const metrics = PermissionMonitoringService.getMetrics();
      const performanceMetrics = PermissionMonitoringService.getPerformanceMetrics();
      const securityMetrics = PermissionMonitoringService.getSecurityMetrics();
      
      res.json({
        success: true,
        data: {
          overview: {
            status: 'healthy', // 可以根据指标动态计算
            uptime: metrics.uptime,
            timestamp: metrics.timestamp
          },
          performance: performanceMetrics,
          security: securityMetrics,
          cache: metrics.cacheMetrics,
          alertsCount: metrics.alerts.filter(a => !a.resolved).length
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'MONITORING_OVERVIEW_ERROR',
          message: '获取监控概览失败',
          details: error.message
        }
      });
    }
  }
);

/**
 * 获取性能指标
 */
router.get('/performance', 
  async (req, res) => {
    try {
      const metrics = PermissionMonitoringService.getPerformanceMetrics();
      const fullMetrics = PermissionMonitoringService.getMetrics();
      
      // 获取详细的响应时间数据
      const responseTimeData = fullMetrics.permissionChecks.responseTimes
        .slice(-100) // 最近100次检查
        .map(item => ({
          time: item.time,
          timestamp: item.timestamp,
          success: item.success,
          permission: item.permission
        }));
      
      res.json({
        success: true,
        data: {
          summary: metrics,
          responseTimeHistory: responseTimeData,
          thresholds: {
            maxResponseTime: 100,
            warningResponseTime: 50
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'PERFORMANCE_METRICS_ERROR',
          message: '获取性能指标失败',
          details: error.message
        }
      });
    }
  }
);

/**
 * 获取安全指标
 */
router.get('/security', 
  async (req, res) => {
    try {
      const securityMetrics = PermissionMonitoringService.getSecurityMetrics();
      
      res.json({
        success: true,
        data: {
          metrics: securityMetrics,
          thresholds: {
            maxDeniedAccess: 10,
            maxFailedLogins: 5,
            suspiciousActivityThreshold: 80
          },
          riskLevel: securityMetrics.suspiciousScore > 80 ? 'high' : 
                    securityMetrics.suspiciousScore > 50 ? 'medium' : 'low'
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'SECURITY_METRICS_ERROR',
          message: '获取安全指标失败',
          details: error.message
        }
      });
    }
  }
);

/**
 * 获取缓存指标
 */
router.get('/cache', 
  async (req, res) => {
    try {
      const metrics = PermissionMonitoringService.getMetrics();
      
      res.json({
        success: true,
        data: {
          metrics: metrics.cacheMetrics,
          recommendations: {
            hitRate: metrics.cacheMetrics.hitRate < 0.8 ? 
              '缓存命中率较低，建议检查缓存配置' : '缓存性能良好',
            size: metrics.cacheMetrics.size > 5000 ? 
              '缓存大小较大，建议清理过期数据' : '缓存大小正常'
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'CACHE_METRICS_ERROR',
          message: '获取缓存指标失败',
          details: error.message
        }
      });
    }
  }
);

/**
 * 获取告警列表
 */
router.get('/alerts', 
  async (req, res) => {
    try {
      const { limit = 50, resolved } = req.query;
      let alerts = PermissionMonitoringService.getAlerts(parseInt(limit));
      
      // 过滤已解决/未解决的告警
      if (resolved !== undefined) {
        const isResolved = resolved === 'true';
        alerts = alerts.filter(alert => alert.resolved === isResolved);
      }
      
      res.json({
        success: true,
        data: {
          alerts,
          summary: {
            total: alerts.length,
            unresolved: alerts.filter(a => !a.resolved).length,
            critical: alerts.filter(a => a.level === 'critical').length,
            error: alerts.filter(a => a.level === 'error').length,
            warning: alerts.filter(a => a.level === 'warning').length
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'ALERTS_ERROR',
          message: '获取告警列表失败',
          details: error.message
        }
      });
    }
  }
);

/**
 * 解决告警
 */
router.post('/alerts/:alertId/resolve', 
  async (req, res) => {
    try {
      const { alertId } = req.params;
      const resolved = PermissionMonitoringService.resolveAlert(alertId);
      
      if (resolved) {
        res.json({
          success: true,
          message: '告警已解决'
        });
      } else {
        res.status(404).json({
          success: false,
          error: {
            code: 'ALERT_NOT_FOUND',
            message: '告警不存在'
          }
        });
      }
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'RESOLVE_ALERT_ERROR',
          message: '解决告警失败',
          details: error.message
        }
      });
    }
  }
);

/**
 * 获取实时监控数据（WebSocket或Server-Sent Events）
 */
router.get('/realtime', 
  async (req, res) => {
    try {
      // 设置Server-Sent Events响应头
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });
      
      // 发送初始数据
      const sendData = () => {
        const metrics = PermissionMonitoringService.getMetrics();
        const data = {
          performance: PermissionMonitoringService.getPerformanceMetrics(),
          security: PermissionMonitoringService.getSecurityMetrics(),
          cache: metrics.cacheMetrics,
          timestamp: new Date().toISOString()
        };
        
        res.write(`data: ${JSON.stringify(data)}\n\n`);
      };
      
      // 立即发送一次数据
      sendData();
      
      // 每10秒发送一次数据
      const interval = setInterval(sendData, 10000);
      
      // 监听权限检查事件
      const onPermissionCheck = (data) => {
        res.write(`event: permissionCheck\ndata: ${JSON.stringify(data)}\n\n`);
      };
      
      // 监听告警事件
      const onAlert = (alert) => {
        res.write(`event: alert\ndata: ${JSON.stringify(alert)}\n\n`);
      };
      
      PermissionMonitoringService.on('permissionCheck', onPermissionCheck);
      PermissionMonitoringService.on('alert', onAlert);
      
      // 客户端断开连接时清理
      req.on('close', () => {
        clearInterval(interval);
        PermissionMonitoringService.off('permissionCheck', onPermissionCheck);
        PermissionMonitoringService.off('alert', onAlert);
      });
      
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'REALTIME_ERROR',
          message: '实时监控连接失败',
          details: error.message
        }
      });
    }
  }
);

/**
 * 重置监控指标
 */
router.post('/reset', 
  async (req, res) => {
    try {
      PermissionMonitoringService.resetMetrics();
      
      res.json({
        success: true,
        message: '监控指标已重置',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'RESET_METRICS_ERROR',
          message: '重置监控指标失败',
          details: error.message
        }
      });
    }
  }
);

/**
 * 获取监控配置
 */
router.get('/config', 
  async (req, res) => {
    try {
      res.json({
        success: true,
        data: {
          thresholds: {
            maxResponseTime: 100,
            maxDeniedAccessCount: 10,
            maxFailedLoginCount: 5,
            suspiciousActivityThreshold: 80
          },
          intervals: {
            cacheMetrics: 30000,
            securityMetrics: 60000,
            cleanup: 300000
          },
          retention: {
            metricsRetentionTime: 24 * 60 * 60 * 1000,
            alertCooldown: 5 * 60 * 1000
          }
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'CONFIG_ERROR',
          message: '获取监控配置失败',
          details: error.message
        }
      });
    }
  }
);

/**
 * 更新监控配置
 */
router.put('/config', 
  async (req, res) => {
    try {
      const { thresholds } = req.body;
      
      if (thresholds) {
        // 更新监控服务的配置
        if (thresholds.maxResponseTime) {
          PermissionMonitoringService.config.maxResponseTime = thresholds.maxResponseTime;
        }
        if (thresholds.maxDeniedAccessCount) {
          PermissionMonitoringService.config.maxDeniedAccessCount = thresholds.maxDeniedAccessCount;
        }
        if (thresholds.maxFailedLoginCount) {
          PermissionMonitoringService.config.maxFailedLoginCount = thresholds.maxFailedLoginCount;
        }
        if (thresholds.suspiciousActivityThreshold) {
          PermissionMonitoringService.config.suspiciousActivityThreshold = thresholds.suspiciousActivityThreshold;
        }
      }
      
      res.json({
        success: true,
        message: '监控配置已更新',
        data: PermissionMonitoringService.config
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: {
          code: 'UPDATE_CONFIG_ERROR',
          message: '更新监控配置失败',
          details: error.message
        }
      });
    }
  }
);

module.exports = router;