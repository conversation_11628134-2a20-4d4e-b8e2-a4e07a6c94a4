const express = require('express');
const router = express.Router();
const EnhancedPermissionService = require('../services/EnhancedPermissionService');
const { authenticateToken } = require('../auth');

/**
 * 增强版权限管理API路由
 */

// 简单的权限检查中间件
const requireSuperAdmin = (req, res, next) => {
    if (req.user.role !== 'super_admin') {
        return res.status(403).json({
            success: false,
            message: '需要超级管理员权限'
        });
    }
    next();
};

// 获取所有页面权限定义
router.get('/page/all', authenticateToken, async (req, res) => {
    try {
        const permissions = await EnhancedPermissionService.getAllPagePermissions();
        res.json({
            success: true,
            data: permissions,
            message: '获取页面权限列表成功'
        });
    } catch (error) {
        console.error('获取页面权限列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取页面权限列表失败',
            error: error.message
        });
    }
});

// 获取用户页面权限
router.get('/user/:userId/page', authenticateToken, async (req, res) => {
    try {
        const { userId } = req.params;
        
        // 检查权限：只有超级管理员或用户本人可以查看
        if (req.user.role !== 'super_admin' && req.user.id !== parseInt(userId)) {
            return res.status(403).json({
                success: false,
                message: '无权限查看该用户的权限信息'
            });
        }

        const permissions = await EnhancedPermissionService.getUserPagePermissions(parseInt(userId));
        res.json({
            success: true,
            data: permissions,
            message: '获取用户页面权限成功'
        });
    } catch (error) {
        console.error('获取用户页面权限失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户页面权限失败',
            error: error.message
        });
    }
});

// 获取用户网站权限
router.get('/user/:userId/websites', authenticateToken, async (req, res) => {
    try {
        const { userId } = req.params;
        
        // 检查权限：只有超级管理员或用户本人可以查看
        if (req.user.role !== 'super_admin' && req.user.id !== parseInt(userId)) {
            return res.status(403).json({
                success: false,
                message: '无权限查看该用户的权限信息'
            });
        }

        const permissions = await EnhancedPermissionService.getUserWebsitePermissions(parseInt(userId));
        res.json({
            success: true,
            data: permissions,
            message: '获取用户网站权限成功'
        });
    } catch (error) {
        console.error('获取用户网站权限失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户网站权限失败',
            error: error.message
        });
    }
});

// 获取用户服务器权限
router.get('/user/:userId/servers', authenticateToken, async (req, res) => {
    try {
        const { userId } = req.params;
        
        // 检查权限：只有超级管理员或用户本人可以查看
        if (req.user.role !== 'super_admin' && req.user.id !== parseInt(userId)) {
            return res.status(403).json({
                success: false,
                message: '无权限查看该用户的权限信息'
            });
        }

        const permissions = await EnhancedPermissionService.getUserServerPermissions(parseInt(userId));
        res.json({
            success: true,
            data: permissions,
            message: '获取用户服务器权限成功'
        });
    } catch (error) {
        console.error('获取用户服务器权限失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户服务器权限失败',
            error: error.message
        });
    }
});

// 获取用户完整权限信息
router.get('/user/:userId/complete', authenticateToken, async (req, res) => {
    try {
        const { userId } = req.params;
        
        // 检查权限：只有超级管理员或用户本人可以查看
        if (req.user.role !== 'super_admin' && req.user.id !== parseInt(userId)) {
            return res.status(403).json({
                success: false,
                message: '无权限查看该用户的权限信息'
            });
        }

        const permissions = await EnhancedPermissionService.getUserCompletePermissions(parseInt(userId));
        res.json({
            success: true,
            data: permissions,
            message: '获取用户完整权限信息成功'
        });
    } catch (error) {
        console.error('获取用户完整权限信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取用户完整权限信息失败',
            error: error.message
        });
    }
});

// 更新用户页面权限
router.put('/user/:userId/page', authenticateToken, requireSuperAdmin, async (req, res) => {
    try {
        const { userId } = req.params;
        const { permissions } = req.body;

        if (!Array.isArray(permissions)) {
            return res.status(400).json({
                success: false,
                message: '权限数据格式错误'
            });
        }

        const result = await EnhancedPermissionService.updateUserPagePermissions(
            parseInt(userId),
            permissions,
            req.user.id
        );

        // 记录权限变更日志
        await EnhancedPermissionService.logPermissionChange(
            parseInt(userId),
            req.user.id,
            'page_permissions_update',
            { permissions }
        );

        res.json(result);
    } catch (error) {
        console.error('更新用户页面权限失败:', error);
        res.status(500).json({
            success: false,
            message: '更新用户页面权限失败',
            error: error.message
        });
    }
});

// 更新用户网站权限
router.put('/user/:userId/websites', authenticateToken, requireSuperAdmin, async (req, res) => {
    try {
        const { userId } = req.params;
        const { websitePermissions } = req.body;

        if (!Array.isArray(websitePermissions)) {
            return res.status(400).json({
                success: false,
                message: '网站权限数据格式错误'
            });
        }

        const result = await EnhancedPermissionService.updateUserWebsitePermissions(
            parseInt(userId),
            websitePermissions,
            req.user.id
        );

        // 记录权限变更日志
        await EnhancedPermissionService.logPermissionChange(
            parseInt(userId),
            req.user.id,
            'website_permissions_update',
            { websitePermissions }
        );

        res.json(result);
    } catch (error) {
        console.error('更新用户网站权限失败:', error);
        res.status(500).json({
            success: false,
            message: '更新用户网站权限失败',
            error: error.message
        });
    }
});

// 更新用户服务器权限
router.put('/user/:userId/servers', authenticateToken, requireSuperAdmin, async (req, res) => {
    try {
        const { userId } = req.params;
        const { serverPermissions } = req.body;

        if (!Array.isArray(serverPermissions)) {
            return res.status(400).json({
                success: false,
                message: '服务器权限数据格式错误'
            });
        }

        const result = await EnhancedPermissionService.updateUserServerPermissions(
            parseInt(userId),
            serverPermissions,
            req.user.id
        );

        // 记录权限变更日志
        await EnhancedPermissionService.logPermissionChange(
            parseInt(userId),
            req.user.id,
            'server_permissions_update',
            { serverPermissions }
        );

        res.json(result);
    } catch (error) {
        console.error('更新用户服务器权限失败:', error);
        res.status(500).json({
            success: false,
            message: '更新用户服务器权限失败',
            error: error.message
        });
    }
});

// 批量更新用户权限
router.put('/user/:userId/batch', authenticateToken, requireSuperAdmin, async (req, res) => {
    try {
        const { userId } = req.params;
        const permissionData = req.body;

        const result = await EnhancedPermissionService.updateUserPermissions(
            parseInt(userId),
            permissionData,
            req.user.id
        );

        // 记录权限变更日志
        await EnhancedPermissionService.logPermissionChange(
            parseInt(userId),
            req.user.id,
            'batch_permissions_update',
            permissionData
        );

        res.json(result);
    } catch (error) {
        console.error('批量更新用户权限失败:', error);
        res.status(500).json({
            success: false,
            message: '批量更新用户权限失败',
            error: error.message
        });
    }
});

// 检查用户页面权限
router.get('/check/user/:userId/page/:permissionCode', authenticateToken, async (req, res) => {
    try {
        const { userId, permissionCode } = req.params;
        
        const hasPermission = await EnhancedPermissionService.checkUserPagePermission(
            parseInt(userId),
            permissionCode
        );

        res.json({
            success: true,
            data: { hasPermission },
            message: '权限检查完成'
        });
    } catch (error) {
        console.error('检查用户页面权限失败:', error);
        res.status(500).json({
            success: false,
            message: '检查用户页面权限失败',
            error: error.message
        });
    }
});

// 检查用户网站权限
router.get('/check/user/:userId/website/:websiteId/:action', authenticateToken, async (req, res) => {
    try {
        const { userId, websiteId, action } = req.params;
        
        const hasPermission = await EnhancedPermissionService.checkUserWebsitePermission(
            parseInt(userId),
            parseInt(websiteId),
            action
        );

        res.json({
            success: true,
            data: { hasPermission },
            message: '权限检查完成'
        });
    } catch (error) {
        console.error('检查用户网站权限失败:', error);
        res.status(500).json({
            success: false,
            message: '检查用户网站权限失败',
            error: error.message
        });
    }
});

module.exports = router;
