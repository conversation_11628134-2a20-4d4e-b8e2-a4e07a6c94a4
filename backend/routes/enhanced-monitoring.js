/**
 * 增强版监控API路由
 * 提供coolmonitor风格的监控功能接口
 */

const express = require('express');
const router = express.Router();
const EnhancedWebsiteMonitor = require('../services/enhanced-website-monitor');
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'sitemanager123',
  database: process.env.DB_NAME || 'sitemanager',
  charset: 'utf8mb4'
};

/**
 * 获取数据库连接
 */
async function getDbConnection() {
  return await mysql.createConnection(dbConfig);
}

/**
 * 检查单个网站
 * POST /api/v1/enhanced-monitoring/websites/:id/check
 */
router.post('/websites/:id/check', async (req, res) => {
  try {
    const websiteId = parseInt(req.params.id);
    const config = req.body || {};

    const monitor = new EnhancedWebsiteMonitor();
    const result = await monitor.checkWebsite(websiteId, config);

    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('检查网站失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * 批量检查网站
 * POST /api/v1/enhanced-monitoring/websites/batch-check
 */
router.post('/websites/batch-check', async (req, res) => {
  try {
    const { websiteIds, config = {} } = req.body;

    if (!Array.isArray(websiteIds) || websiteIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供有效的网站ID列表'
      });
    }

    const monitor = new EnhancedWebsiteMonitor();
    const results = await monitor.checkWebsitesBatch(websiteIds, config);

    res.json({
      success: true,
      data: {
        total: results.length,
        results
      }
    });
  } catch (error) {
    console.error('批量检查网站失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * 获取网站监控配置
 * GET /api/v1/enhanced-monitoring/websites/:id/config
 */
router.get('/websites/:id/config', async (req, res) => {
  const connection = await getDbConnection();
  
  try {
    const websiteId = parseInt(req.params.id);

    const [configs] = await connection.execute(
      'SELECT * FROM website_monitor_configs WHERE website_id = ?',
      [websiteId]
    );

    if (configs.length === 0) {
      // 返回默认配置
      const defaultConfig = {
        website_id: websiteId,
        http_method: 'GET',
        status_codes: '200-299',
        connect_timeout: 10,
        retries: 1,
        retry_interval: 30,
        enable_http_check: 1,
        enable_ssl_check: 1,
        enable_keyword_check: 0,
        notify_cert_expiry: 1,
        ignore_tls: 0,
        keyword: '',
        request_headers: '',
        request_body: '',
        max_redirects: 10,
        check_interval: 300
      };

      res.json({
        success: true,
        data: defaultConfig
      });
    } else {
      res.json({
        success: true,
        data: configs[0]
      });
    }
  } catch (error) {
    console.error('获取监控配置失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  } finally {
    await connection.end();
  }
});

/**
 * 更新网站监控配置
 * PUT /api/v1/enhanced-monitoring/websites/:id/config
 */
router.put('/websites/:id/config', async (req, res) => {
  const connection = await getDbConnection();
  
  try {
    const websiteId = parseInt(req.params.id);
    const config = req.body;

    // 检查网站是否存在
    const [websites] = await connection.execute(
      'SELECT id FROM websites WHERE id = ?',
      [websiteId]
    );

    if (websites.length === 0) {
      return res.status(404).json({
        success: false,
        message: '网站不存在'
      });
    }

    // 检查配置是否已存在
    const [existingConfigs] = await connection.execute(
      'SELECT id FROM website_monitor_configs WHERE website_id = ?',
      [websiteId]
    );

    if (existingConfigs.length > 0) {
      // 更新现有配置
      await connection.execute(`
        UPDATE website_monitor_configs SET
          http_method = ?,
          status_codes = ?,
          connect_timeout = ?,
          retries = ?,
          retry_interval = ?,
          enable_http_check = ?,
          enable_ssl_check = ?,
          enable_keyword_check = ?,
          notify_cert_expiry = ?,
          ignore_tls = ?,
          keyword = ?,
          request_headers = ?,
          request_body = ?,
          max_redirects = ?,
          check_interval = ?,
          updated_at = NOW()
        WHERE website_id = ?
      `, [
        config.http_method || 'GET',
        config.status_codes || '200-299',
        config.connect_timeout || 10,
        config.retries || 1,
        config.retry_interval || 30,
        config.enable_http_check !== false ? 1 : 0,
        config.enable_ssl_check !== false ? 1 : 0,
        config.enable_keyword_check === true ? 1 : 0,
        config.notify_cert_expiry !== false ? 1 : 0,
        config.ignore_tls === true ? 1 : 0,
        config.keyword || '',
        config.request_headers || '',
        config.request_body || '',
        config.max_redirects || 10,
        config.check_interval || 300,
        websiteId
      ]);
    } else {
      // 创建新配置
      await connection.execute(`
        INSERT INTO website_monitor_configs (
          website_id, http_method, status_codes, connect_timeout, retries, retry_interval,
          enable_http_check, enable_ssl_check, enable_keyword_check, notify_cert_expiry,
          ignore_tls, keyword, request_headers, request_body, max_redirects, check_interval
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        websiteId,
        config.http_method || 'GET',
        config.status_codes || '200-299',
        config.connect_timeout || 10,
        config.retries || 1,
        config.retry_interval || 30,
        config.enable_http_check !== false ? 1 : 0,
        config.enable_ssl_check !== false ? 1 : 0,
        config.enable_keyword_check === true ? 1 : 0,
        config.notify_cert_expiry !== false ? 1 : 0,
        config.ignore_tls === true ? 1 : 0,
        config.keyword || '',
        config.request_headers || '',
        config.request_body || '',
        config.max_redirects || 10,
        config.check_interval || 300
      ]);
    }

    res.json({
      success: true,
      message: '监控配置已更新'
    });
  } catch (error) {
    console.error('更新监控配置失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  } finally {
    await connection.end();
  }
});

/**
 * 获取网站监控历史
 * GET /api/v1/enhanced-monitoring/websites/:id/history
 */
router.get('/websites/:id/history', async (req, res) => {
  const connection = await getDbConnection();
  
  try {
    const websiteId = parseInt(req.params.id);
    const { range = '24h', limit = 100 } = req.query;

    // 计算时间范围
    let timeCondition = '';
    switch (range) {
      case '2h':
        timeCondition = 'AND check_time >= DATE_SUB(NOW(), INTERVAL 2 HOUR)';
        break;
      case '24h':
        timeCondition = 'AND check_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
        break;
      case '7d':
        timeCondition = 'AND check_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        break;
      case '30d':
        timeCondition = 'AND check_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        break;
      case '90d':
        timeCondition = 'AND check_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)';
        break;
      default:
        timeCondition = 'AND check_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
    }

    let query = `
      SELECT
        id,
        status,
        message,
        response_time as ping,
        status_code,
        check_time as timestamp,
        details,
        error_message
      FROM website_monitor_history
      WHERE website_id = ? ${timeCondition}
      ORDER BY check_time DESC
      LIMIT ?
    `;

    const [history] = await connection.execute(query, [websiteId, parseInt(limit)]);

    res.json({
      success: true,
      data: history.map(record => ({
        ...record,
        timestamp: record.timestamp.toISOString(),
        details: record.details ? JSON.parse(record.details) : null
      }))
    });
  } catch (error) {
    console.error('获取监控历史失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  } finally {
    await connection.end();
  }
});

/**
 * 获取监控状态概览
 * GET /api/v1/enhanced-monitoring/overview
 */
router.get('/overview', async (req, res) => {
  const connection = await getDbConnection();
  
  try {
    // 获取总体统计
    const [stats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_websites,
        SUM(CASE WHEN access_status = '正常' THEN 1 ELSE 0 END) as healthy_websites,
        SUM(CASE WHEN ssl_status = 'valid' THEN 1 ELSE 0 END) as valid_ssl_websites,
        SUM(CASE WHEN ssl_expire_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY) 
                 AND ssl_expire_date > CURDATE() THEN 1 ELSE 0 END) as ssl_expiring_soon,
        SUM(CASE WHEN ssl_expire_date <= CURDATE() THEN 1 ELSE 0 END) as ssl_expired
      FROM websites 
      WHERE status = 'active' AND monitor_enabled = 1
    `);

    // 获取最近24小时的检查统计
    const [recentStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_checks,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as successful_checks,
        AVG(response_time) as avg_response_time
      FROM website_monitor_history 
      WHERE check_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    `);

    res.json({
      success: true,
      data: {
        overview: stats[0],
        recent_24h: recentStats[0]
      }
    });
  } catch (error) {
    console.error('获取监控概览失败:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  } finally {
    await connection.end();
  }
});

module.exports = router;
