const express = require('express');
const router = express.Router();
const PermissionConfigService = require('../services/PermissionConfigService');
const PermissionCacheService = require('../services/PermissionCacheService');
const db = require('../utils/db');

/**
 * 权限系统健康检查路由
 */

/**
 * 获取权限系统整体健康状态
 */
router.get('/permission-system', async (req, res) => {
  try {
    const healthStatus = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      components: {},
      metrics: {}
    };

    // 检查配置服务状态
    try {
      const configStatus = PermissionConfigService.getHealthStatus();
      healthStatus.components.config = {
        status: configStatus.configLoaded ? 'healthy' : 'unhealthy',
        details: configStatus
      };
    } catch (error) {
      healthStatus.components.config = {
        status: 'unhealthy',
        error: error.message
      };
      healthStatus.status = 'degraded';
    }

    // 检查缓存服务状态
    try {
      const cacheStats = await PermissionCacheService.getCacheStats();
      healthStatus.components.cache = {
        status: 'healthy',
        details: cacheStats
      };
      healthStatus.metrics.cacheHitRate = cacheStats.hitRate;
    } catch (error) {
      healthStatus.components.cache = {
        status: 'unhealthy',
        error: error.message
      };
      healthStatus.status = 'degraded';
    }

    // 检查数据库连接
    try {
      const [rows] = await db.execute('SELECT 1 as test');
      healthStatus.components.database = {
        status: 'healthy',
        details: { connectionTest: 'passed' }
      };
    } catch (error) {
      healthStatus.components.database = {
        status: 'unhealthy',
        error: error.message
      };
      healthStatus.status = 'unhealthy';
    }

    // 检查权限表结构
    try {
      const tables = ['users', 'roles', 'permissions', 'role_permissions', 'user_custom_permissions', 'audit_logs'];
      const tableStatus = {};
      
      for (const table of tables) {
        try {
          const [rows] = await db.execute(`SELECT COUNT(*) as count FROM ${table} LIMIT 1`);
          tableStatus[table] = 'exists';
        } catch (error) {
          tableStatus[table] = 'missing';
          healthStatus.status = 'degraded';
        }
      }
      
      healthStatus.components.database.details.tables = tableStatus;
    } catch (error) {
      console.error('检查数据库表结构失败:', error);
    }

    // 计算响应时间指标
    const startTime = Date.now();
    try {
      // 模拟权限检查性能测试
      const testUserId = 1;
      const testPermission = 'user.read.own';
      
      const PermissionService = require('../services/PermissionService');
      const permissionService = new PermissionService();
      
      await permissionService.hasPermission(testUserId, testPermission);
      
      const responseTime = Date.now() - startTime;
      healthStatus.metrics.permissionCheckResponseTime = responseTime;
      
      if (responseTime > 100) {
        healthStatus.status = 'degraded';
        healthStatus.components.performance = {
          status: 'degraded',
          details: { responseTime, threshold: 100 }
        };
      } else {
        healthStatus.components.performance = {
          status: 'healthy',
          details: { responseTime, threshold: 100 }
        };
      }
    } catch (error) {
      healthStatus.components.performance = {
        status: 'unhealthy',
        error: error.message
      };
      healthStatus.status = 'degraded';
    }

    // 设置HTTP状态码
    const httpStatus = healthStatus.status === 'healthy' ? 200 : 
                     healthStatus.status === 'degraded' ? 200 : 503;

    res.status(httpStatus).json({
      success: true,
      data: healthStatus
    });

  } catch (error) {
    console.error('权限系统健康检查失败:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'HEALTH_CHECK_ERROR',
        message: '权限系统健康检查失败',
        details: error.message
      },
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 获取权限配置状态
 */
router.get('/permission-config', async (req, res) => {
  try {
    const configStatus = PermissionConfigService.getHealthStatus();
    const config = PermissionConfigService.getConfig();
    
    res.json({
      success: true,
      data: {
        status: configStatus,
        summary: {
          version: config?.version,
          rolesCount: Object.keys(config?.roles || {}).length,
          permissionsCount: Object.keys(config?.permissions || {}).length,
          templatesCount: Object.keys(config?.templates || {}).length
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'CONFIG_STATUS_ERROR',
        message: '获取配置状态失败',
        details: error.message
      }
    });
  }
});

/**
 * 获取权限缓存状态
 */
router.get('/permission-cache', async (req, res) => {
  try {
    const cacheStats = await PermissionCacheService.getCacheStats();
    
    res.json({
      success: true,
      data: cacheStats
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'CACHE_STATUS_ERROR',
        message: '获取缓存状态失败',
        details: error.message
      }
    });
  }
});

/**
 * 获取权限系统性能指标
 */
router.get('/permission-metrics', async (req, res) => {
  try {
    const metrics = {
      timestamp: new Date().toISOString(),
      performance: {},
      usage: {},
      errors: {}
    };

    // 权限验证性能测试
    const performanceTests = [
      { userId: 1, permission: 'user.read.own' },
      { userId: 1, permission: 'server.create' },
      { userId: 1, permission: 'website.update.any' }
    ];

    const PermissionService = require('../services/PermissionService');
    const permissionService = new PermissionService();

    const responseTimes = [];
    for (const test of performanceTests) {
      const startTime = Date.now();
      try {
        await permissionService.hasPermission(test.userId, test.permission);
        responseTimes.push(Date.now() - startTime);
      } catch (error) {
        responseTimes.push(-1); // 错误标记
      }
    }

    metrics.performance = {
      averageResponseTime: responseTimes.filter(t => t > 0).reduce((a, b) => a + b, 0) / responseTimes.filter(t => t > 0).length || 0,
      maxResponseTime: Math.max(...responseTimes.filter(t => t > 0)),
      minResponseTime: Math.min(...responseTimes.filter(t => t > 0)),
      errorCount: responseTimes.filter(t => t === -1).length
    };

    // 获取缓存使用情况
    const cacheStats = await PermissionCacheService.getCacheStats();
    metrics.usage = {
      cacheHitRate: cacheStats.hitRate,
      localCacheSize: cacheStats.localCacheSize,
      totalRequests: cacheStats.localCacheHits + cacheStats.localCacheMisses
    };

    // 获取最近的错误统计
    try {
      const [errorStats] = await db.execute(`
        SELECT 
          COUNT(*) as total_errors,
          COUNT(CASE WHEN result = 'denied' THEN 1 END) as permission_denied,
          COUNT(CASE WHEN created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 1 END) as recent_errors
        FROM audit_logs 
        WHERE action = 'permission_access' 
          AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
      `);
      
      if (errorStats.length > 0) {
        metrics.errors = errorStats[0];
      }
    } catch (error) {
      console.error('获取错误统计失败:', error);
    }

    res.json({
      success: true,
      data: metrics
    });

  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'METRICS_ERROR',
        message: '获取性能指标失败',
        details: error.message
      }
    });
  }
});

/**
 * 重新加载权限配置
 */
router.post('/reload-config', async (req, res) => {
  try {
    const result = await PermissionConfigService.reloadConfig();
    
    if (result.success) {
      // 清除相关缓存
      await PermissionCacheService.clearAllCache();
      
      res.json({
        success: true,
        message: '权限配置重新加载成功，缓存已清除',
        timestamp: new Date().toISOString()
      });
    } else {
      res.status(400).json({
        success: false,
        error: {
          code: 'CONFIG_RELOAD_ERROR',
          message: result.message
        }
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'CONFIG_RELOAD_ERROR',
        message: '重新加载配置失败',
        details: error.message
      }
    });
  }
});

/**
 * 清除权限缓存
 */
router.post('/clear-cache', async (req, res) => {
  try {
    const { userId, role } = req.body;
    
    if (userId) {
      await PermissionCacheService.clearUserPermissions(userId);
      res.json({
        success: true,
        message: `用户 ${userId} 的权限缓存已清除`
      });
    } else if (role) {
      await PermissionCacheService.clearRolePermissions(role);
      res.json({
        success: true,
        message: `角色 ${role} 的权限缓存已清除`
      });
    } else {
      await PermissionCacheService.clearAllCache();
      res.json({
        success: true,
        message: '所有权限缓存已清除'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: {
        code: 'CACHE_CLEAR_ERROR',
        message: '清除缓存失败',
        details: error.message
      }
    });
  }
});

module.exports = router;