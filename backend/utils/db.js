/**
 * 数据库工具模块
 * 提供数据库连接和查询功能
 */

// 确保环境变量已加载
require('dotenv').config();

const mysql = require('mysql2/promise');

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'sitemanager',
  password: process.env.DB_PASSWORD || 'sitemanager123',
  database: process.env.DB_NAME || 'sitemanager',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// 创建连接池
const pool = mysql.createPool(dbConfig);

/**
 * 执行SQL查询
 * @param {string} sql SQL语句
 * @param {Array} params 参数数组
 * @returns {Promise} 查询结果
 */
async function execute(sql, params = []) {
  try {
    const [rows, fields] = await pool.execute(sql, params);
    return [rows, fields];
  } catch (error) {
    console.error('数据库查询错误:', error);
    throw error;
  }
}

/**
 * 开始事务
 */
async function beginTransaction() {
  const connection = await pool.getConnection();
  await connection.beginTransaction();
  return connection;
}

/**
 * 提交事务
 */
async function commit(connection) {
  await connection.commit();
  connection.release();
}

/**
 * 回滚事务
 */
async function rollback(connection) {
  await connection.rollback();
  connection.release();
}

/**
 * 关闭连接池
 */
async function end() {
  await pool.end();
}

module.exports = {
  execute,
  beginTransaction,
  commit,
  rollback,
  end,
  pool
};