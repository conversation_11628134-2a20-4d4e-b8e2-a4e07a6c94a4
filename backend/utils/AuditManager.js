/**
 * 审计日志管理工具类
 * 提供审计日志的高级管理功能
 */

const AuditService = require('../services/AuditService');

class AuditManager {
  constructor(database) {
    this.db = database;
    this.auditService = new AuditService(database);
    
    // 审计规则配置
    this.auditRules = {
      // 需要审计的敏感操作
      sensitiveActions: [
        'user.user.delete',
        'site.website.delete',
        'server.server.delete',
        'system.settings.edit',
        'system.backup.create',
        'system.permission.manage'
      ],
      
      // 需要实时告警的操作
      alertActions: [
        'login_failure',
        'permission_denied',
        'suspicious_access',
        'data_export'
      ],
      
      // 异常检测阈值
      anomalyThresholds: {
        maxFailedLogins: 5,
        maxPermissionDenials: 10,
        maxRequestsPerMinute: 100,
        suspiciousIPThreshold: 3
      }
    };

    // 告警处理器
    this.alertHandlers = [];
  }

  /**
   * 注册告警处理器
   * @param {Function} handler - 告警处理函数
   */
  registerAlertHandler(handler) {
    this.alertHandlers.push(handler);
  }

  /**
   * 记录用户操作审计
   * @param {Object} operationInfo - 操作信息
   */
  async auditUserOperation(operationInfo) {
    try {
      const {
        userId,
        action,
        resource,
        resourceId,
        result = 'success',
        details = {},
        context = {}
      } = operationInfo;

      // 记录基础审计日志
      await this.auditService.logSensitiveOperation(
        userId,
        action,
        resource,
        resourceId,
        details,
        context
      );

      // 检查是否需要特殊处理
      await this.handleSpecialAuditRules(operationInfo);

      // 检查是否需要告警
      if (this.shouldTriggerAlert(action, result)) {
        await this.triggerAlert(operationInfo);
      }

    } catch (error) {
      console.error('记录用户操作审计失败:', error.message);
    }
  }

  /**
   * 处理特殊审计规则
   * @param {Object} operationInfo - 操作信息
   */
  async handleSpecialAuditRules(operationInfo) {
    const { action, userId, result } = operationInfo;

    // 处理敏感操作
    if (this.auditRules.sensitiveActions.includes(action)) {
      await this.handleSensitiveOperation(operationInfo);
    }

    // 处理登录失败
    if (action === 'login' && result === 'failure') {
      await this.handleLoginFailure(operationInfo);
    }

    // 处理权限拒绝
    if (result === 'denied') {
      await this.handlePermissionDenial(operationInfo);
    }
  }

  /**
   * 处理敏感操作
   * @param {Object} operationInfo - 操作信息
   */
  async handleSensitiveOperation(operationInfo) {
    const { userId, action, resource, resourceId } = operationInfo;

    // 记录额外的敏感操作日志
    await this.auditService.logSensitiveOperation(
      userId,
      `sensitive_${action}`,
      resource,
      resourceId,
      {
        ...operationInfo.details,
        sensitiveOperation: true,
        requiresReview: true
      },
      operationInfo.context
    );

    console.log(`🔒 敏感操作记录: 用户 ${userId} 执行 ${action} 操作`);
  }

  /**
   * 处理登录失败
   * @param {Object} operationInfo - 操作信息
   */
  async handleLoginFailure(operationInfo) {
    const { userId, context } = operationInfo;
    const ip = context.ip;

    // 检查同一IP的失败次数
    const recentFailures = await this.getRecentLoginFailures(ip, 300); // 5分钟内

    if (recentFailures >= this.auditRules.anomalyThresholds.maxFailedLogins) {
      await this.triggerAlert({
        type: 'multiple_login_failures',
        ip,
        userId,
        failureCount: recentFailures,
        severity: 'high'
      });
    }
  }

  /**
   * 处理权限拒绝
   * @param {Object} operationInfo - 操作信息
   */
  async handlePermissionDenial(operationInfo) {
    const { userId, action, context } = operationInfo;

    // 检查用户的权限拒绝频率
    const recentDenials = await this.getRecentPermissionDenials(userId, 3600); // 1小时内

    if (recentDenials >= this.auditRules.anomalyThresholds.maxPermissionDenials) {
      await this.triggerAlert({
        type: 'excessive_permission_denials',
        userId,
        action,
        denialCount: recentDenials,
        severity: 'medium'
      });
    }
  }

  /**
   * 获取最近的登录失败次数
   * @param {string} ip - IP地址
   * @param {number} timeWindow - 时间窗口（秒）
   * @returns {Promise<number>} 失败次数
   */
  async getRecentLoginFailures(ip, timeWindow) {
    try {
      const [result] = await this.db.execute(`
        SELECT COUNT(*) as count
        FROM audit_logs
        WHERE action = 'login'
          AND result = 'failure'
          AND ip_address = ?
          AND created_at >= DATE_SUB(NOW(), INTERVAL ? SECOND)
      `, [ip, timeWindow]);

      return result[0].count;
    } catch (error) {
      console.error('获取登录失败次数失败:', error.message);
      return 0;
    }
  }

  /**
   * 获取最近的权限拒绝次数
   * @param {number} userId - 用户ID
   * @param {number} timeWindow - 时间窗口（秒）
   * @returns {Promise<number>} 拒绝次数
   */
  async getRecentPermissionDenials(userId, timeWindow) {
    try {
      const [result] = await this.db.execute(`
        SELECT COUNT(*) as count
        FROM audit_logs
        WHERE user_id = ?
          AND result = 'denied'
          AND created_at >= DATE_SUB(NOW(), INTERVAL ? SECOND)
      `, [userId, timeWindow]);

      return result[0].count;
    } catch (error) {
      console.error('获取权限拒绝次数失败:', error.message);
      return 0;
    }
  }

  /**
   * 判断是否应该触发告警
   * @param {string} action - 操作类型
   * @param {string} result - 操作结果
   * @returns {boolean} 是否触发告警
   */
  shouldTriggerAlert(action, result) {
    return this.auditRules.alertActions.includes(action) || 
           (result === 'denied' || result === 'failure');
  }

  /**
   * 触发告警
   * @param {Object} alertInfo - 告警信息
   */
  async triggerAlert(alertInfo) {
    try {
      const alert = {
        ...alertInfo,
        timestamp: new Date().toISOString(),
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };

      // 记录告警日志
      await this.auditService.logSensitiveOperation(
        alertInfo.userId || 0,
        'security_alert',
        'system',
        null,
        alert,
        { automated: true }
      );

      // 调用所有注册的告警处理器
      for (const handler of this.alertHandlers) {
        try {
          await handler(alert);
        } catch (error) {
          console.error('告警处理器执行失败:', error.message);
        }
      }

      console.log(`🚨 安全告警触发: ${alert.type}`, alert);

    } catch (error) {
      console.error('触发告警失败:', error.message);
    }
  }

  /**
   * 生成审计摘要报告
   * @param {Object} options - 报告选项
   * @returns {Promise<Object>} 审计摘要
   */
  async generateAuditSummary(options = {}) {
    try {
      const {
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000), // 默认24小时
        endDate = new Date(),
        includeAnomalies = true
      } = options;

      const summary = {
        period: { startDate, endDate },
        generatedAt: new Date().toISOString(),
        statistics: {},
        anomalies: [],
        recommendations: []
      };

      // 获取基础统计
      summary.statistics = await this.auditService.getAuditStatistics({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      });

      // 获取异常检测结果
      if (includeAnomalies) {
        summary.anomalies = await this.auditService.detectAnomalousAccess();
      }

      // 生成安全建议
      summary.recommendations = await this.generateSecurityRecommendations(summary);

      return summary;

    } catch (error) {
      console.error('生成审计摘要失败:', error.message);
      throw error;
    }
  }

  /**
   * 生成安全建议
   * @param {Object} summary - 审计摘要
   * @returns {Promise<Array>} 安全建议列表
   */
  async generateSecurityRecommendations(summary) {
    const recommendations = [];

    // 检查失败率
    const totalRequests = summary.statistics.resultStatistics.reduce((sum, stat) => sum + stat.count, 0);
    const deniedRequests = summary.statistics.resultStatistics.find(stat => stat.result === 'denied')?.count || 0;
    const deniedRate = totalRequests > 0 ? (deniedRequests / totalRequests) : 0;

    if (deniedRate > 0.1) { // 拒绝率超过10%
      recommendations.push({
        type: 'high_denial_rate',
        severity: 'medium',
        message: `权限拒绝率较高 (${(deniedRate * 100).toFixed(1)}%)，建议检查用户权限配置`,
        action: '审查用户权限分配'
      });
    }

    // 检查异常访问
    if (summary.anomalies.length > 0) {
      const highSeverityAnomalies = summary.anomalies.filter(a => a.severity === 'high');
      if (highSeverityAnomalies.length > 0) {
        recommendations.push({
          type: 'high_severity_anomalies',
          severity: 'high',
          message: `发现 ${highSeverityAnomalies.length} 个高风险异常访问`,
          action: '立即调查异常访问模式'
        });
      }
    }

    // 检查用户活动分布
    const topUsers = summary.statistics.userStatistics.slice(0, 3);
    if (topUsers.length > 0 && topUsers[0].count > 1000) {
      recommendations.push({
        type: 'high_activity_user',
        severity: 'low',
        message: `用户 ${topUsers[0].username} 活动频率较高 (${topUsers[0].count} 次操作)`,
        action: '确认用户操作的合理性'
      });
    }

    return recommendations;
  }

  /**
   * 清理和归档旧审计日志
   * @param {Object} options - 清理选项
   * @returns {Promise<Object>} 清理结果
   */
  async cleanupAndArchiveAuditLogs(options = {}) {
    try {
      const {
        retentionDays = 90,
        archiveBeforeDelete = true,
        batchSize = 1000
      } = options;

      const result = {
        archivedCount: 0,
        deletedCount: 0,
        errors: []
      };

      // 如果需要归档，先执行归档操作
      if (archiveBeforeDelete) {
        // 这里可以实现归档逻辑，例如导出到文件或备份数据库
        console.log('开始归档旧审计日志...');
      }

      // 执行清理
      result.deletedCount = await this.auditService.cleanupExpiredLogs();

      console.log(`审计日志清理完成: 删除 ${result.deletedCount} 条记录`);
      return result;

    } catch (error) {
      console.error('清理审计日志失败:', error.message);
      throw error;
    }
  }

  /**
   * 更新审计规则
   * @param {Object} newRules - 新的审计规则
   */
  updateAuditRules(newRules) {
    this.auditRules = { ...this.auditRules, ...newRules };
    console.log('审计规则已更新:', this.auditRules);
  }

  /**
   * 获取当前审计规则
   * @returns {Object} 当前审计规则
   */
  getAuditRules() {
    return { ...this.auditRules };
  }

  /**
   * 验证审计日志完整性
   * @param {Object} options - 验证选项
   * @returns {Promise<Object>} 验证结果
   */
  async validateAuditLogIntegrity(options = {}) {
    try {
      const {
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000),
        endDate = new Date()
      } = options;

      const validation = {
        period: { startDate, endDate },
        validatedAt: new Date().toISOString(),
        issues: [],
        summary: {}
      };

      // 检查日志连续性
      const [gaps] = await this.db.execute(`
        SELECT 
          DATE(created_at) as log_date,
          COUNT(*) as log_count
        FROM audit_logs
        WHERE created_at BETWEEN ? AND ?
        GROUP BY DATE(created_at)
        ORDER BY log_date
      `, [startDate.toISOString(), endDate.toISOString()]);

      // 检查是否有日期缺失
      const expectedDays = Math.ceil((endDate - startDate) / (24 * 60 * 60 * 1000));
      if (gaps.length < expectedDays) {
        validation.issues.push({
          type: 'missing_log_dates',
          severity: 'medium',
          message: `期望 ${expectedDays} 天的日志，实际只有 ${gaps.length} 天`
        });
      }

      // 检查异常的日志量
      const avgLogsPerDay = gaps.reduce((sum, day) => sum + day.log_count, 0) / gaps.length;
      gaps.forEach(day => {
        if (day.log_count < avgLogsPerDay * 0.1) { // 少于平均值的10%
          validation.issues.push({
            type: 'unusually_low_activity',
            severity: 'low',
            message: `${day.log_date} 的日志量异常低: ${day.log_count} 条`
          });
        }
      });

      validation.summary = {
        totalDays: gaps.length,
        totalLogs: gaps.reduce((sum, day) => sum + day.log_count, 0),
        averageLogsPerDay: Math.round(avgLogsPerDay),
        issuesFound: validation.issues.length
      };

      return validation;

    } catch (error) {
      console.error('验证审计日志完整性失败:', error.message);
      throw error;
    }
  }
}

module.exports = AuditManager;