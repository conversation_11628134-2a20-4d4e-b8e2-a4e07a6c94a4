/**
 * 权限性能优化工具类
 * 功能：
 * 1. 实现权限数据预加载和预计算
 * 2. 添加权限查询结果缓存优化
 * 3. 实现权限验证批量处理
 * 4. 添加权限系统性能监控
 * 5. 优化权限中间件执行效率
 */

const EventEmitter = require('events');

class PermissionPerformanceOptimizer extends EventEmitter {
  constructor(database, cacheService) {
    super();
    this.db = database;
    this.cache = cacheService;
    
    // 性能监控指标
    this.metrics = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      slowQueries: 0,
      batchProcessed: 0,
      precomputedHits: 0,
      
      // 响应时间统计
      responseTimes: [],
      maxResponseTime: 0,
      minResponseTime: Infinity,
      
      // 错误统计
      errors: 0,
      timeouts: 0,
      
      // 最后更新时间
      lastUpdated: new Date()
    };

    // 预计算缓存
    this.precomputedCache = new Map();
    
    // 批量处理队列
    this.batchQueue = new Map();
    this.batchTimeout = null;
    this.batchSize = 100;
    this.batchDelay = 50; // 50ms

    // 性能阈值配置
    this.thresholds = {
      slowQueryTime: 100,    // 慢查询阈值 (ms)
      maxResponseTime: 50,   // 最大响应时间 (ms)
      cacheHitRate: 0.95,    // 缓存命中率阈值
      errorRate: 0.01        // 错误率阈值
    };

    // 启动性能监控
    this.startPerformanceMonitoring();
  }

  /**
   * 启动性能监控
   */
  startPerformanceMonitoring() {
    // 定期清理性能数据
    setInterval(() => {
      this.cleanupMetrics();
    }, 300000); // 每5分钟清理一次

    // 定期预计算热点权限
    setInterval(() => {
      this.precomputeHotPermissions();
    }, 600000); // 每10分钟预计算一次

    // 定期生成性能报告
    setInterval(() => {
      this.generatePerformanceReport();
    }, 3600000); // 每小时生成一次报告

    console.log('✅ 权限性能监控已启动');
  }

  /**
   * 优化权限检查
   */
  async optimizedPermissionCheck(userId, permissions, options = {}) {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // 单个权限检查
      if (typeof permissions === 'string') {
        return await this.checkSinglePermission(userId, permissions, options);
      }

      // 批量权限检查
      if (Array.isArray(permissions)) {
        return await this.checkMultiplePermissions(userId, permissions, options);
      }

      throw new Error('无效的权限参数');

    } catch (error) {
      this.metrics.errors++;
      throw error;
    } finally {
      const responseTime = Date.now() - startTime;
      this.updateResponseTimeMetrics(responseTime);
    }
  }

  /**
   * 检查单个权限（优化版）
   */
  async checkSinglePermission(userId, permission, options = {}) {
    // 1. 尝试从预计算缓存获取
    const precomputedKey = `precomputed_${userId}_${permission}`;
    if (this.precomputedCache.has(precomputedKey)) {
      this.metrics.precomputedHits++;
      return this.precomputedCache.get(precomputedKey);
    }

    // 2. 尝试从普通缓存获取
    const cacheKey = `permission_${userId}_${permission}`;
    const cached = await this.cache.get(cacheKey);
    if (cached !== null) {
      this.metrics.cacheHits++;
      return cached;
    }

    this.metrics.cacheMisses++;

    // 3. 从数据库查询
    const result = await this.queryPermissionFromDB(userId, permission);

    // 4. 缓存结果
    await this.cache.set(cacheKey, result, 300); // 缓存5分钟

    return result;
  }

  /**
   * 批量权限检查（优化版）
   */
  async checkMultiplePermissions(userId, permissions, options = {}) {
    const results = {};
    const uncachedPermissions = [];

    // 1. 先从缓存获取已有结果
    for (const permission of permissions) {
      const cacheKey = `permission_${userId}_${permission}`;
      const cached = await this.cache.get(cacheKey);
      
      if (cached !== null) {
        results[permission] = cached;
        this.metrics.cacheHits++;
      } else {
        uncachedPermissions.push(permission);
        this.metrics.cacheMisses++;
      }
    }

    // 2. 批量查询未缓存的权限
    if (uncachedPermissions.length > 0) {
      const batchResults = await this.batchQueryPermissions(userId, uncachedPermissions);
      
      // 3. 合并结果并缓存
      for (const [permission, result] of Object.entries(batchResults)) {
        results[permission] = result;
        const cacheKey = `permission_${userId}_${permission}`;
        await this.cache.set(cacheKey, result, 300);
      }

      this.metrics.batchProcessed++;
    }

    return results;
  }

  /**
   * 批量查询权限
   */
  async batchQueryPermissions(userId, permissions) {
    try {
      const placeholders = permissions.map(() => '?').join(',');
      
      const [results] = await this.db.execute(`
        SELECT 
          p.code as permission_code,
          CASE 
            WHEN ucp.granted IS NOT NULL THEN ucp.granted
            WHEN rp.permission_code IS NOT NULL THEN 1
            ELSE 0
          END as granted
        FROM (${permissions.map(p => `SELECT '${p}' as code`).join(' UNION ')}) p
        LEFT JOIN users u ON u.id = ?
        LEFT JOIN role_permissions rp ON u.role = rp.role AND p.code = rp.permission_code
        LEFT JOIN user_custom_permissions ucp ON u.id = ucp.user_id AND p.code = ucp.permission_code
      `, [userId]);

      const batchResults = {};
      results.forEach(row => {
        batchResults[row.permission_code] = Boolean(row.granted);
      });

      // 确保所有权限都有结果
      permissions.forEach(permission => {
        if (!(permission in batchResults)) {
          batchResults[permission] = false;
        }
      });

      return batchResults;

    } catch (error) {
      console.error('批量查询权限失败:', error.message);
      
      // 回退到单个查询
      const results = {};
      for (const permission of permissions) {
        results[permission] = await this.queryPermissionFromDB(userId, permission);
      }
      return results;
    }
  }

  /**
   * 从数据库查询权限
   */
  async queryPermissionFromDB(userId, permission) {
    try {
      const [results] = await this.db.execute(`
        SELECT 
          CASE 
            WHEN ucp.granted IS NOT NULL THEN ucp.granted
            WHEN rp.permission_code IS NOT NULL THEN 1
            ELSE 0
          END as granted
        FROM users u
        LEFT JOIN role_permissions rp ON u.role = rp.role AND rp.permission_code = ?
        LEFT JOIN user_custom_permissions ucp ON u.id = ucp.user_id AND ucp.permission_code = ?
        WHERE u.id = ?
      `, [permission, permission, userId]);

      return results.length > 0 ? Boolean(results[0].granted) : false;

    } catch (error) {
      console.error('查询权限失败:', error.message);
      return false;
    }
  }

  /**
   * 预计算热点权限
   */
  async precomputeHotPermissions() {
    try {
      console.log('🔄 开始预计算热点权限...');

      // 1. 获取最常用的权限
      const [hotPermissions] = await this.db.execute(`
        SELECT 
          resource as permission_code,
          COUNT(*) as access_count
        FROM audit_logs 
        WHERE action = 'permission_check' 
          AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        GROUP BY resource
        ORDER BY access_count DESC
        LIMIT 20
      `);

      // 2. 获取活跃用户
      const [activeUsers] = await this.db.execute(`
        SELECT DISTINCT user_id
        FROM audit_logs 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        LIMIT 100
      `);

      // 3. 预计算权限结果
      let precomputedCount = 0;
      for (const user of activeUsers) {
        for (const perm of hotPermissions) {
          const key = `precomputed_${user.user_id}_${perm.permission_code}`;
          
          if (!this.precomputedCache.has(key)) {
            const result = await this.queryPermissionFromDB(user.user_id, perm.permission_code);
            this.precomputedCache.set(key, result);
            precomputedCount++;
          }
        }
      }

      console.log(`✅ 预计算完成，处理了 ${precomputedCount} 个权限组合`);

    } catch (error) {
      console.error('预计算热点权限失败:', error.message);
    }
  }

  /**
   * 更新响应时间指标
   */
  updateResponseTimeMetrics(responseTime) {
    // 更新响应时间统计
    this.metrics.responseTimes.push(responseTime);
    
    // 保持最近1000个响应时间记录
    if (this.metrics.responseTimes.length > 1000) {
      this.metrics.responseTimes.shift();
    }

    // 更新最大最小值
    this.metrics.maxResponseTime = Math.max(this.metrics.maxResponseTime, responseTime);
    this.metrics.minResponseTime = Math.min(this.metrics.minResponseTime, responseTime);

    // 计算平均响应时间
    const sum = this.metrics.responseTimes.reduce((a, b) => a + b, 0);
    this.metrics.averageResponseTime = sum / this.metrics.responseTimes.length;

    // 检查慢查询
    if (responseTime > this.thresholds.slowQueryTime) {
      this.metrics.slowQueries++;
    }

    // 检查是否超过阈值
    if (responseTime > this.thresholds.maxResponseTime) {
      this.emit('performanceAlert', {
        type: 'SLOW_RESPONSE',
        responseTime,
        threshold: this.thresholds.maxResponseTime
      });
    }
  }

  /**
   * 清理性能指标
   */
  cleanupMetrics() {
    // 清理过期的预计算缓存
    const now = Date.now();
    for (const [key, value] of this.precomputedCache.entries()) {
      if (value.timestamp && now - value.timestamp > 3600000) { // 1小时过期
        this.precomputedCache.delete(key);
      }
    }

    // 重置部分指标
    if (this.metrics.responseTimes.length > 500) {
      this.metrics.responseTimes = this.metrics.responseTimes.slice(-500);
    }

    this.metrics.lastUpdated = new Date();
  }

  /**
   * 生成性能报告
   */
  generatePerformanceReport() {
    const report = {
      timestamp: new Date().toISOString(),
      metrics: { ...this.metrics },
      analysis: this.analyzePerformance(),
      recommendations: this.generateRecommendations()
    };

    // 发出性能报告事件
    this.emit('performanceReport', report);

    // 检查性能告警
    this.checkPerformanceAlerts(report);

    return report;
  }

  /**
   * 分析性能数据
   */
  analyzePerformance() {
    const cacheHitRate = this.metrics.totalRequests > 0 
      ? this.metrics.cacheHits / this.metrics.totalRequests 
      : 0;

    const errorRate = this.metrics.totalRequests > 0 
      ? this.metrics.errors / this.metrics.totalRequests 
      : 0;

    const slowQueryRate = this.metrics.totalRequests > 0 
      ? this.metrics.slowQueries / this.metrics.totalRequests 
      : 0;

    return {
      cacheHitRate: (cacheHitRate * 100).toFixed(2) + '%',
      errorRate: (errorRate * 100).toFixed(2) + '%',
      slowQueryRate: (slowQueryRate * 100).toFixed(2) + '%',
      averageResponseTime: this.metrics.averageResponseTime.toFixed(2) + 'ms',
      maxResponseTime: this.metrics.maxResponseTime + 'ms',
      minResponseTime: this.metrics.minResponseTime + 'ms',
      precomputedCacheSize: this.precomputedCache.size,
      batchProcessingEfficiency: this.metrics.batchProcessed > 0 ? 'Active' : 'Inactive'
    };
  }

  /**
   * 生成优化建议
   */
  generateRecommendations() {
    const recommendations = [];
    const analysis = this.analyzePerformance();

    // 缓存命中率建议
    const cacheHitRate = parseFloat(analysis.cacheHitRate) / 100;
    if (cacheHitRate < this.thresholds.cacheHitRate) {
      recommendations.push({
        type: 'CACHE_OPTIMIZATION',
        priority: 'HIGH',
        message: `缓存命中率 ${analysis.cacheHitRate} 低于阈值 ${(this.thresholds.cacheHitRate * 100).toFixed(1)}%`,
        suggestion: '考虑增加缓存时间或预计算更多权限组合'
      });
    }

    // 响应时间建议
    if (this.metrics.averageResponseTime > this.thresholds.maxResponseTime) {
      recommendations.push({
        type: 'RESPONSE_TIME_OPTIMIZATION',
        priority: 'HIGH',
        message: `平均响应时间 ${analysis.averageResponseTime} 超过阈值 ${this.thresholds.maxResponseTime}ms`,
        suggestion: '考虑优化数据库查询或增加更多缓存层'
      });
    }

    // 错误率建议
    const errorRate = parseFloat(analysis.errorRate) / 100;
    if (errorRate > this.thresholds.errorRate) {
      recommendations.push({
        type: 'ERROR_RATE_OPTIMIZATION',
        priority: 'MEDIUM',
        message: `错误率 ${analysis.errorRate} 超过阈值 ${(this.thresholds.errorRate * 100).toFixed(1)}%`,
        suggestion: '检查权限查询逻辑和数据库连接稳定性'
      });
    }

    // 预计算建议
    if (this.precomputedCache.size < 100) {
      recommendations.push({
        type: 'PRECOMPUTATION_OPTIMIZATION',
        priority: 'LOW',
        message: '预计算缓存较小，可能影响热点权限查询性能',
        suggestion: '考虑增加预计算的权限组合数量'
      });
    }

    return recommendations;
  }

  /**
   * 检查性能告警
   */
  checkPerformanceAlerts(report) {
    const analysis = report.analysis;

    // 缓存命中率告警
    const cacheHitRate = parseFloat(analysis.cacheHitRate) / 100;
    if (cacheHitRate < this.thresholds.cacheHitRate) {
      this.emit('performanceAlert', {
        type: 'LOW_CACHE_HIT_RATE',
        value: analysis.cacheHitRate,
        threshold: (this.thresholds.cacheHitRate * 100).toFixed(1) + '%',
        severity: 'WARNING'
      });
    }

    // 响应时间告警
    if (this.metrics.averageResponseTime > this.thresholds.maxResponseTime) {
      this.emit('performanceAlert', {
        type: 'HIGH_RESPONSE_TIME',
        value: analysis.averageResponseTime,
        threshold: this.thresholds.maxResponseTime + 'ms',
        severity: 'CRITICAL'
      });
    }

    // 错误率告警
    const errorRate = parseFloat(analysis.errorRate) / 100;
    if (errorRate > this.thresholds.errorRate) {
      this.emit('performanceAlert', {
        type: 'HIGH_ERROR_RATE',
        value: analysis.errorRate,
        threshold: (this.thresholds.errorRate * 100).toFixed(1) + '%',
        severity: 'WARNING'
      });
    }
  }

  /**
   * 获取性能指标
   */
  getMetrics() {
    return {
      ...this.metrics,
      analysis: this.analyzePerformance(),
      cacheSize: this.precomputedCache.size,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 重置性能指标
   */
  resetMetrics() {
    this.metrics = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      slowQueries: 0,
      batchProcessed: 0,
      precomputedHits: 0,
      responseTimes: [],
      maxResponseTime: 0,
      minResponseTime: Infinity,
      errors: 0,
      timeouts: 0,
      lastUpdated: new Date()
    };

    console.log('性能指标已重置');
  }

  /**
   * 更新性能阈值
   */
  updateThresholds(newThresholds) {
    this.thresholds = { ...this.thresholds, ...newThresholds };
    console.log('性能阈值已更新:', this.thresholds);
  }

  /**
   * 清理预计算缓存
   */
  clearPrecomputedCache() {
    this.precomputedCache.clear();
    console.log('预计算缓存已清理');
  }
}

module.exports = PermissionPerformanceOptimizer;