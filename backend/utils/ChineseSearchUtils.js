/**
 * 中文智能搜索工具类
 * 支持中文分词、智能匹配、部分匹配等高级搜索功能
 */
class ChineseSearchUtils {
  
  /**
   * 中文常用停用词列表
   */
  static STOP_WORDS = new Set([
    '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
    '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
    '自己', '这', '那', '里', '就是', '还', '把', '比', '或', '等', '及', '与',
    '公司', '有限', '股份', '集团', '企业', '商贸', '贸易', '科技', '技术', '发展',
    '实业', '投资', '控股', '管理', '服务', '咨询', '工程', '建设', '装饰', '设计'
  ]);

  /**
   * 地区词汇映射 - 支持省市区的智能匹配
   */
  static REGION_MAPPING = {
    // 省份简称映射
    '广东': ['广东省', '粤'],
    '江苏': ['江苏省', '苏'],
    '浙江': ['浙江省', '浙'],
    '山东': ['山东省', '鲁'],
    '河南': ['河南省', '豫'],
    '四川': ['四川省', '川', '蜀'],
    '湖北': ['湖北省', '鄂'],
    '湖南': ['湖南省', '湘'],
    '河北': ['河北省', '冀'],
    '福建': ['福建省', '闽'],
    
    // 主要城市映射
    '北京': ['北京市', '京'],
    '上海': ['上海市', '沪'],
    '天津': ['天津市', '津'],
    '重庆': ['重庆市', '渝'],
    '深圳': ['深圳市', '深'],
    '广州': ['广州市', '穗'],
    '杭州': ['杭州市'],
    '南京': ['南京市', '宁'],
    '武汉': ['武汉市'],
    '成都': ['成都市', '蓉'],
    '西安': ['西安市'],
    '苏州': ['苏州市'],
    '青岛': ['青岛市'],
    '大连': ['大连市'],
    '宁波': ['宁波市'],
    '厦门': ['厦门市'],
    '长沙': ['长沙市'],
    '郑州': ['郑州市'],
    '济南': ['济南市'],
    '哈尔滨': ['哈尔滨市'],
    '长春': ['长春市'],
    '沈阳': ['沈阳市'],
    '石家庄': ['石家庄市'],
    '太原': ['太原市'],
    '合肥': ['合肥市'],
    '南昌': ['南昌市'],
    '福州': ['福州市'],
    '海口': ['海口市'],
    '南宁': ['南宁市'],
    '昆明': ['昆明市'],
    '贵阳': ['贵阳市'],
    '兰州': ['兰州市'],
    '西宁': ['西宁市'],
    '银川': ['银川市'],
    '乌鲁木齐': ['乌鲁木齐市'],
    '拉萨': ['拉萨市']
  };

  /**
   * 行业词汇映射
   */
  static INDUSTRY_MAPPING = {
    '工业': ['制造业', '生产', '制造', '加工'],
    '科技': ['技术', '高新', '信息', 'IT', '软件', '互联网'],
    '贸易': ['商贸', '进出口', '外贸', '商务', '销售'],
    '金融': ['银行', '保险', '证券', '投资', '基金'],
    '房地产': ['地产', '置业', '建筑', '装修', '装饰'],
    '医疗': ['医药', '健康', '生物', '制药'],
    '教育': ['培训', '学校', '学院', '教学'],
    '物流': ['运输', '快递', '仓储', '配送'],
    '能源': ['电力', '石油', '天然气', '新能源', '环保'],
    '农业': ['农产品', '种植', '养殖', '农资']
  };

  /**
   * 简单的中文分词函数
   * @param {string} text - 待分词的文本
   * @returns {Array} 分词结果数组
   */
  static segmentChinese(text) {
    if (!text || typeof text !== 'string') return [];
    
    // 移除标点符号和特殊字符，保留中文、英文、数字
    const cleanText = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, ' ');
    
    // 基于规则的简单分词
    const segments = [];
    let currentSegment = '';
    
    for (let i = 0; i < cleanText.length; i++) {
      const char = cleanText[i];
      
      if (char === ' ') {
        if (currentSegment.trim()) {
          segments.push(currentSegment.trim());
          currentSegment = '';
        }
        continue;
      }
      
      // 中文字符处理
      if (/[\u4e00-\u9fa5]/.test(char)) {
        currentSegment += char;
        
        // 检查是否形成了完整的词汇
        if (this.isCompleteWord(currentSegment)) {
          segments.push(currentSegment);
          currentSegment = '';
        } else if (currentSegment.length >= 4) {
          // 如果当前片段过长，进行切分
          segments.push(currentSegment.substring(0, 2));
          currentSegment = currentSegment.substring(2);
        }
      }
      // 英文和数字处理
      else if (/[a-zA-Z0-9]/.test(char)) {
        currentSegment += char;
      }
    }
    
    if (currentSegment.trim()) {
      segments.push(currentSegment.trim());
    }
    
    // 过滤停用词和短词
    return segments.filter(segment => 
      segment.length > 0 && 
      !this.STOP_WORDS.has(segment) &&
      segment.length >= 1
    );
  }

  /**
   * 检查是否为完整词汇
   * @param {string} word - 词汇
   * @returns {boolean} 是否为完整词汇
   */
  static isCompleteWord(word) {
    // 地区词汇
    if (this.REGION_MAPPING[word]) return true;
    
    // 行业词汇
    if (this.INDUSTRY_MAPPING[word]) return true;
    
    // 常见完整词汇模式
    const completeWordPatterns = [
      /^.+公司$/,
      /^.+集团$/,
      /^.+企业$/,
      /^.+工业$/,
      /^.+科技$/,
      /^.+贸易$/,
      /^.+技术$/,
      /^.+发展$/,
      /^.+建设$/,
      /^.+管理$/,
      /^.+服务$/
    ];
    
    return completeWordPatterns.some(pattern => pattern.test(word));
  }

  /**
   * 扩展搜索词汇 - 添加同义词和相关词
   * @param {Array} segments - 分词结果
   * @returns {Array} 扩展后的词汇数组
   */
  static expandSearchTerms(segments) {
    const expandedTerms = new Set(segments);
    
    segments.forEach(segment => {
      // 地区词汇扩展
      Object.entries(this.REGION_MAPPING).forEach(([key, values]) => {
        if (values.includes(segment) || key === segment) {
          expandedTerms.add(key);
          values.forEach(value => expandedTerms.add(value));
        }
      });
      
      // 行业词汇扩展
      Object.entries(this.INDUSTRY_MAPPING).forEach(([key, values]) => {
        if (values.includes(segment) || key === segment) {
          expandedTerms.add(key);
          values.forEach(value => expandedTerms.add(value));
        }
      });
    });
    
    return Array.from(expandedTerms);
  }

  /**
   * 构建精准搜索条件 - 优先精确匹配，然后AND逻辑
   * @param {string} searchText - 搜索文本
   * @param {Array} searchFields - 搜索字段
   * @param {Object} options - 搜索选项
   * @returns {Object} 搜索条件和参数
   */
  static buildIntelligentSearchCondition(searchText, searchFields, options = {}) {
    if (!searchText || !searchText.trim()) {
      return { condition: '', params: [] };
    }

    const {
      caseSensitive = false,
      enableRegionMapping = true
    } = options;

    const originalText = searchText.trim();

    // 分词处理
    const segments = this.segmentChinese(originalText);
    if (segments.length === 0) {
      return { condition: '', params: [] };
    }

    console.log('精准搜索分词结果:', {
      original: originalText,
      segments: segments
    });

    const conditions = [];
    const params = [];

    // 策略1: 优先尝试完整文本匹配
    const exactFieldConditions = searchFields.map(field => {
      const fieldName = caseSensitive ? field : `LOWER(${field})`;
      const processedText = caseSensitive ? originalText : originalText.toLowerCase();
      params.push(`%${processedText}%`);
      return `${fieldName} LIKE ?`;
    });
    conditions.push(`(${exactFieldConditions.join(' OR ')})`);

    // 策略2: 如果有多个分词，使用AND逻辑（所有词都必须匹配）
    if (segments.length > 1) {
      const segmentConditions = segments.map(segment => {
        // 对地区词汇进行有限扩展
        const searchTerms = enableRegionMapping ? this.getRegionVariants(segment) : [segment];

        const termConditions = searchTerms.map(term => {
          const termFieldConditions = searchFields.map(field => {
            const fieldName = caseSensitive ? field : `LOWER(${field})`;
            const processedTerm = caseSensitive ? term : term.toLowerCase();
            params.push(`%${processedTerm}%`);
            return `${fieldName} LIKE ?`;
          });
          return `(${termFieldConditions.join(' OR ')})`;
        });

        return `(${termConditions.join(' OR ')})`;
      });

      // 所有分词都必须匹配（AND逻辑）
      conditions.push(`(${segmentConditions.join(' AND ')})`);
    }

    // 使用OR逻辑连接两种策略：精确匹配 OR 分词AND匹配
    const finalCondition = `(${conditions.join(' OR ')})`;

    return {
      condition: finalCondition,
      params: params
    };
  }

  /**
   * 获取地区词汇的有限变体（仅地区映射，不扩展行业词汇）
   * @param {string} term - 词汇
   * @returns {Array} 变体数组
   */
  static getRegionVariants(term) {
    const variants = [term];

    // 仅对地区词汇进行扩展
    Object.entries(this.REGION_MAPPING).forEach(([key, values]) => {
      if (key === term) {
        variants.push(...values);
      } else if (values.includes(term)) {
        variants.push(key);
        variants.push(...values.filter(v => v !== term));
      }
    });

    // 去重并返回
    return [...new Set(variants)];
  }

  /**
   * 前端精准过滤数据 - 优先精确匹配，然后AND逻辑
   * @param {Array} data - 数据数组
   * @param {string} searchText - 搜索文本
   * @param {Array} searchFields - 搜索字段
   * @param {Object} options - 搜索选项
   * @returns {Array} 过滤后的数据
   */
  static intelligentFilterData(data, searchText, searchFields, options = {}) {
    if (!searchText || !searchText.trim() || !Array.isArray(data)) {
      return data;
    }

    const {
      caseSensitive = false,
      enableRegionMapping = true
    } = options;

    const originalText = searchText.trim();

    // 分词处理
    const segments = this.segmentChinese(originalText);
    if (segments.length === 0) {
      return data;
    }

    return data.filter(item => {
      // 策略1: 优先尝试完整文本匹配
      const exactMatch = searchFields.some(field => {
        const value = this.getNestedValue(item, field);
        if (value == null) return false;

        const stringValue = caseSensitive ? String(value) : String(value).toLowerCase();
        const searchValue = caseSensitive ? originalText : originalText.toLowerCase();

        return stringValue.includes(searchValue);
      });

      if (exactMatch) {
        return true;
      }

      // 策略2: 如果没有精确匹配且有多个分词，使用AND逻辑
      if (segments.length > 1) {
        return segments.every(segment => {
          // 对地区词汇进行有限扩展
          const searchTerms = enableRegionMapping ? this.getRegionVariants(segment) : [segment];

          return searchTerms.some(term => {
            return searchFields.some(field => {
              const value = this.getNestedValue(item, field);
              if (value == null) return false;

              const stringValue = caseSensitive ? String(value) : String(value).toLowerCase();
              const searchTerm = caseSensitive ? term : term.toLowerCase();

              return stringValue.includes(searchTerm);
            });
          });
        });
      }

      // 单个词汇的情况，使用地区扩展
      if (segments.length === 1) {
        const searchTerms = enableRegionMapping ? this.getRegionVariants(segments[0]) : segments;

        return searchTerms.some(term => {
          return searchFields.some(field => {
            const value = this.getNestedValue(item, field);
            if (value == null) return false;

            const stringValue = caseSensitive ? String(value) : String(value).toLowerCase();
            const searchTerm = caseSensitive ? term : term.toLowerCase();

            return stringValue.includes(searchTerm);
          });
        });
      }

      return false;
    });
  }

  /**
   * 获取嵌套对象的值
   * @param {Object} obj - 对象
   * @param {string} path - 路径
   * @returns {any} 值
   */
  static getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }

  /**
   * 计算搜索相关性得分
   * @param {string} text - 文本
   * @param {string} searchText - 搜索文本
   * @returns {number} 相关性得分 (0-100)
   */
  static calculateRelevanceScore(text, searchText) {
    if (!text || !searchText) return 0;
    
    const textSegments = this.segmentChinese(text);
    const searchSegments = this.segmentChinese(searchText);
    const expandedSearchTerms = this.expandSearchTerms(searchSegments);
    
    let matchCount = 0;
    let totalWeight = 0;
    
    expandedSearchTerms.forEach(term => {
      const weight = searchSegments.includes(term) ? 2 : 1; // 原始词汇权重更高
      totalWeight += weight;
      
      if (textSegments.some(segment => segment.includes(term) || term.includes(segment))) {
        matchCount += weight;
      }
    });
    
    return totalWeight > 0 ? Math.round((matchCount / totalWeight) * 100) : 0;
  }
}

module.exports = ChineseSearchUtils;
