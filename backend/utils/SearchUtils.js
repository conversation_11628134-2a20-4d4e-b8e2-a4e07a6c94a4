const ChineseSearchUtils = require('./ChineseSearchUtils');

/**
 * 搜索工具类 - 提供强大的模糊搜索功能
 */
class SearchUtils {
  /**
   * 构建精准搜索条件 - 优先精确匹配，然后AND逻辑
   * @param {string} searchText - 搜索文本
   * @param {Array} searchFields - 搜索字段列表
   * @param {Object} options - 搜索选项
   * @returns {Object} { condition, params }
   */
  static buildIntelligentSearchCondition(searchText, searchFields, options = {}) {
    const {
      enableChineseSegmentation = true,
      ...otherOptions
    } = options;

    // 检测是否包含中文字符
    const containsChinese = /[\u4e00-\u9fa5]/.test(searchText);

    if (enableChineseSegmentation && containsChinese) {
      // 使用中文精准搜索
      return ChineseSearchUtils.buildIntelligentSearchCondition(
        searchText,
        searchFields,
        otherOptions
      );
    } else {
      // 对于非中文搜索，使用多关键词AND逻辑
      const { multiKeyword = true, keywordLogic = 'AND' } = options;

      // 强制使用AND逻辑，确保精准搜索
      const searchOptions = {
        ...otherOptions,
        multiKeyword,
        keywordLogic: 'AND'  // 强制使用AND逻辑
      };

      return this.buildSearchCondition(searchText, searchFields, searchOptions);
    }
  }

  /**
   * 构建SQL搜索条件
   * @param {string} searchText - 搜索文本
   * @param {Array} searchFields - 搜索字段列表
   * @param {Object} options - 搜索选项
   * @returns {Object} { condition, params }
   */
  static buildSearchCondition(searchText, searchFields, options = {}) {
    if (!searchText || !searchText.trim()) {
      return { condition: '', params: [] };
    }

    const {
      multiKeyword = true,      // 是否支持多关键词搜索
      keywordLogic = 'AND',     // 多关键词逻辑：AND 或 OR
      fuzzyMatch = true,        // 是否模糊匹配
      caseSensitive = false     // 是否区分大小写
    } = options;

    const searchValue = caseSensitive ? searchText.trim() : searchText.trim().toLowerCase();
    
    // 处理多关键词
    const keywords = multiKeyword 
      ? searchValue.split(/\s+/).filter(keyword => keyword.length > 0)
      : [searchValue];

    if (keywords.length === 0) {
      return { condition: '', params: [] };
    }

    const conditions = [];
    const params = [];

    keywords.forEach(keyword => {
      const fieldConditions = searchFields.map(field => {
        const fieldName = caseSensitive ? field : `LOWER(${field})`;
        const processedKeyword = caseSensitive ? keyword : keyword.toLowerCase();
        const searchTerm = fuzzyMatch ? `%${processedKeyword}%` : processedKeyword;
        params.push(searchTerm);
        return `${fieldName} LIKE ?`;
      });

      conditions.push(`(${fieldConditions.join(' OR ')})`);
    });

    const finalCondition = keywords.length > 1 
      ? `(${conditions.join(` ${keywordLogic} `)})`
      : conditions[0];

    return {
      condition: finalCondition,
      params: params
    };
  }

  /**
   * 精准搜索过滤 - 优先精确匹配，然后AND逻辑
   * @param {Array} data - 数据数组
   * @param {string} searchText - 搜索文本
   * @param {Array} searchFields - 搜索字段列表
   * @param {Object} options - 搜索选项
   * @returns {Array} 过滤后的数据
   */
  static intelligentFilterData(data, searchText, searchFields, options = {}) {
    const {
      enableChineseSegmentation = true,
      ...otherOptions
    } = options;

    // 检测是否包含中文字符
    const containsChinese = /[\u4e00-\u9fa5]/.test(searchText);

    if (enableChineseSegmentation && containsChinese) {
      // 使用中文精准搜索
      return ChineseSearchUtils.intelligentFilterData(
        data,
        searchText,
        searchFields,
        otherOptions
      );
    } else {
      // 对于非中文搜索，使用多关键词AND逻辑
      const { multiKeyword = true, keywordLogic = 'AND' } = options;

      // 强制使用AND逻辑，确保精准搜索
      const searchOptions = {
        ...otherOptions,
        multiKeyword,
        keywordLogic: 'AND'  // 强制使用AND逻辑
      };

      return this.filterData(data, searchText, searchFields, searchOptions);
    }
  }

  /**
   * 前端JavaScript搜索过滤
   * @param {Array} data - 数据数组
   * @param {string} searchText - 搜索文本
   * @param {Array} searchFields - 搜索字段列表（支持嵌套字段，如 'user.name'）
   * @param {Object} options - 搜索选项
   * @returns {Array} 过滤后的数据
   */
  static filterData(data, searchText, searchFields, options = {}) {
    if (!searchText || !searchText.trim() || !Array.isArray(data)) {
      return data;
    }

    const {
      multiKeyword = true,
      keywordLogic = 'AND',
      caseSensitive = false,
      highlightMatch = false
    } = options;

    const searchValue = caseSensitive ? searchText.trim() : searchText.trim().toLowerCase();
    const keywords = multiKeyword 
      ? searchValue.split(/\s+/).filter(keyword => keyword.length > 0)
      : [searchValue];

    return data.filter(item => {
      const keywordMatches = keywords.map(keyword => {
        return searchFields.some(field => {
          const value = this.getNestedValue(item, field);
          if (value == null) return false;
          
          const stringValue = caseSensitive ? String(value) : String(value).toLowerCase();
          return stringValue.includes(keyword);
        });
      });

      return keywordLogic === 'AND' 
        ? keywordMatches.every(match => match)
        : keywordMatches.some(match => match);
    });
  }

  /**
   * 获取嵌套对象的值
   * @param {Object} obj - 对象
   * @param {string} path - 路径，如 'user.profile.name'
   * @returns {any} 值
   */
  static getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }

  /**
   * 高亮搜索关键词
   * @param {string} text - 原始文本
   * @param {string} searchText - 搜索文本
   * @param {Object} options - 选项
   * @returns {string} 高亮后的HTML
   */
  static highlightText(text, searchText, options = {}) {
    if (!text || !searchText) return text;

    const {
      caseSensitive = false,
      highlightClass = 'search-highlight',
      multiKeyword = true
    } = options;

    const searchValue = caseSensitive ? searchText.trim() : searchText.trim().toLowerCase();
    const keywords = multiKeyword 
      ? searchValue.split(/\s+/).filter(keyword => keyword.length > 0)
      : [searchValue];

    let result = text;
    
    keywords.forEach(keyword => {
      const flags = caseSensitive ? 'g' : 'gi';
      const regex = new RegExp(`(${this.escapeRegExp(keyword)})`, flags);
      result = result.replace(regex, `<span class="${highlightClass}">$1</span>`);
    });

    return result;
  }

  /**
   * 转义正则表达式特殊字符
   * @param {string} string - 字符串
   * @returns {string} 转义后的字符串
   */
  static escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * 智能搜索建议
   * @param {string} searchText - 搜索文本
   * @param {Array} data - 数据数组
   * @param {Array} searchFields - 搜索字段
   * @param {number} maxSuggestions - 最大建议数量
   * @returns {Array} 搜索建议
   */
  static getSearchSuggestions(searchText, data, searchFields, maxSuggestions = 5) {
    if (!searchText || !searchText.trim()) return [];

    const suggestions = new Set();
    const searchValue = searchText.toLowerCase();

    data.forEach(item => {
      searchFields.forEach(field => {
        const value = this.getNestedValue(item, field);
        if (value) {
          const stringValue = String(value).toLowerCase();
          if (stringValue.includes(searchValue) && stringValue !== searchValue) {
            suggestions.add(String(value));
          }
        }
      });
    });

    return Array.from(suggestions)
      .sort((a, b) => {
        // 优先显示以搜索词开头的建议
        const aStartsWith = a.toLowerCase().startsWith(searchValue);
        const bStartsWith = b.toLowerCase().startsWith(searchValue);
        if (aStartsWith && !bStartsWith) return -1;
        if (!aStartsWith && bStartsWith) return 1;
        return a.length - b.length; // 然后按长度排序
      })
      .slice(0, maxSuggestions);
  }

  /**
   * 搜索统计信息
   * @param {Array} originalData - 原始数据
   * @param {Array} filteredData - 过滤后数据
   * @param {string} searchText - 搜索文本
   * @returns {Object} 统计信息
   */
  static getSearchStats(originalData, filteredData, searchText) {
    return {
      total: originalData.length,
      filtered: filteredData.length,
      searchText: searchText,
      matchRate: originalData.length > 0 ? (filteredData.length / originalData.length * 100).toFixed(1) : 0,
      keywords: searchText ? searchText.trim().split(/\s+/).filter(k => k.length > 0) : []
    };
  }
}

module.exports = SearchUtils;
