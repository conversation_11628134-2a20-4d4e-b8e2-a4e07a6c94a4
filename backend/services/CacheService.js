/**
 * 缓存服务 - 基于Redis实现API响应缓存
 * 用于提升系统性能，减少数据库查询压力
 */

const redis = require('redis');

class CacheService {
    constructor() {
        this.client = null;
        this.isConnected = false;
        this.init();
    }

    /**
     * 初始化Redis连接
     */
    async init() {
        try {
            this.client = redis.createClient({
                host: process.env.REDIS_HOST || '127.0.0.1',
                port: process.env.REDIS_PORT || 6379,
                password: process.env.REDIS_PASSWORD || undefined,
                db: process.env.REDIS_DB || 0,
                retry_strategy: (options) => {
                    if (options.error && options.error.code === 'ECONNREFUSED') {
                        console.error('Redis连接被拒绝');
                        return new Error('Redis服务器连接被拒绝');
                    }
                    if (options.total_retry_time > 1000 * 60 * 60) {
                        console.error('Redis重试时间超过1小时，停止重试');
                        return new Error('重试时间过长');
                    }
                    if (options.attempt > 10) {
                        console.error('Redis重试次数超过10次，停止重试');
                        return undefined;
                    }
                    // 重试间隔：最小100ms，最大3000ms
                    return Math.min(options.attempt * 100, 3000);
                }
            });

            this.client.on('connect', () => {
                console.log('✅ Redis连接成功');
                this.isConnected = true;
            });

            this.client.on('error', (err) => {
                console.error('❌ Redis连接错误:', err.message);
                this.isConnected = false;
            });

            this.client.on('end', () => {
                console.log('⚠️ Redis连接断开');
                this.isConnected = false;
            });

            await this.client.connect();
        } catch (error) {
            console.error('❌ Redis初始化失败:', error.message);
            this.isConnected = false;
        }
    }

    /**
     * 获取缓存数据
     * @param {string} key 缓存键
     * @returns {Promise<any|null>} 缓存数据或null
     */
    async get(key) {
        if (!this.isConnected || !this.client) {
            console.warn('⚠️ Redis未连接，跳过缓存读取');
            return null;
        }

        try {
            const cached = await this.client.get(key);
            if (cached) {
                console.log(`🎯 缓存命中: ${key}`);
                return JSON.parse(cached);
            }
            console.log(`❌ 缓存未命中: ${key}`);
            return null;
        } catch (error) {
            console.error(`❌ 缓存读取失败 (${key}):`, error.message);
            return null;
        }
    }

    /**
     * 设置缓存数据
     * @param {string} key 缓存键
     * @param {any} data 要缓存的数据
     * @param {number} ttl 过期时间（秒），默认300秒（5分钟）
     * @returns {Promise<boolean>} 是否设置成功
     */
    async set(key, data, ttl = 300) {
        if (!this.isConnected || !this.client) {
            console.warn('⚠️ Redis未连接，跳过缓存写入');
            return false;
        }

        try {
            await this.client.setEx(key, ttl, JSON.stringify(data));
            console.log(`✅ 缓存设置成功: ${key} (TTL: ${ttl}s)`);
            return true;
        } catch (error) {
            console.error(`❌ 缓存设置失败 (${key}):`, error.message);
            return false;
        }
    }

    /**
     * 删除缓存
     * @param {string} key 缓存键
     * @returns {Promise<boolean>} 是否删除成功
     */
    async del(key) {
        if (!this.isConnected || !this.client) {
            console.warn('⚠️ Redis未连接，跳过缓存删除');
            return false;
        }

        try {
            const result = await this.client.del(key);
            console.log(`🗑️ 缓存删除: ${key} (结果: ${result})`);
            return result > 0;
        } catch (error) {
            console.error(`❌ 缓存删除失败 (${key}):`, error.message);
            return false;
        }
    }

    /**
     * 批量删除缓存（支持通配符）
     * @param {string} pattern 匹配模式，如 'websites:*'
     * @returns {Promise<number>} 删除的键数量
     */
    async delPattern(pattern) {
        if (!this.isConnected || !this.client) {
            console.warn('⚠️ Redis未连接，跳过批量缓存删除');
            return 0;
        }

        try {
            const keys = await this.client.keys(pattern);
            if (keys.length === 0) {
                console.log(`🔍 未找到匹配的缓存键: ${pattern}`);
                return 0;
            }

            const result = await this.client.del(keys);
            console.log(`🗑️ 批量删除缓存: ${pattern} (删除${result}个键)`);
            return result;
        } catch (error) {
            console.error(`❌ 批量缓存删除失败 (${pattern}):`, error.message);
            return 0;
        }
    }

    /**
     * 检查缓存键是否存在
     * @param {string} key 缓存键
     * @returns {Promise<boolean>} 是否存在
     */
    async exists(key) {
        if (!this.isConnected || !this.client) {
            return false;
        }

        try {
            const result = await this.client.exists(key);
            return result === 1;
        } catch (error) {
            console.error(`❌ 缓存存在性检查失败 (${key}):`, error.message);
            return false;
        }
    }

    /**
     * 设置缓存过期时间
     * @param {string} key 缓存键
     * @param {number} ttl 过期时间（秒）
     * @returns {Promise<boolean>} 是否设置成功
     */
    async expire(key, ttl) {
        if (!this.isConnected || !this.client) {
            return false;
        }

        try {
            const result = await this.client.expire(key, ttl);
            console.log(`⏰ 设置缓存过期时间: ${key} (TTL: ${ttl}s)`);
            return result === 1;
        } catch (error) {
            console.error(`❌ 设置缓存过期时间失败 (${key}):`, error.message);
            return false;
        }
    }

    /**
     * 获取缓存统计信息
     * @returns {Promise<object>} 缓存统计
     */
    async getStats() {
        if (!this.isConnected || !this.client) {
            return {
                connected: false,
                keys: 0,
                memory: '0B'
            };
        }

        try {
            const info = await this.client.info('memory');
            const dbSize = await this.client.dbSize();
            
            // 解析内存使用信息
            const memoryMatch = info.match(/used_memory_human:(.+)/);
            const memory = memoryMatch ? memoryMatch[1].trim() : '未知';

            return {
                connected: this.isConnected,
                keys: dbSize,
                memory: memory,
                info: info
            };
        } catch (error) {
            console.error('❌ 获取缓存统计失败:', error.message);
            return {
                connected: false,
                keys: 0,
                memory: '0B',
                error: error.message
            };
        }
    }

    /**
     * 清空所有缓存
     * @returns {Promise<boolean>} 是否清空成功
     */
    async flushAll() {
        if (!this.isConnected || !this.client) {
            console.warn('⚠️ Redis未连接，无法清空缓存');
            return false;
        }

        try {
            await this.client.flushAll();
            console.log('🧹 已清空所有缓存');
            return true;
        } catch (error) {
            console.error('❌ 清空缓存失败:', error.message);
            return false;
        }
    }

    /**
     * 关闭Redis连接
     */
    async close() {
        if (this.client) {
            try {
                await this.client.quit();
                console.log('👋 Redis连接已关闭');
            } catch (error) {
                console.error('❌ 关闭Redis连接失败:', error.message);
            }
        }
    }

    /**
     * 生成缓存键
     * @param {string} prefix 前缀
     * @param {...any} parts 键的组成部分
     * @returns {string} 缓存键
     */
    static generateKey(prefix, ...parts) {
        return `${prefix}:${parts.filter(p => p !== undefined && p !== null).join(':')}`;
    }
}

// 创建单例实例
const cacheService = new CacheService();

module.exports = cacheService;