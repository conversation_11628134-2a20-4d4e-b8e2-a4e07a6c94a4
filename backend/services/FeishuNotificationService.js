const fetch = require('node-fetch');

/**
 * 飞书通知服务
 * 专门处理网站状态异常的飞书通知，支持表格格式展示
 */
class FeishuNotificationService {
  constructor(db) {
    this.db = db;
    this.config = {
      webhookUrl: null,
      botName: '网站监控机器人',
      enabled: false,
      notificationThreshold: 5, // 连续失败5次后发送通知
      batchSize: 10 // 批量通知时每批最多10个网站
    };
    
    // 初始化配置
    this.loadConfig();
  }

  /**
   * 从数据库加载飞书配置
   */
  async loadConfig() {
    try {
      const [rows] = await this.db.execute(`
        SELECT config, is_active
        FROM notification_configs
        WHERE type = 'feishu'
        ORDER BY created_at DESC
        LIMIT 1
      `);

      if (rows.length > 0) {
        try {
          const configData = rows[0].config;
          console.log('🔍 配置数据类型:', typeof configData);

          // 如果已经是对象，直接使用；如果是字符串，则解析
          const config = typeof configData === 'object' ? configData : JSON.parse(configData);
          this.config = {
            ...this.config,
            ...config,
            enabled: rows[0].is_active === 1
          };
          console.log('✅ 飞书配置加载成功:', this.config);
        } catch (parseError) {
          console.error('❌ 解析飞书配置失败:', parseError);
          console.log('使用默认配置');
        }
      } else {
        console.log('ℹ️  未找到飞书配置，使用默认配置');
      }
    } catch (error) {
      console.error('❌ 加载飞书配置失败:', error);
    }
  }

  /**
   * 发送网站状态异常通知（表格格式）
   */
  async sendWebsiteAbnormalNotification() {
    // 确保配置已加载
    await this.loadConfig();

    if (!this.config.enabled || !this.config.webhookUrl) {
      console.log('ℹ️  飞书通知未启用或未配置，跳过发送');
      return { success: false, reason: '飞书通知未启用' };
    }

    try {
      // 获取需要通知的异常网站
      const abnormalWebsites = await this.getAbnormalWebsites();
      
      if (abnormalWebsites.length === 0) {
        console.log('ℹ️  没有需要发送通知的异常网站');
        return { success: true, message: '没有异常网站' };
      }

      console.log(`📢 发现 ${abnormalWebsites.length} 个异常网站需要发送飞书通知`);

      // 构建表格格式的通知内容
      const message = this.buildTableNotificationMessage(abnormalWebsites);

      // 发送飞书通知
      const result = await this.sendFeishuMessage(message);

      if (result.success) {
        // 更新通知状态
        await this.updateNotificationStatus(abnormalWebsites);
        console.log(`✅ 飞书异常通知发送成功: ${abnormalWebsites.length} 个网站`);
      }

      return result;
    } catch (error) {
      console.error('❌ 发送飞书异常通知失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取需要通知的异常网站
   */
  async getAbnormalWebsites() {
    try {
      const [rows] = await this.db.execute(`
        SELECT
          w.id,
          w.site_name,
          w.domain,
          w.site_url,
          p.name as platform_name,
          w.access_status,
          w.access_status_code,
          w.last_check_time,
          ws.consecutive_failures,
          ws.first_failure_time,
          ws.notification_sent,
          ws.last_notification_time,
          TIMESTAMPDIFF(HOUR, ws.first_failure_time, NOW()) as alarm_duration_hours,
          TIMESTAMPDIFF(MINUTE, ws.first_failure_time, NOW()) as alarm_duration_minutes
        FROM websites w
        LEFT JOIN website_status_stats ws ON w.id = ws.website_id
        LEFT JOIN platforms p ON w.platform_id = p.id
        WHERE w.status = 'active'
        AND ws.consecutive_failures >= ?
        AND (
          -- 真正的异常：状态码不是2xx或3xx
          (w.access_status_code < 200 OR w.access_status_code >= 400) OR
          -- 或者状态为offline（通常状态码为0）
          w.access_status = 'offline'
        )
        AND (ws.notification_sent = FALSE OR ws.last_notification_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE))
        ORDER BY ws.consecutive_failures DESC, ws.first_failure_time ASC
        LIMIT 20
      `, [this.config.notificationThreshold]);

      return rows.map(row => ({
        id: row.id,
        siteName: row.site_name,
        domain: row.domain,
        siteUrl: row.site_url,
        platform: row.platform_name || 'WordPress',
        statusCode: row.access_status_code || 0,
        consecutiveFailures: row.consecutive_failures,
        firstFailureTime: row.first_failure_time,
        lastCheckTime: row.last_check_time,
        alarmDurationHours: row.alarm_duration_hours,
        alarmDurationMinutes: row.alarm_duration_minutes,
        notificationSent: row.notification_sent,
        lastNotificationTime: row.last_notification_time
      }));
    } catch (error) {
      console.error('❌ 获取异常网站列表失败:', error);
      throw error;
    }
  }

  /**
   * 构建表格格式的通知消息
   */
  buildTableNotificationMessage(websites) {
    const currentTime = new Date().toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    let message = `🚨 网站访问异常提醒\n\n`;
    message += `报告数量：${websites.length}\n`;
    message += `生成时间：${currentTime}\n\n`;

    // 使用更宽的表格格式，适配中文显示
    message += `┌──────┬──────────┬──────────────────────────────┬──────────────────────────────────────────┬────────┬─────────────┬──────────────┐\n`;
    message += `│ ID   │ Platform │         Site Name            │                   URL                    │ Status │ Last Alarm  │Alarm Duration│\n`;
    message += `│      │          │                              │                                          │  Code  │             │              │\n`;
    message += `├──────┼──────────┼──────────────────────────────┼──────────────────────────────────────────┼────────┼─────────────┼──────────────┤\n`;

    // 表格内容
    websites.forEach((website, index) => {
      // 处理中文字符宽度问题的函数
      const padString = (str, length) => {
        const strLength = this.getStringDisplayLength(str);
        const padding = Math.max(0, length - strLength);
        return str + ' '.repeat(padding);
      };

      const siteId = String(website.id).padStart(4, ' ');
      const platform = padString(String(website.platform).substring(0, 8), 8);
      const siteName = padString(String(website.siteName).substring(0, 28), 28);

      // URL处理：显示域名部分，如果太长则截断并加省略号
      let displayUrl = String(website.siteUrl || '');
      if (displayUrl.length > 38) {
        // 尝试只显示域名部分
        try {
          const urlObj = new URL(displayUrl);
          displayUrl = urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
          if (displayUrl.length > 38) {
            displayUrl = displayUrl.substring(0, 35) + '...';
          }
        } catch {
          displayUrl = displayUrl.substring(0, 35) + '...';
        }
      }
      const url = padString(displayUrl, 38);

      const statusCode = String(website.statusCode).padStart(4, ' ');

      // 格式化最后告警时间 - 使用更紧凑的格式
      const lastAlarmTime = website.lastCheckTime ?
        new Date(website.lastCheckTime).toLocaleString('zh-CN', {
          timeZone: 'Asia/Shanghai',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        }).replace(/\//g, '-').replace(/年|月/g, '-').replace(/日/g, '') : '未知';
      const lastAlarm = padString(lastAlarmTime, 11);

      // 格式化告警持续时间 - 使用更简洁的格式
      let durationText;
      if (website.alarmDurationHours >= 24) {
        const days = Math.floor(website.alarmDurationHours / 24);
        const hours = website.alarmDurationHours % 24;
        durationText = `${days}天${hours}小时`;
      } else if (website.alarmDurationHours > 0) {
        const minutes = website.alarmDurationMinutes % 60;
        durationText = `${website.alarmDurationHours}小时${minutes}分`;
      } else {
        durationText = `${website.alarmDurationMinutes}分钟`;
      }
      const duration = padString(durationText.substring(0, 12), 12);

      message += `│ ${siteId} │ ${platform} │ ${siteName} │ ${url} │  ${statusCode}  │ ${lastAlarm} │ ${duration} │\n`;
    });

    message += `└──────┴──────────┴──────────────────────────────┴──────────────────────────────────────────┴────────┴─────────────┴──────────────┘\n\n`;

    // 添加处理建议
    message += `🔧 处理建议：\n`;
    message += `1. 优先处理持续时间最长的异常网站\n`;
    message += `2. 检查服务器资源使用情况\n`;
    message += `3. 验证域名解析和网络连接\n`;
    message += `4. 查看网站服务日志排查问题\n\n`;

    message += `📊 统计信息：\n`;
    message += `• 异常网站总数：${websites.length}\n`;
    message += `• 平台分布：${this.getPlatformStats(websites)}\n`;
    message += `• 状态码分布：${this.getStatusCodeStats(websites)}\n\n`;

    message += `⏰ 下次检查时间：${new Date(Date.now() + 10 * 60 * 1000).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n`;
    message += `🤖 ${this.config.botName}`;

    return message;
  }

  /**
   * 计算字符串显示长度（中文字符算2个长度）
   */
  getStringDisplayLength(str) {
    let length = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charAt(i);
      // 中文字符、全角字符等宽字符算2个长度
      if (char.match(/[\u4e00-\u9fa5\uff00-\uffff]/)) {
        length += 2;
      } else {
        length += 1;
      }
    }
    return length;
  }

  /**
   * 获取平台统计信息
   */
  getPlatformStats(websites) {
    const stats = {};
    websites.forEach(site => {
      const platform = site.platform || '未知';
      stats[platform] = (stats[platform] || 0) + 1;
    });
    
    return Object.entries(stats)
      .map(([platform, count]) => `${platform}(${count})`)
      .join(', ');
  }

  /**
   * 获取状态码统计信息
   */
  getStatusCodeStats(websites) {
    const stats = {};
    websites.forEach(site => {
      const code = site.statusCode || 0;
      stats[code] = (stats[code] || 0) + 1;
    });
    
    return Object.entries(stats)
      .map(([code, count]) => `${code}(${count})`)
      .join(', ');
  }

  /**
   * 发送飞书消息
   */
  async sendFeishuMessage(message) {
    try {
      const response = await fetch(this.config.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          msg_type: 'text',
          content: {
            text: message
          }
        }),
      });

      const result = await response.json();

      if (response.ok && result.code === 0) {
        return { success: true, data: result };
      } else {
        const errorMsg = result.msg || '飞书通知发送失败';
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error(`❌ 飞书消息发送失败: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  /**
   * 更新通知状态
   */
  async updateNotificationStatus(websites) {
    try {
      const websiteIds = websites.map(site => site.id);
      
      if (websiteIds.length === 0) return;

      await this.db.execute(`
        UPDATE website_status_stats 
        SET notification_sent = TRUE,
            notification_count = notification_count + 1,
            last_notification_time = NOW()
        WHERE website_id IN (${websiteIds.map(() => '?').join(',')})
      `, websiteIds);

      console.log(`✅ 更新通知状态: ${websiteIds.length} 个网站`);
    } catch (error) {
      console.error('❌ 更新通知状态失败:', error);
    }
  }

  /**
   * 测试飞书通知
   */
  async testNotification() {
    // 确保配置已加载
    await this.loadConfig();

    if (!this.config.enabled || !this.config.webhookUrl) {
      return { success: false, message: '飞书通知未启用或未配置' };
    }

    const testMessage = `🧪 飞书通知测试\n\n` +
      `这是一条测试消息，用于验证飞书通知配置是否正确。\n\n` +
      `测试时间：${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n` +
      `🤖 ${this.config.botName}`;

    return await this.sendFeishuMessage(testMessage);
  }
}

module.exports = FeishuNotificationService;
