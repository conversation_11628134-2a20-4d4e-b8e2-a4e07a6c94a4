/**
 * 站点存活检测与通知服务
 * 支持1000+站点的高效检测和实时通知
 */

const axios = require('axios');
const https = require('https');
const dns = require('dns').promises;
const EventEmitter = require('events');

class MonitoringService extends EventEmitter {
  constructor(db, notificationService) {
    super();
    this.db = db;
    this.notificationService = notificationService;
    this.isRunning = false;
    this.activeChecks = new Map();
    this.checkQueue = [];
    
    // 检测配置
    this.config = {
      batchSize: 50,           // 每批检测站点数
      concurrency: 10,         // 并发检测数
      batchDelay: 2000,        // 批次间延迟(ms)
      retryAttempts: 3,        // 重试次数
      retryDelay: 1000,        // 重试延迟(ms)
      
      // 检测级别配置
      levels: {
        critical: { interval: 30, timeout: 5000, priority: 1 },
        important: { interval: 60, timeout: 8000, priority: 2 },
        normal: { interval: 300, timeout: 10000, priority: 3 },
        low: { interval: 900, timeout: 15000, priority: 4 }
      }
    };
    
    // HTTP客户端配置
    this.httpClient = axios.create({
      timeout: 10000,
      maxRedirects: 3,
      validateStatus: (status) => status < 500, // 4xx也算成功
      httpsAgent: new https.Agent({
        rejectUnauthorized: false, // 允许自签名证书
        timeout: 10000
      })
    });
  }

  /**
   * 启动监控服务
   */
  async start() {
    if (this.isRunning) {
      console.log('监控服务已在运行中');
      return;
    }

    console.log('🚀 启动站点存活监控服务...');
    this.isRunning = true;
    
    // 启动主检测循环
    this.startMainLoop();
    
    // 启动故障恢复检测
    this.startRecoveryCheck();
    
    console.log('✅ 监控服务启动成功');
  }

  /**
   * 停止监控服务
   */
  async stop() {
    console.log('🛑 停止监控服务...');
    this.isRunning = false;
    
    // 清理定时器
    if (this.mainTimer) clearTimeout(this.mainTimer);
    if (this.recoveryTimer) clearTimeout(this.recoveryTimer);
    
    // 等待当前检测完成
    await this.waitForActiveChecks();
    
    console.log('✅ 监控服务已停止');
  }

  /**
   * 主检测循环
   */
  async startMainLoop() {
    while (this.isRunning) {
      try {
        await this.performBatchCheck();
        await this.sleep(5000); // 主循环间隔5秒
      } catch (error) {
        console.error('主检测循环错误:', error);
        await this.sleep(10000); // 错误时延长间隔
      }
    }
  }

  /**
   * 执行批量检测
   */
  async performBatchCheck() {
    // 获取需要检测的站点
    const sitesToCheck = await this.getSitesToCheck();
    
    if (sitesToCheck.length === 0) {
      return;
    }

    console.log(`📊 开始检测 ${sitesToCheck.length} 个站点`);
    
    // 按优先级排序
    sitesToCheck.sort((a, b) => a.priority - b.priority);
    
    // 分批处理
    const batches = this.createBatches(sitesToCheck, this.config.batchSize);
    
    for (let i = 0; i < batches.length; i++) {
      if (!this.isRunning) break;
      
      const batch = batches[i];
      console.log(`🔍 检测第 ${i + 1}/${batches.length} 批，包含 ${batch.length} 个站点`);
      
      await this.processBatch(batch);
      
      // 批次间延迟
      if (i < batches.length - 1) {
        await this.sleep(this.config.batchDelay);
      }
    }
  }

  /**
   * 处理单个批次
   */
  async processBatch(batch) {
    const chunks = this.createBatches(batch, this.config.concurrency);
    
    for (const chunk of chunks) {
      if (!this.isRunning) break;
      
      const promises = chunk.map(site => this.checkSite(site));
      await Promise.allSettled(promises);
      
      // 并发块间小延迟
      await this.sleep(500);
    }
  }

  /**
   * 检测单个站点
   */
  async checkSite(site) {
    const checkId = `${site.id}_${Date.now()}`;
    this.activeChecks.set(checkId, site);
    
    try {
      const startTime = Date.now();
      
      // 执行多种检测
      const [httpResult, sslResult, dnsResult] = await Promise.allSettled([
        this.checkHTTP(site),
        this.checkSSL(site),
        this.checkDNS(site)
      ]);
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      // 综合检测结果
      const result = this.combineResults(site, {
        http: httpResult,
        ssl: sslResult,
        dns: dnsResult,
        responseTime
      });
      
      // 保存结果并检查状态变化
      await this.saveResult(site, result);
      await this.checkStatusChange(site, result);
      
    } catch (error) {
      console.error(`检测站点 ${site.domain} 失败:`, error);
      await this.handleCheckError(site, error);
    } finally {
      this.activeChecks.delete(checkId);
    }
  }

  /**
   * HTTP检测
   */
  async checkHTTP(site) {
    const url = site.siteUrl || `https://${site.domain}`;
    const timeout = this.config.levels[site.level]?.timeout || 10000;
    
    try {
      const response = await this.httpClient.get(url, { timeout });
      
      return {
        success: true,
        statusCode: response.status,
        responseTime: response.headers['x-response-time'] || 0,
        headers: response.headers,
        redirects: response.request._redirects || 0
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        code: error.code,
        statusCode: error.response?.status || 0
      };
    }
  }

  /**
   * SSL检测
   */
  async checkSSL(site) {
    return new Promise((resolve) => {
      const domain = site.domain;
      const options = {
        host: domain,
        port: 443,
        servername: domain,
        rejectUnauthorized: false
      };
      
      const socket = require('tls').connect(options, () => {
        const cert = socket.getPeerCertificate();
        const now = new Date();
        const expiry = new Date(cert.valid_to);
        const daysUntilExpiry = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24));
        
        resolve({
          success: true,
          issuer: cert.issuer?.CN || 'Unknown',
          subject: cert.subject?.CN || domain,
          validFrom: cert.valid_from,
          validTo: cert.valid_to,
          daysUntilExpiry,
          isValid: daysUntilExpiry > 0
        });
        
        socket.end();
      });
      
      socket.on('error', (error) => {
        resolve({
          success: false,
          error: error.message
        });
      });
      
      socket.setTimeout(5000, () => {
        socket.destroy();
        resolve({
          success: false,
          error: 'SSL检测超时'
        });
      });
    });
  }

  /**
   * DNS检测
   */
  async checkDNS(site) {
    try {
      const records = await dns.resolve4(site.domain);
      return {
        success: true,
        records,
        resolvable: true
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        resolvable: false
      };
    }
  }

  /**
   * 获取需要检测的站点
   */
  async getSitesToCheck() {
    const now = new Date();
    
    const query = `
      SELECT 
        w.*,
        COALESCE(w.monitor_level, 'normal') as level,
        COALESCE(w.last_check_time, '1970-01-01') as last_check,
        CASE 
          WHEN w.monitor_level = 'critical' THEN 1
          WHEN w.monitor_level = 'important' THEN 2
          WHEN w.monitor_level = 'normal' THEN 3
          ELSE 4
        END as priority
      FROM websites w
      WHERE w.status = 'active'
        AND (
          w.last_check_time IS NULL OR
          (w.monitor_level = 'critical' AND w.last_check_time < DATE_SUB(?, INTERVAL 30 SECOND)) OR
          (w.monitor_level = 'important' AND w.last_check_time < DATE_SUB(?, INTERVAL 1 MINUTE)) OR
          (w.monitor_level = 'normal' AND w.last_check_time < DATE_SUB(?, INTERVAL 5 MINUTE)) OR
          (w.monitor_level = 'low' AND w.last_check_time < DATE_SUB(?, INTERVAL 15 MINUTE))
        )
      ORDER BY priority ASC, last_check_time ASC
      LIMIT 1000
    `;
    
    const [rows] = await this.db.execute(query, [now, now, now, now]);
    return rows;
  }

  /**
   * 工具方法
   */
  createBatches(array, size) {
    const batches = [];
    for (let i = 0; i < array.length; i += size) {
      batches.push(array.slice(i, i + size));
    }
    return batches;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async waitForActiveChecks() {
    while (this.activeChecks.size > 0) {
      await this.sleep(100);
    }
  }
}

module.exports = MonitoringService;
