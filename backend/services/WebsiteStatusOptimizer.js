/**
 * 网站状态优化服务
 * 用于优化网站检测逻辑，减少误报和提高检测准确性
 */

class WebsiteStatusOptimizer {
  constructor(db) {
    this.db = db;
  }

  /**
   * 重置异常网站状态
   * 将连续失败次数异常高的网站状态重置
   */
  async resetAbnormalWebsiteStatus() {
    try {
      console.log('🔧 开始重置异常网站状态...');
      
      // 重置连续失败次数超过100的网站（这些明显是异常数据）
      const [result] = await this.db.execute(`
        UPDATE website_status_stats 
        SET consecutive_failures = 0,
            notification_sent = 0,
            notification_count = 0,
            last_notification_time = NULL,
            updated_at = CURRENT_TIMESTAMP
        WHERE consecutive_failures > 100
      `);

      console.log(`✅ 已重置 ${result.affectedRows} 个异常网站的状态`);

      // 获取重置后的统计
      const [stats] = await this.db.execute(`
        SELECT 
          CASE 
            WHEN consecutive_failures = 0 THEN '正常'
            WHEN consecutive_failures BETWEEN 1 AND 5 THEN '轻微异常'
            WHEN consecutive_failures BETWEEN 6 AND 20 THEN '需要关注'
            ELSE '严重异常'
          END as status_category,
          COUNT(*) as count
        FROM website_status_stats 
        GROUP BY 
          CASE 
            WHEN consecutive_failures = 0 THEN '正常'
            WHEN consecutive_failures BETWEEN 1 AND 5 THEN '轻微异常'
            WHEN consecutive_failures BETWEEN 6 AND 20 THEN '需要关注'
            ELSE '严重异常'
          END
        ORDER BY count DESC
      `);

      console.log('📊 网站状态分布统计:');
      stats.forEach(stat => {
        console.log(`   ${stat.status_category}: ${stat.count} 个网站`);
      });

      return {
        resetCount: result.affectedRows,
        statusDistribution: stats
      };
    } catch (error) {
      console.error('❌ 重置异常网站状态失败:', error);
      throw error;
    }
  }

  /**
   * 智能检测网站状态
   * 根据历史数据和当前状态，智能判断网站是否真的异常
   */
  async intelligentStatusCheck(websiteId, currentStatusCode, responseTime) {
    try {
      // 获取网站历史状态
      const [history] = await this.db.execute(`
        SELECT 
          consecutive_failures,
          total_checks,
          success_checks,
          last_success_time,
          last_failure_time,
          current_status_code
        FROM website_status_stats 
        WHERE website_id = ?
      `, [websiteId]);

      if (history.length === 0) {
        // 新网站，创建初始记录
        return this.createInitialStatusRecord(websiteId, currentStatusCode, responseTime);
      }

      const stats = history[0];
      const isCurrentSuccess = this.isSuccessStatusCode(currentStatusCode);
      
      // 智能判断逻辑
      if (isCurrentSuccess) {
        // 当前检测成功
        return this.handleSuccessfulCheck(websiteId, stats, currentStatusCode, responseTime);
      } else {
        // 当前检测失败
        return this.handleFailedCheck(websiteId, stats, currentStatusCode, responseTime);
      }
    } catch (error) {
      console.error(`❌ 智能状态检测失败 (网站ID: ${websiteId}):`, error);
      throw error;
    }
  }

  /**
   * 判断状态码是否表示成功
   */
  isSuccessStatusCode(statusCode) {
    // 2xx 和 3xx 状态码都认为是成功
    return statusCode >= 200 && statusCode < 400;
  }

  /**
   * 创建初始状态记录
   */
  async createInitialStatusRecord(websiteId, statusCode, responseTime) {
    const isSuccess = this.isSuccessStatusCode(statusCode);
    
    await this.db.execute(`
      INSERT INTO website_status_stats 
      (website_id, consecutive_failures, total_checks, success_checks, 
       current_status_code, current_response_time, last_success_time, last_failure_time)
      VALUES (?, ?, 1, ?, ?, ?, ?, ?)
    `, [
      websiteId,
      isSuccess ? 0 : 1,
      isSuccess ? 1 : 0,
      statusCode,
      responseTime,
      isSuccess ? new Date() : null,
      isSuccess ? null : new Date()
    ]);

    return { isSuccess, shouldNotify: false };
  }

  /**
   * 处理成功的检测
   */
  async handleSuccessfulCheck(websiteId, stats, statusCode, responseTime) {
    await this.db.execute(`
      UPDATE website_status_stats 
      SET consecutive_failures = 0,
          total_checks = total_checks + 1,
          success_checks = success_checks + 1,
          current_status_code = ?,
          current_response_time = ?,
          last_success_time = CURRENT_TIMESTAMP,
          notification_sent = 0,
          updated_at = CURRENT_TIMESTAMP
      WHERE website_id = ?
    `, [statusCode, responseTime, websiteId]);

    return { isSuccess: true, shouldNotify: false };
  }

  /**
   * 处理失败的检测
   */
  async handleFailedCheck(websiteId, stats, statusCode, responseTime) {
    const newFailureCount = stats.consecutive_failures + 1;
    
    // 智能判断是否应该增加失败计数
    // 如果是临时性错误（如502, 503），不立即增加计数
    const shouldIncreaseCount = this.shouldIncreaseFailureCount(statusCode, stats);
    
    const actualFailureCount = shouldIncreaseCount ? newFailureCount : stats.consecutive_failures;
    
    await this.db.execute(`
      UPDATE website_status_stats 
      SET consecutive_failures = ?,
          total_checks = total_checks + 1,
          current_status_code = ?,
          current_response_time = ?,
          last_failure_time = CURRENT_TIMESTAMP,
          ${stats.consecutive_failures === 0 ? 'first_failure_time = CURRENT_TIMESTAMP,' : ''}
          updated_at = CURRENT_TIMESTAMP
      WHERE website_id = ?
    `, [actualFailureCount, statusCode, responseTime, websiteId]);

    // 判断是否需要发送通知
    const shouldNotify = actualFailureCount >= 5 && !stats.notification_sent;

    return { 
      isSuccess: false, 
      shouldNotify,
      consecutiveFailures: actualFailureCount
    };
  }

  /**
   * 判断是否应该增加失败计数
   */
  shouldIncreaseFailureCount(statusCode, stats) {
    // 对于某些临时性错误，不立即增加计数
    const temporaryErrors = [502, 503, 504]; // 服务器临时不可用
    
    if (temporaryErrors.includes(statusCode)) {
      // 如果已经连续失败多次，则增加计数
      return stats.consecutive_failures >= 3;
    }
    
    // 其他错误直接增加计数
    return true;
  }

  /**
   * 获取需要通知的异常网站
   */
  async getAbnormalWebsitesForNotification() {
    try {
      const [websites] = await this.db.execute(`
        SELECT 
          ws.website_id,
          w.site_name,
          w.site_url,
          ws.consecutive_failures,
          ws.current_status_code,
          ws.last_failure_time,
          ws.first_failure_time,
          ws.notification_sent,
          ws.last_notification_time
        FROM website_status_stats ws
        JOIN websites w ON ws.website_id = w.id
        WHERE ws.consecutive_failures >= 5 
          AND w.status = 'active'
          AND (
            ws.notification_sent = 0 
            OR ws.last_notification_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
          )
        ORDER BY ws.consecutive_failures DESC, ws.first_failure_time ASC
        LIMIT 50
      `);

      return websites;
    } catch (error) {
      console.error('❌ 获取异常网站列表失败:', error);
      throw error;
    }
  }

  /**
   * 更新通知状态
   */
  async updateNotificationStatus(websiteIds) {
    try {
      if (websiteIds.length === 0) return;

      const placeholders = websiteIds.map(() => '?').join(',');
      await this.db.execute(`
        UPDATE website_status_stats 
        SET notification_sent = 1,
            notification_count = notification_count + 1,
            last_notification_time = CURRENT_TIMESTAMP
        WHERE website_id IN (${placeholders})
      `, websiteIds);

      console.log(`✅ 已更新 ${websiteIds.length} 个网站的通知状态`);
    } catch (error) {
      console.error('❌ 更新通知状态失败:', error);
      throw error;
    }
  }

  /**
   * 清理过期的状态记录
   */
  async cleanupExpiredRecords() {
    try {
      // 删除30天前的无效网站状态记录
      const [result] = await this.db.execute(`
        DELETE ws FROM website_status_stats ws
        LEFT JOIN websites w ON ws.website_id = w.id
        WHERE w.id IS NULL 
           OR (w.status = 'inactive' AND ws.updated_at < DATE_SUB(NOW(), INTERVAL 30 DAY))
      `);

      if (result.affectedRows > 0) {
        console.log(`🧹 已清理 ${result.affectedRows} 条过期的网站状态记录`);
      }

      return result.affectedRows;
    } catch (error) {
      console.error('❌ 清理过期记录失败:', error);
      throw error;
    }
  }

  /**
   * 获取网站状态统计
   */
  async getWebsiteStatusStats() {
    try {
      const [stats] = await this.db.execute(`
        SELECT 
          COUNT(*) as total_websites,
          SUM(CASE WHEN ws.consecutive_failures = 0 THEN 1 ELSE 0 END) as healthy_websites,
          SUM(CASE WHEN ws.consecutive_failures BETWEEN 1 AND 5 THEN 1 ELSE 0 END) as warning_websites,
          SUM(CASE WHEN ws.consecutive_failures > 5 THEN 1 ELSE 0 END) as critical_websites,
          AVG(CASE WHEN ws.consecutive_failures = 0 THEN ws.current_response_time ELSE NULL END) as avg_response_time
        FROM website_status_stats ws
        JOIN websites w ON ws.website_id = w.id
        WHERE w.status = 'active'
      `);

      return stats[0];
    } catch (error) {
      console.error('❌ 获取网站状态统计失败:', error);
      throw error;
    }
  }
}

module.exports = WebsiteStatusOptimizer;