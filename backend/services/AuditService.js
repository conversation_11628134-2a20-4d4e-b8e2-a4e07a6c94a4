/**
 * 审计服务类
 * 功能：
 * 1. 实现权限访问日志记录功能
 * 2. 实现权限变更日志记录功能
 * 3. 实现日志查询和筛选功能
 * 4. 添加日志自动清理和归档机制
 */

class AuditService {
  constructor(database) {
    this.db = database;
    
    // 审计配置
    this.config = {
      retentionDays: 90,        // 日志保留天数
      batchSize: 1000,          // 批量处理大小
      cleanupInterval: 86400000, // 清理间隔（24小时）
      maxLogSize: 10000         // 单条日志最大大小（字符）
    };

    // 启动定期清理
    this.startCleanupTimer();
  }

  /**
   * 记录权限访问日志
   * @param {number} userId - 用户ID
   * @param {string} action - 操作类型
   * @param {string} resource - 资源
   * @param {string} result - 结果 ('granted', 'denied', 'error')
   * @param {Object} details - 详细信息
   * @returns {Promise<boolean>} 记录是否成功
   */
  async logPermissionAccess(userId, action, resource, result, details = {}) {
    try {
      const logEntry = {
        userId,
        action,
        resource,
        result,
        details: this.sanitizeDetails(details),
        ip: details.ip || null,
        userAgent: this.truncateUserAgent(details.userAgent),
        timestamp: new Date()
      };

      await this.writeAuditLog(logEntry);
      return true;

    } catch (error) {
      console.error('记录权限访问日志失败:', error.message);
      return false;
    }
  }

  /**
   * 记录权限变更日志
   * @param {number} adminId - 管理员ID
   * @param {number} targetUserId - 目标用户ID
   * @param {Object} changes - 变更内容
   * @param {Object} context - 上下文信息
   * @returns {Promise<boolean>} 记录是否成功
   */
  async logPermissionChange(adminId, targetUserId, changes, context = {}) {
    try {
      const logEntry = {
        userId: adminId,
        action: 'permission_change',
        resource: 'user_permissions',
        resourceId: targetUserId.toString(),
        result: 'success',
        details: this.sanitizeDetails({
          targetUserId,
          changes,
          context,
          timestamp: new Date().toISOString()
        }),
        ip: context.ip || null,
        userAgent: this.truncateUserAgent(context.userAgent),
        timestamp: new Date()
      };

      await this.writeAuditLog(logEntry);
      return true;

    } catch (error) {
      console.error('记录权限变更日志失败:', error.message);
      return false;
    }
  }

  /**
   * 记录角色变更日志
   * @param {number} adminId - 管理员ID
   * @param {number} targetUserId - 目标用户ID
   * @param {string} oldRole - 旧角色
   * @param {string} newRole - 新角色
   * @param {Object} context - 上下文信息
   * @returns {Promise<boolean>} 记录是否成功
   */
  async logRoleChange(adminId, targetUserId, oldRole, newRole, context = {}) {
    try {
      const logEntry = {
        userId: adminId,
        action: 'role_change',
        resource: 'user_role',
        resourceId: targetUserId.toString(),
        result: 'success',
        details: this.sanitizeDetails({
          targetUserId,
          oldRole,
          newRole,
          context,
          timestamp: new Date().toISOString()
        }),
        ip: context.ip || null,
        userAgent: this.truncateUserAgent(context.userAgent),
        timestamp: new Date()
      };

      await this.writeAuditLog(logEntry);
      return true;

    } catch (error) {
      console.error('记录角色变更日志失败:', error.message);
      return false;
    }
  }

  /**
   * 记录登录日志
   * @param {number} userId - 用户ID
   * @param {string} result - 登录结果 ('success', 'failure')
   * @param {Object} context - 上下文信息
   * @returns {Promise<boolean>} 记录是否成功
   */
  async logLogin(userId, result, context = {}) {
    try {
      const logEntry = {
        userId,
        action: 'login',
        resource: 'authentication',
        result,
        details: this.sanitizeDetails({
          loginMethod: context.loginMethod || 'password',
          failureReason: context.failureReason || null,
          timestamp: new Date().toISOString()
        }),
        ip: context.ip || null,
        userAgent: this.truncateUserAgent(context.userAgent),
        timestamp: new Date()
      };

      await this.writeAuditLog(logEntry);
      return true;

    } catch (error) {
      console.error('记录登录日志失败:', error.message);
      return false;
    }
  }

  /**
   * 记录敏感操作日志
   * @param {number} userId - 用户ID
   * @param {string} action - 操作类型
   * @param {string} resource - 资源
   * @param {string} resourceId - 资源ID
   * @param {Object} details - 详细信息
   * @param {Object} context - 上下文信息
   * @returns {Promise<boolean>} 记录是否成功
   */
  async logSensitiveOperation(userId, action, resource, resourceId, details = {}, context = {}) {
    try {
      const logEntry = {
        userId,
        action,
        resource,
        resourceId,
        result: 'success',
        details: this.sanitizeDetails({
          ...details,
          sensitive: true,
          timestamp: new Date().toISOString()
        }),
        ip: context.ip || null,
        userAgent: this.truncateUserAgent(context.userAgent),
        timestamp: new Date()
      };

      await this.writeAuditLog(logEntry);
      return true;

    } catch (error) {
      console.error('记录敏感操作日志失败:', error.message);
      return false;
    }
  }

  /**
   * 写入审计日志到数据库
   * @param {Object} logEntry - 日志条目
   */
  async writeAuditLog(logEntry) {
    try {
      await this.db.execute(`
        INSERT INTO audit_logs 
        (user_id, action, resource, resource_id, result, details, ip_address, user_agent, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        logEntry.userId,
        logEntry.action,
        logEntry.resource,
        logEntry.resourceId || null,
        logEntry.result,
        JSON.stringify(logEntry.details),
        logEntry.ip,
        logEntry.userAgent,
        logEntry.timestamp
      ]);

    } catch (error) {
      console.error('写入审计日志失败:', error.message);
      throw error;
    }
  }

  /**
   * 查询审计日志
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Object>} 查询结果
   */
  async getAuditLogs(filters = {}) {
    try {
      const {
        userId = null,
        action = null,
        resource = null,
        result = null,
        startDate = null,
        endDate = null,
        page = 1,
        limit = 50,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = filters;

      // 构建WHERE条件
      const whereConditions = [];
      const params = [];

      if (userId) {
        whereConditions.push('al.user_id = ?');
        params.push(userId);
      }

      if (action) {
        whereConditions.push('al.action = ?');
        params.push(action);
      }

      if (resource) {
        whereConditions.push('al.resource = ?');
        params.push(resource);
      }

      if (result) {
        whereConditions.push('al.result = ?');
        params.push(result);
      }

      if (startDate) {
        whereConditions.push('al.created_at >= ?');
        params.push(startDate);
      }

      if (endDate) {
        whereConditions.push('al.created_at <= ?');
        params.push(endDate);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 查询总数
      const [countResult] = await this.db.execute(`
        SELECT COUNT(*) as total
        FROM audit_logs al
        ${whereClause}
      `, params);

      const total = countResult[0].total;

      // 查询日志数据
      const offset = (page - 1) * limit;
      const [logs] = await this.db.execute(`
        SELECT 
          al.*,
          u.username,
          u.role as user_role
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        ${whereClause}
        ORDER BY al.${sortBy} ${sortOrder}
        LIMIT ? OFFSET ?
      `, [...params, parseInt(limit), parseInt(offset)]);

      // 解析详细信息
      const processedLogs = logs.map(log => ({
        ...log,
        details: this.parseLogDetails(log.details)
      }));

      return {
        logs: processedLogs,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      console.error('查询审计日志失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取审计统计信息
   * @param {Object} filters - 筛选条件
   * @returns {Promise<Object>} 统计信息
   */
  async getAuditStatistics(filters = {}) {
    try {
      const {
        startDate = null,
        endDate = null,
        userId = null
      } = filters;

      const whereConditions = [];
      const params = [];

      if (startDate) {
        whereConditions.push('created_at >= ?');
        params.push(startDate);
      }

      if (endDate) {
        whereConditions.push('created_at <= ?');
        params.push(endDate);
      }

      if (userId) {
        whereConditions.push('user_id = ?');
        params.push(userId);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 按操作类型统计
      const [actionStats] = await this.db.execute(`
        SELECT 
          action,
          COUNT(*) as count,
          COUNT(CASE WHEN result = 'granted' THEN 1 END) as granted,
          COUNT(CASE WHEN result = 'denied' THEN 1 END) as denied,
          COUNT(CASE WHEN result = 'error' THEN 1 END) as errors
        FROM audit_logs
        ${whereClause}
        GROUP BY action
        ORDER BY count DESC
      `, params);

      // 按结果统计
      const [resultStats] = await this.db.execute(`
        SELECT 
          result,
          COUNT(*) as count
        FROM audit_logs
        ${whereClause}
        GROUP BY result
      `, params);

      // 按用户统计（前10名）
      const [userStats] = await this.db.execute(`
        SELECT 
          al.user_id,
          u.username,
          u.role,
          COUNT(*) as count
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        ${whereClause}
        GROUP BY al.user_id, u.username, u.role
        ORDER BY count DESC
        LIMIT 10
      `, params);

      // 按时间统计（最近7天）
      const [timeStats] = await this.db.execute(`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as count
        FROM audit_logs
        ${whereClause}
        AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `, params);

      return {
        actionStatistics: actionStats,
        resultStatistics: resultStats,
        userStatistics: userStats,
        timeStatistics: timeStats
      };

    } catch (error) {
      console.error('获取审计统计信息失败:', error.message);
      throw error;
    }
  }

  /**
   * 检测异常访问模式
   * @param {Object} options - 检测选项
   * @returns {Promise<Array>} 异常访问列表
   */
  async detectAnomalousAccess(options = {}) {
    try {
      const {
        timeWindow = 3600,    // 时间窗口（秒）
        maxFailures = 10,     // 最大失败次数
        maxRequests = 1000    // 最大请求次数
      } = options;

      const anomalies = [];

      // 检测频繁失败的用户
      const [frequentFailures] = await this.db.execute(`
        SELECT 
          user_id,
          u.username,
          COUNT(*) as failure_count,
          MAX(created_at) as last_failure
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE al.result = 'denied' 
          AND al.created_at >= DATE_SUB(NOW(), INTERVAL ? SECOND)
        GROUP BY user_id, u.username
        HAVING failure_count >= ?
        ORDER BY failure_count DESC
      `, [timeWindow, maxFailures]);

      frequentFailures.forEach(failure => {
        anomalies.push({
          type: 'frequent_failures',
          userId: failure.user_id,
          username: failure.username,
          count: failure.failure_count,
          lastOccurrence: failure.last_failure,
          severity: failure.failure_count > maxFailures * 2 ? 'high' : 'medium'
        });
      });

      // 检测异常高频访问
      const [highFrequencyAccess] = await this.db.execute(`
        SELECT 
          user_id,
          u.username,
          COUNT(*) as request_count,
          MAX(created_at) as last_request
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE al.created_at >= DATE_SUB(NOW(), INTERVAL ? SECOND)
        GROUP BY user_id, u.username
        HAVING request_count >= ?
        ORDER BY request_count DESC
      `, [timeWindow, maxRequests]);

      highFrequencyAccess.forEach(access => {
        anomalies.push({
          type: 'high_frequency_access',
          userId: access.user_id,
          username: access.username,
          count: access.request_count,
          lastOccurrence: access.last_request,
          severity: access.request_count > maxRequests * 2 ? 'high' : 'medium'
        });
      });

      // 检测异常IP访问
      const [suspiciousIPs] = await this.db.execute(`
        SELECT 
          ip_address,
          COUNT(DISTINCT user_id) as user_count,
          COUNT(*) as request_count,
          COUNT(CASE WHEN result = 'denied' THEN 1 END) as denied_count
        FROM audit_logs
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? SECOND)
          AND ip_address IS NOT NULL
        GROUP BY ip_address
        HAVING user_count >= 5 OR denied_count >= ?
        ORDER BY denied_count DESC, user_count DESC
      `, [timeWindow, maxFailures]);

      suspiciousIPs.forEach(ip => {
        anomalies.push({
          type: 'suspicious_ip',
          ipAddress: ip.ip_address,
          userCount: ip.user_count,
          requestCount: ip.request_count,
          deniedCount: ip.denied_count,
          severity: ip.denied_count > maxFailures * 2 ? 'high' : 'medium'
        });
      });

      return anomalies;

    } catch (error) {
      console.error('检测异常访问模式失败:', error.message);
      throw error;
    }
  }

  /**
   * 清理过期日志
   * @returns {Promise<number>} 清理的日志数量
   */
  async cleanupExpiredLogs() {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.config.retentionDays);

      const [result] = await this.db.execute(
        'DELETE FROM audit_logs WHERE created_at < ?',
        [cutoffDate]
      );

      const deletedCount = result.affectedRows;
      
      if (deletedCount > 0) {
        console.log(`清理了 ${deletedCount} 条过期审计日志`);
      }

      return deletedCount;

    } catch (error) {
      console.error('清理过期日志失败:', error.message);
      throw error;
    }
  }

  /**
   * 导出审计日志
   * @param {Object} filters - 筛选条件
   * @param {string} format - 导出格式 ('json', 'csv')
   * @returns {Promise<string>} 导出数据
   */
  async exportAuditLogs(filters = {}, format = 'json') {
    try {
      const { logs } = await this.getAuditLogs({ ...filters, limit: 10000 });

      if (format === 'csv') {
        return this.convertToCSV(logs);
      } else {
        return JSON.stringify(logs, null, 2);
      }

    } catch (error) {
      console.error('导出审计日志失败:', error.message);
      throw error;
    }
  }

  /**
   * 转换为CSV格式
   * @param {Array} logs - 日志数组
   * @returns {string} CSV字符串
   */
  convertToCSV(logs) {
    if (logs.length === 0) {
      return '';
    }

    const headers = ['id', 'user_id', 'username', 'action', 'resource', 'result', 'ip_address', 'created_at'];
    const csvRows = [headers.join(',')];

    logs.forEach(log => {
      const row = headers.map(header => {
        const value = log[header] || '';
        return `"${value.toString().replace(/"/g, '""')}"`;
      });
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  /**
   * 清理和验证详细信息
   * @param {Object} details - 详细信息
   * @returns {Object} 清理后的详细信息
   */
  sanitizeDetails(details) {
    try {
      // 移除敏感信息
      const sanitized = { ...details };
      delete sanitized.password;
      delete sanitized.token;
      delete sanitized.secret;

      // 限制大小
      const jsonString = JSON.stringify(sanitized);
      if (jsonString.length > this.config.maxLogSize) {
        return { truncated: true, originalSize: jsonString.length };
      }

      return sanitized;

    } catch (error) {
      return { error: 'Failed to sanitize details' };
    }
  }

  /**
   * 截断用户代理字符串
   * @param {string} userAgent - 用户代理
   * @returns {string} 截断后的用户代理
   */
  truncateUserAgent(userAgent) {
    if (!userAgent) return null;
    return userAgent.length > 500 ? userAgent.substring(0, 500) + '...' : userAgent;
  }

  /**
   * 解析日志详细信息
   * @param {string} details - 详细信息JSON字符串
   * @returns {Object} 解析后的对象
   */
  parseLogDetails(details) {
    try {
      return typeof details === 'string' ? JSON.parse(details) : details;
    } catch (error) {
      return { parseError: true, raw: details };
    }
  }

  /**
   * 启动定期清理定时器
   */
  startCleanupTimer() {
    setInterval(async () => {
      try {
        await this.cleanupExpiredLogs();
      } catch (error) {
        console.error('定期清理审计日志失败:', error.message);
      }
    }, this.config.cleanupInterval);

    console.log('审计日志定期清理定时器已启动');
  }

  /**
   * 更新配置
   * @param {Object} newConfig - 新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('审计服务配置已更新:', this.config);
  }

  /**
   * 获取配置
   * @returns {Object} 当前配置
   */
  getConfig() {
    return { ...this.config };
  }
}

module.exports = AuditService;