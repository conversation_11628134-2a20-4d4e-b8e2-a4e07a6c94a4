/**
 * 增强版HTTP监控检查器
 * 基于coolmonitor的HTTP检查器实现，集成到我们的网站管理系统
 * 
 * 主要功能：
 * - 灵活的HTTP状态码配置
 * - 支持多种HTTP方法和自定义请求头
 * - 智能重试机制
 * - SSL证书集成监控
 * - 关键词内容检测
 * - 详细错误分类和处理
 * - 证书到期通知
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');
const tls = require('tls');

// 监控状态常量
const MONITOR_STATUS = {
  DOWN: 0,
  UP: 1,
  PENDING: 2
};

// 错误消息常量
const ERROR_MESSAGES = {
  CONNECTION_REFUSED: '连接被拒绝',
  TIMEOUT: '连接超时',
  HOST_NOT_FOUND: '无法解析主机名',
  INVALID_STATUS: '状态码不符合预期',
  KEYWORD_NOT_FOUND: '未找到关键词',
  NETWORK_ERROR: '网络错误',
  SSL_ERROR: 'SSL证书错误',
  AUTHENTICATION_FAILED: '身份验证失败',
  UNKNOWN_ERROR: '未知错误'
};

/**
 * 检查HTTP状态码是否符合预期
 * 支持多种格式：200-299, 200,201,202, 200
 */
function checkStatusCode(statusCode, expectedStatusCodes = '200-299') {
  const statusParts = expectedStatusCodes.split(',');
  
  for (const part of statusParts) {
    const trimmedPart = part.trim();
    
    // 范围表示法，如 200-299
    if (trimmedPart.includes('-')) {
      const [min, max] = trimmedPart.split('-').map(s => parseInt(s));
      if (statusCode >= min && statusCode <= max) {
        return true;
      }
    } 
    // 单个状态码，如 200
    else if (parseInt(trimmedPart) === statusCode) {
      return true;
    }
  }
  
  return false;
}

/**
 * 获取网络错误的可读消息
 */
function getNetworkErrorMessage(error) {
  const errorMessage = error instanceof Error ? error.message : String(error);
  
  if (errorMessage.includes('ECONNREFUSED')) {
    return ERROR_MESSAGES.CONNECTION_REFUSED;
  } else if (errorMessage.includes('ETIMEDOUT') || errorMessage.includes('timeout')) {
    return ERROR_MESSAGES.TIMEOUT;
  } else if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('getaddrinfo')) {
    return ERROR_MESSAGES.HOST_NOT_FOUND;
  } else if (errorMessage.includes('certificate') || errorMessage.includes('SSL')) {
    return ERROR_MESSAGES.SSL_ERROR;
  } else {
    return `${ERROR_MESSAGES.NETWORK_ERROR}: ${errorMessage}`;
  }
}

/**
 * 增强版HTTP检查器类
 */
class EnhancedHttpChecker {
  constructor() {
    this.defaultConfig = {
      httpMethod: 'GET',
      statusCodes: '200-299',
      maxRedirects: 10,
      connectTimeout: 10, // 秒
      retries: 0,
      retryInterval: 60, // 秒
      ignoreTls: false,
      notifyCertExpiry: false
    };
  }

  /**
   * HTTP监控检查（单次执行，不包含重试逻辑）
   */
  async checkHttpSingle(config) {
    const {
      url,
      httpMethod = this.defaultConfig.httpMethod,
      statusCodes = this.defaultConfig.statusCodes,
      maxRedirects = this.defaultConfig.maxRedirects,
      requestBody = '',
      requestHeaders = '',
      connectTimeout = this.defaultConfig.connectTimeout,
      ignoreTls = this.defaultConfig.ignoreTls,
      notifyCertExpiry = this.defaultConfig.notifyCertExpiry,
      monitorId = '',
      monitorName = ''
    } = config;

    if (!url) {
      return {
        status: MONITOR_STATUS.DOWN,
        message: 'URL不能为空',
        ping: 0
      };
    }

    const startTime = Date.now();

    try {
      // 执行HTTP请求检查（简化版，先不包含证书检查）
      const httpResult = await this.performHttpRequest({
        url,
        httpMethod,
        requestBody,
        requestHeaders,
        connectTimeout,
        maxRedirects,
        ignoreTls
      });

      const responseTime = Date.now() - startTime;

      // 检查状态码是否符合预期
      const isStatusValid = checkStatusCode(httpResult.statusCode, statusCodes);

      if (isStatusValid) {
        return {
          status: MONITOR_STATUS.UP,
          message: `状态码: ${httpResult.statusCode}`,
          ping: responseTime
        };
      } else {
        return {
          status: MONITOR_STATUS.DOWN,
          message: `状态码不符合预期: ${httpResult.statusCode}`,
          ping: responseTime
        };
      }
    } catch (error) {
      const errorMessage = getNetworkErrorMessage(error);
      const actualTime = Date.now() - startTime;

      return {
        status: MONITOR_STATUS.DOWN,
        message: errorMessage,
        ping: actualTime
      };
    }
  }

  /**
   * HTTP监控检查（包含重试逻辑）
   */
  async checkHttp(config) {
    const { retries = 0, retryInterval = 60 } = config;

    // 执行首次检查
    const result = await this.checkHttpSingle(config);

    // 如果首次检查成功，直接返回
    if (result.status === MONITOR_STATUS.UP) {
      return result;
    }

    // 如果配置了重试次数且首次检查失败，进行重试
    if (retries > 0) {
      for (let i = 0; i < retries; i++) {
        // 等待重试间隔时间（秒）
        await new Promise(resolve => setTimeout(resolve, retryInterval * 1000));

        // 执行重试检查
        const retryResult = await this.checkHttpSingle(config);

        if (retryResult.status === MONITOR_STATUS.UP) {
          return {
            ...retryResult,
            message: `重试成功 (${i + 1}/${retries}): ${retryResult.message}`
          };
        }
      }

      return {
        ...result,
        message: `重试${retries}次后仍然失败: ${result.message}`
      };
    }

    return result;
  }

  /**
   * 关键词监控检查（单次执行，不包含重试逻辑）
   */
  async checkKeywordSingle(config) {
    const {
      url,
      keyword = '',
      httpMethod = this.defaultConfig.httpMethod,
      statusCodes = this.defaultConfig.statusCodes,
      maxRedirects = this.defaultConfig.maxRedirects,
      requestBody = '',
      requestHeaders = '',
      connectTimeout = this.defaultConfig.connectTimeout,
      ignoreTls = this.defaultConfig.ignoreTls
    } = config;

    if (!url) {
      return {
        status: MONITOR_STATUS.DOWN,
        message: 'URL不能为空',
        ping: 0
      };
    }

    if (!keyword) {
      return {
        status: MONITOR_STATUS.DOWN,
        message: '关键词不能为空',
        ping: 0
      };
    }

    const startTime = Date.now();

    try {
      // 执行HTTP请求检查
      const httpResult = await this.performHttpRequest({
        url,
        httpMethod,
        requestBody,
        requestHeaders,
        connectTimeout,
        maxRedirects,
        ignoreTls,
        needResponseBody: true // 关键词检查需要响应体
      });

      const responseTime = Date.now() - startTime;

      // 检查状态码是否符合预期
      const isStatusValid = checkStatusCode(httpResult.statusCode, statusCodes);

      if (!isStatusValid) {
        return {
          status: MONITOR_STATUS.DOWN,
          message: `状态码不符合预期: ${httpResult.statusCode}`,
          ping: responseTime
        };
      }

      // 检查响应内容中是否包含关键词
      const responseText = httpResult.responseBody || '';

      // 支持多个关键词，用英文逗号分隔，每个关键词之间是"或"的关系
      const keywords = keyword.split(',').map(k => k.trim()).filter(k => k.length > 0);
      let keywordFound = false;
      let foundKeyword = '';

      for (const kw of keywords) {
        if (responseText.includes(kw)) {
          keywordFound = true;
          foundKeyword = kw;
          break;
        }
      }

      if (keywordFound) {
        const keywordInfo = keywords.length > 1 ? ` (匹配到: ${foundKeyword})` : '';
        return {
          status: MONITOR_STATUS.UP,
          message: `找到关键词${keywordInfo}，状态码: ${httpResult.statusCode}`,
          ping: responseTime
        };
      } else {
        const keywordInfo = keywords.length > 1 ? ` (检查了 ${keywords.length} 个关键词)` : '';
        return {
          status: MONITOR_STATUS.DOWN,
          message: `${ERROR_MESSAGES.KEYWORD_NOT_FOUND}${keywordInfo}`,
          ping: responseTime
        };
      }
    } catch (error) {
      const errorMessage = getNetworkErrorMessage(error);
      return {
        status: MONITOR_STATUS.DOWN,
        message: errorMessage,
        ping: Date.now() - startTime
      };
    }
  }

  /**
   * 关键词监控检查（包含重试逻辑）
   */
  async checkKeyword(config) {
    const { retries = 0, retryInterval = 60 } = config;

    // 执行首次检查
    const result = await this.checkKeywordSingle(config);

    // 如果首次检查成功，直接返回
    if (result.status === MONITOR_STATUS.UP) {
      return result;
    }

    // 如果配置了重试次数且首次检查失败，进行重试
    if (retries > 0) {
      for (let i = 0; i < retries; i++) {
        // 等待重试间隔时间（秒）
        await new Promise(resolve => setTimeout(resolve, retryInterval * 1000));

        // 执行重试检查
        const retryResult = await this.checkKeywordSingle(config);

        if (retryResult.status === MONITOR_STATUS.UP) {
          return {
            ...retryResult,
            message: `重试成功 (${i + 1}/${retries}): ${retryResult.message}`
          };
        }
      }

      return {
        ...result,
        message: `重试${retries}次后仍然失败: ${result.message}`
      };
    }

    return result;
  }

  /**
   * 执行HTTP请求（简化版）
   */
  async performHttpRequest(options) {
    const {
      url,
      httpMethod = 'GET',
      connectTimeout = 10,
      ignoreTls = false,
      needResponseBody = false
    } = options;

    return new Promise((resolve, reject) => {
      try {
        const urlObj = new URL(url);
        const client = urlObj.protocol === 'https:' ? https : http;

        const requestOptions = {
          hostname: urlObj.hostname,
          port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
          path: urlObj.pathname + urlObj.search,
          method: httpMethod,
          timeout: connectTimeout * 1000,
          headers: {
            'User-Agent': 'SiteManager-EnhancedChecker/2.0',
            'Accept': '*/*',
            'Connection': 'close'
          }
        };

        if (urlObj.protocol === 'https:') {
          requestOptions.rejectUnauthorized = !ignoreTls;
        }

        const req = client.request(requestOptions, (res) => {
          let responseBody = '';

          // 必须消费响应数据才能触发end事件
          res.on('data', (chunk) => {
            if (needResponseBody) {
              responseBody += chunk.toString();
            }
            // 如果不需要响应体，也要消费数据
          });

          res.on('end', () => {
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              responseBody: needResponseBody ? responseBody : null
            });
          });
        });

        req.on('error', (error) => {
          reject(new Error(`网络错误: ${error.message}`));
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new Error('连接超时'));
        });

        req.end();
      } catch (error) {
        reject(new Error(`请求失败: ${error.message}`));
      }
    });
  }

  /**
   * HTTPS证书监控检查
   */
  async checkHttpsCertificate(config) {
    const {
      url,
      connectTimeout = 10,
      monitorId,
      monitorName
    } = config;

    if (!url) {
      return {
        status: MONITOR_STATUS.DOWN,
        message: 'URL不能为空',
        ping: 0
      };
    }

    const startTime = Date.now();

    try {
      // 检查URL是否是HTTPS
      if (!url.startsWith('https://')) {
        return {
          status: MONITOR_STATUS.DOWN,
          message: '仅支持HTTPS URL (必须以https://开头)',
          ping: 0
        };
      }

      // 从URL中提取主机名和端口
      const urlObj = new URL(url);
      const hostname = urlObj.hostname;
      const port = urlObj.port || '443';

      const certInfo = await this.getSSLCertificateInfo(hostname, parseInt(port), connectTimeout);

      if (!certInfo.success) {
        return {
          status: MONITOR_STATUS.DOWN,
          message: `证书检查失败: ${certInfo.error}`,
          ping: Date.now() - startTime
        };
      }

      const daysRemaining = certInfo.daysRemaining;
      let certificateWarning = '';

      // 如果证书将在7天内过期，发出提醒但保持状态为UP
      if (daysRemaining <= 7 && daysRemaining > 0) {
        certificateWarning = `【警告】证书将在${daysRemaining}天后过期，请及时更新`;
      }

      // 检查证书是否有效
      if (!certInfo.isValid || daysRemaining <= 0) {
        return {
          status: MONITOR_STATUS.DOWN,
          message: daysRemaining <= 0 ? '证书已过期' : '证书无效',
          ping: Date.now() - startTime
        };
      }

      // 构建返回消息，包含证书剩余天数信息
      let message = `HTTPS证书有效`;

      // 添加剩余天数信息
      if (daysRemaining > 0) {
        message += ` (剩余${daysRemaining}天)`;

        // 如果有警告，再另外添加警告信息
        if (certificateWarning) {
          message += `. ${certificateWarning}`;
        }
      }

      return {
        status: MONITOR_STATUS.UP,
        message: message,
        ping: Date.now() - startTime,
        certificateDaysRemaining: daysRemaining
      };
    } catch (error) {
      const errorMessage = getNetworkErrorMessage(error);
      return {
        status: MONITOR_STATUS.DOWN,
        message: errorMessage,
        ping: Date.now() - startTime
      };
    }
  }

  /**
   * 获取SSL证书信息
   */
  async getSSLCertificateInfo(hostname, port = 443, timeout = 10) {
    return new Promise((resolve) => {
      const options = {
        host: hostname,
        port: port,
        servername: hostname,
        rejectUnauthorized: false
      };

      const socket = tls.connect(options, () => {
        try {
          const cert = socket.getPeerCertificate();
          const now = new Date();
          const expiry = new Date(cert.valid_to);
          const daysUntilExpiry = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24));

          resolve({
            success: true,
            issuer: cert.issuer?.CN || 'Unknown',
            subject: cert.subject?.CN || hostname,
            validFrom: cert.valid_from,
            validTo: cert.valid_to,
            daysRemaining: daysUntilExpiry,
            isValid: daysUntilExpiry > 0,
            serialNumber: cert.serialNumber,
            fingerprint: cert.fingerprint
          });
        } catch (error) {
          resolve({
            success: false,
            error: error.message
          });
        } finally {
          socket.end();
        }
      });

      socket.on('error', (error) => {
        resolve({
          success: false,
          error: error.message
        });
      });

      socket.setTimeout(timeout * 1000, () => {
        socket.destroy();
        resolve({
          success: false,
          error: 'SSL检测超时'
        });
      });
    });
  }
}

module.exports = {
  EnhancedHttpChecker,
  MONITOR_STATUS,
  ERROR_MESSAGES,
  checkStatusCode,
  getNetworkErrorMessage
};
