/**
 * 站点存活检测服务
 * 支持1000+站点的高效检测和实时通知
 */

const axios = require('axios');
const https = require('https');
const EventEmitter = require('events');

class SiteMonitor extends EventEmitter {
  constructor(db, notificationService) {
    super();
    this.db = db;
    this.notificationService = notificationService;
    this.isRunning = false;
    this.activeChecks = new Set();
    
    // 配置参数
    this.config = {
      checkInterval: 5 * 60 * 1000,    // 5分钟检测一次
      batchSize: 50,                   // 每批检测50个站点
      concurrency: 10,                 // 每批内并发10个请求
      batchDelay: 2000,               // 批次间延迟2秒
      timeout: 15000,                 // 请求超时15秒
      retryAttempts: 2,               // 失败重试2次
      retryDelay: 3000,               // 重试延迟3秒
      failureThreshold: 3,            // 连续失败3次才算故障
      recoveryThreshold: 2            // 连续成功2次才算恢复
    };
    
    // HTTP客户端
    this.httpClient = axios.create({
      timeout: this.config.timeout,
      maxRedirects: 5,
      validateStatus: (status) => status < 500,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false,
        timeout: this.config.timeout
      }),
      headers: {
        'User-Agent': 'SiteMonitor/1.0 (Website Health Check)'
      }
    });
    
    this.stats = {
      totalChecks: 0,
      successfulChecks: 0,
      failedChecks: 0,
      notificationsSent: 0,
      lastRunTime: null,
      averageResponseTime: 0
    };
  }

  /**
   * 启动监控服务
   */
  async start() {
    if (this.isRunning) {
      console.log('⚠️  监控服务已在运行中');
      return;
    }

    console.log('🚀 启动站点存活监控服务...');
    this.isRunning = true;
    
    // 立即执行一次检测
    await this.runCheckCycle();
    
    // 设置定时检测
    this.scheduleNextCheck();
    
    console.log(`✅ 监控服务启动成功，检测间隔: ${this.config.checkInterval / 1000}秒`);
  }

  /**
   * 停止监控服务
   */
  async stop() {
    console.log('🛑 停止监控服务...');
    this.isRunning = false;
    
    if (this.checkTimer) {
      clearTimeout(this.checkTimer);
    }
    
    // 等待当前检测完成
    while (this.activeChecks.size > 0) {
      await this.sleep(100);
    }
    
    console.log('✅ 监控服务已停止');
  }

  /**
   * 调度下次检测
   */
  scheduleNextCheck() {
    if (!this.isRunning) return;
    
    this.checkTimer = setTimeout(async () => {
      await this.runCheckCycle();
      this.scheduleNextCheck();
    }, this.config.checkInterval);
  }

  /**
   * 执行检测周期
   */
  async runCheckCycle() {
    const startTime = Date.now();
    console.log(`🔍 开始新的检测周期 - ${new Date().toLocaleString()}`);
    
    try {
      // 获取所有活跃站点
      const sites = await this.getActiveSites();
      console.log(`📊 获取到 ${sites.length} 个活跃站点`);
      
      if (sites.length === 0) {
        console.log('ℹ️  没有需要检测的站点');
        return;
      }
      
      // 分批检测
      await this.processSitesInBatches(sites);
      
      const duration = Date.now() - startTime;
      this.stats.lastRunTime = new Date();
      
      console.log(`✅ 检测周期完成，耗时: ${duration}ms`);
      this.printStats();
      
    } catch (error) {
      console.error('❌ 检测周期执行失败:', error);
    }
  }

  /**
   * 分批处理站点
   */
  async processSitesInBatches(sites) {
    const batches = this.createBatches(sites, this.config.batchSize);
    
    for (let i = 0; i < batches.length; i++) {
      if (!this.isRunning) break;
      
      const batch = batches[i];
      console.log(`🔄 处理第 ${i + 1}/${batches.length} 批，包含 ${batch.length} 个站点`);
      
      await this.processBatch(batch);
      
      // 批次间延迟，避免服务器过载
      if (i < batches.length - 1) {
        await this.sleep(this.config.batchDelay);
      }
    }
  }

  /**
   * 处理单个批次
   */
  async processBatch(batch) {
    const chunks = this.createBatches(batch, this.config.concurrency);
    
    for (const chunk of chunks) {
      if (!this.isRunning) break;
      
      const promises = chunk.map(site => this.checkSiteWithRetry(site));
      await Promise.allSettled(promises);
      
      // 并发块间小延迟
      await this.sleep(500);
    }
  }

  /**
   * 带重试的站点检测
   */
  async checkSiteWithRetry(site) {
    const siteId = `${site.id}`;
    this.activeChecks.add(siteId);
    
    let lastError = null;
    let result = null;
    
    for (let attempt = 1; attempt <= this.config.retryAttempts + 1; attempt++) {
      try {
        result = await this.checkSite(site);
        if (result.success) {
          break; // 成功则跳出重试循环
        }
        lastError = result.error;
      } catch (error) {
        lastError = error.message;
        result = {
          success: false,
          error: error.message,
          statusCode: 0,
          responseTime: this.config.timeout
        };
      }
      
      // 如果不是最后一次尝试，则等待后重试
      if (attempt <= this.config.retryAttempts) {
        console.log(`⚠️  ${site.domain} 第${attempt}次检测失败，${this.config.retryDelay}ms后重试`);
        await this.sleep(this.config.retryDelay);
      }
    }
    
    // 保存检测结果
    await this.saveCheckResult(site, result);
    
    // 检查状态变化并发送通知
    await this.handleStatusChange(site, result);
    
    this.activeChecks.delete(siteId);
  }

  /**
   * 检测单个站点
   */
  async checkSite(site) {
    const startTime = Date.now();
    const url = site.siteUrl || `https://${site.domain}`;
    
    try {
      const response = await this.httpClient.get(url);
      const responseTime = Date.now() - startTime;
      
      this.stats.totalChecks++;
      this.stats.successfulChecks++;
      this.updateAverageResponseTime(responseTime);
      
      return {
        success: true,
        statusCode: response.status,
        responseTime,
        headers: response.headers,
        url: response.request.res.responseUrl || url
      };
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      this.stats.totalChecks++;
      this.stats.failedChecks++;
      
      return {
        success: false,
        error: error.message,
        statusCode: error.response?.status || 0,
        responseTime,
        code: error.code
      };
    }
  }

  /**
   * 保存检测结果
   */
  async saveCheckResult(site, result) {
    const now = new Date();
    
    try {
      // 更新网站表的检测信息
      await this.db.execute(`
        UPDATE websites 
        SET 
          access_status_code = ?,
          access_status = ?,
          response_time = ?,
          last_check_time = ?,
          consecutive_failures = CASE 
            WHEN ? THEN 0 
            ELSE consecutive_failures + 1 
          END,
          last_failure_time = CASE 
            WHEN ? THEN last_failure_time 
            ELSE ? 
          END
        WHERE id = ?
      `, [
        result.statusCode,
        result.success ? 'online' : 'error',
        result.responseTime,
        now,
        result.success,
        result.success,
        now,
        site.id
      ]);
      
      // 插入检测历史记录
      await this.db.execute(`
        INSERT INTO site_check_history 
        (website_id, check_time, status_code, response_time, success, error_message)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        site.id,
        now,
        result.statusCode,
        result.responseTime,
        result.success,
        result.success ? null : result.error
      ]);
      
    } catch (error) {
      console.error(`保存检测结果失败 ${site.domain}:`, error);
    }
  }

  /**
   * 处理状态变化
   */
  async handleStatusChange(site, result) {
    try {
      // 获取当前站点状态
      const [rows] = await this.db.execute(`
        SELECT consecutive_failures, notification_sent, access_status
        FROM websites 
        WHERE id = ?
      `, [site.id]);
      
      if (rows.length === 0) return;
      
      const currentSite = rows[0];
      const wasOnline = currentSite.access_status === 'online';
      const isOnline = result.success;
      
      // 检测到故障
      if (wasOnline && !isOnline && currentSite.consecutive_failures >= this.config.failureThreshold) {
        if (!currentSite.notification_sent) {
          await this.sendFailureNotification(site, result);
          await this.markNotificationSent(site.id, true);
        }
      }
      
      // 检测到恢复
      if (!wasOnline && isOnline) {
        if (currentSite.notification_sent) {
          await this.sendRecoveryNotification(site, result);
          await this.markNotificationSent(site.id, false);
        }
      }
      
    } catch (error) {
      console.error(`处理状态变化失败 ${site.domain}:`, error);
    }
  }

  /**
   * 发送故障通知
   */
  async sendFailureNotification(site, result) {
    const message = {
      type: 'site_down',
      site: {
        id: site.id,
        name: site.siteName || site.domain,
        domain: site.domain,
        url: site.siteUrl
      },
      error: {
        message: result.error,
        statusCode: result.statusCode,
        responseTime: result.responseTime
      },
      timestamp: new Date()
    };
    
    console.log(`🚨 站点故障: ${site.domain} - ${result.error}`);
    
    if (this.notificationService) {
      await this.notificationService.sendNotification(message);
      this.stats.notificationsSent++;
    }
    
    this.emit('site_down', message);
  }

  /**
   * 发送恢复通知
   */
  async sendRecoveryNotification(site, result) {
    const message = {
      type: 'site_up',
      site: {
        id: site.id,
        name: site.siteName || site.domain,
        domain: site.domain,
        url: site.siteUrl
      },
      recovery: {
        statusCode: result.statusCode,
        responseTime: result.responseTime
      },
      timestamp: new Date()
    };
    
    console.log(`✅ 站点恢复: ${site.domain}`);
    
    if (this.notificationService) {
      await this.notificationService.sendNotification(message);
      this.stats.notificationsSent++;
    }
    
    this.emit('site_up', message);
  }

  /**
   * 获取活跃站点
   */
  async getActiveSites() {
    const [rows] = await this.db.execute(`
      SELECT id, site_id, site_name as siteName, domain, site_url as siteUrl,
             access_status, consecutive_failures, notification_sent
      FROM websites 
      WHERE status = 'active'
      ORDER BY last_check_time ASC, id ASC
    `);
    
    return rows;
  }

  /**
   * 工具方法
   */
  createBatches(array, size) {
    const batches = [];
    for (let i = 0; i < array.length; i += size) {
      batches.push(array.slice(i, i + size));
    }
    return batches;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async markNotificationSent(siteId, sent) {
    await this.db.execute(`
      UPDATE websites 
      SET notification_sent = ?, last_notification_time = ?
      WHERE id = ?
    `, [sent, new Date(), siteId]);
  }

  updateAverageResponseTime(responseTime) {
    if (this.stats.successfulChecks === 1) {
      this.stats.averageResponseTime = responseTime;
    } else {
      this.stats.averageResponseTime = 
        (this.stats.averageResponseTime * (this.stats.successfulChecks - 1) + responseTime) / 
        this.stats.successfulChecks;
    }
  }

  printStats() {
    console.log('📈 监控统计:');
    console.log(`   总检测次数: ${this.stats.totalChecks}`);
    console.log(`   成功: ${this.stats.successfulChecks}, 失败: ${this.stats.failedChecks}`);
    console.log(`   成功率: ${((this.stats.successfulChecks / this.stats.totalChecks) * 100).toFixed(2)}%`);
    console.log(`   平均响应时间: ${Math.round(this.stats.averageResponseTime)}ms`);
    console.log(`   发送通知: ${this.stats.notificationsSent} 次`);
  }

  getStats() {
    return { ...this.stats };
  }
}

module.exports = SiteMonitor;
