/**
 * 增强版网站监控服务
 * 集成coolmonitor的监控功能到我们的网站管理系统
 * 
 * 主要功能：
 * - 增强的HTTP/HTTPS监控
 * - SSL证书监控和到期提醒
 * - 关键词内容检测
 * - 灵活的监控配置
 * - 智能重试机制
 * - 详细的监控历史记录
 */

const { EnhancedHttpChe<PERSON>, MONITOR_STATUS } = require('./enhanced-http-checker');
const mysql = require('mysql2/promise');

class EnhancedWebsiteMonitor {
  constructor() {
    this.httpChecker = new EnhancedHttpChecker();
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'sitemanager',
      password: process.env.DB_PASSWORD || 'sitemanager123',
      database: process.env.DB_NAME || 'sitemanager',
      charset: 'utf8mb4'
    };
  }

  /**
   * 获取数据库连接
   */
  async getDbConnection() {
    return await mysql.createConnection(this.dbConfig);
  }

  /**
   * 检查单个网站的完整监控
   */
  async checkWebsite(websiteId, config = {}) {
    const connection = await this.getDbConnection();
    
    try {
      // 获取网站信息
      const [websites] = await connection.execute(
        'SELECT * FROM websites WHERE id = ?',
        [websiteId]
      );

      if (websites.length === 0) {
        throw new Error(`网站ID ${websiteId} 不存在`);
      }

      const website = websites[0];
      const url = website.siteUrl || `https://${website.domain}`;

      // 获取网站的监控配置
      const monitorConfig = await this.getWebsiteMonitorConfig(websiteId, connection);
      
      // 合并配置
      const finalConfig = {
        url,
        monitorId: websiteId.toString(),
        monitorName: website.site_name,
        ...monitorConfig,
        ...config
      };

      console.log(`🔍 开始检查网站: ${website.site_name} (${url})`);

      // 执行不同类型的检查
      const results = {};

      // 1. HTTP/HTTPS基础检查
      if (finalConfig.enableHttpCheck !== false) {
        console.log(`📡 执行HTTP检查: ${url}`);
        results.http = await this.httpChecker.checkHttp(finalConfig);
      }

      // 2. SSL证书检查（仅HTTPS）
      if (url.startsWith('https://') && finalConfig.enableSslCheck !== false) {
        console.log(`🔒 执行SSL证书检查: ${url}`);
        results.ssl = await this.httpChecker.checkHttpsCertificate(finalConfig);
      }

      // 3. 关键词检查（如果配置了关键词）
      if (finalConfig.keyword && finalConfig.enableKeywordCheck !== false) {
        console.log(`🔍 执行关键词检查: ${finalConfig.keyword}`);
        results.keyword = await this.httpChecker.checkKeyword(finalConfig);
      }

      // 综合检查结果
      const combinedResult = this.combineCheckResults(results);

      // 保存检查结果到数据库
      await this.saveCheckResult(websiteId, combinedResult, connection);

      // 更新网站状态
      await this.updateWebsiteStatus(websiteId, combinedResult, connection);

      console.log(`✅ 网站检查完成: ${website.site_name} - ${combinedResult.status === MONITOR_STATUS.UP ? '正常' : '异常'}`);

      return {
        websiteId,
        siteName: website.site_name,
        url,
        ...combinedResult,
        checkTime: new Date().toISOString()
      };

    } catch (error) {
      console.error(`❌ 检查网站失败 (ID: ${websiteId}):`, error);
      throw error;
    } finally {
      await connection.end();
    }
  }

  /**
   * 批量检查多个网站
   */
  async checkWebsitesBatch(websiteIds, config = {}) {
    const results = [];
    const concurrencyLimit = config.concurrency || 5;
    
    console.log(`🚀 开始批量检查 ${websiteIds.length} 个网站，并发数: ${concurrencyLimit}`);

    // 分批处理
    for (let i = 0; i < websiteIds.length; i += concurrencyLimit) {
      const batch = websiteIds.slice(i, i + concurrencyLimit);
      
      const batchPromises = batch.map(async (websiteId) => {
        try {
          return await this.checkWebsite(websiteId, config);
        } catch (error) {
          return {
            websiteId,
            status: MONITOR_STATUS.DOWN,
            message: error.message,
            ping: 0,
            checkTime: new Date().toISOString()
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // 批次间短暂延迟，避免过载
      if (i + concurrencyLimit < websiteIds.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    console.log(`🎯 批量检查完成，共检查 ${results.length} 个网站`);
    return results;
  }

  /**
   * 获取网站的监控配置
   */
  async getWebsiteMonitorConfig(websiteId, connection) {
    try {
      const [configs] = await connection.execute(
        'SELECT * FROM website_monitor_configs WHERE website_id = ?',
        [websiteId]
      );

      if (configs.length === 0) {
        // 返回默认配置
        return {
          httpMethod: 'GET',
          statusCodes: '200-299,301,302',
          connectTimeout: 10,
          retries: 1,
          retryInterval: 30,
          enableHttpCheck: true,
          enableSslCheck: true,
          enableKeywordCheck: false,
          notifyCertExpiry: true,
          ignoreTls: false
        };
      }

      const config = configs[0];
      return {
        httpMethod: config.http_method || 'GET',
        statusCodes: config.status_codes || '200-299',
        connectTimeout: config.connect_timeout || 10,
        retries: config.retries || 1,
        retryInterval: config.retry_interval || 30,
        enableHttpCheck: config.enable_http_check !== 0,
        enableSslCheck: config.enable_ssl_check !== 0,
        enableKeywordCheck: config.enable_keyword_check !== 0,
        notifyCertExpiry: config.notify_cert_expiry !== 0,
        ignoreTls: config.ignore_tls !== 0,
        keyword: config.keyword || '',
        requestHeaders: config.request_headers || '',
        requestBody: config.request_body || '',
        maxRedirects: config.max_redirects || 10
      };
    } catch (error) {
      console.warn(`获取网站 ${websiteId} 的监控配置失败，使用默认配置:`, error);
      return {
        httpMethod: 'GET',
        statusCodes: '200-299,301,302',
        connectTimeout: 10,
        retries: 1,
        retryInterval: 30,
        enableHttpCheck: true,
        enableSslCheck: true,
        enableKeywordCheck: false,
        notifyCertExpiry: true,
        ignoreTls: false
      };
    }
  }

  /**
   * 综合多个检查结果
   */
  combineCheckResults(results) {
    const { http, ssl, keyword } = results;
    
    // 优先级：关键词检查 > HTTP检查 > SSL检查
    let finalResult = {
      status: MONITOR_STATUS.UP,
      message: '所有检查通过',
      ping: 0,
      details: {}
    };

    // HTTP检查结果
    if (http) {
      finalResult.status = http.status;
      finalResult.message = http.message;
      finalResult.ping = http.ping;
      finalResult.details.http = http;
    }

    // SSL检查结果
    if (ssl) {
      finalResult.details.ssl = ssl;
      
      // 如果HTTP检查通过但SSL检查失败，更新状态
      if (finalResult.status === MONITOR_STATUS.UP && ssl.status === MONITOR_STATUS.DOWN) {
        finalResult.status = ssl.status;
        finalResult.message = `HTTP正常但SSL异常: ${ssl.message}`;
      }
      
      // 如果SSL有证书到期警告，添加到消息中
      if (ssl.message.includes('【警告】') && finalResult.status === MONITOR_STATUS.UP) {
        finalResult.message += ` | ${ssl.message}`;
      }
    }

    // 关键词检查结果（优先级最高）
    if (keyword) {
      finalResult.details.keyword = keyword;
      
      // 关键词检查失败时，覆盖其他检查结果
      if (keyword.status === MONITOR_STATUS.DOWN) {
        finalResult.status = keyword.status;
        finalResult.message = keyword.message;
      }
    }

    return finalResult;
  }

  /**
   * 保存检查结果到数据库
   */
  async saveCheckResult(websiteId, result, connection) {
    try {
      await connection.execute(`
        INSERT INTO website_monitor_history 
        (website_id, status, message, response_time, check_time, details)
        VALUES (?, ?, ?, ?, NOW(), ?)
      `, [
        websiteId,
        result.status,
        result.message,
        result.ping,
        JSON.stringify(result.details || {})
      ]);
    } catch (error) {
      console.error(`保存检查结果失败 (网站ID: ${websiteId}):`, error);
    }
  }

  /**
   * 更新网站状态
   */
  async updateWebsiteStatus(websiteId, result, connection) {
    try {
      // 将状态映射到数据库枚举值
      const statusText = result.status === MONITOR_STATUS.UP ? 'online' : 'offline';
      const statusCode = result.details?.http?.statusCode || null;

      await connection.execute(`
        UPDATE websites
        SET access_status = ?, access_status_code = ?, last_check_time = NOW(), response_time = ?
        WHERE id = ?
      `, [
        statusText,
        statusCode,
        result.ping,
        websiteId
      ]);

      // 如果有SSL信息，更新SSL相关字段
      if (result.details?.ssl) {
        const ssl = result.details.ssl;
        await connection.execute(`
          UPDATE websites 
          SET ssl_status = ?, ssl_expire_date = ?, ssl_days_remaining = ?
          WHERE id = ?
        `, [
          ssl.status === MONITOR_STATUS.UP ? 'valid' : 'invalid',
          ssl.certificateDaysRemaining ? 
            new Date(Date.now() + ssl.certificateDaysRemaining * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : 
            null,
          ssl.certificateDaysRemaining || null,
          websiteId
        ]);
      }
    } catch (error) {
      console.error(`更新网站状态失败 (网站ID: ${websiteId}):`, error);
    }
  }
}

module.exports = EnhancedWebsiteMonitor;
