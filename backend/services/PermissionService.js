/**
 * 权限服务类
 * 功能：
 * 1. 实现用户权限查询和计算逻辑
 * 2. 实现权限继承规则 (角色权限 + 自定义权限)
 * 3. 实现权限冲突解决机制
 * 4. 添加权限验证方法 hasPermission(), hasRole()
 */

const PermissionCacheService = require('./PermissionCacheService');

class PermissionService {
  constructor(database) {
    this.db = database;
    this.cache = new PermissionCacheService();
  }

  /**
   * 获取用户完整权限
   * @param {number} userId - 用户ID
   * @param {boolean} useCache - 是否使用缓存，默认true
   * @returns {Promise<Object>} 用户权限信息
   */
  async getUserPermissions(userId, useCache = true) {
    try {
      // 尝试从缓存获取
      if (useCache) {
        const cachedPermissions = await this.cache.getUserPermissions(userId);
        if (cachedPermissions) {
          return cachedPermissions;
        }
      }

      // 从数据库获取用户信息
      const [users] = await this.db.execute(
        'SELECT id, username, role, status FROM users WHERE id = ?',
        [userId]
      );

      if (users.length === 0) {
        throw new Error(`用户不存在: ${userId}`);
      }

      const user = users[0];

      if (user.status !== 'active') {
        throw new Error(`用户状态异常: ${user.status}`);
      }

      // 计算用户的有效权限
      const effectivePermissions = await this.calculateEffectivePermissions(userId);

      const userPermissions = {
        userId: user.id,
        username: user.username,
        role: user.role,
        status: user.status,
        rolePermissions: effectivePermissions.rolePermissions,
        customPermissions: effectivePermissions.customPermissions,
        effectivePermissions: effectivePermissions.effective,
        deniedPermissions: effectivePermissions.denied,
        lastUpdated: new Date().toISOString()
      };

      // 缓存权限数据
      if (useCache) {
        await this.cache.updateUserPermissions(userId, userPermissions);
      }

      return userPermissions;

    } catch (error) {
      console.error(`获取用户权限失败 (用户ID: ${userId}):`, error.message);
      throw error;
    }
  }

  /**
   * 检查用户是否拥有特定权限
   * @param {number} userId - 用户ID
   * @param {string|string[]} permissionCodes - 权限代码或权限代码数组
   * @param {boolean} requireAll - 是否需要拥有所有权限，默认false
   * @returns {Promise<boolean>} 是否拥有权限
   */
  async hasPermission(userId, permissionCodes, requireAll = false) {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      
      // 超级管理员拥有所有权限
      if (userPermissions.role === 'super_admin') {
        return true;
      }

      const codes = Array.isArray(permissionCodes) ? permissionCodes : [permissionCodes];
      const effectivePermissions = userPermissions.effectivePermissions;

      if (requireAll) {
        // 需要拥有所有权限
        return codes.every(code => effectivePermissions.includes(code));
      } else {
        // 只需要拥有其中一个权限
        return codes.some(code => effectivePermissions.includes(code));
      }

    } catch (error) {
      console.error(`权限检查失败 (用户ID: ${userId}, 权限: ${permissionCodes}):`, error.message);
      return false;
    }
  }

  /**
   * 检查用户是否拥有特定角色
   * @param {number} userId - 用户ID
   * @param {string|string[]} roles - 角色或角色数组
   * @returns {Promise<boolean>} 是否拥有角色
   */
  async hasRole(userId, roles) {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      const userRole = userPermissions.role;
      
      const roleList = Array.isArray(roles) ? roles : [roles];
      return roleList.includes(userRole);

    } catch (error) {
      console.error(`角色检查失败 (用户ID: ${userId}, 角色: ${roles}):`, error.message);
      return false;
    }
  }

  /**
   * 更新用户权限
   * @param {number} userId - 用户ID
   * @param {Object} permissions - 权限配置
   * @param {number} operatorId - 操作者ID
   * @returns {Promise<Object>} 更新结果
   */
  async updateUserPermissions(userId, permissions, operatorId) {
    try {
      const { role, customPermissions = [] } = permissions;
      const updateResult = {
        userId,
        updatedRole: null,
        addedPermissions: [],
        revokedPermissions: [],
        errors: []
      };

      // 开始事务
      await this.db.execute('START TRANSACTION');

      try {
        // 更新用户角色（如果提供）
        if (role) {
          const validRoles = ['super_admin', 'admin', 'user'];
          if (!validRoles.includes(role)) {
            throw new Error(`无效的角色: ${role}`);
          }

          await this.db.execute(
            'UPDATE users SET role = ?, updated_at = NOW() WHERE id = ?',
            [role, userId]
          );
          updateResult.updatedRole = role;
        }

        // 更新自定义权限
        if (customPermissions.length > 0) {
          // 删除现有的自定义权限
          await this.db.execute(
            'DELETE FROM user_custom_permissions WHERE user_id = ?',
            [userId]
          );

          // 添加新的自定义权限
          for (const permission of customPermissions) {
            const { code, granted = true } = permission;

            // 验证权限代码是否存在
            const [permissionExists] = await this.db.execute(
              'SELECT id FROM permissions WHERE code = ?',
              [code]
            );

            if (permissionExists.length === 0) {
              updateResult.errors.push(`权限不存在: ${code}`);
              continue;
            }

            // 插入自定义权限
            await this.db.execute(
              `INSERT INTO user_custom_permissions 
               (user_id, permission_code, granted, created_by, created_at) 
               VALUES (?, ?, ?, ?, NOW())`,
              [userId, code, granted, operatorId]
            );

            if (granted) {
              updateResult.addedPermissions.push(code);
            } else {
              updateResult.revokedPermissions.push(code);
            }
          }
        }

        // 提交事务
        await this.db.execute('COMMIT');

        // 清除用户权限缓存
        await this.cache.clearUserPermissions(userId);

        console.log(`用户权限更新成功 (用户ID: ${userId}):`, updateResult);
        return updateResult;

      } catch (error) {
        // 回滚事务
        await this.db.execute('ROLLBACK');
        throw error;
      }

    } catch (error) {
      console.error(`更新用户权限失败 (用户ID: ${userId}):`, error.message);
      throw error;
    }
  }

  /**
   * 权限继承计算
   * @param {number} userId - 用户ID
   * @returns {Promise<Object>} 有效权限计算结果
   */
  async calculateEffectivePermissions(userId) {
    try {
      // 获取用户基本信息
      const [users] = await this.db.execute(
        'SELECT role FROM users WHERE id = ?',
        [userId]
      );

      if (users.length === 0) {
        throw new Error(`用户不存在: ${userId}`);
      }

      const userRole = users[0].role;
      const result = {
        rolePermissions: [],
        customPermissions: [],
        effective: [],
        denied: []
      };

      // 获取角色权限
      const [rolePermissions] = await this.db.execute(
        'SELECT permission_code FROM role_permissions WHERE role = ?',
        [userRole]
      );

      result.rolePermissions = rolePermissions.map(p => p.permission_code);

      // 获取用户自定义权限
      const [customPermissions] = await this.db.execute(
        'SELECT permission_code, granted FROM user_custom_permissions WHERE user_id = ?',
        [userId]
      );

      result.customPermissions = customPermissions;

      // 计算有效权限
      // 1. 从角色权限开始
      const effectiveSet = new Set(result.rolePermissions);

      // 2. 应用自定义权限
      for (const customPerm of customPermissions) {
        if (customPerm.granted) {
          // 授予权限
          effectiveSet.add(customPerm.permission_code);
        } else {
          // 撤销权限
          effectiveSet.delete(customPerm.permission_code);
          result.denied.push(customPerm.permission_code);
        }
      }

      result.effective = Array.from(effectiveSet);

      return result;

    } catch (error) {
      console.error(`计算有效权限失败 (用户ID: ${userId}):`, error.message);
      throw error;
    }
  }

  /**
   * 批量检查权限
   * @param {number} userId - 用户ID
   * @param {string[]} permissionCodes - 权限代码数组
   * @returns {Promise<Object>} 权限检查结果
   */
  async checkMultiplePermissions(userId, permissionCodes) {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      const results = {};

      // 超级管理员拥有所有权限
      if (userPermissions.role === 'super_admin') {
        permissionCodes.forEach(code => {
          results[code] = true;
        });
        return {
          userId,
          hasAllPermissions: true,
          permissionResults: results
        };
      }

      const effectivePermissions = userPermissions.effectivePermissions;
      let hasAllPermissions = true;

      permissionCodes.forEach(code => {
        const hasPermission = effectivePermissions.includes(code);
        results[code] = hasPermission;
        if (!hasPermission) {
          hasAllPermissions = false;
        }
      });

      return {
        userId,
        hasAllPermissions,
        permissionResults: results
      };

    } catch (error) {
      console.error(`批量权限检查失败 (用户ID: ${userId}):`, error.message);
      throw error;
    }
  }

  /**
   * 获取权限详细信息
   * @param {string} permissionCode - 权限代码
   * @returns {Promise<Object>} 权限详细信息
   */
  async getPermissionDetails(permissionCode) {
    try {
      const [permissions] = await this.db.execute(
        'SELECT * FROM permissions WHERE code = ?',
        [permissionCode]
      );

      if (permissions.length === 0) {
        throw new Error(`权限不存在: ${permissionCode}`);
      }

      const permission = permissions[0];

      // 获取拥有此权限的角色
      const [roles] = await this.db.execute(
        'SELECT DISTINCT role FROM role_permissions WHERE permission_code = ?',
        [permissionCode]
      );

      // 获取拥有此权限的用户数量
      const [userCount] = await this.db.execute(`
        SELECT COUNT(DISTINCT u.id) as count
        FROM users u
        LEFT JOIN role_permissions rp ON u.role = rp.role
        LEFT JOIN user_custom_permissions ucp ON u.id = ucp.user_id
        WHERE (rp.permission_code = ? OR (ucp.permission_code = ? AND ucp.granted = 1))
      `, [permissionCode, permissionCode]);

      return {
        ...permission,
        roles: roles.map(r => r.role),
        userCount: userCount[0].count
      };

    } catch (error) {
      console.error(`获取权限详情失败 (权限: ${permissionCode}):`, error.message);
      throw error;
    }
  }

  /**
   * 获取用户权限变更历史
   * @param {number} userId - 用户ID
   * @param {number} limit - 限制数量，默认50
   * @returns {Promise<Array>} 权限变更历史
   */
  async getUserPermissionHistory(userId, limit = 50) {
    try {
      const [history] = await this.db.execute(`
        SELECT 
          al.id,
          al.action,
          al.resource,
          al.resource_id,
          al.result,
          al.details,
          al.created_at,
          u.username as operator_username
        FROM audit_logs al
        LEFT JOIN users u ON JSON_EXTRACT(al.details, '$.operatorId') = u.id
        WHERE al.user_id = ? 
          AND al.action IN ('permission_update', 'role_change', 'permission_grant', 'permission_revoke')
        ORDER BY al.created_at DESC
        LIMIT ?
      `, [userId, limit]);

      return history.map(record => ({
        ...record,
        details: typeof record.details === 'string' ? JSON.parse(record.details) : record.details
      }));

    } catch (error) {
      console.error(`获取用户权限历史失败 (用户ID: ${userId}):`, error.message);
      throw error;
    }
  }

  /**
   * 验证权限依赖关系
   * @param {string[]} permissionCodes - 权限代码数组
   * @returns {Promise<Object>} 依赖关系验证结果
   */
  async validatePermissionDependencies(permissionCodes) {
    try {
      // 权限依赖规则定义
      const dependencyRules = {
        'user.user.delete': ['user.user.edit', 'user.user.view'],
        'site.website.delete': ['site.website.edit', 'site.website.view'],
        'server.server.delete': ['server.server.edit', 'server.server.view'],
        'system.backup.restore': ['system.backup.create'],
        'system.permission.manage': ['user.user.view', 'user.list.view']
      };

      const validationResult = {
        valid: true,
        missingDependencies: [],
        warnings: []
      };

      for (const permissionCode of permissionCodes) {
        if (dependencyRules[permissionCode]) {
          const requiredPermissions = dependencyRules[permissionCode];
          const missingDeps = requiredPermissions.filter(dep => !permissionCodes.includes(dep));
          
          if (missingDeps.length > 0) {
            validationResult.valid = false;
            validationResult.missingDependencies.push({
              permission: permissionCode,
              missing: missingDeps
            });
          }
        }
      }

      return validationResult;

    } catch (error) {
      console.error('权限依赖关系验证失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取权限统计信息
   * @returns {Promise<Object>} 权限统计信息
   */
  async getPermissionStatistics() {
    try {
      // 权限总数统计
      const [permissionCount] = await this.db.execute(
        'SELECT COUNT(*) as total FROM permissions'
      );

      // 按模块统计权限
      const [moduleStats] = await this.db.execute(`
        SELECT 
          module,
          COUNT(*) as permission_count,
          COUNT(DISTINCT resource) as resource_count,
          COUNT(DISTINCT action) as action_count
        FROM permissions 
        GROUP BY module
        ORDER BY permission_count DESC
      `);

      // 角色权限统计
      const [roleStats] = await this.db.execute(`
        SELECT 
          role,
          COUNT(*) as permission_count
        FROM role_permissions 
        GROUP BY role
        ORDER BY permission_count DESC
      `);

      // 用户自定义权限统计
      const [customPermStats] = await this.db.execute(`
        SELECT 
          COUNT(*) as total_custom_permissions,
          COUNT(CASE WHEN granted = 1 THEN 1 END) as granted_permissions,
          COUNT(CASE WHEN granted = 0 THEN 1 END) as revoked_permissions,
          COUNT(DISTINCT user_id) as users_with_custom_permissions
        FROM user_custom_permissions
      `);

      return {
        totalPermissions: permissionCount[0].total,
        moduleStatistics: moduleStats,
        roleStatistics: roleStats,
        customPermissionStatistics: customPermStats[0]
      };

    } catch (error) {
      console.error('获取权限统计信息失败:', error.message);
      throw error;
    }
  }
}

module.exports = PermissionService;