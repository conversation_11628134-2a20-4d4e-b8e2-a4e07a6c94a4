const mysql = require('mysql2/promise');
const db = require('../utils/db');

/**
 * 增强版权限服务
 * 支持页面级、模块级、资源级权限管理
 * 基于Context7最佳实践优化：
 * 1. 实现权限属性过滤功能
 * 2. 优化权限检查性能
 * 3. 添加权限锁定机制
 * 4. 实现细粒度权限控制
 */
class EnhancedPermissionService {
    constructor() {
        this.pool = db.pool;
        this.permissionCache = new Map(); // 权限缓存
        this.locked = false; // 权限锁定状态
        this.cacheExpiry = 5 * 60 * 1000; // 缓存过期时间：5分钟
    }

    /**
     * 锁定权限系统，防止运行时修改
     * 基于AccessControl.js最佳实践
     */
    lock() {
        this.locked = true;
        console.log('权限系统已锁定，禁止修改权限配置');
        return this;
    }

    /**
     * 检查权限系统是否已锁定
     */
    isLocked() {
        return this.locked;
    }

    /**
     * 清除权限缓存
     */
    clearCache() {
        this.permissionCache.clear();
        console.log('权限缓存已清除');
    }

    /**
     * 获取缓存键
     */
    getCacheKey(type, userId, ...params) {
        return `${type}:${userId}:${params.join(':')}`;
    }

    /**
     * 从缓存获取数据
     */
    getFromCache(key) {
        const cached = this.permissionCache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
            return cached.data;
        }
        return null;
    }

    /**
     * 设置缓存数据
     */
    setCache(key, data) {
        this.permissionCache.set(key, {
            data,
            timestamp: Date.now()
        });
    }

    /**
     * 获取所有页面权限定义
     * 优化：添加缓存支持
     */
    async getAllPagePermissions() {
        const cacheKey = this.getCacheKey('all_page_permissions');
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            return cached;
        }

        const connection = await this.pool.getConnection();
        try {
            const [rows] = await connection.execute(`
                SELECT
                    permission_code,
                    permission_name,
                    description,
                    route_path,
                    parent_code,
                    sort_order,
                    is_active,
                    attributes
                FROM page_permissions
                WHERE is_active = TRUE
                ORDER BY sort_order ASC, permission_code ASC
            `);

            // 缓存结果
            this.setCache(cacheKey, rows);
            return rows;
        } finally {
            connection.release();
        }
    }

    /**
     * 权限属性过滤功能
     * 基于AccessControl.js最佳实践实现
     */
    filterDataByPermission(data, attributes) {
        if (!attributes || attributes.includes('*')) {
            // 如果包含通配符，返回所有数据（除了被明确拒绝的）
            const deniedAttrs = attributes ? attributes.filter(attr => attr.startsWith('!')) : [];
            if (deniedAttrs.length === 0) {
                return data;
            }

            // 过滤掉被拒绝的属性
            const filtered = { ...data };
            deniedAttrs.forEach(attr => {
                const key = attr.substring(1); // 移除 '!' 前缀
                if (key.includes('.')) {
                    // 处理嵌套属性，如 'record.id'
                    const parts = key.split('.');
                    let current = filtered;
                    for (let i = 0; i < parts.length - 1; i++) {
                        if (current[parts[i]]) {
                            current = current[parts[i]];
                        } else {
                            break;
                        }
                    }
                    if (current && current[parts[parts.length - 1]]) {
                        delete current[parts[parts.length - 1]];
                    }
                } else {
                    delete filtered[key];
                }
            });
            return filtered;
        }

        // 只返回明确允许的属性
        const filtered = {};
        attributes.forEach(attr => {
            if (!attr.startsWith('!') && data[attr] !== undefined) {
                filtered[attr] = data[attr];
            }
        });
        return filtered;
    }

    /**
     * 检查用户是否拥有特定权限
     * 基于Laravel Permission最佳实践：优先检查权限而不是角色
     */
    async checkUserPermission(userId, permissionCode, resource = null) {
        const cacheKey = this.getCacheKey('user_permission', userId, permissionCode, resource || 'none');
        const cached = this.getFromCache(cacheKey);
        if (cached !== null) {
            return cached;
        }

        const connection = await this.pool.getConnection();
        try {
            // 首先检查用户角色
            const [userRows] = await connection.execute(
                'SELECT role FROM users WHERE id = ?',
                [userId]
            );

            if (userRows.length === 0) {
                this.setCache(cacheKey, { granted: false, reason: '用户不存在' });
                return { granted: false, reason: '用户不存在' };
            }

            const userRole = userRows[0].role;

            // 超级管理员拥有所有权限（Gate::before模式）
            if (userRole === 'super_admin') {
                const result = {
                    granted: true,
                    attributes: ['*'],
                    reason: '超级管理员权限',
                    filter: (data) => this.filterDataByPermission(data, ['*'])
                };
                this.setCache(cacheKey, result);
                return result;
            }

            // 检查具体权限
            const [permissionRows] = await connection.execute(`
                SELECT
                    pp.permission_code,
                    pp.attributes,
                    COALESCE(upp.granted, FALSE) as granted,
                    upp.granted_at,
                    upp.expires_at
                FROM page_permissions pp
                LEFT JOIN user_page_permissions upp ON pp.permission_code = upp.permission_code
                    AND upp.user_id = ?
                    AND (upp.expires_at IS NULL OR upp.expires_at > NOW())
                WHERE pp.permission_code = ? AND pp.is_active = TRUE
            `, [userId, permissionCode]);

            if (permissionRows.length === 0) {
                const result = { granted: false, reason: '权限不存在' };
                this.setCache(cacheKey, result);
                return result;
            }

            const permission = permissionRows[0];
            const attributes = permission.attributes ? permission.attributes.split(',').map(attr => attr.trim()) : ['*'];

            const result = {
                granted: permission.granted,
                attributes: attributes,
                reason: permission.granted ? '权限已授予' : '权限未授予',
                filter: (data) => this.filterDataByPermission(data, attributes)
            };

            this.setCache(cacheKey, result);
            return result;
        } finally {
            connection.release();
        }
    }

    /**
     * 获取用户的页面权限
     */
    async getUserPagePermissions(userId) {
        const connection = await this.pool.getConnection();
        try {
            // 首先检查用户角色
            const [userRows] = await connection.execute(
                'SELECT role FROM users WHERE id = ?',
                [userId]
            );

            if (userRows.length === 0) {
                throw new Error('用户不存在');
            }

            const userRole = userRows[0].role;

            // 超级管理员拥有所有权限
            if (userRole === 'super_admin') {
                const [allPermissions] = await connection.execute(`
                    SELECT 
                        permission_code,
                        permission_name,
                        description,
                        TRUE as granted,
                        NOW() as granted_at
                    FROM page_permissions 
                    WHERE is_active = TRUE
                    ORDER BY sort_order ASC
                `);
                return allPermissions;
            }

            // 获取用户的具体权限
            const [rows] = await connection.execute(`
                SELECT 
                    pp.permission_code,
                    pp.permission_name,
                    pp.description,
                    COALESCE(upp.granted, FALSE) as granted,
                    upp.granted_at,
                    upp.expires_at
                FROM page_permissions pp
                LEFT JOIN user_page_permissions upp ON pp.permission_code = upp.permission_code 
                    AND upp.user_id = ?
                    AND (upp.expires_at IS NULL OR upp.expires_at > NOW())
                WHERE pp.is_active = TRUE
                ORDER BY pp.sort_order ASC
            `, [userId]);

            return rows;
        } finally {
            connection.release();
        }
    }

    /**
     * 更新用户页面权限
     */
    async updateUserPagePermissions(userId, permissions, grantedBy) {
        const connection = await this.pool.getConnection();
        try {
            await connection.beginTransaction();

            // 删除用户现有的页面权限
            await connection.execute(
                'DELETE FROM user_page_permissions WHERE user_id = ?',
                [userId]
            );

            // 插入新的权限
            if (permissions && permissions.length > 0) {
                const values = permissions.map(permissionCode => [
                    userId,
                    permissionCode,
                    true,
                    grantedBy,
                    new Date()
                ]);

                await connection.execute(`
                    INSERT INTO user_page_permissions 
                    (user_id, permission_code, granted, granted_by, granted_at)
                    VALUES ${values.map(() => '(?, ?, ?, ?, ?)').join(', ')}
                `, values.flat());
            }

            await connection.commit();
            return { success: true, message: '页面权限更新成功' };
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    }

    /**
     * 获取用户的网站权限
     */
    async getUserWebsitePermissions(userId) {
        const connection = await this.pool.getConnection();
        try {
            // 首先检查用户角色
            const [userRows] = await connection.execute(
                'SELECT role FROM users WHERE id = ?',
                [userId]
            );

            if (userRows.length === 0) {
                throw new Error('用户不存在');
            }

            const userRole = userRows[0].role;

            // 获取所有网站和用户权限
            const [rows] = await connection.execute(`
                SELECT
                    w.id,
                    w.site_name as name,
                    w.domain,
                    w.site_url as url,
                    CASE 
                        WHEN ? = 'super_admin' THEN TRUE
                        ELSE COALESCE(ewp.access_granted, FALSE)
                    END as access_granted,
                    CASE 
                        WHEN ? = 'super_admin' THEN TRUE
                        ELSE COALESCE(ewp.edit_granted, FALSE)
                    END as edit_granted,
                    CASE 
                        WHEN ? = 'super_admin' THEN TRUE
                        ELSE COALESCE(ewp.view_credentials, FALSE)
                    END as view_credentials,
                    CASE 
                        WHEN ? = 'super_admin' THEN TRUE
                        ELSE COALESCE(ewp.manage_ssl, FALSE)
                    END as manage_ssl,
                    CASE 
                        WHEN ? = 'super_admin' THEN TRUE
                        ELSE COALESCE(ewp.manage_backup, FALSE)
                    END as manage_backup,
                    ewp.granted_at,
                    ewp.expires_at
                FROM websites w
                LEFT JOIN enhanced_website_permissions ewp ON w.id = ewp.website_id 
                    AND ewp.user_id = ?
                    AND ewp.is_active = TRUE
                    AND (ewp.expires_at IS NULL OR ewp.expires_at > NOW())
                WHERE w.status = 'active'
                ORDER BY w.site_name ASC
            `, [userRole, userRole, userRole, userRole, userRole, userId]);

            return rows;
        } finally {
            connection.release();
        }
    }

    /**
     * 更新用户网站权限
     */
    async updateUserWebsitePermissions(userId, websitePermissions, grantedBy) {
        const connection = await this.pool.getConnection();
        try {
            await connection.beginTransaction();

            // 删除用户现有的网站权限
            await connection.execute(
                'DELETE FROM enhanced_website_permissions WHERE user_id = ?',
                [userId]
            );

            // 插入新的网站权限
            if (websitePermissions && websitePermissions.length > 0) {
                const values = websitePermissions.map(wp => [
                    userId,
                    wp.id,
                    wp.access_granted || false,
                    wp.edit_granted || false,
                    wp.view_credentials || false,
                    wp.manage_ssl || false,
                    wp.manage_backup || false,
                    grantedBy,
                    new Date(),
                    true
                ]);

                await connection.execute(`
                    INSERT INTO enhanced_website_permissions 
                    (user_id, website_id, access_granted, edit_granted, view_credentials, 
                     manage_ssl, manage_backup, granted_by, granted_at, is_active)
                    VALUES ${values.map(() => '(?, ?, ?, ?, ?, ?, ?, ?, ?, ?)').join(', ')}
                `, values.flat());
            }

            await connection.commit();
            return { success: true, message: '网站权限更新成功' };
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    }

    /**
     * 获取用户的服务器权限
     */
    async getUserServerPermissions(userId) {
        const connection = await this.pool.getConnection();
        try {
            // 首先检查用户角色
            const [userRows] = await connection.execute(
                'SELECT role FROM users WHERE id = ?',
                [userId]
            );

            if (userRows.length === 0) {
                throw new Error('用户不存在');
            }

            const userRole = userRows[0].role;

            // 获取所有服务器和用户权限
            const [rows] = await connection.execute(`
                SELECT
                    s.id,
                    s.name,
                    s.ip_address as ip,
                    s.location,
                    CASE 
                        WHEN ? = 'super_admin' THEN TRUE
                        ELSE COALESCE(esp.access_granted, FALSE)
                    END as access_granted,
                    CASE 
                        WHEN ? = 'super_admin' THEN TRUE
                        ELSE COALESCE(esp.edit_granted, FALSE)
                    END as edit_granted,
                    CASE 
                        WHEN ? = 'super_admin' THEN TRUE
                        ELSE COALESCE(esp.connect_granted, FALSE)
                    END as connect_granted,
                    CASE 
                        WHEN ? = 'super_admin' THEN TRUE
                        ELSE COALESCE(esp.view_credentials, FALSE)
                    END as view_credentials,
                    esp.granted_at,
                    esp.expires_at
                FROM servers s
                LEFT JOIN enhanced_server_permissions esp ON s.id = esp.server_id 
                    AND esp.user_id = ?
                    AND esp.is_active = TRUE
                    AND (esp.expires_at IS NULL OR esp.expires_at > NOW())
                WHERE s.status = 'active'
                ORDER BY s.name ASC
            `, [userRole, userRole, userRole, userRole, userId]);

            return rows;
        } finally {
            connection.release();
        }
    }

    /**
     * 更新用户服务器权限
     */
    async updateUserServerPermissions(userId, serverPermissions, grantedBy) {
        const connection = await this.pool.getConnection();
        try {
            await connection.beginTransaction();

            // 删除用户现有的服务器权限
            await connection.execute(
                'DELETE FROM enhanced_server_permissions WHERE user_id = ?',
                [userId]
            );

            // 插入新的服务器权限
            if (serverPermissions && serverPermissions.length > 0) {
                const values = serverPermissions.map(sp => [
                    userId,
                    sp.id,
                    sp.access_granted || false,
                    sp.edit_granted || false,
                    sp.connect_granted || false,
                    sp.view_credentials || false,
                    grantedBy,
                    new Date(),
                    true
                ]);

                await connection.execute(`
                    INSERT INTO enhanced_server_permissions 
                    (user_id, server_id, access_granted, edit_granted, connect_granted, 
                     view_credentials, granted_by, granted_at, is_active)
                    VALUES ${values.map(() => '(?, ?, ?, ?, ?, ?, ?, ?, ?)').join(', ')}
                `, values.flat());
            }

            await connection.commit();
            return { success: true, message: '服务器权限更新成功' };
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    }

    /**
     * 检查用户页面权限
     */
    async checkUserPagePermission(userId, permissionCode) {
        const connection = await this.pool.getConnection();
        try {
            const [result] = await connection.execute(
                'CALL CheckUserPagePermission(?, ?)',
                [userId, permissionCode]
            );
            return result[0] && result[0][0] && result[0][0].has_permission;
        } finally {
            connection.release();
        }
    }

    /**
     * 检查用户网站权限
     */
    async checkUserWebsitePermission(userId, websiteId, action = 'access') {
        const connection = await this.pool.getConnection();
        try {
            const [result] = await connection.execute(
                'CALL CheckUserWebsitePermission(?, ?, ?)',
                [userId, websiteId, action]
            );
            return result[0] && result[0][0] && result[0][0].has_permission;
        } finally {
            connection.release();
        }
    }

    /**
     * 批量更新用户权限
     */
    async updateUserPermissions(userId, permissionData, grantedBy) {
        const connection = await this.pool.getConnection();
        try {
            await connection.beginTransaction();

            // 更新页面权限
            if (permissionData.pagePermissions) {
                const pageResult = await this.updateUserPagePermissions(userId, permissionData.pagePermissions, grantedBy);
                if (!pageResult.success) {
                    throw new Error('页面权限更新失败');
                }
            }

            // 更新网站权限
            if (permissionData.websitePermissions) {
                const websiteResult = await this.updateUserWebsitePermissions(userId, permissionData.websitePermissions, grantedBy);
                if (!websiteResult.success) {
                    throw new Error('网站权限更新失败');
                }
            }

            // 更新服务器权限
            if (permissionData.serverPermissions) {
                const serverResult = await this.updateUserServerPermissions(userId, permissionData.serverPermissions, grantedBy);
                if (!serverResult.success) {
                    throw new Error('服务器权限更新失败');
                }
            }

            await connection.commit();
            return { success: true, message: '用户权限更新成功' };
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    }

    /**
     * 获取用户完整权限信息
     */
    async getUserCompletePermissions(userId) {
        try {
            const [pagePermissions, websitePermissions, serverPermissions] = await Promise.all([
                this.getUserPagePermissions(userId),
                this.getUserWebsitePermissions(userId),
                this.getUserServerPermissions(userId)
            ]);

            return {
                pagePermissions,
                websitePermissions,
                serverPermissions
            };
        } catch (error) {
            throw error;
        }
    }

    /**
     * 记录权限变更日志
     */
    async logPermissionChange(userId, changedBy, changeType, details) {
        const connection = await this.pool.getConnection();
        try {
            await connection.execute(`
                INSERT INTO permission_change_logs 
                (user_id, changed_by, change_type, details, created_at)
                VALUES (?, ?, ?, ?, NOW())
            `, [userId, changedBy, changeType, JSON.stringify(details)]);
        } catch (error) {
            console.error('记录权限变更日志失败:', error);
            // 不抛出错误，避免影响主要功能
        } finally {
            connection.release();
        }
    }
}

module.exports = new EnhancedPermissionService();
