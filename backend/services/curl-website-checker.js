const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

/**
 * 基于curl的网站检测服务
 * 使用curl命令进行网站状态和SSL检测
 */
class CurlWebsiteChecker {
  constructor() {
    this.timeout = 30; // 30秒超时
    this.connectTimeout = 10; // 10秒连接超时
  }

  /**
   * 检测单个网站
   * @param {string} url - 网站URL
   * @returns {Promise<Object>} 检测结果
   */
  async checkWebsite(url) {
    try {
      const startTime = Date.now();
      
      // 构建curl命令
      const curlCmd = [
        'curl',
        '-kv', // -k忽略SSL错误，-v详细输出
        `--connect-timeout ${this.connectTimeout}`,
        `--max-time ${this.timeout}`,
        '-w "\\nHTTP_CODE:%{http_code}\\nTOTAL_TIME:%{time_total}\\nCONNECT_TIME:%{time_connect}\\nSSL_VERIFY_RESULT:%{ssl_verify_result}\\n"',
        '-o /dev/null', // 不保存响应体
        '-s', // 静默模式，不显示进度
        `"${url}"`
      ].join(' ');

      const { stdout, stderr } = await execAsync(curlCmd);
      const endTime = Date.now();
      
      // 解析curl输出
      const result = this.parseCurlOutput(stdout, stderr);
      
      // 提取SSL证书信息
      const sslInfo = this.extractSSLInfo(stderr);
      
      return {
        success: true,
        url: url,
        statusCode: result.httpCode,
        responseTime: Math.round((endTime - startTime)),
        totalTime: result.totalTime,
        connectTime: result.connectTime,
        sslVerifyResult: result.sslVerifyResult,
        ssl: sslInfo,
        timestamp: new Date().toISOString(),
        error: null
      };
      
    } catch (error) {
      return {
        success: false,
        url: url,
        statusCode: 0,
        responseTime: 0,
        totalTime: 0,
        connectTime: 0,
        sslVerifyResult: -1,
        ssl: null,
        timestamp: new Date().toISOString(),
        error: error.message
      };
    }
  }

  /**
   * 批量检测网站
   * @param {Array} urls - 网站URL数组
   * @param {number} concurrency - 并发数
   * @returns {Promise<Array>} 检测结果数组
   */
  async checkWebsites(urls, concurrency = 5) {
    const results = [];
    
    // 分批处理
    for (let i = 0; i < urls.length; i += concurrency) {
      const batch = urls.slice(i, i + concurrency);
      console.log(`🔍 检测第 ${Math.floor(i/concurrency) + 1} 批，包含 ${batch.length} 个网站`);
      
      const batchPromises = batch.map(url => this.checkWebsite(url));
      const batchResults = await Promise.allSettled(batchPromises);
      
      // 处理结果
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            url: batch[index],
            statusCode: 0,
            responseTime: 0,
            error: result.reason?.message || '检测失败'
          });
        }
      });
      
      // 避免过于频繁的请求
      if (i + concurrency < urls.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
    
    return results;
  }

  /**
   * 解析curl输出的统计信息
   * @param {string} stdout - curl标准输出
   * @param {string} stderr - curl错误输出
   * @returns {Object} 解析后的结果
   */
  parseCurlOutput(stdout, stderr) {
    const output = stdout + stderr;
    
    const httpCodeMatch = output.match(/HTTP_CODE:(\d+)/);
    const totalTimeMatch = output.match(/TOTAL_TIME:([\d.]+)/);
    const connectTimeMatch = output.match(/CONNECT_TIME:([\d.]+)/);
    const sslVerifyMatch = output.match(/SSL_VERIFY_RESULT:(\d+)/);
    
    return {
      httpCode: httpCodeMatch ? parseInt(httpCodeMatch[1]) : 0,
      totalTime: totalTimeMatch ? parseFloat(totalTimeMatch[1]) : 0,
      connectTime: connectTimeMatch ? parseFloat(connectTimeMatch[1]) : 0,
      sslVerifyResult: sslVerifyMatch ? parseInt(sslVerifyMatch[1]) : -1
    };
  }

  /**
   * 从curl输出中提取SSL证书信息
   * @param {string} stderr - curl错误输出
   * @returns {Object|null} SSL证书信息
   */
  extractSSLInfo(stderr) {
    try {
      // 提取证书主题
      const subjectMatch = stderr.match(/\*\s+subject:\s*(.+)/);
      const subject = subjectMatch ? subjectMatch[1].trim() : null;
      
      // 提取证书颁发者
      const issuerMatch = stderr.match(/\*\s+issuer:\s*(.+)/);
      const issuer = issuerMatch ? issuerMatch[1].trim() : null;
      
      // 提取证书有效期
      const startDateMatch = stderr.match(/\*\s+start date:\s*(.+)/);
      const expireDateMatch = stderr.match(/\*\s+expire date:\s*(.+)/);
      
      const startDate = startDateMatch ? startDateMatch[1].trim() : null;
      const expireDate = expireDateMatch ? expireDateMatch[1].trim() : null;
      
      // 提取SSL协议版本
      const sslVersionMatch = stderr.match(/\*\s+SSL connection using\s+(.+)/);
      const sslVersion = sslVersionMatch ? sslVersionMatch[1].trim() : null;
      
      if (subject || issuer || expireDate) {
        return {
          subject,
          issuer,
          startDate,
          expireDate,
          sslVersion,
          isValid: expireDate ? new Date(expireDate) > new Date() : false
        };
      }
      
      return null;
    } catch (error) {
      console.error('解析SSL信息失败:', error);
      return null;
    }
  }

  /**
   * 检测网站是否可访问（简化版）
   * @param {string} url - 网站URL
   * @returns {Promise<boolean>} 是否可访问
   */
  async isWebsiteAccessible(url) {
    try {
      const result = await this.checkWebsite(url);
      return result.success && result.statusCode >= 200 && result.statusCode < 400;
    } catch (error) {
      return false;
    }
  }
}

module.exports = CurlWebsiteChecker;
