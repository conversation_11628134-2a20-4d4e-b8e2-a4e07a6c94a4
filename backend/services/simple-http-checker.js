/**
 * 简化版HTTP检查器
 * 用于测试基本功能
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// 监控状态常量
const MONITOR_STATUS = {
  DOWN: 0,
  UP: 1,
  PENDING: 2
};

/**
 * 检查HTTP状态码是否符合预期
 */
function checkStatusCode(statusCode, expectedStatusCodes = '200-299') {
  const statusParts = expectedStatusCodes.split(',');
  
  for (const part of statusParts) {
    const trimmedPart = part.trim();
    
    if (trimmedPart.includes('-')) {
      const [min, max] = trimmedPart.split('-').map(s => parseInt(s));
      if (statusCode >= min && statusCode <= max) {
        return true;
      }
    } else if (parseInt(trimmedPart) === statusCode) {
      return true;
    }
  }
  
  return false;
}

/**
 * 简化版HTTP检查器
 */
class SimpleHttpChecker {
  async checkHttp(config) {
    const {
      url,
      httpMethod = 'GET',
      statusCodes = '200-299',
      connectTimeout = 10
    } = config;

    if (!url) {
      return {
        status: MONITOR_STATUS.DOWN,
        message: 'URL不能为空',
        ping: 0
      };
    }

    const startTime = Date.now();

    try {
      const result = await this.performRequest(url, httpMethod, connectTimeout);
      const responseTime = Date.now() - startTime;

      const isStatusValid = checkStatusCode(result.statusCode, statusCodes);

      if (isStatusValid) {
        return {
          status: MONITOR_STATUS.UP,
          message: `状态码: ${result.statusCode}`,
          ping: responseTime
        };
      } else {
        return {
          status: MONITOR_STATUS.DOWN,
          message: `状态码不符合预期: ${result.statusCode}`,
          ping: responseTime
        };
      }
    } catch (error) {
      return {
        status: MONITOR_STATUS.DOWN,
        message: error.message,
        ping: Date.now() - startTime
      };
    }
  }

  performRequest(url, method = 'GET', timeout = 10) {
    return new Promise((resolve, reject) => {
      try {
        const urlObj = new URL(url);
        const client = urlObj.protocol === 'https:' ? https : http;
        
        const options = {
          hostname: urlObj.hostname,
          port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
          path: urlObj.pathname + urlObj.search,
          method: method,
          timeout: timeout * 1000,
          headers: {
            'User-Agent': 'SiteManager-SimpleChecker/1.0',
            'Accept': '*/*',
            'Connection': 'close'
          }
        };

        if (urlObj.protocol === 'https:') {
          options.rejectUnauthorized = false; // 忽略SSL证书错误
        }

        const req = client.request(options, (res) => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers
          });
        });

        req.on('error', (error) => {
          reject(new Error(`网络错误: ${error.message}`));
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new Error('连接超时'));
        });

        req.end();
      } catch (error) {
        reject(new Error(`请求失败: ${error.message}`));
      }
    });
  }
}

module.exports = {
  SimpleHttpChecker,
  MONITOR_STATUS,
  checkStatusCode
};
