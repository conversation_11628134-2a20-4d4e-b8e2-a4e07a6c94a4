const mysql = require('mysql2/promise');

class NotificationService {
  constructor(db = null) {
    this.db = db;
  }

  // 设置数据库连接
  setDatabase(db) {
    this.db = db;
  }

  // 发送飞书通知
  async sendFeishuNotification(webhookUrl, message) {
    try {
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          msg_type: 'text',
          content: {
            text: message
          }
        }),
      });

      const result = await response.json();
      
      if (response.ok && result.code === 0) {
        console.log('✅ 飞书通知发送成功');
        return { success: true, data: result };
      } else {
        console.error('❌ 飞书通知发送失败:', result);
        return { success: false, error: result.msg || '发送失败' };
      }
    } catch (error) {
      console.error('❌ 飞书通知发送异常:', error);
      return { success: false, error: error.message };
    }
  }

  // 获取飞书配置
  async getFeishuConfig() {
    try {
      const [rows] = await this.db.execute(
        'SELECT config FROM notification_configs WHERE type = ? AND is_active = 1 LIMIT 1',
        ['feishu']
      );

      if (rows.length > 0) {
        return JSON.parse(rows[0].config);
      }
      return null;
    } catch (error) {
      console.error('获取飞书配置失败:', error);
      return null;
    }
  }

  // 记录通知日志
  async logNotification(websiteId, notificationType, triggerReason, message, status, responseData = null, errorMessage = null) {
    try {
      await this.db.execute(
        `INSERT INTO notification_logs 
         (website_id, notification_type, trigger_reason, message, status, response_data, error_message, sent_at) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          websiteId,
          notificationType,
          triggerReason,
          message,
          status,
          responseData ? JSON.stringify(responseData) : null,
          errorMessage,
          status === 'sent' ? new Date() : null
        ]
      );
    } catch (error) {
      console.error('记录通知日志失败:', error);
    }
  }

  // 检查是否需要发送通知
  async shouldSendNotification(websiteId, consecutiveFailures) {
    try {
      // 检查是否达到通知阈值（5次连续失败）
      if (consecutiveFailures < 5) {
        return false;
      }

      // 检查是否已经发送过通知（避免重复发送）
      const [rows] = await this.db.execute(
        'SELECT notification_sent FROM websites WHERE id = ?',
        [websiteId]
      );

      if (rows.length > 0 && rows[0].notification_sent) {
        return false;
      }

      return true;
    } catch (error) {
      console.error('检查通知条件失败:', error);
      return false;
    }
  }

  // 发送网站异常通知
  async sendWebsiteFailureNotification(website, consecutiveFailures, lastStatusCode) {
    try {
      // 检查是否需要发送通知
      if (!(await this.shouldSendNotification(website.id, consecutiveFailures))) {
        return { success: false, reason: '不满足通知条件' };
      }

      // 获取飞书配置
      const feishuConfig = await this.getFeishuConfig();
      if (!feishuConfig || !feishuConfig.webhookUrl) {
        console.warn('⚠️  未配置飞书通知，跳过发送');
        return { success: false, reason: '未配置飞书通知' };
      }

      // 构建通知消息
      const message = this.buildFailureMessage(website, consecutiveFailures, lastStatusCode);

      // 发送飞书通知
      const result = await this.sendFeishuNotification(feishuConfig.webhookUrl, message);

      // 记录通知日志
      await this.logNotification(
        website.id,
        'feishu',
        'consecutive_failures',
        message,
        result.success ? 'sent' : 'failed',
        result.data,
        result.error
      );

      if (result.success) {
        // 更新网站通知状态
        await this.db.execute(
          'UPDATE websites SET notification_sent = 1, last_notification_time = NOW() WHERE id = ?',
          [website.id]
        );

        console.log(`✅ 网站异常通知发送成功: ${website.siteName}`);
        return { success: true, message: '通知发送成功' };
      } else {
        console.error(`❌ 网站异常通知发送失败: ${website.siteName} - ${result.error}`);
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('发送网站异常通知失败:', error);
      return { success: false, error: error.message };
    }
  }

  // 构建失败通知消息
  buildFailureMessage(website, consecutiveFailures, lastStatusCode) {
    const currentTime = new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' });
    
    return `🚨 网站异常告警

📍 站点信息：
• 站点名称：${website.siteName || website.site_name}
• 站点URL：${website.siteUrl || website.site_url}
• 域名：${website.domain}

⚠️ 异常详情：
• 连续失败次数：${consecutiveFailures} 次
• 最后状态码：${lastStatusCode || '无响应'}
• 告警时间：${currentTime}

🔧 建议操作：
1. 检查网站服务器状态
2. 验证域名解析是否正常
3. 检查网站服务是否运行
4. 查看服务器资源使用情况

请及时处理以确保网站正常运行！`;
  }

  // 重置网站通知状态（当网站恢复正常时调用）
  async resetNotificationStatus(websiteId) {
    try {
      await this.db.execute(
        'UPDATE websites SET notification_sent = 0, consecutive_failures = 0, last_failure_time = NULL WHERE id = ?',
        [websiteId]
      );
      console.log(`🔄 重置网站通知状态: ID ${websiteId}`);
    } catch (error) {
      console.error('重置网站通知状态失败:', error);
    }
  }

  // 获取通知统计
  async getNotificationStats() {
    try {
      const [totalRows] = await this.db.execute(
        'SELECT COUNT(*) as total FROM notification_logs WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)'
      );

      const [sentRows] = await this.db.execute(
        'SELECT COUNT(*) as sent FROM notification_logs WHERE status = "sent" AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)'
      );

      const [failedRows] = await this.db.execute(
        'SELECT COUNT(*) as failed FROM notification_logs WHERE status = "failed" AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)'
      );

      return {
        total: totalRows[0].total,
        sent: sentRows[0].sent,
        failed: failedRows[0].failed,
        successRate: totalRows[0].total > 0 ? (sentRows[0].sent / totalRows[0].total * 100).toFixed(2) : 0
      };
    } catch (error) {
      console.error('获取通知统计失败:', error);
      return { total: 0, sent: 0, failed: 0, successRate: 0 };
    }
  }
}

module.exports = NotificationService;
