/**
 * 通知服务
 * 支持多种通知方式：飞书、邮件、短信、微信、钉钉、Webhook
 * 集成数据库存储和日志记录功能
 */

const nodemailer = require('nodemailer');
const axios = require('axios');
const mysql = require('mysql2/promise');

class NotificationService {
  constructor(config = {}) {
    // 数据库配置
    this.dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'sitemanager',
      password: process.env.DB_PASSWORD || 'sitemanager123',
      database: process.env.DB_NAME || 'sitemanager',
      charset: 'utf8mb4'
    };

    this.config = {
      // 飞书配置
      feishu: {
        enabled: config.feishu?.enabled || false,
        webhookUrl: config.feishu?.webhookUrl || '',
        botName: config.feishu?.botName || '网站监控机器人',
        notificationThreshold: config.feishu?.notificationThreshold || 5
      },

      // 邮件配置
      email: {
        enabled: config.email?.enabled || false,
        host: config.email?.host || 'smtp.qq.com',
        port: config.email?.port || 587,
        secure: config.email?.secure || false,
        user: config.email?.user || '',
        pass: config.email?.pass || '',
        from: config.email?.from || '',
        to: config.email?.to || []
      },

      // 短信配置（阿里云短信）
      sms: {
        enabled: config.sms?.enabled || false,
        accessKeyId: config.sms?.accessKeyId || '',
        accessKeySecret: config.sms?.accessKeySecret || '',
        signName: config.sms?.signName || '站点监控',
        templateCode: config.sms?.templateCode || '',
        phoneNumbers: config.sms?.phoneNumbers || []
      },

      // 微信配置（企业微信机器人）
      wechat: {
        enabled: config.wechat?.enabled || false,
        webhookUrl: config.wechat?.webhookUrl || '',
        mentionedList: config.wechat?.mentionedList || []
      },

      // 钉钉配置
      dingtalk: {
        enabled: config.dingtalk?.enabled || false,
        webhookUrl: config.dingtalk?.webhookUrl || '',
        secret: config.dingtalk?.secret || '',
        atMobiles: config.dingtalk?.atMobiles || [],
        isAtAll: config.dingtalk?.isAtAll || false
      },

      // Webhook配置
      webhook: {
        enabled: config.webhook?.enabled || false,
        urls: config.webhook?.urls || [],
        headers: config.webhook?.headers || {}
      }
    };

    // 初始化邮件发送器
    if (this.config.email.enabled) {
      this.emailTransporter = nodemailer.createTransporter({
        host: this.config.email.host,
        port: this.config.email.port,
        secure: this.config.email.secure,
        auth: {
          user: this.config.email.user,
          pass: this.config.email.pass
        }
      });
    }
  }

  /**
   * 获取数据库连接
   */
  async getDbConnection() {
    return await mysql.createConnection(this.dbConfig);
  }

  /**
   * 从数据库加载通知配置
   */
  async loadConfigFromDatabase() {
    const connection = await this.getDbConnection();
    try {
      const [configs] = await connection.execute(
        'SELECT * FROM notification_configs WHERE is_active = 1'
      );

      for (const config of configs) {
        let parsedConfig;
        try {
          // 如果config.config是字符串，则解析；如果已经是对象，则直接使用
          parsedConfig = typeof config.config === 'string' ? JSON.parse(config.config) : config.config;
        } catch (error) {
          console.error(`解析通知配置失败 (${config.type}):`, error);
          continue;
        }

        if (this.config[config.type]) {
          this.config[config.type] = {
            ...this.config[config.type],
            ...parsedConfig,
            enabled: true
          };
        }
      }

      // 重新初始化邮件发送器
      if (this.config.email.enabled) {
        this.emailTransporter = nodemailer.createTransport({
          host: this.config.email.host,
          port: this.config.email.port,
          secure: this.config.email.secure,
          auth: {
            user: this.config.email.user,
            pass: this.config.email.pass
          }
        });
      }
    } finally {
      await connection.end();
    }
  }

  /**
   * 记录通知日志
   */
  async logNotification(websiteId, notificationType, triggerReason, message, status, responseData = null, errorMessage = null) {
    const connection = await this.getDbConnection();
    try {
      await connection.execute(
        `INSERT INTO notification_logs
         (website_id, notification_type, trigger_reason, message, status, response_data, error_message, sent_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          websiteId,
          notificationType,
          triggerReason,
          message,
          status,
          responseData ? JSON.stringify(responseData) : null,
          errorMessage,
          status === 'sent' ? new Date() : null
        ]
      );
    } catch (error) {
      console.error('记录通知日志失败:', error);
    } finally {
      await connection.end();
    }
  }

  /**
   * 发送飞书通知
   */
  async sendFeishuNotification(message, websiteId = null, triggerReason = 'manual') {
    if (!this.config.feishu.enabled || !this.config.feishu.webhookUrl) {
      return;
    }

    try {
      let content;

      // 处理不同类型的消息
      if (typeof message === 'string') {
        content = message;
      } else if (message.type === 'batch_failure') {
        // 批量通知使用预生成的内容
        content = `🤖 ${this.config.feishu.botName || '网站监控机器人'}\n\n${message.content}`;
      } else {
        // 单个网站通知
        content = this.generateFeishuContent(message);
      }

      const response = await fetch(this.config.feishu.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          msg_type: 'text',
          content: {
            text: content
          }
        }),
      });

      const result = await response.json();

      if (response.ok && result.code === 0) {
        if (websiteId) {
          await this.logNotification(websiteId, 'feishu', triggerReason, content, 'sent', result);
        }
        console.log(`🤖 飞书通知已发送: ${message.site?.domain || '测试通知'}`);
        return { success: true, data: result };
      } else {
        const errorMsg = result.msg || '飞书通知发送失败';
        if (websiteId) {
          await this.logNotification(websiteId, 'feishu', triggerReason, content, 'failed', result, errorMsg);
        }
        throw new Error(errorMsg);
      }
    } catch (error) {
      if (websiteId) {
        await this.logNotification(websiteId, 'feishu', triggerReason, typeof message === 'string' ? message : JSON.stringify(message), 'failed', null, error.message);
      }
      console.error(`飞书通知发送失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 发送通知（主入口）
   */
  async sendNotification(message, websiteId = null, triggerReason = 'manual') {
    const promises = [];

    try {
      // 飞书通知
      if (this.config.feishu.enabled) {
        promises.push(this.sendFeishuNotification(message, websiteId, triggerReason));
      }

      // 邮件通知
      if (this.config.email.enabled) {
        promises.push(this.sendEmailNotification(message, websiteId, triggerReason));
      }

      // 短信通知
      if (this.config.sms.enabled) {
        promises.push(this.sendSMSNotification(message, websiteId, triggerReason));
      }

      // 微信通知
      if (this.config.wechat.enabled) {
        promises.push(this.sendWeChatNotification(message, websiteId, triggerReason));
      }

      // 钉钉通知
      if (this.config.dingtalk.enabled) {
        promises.push(this.sendDingTalkNotification(message, websiteId, triggerReason));
      }

      // Webhook通知
      if (this.config.webhook.enabled) {
        promises.push(this.sendWebhookNotification(message, websiteId, triggerReason));
      }

      // 并发发送所有通知
      const results = await Promise.allSettled(promises);

      // 统计发送结果
      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      console.log(`📤 通知发送完成: 成功 ${successful}, 失败 ${failed}`);

      // 记录失败的通知
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`通知发送失败 [${index}]:`, result.reason);
        }
      });

      return { successful, failed, results };

    } catch (error) {
      console.error('发送通知时出错:', error);
      throw error;
    }
  }

  /**
   * 发送邮件通知
   */
  async sendEmailNotification(message, websiteId = null, triggerReason = 'manual') {
    if (!this.emailTransporter || this.config.email.to.length === 0) {
      return;
    }

    let subject = '邮件通知'; // 默认主题

    try {
      subject = message.type === 'site_down'
        ? `🚨 站点故障警报 - ${message.site.domain}`
        : `✅ 站点恢复通知 - ${message.site.domain}`;

      const html = this.generateEmailHTML(message);

      const mailOptions = {
        from: this.config.email.from,
        to: this.config.email.to.join(','),
        subject,
        html
      };

      const result = await this.emailTransporter.sendMail(mailOptions);

      if (websiteId) {
        await this.logNotification(websiteId, 'email', triggerReason, subject, 'sent', result);
      }

      console.log(`📧 邮件通知已发送: ${message.site.domain}`);
      return { success: true, data: result };
    } catch (error) {
      if (websiteId) {
        await this.logNotification(websiteId, 'email', triggerReason, subject, 'failed', null, error.message);
      }
      console.error(`邮件通知发送失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 发送短信通知
   */
  async sendSMSNotification(message) {
    // 这里需要集成具体的短信服务商API
    // 以阿里云短信为例
    const content = message.type === 'site_down'
      ? `站点${message.site.domain}发生故障，错误：${message.error.message}`
      : `站点${message.site.domain}已恢复正常`;
    
    console.log(`📱 短信通知: ${content}`);
    // TODO: 实现具体的短信发送逻辑
  }

  /**
   * 发送微信通知（企业微信机器人）
   */
  async sendWeChatNotification(message) {
    if (!this.config.wechat.webhookUrl) {
      return;
    }
    
    const content = this.generateWeChatContent(message);
    
    const payload = {
      msgtype: 'markdown',
      markdown: {
        content
      }
    };
    
    await axios.post(this.config.wechat.webhookUrl, payload);
    console.log(`💬 微信通知已发送: ${message.site.domain}`);
  }

  /**
   * 发送钉钉通知
   */
  async sendDingTalkNotification(message) {
    if (!this.config.dingtalk.webhookUrl) {
      return;
    }
    
    const content = this.generateDingTalkContent(message);
    
    const payload = {
      msgtype: 'markdown',
      markdown: {
        title: message.type === 'site_down' ? '站点故障警报' : '站点恢复通知',
        text: content
      },
      at: {
        atMobiles: this.config.dingtalk.atMobiles,
        isAtAll: this.config.dingtalk.isAtAll
      }
    };
    
    // 如果配置了签名，需要计算签名
    let url = this.config.dingtalk.webhookUrl;
    if (this.config.dingtalk.secret) {
      const timestamp = Date.now();
      const crypto = require('crypto');
      const stringToSign = `${timestamp}\n${this.config.dingtalk.secret}`;
      const sign = crypto.createHmac('sha256', this.config.dingtalk.secret)
        .update(stringToSign)
        .digest('base64');
      url += `&timestamp=${timestamp}&sign=${encodeURIComponent(sign)}`;
    }
    
    await axios.post(url, payload);
    console.log(`📢 钉钉通知已发送: ${message.site.domain}`);
  }

  /**
   * 发送Webhook通知
   */
  async sendWebhookNotification(message) {
    if (this.config.webhook.urls.length === 0) {
      return;
    }
    
    const payload = {
      ...message,
      timestamp: message.timestamp.toISOString()
    };
    
    const promises = this.config.webhook.urls.map(url => 
      axios.post(url, payload, {
        headers: this.config.webhook.headers,
        timeout: 10000
      })
    );
    
    await Promise.allSettled(promises);
    console.log(`🔗 Webhook通知已发送: ${message.site.domain}`);
  }

  /**
   * 生成邮件HTML内容
   */
  generateEmailHTML(message) {
    const isDown = message.type === 'site_down';
    const statusColor = isDown ? '#ff4d4f' : '#52c41a';
    const statusIcon = isDown ? '🚨' : '✅';
    const statusText = isDown ? '站点故障警报' : '站点恢复通知';

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: ${statusColor}; color: white; padding: 20px; text-align: center;">
          <h1>${statusIcon} ${statusText}</h1>
        </div>

        <div style="padding: 20px; background: #f5f5f5;">
          <h2>站点信息</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; background: #fff;"><strong>平台类型</strong></td>
              <td style="padding: 8px; border: 1px solid #ddd; background: #fff;">${message.site.platform}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; background: #fff;"><strong>站点名称</strong></td>
              <td style="padding: 8px; border: 1px solid #ddd; background: #fff;">${message.site.name}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; background: #fff;"><strong>域名</strong></td>
              <td style="padding: 8px; border: 1px solid #ddd; background: #fff;">${message.site.domain}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; background: #fff;"><strong>URL</strong></td>
              <td style="padding: 8px; border: 1px solid #ddd; background: #fff;">
                <a href="${message.site.url}" target="_blank">${message.site.url}</a>
              </td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; background: #fff;"><strong>检测时间</strong></td>
              <td style="padding: 8px; border: 1px solid #ddd; background: #fff;">${message.timestamp.toLocaleString()}</td>
            </tr>
          </table>

          ${isDown ? `
            <h2 style="color: #ff4d4f;">故障详情</h2>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;"><strong>状态码</strong></td>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;">${message.error.statusCode}</td>
              </tr>
              <tr>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;"><strong>错误次数</strong></td>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;">${message.error.errorCount}</td>
              </tr>
              <tr>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;"><strong>所在服务器</strong></td>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;">${message.error.server} (${message.error.location})</td>
              </tr>
              <tr>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;"><strong>持续时间</strong></td>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;">${message.error.duration}</td>
              </tr>
            </table>
          ` : `
            <h2 style="color: #52c41a;">恢复详情</h2>
            <table style="width: 100%; border-collapse: collapse;">
              <tr>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;"><strong>状态码</strong></td>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;">${message.recovery.statusCode}</td>
              </tr>
              <tr>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;"><strong>响应时间</strong></td>
                <td style="padding: 8px; border: 1px solid #ddd; background: #fff;">${message.recovery.responseTime}ms</td>
              </tr>
            </table>
          `}
        </div>

        <div style="padding: 20px; text-align: center; color: #666; font-size: 12px;">
          <p>此邮件由站点监控系统自动发送，请勿回复。</p>
          <p>监控系统 - ${new Date().getFullYear()}</p>
        </div>
      </div>
    `;
  }

  /**
   * 生成飞书通知内容
   */
  generateFeishuContent(message) {
    const isDown = message.type === 'site_down';
    const statusIcon = isDown ? '🚨' : '✅';
    const statusText = isDown ? '站点故障警报' : '站点恢复通知';

    let content = `🤖 ${this.config.feishu.botName || '网站监控机器人'}\n\n`;
    content += `${statusIcon} ${statusText}\n\n`;
    content += `🏷️ 平台类型：${message.site.platform}\n\n`;
    content += `📍 站点信息：\n`;
    content += `• 站点名称：${message.site.name}\n`;
    content += `• 域名：${message.site.domain}\n`;
    content += `• URL：${message.site.url}\n`;
    content += `• 检测时间：${message.timestamp.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n\n`;

    if (isDown) {
      content += `❌ 故障详情：\n`;
      content += `• 错误信息：${message.error.message}\n`;
      content += `• 状态码：${message.error.statusCode}\n`;
      content += `• 错误次数：${message.error.errorCount}次\n`;
      content += `• 所在服务器：${message.error.server} (${message.error.location})\n`;
      content += `• 持续时间：${message.error.duration}\n\n`;
      content += `🔧 建议操作：\n`;
      content += `• 检查服务器状态\n`;
      content += `• 查看网站日志\n`;
      content += `• 联系技术支持`;
    } else {
      content += `✅ 恢复详情：\n`;
      content += `• 状态码：${message.recovery.statusCode}\n`;
      content += `• 响应时间：${message.recovery.responseTime}ms\n\n`;
      content += `🎉 站点已恢复正常访问！`;
    }

    return content;
  }

  /**
   * 生成微信通知内容
   */
  generateWeChatContent(message) {
    const isDown = message.type === 'site_down';
    const statusIcon = isDown ? '🚨' : '✅';
    const statusText = isDown ? '站点故障警报' : '站点恢复通知';

    let content = `# ${statusIcon} ${statusText}\n\n`;
    content += `**站点名称：** ${message.site.name}\n`;
    content += `**域名：** ${message.site.domain}\n`;
    content += `**URL：** [${message.site.url}](${message.site.url})\n`;
    content += `**检测时间：** ${message.timestamp.toLocaleString()}\n\n`;

    if (isDown) {
      content += `**错误信息：** ${message.error.message}\n`;
      content += `**状态码：** ${message.error.statusCode}\n`;
      content += `**响应时间：** ${message.error.responseTime}ms\n`;
    } else {
      content += `**状态码：** ${message.recovery.statusCode}\n`;
      content += `**响应时间：** ${message.recovery.responseTime}ms\n`;
    }

    return content;
  }

  /**
   * 生成钉钉通知内容
   */
  generateDingTalkContent(message) {
    return this.generateWeChatContent(message); // 格式相同
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    
    // 重新初始化邮件发送器
    if (this.config.email.enabled && newConfig.email) {
      this.emailTransporter = nodemailer.createTransport({
        host: this.config.email.host,
        port: this.config.email.port,
        secure: this.config.email.secure,
        auth: {
          user: this.config.email.user,
          pass: this.config.email.pass
        }
      });
    }
  }

  /**
   * 测试通知配置
   */
  async testNotification(type = 'feishu') {
    const testMessage = {
      type: 'site_down',
      site: {
        id: 1,
        name: '测试站点',
        domain: 'test.example.com',
        url: 'https://test.example.com'
      },
      error: {
        message: '连接超时',
        statusCode: 0,
        responseTime: 15000,
        errorCount: 3,
        server: '测试服务器',
        location: '北京',
        duration: '5分钟'
      },
      timestamp: new Date()
    };

    switch (type) {
      case 'feishu':
        return await this.sendFeishuNotification(testMessage);
      case 'email':
        return await this.sendEmailNotification(testMessage);
      case 'wechat':
        return await this.sendWeChatNotification(testMessage);
      case 'dingtalk':
        return await this.sendDingTalkNotification(testMessage);
      case 'webhook':
        return await this.sendWebhookNotification(testMessage);
      default:
        return await this.sendNotification(testMessage);
    }
  }

  /**
   * 发送网站故障通知
   */
  async sendWebsiteFailureNotification(websiteData, errorData, triggerReason = 'consecutive_failures') {
    const message = {
      type: 'site_down',
      site: {
        id: websiteData.id,
        name: websiteData.site_name,
        domain: websiteData.domain || websiteData.url,
        url: websiteData.url || websiteData.site_url,
        platform: websiteData.platform_type || '未知平台'
      },
      error: {
        message: errorData.message || errorData || '网站无法访问',
        statusCode: errorData.statusCode || websiteData.access_status_code || 0,
        responseTime: errorData.responseTime || 0,
        errorCount: errorData.errorCount || websiteData.consecutive_failures || 1,
        server: websiteData.server_name || websiteData.server_location || '未知服务器',
        location: websiteData.server_location || '未知位置',
        duration: errorData.duration || '未知'
      },
      timestamp: new Date()
    };

    return await this.sendNotification(message, websiteData.id, triggerReason);
  }

  /**
   * 发送网站恢复通知
   */
  async sendWebsiteRecoveryNotification(websiteData, recoveryData) {
    const message = {
      type: 'site_up',
      site: {
        id: websiteData.id,
        name: websiteData.site_name,
        domain: websiteData.domain || websiteData.url,
        url: websiteData.url
      },
      recovery: {
        statusCode: recoveryData.statusCode || 200,
        responseTime: recoveryData.responseTime || 0
      },
      timestamp: new Date()
    };

    return await this.sendNotification(message, websiteData.id, 'recovery');
  }

  /**
   * 发送SSL证书到期通知
   */
  async sendSSLExpiryNotification(websiteData, sslData) {
    const daysUntilExpiry = Math.ceil((new Date(sslData.expireDate) - new Date()) / (1000 * 60 * 60 * 24));

    const message = `🔒 SSL证书到期提醒

🏷️ 平台类型：${websiteData.platform_type || '未知平台'}

📍 站点信息：
• 站点名称：${websiteData.site_name}
• 域名：${websiteData.domain || websiteData.url}
• URL：${websiteData.url}

⚠️ SSL证书信息：
• 证书颁发者：${sslData.issuer || '未知'}
• 到期时间：${sslData.expireDate}
• 剩余天数：${daysUntilExpiry}天

🔧 建议操作：
• 及时续费SSL证书
• 更新证书配置
• 确保网站安全访问`;

    return await this.sendNotification(message, websiteData.id, 'ssl_expiry');
  }

  /**
   * 发送批量网站故障通知（累计通知）
   */
  async sendBatchWebsiteFailureNotification(failedWebsites) {
    if (!failedWebsites || failedWebsites.length === 0) {
      return { success: true, sentCount: 0, failedCount: 0, details: [] };
    }

    // 按平台类型分组
    const groupedByPlatform = failedWebsites.reduce((groups, website) => {
      const platform = website.platform_type || '未知平台';
      if (!groups[platform]) {
        groups[platform] = [];
      }
      groups[platform].push(website);
      return groups;
    }, {});

    // 生成批量通知内容
    const batchMessage = this.generateBatchNotificationContent(groupedByPlatform);

    // 发送批量通知
    const results = await this.sendNotification(batchMessage, null, 'batch_failures');

    // 记录每个网站的通知日志
    for (const website of failedWebsites) {
      await this.logNotification(
        website.id,
        'batch',
        'batch_failures',
        `批量通知：${website.site_name}`,
        'sent',
        results
      );
    }

    return {
      success: true,
      sentCount: results.sentCount || 0,
      failedCount: results.failedCount || 0,
      details: results.details || [],
      websiteCount: failedWebsites.length,
      platformCount: Object.keys(groupedByPlatform).length
    };
  }

  /**
   * 生成批量通知内容
   */
  generateBatchNotificationContent(groupedByPlatform) {
    const totalWebsites = Object.values(groupedByPlatform).reduce((sum, websites) => sum + websites.length, 0);
    const platformCount = Object.keys(groupedByPlatform).length;

    let content = `🚨 批量网站故障警报\n\n`;
    content += `📊 故障统计：\n`;
    content += `• 故障网站总数：${totalWebsites} 个\n`;
    content += `• 涉及平台类型：${platformCount} 个\n`;
    content += `• 检测时间：${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}\n\n`;

    // 按平台分组显示
    for (const [platform, websites] of Object.entries(groupedByPlatform)) {
      content += `🏷️ ${platform}（${websites.length}个站点）：\n`;

      websites.forEach((website, index) => {
        const statusCode = website.access_status_code || 0;
        const failures = website.consecutive_failures || 1;
        content += `${index + 1}. ${website.site_name}\n`;
        content += `   • URL：${website.site_url || website.url}\n`;
        content += `   • 状态码：${statusCode}\n`;
        content += `   • 连续失败：${failures}次\n`;
        if (website.server_location) {
          content += `   • 服务器：${website.server_location}\n`;
        }
        content += `\n`;
      });
    }

    content += `🔧 建议操作：\n`;
    content += `• 优先处理连续失败次数较多的站点\n`;
    content += `• 检查服务器状态和网络连接\n`;
    content += `• 联系相关技术人员进行故障排查\n`;
    content += `• 及时更新客户和相关方\n\n`;
    content += `⏰ 下次检测时间：${new Date(Date.now() + 5 * 60 * 1000).toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`;

    return {
      type: 'batch_failure',
      content: content,
      summary: {
        totalWebsites,
        platformCount,
        platforms: Object.keys(groupedByPlatform)
      },
      timestamp: new Date()
    };
  }
}

module.exports = NotificationService;
