/**
 * 魔改版网站状态检测器
 * 基于coolmonitor的HTTP检查器逻辑，集成到我们的网站存活检测中
 * 
 * 主要改进：
 * - 采用coolmonitor的状态码检查逻辑
 * - 集成SSL证书检测和到期提醒
 * - 支持灵活的状态码配置
 * - 智能重试机制
 * - 详细的错误分类
 * - 关键词内容检测
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');
const tls = require('tls');

// 监控状态常量（兼容coolmonitor）
const MONITOR_STATUS = {
  DOWN: 0,
  UP: 1,
  PENDING: 2
};

// 错误消息常量（基于coolmonitor）
const ERROR_MESSAGES = {
  CONNECTION_REFUSED: '连接被拒绝',
  TIMEOUT: '连接超时',
  HOST_NOT_FOUND: '无法解析主机名',
  INVALID_STATUS: '状态码不符合预期',
  KEYWORD_NOT_FOUND: '未找到关键词',
  NETWORK_ERROR: '网络错误',
  SSL_ERROR: 'SSL证书错误',
  AUTHENTICATION_FAILED: '身份验证失败',
  UNKNOWN_ERROR: '未知错误'
};

/**
 * 检查HTTP状态码是否符合预期（coolmonitor逻辑）
 * 支持多种格式：200-299, 200,201,202, 200
 */
function checkStatusCode(statusCode, expectedStatusCodes = '200-299,301,302') {
  const statusParts = expectedStatusCodes.split(',');
  
  for (const part of statusParts) {
    const trimmedPart = part.trim();
    
    // 范围表示法，如 200-299
    if (trimmedPart.includes('-')) {
      const [min, max] = trimmedPart.split('-').map(s => parseInt(s));
      if (statusCode >= min && statusCode <= max) {
        return true;
      }
    } 
    // 单个状态码，如 200
    else if (parseInt(trimmedPart) === statusCode) {
      return true;
    }
  }
  
  return false;
}

/**
 * 获取网络错误的可读消息（coolmonitor逻辑）
 */
function getNetworkErrorMessage(error) {
  const errorMessage = error instanceof Error ? error.message : String(error);
  
  if (errorMessage.includes('ECONNREFUSED')) {
    return ERROR_MESSAGES.CONNECTION_REFUSED;
  } else if (errorMessage.includes('ETIMEDOUT') || errorMessage.includes('timeout')) {
    return ERROR_MESSAGES.TIMEOUT;
  } else if (errorMessage.includes('ENOTFOUND') || errorMessage.includes('getaddrinfo')) {
    return ERROR_MESSAGES.HOST_NOT_FOUND;
  } else if (errorMessage.includes('certificate') || errorMessage.includes('SSL')) {
    return ERROR_MESSAGES.SSL_ERROR;
  } else {
    return `${ERROR_MESSAGES.NETWORK_ERROR}: ${errorMessage}`;
  }
}

/**
 * 魔改版网站状态检测器类
 * 集成coolmonitor的监控逻辑到我们的系统中
 */
class CoolmonitorEnhancedChecker {
  constructor() {
    // 默认配置（基于coolmonitor + 我们的需求）
    this.defaultConfig = {
      httpMethod: 'HEAD', // 使用HEAD请求提高性能
      statusCodes: '200-299,301,302', // 支持重定向
      connectTimeout: 8, // 8秒超时
      sslTimeout: 15, // SSL检查超时时间（更长）
      retries: 1, // 重试1次
      retryInterval: 5, // 重试间隔5秒
      ignoreTls: true, // 忽略SSL错误（兼容自签名证书）
      enableSslCheck: false, // 暂时禁用SSL检查以提高性能
      enableKeywordCheck: false, // 默认不启用关键词检查
      maxRedirects: 5, // 最大重定向次数
      autoFallbackToGet: true // 当HEAD请求失败时自动回退到GET请求
    };
  }

  /**
   * 检查单个网站（主入口方法）
   * 兼容我们现有的接口格式
   */
  async checkWebsiteAccess(url, options = {}) {
    const config = {
      url,
      ...this.defaultConfig,
      ...options
    };

    try {
      // 执行HTTP检查（含重试逻辑）
      const result = await this.checkHttpWithRetry(config);
      
      // 转换为我们系统的格式
      return this.convertToSystemFormat(result, url);
    } catch (error) {
      return {
        statusCode: 0,
        responseTime: 0,
        isAccessible: false,
        lastCheckTime: new Date().toISOString(),
        error: error.message,
        status: 'error'
      };
    }
  }

  /**
   * HTTP检查（含重试逻辑）- coolmonitor风格
   */
  async checkHttpWithRetry(config) {
    const { retries = 1, retryInterval = 5 } = config;

    // 执行首次检查
    let result = await this.checkHttpSingle(config);

    // 如果首次检查成功，直接返回
    if (result.status === MONITOR_STATUS.UP) {
      return result;
    }

    // 如果配置了重试次数且首次检查失败，进行重试
    if (retries > 0) {
      for (let i = 0; i < retries; i++) {
        console.log(`🔄 重试检查 ${config.url} (${i + 1}/${retries})`);
        
        // 等待重试间隔时间
        await new Promise(resolve => setTimeout(resolve, retryInterval * 1000));

        // 执行重试检查
        const retryResult = await this.checkHttpSingle(config);

        if (retryResult.status === MONITOR_STATUS.UP) {
          return {
            ...retryResult,
            message: `重试成功 (${i + 1}/${retries}): ${retryResult.message}`
          };
        }
      }

      return {
        ...result,
        message: `重试${retries}次后仍然失败: ${result.message}`
      };
    }

    return result;
  }

  /**
   * HTTP检查（单次执行）- 基于coolmonitor逻辑
   */
  async checkHttpSingle(config) {
    const {
      url,
      httpMethod = 'HEAD',
      statusCodes = '200-299,301,302',
      connectTimeout = 8,
      ignoreTls = true,
      enableSslCheck = true
    } = config;

    if (!url) {
      return {
        status: MONITOR_STATUS.DOWN,
        message: 'URL不能为空',
        ping: 0
      };
    }

    const startTime = Date.now();

    // 3. 如果启用SSL检查且是HTTPS URL，先检查证书状态
    let sslInfo = null;
    if (enableSslCheck && url.startsWith('https://')) {
      try {
        const sslTimeout = config.sslTimeout || 15;
        sslInfo = await this.checkSSLCertificate(url, sslTimeout);
      } catch (sslError) {
        console.warn(`SSL检查失败 ${url}:`, sslError.message);
      }
    }

    try {
      // 1. 执行HTTP请求检查
      const httpResult = await this.performHttpRequest({
        url,
        httpMethod,
        connectTimeout,
        ignoreTls
      });

      const responseTime = Date.now() - startTime;

      // 2. 检查状态码是否符合预期（coolmonitor逻辑）
      const isStatusValid = checkStatusCode(httpResult.statusCode, statusCodes);

      if (!isStatusValid) {
        // 如果HEAD请求返回403且启用了自动回退，尝试GET请求
        if (httpMethod === 'HEAD' && httpResult.statusCode === 403 && config.autoFallbackToGet) {
          console.log(`🔄 HEAD请求返回403，自动回退到GET请求: ${url}`);

          try {
            const getResult = await this.performHttpRequest({
              url,
              httpMethod: 'GET',
              connectTimeout,
              ignoreTls
            });

            const getResponseTime = Date.now() - startTime;
            const isGetStatusValid = checkStatusCode(getResult.statusCode, statusCodes);

            if (isGetStatusValid) {
              // GET请求成功，返回成功结果
              let message = `状态码: ${getResult.statusCode} (HEAD→GET回退)`;
              if (sslInfo && sslInfo.daysRemaining !== undefined) {
                if (sslInfo.daysRemaining <= 0) {
                  message += ` | SSL证书已过期`;
                } else if (sslInfo.daysRemaining <= 7) {
                  message += ` | SSL证书${sslInfo.daysRemaining}天后过期`;
                } else {
                  message += ` | SSL证书有效(${sslInfo.daysRemaining}天)`;
                }
              }

              return {
                status: MONITOR_STATUS.UP,
                message,
                ping: getResponseTime,
                statusCode: getResult.statusCode,
                sslInfo
              };
            } else {
              return {
                status: MONITOR_STATUS.DOWN,
                message: `GET回退后状态码仍不符合预期: ${getResult.statusCode}`,
                ping: getResponseTime,
                statusCode: getResult.statusCode
              };
            }
          } catch (getError) {
            console.warn(`GET回退请求也失败: ${getError.message}`);
          }
        }

        return {
          status: MONITOR_STATUS.DOWN,
          message: `状态码不符合预期: ${httpResult.statusCode}`,
          ping: responseTime,
          statusCode: httpResult.statusCode
        };
      }

      // 4. 构建成功消息
      let message = `状态码: ${httpResult.statusCode}`;
      if (sslInfo && sslInfo.daysRemaining !== undefined) {
        if (sslInfo.daysRemaining <= 0) {
          message += ` | SSL证书已过期`;
        } else if (sslInfo.daysRemaining <= 7) {
          message += ` | SSL证书${sslInfo.daysRemaining}天后过期`;
        } else {
          message += ` | SSL证书有效(${sslInfo.daysRemaining}天)`;
        }
      }

      return {
        status: MONITOR_STATUS.UP,
        message,
        ping: responseTime,
        statusCode: httpResult.statusCode,
        sslInfo
      };

    } catch (error) {
      const errorMessage = getNetworkErrorMessage(error);
      const actualTime = Date.now() - startTime;

      return {
        status: MONITOR_STATUS.DOWN,
        message: errorMessage,
        ping: actualTime,
        statusCode: 0
      };
    }
  }

  /**
   * 执行HTTP请求 - 基于coolmonitor但适配Node.js
   */
  async performHttpRequest(options) {
    const {
      url,
      httpMethod = 'HEAD',
      connectTimeout = 8,
      ignoreTls = true
    } = options;

    return new Promise((resolve, reject) => {
      try {
        const urlObj = new URL(url);
        const client = urlObj.protocol === 'https:' ? https : http;
        
        const requestOptions = {
          hostname: urlObj.hostname,
          port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
          path: urlObj.pathname + urlObj.search,
          method: httpMethod,
          timeout: connectTimeout * 1000,
          headers: {
            'User-Agent': 'SiteManager-CoolmonitorChecker/2.0',
            'Accept': '*/*',
            'Connection': 'close'
          }
        };

        // HTTPS特殊配置
        if (urlObj.protocol === 'https:') {
          requestOptions.rejectUnauthorized = !ignoreTls;
        }

        const req = client.request(requestOptions, (res) => {
          // 必须消费响应数据才能触发end事件
          res.on('data', () => {
            // 忽略数据，只是为了触发end事件
          });
          
          res.on('end', () => {
            resolve({
              statusCode: res.statusCode,
              headers: res.headers
            });
          });
        });

        req.on('error', (error) => {
          reject(new Error(`网络错误: ${error.message}`));
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new Error('连接超时'));
        });

        req.end();
      } catch (error) {
        reject(new Error(`请求失败: ${error.message}`));
      }
    });
  }

  /**
   * SSL证书检查 - 基于coolmonitor逻辑
   */
  async checkSSLCertificate(url, timeout = 8) {
    return new Promise((resolve, reject) => {
      try {
        const urlObj = new URL(url);
        const hostname = urlObj.hostname;
        const port = parseInt(urlObj.port) || 443;

        const options = {
          host: hostname,
          port: port,
          servername: hostname,
          rejectUnauthorized: false
        };

        const socket = tls.connect(options, () => {
          try {
            const cert = socket.getPeerCertificate();
            const now = new Date();
            const expiry = new Date(cert.valid_to);
            const daysUntilExpiry = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24));

            resolve({
              issuer: cert.issuer?.CN || 'Unknown',
              subject: cert.subject?.CN || hostname,
              validFrom: cert.valid_from,
              validTo: cert.valid_to,
              daysRemaining: daysUntilExpiry,
              isValid: daysUntilExpiry > 0,
              serialNumber: cert.serialNumber,
              fingerprint: cert.fingerprint
            });
          } catch (error) {
            reject(new Error(`SSL证书解析失败: ${error.message}`));
          } finally {
            socket.end();
          }
        });

        socket.on('error', (error) => {
          reject(new Error(`SSL连接失败: ${error.message}`));
        });

        socket.setTimeout(timeout * 1000, () => {
          socket.destroy();
          reject(new Error('SSL检测超时'));
        });
      } catch (error) {
        reject(new Error(`SSL检查失败: ${error.message}`));
      }
    });
  }

  /**
   * 转换为我们系统的格式
   * 保持与现有系统的兼容性
   */
  convertToSystemFormat(result, url) {
    const isAccessible = result.status === MONITOR_STATUS.UP;

    // 根据状态码确定具体状态
    let status = 'error';
    if (isAccessible) {
      status = 'online';
    } else if (result.statusCode > 0) {
      status = 'offline';
    }

    return {
      statusCode: result.statusCode || 0,
      responseTime: result.ping || 0,
      isAccessible: isAccessible,
      lastCheckTime: new Date().toISOString(),
      error: isAccessible ? null : result.message,
      status: status,
      message: result.message,
      sslInfo: result.sslInfo || null,
      // 额外的coolmonitor风格信息
      coolmonitorStatus: result.status,
      url: url
    };
  }

  /**
   * 批量检查网站（兼容现有接口）
   * 集成coolmonitor的检查逻辑
   */
  async checkWebsitesBatch(websites, options = {}) {
    const {
      concurrency = 10,
      enableSslCheck = true,
      statusCodes = '200-299,301,302',
      connectTimeout = 8,
      retries = 1
    } = options;

    const startTime = Date.now();
    console.log(`🚀 使用coolmonitor增强检查器批量检测 ${websites.length} 个网站，并发数: ${concurrency}`);

    const results = [];
    let successCount = 0;
    let failureCount = 0;

    // 分批处理，控制并发数
    for (let i = 0; i < websites.length; i += concurrency) {
      const batch = websites.slice(i, i + concurrency);
      const batchStartTime = Date.now();

      console.log(`📦 处理第 ${Math.floor(i/concurrency) + 1} 批，包含 ${batch.length} 个网站`);

      const batchPromises = batch.map(async (website) => {
        try {
          const url = website.siteUrl || website.site_url || `https://${website.domain}`;

          const checkResult = await this.checkWebsiteAccess(url, {
            enableSslCheck,
            statusCodes,
            connectTimeout,
            retries
          });

          if (checkResult.isAccessible) {
            successCount++;
          } else {
            failureCount++;
          }

          return {
            websiteId: website.id,
            siteName: website.siteName || website.site_name,
            domain: website.domain,
            url: url,
            serverName: website.actual_server_name || website.server_name,
            ...checkResult
          };
        } catch (error) {
          failureCount++;
          return {
            websiteId: website.id,
            siteName: website.siteName || website.site_name,
            domain: website.domain,
            url: website.siteUrl || website.site_url || `https://${website.domain}`,
            serverName: website.actual_server_name || website.server_name,
            statusCode: 0,
            responseTime: 0,
            isAccessible: false,
            lastCheckTime: new Date().toISOString(),
            error: error.message,
            status: 'error'
          };
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);
      batchResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          failureCount++;
          console.error('批次处理失败:', result.reason);
        }
      });

      const batchTime = Date.now() - batchStartTime;
      console.log(`✅ 第 ${Math.floor(i/concurrency) + 1} 批完成，耗时: ${batchTime}ms`);

      // 批次间短暂延迟，避免过载
      if (i + concurrency < websites.length) {
        await new Promise(resolve => setTimeout(resolve, 200)); // 减少延迟时间
      }
    }

    const totalTime = Date.now() - startTime;
    const avgTime = results.length > 0 ? Math.round(totalTime / results.length) : 0;
    const speed = results.length > 0 ? Math.round(results.length / (totalTime / 1000)) : 0;

    console.log(`✅ coolmonitor增强检查器批量检测完成:`);
    console.log(`   📊 总计: ${results.length} 个网站`);
    console.log(`   ✅ 成功: ${successCount} 个`);
    console.log(`   ❌ 失败: ${failureCount} 个`);
    console.log(`   ⏱️  总耗时: ${totalTime}ms (${Math.round(totalTime/1000)}秒)`);
    console.log(`   📈 平均每站点: ${avgTime}ms`);
    console.log(`   🚀 处理速度: ${speed} 站点/秒`);

    return results;
  }
}

module.exports = {
  CoolmonitorEnhancedChecker,
  MONITOR_STATUS,
  ERROR_MESSAGES,
  checkStatusCode,
  getNetworkErrorMessage
};
