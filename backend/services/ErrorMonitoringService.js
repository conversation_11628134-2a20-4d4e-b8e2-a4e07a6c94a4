/**
 * 错误监控服务
 * 用于监控系统错误，记录日志，发送告警
 */

const fs = require('fs').promises;
const path = require('path');

class ErrorMonitoringService {
  constructor(db) {
    this.db = db;
    this.errorLogPath = path.join(__dirname, '../logs/errors.log');
    this.maxLogSize = 10 * 1024 * 1024; // 10MB
    this.maxLogFiles = 5;
  }

  /**
   * 记录错误
   */
  async logError(error, context = {}) {
    try {
      const errorInfo = {
        timestamp: new Date().toISOString(),
        message: error.message,
        stack: error.stack,
        context,
        level: 'error'
      };

      // 写入日志文件
      await this.writeToLogFile(errorInfo);

      // 写入数据库
      await this.writeToDatabase(errorInfo);

      // 检查是否需要发送告警
      await this.checkAndSendAlert(errorInfo);

    } catch (logError) {
      console.error('❌ 记录错误失败:', logError);
    }
  }

  /**
   * 记录警告
   */
  async logWarning(message, context = {}) {
    try {
      const warningInfo = {
        timestamp: new Date().toISOString(),
        message,
        context,
        level: 'warning'
      };

      await this.writeToLogFile(warningInfo);
      await this.writeToDatabase(warningInfo);

    } catch (logError) {
      console.error('❌ 记录警告失败:', logError);
    }
  }

  /**
   * 记录信息
   */
  async logInfo(message, context = {}) {
    try {
      const infoLog = {
        timestamp: new Date().toISOString(),
        message,
        context,
        level: 'info'
      };

      await this.writeToLogFile(infoLog);

    } catch (logError) {
      console.error('❌ 记录信息失败:', logError);
    }
  }

  /**
   * 写入日志文件
   */
  async writeToLogFile(logInfo) {
    try {
      // 检查日志文件大小，必要时轮转
      await this.rotateLogIfNeeded();

      const logLine = JSON.stringify(logInfo) + '\n';
      await fs.appendFile(this.errorLogPath, logLine, 'utf8');

    } catch (error) {
      console.error('❌ 写入日志文件失败:', error);
    }
  }

  /**
   * 写入数据库
   */
  async writeToDatabase(logInfo) {
    try {
      // 确保错误日志表存在
      await this.ensureErrorLogTable();

      await this.db.execute(`
        INSERT INTO error_logs (level, message, stack_trace, context, created_at)
        VALUES (?, ?, ?, ?, ?)
      `, [
        logInfo.level,
        logInfo.message,
        logInfo.stack || null,
        JSON.stringify(logInfo.context),
        logInfo.timestamp
      ]);

    } catch (error) {
      console.error('❌ 写入数据库失败:', error);
    }
  }

  /**
   * 确保错误日志表存在
   */
  async ensureErrorLogTable() {
    try {
      await this.db.execute(`
        CREATE TABLE IF NOT EXISTS error_logs (
          id INT AUTO_INCREMENT PRIMARY KEY,
          level ENUM('error', 'warning', 'info') NOT NULL,
          message TEXT NOT NULL,
          stack_trace TEXT,
          context JSON,
          created_at DATETIME NOT NULL,
          INDEX idx_level_created (level, created_at),
          INDEX idx_created (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
    } catch (error) {
      console.error('❌ 创建错误日志表失败:', error);
    }
  }

  /**
   * 日志轮转
   */
  async rotateLogIfNeeded() {
    try {
      const stats = await fs.stat(this.errorLogPath).catch(() => null);
      
      if (!stats || stats.size < this.maxLogSize) {
        return;
      }

      // 轮转日志文件
      for (let i = this.maxLogFiles - 1; i >= 1; i--) {
        const oldFile = `${this.errorLogPath}.${i}`;
        const newFile = `${this.errorLogPath}.${i + 1}`;
        
        try {
          await fs.rename(oldFile, newFile);
        } catch (error) {
          // 文件不存在，忽略错误
        }
      }

      // 重命名当前日志文件
      await fs.rename(this.errorLogPath, `${this.errorLogPath}.1`);

    } catch (error) {
      console.error('❌ 日志轮转失败:', error);
    }
  }

  /**
   * 检查并发送告警
   */
  async checkAndSendAlert(errorInfo) {
    try {
      // 检查最近5分钟内的错误数量
      const [errorCount] = await this.db.execute(`
        SELECT COUNT(*) as count
        FROM error_logs
        WHERE level = 'error' 
          AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
      `);

      const recentErrors = errorCount[0].count;

      // 如果错误数量超过阈值，发送告警
      if (recentErrors >= 10) {
        await this.sendErrorAlert(recentErrors, errorInfo);
      }

    } catch (error) {
      console.error('❌ 检查告警失败:', error);
    }
  }

  /**
   * 发送错误告警
   */
  async sendErrorAlert(errorCount, latestError) {
    try {
      // 检查是否已经发送过告警（避免重复发送）
      const [lastAlert] = await this.db.execute(`
        SELECT created_at
        FROM error_logs
        WHERE level = 'info' 
          AND message LIKE '%错误告警%'
          AND created_at > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
        ORDER BY created_at DESC
        LIMIT 1
      `);

      if (lastAlert.length > 0) {
        return; // 30分钟内已发送过告警
      }

      // 记录告警信息
      await this.logInfo(`系统错误告警: 最近5分钟内发生 ${errorCount} 个错误`, {
        errorCount,
        latestError: latestError.message
      });

      console.warn(`🚨 系统错误告警: 最近5分钟内发生 ${errorCount} 个错误`);
      console.warn(`🚨 最新错误: ${latestError.message}`);

    } catch (error) {
      console.error('❌ 发送错误告警失败:', error);
    }
  }

  /**
   * 获取错误统计
   */
  async getErrorStats(hours = 24) {
    try {
      const [stats] = await this.db.execute(`
        SELECT 
          level,
          COUNT(*) as count,
          MAX(created_at) as latest_occurrence
        FROM error_logs
        WHERE created_at > DATE_SUB(NOW(), INTERVAL ? HOUR)
        GROUP BY level
        ORDER BY count DESC
      `, [hours]);

      return stats;
    } catch (error) {
      console.error('❌ 获取错误统计失败:', error);
      return [];
    }
  }

  /**
   * 获取最近的错误
   */
  async getRecentErrors(limit = 50) {
    try {
      const [errors] = await this.db.execute(`
        SELECT level, message, stack_trace, context, created_at
        FROM error_logs
        WHERE level IN ('error', 'warning')
        ORDER BY created_at DESC
        LIMIT ?
      `, [limit]);

      return errors.map(error => ({
        ...error,
        context: error.context ? JSON.parse(error.context) : {}
      }));
    } catch (error) {
      console.error('❌ 获取最近错误失败:', error);
      return [];
    }
  }

  /**
   * 清理旧的错误日志
   */
  async cleanupOldLogs(days = 30) {
    try {
      const [result] = await this.db.execute(`
        DELETE FROM error_logs
        WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
      `, [days]);

      if (result.affectedRows > 0) {
        console.log(`🧹 已清理 ${result.affectedRows} 条过期的错误日志`);
      }

      return result.affectedRows;
    } catch (error) {
      console.error('❌ 清理错误日志失败:', error);
      return 0;
    }
  }

  /**
   * 创建错误处理中间件（Express）
   */
  createErrorMiddleware() {
    return async (error, req, res, next) => {
      // 记录错误
      await this.logError(error, {
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        body: req.body
      });

      // 返回错误响应
      res.status(500).json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    };
  }

  /**
   * 创建未捕获异常处理器
   */
  setupUncaughtExceptionHandler() {
    process.on('uncaughtException', async (error) => {
      console.error('❌ 未捕获的异常:', error);
      await this.logError(error, { type: 'uncaughtException' });
      
      // 给一些时间让日志写入完成
      setTimeout(() => {
        process.exit(1);
      }, 1000);
    });

    process.on('unhandledRejection', async (reason, promise) => {
      console.error('❌ 未处理的Promise拒绝:', reason);
      await this.logError(new Error(reason), { 
        type: 'unhandledRejection',
        promise: promise.toString()
      });
    });
  }
}

module.exports = ErrorMonitoringService;