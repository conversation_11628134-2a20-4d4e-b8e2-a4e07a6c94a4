const WebsiteCheckerService = require('./website-checker');
const NotificationService = require('./NotificationService');
const { CoolmonitorEnhancedChecker } = require('./coolmonitor-enhanced-checker');
const CurlWebsiteChecker = require('./curl-website-checker');
const FeishuNotificationService = require('./FeishuNotificationService');

/**
 * 网站状态检测服务
 * 专门负责网站存活状态检测、数据存储和通知发送
 */
class WebsiteStatusService {
  constructor(db) {
    this.db = db;
    this.websiteChecker = new WebsiteCheckerService();
    this.coolmonitorChecker = new CoolmonitorEnhancedChecker(); // 新增coolmonitor增强检查器
    this.curlChecker = new CurlWebsiteChecker(); // 新增curl检查器
    this.notificationService = new NotificationService();
    this.feishuNotificationService = new FeishuNotificationService(db); // 新增飞书通知服务
    this.notificationThreshold = 5; // 连续失败5次发送通知

    // 初始化时加载通知配置
    this.initializeNotificationService();
  }

  /**
   * 初始化通知服务
   */
  async initializeNotificationService() {
    try {
      await this.notificationService.loadConfigFromDatabase();
      console.log('✅ 通知服务初始化完成');
    } catch (error) {
      console.error('⚠️  通知服务初始化失败:', error);
    }
  }

  /**
   * 执行完整的网站状态检测流程
   */
  async performStatusCheck() {
    const startTime = Date.now();
    console.log('🌍 开始执行网站状态检测...');

    try {
      // 1. 获取所有活跃网站
      const activeWebsites = await this.getActiveWebsites();
      console.log(`🔍 找到 ${activeWebsites.length} 个活跃网站需要检测`);

      if (activeWebsites.length === 0) {
        console.log('ℹ️  没有需要检测的网站');
        return;
      }

      // 2. 执行高性能批量检测
      const checkResults = await this.performBatchCheck(activeWebsites);
      
      // 3. 批量更新状态统计
      await this.updateStatusStatistics(checkResults);
      
      // 4. 同步状态到websites表
      await this.syncStatusToWebsitesTable(checkResults);
      
      // 5. 检查并发送通知
      await this.checkAndSendNotifications();

      // 6. 发送飞书异常通知（表格格式）
      await this.sendFeishuAbnormalNotification();
      
      // 6. 清理旧记录
      await this.cleanOldRecords();

      const totalTime = Date.now() - startTime;
      console.log(`🎯 网站状态检测完成，总耗时: ${totalTime}ms`);
      
      return {
        totalWebsites: activeWebsites.length,
        totalTime,
        avgTimePerSite: Math.round(totalTime / activeWebsites.length)
      };

    } catch (error) {
      console.error('❌ 网站状态检测失败:', error);
      throw error;
    }
  }

  /**
   * 获取待检测网站（每次最多20个，与PHP文档系统一致）
   */
  async getActiveWebsites() {
    try {
      const checkInterval = 600; // 10分钟间隔（秒）

      const [rows] = await this.db.execute(`
        SELECT
          w.id,
          w.site_name as siteName,
          w.domain,
          w.site_url as siteUrl,
          w.server_id,
          w.status_check,
          w.ssl_check,
          w.tries,
          w.last_check_time,
          w.consecutive_failures,
          s.name as actual_server_name,
          s.ip_address as server_ip
        FROM websites w
        LEFT JOIN servers s ON w.server_id = s.id
        WHERE w.status = 'active'
          AND w.status_check = 1
          AND w.site_url IS NOT NULL
          AND w.site_url != ''
          AND (
            w.last_check_time IS NULL OR
            w.last_check_time < DATE_SUB(NOW(), INTERVAL ? SECOND)
          )
        ORDER BY w.tries ASC, w.last_check_time ASC
        LIMIT 20
      `, [checkInterval]);

      return rows;
    } catch (error) {
      console.error('获取活跃网站失败:', error);
      throw error;
    }
  }

  /**
   * 执行批量检测（使用curl检测器）
   */
  async performBatchCheck(websites) {
    try {
      console.log(`🚀 开始批量检测 ${websites.length} 个网站`);
      console.log('🔧 使用curl检测器模式（高性能、高可靠性）');

      // 提取URL列表
      const urls = websites.map(w => w.siteUrl).filter(url => url && url.trim());
      console.log(`📋 有效URL数量: ${urls.length}`);

      // 固定批量处理数量（与PHP文档系统一致）
      const batchSize = 20;
      const concurrency = 10; // 保持合理的并发数

      console.log(`📊 检测 ${urls.length} 个网站，使用批量大小 ${batchSize}，并发数 ${concurrency}`);

      // 使用curl检测器进行批量检测
      const curlResults = await this.curlChecker.checkWebsites(urls, concurrency);

      // 将curl结果转换为标准格式
      const results = websites.map(website => {
        const curlResult = curlResults.find(r => r.url === website.siteUrl);

        if (!curlResult) {
          return {
            websiteId: website.id,
            statusCode: 0,
            responseTime: 0,
            isSuccess: false,
            isAccessible: false, // 添加isAccessible字段
            error: 'URL无效或为空'
          };
        }

        return {
          websiteId: website.id,
          statusCode: curlResult.statusCode,
          responseTime: curlResult.responseTime,
          isSuccess: curlResult.success && curlResult.statusCode >= 200 && curlResult.statusCode < 400,
          isAccessible: curlResult.success && curlResult.statusCode >= 200 && curlResult.statusCode < 400, // 添加isAccessible字段
          error: curlResult.error,
          ssl: curlResult.ssl
        };
      });

      console.log(`✅ 批量检测完成: ${results.length} 个结果`);
      return results;
    } catch (error) {
      console.error('批量检测失败:', error);
      throw error;
    }
  }

  /**
   * 传统并发检测（魔改版 - 使用coolmonitor逻辑）
   */
  async performTraditionalCheck(websites) {
    console.log('🔄 传统检测模式已被coolmonitor增强检查器替代');

    // 直接使用coolmonitor增强检查器，不再使用旧的检测逻辑
    return await this.coolmonitorChecker.checkWebsitesBatch(websites, {
      concurrency: 20, // 传统模式使用更高的并发数
      enableSslCheck: true,
      statusCodes: '200-299,301,302',
      connectTimeout: 8,
      retries: 1
    });
  }

  /**
   * 批量更新状态统计
   */
  async updateStatusStatistics(checkResults) {
    console.log(`📊 更新 ${checkResults.length} 个网站的状态统计`);

    const updatePromises = checkResults.map(async (result) => {
      try {
        // 插入检测记录
        await this.db.execute(`
          INSERT INTO website_status_checks (
            website_id, status_code, response_time, is_accessible, error_message, check_type
          ) VALUES (?, ?, ?, ?, ?, 'scheduled')
        `, [
          result.websiteId,
          result.statusCode || 0,
          result.responseTime || 0,
          result.isAccessible || false,
          result.error || null
        ]);

        // 更新或插入状态统计
        if (result.isAccessible) {
          // 网站正常，重置异常计数
          await this.db.execute(`
            INSERT INTO website_status_stats (
              website_id, consecutive_failures, total_checks, success_checks,
              last_success_time, current_status_code, current_response_time
            ) VALUES (?, 0, 1, 1, NOW(), ?, ?)
            ON DUPLICATE KEY UPDATE
              total_checks = total_checks + 1,
              success_checks = success_checks + 1,
              consecutive_failures = 0,
              last_success_time = NOW(),
              first_failure_time = NULL,
              current_status_code = VALUES(current_status_code),
              current_response_time = VALUES(current_response_time),
              notification_sent = FALSE
          `, [result.websiteId, result.statusCode || 0, result.responseTime || 0]);
        } else {
          // 网站异常，增加异常计数
          await this.db.execute(`
            INSERT INTO website_status_stats (
              website_id, consecutive_failures, total_checks, success_checks,
              last_failure_time, first_failure_time, current_status_code, current_response_time
            ) VALUES (?, 1, 1, 0, NOW(), NOW(), ?, ?)
            ON DUPLICATE KEY UPDATE
              total_checks = total_checks + 1,
              consecutive_failures = consecutive_failures + 1,
              last_failure_time = NOW(),
              first_failure_time = COALESCE(first_failure_time, NOW()),
              current_status_code = VALUES(current_status_code),
              current_response_time = VALUES(current_response_time)
          `, [result.websiteId, result.statusCode || 0, result.responseTime || 0]);
        }

      } catch (error) {
        console.error(`更新网站 ${result.websiteId} 状态统计失败:`, error);
      }
    });

    await Promise.allSettled(updatePromises);
    console.log('✅ 状态统计更新完成');
  }

  /**
   * 同步状态到websites表（保持兼容性）
   */
  async syncStatusToWebsitesTable(checkResults) {
    console.log('🔄 同步状态到websites表');

    try {
      // 批量更新websites表的access_status_code字段（使用coolmonitor逻辑）
      const updatePromises = checkResults.map(async (result) => {
        try {
          // 使用coolmonitor的isAccessible判断结果，而不是简单的状态码范围
          const accessStatus = result.isAccessible ? 'online' :
                              (result.statusCode === 0 ? 'offline' : 'error');

          // 获取当前网站的连续失败次数，用于同步到websites表
          const [statsRows] = await this.db.execute(`
            SELECT consecutive_failures FROM website_status_stats WHERE website_id = ?
          `, [result.websiteId]);

          const consecutiveFailures = statsRows.length > 0 ? statsRows[0].consecutive_failures : 0;

          await this.db.execute(`
            UPDATE websites
            SET
              access_status_code = ?,
              access_status = ?,
              response_time = ?,
              consecutive_failures = ?,
              last_check_time = NOW()
            WHERE id = ?
          `, [
            result.statusCode,
            accessStatus,
            result.responseTime || 0,
            consecutiveFailures,
            result.websiteId
          ]);
        } catch (error) {
          console.error(`更新网站 ${result.websiteId} 状态失败:`, error.message);
        }
      });

      await Promise.allSettled(updatePromises);

      const successCount = checkResults.filter(r => r.isAccessible).length;
      const failCount = checkResults.filter(r => !r.isAccessible).length;

      console.log(`✅ 状态同步完成: 成功 ${successCount} 个, 失败 ${failCount} 个`);
    } catch (error) {
      console.error('❌ 同步状态到websites表失败:', error);
    }
  }

  /**
   * 检查并发送通知
   */
  async checkAndSendNotifications() {
    try {
      console.log('🔔 检查需要发送通知的网站');

      // 获取需要通知的网站，包含平台类型信息
      const [websites] = await this.db.execute(`
        SELECT
          w.id,
          w.site_name,
          w.site_url,
          w.domain,
          p.name as platform_type,
          ws.consecutive_failures,
          ws.first_failure_time,
          ws.last_failure_time,
          ws.current_status_code,
          w.access_status_code,
          TIMESTAMPDIFF(MINUTE, ws.first_failure_time, NOW()) as error_duration_minutes,
          CONCAT(
            TIMESTAMPDIFF(HOUR, ws.first_failure_time, NOW()), '小时',
            MOD(TIMESTAMPDIFF(MINUTE, ws.first_failure_time, NOW()), 60), '分钟'
          ) as error_duration_display,
          COALESCE(s.name, '未知服务器') as server_name,
          COALESCE(s.location, '未知位置') as server_location
        FROM websites w
        INNER JOIN website_status_stats ws ON w.id = ws.website_id
        LEFT JOIN platforms p ON w.platform_id = p.platform_id
        LEFT JOIN servers s ON w.server_id = s.id
        WHERE w.status = 'active'
        AND ws.consecutive_failures >= ?
        AND ws.notification_sent = FALSE
        ORDER BY ws.consecutive_failures DESC, ws.first_failure_time ASC
      `, [this.notificationThreshold]);

      if (websites.length === 0) {
        console.log('ℹ️  没有需要发送通知的网站');
        return;
      }

      console.log(`📢 发现 ${websites.length} 个网站需要发送通知`);

      // 使用批量通知模式
      if (websites.length > 1) {
        await this.sendBatchNotifications(websites);
      } else {
        // 单个网站仍使用原有方式
        await this.sendWebsiteNotification(websites[0]);

        // 标记通知已发送
        await this.db.execute(`
          UPDATE website_status_stats
          SET notification_sent = TRUE,
              notification_count = notification_count + 1,
              last_notification_time = NOW()
          WHERE website_id = ?
        `, [websites[0].id]);
      }

      console.log(`🎯 通知发送完成: ${websites.length} 个通知已发送`);

    } catch (error) {
      console.error('检查和发送通知失败:', error);
    }
  }

  /**
   * 发送飞书异常通知（表格格式）
   */
  async sendFeishuAbnormalNotification() {
    try {
      console.log('📱 开始检查飞书异常通知...');

      const result = await this.feishuNotificationService.sendWebsiteAbnormalNotification();

      if (result.success) {
        console.log('✅ 飞书异常通知发送成功');
      } else {
        console.log(`ℹ️  飞书异常通知: ${result.reason || result.message || '未发送'}`);
      }

      return result;
    } catch (error) {
      console.error('❌ 发送飞书异常通知失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 发送批量通知
   */
  async sendBatchNotifications(websites) {
    try {
      // 使用通知服务发送批量通知
      const result = await this.notificationService.sendBatchWebsiteFailureNotification(websites);

      if (result.success) {
        console.log(`📤 批量通知发送成功: ${result.websiteCount} 个网站, ${result.platformCount} 个平台`);

        // 标记所有网站的通知已发送
        const websiteIds = websites.map(w => w.id);
        await this.db.execute(`
          UPDATE website_status_stats
          SET notification_sent = TRUE,
              notification_count = notification_count + 1,
              last_notification_time = NOW()
          WHERE website_id IN (${websiteIds.map(() => '?').join(',')})
        `, websiteIds);

        // 输出每个网站的通知信息
        websites.forEach(website => {
          console.log(`📢 已发送通知: ${website.site_name} (连续失败${website.consecutive_failures}次)`);
        });

      } else {
        console.error('❌ 批量通知发送失败:', result.message);
      }

    } catch (error) {
      console.error('❌ 发送批量通知失败:', error);

      // 批量通知失败时，回退到逐个发送
      console.log('🔄 回退到逐个发送通知模式');
      for (const website of websites) {
        try {
          await this.sendWebsiteNotification(website);

          await this.db.execute(`
            UPDATE website_status_stats
            SET notification_sent = TRUE,
                notification_count = notification_count + 1,
                last_notification_time = NOW()
            WHERE website_id = ?
          `, [website.id]);

        } catch (singleError) {
          console.error(`发送网站 ${website.site_name} 通知失败:`, singleError);
        }
      }
    }
  }

  /**
   * 发送网站异常通知
   */
  async sendWebsiteNotification(website) {
    try {
      // 构建网站数据对象
      const websiteData = {
        id: website.id,
        site_name: website.site_name,
        domain: website.domain,
        url: website.site_url || `https://${website.domain}`,
        server_name: '未知服务器',
        server_location: '未知位置'
      };

      // 构建错误数据对象
      const errorData = {
        message: website.current_status_code ? `HTTP ${website.current_status_code}` : '网站无法访问',
        statusCode: website.current_status_code || 0,
        responseTime: 0,
        errorCount: website.consecutive_failures,
        duration: website.error_duration_display
      };

      // 使用新的通知服务发送故障通知
      await this.notificationService.sendWebsiteFailureNotification(websiteData, errorData);

      console.log(`📢 已发送通知: ${website.site_name} (连续失败${website.consecutive_failures}次)`);
    } catch (error) {
      console.error(`发送网站 ${website.site_name} 通知失败:`, error);
      throw error;
    }
  }

  /**
   * 清理旧记录
   */
  async cleanOldRecords() {
    try {
      // 删除30天前的检测记录（简单删除，保留最近30天的数据）
      const [result] = await this.db.execute(`
        DELETE FROM website_status_checks
        WHERE check_time < DATE_SUB(NOW(), INTERVAL 30 DAY)
      `);

      const deletedCount = result.affectedRows || 0;

      if (deletedCount > 0) {
        console.log(`🧹 清理了 ${deletedCount} 条旧的检测记录`);
      }
    } catch (error) {
      console.error('清理旧记录失败:', error);
    }
  }

  /**
   * 快速网站访问检测
   */
  async checkWebsiteAccessFast(url) {
    const https = require('https');
    const http = require('http');
    const { URL } = require('url');

    return new Promise((resolve) => {
      try {
        const urlObj = new URL(url);
        const client = urlObj.protocol === 'https:' ? https : http;
        const startTime = Date.now();

        const options = {
          hostname: urlObj.hostname,
          port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
          path: urlObj.pathname + urlObj.search,
          method: 'HEAD',
          timeout: 5000,
          headers: {
            'User-Agent': 'SiteManager-StatusChecker/1.0',
            'Connection': 'close'
          },
          rejectUnauthorized: false
        };

        const req = client.request(options, (res) => {
          const responseTime = Date.now() - startTime;
          res.destroy();

          resolve({
            statusCode: res.statusCode,
            responseTime: responseTime,
            isAccessible: res.statusCode >= 200 && res.statusCode < 400,
            lastCheckTime: new Date().toISOString()
          });
        });

        req.on('error', (error) => {
          const responseTime = Date.now() - startTime;
          resolve({
            statusCode: 0,
            responseTime: responseTime,
            isAccessible: false,
            lastCheckTime: new Date().toISOString(),
            error: error.message
          });
        });

        req.on('timeout', () => {
          req.destroy();
          const responseTime = Date.now() - startTime;
          resolve({
            statusCode: 0,
            responseTime: responseTime,
            isAccessible: false,
            lastCheckTime: new Date().toISOString(),
            error: 'Request timeout'
          });
        });

        req.end();
      } catch (error) {
        resolve({
          statusCode: 0,
          responseTime: 0,
          isAccessible: false,
          lastCheckTime: new Date().toISOString(),
          error: error.message
        });
      }
    });
  }

  /**
   * 数组分块工具函数
   */
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 获取网站状态统计
   */
  async getWebsiteStatusStats(websiteId = null) {
    try {
      let query = `
        SELECT
          w.id,
          w.site_name,
          w.domain,
          w.site_url,
          w.server_id,
          s.name as actual_server_name,
          s.ip_address as server_ip,
          COALESCE(ws.consecutive_failures, 0) as consecutive_failures,
          COALESCE(ws.total_checks, 0) as total_checks,
          COALESCE(ws.success_checks, 0) as success_checks,
          ws.last_success_time,
          ws.last_failure_time,
          ws.first_failure_time,
          COALESCE(ws.current_status_code, 0) as current_status_code,
          COALESCE(ws.current_response_time, 0) as current_response_time,
          COALESCE(ws.notification_sent, FALSE) as notification_sent,
          COALESCE(ws.notification_count, 0) as notification_count,
          ws.last_notification_time,
          CASE
            WHEN COALESCE(ws.consecutive_failures, 0) = 0 THEN '正常'
            WHEN COALESCE(ws.consecutive_failures, 0) >= 5 THEN CONCAT('异常 (', ws.consecutive_failures, '次)')
            ELSE CONCAT('警告 (', ws.consecutive_failures, '次)')
          END as status_display,
          CASE
            WHEN COALESCE(ws.consecutive_failures, 0) = 0 THEN 'success'
            WHEN COALESCE(ws.consecutive_failures, 0) >= 5 THEN 'error'
            ELSE 'warning'
          END as status_type,
          CASE
            WHEN ws.last_failure_time IS NOT NULL AND ws.first_failure_time IS NOT NULL
            THEN TIMESTAMPDIFF(MINUTE, ws.first_failure_time, COALESCE(ws.last_success_time, NOW()))
            ELSE 0
          END as error_duration_minutes,
          CASE
            WHEN COALESCE(ws.total_checks, 0) > 0
            THEN ROUND((COALESCE(ws.success_checks, 0) / ws.total_checks) * 100, 2)
            ELSE 0
          END as success_rate
        FROM websites w
        LEFT JOIN website_status_stats ws ON w.id = ws.website_id
        LEFT JOIN servers s ON w.server_id = s.id
        WHERE w.status = 'active'
      `;
      let params = [];

      if (websiteId) {
        query += ' AND w.id = ?';
        params.push(websiteId);
      }

      query += ' ORDER BY consecutive_failures DESC, last_failure_time DESC';

      const [rows] = await this.db.execute(query, params);
      return rows;
    } catch (error) {
      console.error('获取网站状态统计失败:', error);
      throw error;
    }
  }

  /**
   * 获取网站检测历史
   */
  async getWebsiteCheckHistory(websiteId, limit = 100) {
    try {
      const [rows] = await this.db.execute(`
        SELECT 
          check_time,
          status_code,
          response_time,
          is_accessible,
          error_message,
          check_type
        FROM website_status_checks
        WHERE website_id = ?
        ORDER BY check_time DESC
        LIMIT ?
      `, [websiteId, limit]);
      
      return rows;
    } catch (error) {
      console.error('获取网站检测历史失败:', error);
      throw error;
    }
  }
}

module.exports = WebsiteStatusService;
