/**
 * 角色管理服务类
 * 功能：
 * 1. 实现角色权限查询和更新功能
 * 2. 实现角色模板管理功能
 * 3. 实现批量角色权限更新
 * 4. 添加角色权限验证逻辑
 */

class RoleService {
  constructor(database) {
    this.db = database;
    
    // 系统预定义角色
    this.systemRoles = {
      super_admin: {
        name: '超级管理员',
        description: '拥有系统所有权限，包括用户管理、系统设置等',
        level: 100,
        isSystem: true
      },
      admin: {
        name: '管理员',
        description: '拥有大部分管理权限，但不能删除用户和修改系统设置',
        level: 80,
        isSystem: true
      },
      user: {
        name: '普通用户',
        description: '只有查看权限，可以查看站点和服务器信息',
        level: 20,
        isSystem: true
      }
    };
  }

  /**
   * 获取角色权限
   * @param {string} role - 角色名称
   * @returns {Promise<Object>} 角色权限信息
   */
  async getRolePermissions(role) {
    try {
      // 验证角色是否存在
      if (!this.systemRoles[role]) {
        throw new Error(`角色不存在: ${role}`);
      }

      // 获取角色权限
      const [permissions] = await this.db.execute(`
        SELECT 
          p.id,
          p.name,
          p.code,
          p.description,
          p.module,
          p.resource,
          p.action,
          rp.created_at as assigned_at
        FROM permissions p
        INNER JOIN role_permissions rp ON p.code = rp.permission_code
        WHERE rp.role = ?
        ORDER BY p.module, p.resource, p.action
      `, [role]);

      // 按模块分组权限
      const permissionsByModule = {};
      permissions.forEach(permission => {
        if (!permissionsByModule[permission.module]) {
          permissionsByModule[permission.module] = [];
        }
        permissionsByModule[permission.module].push(permission);
      });

      return {
        role,
        roleInfo: this.systemRoles[role],
        permissions,
        permissionsByModule,
        totalPermissions: permissions.length
      };

    } catch (error) {
      console.error(`获取角色权限失败 (角色: ${role}):`, error.message);
      throw error;
    }
  }

  /**
   * 更新角色权限
   * @param {string} role - 角色名称
   * @param {string[]} permissionCodes - 权限代码数组
   * @param {number} operatorId - 操作者ID
   * @returns {Promise<Object>} 更新结果
   */
  async updateRolePermissions(role, permissionCodes, operatorId) {
    try {
      // 验证角色
      if (!this.systemRoles[role]) {
        throw new Error(`角色不存在: ${role}`);
      }

      // 验证权限代码
      const validationResult = await this.validatePermissionCodes(permissionCodes);
      if (!validationResult.valid) {
        throw new Error(`无效的权限代码: ${validationResult.invalidCodes.join(', ')}`);
      }

      const updateResult = {
        role,
        addedPermissions: [],
        removedPermissions: [],
        totalPermissions: permissionCodes.length,
        errors: []
      };

      // 开始事务
      await this.db.execute('START TRANSACTION');

      try {
        // 获取当前角色权限
        const [currentPermissions] = await this.db.execute(
          'SELECT permission_code FROM role_permissions WHERE role = ?',
          [role]
        );

        const currentCodes = currentPermissions.map(p => p.permission_code);
        const newCodes = permissionCodes;

        // 计算需要添加和删除的权限
        const toAdd = newCodes.filter(code => !currentCodes.includes(code));
        const toRemove = currentCodes.filter(code => !newCodes.includes(code));

        // 删除不再需要的权限
        if (toRemove.length > 0) {
          const placeholders = toRemove.map(() => '?').join(',');
          await this.db.execute(
            `DELETE FROM role_permissions WHERE role = ? AND permission_code IN (${placeholders})`,
            [role, ...toRemove]
          );
          updateResult.removedPermissions = toRemove;
        }

        // 添加新权限
        if (toAdd.length > 0) {
          for (const permissionCode of toAdd) {
            await this.db.execute(
              'INSERT INTO role_permissions (role, permission_code, created_at) VALUES (?, ?, NOW())',
              [role, permissionCode]
            );
          }
          updateResult.addedPermissions = toAdd;
        }

        // 记录审计日志
        await this.recordRolePermissionChange(role, updateResult, operatorId);

        // 提交事务
        await this.db.execute('COMMIT');

        // 清除相关用户的权限缓存
        await this.clearRoleUsersCache(role);

        console.log(`角色权限更新成功 (角色: ${role}):`, updateResult);
        return updateResult;

      } catch (error) {
        // 回滚事务
        await this.db.execute('ROLLBACK');
        throw error;
      }

    } catch (error) {
      console.error(`更新角色权限失败 (角色: ${role}):`, error.message);
      throw error;
    }
  }

  /**
   * 获取角色模板
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} 角色模板列表
   */
  async getRoleTemplates(options = {}) {
    try {
      const { includeSystem = true, createdBy = null, limit = 50 } = options;
      
      let whereClause = '';
      const params = [];

      if (!includeSystem) {
        whereClause = 'WHERE is_system = 0';
      }

      if (createdBy) {
        whereClause += whereClause ? ' AND' : 'WHERE';
        whereClause += ' created_by = ?';
        params.push(createdBy);
      }

      const [templates] = await this.db.execute(`
        SELECT 
          rt.*,
          u.username as creator_username
        FROM role_templates rt
        LEFT JOIN users u ON rt.created_by = u.id
        ${whereClause}
        ORDER BY rt.is_system DESC, rt.created_at DESC
        LIMIT ?
      `, [...params, limit]);

      return templates.map(template => ({
        ...template,
        permissions: typeof template.permissions === 'string' 
          ? JSON.parse(template.permissions) 
          : template.permissions
      }));

    } catch (error) {
      console.error('获取角色模板失败:', error.message);
      throw error;
    }
  }

  /**
   * 创建角色模板
   * @param {Object} templateData - 模板数据
   * @param {number} creatorId - 创建者ID
   * @returns {Promise<Object>} 创建结果
   */
  async createRoleTemplate(templateData, creatorId) {
    try {
      const { name, description, permissions, isSystem = false } = templateData;

      // 验证模板名称唯一性
      const [existingTemplate] = await this.db.execute(
        'SELECT id FROM role_templates WHERE name = ?',
        [name]
      );

      if (existingTemplate.length > 0) {
        throw new Error(`模板名称已存在: ${name}`);
      }

      // 验证权限代码
      const validationResult = await this.validatePermissionCodes(permissions);
      if (!validationResult.valid) {
        throw new Error(`无效的权限代码: ${validationResult.invalidCodes.join(', ')}`);
      }

      // 创建模板
      const [result] = await this.db.execute(`
        INSERT INTO role_templates 
        (name, description, permissions, is_system, created_by, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, NOW(), NOW())
      `, [name, description, JSON.stringify(permissions), isSystem, creatorId]);

      const templateId = result.insertId;

      console.log(`角色模板创建成功 (ID: ${templateId}, 名称: ${name})`);

      return {
        id: templateId,
        name,
        description,
        permissions,
        isSystem,
        createdBy: creatorId
      };

    } catch (error) {
      console.error('创建角色模板失败:', error.message);
      throw error;
    }
  }

  /**
   * 应用角色模板到角色
   * @param {string} role - 目标角色
   * @param {number} templateId - 模板ID
   * @param {number} operatorId - 操作者ID
   * @returns {Promise<Object>} 应用结果
   */
  async applyRoleTemplate(role, templateId, operatorId) {
    try {
      // 获取模板信息
      const [templates] = await this.db.execute(
        'SELECT * FROM role_templates WHERE id = ?',
        [templateId]
      );

      if (templates.length === 0) {
        throw new Error(`角色模板不存在: ${templateId}`);
      }

      const template = templates[0];
      const permissions = typeof template.permissions === 'string' 
        ? JSON.parse(template.permissions) 
        : template.permissions;

      // 应用模板权限到角色
      const updateResult = await this.updateRolePermissions(role, permissions, operatorId);

      console.log(`角色模板应用成功 (角色: ${role}, 模板: ${template.name})`);

      return {
        role,
        templateName: template.name,
        templateId,
        ...updateResult
      };

    } catch (error) {
      console.error(`应用角色模板失败 (角色: ${role}, 模板ID: ${templateId}):`, error.message);
      throw error;
    }
  }

  /**
   * 批量更新角色权限
   * @param {Object} rolePermissions - 角色权限映射 {role: [permissions]}
   * @param {number} operatorId - 操作者ID
   * @returns {Promise<Object>} 批量更新结果
   */
  async batchUpdateRolePermissions(rolePermissions, operatorId) {
    try {
      const results = {};
      const errors = [];

      // 开始事务
      await this.db.execute('START TRANSACTION');

      try {
        for (const [role, permissions] of Object.entries(rolePermissions)) {
          try {
            const result = await this.updateRolePermissions(role, permissions, operatorId);
            results[role] = result;
          } catch (error) {
            errors.push({ role, error: error.message });
          }
        }

        if (errors.length > 0) {
          throw new Error(`部分角色权限更新失败: ${JSON.stringify(errors)}`);
        }

        // 提交事务
        await this.db.execute('COMMIT');

        console.log('批量角色权限更新成功:', Object.keys(results));

        return {
          success: true,
          results,
          totalRoles: Object.keys(rolePermissions).length,
          errors: []
        };

      } catch (error) {
        // 回滚事务
        await this.db.execute('ROLLBACK');
        throw error;
      }

    } catch (error) {
      console.error('批量更新角色权限失败:', error.message);
      return {
        success: false,
        results: {},
        totalRoles: Object.keys(rolePermissions).length,
        errors: [error.message]
      };
    }
  }

  /**
   * 获取角色用户统计
   * @param {string} role - 角色名称
   * @returns {Promise<Object>} 角色用户统计
   */
  async getRoleUserStatistics(role) {
    try {
      // 获取角色用户数量
      const [userCount] = await this.db.execute(
        'SELECT COUNT(*) as count FROM users WHERE role = ? AND status = "active"',
        [role]
      );

      // 获取角色用户列表
      const [users] = await this.db.execute(`
        SELECT 
          id, 
          username, 
          email, 
          status, 
          last_login, 
          created_at
        FROM users 
        WHERE role = ? 
        ORDER BY last_login DESC
        LIMIT 10
      `, [role]);

      // 获取最近权限变更
      const [recentChanges] = await this.db.execute(`
        SELECT 
          al.created_at,
          al.details,
          u.username as operator_username
        FROM audit_logs al
        LEFT JOIN users u ON JSON_EXTRACT(al.details, '$.operatorId') = u.id
        WHERE al.action = 'role_permission_update' 
          AND JSON_EXTRACT(al.details, '$.role') = ?
        ORDER BY al.created_at DESC
        LIMIT 5
      `, [role]);

      return {
        role,
        roleInfo: this.systemRoles[role],
        userCount: userCount[0].count,
        users,
        recentChanges: recentChanges.map(change => ({
          ...change,
          details: typeof change.details === 'string' ? JSON.parse(change.details) : change.details
        }))
      };

    } catch (error) {
      console.error(`获取角色用户统计失败 (角色: ${role}):`, error.message);
      throw error;
    }
  }

  /**
   * 验证权限代码有效性
   * @param {string[]} permissionCodes - 权限代码数组
   * @returns {Promise<Object>} 验证结果
   */
  async validatePermissionCodes(permissionCodes) {
    try {
      if (!Array.isArray(permissionCodes)) {
        return { valid: false, invalidCodes: ['权限代码必须是数组'] };
      }

      const [validPermissions] = await this.db.execute(
        `SELECT code FROM permissions WHERE code IN (${permissionCodes.map(() => '?').join(',')})`,
        permissionCodes
      );

      const validCodes = validPermissions.map(p => p.code);
      const invalidCodes = permissionCodes.filter(code => !validCodes.includes(code));

      return {
        valid: invalidCodes.length === 0,
        validCodes,
        invalidCodes
      };

    } catch (error) {
      console.error('验证权限代码失败:', error.message);
      return { valid: false, invalidCodes: ['验证失败'] };
    }
  }

  /**
   * 记录角色权限变更审计日志
   * @param {string} role - 角色名称
   * @param {Object} changes - 变更信息
   * @param {number} operatorId - 操作者ID
   */
  async recordRolePermissionChange(role, changes, operatorId) {
    try {
      await this.db.execute(`
        INSERT INTO audit_logs 
        (user_id, action, resource, resource_id, result, details, created_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        operatorId,
        'role_permission_update',
        'role',
        role,
        'success',
        JSON.stringify({
          role,
          operatorId,
          changes,
          timestamp: new Date().toISOString()
        })
      ]);

    } catch (error) {
      console.error('记录角色权限变更日志失败:', error.message);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 清除角色用户的权限缓存
   * @param {string} role - 角色名称
   */
  async clearRoleUsersCache(role) {
    try {
      // 获取该角色的所有用户
      const [users] = await this.db.execute(
        'SELECT id FROM users WHERE role = ?',
        [role]
      );

      // 清除权限缓存
      const PermissionCacheService = require('./PermissionCacheService');
      const cache = new PermissionCacheService();

      for (const user of users) {
        await cache.clearUserPermissions(user.id);
      }

      console.log(`已清除角色 ${role} 的 ${users.length} 个用户的权限缓存`);

    } catch (error) {
      console.error(`清除角色用户缓存失败 (角色: ${role}):`, error.message);
      // 不抛出错误，避免影响主流程
    }
  }

  /**
   * 获取所有系统角色信息
   * @returns {Object} 系统角色信息
   */
  getSystemRoles() {
    return this.systemRoles;
  }

  /**
   * 检查角色权限级别
   * @param {string} role1 - 角色1
   * @param {string} role2 - 角色2
   * @returns {number} 比较结果 (-1: role1 < role2, 0: 相等, 1: role1 > role2)
   */
  compareRoleLevel(role1, role2) {
    const level1 = this.systemRoles[role1]?.level || 0;
    const level2 = this.systemRoles[role2]?.level || 0;

    if (level1 < level2) return -1;
    if (level1 > level2) return 1;
    return 0;
  }

  /**
   * 检查用户是否可以管理目标角色
   * @param {string} operatorRole - 操作者角色
   * @param {string} targetRole - 目标角色
   * @returns {boolean} 是否可以管理
   */
  canManageRole(operatorRole, targetRole) {
    // 超级管理员可以管理所有角色
    if (operatorRole === 'super_admin') {
      return true;
    }

    // 管理员不能管理超级管理员
    if (operatorRole === 'admin' && targetRole === 'super_admin') {
      return false;
    }

    // 其他情况根据角色级别判断
    return this.compareRoleLevel(operatorRole, targetRole) >= 0;
  }
}

module.exports = RoleService;