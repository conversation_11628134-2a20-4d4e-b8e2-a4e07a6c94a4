const AccurateHttpDetectionService = require('./AccurateHttpDetectionService');

/**
 * 精确的网站状态检测服务
 * 基于PHP参考代码实现，解决误报问题
 */
class AccurateWebsiteStatusService {
  constructor(db) {
    this.db = db;
    this.httpDetector = new AccurateHttpDetectionService();
    
    // 配置参数（对应PHP代码中的常量）
    this.CHECK_INTERVAL = 600; // 10分钟检查间隔
    this.STATUS_CODE_ALARM_THRESHOLD = 600; // 10分钟告警阈值
    this.STATUS_CODE_DELAYED_REMINDER = 1800; // 30分钟延迟提醒
  }

  /**
   * 获取需要检测的网站列表
   * 对应PHP代码中的查询逻辑
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 网站列表
   */
  async getWebsitesToCheck(limit = 20) {
    try {
      const checkThreshold = new Date(Date.now() - this.CHECK_INTERVAL * 1000);

      console.log(`🔍 查询需要检测的网站，检查阈值: ${checkThreshold.toISOString()}`);

      const [rows] = await this.db.execute(`
        SELECT
          id,
          site_name,
          domain,
          site_url,
          platform_id,
          status_check,
          last_check_time,
          access_status,
          access_status_code,
          tries
        FROM websites
        WHERE status = 'active'
          AND status_check = 1
          AND (last_check_time IS NULL OR last_check_time < ?)
        ORDER BY tries ASC, last_check_time ASC
        LIMIT ${limit}
      `, [checkThreshold]);

      console.log(`✅ 找到 ${rows.length} 个需要检测的网站`);
      return rows;
    } catch (error) {
      console.error('❌ 获取待检测网站列表失败:', error);
      throw error;
    }
  }

  /**
   * 批量检测网站状态
   * @param {number} batchSize - 批次大小
   * @param {number} concurrency - 并发数
   * @returns {Promise<Object>} 检测结果统计
   */
  async batchCheckWebsites(batchSize = 20, concurrency = 10) {
    try {
      console.log('🌍 开始精确网站状态检测...');
      const startTime = Date.now();

      // 获取需要检测的网站
      const websites = await this.getWebsitesToCheck(batchSize);
      
      if (websites.length === 0) {
        console.log('ℹ️  没有需要检测的网站');
        return { total: 0, success: 0, failed: 0, duration: 0 };
      }

      console.log(`🚀 开始批量检测 ${websites.length} 个网站`);

      // 使用精确的HTTP检测服务
      const results = await this.httpDetector.batchDetect(websites, concurrency);

      // 更新检测结果到数据库
      const updateResults = await this.updateWebsiteStatuses(results);

      const duration = Date.now() - startTime;
      const stats = {
        total: results.length,
        success: results.filter(r => r.isSuccess).length,
        failed: results.filter(r => !r.isSuccess).length,
        duration: Math.round(duration / 1000)
      };

      console.log(`✅ 精确检测完成: 总计${stats.total}个，成功${stats.success}个，失败${stats.failed}个，耗时${stats.duration}秒`);
      
      return stats;
    } catch (error) {
      console.error('❌ 批量网站状态检测失败:', error);
      throw error;
    }
  }

  /**
   * 更新网站状态到数据库
   * @param {Array} results - 检测结果
   * @returns {Promise<Object>} 更新统计
   */
  async updateWebsiteStatuses(results) {
    let successCount = 0;
    let errorCount = 0;

    for (const result of results) {
      try {
        await this.updateSingleWebsiteStatus(result);
        successCount++;
      } catch (error) {
        console.error(`❌ 更新网站状态失败 (ID: ${result.id}):`, error.message);
        errorCount++;

        // 即使更新失败，也要更新 last_check_time，避免重复检测
        try {
          await this.db.execute(`
            UPDATE websites
            SET last_check_time = NOW(), tries = tries + 1
            WHERE id = ?
          `, [result.id]);
          console.log(`📝 已更新网站 ${result.id} 的检测时间`);
        } catch (timeUpdateError) {
          console.error(`❌ 更新检测时间失败 (ID: ${result.id}):`, timeUpdateError.message);
        }
      }
    }

    console.log(`📊 状态更新完成: 成功${successCount}个，失败${errorCount}个`);
    return { success: successCount, error: errorCount };
  }

  /**
   * 更新单个网站状态
   * @param {Object} result - 检测结果
   * @returns {Promise<void>}
   */
  async updateSingleWebsiteStatus(result) {
    const now = new Date();
    const isSuccess = result.isSuccess && this.httpDetector.isSuccessStatusCode(result.statusCode);
    
    // 确定访问状态
    let accessStatus;
    if (isSuccess) {
      accessStatus = 'online';
    } else if (result.statusCode === 0) {
      accessStatus = 'offline';
    } else {
      accessStatus = 'error';
    }

    // 更新网站基本状态
    await this.db.execute(`
      UPDATE websites 
      SET 
        access_status = ?,
        access_status_code = ?,
        last_check_time = ?,
        response_time = ?,
        server_ip = ?,
        cdn_type = ?,
        tries = tries + 1,
        updated_at = ?
      WHERE id = ?
    `, [
      accessStatus,
      result.statusCode || 0,
      now,
      result.responseTime || 0,
      result.serverIP || '',
      result.cdnType || 'no',
      now,
      result.id
    ]);

    // 更新或创建状态统计记录
    await this.updateWebsiteStatusStats(result.id, isSuccess, result.error, result.statusCode, result.responseTime);

    console.log(`📝 更新网站状态: ${result.site_name} (${result.id}) - ${accessStatus} (${result.statusCode})`);
  }

  /**
   * 更新网站状态统计
   * @param {number} websiteId - 网站ID
   * @param {boolean} isSuccess - 是否成功
   * @param {string} error - 错误信息
   * @param {number} statusCode - 状态码
   * @param {number} responseTime - 响应时间
   * @returns {Promise<void>}
   */
  async updateWebsiteStatusStats(websiteId, isSuccess, error, statusCode = 0, responseTime = 0) {
    const now = new Date();

    if (isSuccess) {
      // 成功时重置失败计数
      await this.db.execute(`
        INSERT INTO website_status_stats (
          website_id, consecutive_failures, total_checks, success_checks,
          last_success_time, first_failure_time, last_failure_time,
          notification_sent, current_status_code, current_response_time,
          created_at, updated_at
        ) VALUES (?, 0, 1, 1, ?, NULL, NULL, FALSE, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          consecutive_failures = 0,
          total_checks = total_checks + 1,
          success_checks = success_checks + 1,
          last_success_time = ?,
          first_failure_time = NULL,
          last_failure_time = NULL,
          notification_sent = FALSE,
          current_status_code = ?,
          current_response_time = ?,
          updated_at = ?
      `, [websiteId, now, statusCode, responseTime, now, now, now, statusCode, responseTime, now]);
    } else {
      // 失败时增加失败计数
      await this.db.execute(`
        INSERT INTO website_status_stats (
          website_id, consecutive_failures, total_checks, success_checks,
          last_success_time, first_failure_time, last_failure_time,
          notification_sent, current_status_code, current_response_time,
          created_at, updated_at
        ) VALUES (?, 1, 1, 0, NULL, ?, ?, FALSE, ?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE
          consecutive_failures = consecutive_failures + 1,
          total_checks = total_checks + 1,
          first_failure_time = COALESCE(first_failure_time, ?),
          last_failure_time = ?,
          current_status_code = ?,
          current_response_time = ?,
          updated_at = ?
      `, [websiteId, now, now, statusCode, responseTime, now, now, now, now, statusCode, responseTime, now]);
    }
  }

  /**
   * 获取异常网站列表（用于通知）
   * 对应PHP代码中的status_notice.php逻辑
   * @returns {Promise<Array>} 异常网站列表
   */
  async getAbnormalWebsitesForNotification() {
    try {
      const currentTime = Math.floor(Date.now() / 1000);
      const checkThreshold = new Date(Date.now() - this.CHECK_INTERVAL * 1000);

      const [rows] = await this.db.execute(`
        SELECT
          w.id as site_id,
          p.name as platform,
          w.site_name,
          w.site_url as url,
          w.access_status_code as status_code,
          w.last_check_time as last_error_check,
          ws.consecutive_failures,
          ws.first_failure_time,
          ws.last_failure_time,
          ws.notification_sent,
          ws.last_notification_time,
          TIMESTAMPDIFF(HOUR, ws.first_failure_time, NOW()) as alarm_duration_hours,
          TIMESTAMPDIFF(MINUTE, ws.first_failure_time, NOW()) as alarm_duration_minutes
        FROM websites w
        LEFT JOIN website_status_stats ws ON w.id = ws.website_id
        LEFT JOIN platforms p ON w.platform_id = p.id
        WHERE w.status_check = 1
          AND w.last_check_time < ?
          AND ws.consecutive_failures >= 3
          AND w.access_status IN ('offline', 'error')
          AND (ws.notification_sent = FALSE OR ws.last_notification_time IS NULL OR ws.last_notification_time < DATE_SUB(NOW(), INTERVAL 30 MINUTE))
        ORDER BY p.name, w.last_check_time ASC
        LIMIT 50
      `, [checkThreshold]);

      console.log(`📢 找到 ${rows.length} 个异常网站需要通知`);
      return rows;
    } catch (error) {
      console.error('❌ 获取异常网站列表失败:', error);
      throw error;
    }
  }

  /**
   * 更新通知时间
   * @param {Array} websiteIds - 网站ID列表
   * @returns {Promise<void>}
   */
  async updateNotificationTime(websiteIds) {
    if (websiteIds.length === 0) return;

    await this.db.execute(`
      UPDATE website_status_stats
      SET notification_sent = TRUE,
          last_notification_time = NOW(),
          notification_count = notification_count + 1
      WHERE website_id IN (${websiteIds.map(() => '?').join(',')})
    `, websiteIds);

    console.log(`📅 更新 ${websiteIds.length} 个网站的通知时间`);
  }
}

module.exports = AccurateWebsiteStatusService;
