const cron = require('node-cron');
const ServerModel = require('../models/Server');
const WebsiteModel = require('../models/Website');
const NotificationService = require('./NotificationService');
const WebsiteCheckerService = require('./website-checker');
const WebsiteStatusService = require('./website-status-service');
const AccurateWebsiteStatusService = require('./AccurateWebsiteStatusService');
const PermissionService = require('./PermissionService');
const IpLocationService = require('./IpLocationService');

class SchedulerService {
  constructor(db = null) {
    this.db = db;
    this.serverModel = db ? new ServerModel(db) : new ServerModel();
    this.websiteModel = db ? new WebsiteModel(db) : new WebsiteModel();
    this.notificationService = new NotificationService();
    this.websiteChecker = new WebsiteCheckerService();
    this.websiteStatusService = db ? new WebsiteStatusService(db) : null;
    this.accurateWebsiteStatusService = db ? new AccurateWebsiteStatusService(db) : null;
    this.permissionService = db ? new PermissionService(db) : null;
    this.ipLocationService = db ? new IpLocationService(db) : null;
    this.tasks = new Map();
  }

  // 设置数据库连接
  setDatabase(db) {
    this.db = db;
    this.serverModel = new ServerModel(db);
    this.websiteModel = new WebsiteModel(db);
    this.notificationService = new NotificationService();
    this.websiteStatusService = new WebsiteStatusService(db); // 重新初始化WebsiteStatusService
    this.accurateWebsiteStatusService = new AccurateWebsiteStatusService(db); // 初始化精确检测服务
  }

  // 启动所有定时任务
  start() {
    console.log('🕐 启动定时任务服务...');

    // 每10分钟检测网站访问状态（与PHP文档系统一致）
    this.scheduleWebsiteAccessCheck();

    // 每天早上8:30检测SSL证书
    this.scheduleSSLCheck();

    // 每天凌晨2点自动更新所有服务器配置信息
    this.scheduleServerConfigUpdate();

    // 每小时更新网站服务器IP信息（与PHP文档系统一致）
    this.scheduleServerIpUpdate();

    console.log('✅ 定时任务服务启动完成');
  }

  // 停止所有定时任务
  stop() {
    console.log('🛑 停止定时任务服务...');
    
    this.tasks.forEach((task, name) => {
      if (task) {
        task.stop();
        console.log(`⏹️  已停止定时任务: ${name}`);
      }
    });
    
    this.tasks.clear();
    console.log('✅ 定时任务服务已停止');
  }

  // 调度网站状态检测任务（精确版本）
  scheduleWebsiteAccessCheck() {
    // 每10分钟执行一次 (*/10 * * * *) - 与PHP文档系统一致
    const task = cron.schedule('*/10 * * * *', async () => {
      console.log('🌍 开始执行定时网站状态检测...');

      if (this.accurateWebsiteStatusService) {
        // 使用精确的状态检测服务（基于PHP逻辑）
        try {
          const stats = await this.accurateWebsiteStatusService.batchCheckWebsites(20, 10);
          console.log(`✅ 精确检测完成: ${stats.total}个网站，成功${stats.success}个，失败${stats.failed}个`);

          // 发送飞书异常通知
          await this.sendFeishuAbnormalNotification();
        } catch (error) {
          console.error('❌ 精确检测失败，回退到传统检测:', error.message);
          await this.fallbackToTraditionalCheck();
        }
      } else if (this.websiteStatusService) {
        // 回退到原有的状态检测服务
        console.warn('⚠️  精确检测服务未初始化，使用原有检测方法');
        await this.websiteStatusService.performStatusCheck();
      } else {
        // 最后回退到传统检测
        console.warn('⚠️  所有检测服务未初始化，使用传统检测方法');
        await this.checkAllWebsiteAccess();
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Shanghai'
    });

    this.tasks.set('websiteAccessCheck', task);
    task.start();

    console.log('🌍 精确网站状态检测定时任务已启动 (每10分钟执行)');
  }

  // 调度SSL证书检测任务
  scheduleSSLCheck() {
    // 每天凌晨00:30执行 (30 0 * * *)
    // 开发测试时可以改为每分钟执行: '* * * * *'
    const task = cron.schedule('30 0 * * *', async () => {
      console.log('🔒 开始执行定时SSL证书检测...');
      await this.checkAllSSLCertificates();
    }, {
      scheduled: false,
      timezone: 'Asia/Shanghai'
    });

    this.tasks.set('sslCheck', task);
    task.start();

    console.log('🔒 SSL证书检测定时任务已启动 (每天凌晨00:30执行)');
  }

  // 调度服务器配置更新任务
  scheduleServerConfigUpdate() {
    // 每天凌晨2点执行 (0 2 * * *)
    // 开发测试时可以改为每分钟执行: '* * * * *'
    const task = cron.schedule('0 2 * * *', async () => {
      console.log('🔄 开始执行定时服务器配置更新任务...');
      await this.updateAllServerConfigs();
    }, {
      scheduled: false,
      timezone: 'Asia/Shanghai'
    });

    this.tasks.set('serverConfigUpdate', task);
    task.start();
    
    console.log('📅 服务器配置更新定时任务已启动 (每天凌晨2点执行)');
  }

  // 更新所有服务器配置信息
  async updateAllServerConfigs() {
    try {
      console.log('📊 获取所有启用监控的服务器...');
      
      // 获取所有启用监控且配置了SSH的服务器
      const servers = await this.serverModel.getAllServers();
      const monitoringServers = servers.filter(server => 
        server.monitoringEnabled && 
        server.sshUsername && 
        (server.sshPassword || server.sshPrivateKey)
      );

      console.log(`🖥️  找到 ${monitoringServers.length} 台需要更新配置的服务器`);

      if (monitoringServers.length === 0) {
        console.log('ℹ️  没有需要更新配置的服务器');
        return;
      }

      let successCount = 0;
      let failCount = 0;

      // 并发处理，但限制并发数量避免过载
      const concurrencyLimit = 3;
      const chunks = this.chunkArray(monitoringServers, concurrencyLimit);

      for (const chunk of chunks) {
        const promises = chunk.map(async (server) => {
          try {
            console.log(`🔍 检测服务器配置: ${server.name} (${server.ipAddress})`);
            
            // 调用配置检测API
            const response = await fetch('http://localhost:3001/api/v1/servers/detect-config', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                name: server.name,
                ipAddress: server.ipAddress,
                sshPort: server.sshPort,
                sshUsername: server.sshUsername,
                sshAuthType: server.sshAuthType,
                sshPassword: server.sshPassword,
                sshPrivateKey: server.sshPrivateKey,
                sshKeyPassphrase: server.sshKeyPassphrase
              }),
            });

            if (response.ok) {
              const result = await response.json();
              if (result.success && result.data) {
                // 更新服务器配置信息
                await this.serverModel.updateServerConfigFromDetection(server.id, result.data);
                console.log(`✅ 服务器配置更新成功: ${server.name}`);
                successCount++;
              } else {
                console.warn(`⚠️  服务器配置检测失败: ${server.name} - ${result.message}`);
                failCount++;
              }
            } else {
              console.error(`❌ 服务器配置检测请求失败: ${server.name} - HTTP ${response.status}`);
              failCount++;
            }
          } catch (error) {
            console.error(`❌ 服务器配置更新异常: ${server.name} - ${error.message}`);
            failCount++;
          }
        });

        // 等待当前批次完成
        await Promise.all(promises);
        
        // 批次间稍作延迟，避免过载
        if (chunks.indexOf(chunk) < chunks.length - 1) {
          await this.sleep(2000);
        }
      }

      console.log(`🎉 定时服务器配置更新任务完成: 成功 ${successCount} 台，失败 ${failCount} 台`);
      
    } catch (error) {
      console.error('❌ 定时服务器配置更新任务失败:', error);
    }
  }

  // 检测所有网站访问状态 - 超高性能Worker Threads版本
  async checkAllWebsiteAccess() {
    const startTime = Date.now();

    try {
      console.log('🌐 获取所有活跃网站...');

      // 获取所有活跃的网站
      const websiteData = await this.websiteModel.getAllWebsites(1, 2000); // 增加到2000个网站
      const activeWebsites = websiteData.websites.filter(website => website.status === 'active');

      console.log(`🔍 找到 ${activeWebsites.length} 个需要检测访问状态的网站`);

      if (activeWebsites.length === 0) {
        console.log('ℹ️  没有需要检测访问状态的网站');
        return;
      }

      // 根据网站数量选择检测策略
      if (activeWebsites.length >= 100) {
        console.log('🚀 使用Worker Threads高性能模式');
        await this.checkWebsitesWithWorkerThreads(activeWebsites);
      } else {
        console.log('⚡ 使用传统高并发模式');
        await this.checkWebsitesTraditional(activeWebsites);
      }

    } catch (error) {
      console.error('❌ 定时网站访问状态检测任务失败:', error);
    }
  }

  // Worker Threads高性能检测方法
  async checkWebsitesWithWorkerThreads(activeWebsites) {
    try {
      // 使用Worker Threads进行检测
      const results = await this.websiteChecker.checkWebsitesBatch(activeWebsites);

      let successCount = 0;
      let failCount = 0;
      let notificationCount = 0;

      // 批量更新数据库和处理通知
      const updatePromises = results.map(async (result) => {
        try {
          // 更新网站访问状态
          await this.updateWebsiteAccessStatusFromResult(result);

          if (result.isAccessible) {
            successCount++;
          } else {
            failCount++;

            // 异步检查通知
            const website = activeWebsites.find(w => w.id === result.websiteId);
            if (website) {
              this.checkAndSendNotification(website, result).then(shouldNotify => {
                if (shouldNotify) {
                  notificationCount++;
                }
              }).catch(error => {
                console.error(`通知检查失败: ${website.siteName}`, error);
              });
            }
          }
        } catch (error) {
          console.error('更新网站状态失败:', error);
        }
      });

      await Promise.allSettled(updatePromises);

      console.log(`🎯 Worker Threads检测完成:`);
      console.log(`   📊 总计: ${activeWebsites.length} 个网站`);
      console.log(`   ✅ 成功: ${successCount} 个`);
      console.log(`   ❌ 失败: ${failCount} 个`);
      console.log(`   🔔 通知: ${notificationCount} 个`);

    } catch (error) {
      console.error('❌ Worker Threads检测失败，回退到传统模式:', error);
      await this.checkWebsitesTraditional(activeWebsites);
    }
  }

  // 传统高并发检测方法（备选方案）
  async checkWebsitesTraditional(activeWebsites) {
    let successCount = 0;
    let failCount = 0;
    let notificationCount = 0;

    // 高并发处理 - 根据网站数量动态调整并发数
    const concurrencyLimit = Math.min(Math.max(Math.ceil(activeWebsites.length / 10), 30), 100);
    console.log(`🚀 使用高并发模式，并发数: ${concurrencyLimit}`);

    // 使用Promise.allSettled进行真正的并发处理
    const batchSize = concurrencyLimit;
    const batches = this.chunkArray(activeWebsites, batchSize);

    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const batchStartTime = Date.now();

      console.log(`📦 处理第 ${i + 1}/${batches.length} 批次，包含 ${batch.length} 个网站`);

      // 并发处理当前批次的所有网站
      const promises = batch.map(async (website) => {
        try {
          // 直接进行HTTP检测，使用更短的超时时间
          const accessResult = await this.checkWebsiteAccessDirectFast(website.siteUrl || `https://${website.domain}`);

          // 更新网站访问状态和异常计数
          await this.updateWebsiteAccessStatus(website, accessResult);

          if (accessResult.isAccessible) {
            successCount++;
            return { website, status: 'success', result: accessResult };
          } else {
            failCount++;

            // 异步检查是否需要发送通知，不阻塞主流程
            this.checkAndSendNotification(website, accessResult).then(shouldNotify => {
              if (shouldNotify) {
                notificationCount++;
              }
            }).catch(error => {
              console.error(`通知检查失败: ${website.siteName}`, error);
            });

            return { website, status: 'failed', result: accessResult };
          }

        } catch (error) {
          console.error(`❌ 检测网站访问状态失败: ${website.siteName} -`, error);
          failCount++;
          return { website, status: 'error', error: error.message };
        }
      });

      // 使用Promise.allSettled确保所有请求都完成，不会因为单个失败而中断
      const results = await Promise.allSettled(promises);

      const batchTime = Date.now() - batchStartTime;
      const avgTimePerSite = Math.round(batchTime / batch.length);

      console.log(`✅ 第 ${i + 1} 批次完成，耗时: ${batchTime}ms，平均每站点: ${avgTimePerSite}ms`);

      // 只在批次间添加极短延迟，避免过载
      if (i < batches.length - 1) {
        await this.sleep(50);
      }
    }

    console.log(`🎯 传统模式检测完成:`);
    console.log(`   📊 总计: ${activeWebsites.length} 个网站`);
    console.log(`   ✅ 成功: ${successCount} 个`);
    console.log(`   ❌ 失败: ${failCount} 个`);
    console.log(`   🔔 通知: ${notificationCount} 个`);
  }

  // 检测所有网站SSL证书
  async checkAllSSLCertificates() {
    try {
      console.log('🔒 获取所有活跃网站...');

      // 获取所有活跃的网站
      const websiteData = await this.websiteModel.getAllWebsites(1, 1000); // 获取前1000个网站
      const activeWebsites = websiteData.websites.filter(website => website.status === 'active');

      console.log(`🔍 找到 ${activeWebsites.length} 个需要检测SSL证书的网站`);

      if (activeWebsites.length === 0) {
        console.log('ℹ️  没有需要检测SSL证书的网站');
        return;
      }

      let successCount = 0;
      let failCount = 0;

      // 并发处理，但限制并发数量避免过载
      const concurrencyLimit = 3;
      const chunks = this.chunkArray(activeWebsites, concurrencyLimit);

      for (const chunk of chunks) {
        const promises = chunk.map(async (website) => {
          try {
            console.log(`🔒 检测SSL证书: ${website.siteName} (${website.domain})`);

            // 调用SSL检测API
            const response = await fetch(`http://localhost:3001/api/v1/websites/${website.id}/check-ssl`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
            });

            if (response.ok) {
              const result = await response.json();
              if (result.success) {
                console.log(`✅ SSL证书检测成功: ${website.siteName} - ${result.data.status} (到期: ${result.data.expireDate})`);
                successCount++;
              } else {
                console.warn(`⚠️  SSL证书检测失败: ${website.siteName} - ${result.message}`);
                failCount++;
              }
            } else {
              console.error(`❌ SSL证书检测请求失败: ${website.siteName} - HTTP ${response.status}`);
              failCount++;
            }
          } catch (error) {
            console.error(`❌ SSL证书检测异常: ${website.siteName} - ${error.message}`);
            failCount++;
          }
        });

        // 等待当前批次完成
        await Promise.all(promises);

        // 批次间稍作延迟，避免过载
        if (chunks.indexOf(chunk) < chunks.length - 1) {
          await this.sleep(2000);
        }
      }

      console.log(`🎉 定时SSL证书检测完成: 成功 ${successCount} 个，失败 ${failCount} 个`);

    } catch (error) {
      console.error('❌ 定时SSL证书检测失败:', error);
    }
  }

  // 手动触发服务器配置更新（用于测试）
  async triggerServerConfigUpdate() {
    console.log('🔄 手动触发服务器配置更新任务...');
    await this.updateAllServerConfigs();
  }

  // 手动触发网站状态检测（用于测试）
  async triggerWebsiteAccessCheck() {
    console.log('🌍 手动触发网站状态检测...');

    if (this.accurateWebsiteStatusService) {
      // 使用精确的状态检测服务
      try {
        const stats = await this.accurateWebsiteStatusService.batchCheckWebsites(20, 10);
        console.log(`✅ 手动精确检测完成: ${stats.total}个网站，成功${stats.success}个，失败${stats.failed}个`);
        return stats;
      } catch (error) {
        console.error('❌ 精确检测失败，回退到传统检测:', error.message);
        return await this.fallbackToTraditionalCheck();
      }
    } else if (this.websiteStatusService) {
      // 回退到原有的状态检测服务
      console.warn('⚠️  精确检测服务未初始化，使用原有检测方法');
      return await this.websiteStatusService.performStatusCheck();
    } else {
      // 最后回退到传统检测
      console.warn('⚠️  所有检测服务未初始化，使用传统检测方法');
      await this.checkAllWebsiteAccess();
    }
  }

  // 发送飞书异常通知
  async sendFeishuAbnormalNotification() {
    try {
      if (!this.accurateWebsiteStatusService) return;

      const abnormalWebsites = await this.accurateWebsiteStatusService.getAbnormalWebsitesForNotification();

      if (abnormalWebsites.length === 0) {
        console.log('ℹ️  没有需要发送飞书通知的异常网站');
        return;
      }

      // 使用飞书通知服务发送通知
      const FeishuNotificationService = require('./FeishuNotificationService');
      const feishuService = new FeishuNotificationService(this.db);

      const result = await feishuService.sendWebsiteAbnormalNotification();

      if (result.success) {
        // 更新通知时间
        const websiteIds = abnormalWebsites.map(w => w.site_id);
        await this.accurateWebsiteStatusService.updateNotificationTime(websiteIds);
        console.log(`📢 飞书异常通知发送成功: ${abnormalWebsites.length} 个网站`);
      } else {
        console.log(`❌ 飞书异常通知发送失败: ${result.reason || result.error}`);
      }
    } catch (error) {
      console.error('❌ 发送飞书异常通知失败:', error);
    }
  }

  // 回退到传统检测方法
  async fallbackToTraditionalCheck() {
    try {
      if (this.websiteStatusService) {
        return await this.websiteStatusService.performStatusCheck();
      } else {
        await this.checkAllWebsiteAccess();
        return { total: 0, success: 0, failed: 0, duration: 0 };
      }
    } catch (error) {
      console.error('❌ 传统检测方法也失败:', error);
      return { total: 0, success: 0, failed: 0, duration: 0 };
    }
  }

  // 手动触发SSL证书检测（用于测试）
  async triggerSSLCheck() {
    console.log('🔒 手动触发SSL证书检测...');
    await this.checkAllSSLCertificates();
  }

  // 将数组分块
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  // 延迟函数
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 获取任务状态
  getTaskStatus() {
    const status = {};
    this.tasks.forEach((task, name) => {
      status[name] = {
        running: task.running,
        scheduled: task.scheduled
      };
    });
    return status;
  }

  // 直接检测网站访问状态
  async checkWebsiteAccessDirect(url) {
    const https = require('https');
    const http = require('http');
    const { URL } = require('url');

    return new Promise((resolve) => {
      try {
        const urlObj = new URL(url);
        const client = urlObj.protocol === 'https:' ? https : http;
        const startTime = Date.now();

        const options = {
          hostname: urlObj.hostname,
          port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
          path: urlObj.pathname + urlObj.search,
          method: 'GET',
          timeout: 10000,
          headers: {
            'User-Agent': 'SiteManager-Monitor/1.0'
          },
          // 忽略SSL证书错误
          rejectUnauthorized: false
        };

        const req = client.request(options, (res) => {
          const responseTime = Date.now() - startTime;

          resolve({
            statusCode: res.statusCode,
            responseTime: responseTime,
            isAccessible: res.statusCode >= 200 && res.statusCode < 400,
            lastCheckTime: new Date().toISOString()
          });
        });

        req.on('error', (error) => {
          const responseTime = Date.now() - startTime;
          resolve({
            statusCode: 0,
            responseTime: responseTime,
            isAccessible: false,
            lastCheckTime: new Date().toISOString(),
            error: error.message
          });
        });

        req.on('timeout', () => {
          req.destroy();
          const responseTime = Date.now() - startTime;
          resolve({
            statusCode: 0,
            responseTime: responseTime,
            isAccessible: false,
            lastCheckTime: new Date().toISOString(),
            error: 'Request timeout'
          });
        });

        req.end();
      } catch (error) {
        resolve({
          statusCode: 0,
          responseTime: 0,
          isAccessible: false,
          lastCheckTime: new Date().toISOString(),
          error: error.message
        });
      }
    });
  }

  // 快速检测网站访问状态 - 优化版本，用于高并发场景
  async checkWebsiteAccessDirectFast(url) {
    const https = require('https');
    const http = require('http');
    const { URL } = require('url');

    return new Promise((resolve) => {
      try {
        const urlObj = new URL(url);
        const client = urlObj.protocol === 'https:' ? https : http;
        const startTime = Date.now();

        const options = {
          hostname: urlObj.hostname,
          port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
          path: urlObj.pathname + urlObj.search,
          method: 'HEAD', // 使用HEAD请求，更快
          timeout: 5000,  // 减少超时时间到5秒
          headers: {
            'User-Agent': 'SiteManager-Monitor/1.0',
            'Connection': 'close' // 立即关闭连接
          },
          // 忽略SSL证书错误
          rejectUnauthorized: false
        };

        const req = client.request(options, (res) => {
          const responseTime = Date.now() - startTime;

          // 立即销毁响应流，不读取内容
          res.destroy();

          resolve({
            statusCode: res.statusCode,
            responseTime: responseTime,
            isAccessible: res.statusCode >= 200 && res.statusCode < 400,
            lastCheckTime: new Date().toISOString()
          });
        });

        req.on('error', (error) => {
          const responseTime = Date.now() - startTime;
          resolve({
            statusCode: 0,
            responseTime: responseTime,
            isAccessible: false,
            lastCheckTime: new Date().toISOString(),
            error: error.message
          });
        });

        req.on('timeout', () => {
          req.destroy();
          const responseTime = Date.now() - startTime;
          resolve({
            statusCode: 0,
            responseTime: responseTime,
            isAccessible: false,
            lastCheckTime: new Date().toISOString(),
            error: 'Request timeout'
          });
        });

        req.end();
      } catch (error) {
        resolve({
          statusCode: 0,
          responseTime: 0,
          isAccessible: false,
          lastCheckTime: new Date().toISOString(),
          error: error.message
        });
      }
    });
  }

  // 更新网站访问状态和异常计数
  async updateWebsiteAccessStatus(website, accessResult) {
    try {
      if (accessResult.isAccessible) {
        // 网站正常，重置异常计数和通知状态
        await this.db.execute(
          `UPDATE websites SET
           access_status_code = ?,
           response_time = ?,
           last_check_time = NOW(),
           consecutive_failures = 0,
           notification_sent = 0,
           last_failure_time = NULL
           WHERE id = ?`,
          [accessResult.statusCode, accessResult.responseTime, website.id]
        );
      } else {
        // 网站异常，增加异常计数
        await this.db.execute(
          `UPDATE websites SET
           access_status_code = ?,
           response_time = ?,
           last_check_time = NOW(),
           consecutive_failures = consecutive_failures + 1,
           last_failure_time = NOW()
           WHERE id = ?`,
          [accessResult.statusCode || 0, accessResult.responseTime, website.id]
        );
      }
    } catch (error) {
      console.error('更新网站访问状态失败:', error);
    }
  }

  // 从Worker Threads结果更新网站访问状态
  async updateWebsiteAccessStatusFromResult(result) {
    try {
      if (result.isAccessible) {
        // 网站正常，重置异常计数和通知状态
        await this.db.execute(
          `UPDATE websites SET
           access_status_code = ?,
           response_time = ?,
           last_check_time = NOW(),
           consecutive_failures = 0,
           notification_sent = 0,
           last_failure_time = NULL
           WHERE id = ?`,
          [result.statusCode, result.responseTime, result.websiteId]
        );
      } else {
        // 网站异常，增加异常计数
        await this.db.execute(
          `UPDATE websites SET
           access_status_code = ?,
           response_time = ?,
           last_check_time = NOW(),
           consecutive_failures = consecutive_failures + 1,
           last_failure_time = NOW()
           WHERE id = ?`,
          [result.statusCode || 0, result.responseTime, result.websiteId]
        );
      }
    } catch (error) {
      console.error('更新网站访问状态失败:', error);
    }
  }

  // 检查并发送通知
  async checkAndSendNotification(website, accessResult) {
    try {
      // 获取当前的连续失败次数
      const [rows] = await this.db.execute(
        'SELECT consecutive_failures, notification_sent FROM websites WHERE id = ?',
        [website.id]
      );

      if (rows.length === 0) {
        console.warn(`⚠️  网站不存在: ID ${website.id}`);
        return false;
      }

      const consecutiveFailures = rows[0].consecutive_failures + 1; // +1 因为这次也是失败
      const notificationSent = rows[0].notification_sent;

      console.log(`🔍 检查通知条件: ${website.siteName} - 连续失败 ${consecutiveFailures} 次, 已发送通知: ${notificationSent}`);

      // 如果达到5次连续失败且还没发送过通知
      if (consecutiveFailures >= 5 && !notificationSent) {
        console.log(`🚨 触发通知条件: ${website.siteName} 连续失败 ${consecutiveFailures} 次`);

        const result = await this.notificationService.sendWebsiteFailureNotification(
          website,
          consecutiveFailures,
          accessResult.statusCode
        );

        if (result.success) {
          console.log(`📢 已发送网站异常通知: ${website.siteName} (连续失败 ${consecutiveFailures} 次)`);

          // 记录通知发送日志
          await this.logNotificationEvent(website.id, 'success', `连续失败${consecutiveFailures}次，通知发送成功`);
          return true;
        } else {
          console.error(`❌ 发送网站异常通知失败: ${website.siteName} - ${result.error || result.reason}`);

          // 记录通知失败日志
          await this.logNotificationEvent(website.id, 'failed', `连续失败${consecutiveFailures}次，通知发送失败: ${result.error || result.reason}`);
        }
      } else if (consecutiveFailures >= 5 && notificationSent) {
        console.log(`ℹ️  ${website.siteName} 已发送过通知，跳过重复发送`);
      }

      return false;
    } catch (error) {
      console.error(`❌ 检查并发送通知失败: ${website.siteName} -`, error);
      await this.logNotificationEvent(website.id, 'error', `检查通知失败: ${error.message}`);
      return false;
    }
  }

  // 记录通知事件日志
  async logNotificationEvent(websiteId, status, message) {
    try {
      await this.db.execute(
        `INSERT INTO notification_logs
         (website_id, notification_type, trigger_reason, message, status, created_at)
         VALUES (?, ?, ?, ?, ?, NOW())`,
        [websiteId, 'system', 'monitoring', message, status]
      );
    } catch (error) {
      console.error('记录通知事件日志失败:', error);
    }
  }

  // 调度服务器IP信息更新任务
  scheduleServerIpUpdate() {
    // 每小时执行一次服务器IP信息更新（与PHP文档系统一致）
    const task = cron.schedule('0 * * * *', async () => {
      console.log('🌐 开始更新网站服务器IP信息...');

      if (this.ipLocationService) {
        await this.ipLocationService.updateAllWebsitesServerInfo();
      } else {
        console.warn('⚠️  IpLocationService未初始化，跳过IP信息更新');
      }
    }, {
      scheduled: false,
      timezone: 'Asia/Shanghai'
    });

    this.tasks.set('serverIpUpdate', task);
    task.start();

    console.log('🌐 服务器IP信息更新定时任务已启动 (每小时执行)');
  }
}

module.exports = SchedulerService;
