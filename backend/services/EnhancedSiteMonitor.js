/**
 * 增强版站点监控服务
 * 按照新的检测逻辑：5分钟检测一次，失败重试，状态检测表管理
 */

const axios = require('axios');
const https = require('https');
const EventEmitter = require('events');

class EnhancedSiteMonitor extends EventEmitter {
  constructor(db, notificationService) {
    super();
    this.db = db;
    this.notificationService = notificationService;
    this.isRunning = false;
    this.retryQueue = new Map(); // 重试队列
    
    // 配置参数
    this.config = {
      checkInterval: 5 * 60 * 1000,    // 5分钟检测一次
      retryInterval: 10 * 1000,        // 10秒重试一次
      notifyInterval: 5 * 60 * 1000,   // 5分钟通知检查一次
      batchSize: 50,                   // 每批检测50个站点
      concurrency: 10,                 // 并发检测数
      timeout: 15000,                  // 请求超时15秒
      failureThreshold: 3,             // 错误次数阈值
      maxRetries: 5                    // 最大重试次数
    };
    
    // HTTP客户端
    this.httpClient = axios.create({
      timeout: this.config.timeout,
      maxRedirects: 5,
      validateStatus: (status) => status < 500,
      httpsAgent: new https.Agent({
        rejectUnauthorized: false,
        timeout: this.config.timeout
      }),
      headers: {
        'User-Agent': 'SiteMonitor/2.0 (Enhanced Health Check)'
      }
    });
    
    this.stats = {
      totalChecks: 0,
      successfulChecks: 0,
      failedChecks: 0,
      retryAttempts: 0,
      notificationsSent: 0,
      lastRunTime: null
    };
  }

  /**
   * 启动监控服务
   */
  async start() {
    if (this.isRunning) {
      console.log('⚠️  监控服务已在运行中');
      return;
    }

    console.log('🚀 启动增强版站点监控服务...');
    this.isRunning = true;
    
    // 立即执行一次检测
    await this.runMainCheck();
    
    // 设置定时检测（5分钟）
    this.scheduleMainCheck();
    
    // 设置重试处理
    this.scheduleRetryCheck();
    
    // 设置通知检查（5分钟）
    this.scheduleNotificationCheck();
    
    console.log(`✅ 监控服务启动成功`);
    console.log(`   主检测间隔: ${this.config.checkInterval / 1000}秒`);
    console.log(`   重试间隔: ${this.config.retryInterval / 1000}秒`);
    console.log(`   通知检查间隔: ${this.config.notifyInterval / 1000}秒`);
  }

  /**
   * 停止监控服务
   */
  async stop() {
    console.log('🛑 停止监控服务...');
    this.isRunning = false;
    
    // 清理定时器
    if (this.mainTimer) clearTimeout(this.mainTimer);
    if (this.retryTimer) clearTimeout(this.retryTimer);
    if (this.notifyTimer) clearTimeout(this.notifyTimer);
    
    // 清空重试队列
    this.retryQueue.clear();
    
    console.log('✅ 监控服务已停止');
  }

  /**
   * 调度主检测
   */
  scheduleMainCheck() {
    if (!this.isRunning) return;
    
    this.mainTimer = setTimeout(async () => {
      await this.runMainCheck();
      this.scheduleMainCheck();
    }, this.config.checkInterval);
  }

  /**
   * 调度重试检测
   */
  scheduleRetryCheck() {
    if (!this.isRunning) return;
    
    this.retryTimer = setTimeout(async () => {
      await this.processRetryQueue();
      this.scheduleRetryCheck();
    }, this.config.retryInterval);
  }

  /**
   * 调度通知检查
   */
  scheduleNotificationCheck() {
    if (!this.isRunning) return;
    
    this.notifyTimer = setTimeout(async () => {
      await this.checkAndSendNotifications();
      this.scheduleNotificationCheck();
    }, this.config.notifyInterval);
  }

  /**
   * 执行主检测周期
   */
  async runMainCheck() {
    const startTime = Date.now();
    console.log(`🔍 开始主检测周期 - ${new Date().toLocaleString()}`);
    
    try {
      // 获取所有活跃站点
      const sites = await this.getActiveSites();
      console.log(`📊 获取到 ${sites.length} 个活跃站点`);
      
      if (sites.length === 0) {
        console.log('ℹ️  没有需要检测的站点');
        return;
      }
      
      // 分批检测
      await this.processSitesInBatches(sites);
      
      const duration = Date.now() - startTime;
      this.stats.lastRunTime = new Date();
      
      console.log(`✅ 主检测周期完成，耗时: ${duration}ms`);
      this.printStats();
      
    } catch (error) {
      console.error('❌ 主检测周期执行失败:', error);
    }
  }

  /**
   * 处理重试队列
   */
  async processRetryQueue() {
    if (this.retryQueue.size === 0) return;
    
    console.log(`🔄 处理重试队列，待重试站点: ${this.retryQueue.size}`);
    
    const retryPromises = [];
    const currentTime = Date.now();
    
    for (const [siteId, retryInfo] of this.retryQueue.entries()) {
      // 检查是否到了重试时间
      if (currentTime >= retryInfo.nextRetryTime) {
        retryPromises.push(this.retryCheckSite(siteId, retryInfo));
      }
    }
    
    if (retryPromises.length > 0) {
      await Promise.allSettled(retryPromises);
    }
  }

  /**
   * 重试检测单个站点
   */
  async retryCheckSite(siteId, retryInfo) {
    try {
      console.log(`🔄 重试检测站点: ${retryInfo.site.domain} (第${retryInfo.attempts}次)`);
      
      const result = await this.checkSite(retryInfo.site);
      this.stats.retryAttempts++;
      
      if (result.success) {
        // 成功：更新状态检测表，清零错误次数
        await this.updateSiteSuccessStatus(retryInfo.site.id, result.statusCode, result.responseTime);
        console.log(`✅ ${retryInfo.site.domain} 重试成功，错误次数已清零`);
        
        // 从重试队列中移除
        this.retryQueue.delete(siteId);
        
        this.stats.successfulChecks++;
      } else {
        // 失败：增加错误次数
        await this.incrementSiteErrorCount(
          retryInfo.site.id, 
          result.statusCode, 
          result.responseTime, 
          result.error
        );
        
        retryInfo.attempts++;
        
        if (retryInfo.attempts >= this.config.maxRetries) {
          console.log(`❌ ${retryInfo.site.domain} 达到最大重试次数，停止重试`);
          this.retryQueue.delete(siteId);
        } else {
          // 设置下次重试时间
          retryInfo.nextRetryTime = Date.now() + this.config.retryInterval;
          console.log(`⏳ ${retryInfo.site.domain} 将在${this.config.retryInterval/1000}秒后重试`);
        }
        
        this.stats.failedChecks++;
      }
      
    } catch (error) {
      console.error(`重试检测站点失败 ${retryInfo.site.domain}:`, error);
    }
  }

  /**
   * 分批处理站点
   */
  async processSitesInBatches(sites) {
    const batches = this.createBatches(sites, this.config.batchSize);
    
    for (let i = 0; i < batches.length; i++) {
      if (!this.isRunning) break;
      
      const batch = batches[i];
      console.log(`🔄 处理第 ${i + 1}/${batches.length} 批，包含 ${batch.length} 个站点`);
      
      await this.processBatch(batch);
      
      // 批次间延迟
      if (i < batches.length - 1) {
        await this.sleep(2000);
      }
    }
  }

  /**
   * 处理单个批次
   */
  async processBatch(batch) {
    const chunks = this.createBatches(batch, this.config.concurrency);
    
    for (const chunk of chunks) {
      if (!this.isRunning) break;
      
      const promises = chunk.map(site => this.checkAndProcessSite(site));
      await Promise.allSettled(promises);
      
      await this.sleep(500);
    }
  }

  /**
   * 检测并处理单个站点
   */
  async checkAndProcessSite(site) {
    try {
      const result = await this.checkSite(site);
      this.stats.totalChecks++;
      
      if (result.success) {
        // 1. 状态正常：直接写入数据库，错误次数清零
        await this.updateSiteSuccessStatus(site.id, result.statusCode, result.responseTime);
        this.stats.successfulChecks++;
        
        // 从重试队列中移除（如果存在）
        this.retryQueue.delete(site.id);
        
      } else {
        // 2. 不正常：流转重试
        this.addToRetryQueue(site, result);
        this.stats.failedChecks++;
      }
      
    } catch (error) {
      console.error(`检测站点失败 ${site.domain}:`, error);
      this.addToRetryQueue(site, {
        success: false,
        error: error.message,
        statusCode: 0,
        responseTime: this.config.timeout
      });
    }
  }

  /**
   * 添加到重试队列
   */
  addToRetryQueue(site, result) {
    const retryInfo = {
      site,
      attempts: 0,
      nextRetryTime: Date.now() + this.config.retryInterval,
      lastError: result.error,
      lastStatusCode: result.statusCode
    };
    
    this.retryQueue.set(site.id, retryInfo);
    console.log(`⏳ ${site.domain} 添加到重试队列`);
  }

  /**
   * 检测单个站点
   */
  async checkSite(site) {
    const startTime = Date.now();
    const url = site.site_url || `https://${site.domain}`;
    
    try {
      const response = await this.httpClient.get(url);
      const responseTime = Date.now() - startTime;
      
      return {
        success: true,
        statusCode: response.status,
        responseTime,
        headers: response.headers
      };
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        success: false,
        error: error.message,
        statusCode: error.response?.status || 0,
        responseTime,
        code: error.code
      };
    }
  }

  /**
   * 更新站点成功状态
   */
  async updateSiteSuccessStatus(platformId, statusCode, responseTime) {
    try {
      await this.db.execute(
        'CALL UpdateSiteSuccessStatus(?, ?, ?)',
        [platformId, statusCode, responseTime]
      );
    } catch (error) {
      console.error(`更新站点成功状态失败 ${platformId}:`, error);
    }
  }

  /**
   * 增加站点错误计数
   */
  async incrementSiteErrorCount(platformId, statusCode, responseTime, errorMessage) {
    try {
      await this.db.execute(
        'CALL IncrementSiteErrorCount(?, ?, ?, ?)',
        [platformId, statusCode, responseTime, errorMessage]
      );
    } catch (error) {
      console.error(`增加站点错误计数失败 ${platformId}:`, error);
    }
  }

  /**
   * 检查并发送通知
   */
  async checkAndSendNotifications() {
    try {
      console.log('📤 检查需要通知的故障站点...');
      
      // 3. 通知：查询错误次数 >= 3的站点
      const [failedSites] = await this.db.execute('CALL GetFailedSitesForNotification()');
      
      if (failedSites.length === 0) {
        console.log('✅ 没有需要通知的故障站点');
        return;
      }
      
      console.log(`🚨 发现 ${failedSites.length} 个故障站点需要通知`);
      
      // 发送通知
      for (const site of failedSites) {
        await this.sendFailureNotification(site);
      }
      
    } catch (error) {
      console.error('检查通知失败:', error);
    }
  }

  /**
   * 发送故障通知
   */
  async sendFailureNotification(site) {
    const message = {
      type: 'site_down',
      site: {
        id: site.platform_id,
        name: site.site_name || site.domain,
        domain: site.domain,
        url: site.site_url
      },
      error: {
        statusCode: site.status_code,
        errorCount: site.error_count,
        server: site.server_name || '未知',
        location: site.server_location || '未知',
        duration: site.failure_duration_text
      },
      timestamp: new Date()
    };
    
    console.log(`🚨 发送故障通知: ${site.domain} - 错误${site.error_count}次，持续${site.failure_duration_text}`);
    
    if (this.notificationService) {
      await this.notificationService.sendNotification(message);
      this.stats.notificationsSent++;
    }
    
    this.emit('site_down', message);
  }

  /**
   * 获取活跃站点
   */
  async getActiveSites() {
    const [rows] = await this.db.execute(`
      SELECT id, site_name, domain, site_url
      FROM websites 
      WHERE status = 'active'
      ORDER BY id ASC
    `);
    
    return rows;
  }

  /**
   * 工具方法
   */
  createBatches(array, size) {
    const batches = [];
    for (let i = 0; i < array.length; i += size) {
      batches.push(array.slice(i, i + size));
    }
    return batches;
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  printStats() {
    console.log('📈 监控统计:');
    console.log(`   总检测次数: ${this.stats.totalChecks}`);
    console.log(`   成功: ${this.stats.successfulChecks}, 失败: ${this.stats.failedChecks}`);
    console.log(`   重试次数: ${this.stats.retryAttempts}`);
    console.log(`   发送通知: ${this.stats.notificationsSent} 次`);
    console.log(`   重试队列: ${this.retryQueue.size} 个站点`);
    
    if (this.stats.totalChecks > 0) {
      const successRate = ((this.stats.successfulChecks / this.stats.totalChecks) * 100).toFixed(2);
      console.log(`   成功率: ${successRate}%`);
    }
  }

  getStats() {
    return {
      ...this.stats,
      retryQueueSize: this.retryQueue.size
    };
  }
}

module.exports = EnhancedSiteMonitor;
