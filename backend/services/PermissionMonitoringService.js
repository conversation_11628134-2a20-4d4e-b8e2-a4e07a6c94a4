const EventEmitter = require('events');
const PermissionCacheService = require('./PermissionCacheService');
const AuditService = require('./AuditService');
const db = require('../utils/db');

// 创建缓存服务实例
const cacheService = new PermissionCacheService();

/**
 * 权限系统监控服务
 * 负责监控权限验证性能、缓存命中率、异常访问等
 */
class PermissionMonitoringService extends EventEmitter {
  constructor() {
    super();
    
    // 监控指标
    this.metrics = {
      permissionChecks: {
        total: 0,
        success: 0,
        failed: 0,
        responseTimes: [],
        averageResponseTime: 0,
        maxResponseTime: 0,
        minResponseTime: Infinity
      },
      cacheMetrics: {
        hits: 0,
        misses: 0,
        hitRate: 0,
        size: 0
      },
      securityMetrics: {
        deniedAccess: 0,
        suspiciousActivity: 0,
        failedLogins: 0,
        privilegeEscalation: 0
      },
      alerts: []
    };
    
    // 监控配置
    this.config = {
      maxResponseTime: 100, // 最大响应时间阈值(ms)
      maxDeniedAccessCount: 10, // 最大拒绝访问次数阈值
      maxFailedLoginCount: 5, // 最大登录失败次数阈值
      suspiciousActivityThreshold: 80, // 可疑活动评分阈值
      metricsRetentionTime: 24 * 60 * 60 * 1000, // 指标保留时间(24小时)
      alertCooldown: 5 * 60 * 1000 // 告警冷却时间(5分钟)
    };
    
    // 告警状态
    this.alertStates = new Map();
    
    // 启动监控
    this.startMonitoring();
  }

  /**
   * 启动监控服务
   */
  startMonitoring() {
    console.log('权限监控服务已启动');
    
    // 定期收集缓存指标
    this.cacheMetricsInterval = setInterval(() => {
      this.collectCacheMetrics();
    }, 30000); // 每30秒收集一次
    
    // 定期检查安全指标
    this.securityMetricsInterval = setInterval(() => {
      this.collectSecurityMetrics();
    }, 60000); // 每分钟检查一次
    
    // 定期清理过期数据
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredData();
    }, 300000); // 每5分钟清理一次
  }

  /**
   * 停止监控服务
   */
  stopMonitoring() {
    if (this.cacheMetricsInterval) {
      clearInterval(this.cacheMetricsInterval);
    }
    if (this.securityMetricsInterval) {
      clearInterval(this.securityMetricsInterval);
    }
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    console.log('权限监控服务已停止');
  }

  /**
   * 记录权限检查指标
   */
  recordPermissionCheck(responseTime, success = true, permission = null, userId = null) {
    const metrics = this.metrics.permissionChecks;
    
    metrics.total++;
    if (success) {
      metrics.success++;
    } else {
      metrics.failed++;
    }
    
    // 记录响应时间
    metrics.responseTimes.push({
      time: responseTime,
      timestamp: Date.now(),
      permission,
      userId,
      success
    });
    
    // 保持响应时间数组大小
    if (metrics.responseTimes.length > 1000) {
      metrics.responseTimes = metrics.responseTimes.slice(-1000);
    }
    
    // 更新统计数据
    this.updateResponseTimeStats();
    
    // 检查性能告警
    if (responseTime > this.config.maxResponseTime) {
      this.triggerAlert('SLOW_PERMISSION_CHECK', {
        responseTime,
        threshold: this.config.maxResponseTime,
        permission,
        userId
      });
    }
    
    // 触发监控事件
    this.emit('permissionCheck', {
      responseTime,
      success,
      permission,
      userId,
      timestamp: Date.now()
    });
  }

  /**
   * 更新响应时间统计
   */
  updateResponseTimeStats() {
    const metrics = this.metrics.permissionChecks;
    const recentTimes = metrics.responseTimes
      .filter(item => Date.now() - item.timestamp < 300000) // 最近5分钟
      .map(item => item.time);
    
    if (recentTimes.length > 0) {
      metrics.averageResponseTime = recentTimes.reduce((a, b) => a + b, 0) / recentTimes.length;
      metrics.maxResponseTime = Math.max(...recentTimes);
      metrics.minResponseTime = Math.min(...recentTimes);
    }
  }

  /**
   * 收集缓存指标
   */
  async collectCacheMetrics() {
    try {
      const cacheStats = cacheService.getCacheStats();

      this.metrics.cacheMetrics = {
        hits: 0, // 暂时设为0，因为PermissionCacheService结构不同
        misses: 0, // 暂时设为0
        hitRate: cacheStats.overall?.hitRate || 0,
        size: cacheStats.memoryCache?.size || 0
      };

      // 检查缓存命中率告警
      const hitRate = cacheStats.overall?.hitRate || 0;
      if (hitRate < 0.8) { // 命中率低于80%
        this.triggerAlert('LOW_CACHE_HIT_RATE', {
          hitRate: hitRate,
          threshold: 0.8
        });
      }

      this.emit('cacheMetrics', this.metrics.cacheMetrics);

    } catch (error) {
      console.error('收集缓存指标失败:', error);
    }
  }

  /**
   * 收集安全指标
   */
  async collectSecurityMetrics() {
    try {
      // 检查数据库连接是否可用
      if (!db || typeof db.execute !== 'function') {
        console.warn('数据库连接不可用，跳过安全指标收集');
        return;
      }

      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      
      // 查询最近一小时的安全事件
      const [securityEvents] = await db.execute(`
        SELECT 
          action,
          result,
          COUNT(*) as count
        FROM audit_logs 
        WHERE created_at >= ? 
          AND (action = 'permission_access' OR action = 'login_attempt')
        GROUP BY action, result
      `, [oneHourAgo]);
      
      // 重置安全指标
      this.metrics.securityMetrics = {
        deniedAccess: 0,
        suspiciousActivity: 0,
        failedLogins: 0,
        privilegeEscalation: 0
      };
      
      // 统计安全事件
      for (const event of securityEvents) {
        if (event.action === 'permission_access' && event.result === 'denied') {
          this.metrics.securityMetrics.deniedAccess += event.count;
        }
        if (event.action === 'login_attempt' && event.result === 'failed') {
          this.metrics.securityMetrics.failedLogins += event.count;
        }
      }
      
      // 检查安全告警
      this.checkSecurityAlerts();
      
      this.emit('securityMetrics', this.metrics.securityMetrics);
      
    } catch (error) {
      console.error('收集安全指标失败:', error);
    }
  }

  /**
   * 检查安全告警
   */
  checkSecurityAlerts() {
    const security = this.metrics.securityMetrics;
    
    // 检查拒绝访问次数
    if (security.deniedAccess > this.config.maxDeniedAccessCount) {
      this.triggerAlert('HIGH_DENIED_ACCESS', {
        count: security.deniedAccess,
        threshold: this.config.maxDeniedAccessCount
      });
    }
    
    // 检查登录失败次数
    if (security.failedLogins > this.config.maxFailedLoginCount) {
      this.triggerAlert('HIGH_FAILED_LOGINS', {
        count: security.failedLogins,
        threshold: this.config.maxFailedLoginCount
      });
    }
    
    // 计算可疑活动评分
    const suspiciousScore = this.calculateSuspiciousScore();
    if (suspiciousScore > this.config.suspiciousActivityThreshold) {
      this.triggerAlert('SUSPICIOUS_ACTIVITY', {
        score: suspiciousScore,
        threshold: this.config.suspiciousActivityThreshold
      });
    }
  }

  /**
   * 计算可疑活动评分
   */
  calculateSuspiciousScore() {
    const security = this.metrics.securityMetrics;
    let score = 0;
    
    // 基于各种安全指标计算评分
    score += security.deniedAccess * 2; // 拒绝访问权重2
    score += security.failedLogins * 3; // 登录失败权重3
    score += security.privilegeEscalation * 10; // 权限提升权重10
    
    return Math.min(score, 100); // 最高100分
  }

  /**
   * 触发告警
   */
  triggerAlert(type, data) {
    const alertKey = `${type}_${JSON.stringify(data)}`;
    const now = Date.now();
    
    // 检查告警冷却时间
    if (this.alertStates.has(alertKey)) {
      const lastAlert = this.alertStates.get(alertKey);
      if (now - lastAlert < this.config.alertCooldown) {
        return; // 在冷却时间内，不重复告警
      }
    }
    
    const alert = {
      id: crypto.randomUUID(),
      type,
      level: this.getAlertLevel(type),
      message: this.getAlertMessage(type, data),
      data,
      timestamp: new Date().toISOString(),
      resolved: false
    };
    
    // 记录告警
    this.metrics.alerts.unshift(alert);
    
    // 保持告警数量
    if (this.metrics.alerts.length > 100) {
      this.metrics.alerts = this.metrics.alerts.slice(0, 100);
    }
    
    // 更新告警状态
    this.alertStates.set(alertKey, now);
    
    // 触发告警事件
    this.emit('alert', alert);
    
    console.warn(`权限系统告警 [${alert.level}]: ${alert.message}`);
    
    // 发送通知（如果配置了通知服务）
    this.sendAlertNotification(alert);
  }

  /**
   * 获取告警级别
   */
  getAlertLevel(type) {
    const levels = {
      'SLOW_PERMISSION_CHECK': 'warning',
      'LOW_CACHE_HIT_RATE': 'warning',
      'HIGH_DENIED_ACCESS': 'error',
      'HIGH_FAILED_LOGINS': 'error',
      'SUSPICIOUS_ACTIVITY': 'critical',
      'PRIVILEGE_ESCALATION': 'critical'
    };
    return levels[type] || 'info';
  }

  /**
   * 获取告警消息
   */
  getAlertMessage(type, data) {
    const messages = {
      'SLOW_PERMISSION_CHECK': `权限检查响应时间过慢: ${data.responseTime}ms (阈值: ${data.threshold}ms)`,
      'LOW_CACHE_HIT_RATE': `权限缓存命中率过低: ${(data.hitRate * 100).toFixed(1)}% (阈值: 80%)`,
      'HIGH_DENIED_ACCESS': `权限拒绝访问次数过高: ${data.count} (阈值: ${data.threshold})`,
      'HIGH_FAILED_LOGINS': `登录失败次数过高: ${data.count} (阈值: ${data.threshold})`,
      'SUSPICIOUS_ACTIVITY': `检测到可疑活动: 评分 ${data.score} (阈值: ${data.threshold})`,
      'PRIVILEGE_ESCALATION': `检测到权限提升尝试`
    };
    return messages[type] || `未知告警类型: ${type}`;
  }

  /**
   * 发送告警通知
   */
  async sendAlertNotification(alert) {
    try {
      // 这里可以集成各种通知服务
      // 例如：邮件、短信、钉钉、飞书等
      
      // 示例：记录到审计日志
      const auditService = new AuditService();
      await auditService.logSecurityEvent(null, 'security_alert', {
        alertType: alert.type,
        alertLevel: alert.level,
        alertMessage: alert.message,
        alertData: alert.data
      });
      
    } catch (error) {
      console.error('发送告警通知失败:', error);
    }
  }

  /**
   * 获取监控指标
   */
  getMetrics() {
    return {
      ...this.metrics,
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };
  }

  /**
   * 获取实时性能指标
   */
  getPerformanceMetrics() {
    const metrics = this.metrics.permissionChecks;
    const recentChecks = metrics.responseTimes
      .filter(item => Date.now() - item.timestamp < 300000); // 最近5分钟
    
    return {
      totalChecks: metrics.total,
      successRate: metrics.total > 0 ? (metrics.success / metrics.total * 100).toFixed(2) : 0,
      averageResponseTime: metrics.averageResponseTime.toFixed(2),
      maxResponseTime: metrics.maxResponseTime,
      minResponseTime: metrics.minResponseTime === Infinity ? 0 : metrics.minResponseTime,
      recentChecksCount: recentChecks.length,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 获取安全指标
   */
  getSecurityMetrics() {
    return {
      ...this.metrics.securityMetrics,
      suspiciousScore: this.calculateSuspiciousScore(),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 获取告警列表
   */
  getAlerts(limit = 50) {
    return this.metrics.alerts.slice(0, limit);
  }

  /**
   * 解决告警
   */
  resolveAlert(alertId) {
    const alert = this.metrics.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      alert.resolvedAt = new Date().toISOString();
      this.emit('alertResolved', alert);
      return true;
    }
    return false;
  }

  /**
   * 清理过期数据
   */
  cleanupExpiredData() {
    const now = Date.now();
    const retentionTime = this.config.metricsRetentionTime;
    
    // 清理过期的响应时间数据
    this.metrics.permissionChecks.responseTimes = 
      this.metrics.permissionChecks.responseTimes.filter(
        item => now - item.timestamp < retentionTime
      );
    
    // 清理过期的告警状态
    for (const [key, timestamp] of this.alertStates.entries()) {
      if (now - timestamp > retentionTime) {
        this.alertStates.delete(key);
      }
    }
    
    // 清理已解决的旧告警
    this.metrics.alerts = this.metrics.alerts.filter(alert => {
      if (alert.resolved) {
        const resolvedTime = new Date(alert.resolvedAt || alert.timestamp).getTime();
        return now - resolvedTime < retentionTime;
      }
      return true;
    });
  }

  /**
   * 重置指标
   */
  resetMetrics() {
    this.metrics = {
      permissionChecks: {
        total: 0,
        success: 0,
        failed: 0,
        responseTimes: [],
        averageResponseTime: 0,
        maxResponseTime: 0,
        minResponseTime: Infinity
      },
      cacheMetrics: {
        hits: 0,
        misses: 0,
        hitRate: 0,
        size: 0
      },
      securityMetrics: {
        deniedAccess: 0,
        suspiciousActivity: 0,
        failedLogins: 0,
        privilegeEscalation: 0
      },
      alerts: []
    };
    
    this.alertStates.clear();
    console.log('权限监控指标已重置');
  }
}

// 创建单例实例
const permissionMonitoringService = new PermissionMonitoringService();

module.exports = permissionMonitoringService;