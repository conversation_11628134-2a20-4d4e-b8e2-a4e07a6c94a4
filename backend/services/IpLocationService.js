const dns = require('dns').promises;
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

/**
 * IP解析和地理位置服务
 * 对应PHP文档中的IP解析和CDN检测功能
 */
class IpLocationService {
  constructor(db) {
    this.db = db;
    this.ipCache = new Map(); // IP信息缓存
    this.dnsCache = new Map(); // DNS解析缓存
  }

  /**
   * 解析域名获取IP地址
   * @param {string} domain - 域名
   * @returns {Promise<Object>} IP信息
   */
  async resolveDomainToIp(domain) {
    try {
      // 清理域名（移除协议和路径）
      const cleanDomain = domain.replace(/^https?:\/\//, '').split('/')[0].split(':')[0];
      
      // 检查缓存
      if (this.dnsCache.has(cleanDomain)) {
        const cached = this.dnsCache.get(cleanDomain);
        if (Date.now() - cached.timestamp < 300000) { // 5分钟缓存
          return cached.data;
        }
      }

      console.log(`🔍 解析域名: ${cleanDomain}`);
      
      // DNS解析
      const addresses = await dns.resolve4(cleanDomain);
      const primaryIp = addresses[0];
      
      if (!primaryIp) {
        throw new Error('域名解析失败');
      }

      // 获取地理位置信息
      const locationInfo = await this.getIpLocation(primaryIp);
      
      const result = {
        domain: cleanDomain,
        ip: primaryIp,
        allIps: addresses,
        location: locationInfo,
        resolvedAt: new Date(),
        status: 'success'
      };

      // 缓存结果
      this.dnsCache.set(cleanDomain, {
        data: result,
        timestamp: Date.now()
      });

      return result;
    } catch (error) {
      console.error(`❌ 域名解析失败 ${domain}:`, error.message);
      return {
        domain: domain,
        ip: null,
        allIps: [],
        location: null,
        resolvedAt: new Date(),
        status: 'failed',
        error: error.message
      };
    }
  }

  /**
   * 获取IP地理位置信息
   * @param {string} ip - IP地址
   * @returns {Promise<Object>} 地理位置信息
   */
  async getIpLocation(ip) {
    try {
      // 检查缓存
      if (this.ipCache.has(ip)) {
        const cached = this.ipCache.get(ip);
        if (Date.now() - cached.timestamp < 3600000) { // 1小时缓存
          return cached.data;
        }
      }

      console.log(`📍 获取IP地理位置: ${ip}`);

      // 使用多种方式获取IP位置信息
      let locationInfo = null;

      // 方法1: 使用ip-api.com (免费，有限制)
      try {
        locationInfo = await this.getLocationFromIpApi(ip);
      } catch (error) {
        console.warn('ip-api.com 查询失败:', error.message);
      }

      // 方法2: 使用whois命令（备用）
      if (!locationInfo) {
        try {
          locationInfo = await this.getLocationFromWhois(ip);
        } catch (error) {
          console.warn('whois 查询失败:', error.message);
        }
      }

      // 方法3: 使用内置IP段数据库（最后备用）
      if (!locationInfo) {
        locationInfo = await this.getLocationFromDatabase(ip);
      }

      // 缓存结果
      if (locationInfo) {
        this.ipCache.set(ip, {
          data: locationInfo,
          timestamp: Date.now()
        });
      }

      return locationInfo;
    } catch (error) {
      console.error(`❌ 获取IP位置失败 ${ip}:`, error.message);
      return null;
    }
  }

  /**
   * 使用ip-api.com获取位置信息
   * @param {string} ip - IP地址
   * @returns {Promise<Object>} 位置信息
   */
  async getLocationFromIpApi(ip) {
    const fetch = (await import('node-fetch')).default;
    
    const response = await fetch(`http://ip-api.com/json/${ip}?lang=zh-CN&fields=status,message,country,countryCode,region,regionName,city,lat,lon,timezone,isp,org,as,query`);
    const data = await response.json();
    
    if (data.status === 'success') {
      return {
        ip: data.query,
        country: data.country,
        countryCode: data.countryCode,
        region: data.regionName,
        city: data.city,
        latitude: data.lat,
        longitude: data.lon,
        timezone: data.timezone,
        isp: data.isp,
        organization: data.org,
        asn: data.as,
        source: 'ip-api.com'
      };
    } else {
      throw new Error(data.message || 'IP查询失败');
    }
  }

  /**
   * 使用whois命令获取位置信息
   * @param {string} ip - IP地址
   * @returns {Promise<Object>} 位置信息
   */
  async getLocationFromWhois(ip) {
    try {
      const { stdout } = await execAsync(`whois ${ip}`);
      const lines = stdout.split('\n');
      
      let country = '';
      let organization = '';
      
      for (const line of lines) {
        const lowerLine = line.toLowerCase();
        if (lowerLine.includes('country:')) {
          country = line.split(':')[1]?.trim() || '';
        }
        if (lowerLine.includes('org:') || lowerLine.includes('organization:')) {
          organization = line.split(':')[1]?.trim() || '';
        }
      }

      return {
        ip: ip,
        country: country,
        organization: organization,
        source: 'whois'
      };
    } catch (error) {
      throw new Error(`whois查询失败: ${error.message}`);
    }
  }

  /**
   * 从数据库获取位置信息（内置IP段数据）
   * @param {string} ip - IP地址
   * @returns {Promise<Object>} 位置信息
   */
  async getLocationFromDatabase(ip) {
    try {
      // 将IP转换为数字进行范围查询
      const ipNum = this.ipToNumber(ip);
      
      const [rows] = await this.db.execute(`
        SELECT country, region, city, isp
        FROM ip_location_ranges
        WHERE ip_start <= ? AND ip_end >= ?
        LIMIT 1
      `, [ipNum, ipNum]);

      if (rows.length > 0) {
        return {
          ip: ip,
          country: rows[0].country,
          region: rows[0].region,
          city: rows[0].city,
          isp: rows[0].isp,
          source: 'database'
        };
      }

      return null;
    } catch (error) {
      console.error('数据库IP查询失败:', error);
      return null;
    }
  }

  /**
   * 检测CDN类型
   * @param {Object} headers - HTTP响应头
   * @returns {string} CDN类型
   */
  detectCdnType(headers) {
    const headerKeys = Object.keys(headers).map(key => key.toLowerCase());
    const headerValues = Object.values(headers).join(' ').toLowerCase();

    // 检测各种CDN
    if (headerKeys.includes('x-qc-cache') || headerValues.includes('quic.cloud')) {
      return 'Quic.cloud';
    }
    
    if (headerKeys.includes('cf-cache-status') || headerKeys.includes('cf-ray') || headerValues.includes('cloudflare')) {
      return 'Cloudflare';
    }
    
    if (headerKeys.includes('x-swift-cachetime') || headerValues.includes('aliyun')) {
      return '阿里云';
    }
    
    if (headerKeys.includes('eo-cache-status') || headerValues.includes('tencent')) {
      return '腾讯云';
    }
    
    if (headerKeys.includes('x-cache') && (headerValues.includes('hit') || headerValues.includes('miss'))) {
      return 'Generic CDN';
    }
    
    if (headerValues.includes('fastly')) {
      return 'Fastly';
    }
    
    if (headerValues.includes('akamai')) {
      return 'Akamai';
    }

    return 'no';
  }

  /**
   * 更新网站的服务器信息
   * @param {number} websiteId - 网站ID
   * @param {string} domain - 域名
   * @returns {Promise<boolean>} 是否成功
   */
  async updateWebsiteServerInfo(websiteId, domain) {
    try {
      console.log(`🔄 更新网站服务器信息: ${domain} (ID: ${websiteId})`);
      
      // 解析域名获取IP
      const ipInfo = await this.resolveDomainToIp(domain);
      
      if (ipInfo.status === 'success') {
        // 更新数据库
        await this.db.execute(`
          UPDATE websites SET
            server_ip = ?,
            server_location = ?,
            server_isp = ?,
            ip_resolved_at = NOW(),
            updated_at = NOW()
          WHERE id = ?
        `, [
          ipInfo.ip,
          ipInfo.location ? `${ipInfo.location.country || ''} ${ipInfo.location.region || ''} ${ipInfo.location.city || ''}`.trim() : null,
          ipInfo.location?.isp || null,
          websiteId
        ]);

        console.log(`✅ 已更新网站 ${websiteId} 的服务器信息`);
        return true;
      } else {
        console.warn(`⚠️  网站 ${websiteId} 域名解析失败`);
        return false;
      }
    } catch (error) {
      console.error(`❌ 更新网站服务器信息失败 ${websiteId}:`, error);
      return false;
    }
  }

  /**
   * 批量更新所有网站的服务器信息
   * @returns {Promise<void>}
   */
  async updateAllWebsitesServerInfo() {
    try {
      console.log('🔄 开始批量更新网站服务器信息...');
      
      const [websites] = await this.db.execute(`
        SELECT id, domain, site_url
        FROM websites
        WHERE status = 'active'
          AND (domain IS NOT NULL OR site_url IS NOT NULL)
        ORDER BY ip_resolved_at IS NULL DESC, ip_resolved_at ASC
        LIMIT 50
      `);

      let successCount = 0;
      let failCount = 0;

      for (const website of websites) {
        const domain = website.domain || website.site_url;
        const success = await this.updateWebsiteServerInfo(website.id, domain);
        
        if (success) {
          successCount++;
        } else {
          failCount++;
        }

        // 避免请求过于频繁
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      console.log(`✅ 服务器信息更新完成: 成功 ${successCount}, 失败 ${failCount}`);
    } catch (error) {
      console.error('❌ 批量更新服务器信息失败:', error);
    }
  }

  /**
   * IP地址转数字
   * @param {string} ip - IP地址
   * @returns {number} IP数字
   */
  ipToNumber(ip) {
    return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet), 0) >>> 0;
  }

  /**
   * 数字转IP地址
   * @param {number} num - IP数字
   * @returns {string} IP地址
   */
  numberToIp(num) {
    return [
      (num >>> 24) & 255,
      (num >>> 16) & 255,
      (num >>> 8) & 255,
      num & 255
    ].join('.');
  }
}

module.exports = IpLocationService;
