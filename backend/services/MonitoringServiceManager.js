/**
 * 监控服务管理器
 * 统一管理不同的监控服务，提供服务切换和状态管理
 */

const EventEmitter = require('events');
const EnhancedMonitoringService = require('./EnhancedMonitoringService');
const MonitoringService = require('./MonitoringService'); // 原有服务
const NotificationService = require('./NotificationService');

class MonitoringServiceManager extends EventEmitter {
  constructor(db) {
    super();
    this.db = db;
    this.notificationService = new NotificationService();
    
    // 可用的监控服务
    this.services = {
      enhanced: null,    // 增强监控服务（新）
      legacy: null       // 原有监控服务
    };
    
    this.currentService = null;
    this.currentServiceType = 'enhanced'; // 默认使用增强服务
    
    // 服务状态
    this.status = {
      isRunning: false,
      currentService: null,
      startTime: null,
      stats: {}
    };
  }

  /**
   * 初始化监控服务
   */
  async initialize() {
    try {
      console.log('🔧 初始化监控服务管理器...');
      
      // 初始化通知服务
      await this.notificationService.loadConfigFromDatabase();
      
      // 创建监控服务实例
      this.services.enhanced = new EnhancedMonitoringService(this.db, this.notificationService);
      this.services.legacy = new MonitoringService(this.db, this.notificationService);
      
      // 设置事件监听
      this.setupEventListeners();
      
      // 设置当前服务
      this.currentService = this.services[this.currentServiceType];
      this.status.currentService = this.currentServiceType;
      
      console.log(`✅ 监控服务管理器初始化完成，当前服务: ${this.currentServiceType}`);
      
    } catch (error) {
      console.error('❌ 监控服务管理器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    // 为所有服务设置事件转发
    Object.entries(this.services).forEach(([type, service]) => {
      if (service) {
        service.on('service_started', () => {
          this.emit('service_started', { type, service });
        });
        
        service.on('service_stopped', () => {
          this.emit('service_stopped', { type, service });
        });
        
        service.on('check_completed', (data) => {
          this.emit('check_completed', { ...data, serviceType: type });
        });
        
        service.on('notification_sent', (data) => {
          this.emit('notification_sent', { ...data, serviceType: type });
        });
        
        service.on('config_updated', (config) => {
          this.emit('config_updated', { config, serviceType: type });
        });
      }
    });
  }

  /**
   * 启动监控服务
   */
  async start(serviceType = null) {
    try {
      // 如果指定了服务类型，则切换服务
      if (serviceType && serviceType !== this.currentServiceType) {
        await this.switchService(serviceType);
      }
      
      if (this.status.isRunning) {
        console.log('⚠️  监控服务已在运行中');
        return;
      }
      
      console.log(`🚀 启动监控服务: ${this.currentServiceType}`);
      
      await this.currentService.start();
      
      this.status.isRunning = true;
      this.status.startTime = new Date();
      
      console.log('✅ 监控服务启动成功');
      this.emit('manager_started');
      
    } catch (error) {
      console.error('❌ 启动监控服务失败:', error);
      throw error;
    }
  }

  /**
   * 停止监控服务
   */
  async stop() {
    try {
      if (!this.status.isRunning) {
        console.log('⚠️  监控服务未在运行');
        return;
      }
      
      console.log('🛑 停止监控服务...');
      
      await this.currentService.stop();
      
      this.status.isRunning = false;
      this.status.startTime = null;
      
      console.log('✅ 监控服务已停止');
      this.emit('manager_stopped');
      
    } catch (error) {
      console.error('❌ 停止监控服务失败:', error);
      throw error;
    }
  }

  /**
   * 重启监控服务
   */
  async restart(serviceType = null) {
    try {
      console.log('🔄 重启监控服务...');
      
      await this.stop();
      await this.sleep(2000); // 等待2秒
      await this.start(serviceType);
      
      console.log('✅ 监控服务重启完成');
      this.emit('manager_restarted');
      
    } catch (error) {
      console.error('❌ 重启监控服务失败:', error);
      throw error;
    }
  }

  /**
   * 切换监控服务
   */
  async switchService(serviceType) {
    try {
      if (!this.services[serviceType]) {
        throw new Error(`不支持的服务类型: ${serviceType}`);
      }
      
      if (serviceType === this.currentServiceType) {
        console.log(`⚠️  已经在使用 ${serviceType} 服务`);
        return;
      }
      
      console.log(`🔄 切换监控服务: ${this.currentServiceType} -> ${serviceType}`);
      
      const wasRunning = this.status.isRunning;
      
      // 停止当前服务
      if (wasRunning) {
        await this.currentService.stop();
      }
      
      // 切换服务
      this.currentServiceType = serviceType;
      this.currentService = this.services[serviceType];
      this.status.currentService = serviceType;
      
      // 如果之前在运行，则启动新服务
      if (wasRunning) {
        await this.currentService.start();
      }
      
      console.log(`✅ 服务切换完成: ${serviceType}`);
      this.emit('service_switched', { from: this.currentServiceType, to: serviceType });
      
    } catch (error) {
      console.error('❌ 切换监控服务失败:', error);
      throw error;
    }
  }

  /**
   * 手动检测网站
   */
  async manualCheck(websiteId) {
    try {
      if (!this.currentService.manualCheck) {
        throw new Error('当前监控服务不支持手动检测');
      }
      
      return await this.currentService.manualCheck(websiteId);
      
    } catch (error) {
      console.error(`手动检测失败 (ID: ${websiteId}):`, error);
      throw error;
    }
  }

  /**
   * 获取服务状态
   */
  getStatus() {
    const currentStats = this.currentService ? this.currentService.getStats() : {};
    
    return {
      ...this.status,
      stats: currentStats,
      availableServices: Object.keys(this.services),
      uptime: this.status.startTime ? Date.now() - this.status.startTime : 0
    };
  }

  /**
   * 获取所有服务的统计信息
   */
  getAllStats() {
    const stats = {};
    
    Object.entries(this.services).forEach(([type, service]) => {
      if (service && service.getStats) {
        stats[type] = service.getStats();
      }
    });
    
    return {
      current: this.currentServiceType,
      services: stats,
      manager: this.getStatus()
    };
  }

  /**
   * 更新监控配置
   */
  updateConfig(config, serviceType = null) {
    try {
      const targetService = serviceType ? this.services[serviceType] : this.currentService;
      
      if (!targetService || !targetService.updateConfig) {
        throw new Error('目标服务不支持配置更新');
      }
      
      targetService.updateConfig(config);
      
      console.log(`📝 ${serviceType || this.currentServiceType} 服务配置已更新`);
      this.emit('config_updated', { config, serviceType: serviceType || this.currentServiceType });
      
    } catch (error) {
      console.error('❌ 更新监控配置失败:', error);
      throw error;
    }
  }

  /**
   * 获取监控配置
   */
  getConfig(serviceType = null) {
    const targetService = serviceType ? this.services[serviceType] : this.currentService;
    
    if (!targetService || !targetService.getConfig) {
      throw new Error('目标服务不支持配置获取');
    }
    
    return targetService.getConfig();
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      const status = this.getStatus();
      const dbCheck = await this.checkDatabase();
      const notificationCheck = await this.checkNotificationService();
      
      return {
        healthy: status.isRunning && dbCheck.healthy && notificationCheck.healthy,
        status,
        database: dbCheck,
        notification: notificationCheck,
        timestamp: new Date()
      };
      
    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * 检查数据库连接
   */
  async checkDatabase() {
    try {
      await this.db.execute('SELECT 1');
      return { healthy: true, message: '数据库连接正常' };
    } catch (error) {
      return { healthy: false, error: error.message };
    }
  }

  /**
   * 检查通知服务
   */
  async checkNotificationService() {
    try {
      // 这里可以添加通知服务的健康检查逻辑
      return { healthy: true, message: '通知服务正常' };
    } catch (error) {
      return { healthy: false, error: error.message };
    }
  }

  /**
   * 工具方法
   */
  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = MonitoringServiceManager;
