/**
 * 动态权限配置服务类
 * 功能：
 * 1. 实现运行时权限配置更新
 * 2. 添加权限规则表达式支持
 * 3. 实现条件权限和时间权限
 * 4. 添加权限依赖关系管理
 * 5. 实现权限配置验证和测试
 */

const EventEmitter = require('events');

class DynamicPermissionService extends EventEmitter {
  constructor(database) {
    super();
    this.db = database;
    
    // 权限规则缓存
    this.ruleCache = new Map();
    this.configCache = new Map();
    
    // 权限表达式解析器
    this.expressionParser = new PermissionExpressionParser();
    
    // 条件权限处理器
    this.conditionHandlers = new Map();
    
    // 初始化默认条件处理器
    this.initializeConditionHandlers();
    
    // 启动配置监听
    this.startConfigWatcher();
  }

  /**
   * 初始化条件处理器
   */
  initializeConditionHandlers() {
    // 时间条件处理器
    this.conditionHandlers.set('time', (condition, context) => {
      const now = new Date();
      const currentTime = now.getHours() * 60 + now.getMinutes();
      
      if (condition.startTime && condition.endTime) {
        const start = this.parseTime(condition.startTime);
        const end = this.parseTime(condition.endTime);
        
        if (start <= end) {
          return currentTime >= start && currentTime <= end;
        } else {
          // 跨天的时间范围
          return currentTime >= start || currentTime <= end;
        }
      }
      
      return true;
    });

    // IP地址条件处理器
    this.conditionHandlers.set('ip', (condition, context) => {
      const clientIP = context.ip;
      
      if (condition.allowedIPs) {
        return condition.allowedIPs.includes(clientIP);
      }
      
      if (condition.blockedIPs) {
        return !condition.blockedIPs.includes(clientIP);
      }
      
      return true;
    });

    // 用户属性条件处理器
    this.conditionHandlers.set('user', (condition, context) => {
      const user = context.user;
      
      if (condition.roles && !condition.roles.includes(user.role)) {
        return false;
      }
      
      if (condition.departments && !condition.departments.includes(user.department)) {
        return false;
      }
      
      if (condition.minLevel && user.level < condition.minLevel) {
        return false;
      }
      
      return true;
    });

    // 资源条件处理器
    this.conditionHandlers.set('resource', (condition, context) => {
      const resource = context.resource;
      
      if (condition.resourceTypes && !condition.resourceTypes.includes(resource.type)) {
        return false;
      }
      
      if (condition.ownership && resource.ownerId !== context.user.id) {
        return false;
      }
      
      return true;
    });
  }

  /**
   * 解析时间字符串 (HH:MM)
   */
  parseTime(timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * 启动配置监听
   */
  startConfigWatcher() {
    // 定期检查配置更新
    setInterval(async () => {
      await this.refreshConfigurations();
    }, 30000); // 每30秒检查一次

    console.log('✅ 动态权限配置监听已启动');
  }

  /**
   * 刷新配置
   */
  async refreshConfigurations() {
    try {
      // 检查权限配置是否有更新
      const [configs] = await this.db.execute(`
        SELECT config_key, config_value, updated_at
        FROM permission_configs
        WHERE updated_at > ?
      `, [new Date(Date.now() - 30000)]); // 最近30秒的更新

      for (const config of configs) {
        const oldValue = this.configCache.get(config.config_key);
        const newValue = typeof config.config_value === 'string' 
          ? JSON.parse(config.config_value) 
          : config.config_value;

        if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
          this.configCache.set(config.config_key, newValue);
          this.emit('configUpdated', {
            key: config.config_key,
            oldValue,
            newValue,
            updatedAt: config.updated_at
          });
        }
      }

    } catch (error) {
      console.error('刷新权限配置失败:', error.message);
    }
  }

  /**
   * 检查动态权限
   */
  async checkDynamicPermission(userId, permissionCode, context = {}) {
    try {
      // 获取权限规则
      const rules = await this.getPermissionRules(permissionCode);
      
      if (!rules || rules.length === 0) {
        // 没有动态规则，使用静态权限检查
        return await this.checkStaticPermission(userId, permissionCode);
      }

      // 获取用户信息
      const user = await this.getUserInfo(userId);
      const fullContext = {
        ...context,
        user,
        timestamp: new Date(),
        permissionCode
      };

      // 检查所有规则
      for (const rule of rules) {
        const ruleResult = await this.evaluateRule(rule, fullContext);
        
        if (rule.effect === 'DENY' && ruleResult) {
          return { granted: false, reason: `拒绝规则: ${rule.name}` };
        }
        
        if (rule.effect === 'ALLOW' && ruleResult) {
          return { granted: true, reason: `允许规则: ${rule.name}` };
        }
      }

      // 如果没有匹配的规则，回退到静态权限
      const staticResult = await this.checkStaticPermission(userId, permissionCode);
      return { 
        granted: staticResult, 
        reason: staticResult ? '静态权限允许' : '静态权限拒绝' 
      };

    } catch (error) {
      console.error('检查动态权限失败:', error.message);
      return { granted: false, reason: '权限检查失败' };
    }
  }

  /**
   * 获取权限规则
   */
  async getPermissionRules(permissionCode) {
    try {
      // 先从缓存获取
      const cacheKey = `rules_${permissionCode}`;
      if (this.ruleCache.has(cacheKey)) {
        return this.ruleCache.get(cacheKey);
      }

      // 从数据库获取
      const [rules] = await this.db.execute(`
        SELECT * FROM permission_rules 
        WHERE permission_code = ? AND enabled = 1
        ORDER BY priority DESC
      `, [permissionCode]);

      const parsedRules = rules.map(rule => ({
        ...rule,
        conditions: typeof rule.conditions === 'string' 
          ? JSON.parse(rule.conditions) 
          : rule.conditions,
        expression: rule.expression
      }));

      // 缓存规则
      this.ruleCache.set(cacheKey, parsedRules);
      
      return parsedRules;

    } catch (error) {
      console.error('获取权限规则失败:', error.message);
      return [];
    }
  }

  /**
   * 评估权限规则
   */
  async evaluateRule(rule, context) {
    try {
      // 如果有表达式，优先使用表达式
      if (rule.expression) {
        return this.expressionParser.evaluate(rule.expression, context);
      }

      // 否则使用条件检查
      if (rule.conditions) {
        return this.evaluateConditions(rule.conditions, context);
      }

      return false;

    } catch (error) {
      console.error(`评估权限规则失败 (${rule.name}):`, error.message);
      return false;
    }
  }

  /**
   * 评估条件
   */
  evaluateConditions(conditions, context) {
    try {
      // 支持多种条件组合
      if (conditions.AND) {
        return conditions.AND.every(condition => 
          this.evaluateSingleCondition(condition, context)
        );
      }

      if (conditions.OR) {
        return conditions.OR.some(condition => 
          this.evaluateSingleCondition(condition, context)
        );
      }

      // 单个条件
      return this.evaluateSingleCondition(conditions, context);

    } catch (error) {
      console.error('评估条件失败:', error.message);
      return false;
    }
  }

  /**
   * 评估单个条件
   */
  evaluateSingleCondition(condition, context) {
    try {
      const handler = this.conditionHandlers.get(condition.type);
      
      if (!handler) {
        console.warn(`未知的条件类型: ${condition.type}`);
        return false;
      }

      return handler(condition, context);

    } catch (error) {
      console.error(`评估单个条件失败 (${condition.type}):`, error.message);
      return false;
    }
  }

  /**
   * 检查静态权限
   */
  async checkStaticPermission(userId, permissionCode) {
    try {
      const [result] = await this.db.execute(`
        SELECT 1 FROM v_user_effective_permissions 
        WHERE user_id = ? AND permission_code = ? AND granted = 1
      `, [userId, permissionCode]);

      return result.length > 0;

    } catch (error) {
      console.error('检查静态权限失败:', error.message);
      return false;
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(userId) {
    try {
      const [users] = await this.db.execute(`
        SELECT id, username, role, department, level, created_at
        FROM users 
        WHERE id = ?
      `, [userId]);

      return users[0] || null;

    } catch (error) {
      console.error('获取用户信息失败:', error.message);
      return null;
    }
  }

  /**
   * 创建权限规则
   */
  async createPermissionRule(ruleData) {
    try {
      const {
        name,
        description,
        permissionCode,
        effect = 'ALLOW',
        priority = 0,
        conditions,
        expression,
        enabled = true
      } = ruleData;

      // 验证规则
      await this.validateRule({ conditions, expression });

      const [result] = await this.db.execute(`
        INSERT INTO permission_rules 
        (name, description, permission_code, effect, priority, conditions, expression, enabled, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `, [
        name,
        description,
        permissionCode,
        effect,
        priority,
        JSON.stringify(conditions),
        expression,
        enabled
      ]);

      // 清除相关缓存
      this.clearRuleCache(permissionCode);

      return {
        id: result.insertId,
        ...ruleData
      };

    } catch (error) {
      console.error('创建权限规则失败:', error.message);
      throw error;
    }
  }

  /**
   * 更新权限规则
   */
  async updatePermissionRule(ruleId, updateData) {
    try {
      // 获取现有规则
      const [rules] = await this.db.execute(
        'SELECT * FROM permission_rules WHERE id = ?',
        [ruleId]
      );

      if (rules.length === 0) {
        throw new Error('权限规则不存在');
      }

      const rule = rules[0];

      // 验证更新数据
      if (updateData.conditions || updateData.expression) {
        await this.validateRule({
          conditions: updateData.conditions,
          expression: updateData.expression
        });
      }

      // 构建更新字段
      const updateFields = [];
      const params = [];

      Object.keys(updateData).forEach(key => {
        if (updateData[key] !== undefined) {
          if (key === 'conditions') {
            updateFields.push('conditions = ?');
            params.push(JSON.stringify(updateData[key]));
          } else {
            updateFields.push(`${key} = ?`);
            params.push(updateData[key]);
          }
        }
      });

      updateFields.push('updated_at = NOW()');
      params.push(ruleId);

      await this.db.execute(`
        UPDATE permission_rules 
        SET ${updateFields.join(', ')}
        WHERE id = ?
      `, params);

      // 清除相关缓存
      this.clearRuleCache(rule.permission_code);

      return { success: true };

    } catch (error) {
      console.error('更新权限规则失败:', error.message);
      throw error;
    }
  }

  /**
   * 删除权限规则
   */
  async deletePermissionRule(ruleId) {
    try {
      // 获取规则信息
      const [rules] = await this.db.execute(
        'SELECT permission_code FROM permission_rules WHERE id = ?',
        [ruleId]
      );

      if (rules.length === 0) {
        throw new Error('权限规则不存在');
      }

      const permissionCode = rules[0].permission_code;

      // 删除规则
      await this.db.execute('DELETE FROM permission_rules WHERE id = ?', [ruleId]);

      // 清除相关缓存
      this.clearRuleCache(permissionCode);

      return { success: true };

    } catch (error) {
      console.error('删除权限规则失败:', error.message);
      throw error;
    }
  }

  /**
   * 验证权限规则
   */
  async validateRule(ruleData) {
    try {
      const { conditions, expression } = ruleData;

      // 验证表达式
      if (expression) {
        this.expressionParser.validate(expression);
      }

      // 验证条件
      if (conditions) {
        this.validateConditions(conditions);
      }

      return { valid: true };

    } catch (error) {
      throw new Error(`规则验证失败: ${error.message}`);
    }
  }

  /**
   * 验证条件
   */
  validateConditions(conditions) {
    if (conditions.AND) {
      conditions.AND.forEach(condition => this.validateSingleCondition(condition));
    } else if (conditions.OR) {
      conditions.OR.forEach(condition => this.validateSingleCondition(condition));
    } else {
      this.validateSingleCondition(conditions);
    }
  }

  /**
   * 验证单个条件
   */
  validateSingleCondition(condition) {
    if (!condition.type) {
      throw new Error('条件必须指定类型');
    }

    if (!this.conditionHandlers.has(condition.type)) {
      throw new Error(`不支持的条件类型: ${condition.type}`);
    }

    // 根据类型验证特定字段
    switch (condition.type) {
      case 'time':
        if (condition.startTime && !/^\d{2}:\d{2}$/.test(condition.startTime)) {
          throw new Error('时间格式错误，应为 HH:MM');
        }
        if (condition.endTime && !/^\d{2}:\d{2}$/.test(condition.endTime)) {
          throw new Error('时间格式错误，应为 HH:MM');
        }
        break;
      case 'ip':
        if (condition.allowedIPs && !Array.isArray(condition.allowedIPs)) {
          throw new Error('allowedIPs 必须是数组');
        }
        if (condition.blockedIPs && !Array.isArray(condition.blockedIPs)) {
          throw new Error('blockedIPs 必须是数组');
        }
        break;
    }
  }

  /**
   * 清除规则缓存
   */
  clearRuleCache(permissionCode) {
    const cacheKey = `rules_${permissionCode}`;
    this.ruleCache.delete(cacheKey);
  }

  /**
   * 测试权限规则
   */
  async testPermissionRule(ruleData, testContext) {
    try {
      // 创建临时规则对象
      const tempRule = {
        name: 'test_rule',
        effect: ruleData.effect || 'ALLOW',
        conditions: ruleData.conditions,
        expression: ruleData.expression
      };

      // 验证规则
      await this.validateRule(ruleData);

      // 评估规则
      const result = await this.evaluateRule(tempRule, testContext);

      return {
        success: true,
        result,
        message: result ? '规则匹配成功' : '规则匹配失败'
      };

    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

/**
 * 权限表达式解析器
 */
class PermissionExpressionParser {
  constructor() {
    // 支持的操作符
    this.operators = {
      '&&': (a, b) => a && b,
      '||': (a, b) => a || b,
      '==': (a, b) => a === b,
      '!=': (a, b) => a !== b,
      '>': (a, b) => a > b,
      '<': (a, b) => a < b,
      '>=': (a, b) => a >= b,
      '<=': (a, b) => a <= b,
      'in': (a, b) => Array.isArray(b) && b.includes(a),
      'contains': (a, b) => typeof a === 'string' && a.includes(b)
    };

    // 支持的函数
    this.functions = {
      'now': () => new Date(),
      'hour': () => new Date().getHours(),
      'day': () => new Date().getDay(),
      'date': () => new Date().toDateString()
    };
  }

  /**
   * 评估表达式
   */
  evaluate(expression, context) {
    try {
      // 简单的表达式解析器实现
      // 实际项目中可能需要更复杂的解析器
      
      // 替换上下文变量
      let processedExpression = this.replaceVariables(expression, context);
      
      // 替换函数调用
      processedExpression = this.replaceFunctions(processedExpression);
      
      // 安全评估表达式
      return this.safeEvaluate(processedExpression);

    } catch (error) {
      console.error('表达式评估失败:', error.message);
      return false;
    }
  }

  /**
   * 替换变量
   */
  replaceVariables(expression, context) {
    return expression.replace(/\$\{([^}]+)\}/g, (match, path) => {
      const value = this.getNestedValue(context, path);
      return JSON.stringify(value);
    });
  }

  /**
   * 替换函数调用
   */
  replaceFunctions(expression) {
    return expression.replace(/(\w+)\(\)/g, (match, funcName) => {
      const func = this.functions[funcName];
      if (func) {
        const result = func();
        return JSON.stringify(result);
      }
      return match;
    });
  }

  /**
   * 获取嵌套值
   */
  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }

  /**
   * 安全评估表达式
   */
  safeEvaluate(expression) {
    // 这里应该使用安全的表达式评估器
    // 避免使用 eval() 等不安全的方法
    
    // 简化实现，实际项目中应该使用专门的表达式解析库
    try {
      // 只允许安全的操作
      const safeExpression = this.sanitizeExpression(expression);
      return Function(`"use strict"; return (${safeExpression})`)();
    } catch (error) {
      console.error('表达式评估错误:', error.message);
      return false;
    }
  }

  /**
   * 清理表达式
   */
  sanitizeExpression(expression) {
    // 移除潜在的危险操作
    const dangerous = [
      'eval', 'Function', 'setTimeout', 'setInterval',
      'require', 'import', 'process', 'global'
    ];

    for (const danger of dangerous) {
      if (expression.includes(danger)) {
        throw new Error(`表达式包含危险操作: ${danger}`);
      }
    }

    return expression;
  }

  /**
   * 验证表达式
   */
  validate(expression) {
    try {
      // 基本语法检查
      this.sanitizeExpression(expression);
      
      // 尝试解析（不执行）
      const testContext = {
        user: { id: 1, role: 'test' },
        resource: { type: 'test' },
        timestamp: new Date()
      };

      this.replaceVariables(expression, testContext);
      
      return { valid: true };

    } catch (error) {
      throw new Error(`表达式验证失败: ${error.message}`);
    }
  }
}

module.exports = DynamicPermissionService;