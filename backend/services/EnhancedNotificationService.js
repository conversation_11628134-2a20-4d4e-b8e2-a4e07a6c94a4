/**
 * 增强通知服务
 * 参考PHP系统的status_notice.php和renewal_notice.php
 * 实现状态异常通知和续费提醒通知，支持SMTP配置和通知频率控制
 */

const nodemailer = require('nodemailer');
const EventEmitter = require('events');

class EnhancedNotificationService extends EventEmitter {
  constructor(db) {
    super();
    this.db = db;
    this.transporter = null;
    this.config = {
      // 参考PHP系统的配置
      smtp: {
        host: 'smtp.feishu.cn',
        port: 465,
        secure: true,
        auth: {
          user: '<EMAIL>',
          pass: 'w2a1V7BH3h85zKbE'
        }
      },
      
      // 通知配置（参考PHP系统）
      statusNotice: {
        enabled: true,
        threshold: 600,        // 10分钟异常触发通知
        interval: 1800,        // 30分钟重复通知间隔
        recipients: [
          '<EMAIL>',
          '<EMAIL>'
        ],
        subject: '数字营销管理平台-网站异常警报'
      },
      
      // 续费提醒配置
      renewalNotice: {
        enabled: true,
        warningDays: 20,       // 20天内到期提醒
        recipients: [
          '<EMAIL>',
          'kiki<PERSON>@ehaitech.com'
        ],
        subject: '数字营销管理平台-续费提醒'
      },
      
      // SSL证书到期提醒
      sslNotice: {
        enabled: true,
        warningDays: 30,       // 30天内到期提醒
        recipients: [
          '<EMAIL>',
          '<EMAIL>'
        ],
        subject: '数字营销管理平台-SSL证书到期提醒'
      }
    };
    
    this.stats = {
      totalSent: 0,
      successCount: 0,
      failureCount: 0,
      lastSentTime: null
    };
  }

  /**
   * 初始化通知服务
   */
  async initialize() {
    try {
      console.log('📧 初始化增强通知服务...');
      
      // 从数据库加载配置
      await this.loadConfigFromDatabase();
      
      // 创建SMTP传输器
      await this.createTransporter();
      
      // 验证SMTP连接
      await this.verifyConnection();
      
      console.log('✅ 增强通知服务初始化成功');
      this.emit('service_initialized');
      
    } catch (error) {
      console.error('❌ 增强通知服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 从数据库加载配置
   */
  async loadConfigFromDatabase() {
    try {
      const [configs] = await this.db.execute(
        'SELECT config_key, config_value, config_type FROM monitor_system_configs WHERE config_key LIKE "notification_%" OR config_key LIKE "smtp_%"'
      );
      
      configs.forEach(config => {
        const keys = config.config_key.split('_');
        let value = config.config_value;
        
        // 类型转换
        switch (config.config_type) {
          case 'number':
            value = parseInt(value);
            break;
          case 'boolean':
            value = value === 'true';
            break;
          case 'json':
            try {
              value = JSON.parse(value);
            } catch (e) {
              value = {};
            }
            break;
        }
        
        // 更新配置
        if (keys[0] === 'smtp') {
          this.config.smtp[keys[1]] = value;
        } else if (keys[0] === 'notification') {
          const category = keys[1]; // status, renewal, ssl
          if (this.config[category + 'Notice']) {
            this.config[category + 'Notice'][keys[2]] = value;
          }
        }
      });
      
      console.log('✅ 通知配置加载完成');
      
    } catch (error) {
      console.warn('⚠️  从数据库加载通知配置失败，使用默认配置:', error.message);
    }
  }

  /**
   * 创建SMTP传输器
   */
  async createTransporter() {
    try {
      this.transporter = nodemailer.createTransporter({
        host: this.config.smtp.host,
        port: this.config.smtp.port,
        secure: this.config.smtp.secure,
        auth: {
          user: this.config.smtp.auth.user,
          pass: this.config.smtp.auth.pass
        },
        tls: {
          rejectUnauthorized: false
        }
      });
      
      console.log('✅ SMTP传输器创建成功');
      
    } catch (error) {
      console.error('❌ SMTP传输器创建失败:', error);
      throw error;
    }
  }

  /**
   * 验证SMTP连接
   */
  async verifyConnection() {
    try {
      if (!this.transporter) {
        throw new Error('SMTP传输器未初始化');
      }
      
      await this.transporter.verify();
      console.log('✅ SMTP连接验证成功');
      
    } catch (error) {
      console.warn('⚠️  SMTP连接验证失败:', error.message);
      // 不抛出错误，允许服务继续运行
    }
  }

  /**
   * 发送状态异常通知（参考PHP系统的status_notice.php）
   */
  async sendStatusNotification() {
    try {
      if (!this.config.statusNotice.enabled) {
        console.log('ℹ️  状态异常通知已禁用');
        return;
      }

      console.log('📊 检查需要发送状态异常通知的网站...');
      
      // 查询异常网站（参考PHP系统的查询逻辑）
      const [failedSites] = await this.db.execute(`
        SELECT 
          w.id, w.site_name, w.domain, w.site_url,
          w.access_status_code, w.last_error_check, w.consecutive_failures,
          w.next_notice, w.last_notification_time,
          TIMESTAMPDIFF(MINUTE, w.last_error_check, NOW()) as error_duration_minutes,
          GROUP_CONCAT(DISTINCT u.email) as manager_emails
        FROM websites w
        LEFT JOIN website_permissions wp ON wp.website_id = w.id
        LEFT JOIN users u ON wp.user_id = u.id
        WHERE w.status = 'active'
          AND w.status_check = 1
          AND w.consecutive_failures >= 5
          AND w.last_error_check IS NOT NULL
          AND w.last_error_check < DATE_SUB(NOW(), INTERVAL ? SECOND)
          AND (w.next_notice IS NULL OR w.next_notice <= NOW())
        GROUP BY w.id
        ORDER BY w.consecutive_failures DESC, w.last_error_check ASC
      `, [this.config.statusNotice.threshold]);

      if (failedSites.length === 0) {
        console.log('ℹ️  没有需要发送状态异常通知的网站');
        return;
      }

      console.log(`📢 发现 ${failedSites.length} 个网站需要发送状态异常通知`);

      // 构建邮件内容
      const emailContent = this.buildStatusNotificationEmail(failedSites);
      
      // 发送邮件
      await this.sendEmail({
        to: this.config.statusNotice.recipients,
        subject: this.config.statusNotice.subject,
        html: emailContent
      });

      // 更新通知状态
      await this.updateNotificationStatus(failedSites, 'status');
      
      // 记录通知日志
      await this.logNotifications(failedSites, 'status_notification');
      
      console.log(`✅ 状态异常通知发送完成: ${failedSites.length} 个网站`);
      this.emit('status_notification_sent', { sites: failedSites });
      
    } catch (error) {
      console.error('❌ 发送状态异常通知失败:', error);
      this.emit('notification_error', { type: 'status', error });
    }
  }

  /**
   * 发送续费提醒通知（参考PHP系统的renewal_notice.php）
   */
  async sendRenewalNotification() {
    try {
      if (!this.config.renewalNotice.enabled) {
        console.log('ℹ️  续费提醒通知已禁用');
        return;
      }

      console.log('📅 检查需要发送续费提醒的网站...');
      
      // 查询即将到期的网站
      const [expiringSites] = await this.db.execute(`
        SELECT 
          w.id, w.site_name, w.domain, w.site_url,
          w.expire_date, w.renewal_fee, w.project_amount,
          DATEDIFF(w.expire_date, CURDATE()) as days_until_expiry,
          p.name as platform_name
        FROM websites w
        LEFT JOIN platforms p ON w.platform_id = p.id
        WHERE w.status = 'active'
          AND w.expire_date IS NOT NULL
          AND w.expire_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
        ORDER BY w.expire_date ASC
      `, [this.config.renewalNotice.warningDays]);

      if (expiringSites.length === 0) {
        console.log('ℹ️  没有需要发送续费提醒的网站');
        return;
      }

      console.log(`📢 发现 ${expiringSites.length} 个网站需要发送续费提醒`);

      // 构建邮件内容
      const emailContent = this.buildRenewalNotificationEmail(expiringSites);
      
      // 发送邮件
      await this.sendEmail({
        to: this.config.renewalNotice.recipients,
        subject: this.config.renewalNotice.subject,
        html: emailContent
      });
      
      // 记录通知日志
      await this.logNotifications(expiringSites, 'renewal_reminder');
      
      console.log(`✅ 续费提醒通知发送完成: ${expiringSites.length} 个网站`);
      this.emit('renewal_notification_sent', { sites: expiringSites });
      
    } catch (error) {
      console.error('❌ 发送续费提醒通知失败:', error);
      this.emit('notification_error', { type: 'renewal', error });
    }
  }

  /**
   * 发送SSL证书到期提醒
   */
  async sendSSLExpiryNotification() {
    try {
      if (!this.config.sslNotice.enabled) {
        console.log('ℹ️  SSL证书到期提醒已禁用');
        return;
      }

      console.log('🔒 检查需要发送SSL证书到期提醒的网站...');
      
      // 查询SSL证书即将到期的网站
      const [expiringSites] = await this.db.execute(`
        SELECT 
          w.id, w.site_name, w.domain, w.site_url,
          w.ssl_expire_date, w.ssl_status, w.ssl_issuer,
          DATEDIFF(w.ssl_expire_date, CURDATE()) as days_until_expiry
        FROM websites w
        WHERE w.status = 'active'
          AND w.ssl_check = 1
          AND w.ssl_expire_date IS NOT NULL
          AND w.ssl_expire_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
        ORDER BY w.ssl_expire_date ASC
      `, [this.config.sslNotice.warningDays]);

      if (expiringSites.length === 0) {
        console.log('ℹ️  没有需要发送SSL证书到期提醒的网站');
        return;
      }

      console.log(`📢 发现 ${expiringSites.length} 个网站需要发送SSL证书到期提醒`);

      // 构建邮件内容
      const emailContent = this.buildSSLExpiryNotificationEmail(expiringSites);
      
      // 发送邮件
      await this.sendEmail({
        to: this.config.sslNotice.recipients,
        subject: this.config.sslNotice.subject,
        html: emailContent
      });
      
      // 记录通知日志
      await this.logNotifications(expiringSites, 'ssl_expiry');
      
      console.log(`✅ SSL证书到期提醒发送完成: ${expiringSites.length} 个网站`);
      this.emit('ssl_notification_sent', { sites: expiringSites });
      
    } catch (error) {
      console.error('❌ 发送SSL证书到期提醒失败:', error);
      this.emit('notification_error', { type: 'ssl', error });
    }
  }

  /**
   * 构建状态异常通知邮件内容
   */
  buildStatusNotificationEmail(sites) {
    const currentTime = new Date().toLocaleString('zh-CN');
    
    let html = `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
        <h2 style="color: #d32f2f; border-bottom: 2px solid #d32f2f; padding-bottom: 10px;">
          🚨 网站异常警报
        </h2>
        <p style="color: #666; margin-bottom: 20px;">
          检测时间: ${currentTime}<br>
          异常网站数量: <strong style="color: #d32f2f;">${sites.length}</strong>
        </p>
        
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <thead>
            <tr style="background-color: #f5f5f5;">
              <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">网站名称</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">域名</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">状态码</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">失败次数</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">持续时长</th>
            </tr>
          </thead>
          <tbody>
    `;
    
    sites.forEach(site => {
      const duration = Math.floor(site.error_duration_minutes / 60);
      const durationText = duration > 0 ? `${duration}小时${site.error_duration_minutes % 60}分钟` : `${site.error_duration_minutes}分钟`;
      
      html += `
        <tr>
          <td style="border: 1px solid #ddd; padding: 12px;">${site.site_name}</td>
          <td style="border: 1px solid #ddd; padding: 12px;">
            <a href="${site.site_url}" target="_blank">${site.domain}</a>
          </td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center; color: #d32f2f;">
            <strong>${site.access_status_code}</strong>
          </td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center; color: #d32f2f;">
            ${site.consecutive_failures}
          </td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">
            ${durationText}
          </td>
        </tr>
      `;
    });
    
    html += `
          </tbody>
        </table>
        
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <h4 style="color: #856404; margin-top: 0;">📋 处理建议</h4>
          <ul style="color: #856404; margin-bottom: 0;">
            <li>立即检查服务器状态和网络连接</li>
            <li>查看服务器日志和错误信息</li>
            <li>联系服务器提供商确认服务状态</li>
            <li>如需要，启动备用服务器或CDN</li>
          </ul>
        </div>
        
        <p style="color: #666; font-size: 12px; border-top: 1px solid #eee; padding-top: 15px;">
          此邮件由SiteManager监控系统自动发送，请勿直接回复。<br>
          如需帮助，请联系技术支持团队。
        </p>
      </div>
    `;
    
    return html;
  }

  /**
   * 构建续费提醒邮件内容
   */
  buildRenewalNotificationEmail(sites) {
    const currentTime = new Date().toLocaleString('zh-CN');
    
    let html = `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
        <h2 style="color: #ff9800; border-bottom: 2px solid #ff9800; padding-bottom: 10px;">
          📅 网站续费提醒
        </h2>
        <p style="color: #666; margin-bottom: 20px;">
          检测时间: ${currentTime}<br>
          即将到期网站数量: <strong style="color: #ff9800;">${sites.length}</strong>
        </p>
        
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <thead>
            <tr style="background-color: #f5f5f5;">
              <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">网站名称</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">域名</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">到期日期</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">剩余天数</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: right;">续费金额</th>
            </tr>
          </thead>
          <tbody>
    `;
    
    sites.forEach(site => {
      const expireDate = new Date(site.expire_date).toLocaleDateString('zh-CN');
      const renewalFee = site.renewal_fee ? `¥${site.renewal_fee}` : '待定';
      
      html += `
        <tr>
          <td style="border: 1px solid #ddd; padding: 12px;">${site.site_name}</td>
          <td style="border: 1px solid #ddd; padding: 12px;">
            <a href="${site.site_url}" target="_blank">${site.domain}</a>
          </td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">
            ${expireDate}
          </td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center; color: ${site.days_until_expiry <= 7 ? '#d32f2f' : '#ff9800'};">
            <strong>${site.days_until_expiry}天</strong>
          </td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: right;">
            ${renewalFee}
          </td>
        </tr>
      `;
    });
    
    html += `
          </tbody>
        </table>
        
        <div style="background-color: #e3f2fd; border: 1px solid #90caf9; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <h4 style="color: #1565c0; margin-top: 0;">📋 续费提醒</h4>
          <ul style="color: #1565c0; margin-bottom: 0;">
            <li>请及时联系客户确认续费意向</li>
            <li>准备续费合同和发票</li>
            <li>确认服务器和域名续费事宜</li>
            <li>更新项目管理系统中的到期时间</li>
          </ul>
        </div>
        
        <p style="color: #666; font-size: 12px; border-top: 1px solid #eee; padding-top: 15px;">
          此邮件由SiteManager监控系统自动发送，请勿直接回复。<br>
          如需帮助，请联系项目管理团队。
        </p>
      </div>
    `;
    
    return html;
  }

  /**
   * 构建SSL证书到期提醒邮件内容
   */
  buildSSLExpiryNotificationEmail(sites) {
    const currentTime = new Date().toLocaleString('zh-CN');
    
    let html = `
      <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
        <h2 style="color: #4caf50; border-bottom: 2px solid #4caf50; padding-bottom: 10px;">
          🔒 SSL证书到期提醒
        </h2>
        <p style="color: #666; margin-bottom: 20px;">
          检测时间: ${currentTime}<br>
          证书即将到期网站数量: <strong style="color: #ff9800;">${sites.length}</strong>
        </p>
        
        <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
          <thead>
            <tr style="background-color: #f5f5f5;">
              <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">网站名称</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">域名</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">到期日期</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: center;">剩余天数</th>
              <th style="border: 1px solid #ddd; padding: 12px; text-align: left;">证书颁发者</th>
            </tr>
          </thead>
          <tbody>
    `;
    
    sites.forEach(site => {
      const expireDate = new Date(site.ssl_expire_date).toLocaleDateString('zh-CN');
      const issuer = site.ssl_issuer || '未知';
      
      html += `
        <tr>
          <td style="border: 1px solid #ddd; padding: 12px;">${site.site_name}</td>
          <td style="border: 1px solid #ddd; padding: 12px;">
            <a href="${site.site_url}" target="_blank">${site.domain}</a>
          </td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center;">
            ${expireDate}
          </td>
          <td style="border: 1px solid #ddd; padding: 12px; text-align: center; color: ${site.days_until_expiry <= 7 ? '#d32f2f' : '#ff9800'};">
            <strong>${site.days_until_expiry}天</strong>
          </td>
          <td style="border: 1px solid #ddd; padding: 12px;">
            ${issuer}
          </td>
        </tr>
      `;
    });
    
    html += `
          </tbody>
        </table>
        
        <div style="background-color: #e8f5e8; border: 1px solid #4caf50; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
          <h4 style="color: #2e7d32; margin-top: 0;">📋 SSL证书续费提醒</h4>
          <ul style="color: #2e7d32; margin-bottom: 0;">
            <li>及时续费SSL证书，避免网站安全警告</li>
            <li>检查证书配置和自动续费设置</li>
            <li>确认域名验证和证书安装</li>
            <li>通知客户SSL证书续费事宜</li>
          </ul>
        </div>
        
        <p style="color: #666; font-size: 12px; border-top: 1px solid #eee; padding-top: 15px;">
          此邮件由SiteManager监控系统自动发送，请勿直接回复。<br>
          如需帮助，请联系技术支持团队。
        </p>
      </div>
    `;
    
    return html;
  }

  /**
   * 发送邮件
   */
  async sendEmail(options) {
    try {
      if (!this.transporter) {
        throw new Error('SMTP传输器未初始化');
      }

      const mailOptions = {
        from: `"SiteManager监控系统" <${this.config.smtp.auth.user}>`,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject: options.subject,
        html: options.html,
        text: options.text || this.htmlToText(options.html)
      };

      const result = await this.transporter.sendMail(mailOptions);

      this.stats.totalSent++;
      this.stats.successCount++;
      this.stats.lastSentTime = new Date();

      console.log('✅ 邮件发送成功:', result.messageId);
      this.emit('email_sent', { messageId: result.messageId, to: options.to });

      return result;

    } catch (error) {
      this.stats.totalSent++;
      this.stats.failureCount++;

      console.error('❌ 邮件发送失败:', error);
      this.emit('email_failed', { error: error.message, to: options.to });

      throw error;
    }
  }

  /**
   * 更新通知状态
   */
  async updateNotificationStatus(sites, type) {
    try {
      const siteIds = sites.map(site => site.id);
      const nextNoticeTime = new Date(Date.now() + this.config.statusNotice.interval * 1000);

      await this.db.execute(
        `UPDATE websites
         SET next_notice = ?,
             last_notification_time = NOW(),
             notification_sent = 1
         WHERE id IN (${siteIds.map(() => '?').join(',')})`,
        [nextNoticeTime, ...siteIds]
      );

      console.log(`✅ 更新通知状态: ${siteIds.length} 个网站`);

    } catch (error) {
      console.error('❌ 更新通知状态失败:', error);
    }
  }

  /**
   * 记录通知日志
   */
  async logNotifications(sites, triggerReason) {
    try {
      const notifications = sites.map(site => [
        site.id,
        'email',
        triggerReason,
        `${triggerReason} notification sent for ${site.site_name}`,
        'sent',
        null,
        null,
        new Date()
      ]);

      await this.db.execute(
        `INSERT INTO notification_logs
         (website_id, notification_type, trigger_reason, message, status, response_data, error_message, sent_at)
         VALUES ${notifications.map(() => '(?, ?, ?, ?, ?, ?, ?, ?)').join(', ')}`,
        notifications.flat()
      );

      console.log(`✅ 记录通知日志: ${notifications.length} 条`);

    } catch (error) {
      console.error('❌ 记录通知日志失败:', error);
    }
  }

  /**
   * HTML转文本
   */
  htmlToText(html) {
    if (!html) return '';

    return html
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 发送测试邮件
   */
  async sendTestEmail(to, message = '这是一条测试邮件') {
    try {
      const html = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #4caf50;">📧 SiteManager 测试邮件</h2>
          <p>${message}</p>
          <p style="color: #666; font-size: 12px;">
            发送时间: ${new Date().toLocaleString('zh-CN')}<br>
            此邮件由SiteManager监控系统发送
          </p>
        </div>
      `;

      return await this.sendEmail({
        to,
        subject: 'SiteManager 测试邮件',
        html
      });

    } catch (error) {
      console.error('❌ 发送测试邮件失败:', error);
      throw error;
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('📝 通知配置已更新');
    this.emit('config_updated', this.config);
  }

  /**
   * 获取配置
   */
  getConfig() {
    return { ...this.config };
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalSent > 0
        ? (this.stats.successCount / this.stats.totalSent * 100).toFixed(2) + '%'
        : '0%'
    };
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      if (!this.transporter) {
        return { healthy: false, error: 'SMTP传输器未初始化' };
      }

      await this.transporter.verify();

      return {
        healthy: true,
        message: 'SMTP连接正常',
        stats: this.getStats()
      };

    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        stats: this.getStats()
      };
    }
  }
}

module.exports = EnhancedNotificationService;
