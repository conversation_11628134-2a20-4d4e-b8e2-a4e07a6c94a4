/**
 * 监控服务集成脚本
 * 将增强监控服务集成到现有的SiteManager系统中
 */

const MonitoringServiceManager = require('./MonitoringServiceManager');

class MonitoringIntegration {
  constructor() {
    this.manager = null;
    this.isInitialized = false;
  }

  /**
   * 初始化监控集成
   */
  async initialize(db) {
    try {
      if (this.isInitialized) {
        console.log('⚠️  监控集成已初始化');
        return this.manager;
      }

      console.log('🔧 初始化监控服务集成...');
      
      // 创建监控服务管理器
      this.manager = new MonitoringServiceManager(db);
      
      // 初始化管理器
      await this.manager.initialize();
      
      // 设置事件监听
      this.setupEventListeners();
      
      this.isInitialized = true;
      
      console.log('✅ 监控服务集成初始化完成');
      return this.manager;
      
    } catch (error) {
      console.error('❌ 监控服务集成初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置事件监听
   */
  setupEventListeners() {
    if (!this.manager) return;

    // 监听服务启动事件
    this.manager.on('service_started', (data) => {
      console.log(`🚀 监控服务已启动: ${data.type}`);
    });

    // 监听服务停止事件
    this.manager.on('service_stopped', (data) => {
      console.log(`🛑 监控服务已停止: ${data.type}`);
    });

    // 监听检测完成事件
    this.manager.on('check_completed', (data) => {
      const { site, result, serviceType } = data;
      if (!result.success) {
        console.log(`⚠️  网站检测失败: ${site.domain} (${serviceType})`);
      }
    });

    // 监听通知发送事件
    this.manager.on('notification_sent', (data) => {
      console.log(`📢 通知已发送: ${data.site.domain} (${data.serviceType})`);
    });

    // 监听服务切换事件
    this.manager.on('service_switched', (data) => {
      console.log(`🔄 监控服务已切换: ${data.from} -> ${data.to}`);
    });

    // 监听配置更新事件
    this.manager.on('config_updated', (data) => {
      console.log(`📝 监控配置已更新: ${data.serviceType}`);
    });
  }

  /**
   * 启动监控服务
   */
  async start(serviceType = 'enhanced') {
    if (!this.isInitialized) {
      throw new Error('监控集成未初始化');
    }
    
    return await this.manager.start(serviceType);
  }

  /**
   * 停止监控服务
   */
  async stop() {
    if (!this.isInitialized) {
      throw new Error('监控集成未初始化');
    }
    
    return await this.manager.stop();
  }

  /**
   * 重启监控服务
   */
  async restart(serviceType = null) {
    if (!this.isInitialized) {
      throw new Error('监控集成未初始化');
    }
    
    return await this.manager.restart(serviceType);
  }

  /**
   * 获取监控状态
   */
  getStatus() {
    if (!this.isInitialized) {
      return { initialized: false, error: '监控集成未初始化' };
    }
    
    return {
      initialized: true,
      ...this.manager.getStatus()
    };
  }

  /**
   * 获取所有统计信息
   */
  getAllStats() {
    if (!this.isInitialized) {
      return { initialized: false, error: '监控集成未初始化' };
    }
    
    return this.manager.getAllStats();
  }

  /**
   * 手动检测网站
   */
  async manualCheck(websiteId) {
    if (!this.isInitialized) {
      throw new Error('监控集成未初始化');
    }
    
    return await this.manager.manualCheck(websiteId);
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    if (!this.isInitialized) {
      return {
        healthy: false,
        error: '监控集成未初始化',
        timestamp: new Date()
      };
    }
    
    return await this.manager.healthCheck();
  }

  /**
   * 切换监控服务
   */
  async switchService(serviceType) {
    if (!this.isInitialized) {
      throw new Error('监控集成未初始化');
    }
    
    return await this.manager.switchService(serviceType);
  }

  /**
   * 更新监控配置
   */
  updateConfig(config, serviceType = null) {
    if (!this.isInitialized) {
      throw new Error('监控集成未初始化');
    }
    
    return this.manager.updateConfig(config, serviceType);
  }

  /**
   * 获取监控配置
   */
  getConfig(serviceType = null) {
    if (!this.isInitialized) {
      throw new Error('监控集成未初始化');
    }
    
    return this.manager.getConfig(serviceType);
  }

  /**
   * 获取管理器实例（用于高级操作）
   */
  getManager() {
    return this.manager;
  }
}

// 创建全局单例
const monitoringIntegration = new MonitoringIntegration();

/**
 * 获取监控集成实例
 */
function getMonitoringIntegration() {
  return monitoringIntegration;
}

/**
 * 初始化监控集成（用于应用启动时调用）
 */
async function initializeMonitoring(db) {
  return await monitoringIntegration.initialize(db);
}

/**
 * 创建Express中间件，用于在路由中访问监控服务
 */
function createMonitoringMiddleware() {
  return (req, res, next) => {
    req.monitoring = monitoringIntegration;
    next();
  };
}

/**
 * 优雅关闭监控服务
 */
async function gracefulShutdown() {
  try {
    console.log('🛑 正在优雅关闭监控服务...');
    
    if (monitoringIntegration.isInitialized) {
      await monitoringIntegration.stop();
    }
    
    console.log('✅ 监控服务已优雅关闭');
  } catch (error) {
    console.error('❌ 监控服务关闭失败:', error);
  }
}

// 导出模块
module.exports = {
  MonitoringIntegration,
  getMonitoringIntegration,
  initializeMonitoring,
  createMonitoringMiddleware,
  gracefulShutdown
};

// 处理进程退出信号
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  gracefulShutdown().then(() => process.exit(1));
});
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
  gracefulShutdown().then(() => process.exit(1));
});
