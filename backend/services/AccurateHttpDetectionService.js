const dns = require('dns').promises;
const http = require('http');
const https = require('https');
const { URL } = require('url');

/**
 * 精确的HTTP检测服务
 * 基于PHP参考代码重新实现，解决误报问题
 */
class AccurateHttpDetectionService {
  constructor() {
    this.timeout = 30000; // 30秒超时
    this.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
  }

  /**
   * 检测网站状态
   * @param {string} url - 网站URL
   * @returns {Promise<Object>} 检测结果
   */
  async detectWebsiteStatus(url) {
    try {
      console.log(`🔍 开始检测网站: ${url}`);
      
      // 解析URL
      const urlObj = new URL(url);
      const host = urlObj.hostname;
      const protocol = urlObj.protocol;

      // 1. 检查是否为IP地址
      if (this.isIPAddress(host)) {
        console.log(`📍 检测到IP地址: ${host}`);
        return await this.detectByIP(url, host, protocol);
      }

      // 2. 域名解析
      const resolvedIP = await this.resolveHostname(host);
      if (!resolvedIP) {
        console.log(`❌ 域名解析失败: ${host}`);
        return {
          isAccessible: false,
          isSuccess: false,
          statusCode: 0,
          responseTime: 0,
          error: '域名解析失败',
          serverIP: '',
          cdnType: 'no',
          status: 'closed'
        };
      }

      console.log(`✅ 域名解析成功: ${host} -> ${resolvedIP}`);

      // 3. HTTP检测和CDN识别
      return await this.detectByDomain(url, host, resolvedIP, protocol);

    } catch (error) {
      console.error(`❌ 网站检测异常: ${url}`, error.message);
      return {
        isAccessible: false,
        isSuccess: false,
        statusCode: 0,
        responseTime: 0,
        error: error.message,
        serverIP: '',
        cdnType: 'no',
        status: 'closed'
      };
    }
  }

  /**
   * 检查是否为IP地址
   * @param {string} host - 主机名
   * @returns {boolean}
   */
  isIPAddress(host) {
    const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
    const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
    return ipv4Regex.test(host) || ipv6Regex.test(host);
  }

  /**
   * 域名解析
   * @param {string} hostname - 主机名
   * @returns {Promise<string|null>} IP地址或null
   */
  async resolveHostname(hostname) {
    try {
      const addresses = await dns.resolve4(hostname);
      return addresses && addresses.length > 0 ? addresses[0] : null;
    } catch (error) {
      console.log(`🔍 IPv4解析失败，尝试IPv6: ${hostname}`);
      try {
        const addresses = await dns.resolve6(hostname);
        return addresses && addresses.length > 0 ? addresses[0] : null;
      } catch (ipv6Error) {
        console.log(`❌ IPv6解析也失败: ${hostname}`);
        return null;
      }
    }
  }

  /**
   * 通过IP地址检测
   * @param {string} url - 完整URL
   * @param {string} ip - IP地址
   * @param {string} protocol - 协议
   * @returns {Promise<Object>}
   */
  async detectByIP(url, ip, protocol) {
    const headers = await this.getHeaders(url);
    
    if (!headers) {
      return {
        isAccessible: false,
        isSuccess: false,
        statusCode: 0,
        responseTime: 0,
        error: '无法获取HTTP头信息',
        serverIP: ip,
        cdnType: 'no',
        status: 'closed'
      };
    }

    return {
      isAccessible: true,
      isSuccess: true,
      statusCode: headers.statusCode,
      responseTime: headers.responseTime,
      error: null,
      serverIP: ip,
      cdnType: 'no',
      status: 'open'
    };
  }

  /**
   * 通过域名检测
   * @param {string} url - 完整URL
   * @param {string} host - 主机名
   * @param {string} resolvedIP - 解析的IP
   * @param {string} protocol - 协议
   * @returns {Promise<Object>}
   */
  async detectByDomain(url, host, resolvedIP, protocol) {
    console.log(`🔍 开始HTTP检测: ${url}`);
    const headers = await this.getHeaders(url);

    if (!headers) {
      console.log(`❌ HTTP检测失败: ${url} - 无法获取头信息`);
      return {
        isAccessible: false,
        isSuccess: false,
        statusCode: 0,
        responseTime: 0,
        error: '网站无法访问',
        serverIP: '',
        cdnType: 'no',
        status: 'closed'
      };
    }

    // CDN检测
    const cdnInfo = this.detectCDN(headers.headers);

    console.log(`✅ HTTP检测成功: ${url} - 状态码: ${headers.statusCode}`);
    return {
      isAccessible: true,
      isSuccess: true,
      statusCode: headers.statusCode,
      responseTime: headers.responseTime,
      error: null,
      serverIP: cdnInfo.cdnType === 'no' ? resolvedIP : '',
      cdnType: cdnInfo.cdnType,
      status: 'open'
    };
  }

  /**
   * 获取HTTP头信息
   * @param {string} url - URL
   * @returns {Promise<Object|null>}
   */
  async getHeaders(url) {
    return new Promise((resolve) => {
      let resolved = false;
      const safeResolve = (result) => {
        if (!resolved) {
          resolved = true;
          resolve(result);
        }
      };

      const startTime = Date.now();
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const client = isHttps ? https : http;
      
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: 'GET', // 使用GET请求，更兼容
        timeout: this.timeout,
        headers: {
          'User-Agent': this.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br',
          'Connection': 'close',
          'Cache-Control': 'no-cache'
        }
      };

      // HTTPS特殊配置
      if (isHttps) {
        options.rejectUnauthorized = false; // 允许自签名证书
      }

      const req = client.request(options, (res) => {
        const responseTime = Date.now() - startTime;

        // 读取少量响应数据以确保连接完整
        let data = '';
        res.on('data', (chunk) => {
          data += chunk;
          // 只读取前1KB数据，避免大文件影响性能
          if (data.length > 1024) {
            res.destroy();
            // 立即返回结果，避免等待end事件
            safeResolve({
              statusCode: res.statusCode,
              headers: res.headers,
              responseTime
            });
          }
        });

        res.on('end', () => {
          safeResolve({
            statusCode: res.statusCode,
            headers: res.headers,
            responseTime
          });
        });

        res.on('error', () => {
          safeResolve({
            statusCode: res.statusCode,
            headers: res.headers,
            responseTime
          });
        });
      });

      req.on('error', (error) => {
        console.log(`🔍 HTTP请求错误: ${url} - ${error.message}`);
        safeResolve(null);
      });

      req.on('timeout', () => {
        console.log(`⏰ HTTP请求超时: ${url}`);
        req.destroy();
        safeResolve(null);
      });

      req.end();
    });
  }

  /**
   * CDN检测
   * @param {Object} headers - HTTP头信息
   * @returns {Object} CDN信息
   */
  detectCDN(headers) {
    // 转换头信息为小写键名，便于检测
    const lowerHeaders = {};
    Object.keys(headers).forEach(key => {
      lowerHeaders[key.toLowerCase()] = headers[key];
    });

    // 按照PHP代码的检测顺序
    if (lowerHeaders['x-qc-cache']) {
      return { cdnType: 'Quic.cloud' };
    }
    
    if (lowerHeaders['cf-cache-status'] || lowerHeaders['cf-ray']) {
      return { cdnType: 'Cloudflare' };
    }
    
    if (lowerHeaders['x-swift-cachetime'] || lowerHeaders['x-swift-savetime']) {
      return { cdnType: '阿里云' };
    }
    
    if (lowerHeaders['eo-cache-status']) {
      return { cdnType: '腾讯云' };
    }

    // 其他CDN检测
    if (lowerHeaders['x-cache'] && lowerHeaders['x-cache'].includes('HIT')) {
      return { cdnType: 'Generic CDN' };
    }

    return { cdnType: 'no' };
  }

  /**
   * 批量检测网站
   * @param {Array} websites - 网站列表
   * @param {number} concurrency - 并发数
   * @returns {Promise<Array>} 检测结果
   */
  async batchDetect(websites, concurrency = 10) {
    console.log(`🚀 开始批量检测 ${websites.length} 个网站，并发数: ${concurrency}`);
    
    const results = [];
    const chunks = this.chunkArray(websites, concurrency);
    
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i];
      console.log(`🔍 检测第 ${i + 1} 批，包含 ${chunk.length} 个网站`);
      
      const chunkPromises = chunk.map(async (website) => {
        try {
          const result = await this.detectWebsiteStatus(website.site_url);
          return {
            ...website,
            ...result
          };
        } catch (error) {
          console.error(`❌ 检测网站失败: ${website.site_url}`, error.message);
          return {
            ...website,
            isAccessible: false,
            isSuccess: false,
            statusCode: 0,
            responseTime: 0,
            error: error.message,
            serverIP: '',
            cdnType: 'no',
            status: 'closed'
          };
        }
      });

      const chunkResults = await Promise.allSettled(chunkPromises);
      const validResults = chunkResults
        .filter(result => result.status === 'fulfilled')
        .map(result => result.value);
      results.push(...validResults);
      
      // 批次间稍作延迟，避免过于频繁的请求
      if (i < chunks.length - 1) {
        await this.sleep(1000);
      }
    }
    
    console.log(`✅ 批量检测完成，共检测 ${results.length} 个网站`);
    return results;
  }

  /**
   * 数组分块
   * @param {Array} array - 原数组
   * @param {number} size - 块大小
   * @returns {Array} 分块后的数组
   */
  chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  /**
   * 延迟函数
   * @param {number} ms - 毫秒数
   * @returns {Promise}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 判断状态码是否表示成功
   * @param {number} statusCode - HTTP状态码
   * @returns {boolean}
   */
  isSuccessStatusCode(statusCode) {
    // 2xx 和 3xx 状态码都认为是成功的
    return statusCode >= 200 && statusCode < 400;
  }

  /**
   * 获取状态描述
   * @param {number} statusCode - HTTP状态码
   * @returns {string}
   */
  getStatusDescription(statusCode) {
    const statusMap = {
      0: '连接失败',
      200: '正常',
      301: '永久重定向',
      302: '临时重定向',
      403: '禁止访问',
      404: '页面不存在',
      500: '服务器错误',
      502: '网关错误',
      503: '服务不可用',
      504: '网关超时'
    };

    return statusMap[statusCode] || `HTTP ${statusCode}`;
  }
}

module.exports = AccurateHttpDetectionService;
