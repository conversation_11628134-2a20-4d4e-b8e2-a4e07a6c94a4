/**
 * 增强监控服务
 * 参考PHP监控系统，实现高性能的网站存活检测
 * 支持HTTP状态检测、CDN检测、IP地址解析、SSL证书检查等功能
 */

const EventEmitter = require('events');
const axios = require('axios');
const https = require('https');
const dns = require('dns').promises;
const tls = require('tls');
const url = require('url');

class EnhancedMonitoringService extends EventEmitter {
  constructor(db, notificationService) {
    super();
    this.db = db;
    this.notificationService = notificationService;
    this.isRunning = false;
    this.activeChecks = new Map();
    
    // 参考PHP系统的配置
    this.config = {
      // 检测间隔配置（秒）
      intervals: {
        critical: 30,    // 关键网站30秒
        important: 60,   // 重要网站1分钟
        normal: 300,     // 普通网站5分钟
        low: 900         // 低优先级15分钟
      },
      
      // 报警阈值配置（秒）
      alarmThreshold: 600,        // 10分钟异常触发报警
      notificationInterval: 1800, // 30分钟重复通知间隔
      
      // 批处理配置
      batchSize: 20,              // 每批检测20个网站
      concurrency: 10,            // 并发检测数
      
      // 超时配置
      timeouts: {
        connect: 10000,           // 连接超时10秒
        response: 15000,          // 响应超时15秒
        dns: 5000,               // DNS解析超时5秒
        ssl: 8000                // SSL检测超时8秒
      },
      
      // 重试配置
      maxRetries: 3,
      retryDelay: 1000,
      
      // SSL配置
      sslWarningDays: 30,         // SSL证书30天预警
      
      // 状态码配置
      validStatusCodes: [200, 201, 202, 301, 302, 303, 307, 308]
    };
    
    // HTTP客户端配置
    this.httpClient = axios.create({
      timeout: this.config.timeouts.response,
      maxRedirects: 10,
      validateStatus: () => true, // 接受所有状态码
      httpsAgent: new https.Agent({
        rejectUnauthorized: false, // 允许自签名证书
        timeout: this.config.timeouts.connect
      })
    });
    
    // 统计信息
    this.stats = {
      totalChecks: 0,
      successfulChecks: 0,
      failedChecks: 0,
      avgResponseTime: 0,
      notificationsSent: 0,
      startTime: null
    };
  }

  /**
   * 启动监控服务
   */
  async start() {
    if (this.isRunning) {
      console.log('📊 增强监控服务已在运行中');
      return;
    }

    console.log('🚀 启动增强监控服务...');
    this.isRunning = true;
    this.stats.startTime = new Date();
    
    // 启动主检测循环
    this.startMainLoop();
    
    // 启动通知检查循环
    this.startNotificationLoop();
    
    console.log('✅ 增强监控服务启动成功');
    this.emit('service_started');
  }

  /**
   * 停止监控服务
   */
  async stop() {
    console.log('🛑 停止增强监控服务...');
    this.isRunning = false;
    
    // 等待当前检测完成
    await this.waitForActiveChecks();
    
    console.log('✅ 增强监控服务已停止');
    this.emit('service_stopped');
  }

  /**
   * 主检测循环
   */
  async startMainLoop() {
    while (this.isRunning) {
      try {
        await this.performBatchCheck();
        await this.sleep(5000); // 主循环间隔5秒
      } catch (error) {
        console.error('主检测循环错误:', error);
        await this.sleep(10000); // 错误时延长间隔
      }
    }
  }

  /**
   * 通知检查循环
   */
  async startNotificationLoop() {
    while (this.isRunning) {
      try {
        await this.checkAndSendNotifications();
        await this.sleep(60000); // 通知检查间隔1分钟
      } catch (error) {
        console.error('通知检查循环错误:', error);
        await this.sleep(120000); // 错误时延长间隔
      }
    }
  }

  /**
   * 执行批量检测
   */
  async performBatchCheck() {
    try {
      // 调用存储过程获取待检测网站
      const [sites] = await this.db.execute(
        'CALL GetSitesToCheck(?, ?)',
        [this.config.batchSize, 60] // 默认60秒间隔
      );

      if (sites.length === 0) {
        return;
      }

      console.log(`🔍 开始批量检测 ${sites.length} 个网站`);
      
      // 按优先级排序（tries字段升序）
      sites.sort((a, b) => (a.tries || 0) - (b.tries || 0));
      
      // 分批并发处理
      const batches = this.createBatches(sites, this.config.concurrency);
      
      for (const batch of batches) {
        if (!this.isRunning) break;
        
        const promises = batch.map(site => this.checkSiteWithRetry(site));
        await Promise.allSettled(promises);
        
        // 批次间短暂延迟
        await this.sleep(1000);
      }
      
      console.log(`✅ 批量检测完成: ${sites.length} 个网站`);
      
    } catch (error) {
      console.error('批量检测失败:', error);
    }
  }

  /**
   * 带重试的网站检测
   */
  async checkSiteWithRetry(site, retryCount = 0) {
    const checkId = `${site.id}_${Date.now()}`;
    this.activeChecks.set(checkId, site);
    
    try {
      const result = await this.checkSite(site);
      await this.saveCheckResult(site, result);
      this.stats.totalChecks++;
      
      if (result.success) {
        this.stats.successfulChecks++;
      } else {
        this.stats.failedChecks++;
        
        // 重试逻辑
        if (retryCount < this.config.maxRetries) {
          console.log(`🔄 重试检测网站 ${site.domain} (${retryCount + 1}/${this.config.maxRetries})`);
          await this.sleep(this.config.retryDelay);
          return await this.checkSiteWithRetry(site, retryCount + 1);
        }
      }
      
      return result;
      
    } catch (error) {
      console.error(`检测网站 ${site.domain} 失败:`, error);
      
      // 记录错误结果
      const errorResult = {
        success: false,
        error: error.message,
        statusCode: 0,
        responseTime: 0,
        timestamp: new Date()
      };
      
      await this.saveCheckResult(site, errorResult);
      this.stats.failedChecks++;
      
      return errorResult;
      
    } finally {
      this.activeChecks.delete(checkId);
    }
  }

  /**
   * 检测单个网站（核心检测逻辑）
   */
  async checkSite(site) {
    const startTime = Date.now();
    const result = {
      success: false,
      statusCode: 0,
      responseTime: 0,
      error: null,
      cdnDetected: 'no',
      serverIp: null,
      sslInfo: null,
      redirectCount: 0,
      timestamp: new Date()
    };

    try {
      // 1. DNS解析
      const dnsResult = await this.checkDNS(site.domain);
      if (dnsResult.success) {
        result.serverIp = dnsResult.ip;
      }

      // 2. HTTP检测
      const httpResult = await this.checkHTTP(site);
      result.statusCode = httpResult.statusCode;
      result.responseTime = httpResult.responseTime;
      result.redirectCount = httpResult.redirectCount;
      result.cdnDetected = httpResult.cdnDetected;
      
      // 判断是否成功
      result.success = this.config.validStatusCodes.includes(httpResult.statusCode);
      
      if (!result.success) {
        result.error = httpResult.error || `HTTP状态码异常: ${httpResult.statusCode}`;
      }

      // 3. SSL检测（如果启用且为HTTPS）
      if (site.ssl_check && site.site_url.startsWith('https://')) {
        const sslResult = await this.checkSSL(site.domain);
        result.sslInfo = sslResult;
      }

      // 4. 更新响应时间统计
      const totalTime = Date.now() - startTime;
      result.responseTime = Math.max(result.responseTime, totalTime);
      
      return result;

    } catch (error) {
      result.error = error.message;
      result.responseTime = Date.now() - startTime;
      return result;
    }
  }

  /**
   * HTTP状态检测（参考PHP系统逻辑）
   */
  async checkHTTP(site) {
    try {
      const startTime = Date.now();
      const response = await this.httpClient.get(site.site_url, {
        timeout: this.config.timeouts.response
      });
      
      const responseTime = Date.now() - startTime;
      
      // CDN检测（参考PHP系统的CDN检测逻辑）
      const cdnDetected = this.detectCDN(response.headers);
      
      return {
        statusCode: response.status,
        responseTime,
        redirectCount: response.request._redirects || 0,
        cdnDetected,
        headers: response.headers,
        success: true
      };
      
    } catch (error) {
      return {
        statusCode: error.response?.status || 0,
        responseTime: Date.now() - Date.now(),
        redirectCount: 0,
        cdnDetected: 'no',
        error: error.message,
        success: false
      };
    }
  }

  /**
   * CDN检测（参考PHP系统逻辑）
   */
  detectCDN(headers) {
    // 参考PHP系统的CDN检测逻辑
    if (headers['x-qc-cache']) {
      return 'Quic.cloud';
    } else if (headers['cf-cache-status'] || headers['cf-ray']) {
      return 'Cloudflare';
    } else if (headers['x-swift-cachetime']) {
      return '阿里云';
    } else if (headers['eo-cache-status']) {
      return '腾讯云';
    } else if (headers['x-cache'] && headers['x-cache'].includes('HIT')) {
      return 'Generic CDN';
    } else {
      return 'no';
    }
  }

  /**
   * DNS解析检测
   */
  async checkDNS(domain) {
    try {
      const addresses = await dns.resolve4(domain);
      return {
        success: true,
        ip: addresses[0],
        addresses
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * SSL证书检测（参考PHP系统逻辑）
   */
  async checkSSL(domain) {
    return new Promise((resolve) => {
      const options = {
        host: domain,
        port: 443,
        servername: domain,
        rejectUnauthorized: false
      };
      
      const socket = tls.connect(options, () => {
        try {
          const cert = socket.getPeerCertificate();
          const now = new Date();
          const expiry = new Date(cert.valid_to);
          const daysUntilExpiry = Math.ceil((expiry - now) / (1000 * 60 * 60 * 24));
          
          resolve({
            success: true,
            issuer: cert.issuer?.CN || 'Unknown',
            subject: cert.subject?.CN || domain,
            validFrom: cert.valid_from,
            validTo: cert.valid_to,
            daysUntilExpiry,
            isValid: daysUntilExpiry > 0,
            needsWarning: daysUntilExpiry <= this.config.sslWarningDays
          });
        } catch (error) {
          resolve({
            success: false,
            error: error.message
          });
        } finally {
          socket.end();
        }
      });
      
      socket.on('error', (error) => {
        resolve({
          success: false,
          error: error.message
        });
      });
      
      socket.setTimeout(this.config.timeouts.ssl, () => {
        socket.destroy();
        resolve({
          success: false,
          error: 'SSL检测超时'
        });
      });
    });
  }

  /**
   * 保存检测结果
   */
  async saveCheckResult(site, result) {
    try {
      // 调用存储过程更新状态统计
      await this.db.execute(
        'CALL UpdateWebsiteStatusStats(?, ?, ?, ?, ?, ?, ?, ?, ?)',
        [
          site.id,
          result.statusCode,
          result.responseTime,
          result.success,
          result.error,
          result.cdnDetected,
          result.serverIp,
          result.sslInfo?.daysUntilExpiry || null,
          result.sslInfo?.issuer || null
        ]
      );
      
      // 发出事件
      this.emit('check_completed', {
        site,
        result,
        timestamp: new Date()
      });
      
    } catch (error) {
      console.error(`保存检测结果失败 ${site.domain}:`, error);
    }
  }

  /**
   * 检查并发送通知
   */
  async checkAndSendNotifications() {
    try {
      // 调用存储过程获取需要通知的故障网站
      const [failedSites] = await this.db.execute(
        'CALL GetFailedSitesForNotification(?, ?)',
        [5, this.config.notificationInterval] // 5次失败，30分钟间隔
      );
      
      if (failedSites.length === 0) {
        return;
      }
      
      console.log(`📢 发现 ${failedSites.length} 个网站需要发送通知`);
      
      for (const site of failedSites) {
        await this.sendFailureNotification(site);
        this.stats.notificationsSent++;
      }
      
    } catch (error) {
      console.error('检查通知失败:', error);
    }
  }

  /**
   * 发送故障通知
   */
  async sendFailureNotification(site) {
    try {
      const message = {
        type: 'site_down',
        site: {
          id: site.id,
          name: site.site_name,
          domain: site.domain,
          url: site.site_url
        },
        error: {
          consecutiveFailures: site.consecutive_failures,
          lastErrorTime: site.last_error_check,
          statusCode: site.access_status_code,
          errorDuration: site.error_duration_minutes
        },
        timestamp: new Date()
      };
      
      if (this.notificationService) {
        await this.notificationService.sendNotification(message);
      }
      
      // 更新通知状态
      await this.db.execute(
        'UPDATE websites SET next_notice = DATE_ADD(NOW(), INTERVAL ? SECOND) WHERE id = ?',
        [this.config.notificationInterval, site.id]
      );
      
      console.log(`🚨 已发送故障通知: ${site.domain}`);
      this.emit('notification_sent', message);
      
    } catch (error) {
      console.error(`发送通知失败 ${site.domain}:`, error);
    }
  }

  /**
   * 工具方法
   */
  createBatches(array, batchSize) {
    const batches = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async waitForActiveChecks() {
    while (this.activeChecks.size > 0) {
      await this.sleep(1000);
    }
  }

  /**
   * 获取服务统计信息
   */
  getStats() {
    const uptime = this.stats.startTime ? Date.now() - this.stats.startTime : 0;

    return {
      ...this.stats,
      uptime,
      activeChecks: this.activeChecks.size,
      isRunning: this.isRunning,
      successRate: this.stats.totalChecks > 0
        ? (this.stats.successfulChecks / this.stats.totalChecks * 100).toFixed(2) + '%'
        : '0%'
    };
  }

  /**
   * 手动检测指定网站
   */
  async manualCheck(websiteId) {
    try {
      // 获取网站信息
      const [sites] = await this.db.execute(
        'SELECT * FROM websites WHERE id = ? AND status = "active"',
        [websiteId]
      );

      if (sites.length === 0) {
        throw new Error('网站不存在或未激活');
      }

      const site = sites[0];
      console.log(`🔍 手动检测网站: ${site.domain}`);

      // 执行检测
      const result = await this.checkSite(site);

      // 保存结果
      await this.saveCheckResult(site, result);

      return {
        success: true,
        site: {
          id: site.id,
          name: site.site_name,
          domain: site.domain,
          url: site.site_url
        },
        result,
        timestamp: new Date()
      };

    } catch (error) {
      console.error(`手动检测失败 (ID: ${websiteId}):`, error);
      throw error;
    }
  }

  /**
   * 更新监控配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('📝 监控配置已更新:', newConfig);
    this.emit('config_updated', this.config);
  }

  /**
   * 获取当前配置
   */
  getConfig() {
    return { ...this.config };
  }
}

module.exports = EnhancedMonitoringService;
