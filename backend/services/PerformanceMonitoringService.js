/**
 * 性能监控服务
 * 监控系统性能指标，包括检测频率、响应时间、资源使用等
 */

const EventEmitter = require('events');
const os = require('os');
const { performance } = require('perf_hooks');

class PerformanceMonitoringService extends EventEmitter {
  constructor(db, logger) {
    super();
    this.db = db;
    this.logger = logger;
    
    // 性能指标存储
    this.metrics = {
      // 检测性能指标
      monitoring: {
        totalChecks: 0,
        successfulChecks: 0,
        failedChecks: 0,
        avgResponseTime: 0,
        maxResponseTime: 0,
        minResponseTime: Infinity,
        checksPerSecond: 0,
        lastCheckTime: null
      },
      
      // 系统资源指标
      system: {
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
        networkConnections: 0,
        uptime: 0
      },
      
      // 数据库性能指标
      database: {
        connectionCount: 0,
        queryCount: 0,
        avgQueryTime: 0,
        slowQueries: 0,
        connectionPoolUsage: 0
      },
      
      // 通知性能指标
      notification: {
        totalNotifications: 0,
        successfulNotifications: 0,
        failedNotifications: 0,
        avgNotificationTime: 0,
        notificationQueue: 0
      },
      
      // 错误统计
      errors: {
        totalErrors: 0,
        errorsByType: {},
        errorRate: 0,
        lastError: null
      }
    };
    
    // 性能历史记录（最近1小时，每分钟一个数据点）
    this.history = {
      monitoring: [],
      system: [],
      database: [],
      notification: []
    };
    
    // 配置参数
    this.config = {
      metricsInterval: 60000, // 1分钟收集一次指标
      historyRetention: 60, // 保留60个数据点（1小时）
      alertThresholds: {
        cpuUsage: 80, // CPU使用率超过80%报警
        memoryUsage: 85, // 内存使用率超过85%报警
        responseTime: 5000, // 响应时间超过5秒报警
        errorRate: 10, // 错误率超过10%报警
        dbQueryTime: 1000 // 数据库查询时间超过1秒报警
      }
    };
    
    // 定时器
    this.metricsTimer = null;
    this.cleanupTimer = null;
    
    // 性能计数器
    this.counters = {
      checksStarted: 0,
      checksCompleted: 0,
      notificationsSent: 0,
      dbQueries: 0
    };
    
    // 时间窗口统计
    this.timeWindows = {
      '1m': { checks: 0, notifications: 0, errors: 0, startTime: Date.now() },
      '5m': { checks: 0, notifications: 0, errors: 0, startTime: Date.now() },
      '1h': { checks: 0, notifications: 0, errors: 0, startTime: Date.now() }
    };
  }

  /**
   * 启动性能监控
   */
  async start() {
    try {
      this.logger.info('启动性能监控服务...');
      
      // 初始化指标收集
      await this.collectInitialMetrics();
      
      // 启动定时指标收集
      this.metricsTimer = setInterval(() => {
        this.collectMetrics();
      }, this.config.metricsInterval);
      
      // 启动定时清理
      this.cleanupTimer = setInterval(() => {
        this.cleanupHistory();
        this.resetTimeWindows();
      }, 300000); // 5分钟清理一次
      
      this.logger.info('性能监控服务启动成功');
      this.emit('started');
      
    } catch (error) {
      this.logger.error('启动性能监控服务失败:', error);
      throw error;
    }
  }

  /**
   * 停止性能监控
   */
  async stop() {
    try {
      this.logger.info('停止性能监控服务...');
      
      if (this.metricsTimer) {
        clearInterval(this.metricsTimer);
        this.metricsTimer = null;
      }
      
      if (this.cleanupTimer) {
        clearInterval(this.cleanupTimer);
        this.cleanupTimer = null;
      }
      
      // 保存最终指标到数据库
      await this.saveMetricsToDatabase();
      
      this.logger.info('性能监控服务已停止');
      this.emit('stopped');
      
    } catch (error) {
      this.logger.error('停止性能监控服务失败:', error);
    }
  }

  /**
   * 记录网站检测开始
   */
  recordCheckStart(websiteId, checkType = 'http') {
    this.counters.checksStarted++;
    this.timeWindows['1m'].checks++;
    this.timeWindows['5m'].checks++;
    this.timeWindows['1h'].checks++;
    
    return {
      websiteId,
      checkType,
      startTime: performance.now(),
      timestamp: Date.now()
    };
  }

  /**
   * 记录网站检测完成
   */
  recordCheckComplete(checkContext, success, responseTime, error = null) {
    this.counters.checksCompleted++;
    this.metrics.monitoring.totalChecks++;
    
    if (success) {
      this.metrics.monitoring.successfulChecks++;
    } else {
      this.metrics.monitoring.failedChecks++;
      if (error) {
        this.recordError('check_failed', error);
      }
    }
    
    // 更新响应时间统计
    if (responseTime > 0) {
      this.updateResponseTimeStats(responseTime);
    }
    
    // 计算检测频率
    this.updateCheckFrequency();
    
    this.metrics.monitoring.lastCheckTime = Date.now();
  }

  /**
   * 记录通知发送
   */
  recordNotification(type, success, processingTime, error = null) {
    this.counters.notificationsSent++;
    this.metrics.notification.totalNotifications++;
    this.timeWindows['1m'].notifications++;
    this.timeWindows['5m'].notifications++;
    this.timeWindows['1h'].notifications++;
    
    if (success) {
      this.metrics.notification.successfulNotifications++;
    } else {
      this.metrics.notification.failedNotifications++;
      if (error) {
        this.recordError('notification_failed', error);
      }
    }
    
    // 更新通知处理时间
    if (processingTime > 0) {
      this.updateNotificationTimeStats(processingTime);
    }
  }

  /**
   * 记录数据库查询
   */
  recordDatabaseQuery(queryType, duration, success = true, error = null) {
    this.counters.dbQueries++;
    this.metrics.database.queryCount++;
    
    if (duration > 0) {
      this.updateDatabaseQueryStats(duration);
    }
    
    if (!success && error) {
      this.recordError('db_query_failed', error);
    }
    
    // 检查慢查询
    if (duration > this.config.alertThresholds.dbQueryTime) {
      this.metrics.database.slowQueries++;
      this.emit('slowQuery', { queryType, duration });
    }
  }

  /**
   * 记录错误
   */
  recordError(type, error) {
    this.metrics.errors.totalErrors++;
    this.timeWindows['1m'].errors++;
    this.timeWindows['5m'].errors++;
    this.timeWindows['1h'].errors++;
    
    if (!this.metrics.errors.errorsByType[type]) {
      this.metrics.errors.errorsByType[type] = 0;
    }
    this.metrics.errors.errorsByType[type]++;
    
    this.metrics.errors.lastError = {
      type,
      message: error.message || error,
      timestamp: Date.now()
    };
    
    // 计算错误率
    this.updateErrorRate();
    
    // 检查是否需要报警
    if (this.metrics.errors.errorRate > this.config.alertThresholds.errorRate) {
      this.emit('highErrorRate', this.metrics.errors.errorRate);
    }
  }

  /**
   * 收集初始指标
   */
  async collectInitialMetrics() {
    await this.collectSystemMetrics();
    await this.collectDatabaseMetrics();
  }

  /**
   * 收集所有指标
   */
  async collectMetrics() {
    try {
      await this.collectSystemMetrics();
      await this.collectDatabaseMetrics();
      
      // 添加到历史记录
      this.addToHistory();
      
      // 检查报警阈值
      this.checkAlertThresholds();
      
      // 定期保存到数据库
      if (this.metrics.monitoring.totalChecks % 100 === 0) {
        await this.saveMetricsToDatabase();
      }
      
    } catch (error) {
      this.logger.error('收集性能指标失败:', error);
      this.recordError('metrics_collection_failed', error);
    }
  }

  /**
   * 收集系统指标
   */
  async collectSystemMetrics() {
    // CPU使用率
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;
    
    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });
    
    this.metrics.system.cpuUsage = Math.round(100 - (totalIdle / totalTick) * 100);
    
    // 内存使用率
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    this.metrics.system.memoryUsage = Math.round(((totalMem - freeMem) / totalMem) * 100);
    
    // 系统运行时间
    this.metrics.system.uptime = os.uptime();
    
    // 网络连接数（简化版本）
    this.metrics.system.networkConnections = process._getActiveHandles().length;
  }

  /**
   * 收集数据库指标
   */
  async collectDatabaseMetrics() {
    try {
      // 获取数据库连接池状态
      if (this.db && this.db.pool) {
        this.metrics.database.connectionCount = this.db.pool._allConnections.length;
        this.metrics.database.connectionPoolUsage = Math.round(
          (this.db.pool._acquiringConnections.length / this.db.pool.config.connectionLimit) * 100
        );
      }
      
    } catch (error) {
      this.logger.warn('收集数据库指标失败:', error);
    }
  }

  /**
   * 更新响应时间统计
   */
  updateResponseTimeStats(responseTime) {
    const current = this.metrics.monitoring;
    
    // 计算平均响应时间
    if (current.totalChecks === 1) {
      current.avgResponseTime = responseTime;
    } else {
      current.avgResponseTime = Math.round(
        (current.avgResponseTime * (current.totalChecks - 1) + responseTime) / current.totalChecks
      );
    }
    
    // 更新最大最小值
    current.maxResponseTime = Math.max(current.maxResponseTime, responseTime);
    current.minResponseTime = Math.min(current.minResponseTime, responseTime);
  }

  /**
   * 更新检测频率
   */
  updateCheckFrequency() {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    
    // 计算每秒检测数
    this.metrics.monitoring.checksPerSecond = Math.round(
      this.timeWindows['1m'].checks / 60
    );
  }

  /**
   * 更新通知处理时间统计
   */
  updateNotificationTimeStats(processingTime) {
    const current = this.metrics.notification;
    
    if (current.totalNotifications === 1) {
      current.avgNotificationTime = processingTime;
    } else {
      current.avgNotificationTime = Math.round(
        (current.avgNotificationTime * (current.totalNotifications - 1) + processingTime) / current.totalNotifications
      );
    }
  }

  /**
   * 更新数据库查询统计
   */
  updateDatabaseQueryStats(duration) {
    const current = this.metrics.database;
    
    if (current.queryCount === 1) {
      current.avgQueryTime = duration;
    } else {
      current.avgQueryTime = Math.round(
        (current.avgQueryTime * (current.queryCount - 1) + duration) / current.queryCount
      );
    }
  }

  /**
   * 更新错误率
   */
  updateErrorRate() {
    const totalOperations = this.metrics.monitoring.totalChecks + this.metrics.notification.totalNotifications;
    if (totalOperations > 0) {
      this.metrics.errors.errorRate = Math.round(
        (this.metrics.errors.totalErrors / totalOperations) * 100
      );
    }
  }

  /**
   * 添加到历史记录
   */
  addToHistory() {
    const timestamp = Date.now();
    
    // 添加监控指标历史
    this.history.monitoring.push({
      timestamp,
      checksPerSecond: this.metrics.monitoring.checksPerSecond,
      avgResponseTime: this.metrics.monitoring.avgResponseTime,
      successRate: this.getSuccessRate()
    });
    
    // 添加系统指标历史
    this.history.system.push({
      timestamp,
      cpuUsage: this.metrics.system.cpuUsage,
      memoryUsage: this.metrics.system.memoryUsage
    });
    
    // 添加数据库指标历史
    this.history.database.push({
      timestamp,
      avgQueryTime: this.metrics.database.avgQueryTime,
      connectionPoolUsage: this.metrics.database.connectionPoolUsage
    });
    
    // 添加通知指标历史
    this.history.notification.push({
      timestamp,
      avgNotificationTime: this.metrics.notification.avgNotificationTime,
      successRate: this.getNotificationSuccessRate()
    });
  }

  /**
   * 清理历史记录
   */
  cleanupHistory() {
    const maxLength = this.config.historyRetention;
    
    Object.keys(this.history).forEach(key => {
      if (this.history[key].length > maxLength) {
        this.history[key] = this.history[key].slice(-maxLength);
      }
    });
  }

  /**
   * 重置时间窗口
   */
  resetTimeWindows() {
    const now = Date.now();
    
    // 重置1分钟窗口
    if (now - this.timeWindows['1m'].startTime >= 60000) {
      this.timeWindows['1m'] = { checks: 0, notifications: 0, errors: 0, startTime: now };
    }
    
    // 重置5分钟窗口
    if (now - this.timeWindows['5m'].startTime >= 300000) {
      this.timeWindows['5m'] = { checks: 0, notifications: 0, errors: 0, startTime: now };
    }
    
    // 重置1小时窗口
    if (now - this.timeWindows['1h'].startTime >= 3600000) {
      this.timeWindows['1h'] = { checks: 0, notifications: 0, errors: 0, startTime: now };
    }
  }

  /**
   * 检查报警阈值
   */
  checkAlertThresholds() {
    const thresholds = this.config.alertThresholds;
    
    // CPU使用率报警
    if (this.metrics.system.cpuUsage > thresholds.cpuUsage) {
      this.emit('highCpuUsage', this.metrics.system.cpuUsage);
    }
    
    // 内存使用率报警
    if (this.metrics.system.memoryUsage > thresholds.memoryUsage) {
      this.emit('highMemoryUsage', this.metrics.system.memoryUsage);
    }
    
    // 响应时间报警
    if (this.metrics.monitoring.avgResponseTime > thresholds.responseTime) {
      this.emit('highResponseTime', this.metrics.monitoring.avgResponseTime);
    }
  }

  /**
   * 获取成功率
   */
  getSuccessRate() {
    if (this.metrics.monitoring.totalChecks === 0) return 100;
    return Math.round((this.metrics.monitoring.successfulChecks / this.metrics.monitoring.totalChecks) * 100);
  }

  /**
   * 获取通知成功率
   */
  getNotificationSuccessRate() {
    if (this.metrics.notification.totalNotifications === 0) return 100;
    return Math.round((this.metrics.notification.successfulNotifications / this.metrics.notification.totalNotifications) * 100);
  }

  /**
   * 获取当前指标
   */
  getCurrentMetrics() {
    return {
      ...this.metrics,
      timestamp: Date.now(),
      successRate: this.getSuccessRate(),
      notificationSuccessRate: this.getNotificationSuccessRate()
    };
  }

  /**
   * 获取历史指标
   */
  getHistoryMetrics(category = null, duration = '1h') {
    if (category) {
      return this.history[category] || [];
    }
    
    return this.history;
  }

  /**
   * 保存指标到数据库
   */
  async saveMetricsToDatabase() {
    try {
      const metrics = this.getCurrentMetrics();
      
      await this.db.execute(`
        INSERT INTO performance_metrics (
          timestamp,
          total_checks,
          successful_checks,
          failed_checks,
          avg_response_time,
          checks_per_second,
          cpu_usage,
          memory_usage,
          total_notifications,
          successful_notifications,
          error_rate,
          metrics_data
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        new Date(),
        metrics.monitoring.totalChecks,
        metrics.monitoring.successfulChecks,
        metrics.monitoring.failedChecks,
        metrics.monitoring.avgResponseTime,
        metrics.monitoring.checksPerSecond,
        metrics.system.cpuUsage,
        metrics.system.memoryUsage,
        metrics.notification.totalNotifications,
        metrics.notification.successfulNotifications,
        metrics.errors.errorRate,
        JSON.stringify(metrics)
      ]);
      
    } catch (error) {
      this.logger.error('保存性能指标到数据库失败:', error);
    }
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    const metrics = this.getCurrentMetrics();
    const issues = [];
    
    // 检查各项指标是否正常
    if (metrics.system.cpuUsage > this.config.alertThresholds.cpuUsage) {
      issues.push(`CPU使用率过高: ${metrics.system.cpuUsage}%`);
    }
    
    if (metrics.system.memoryUsage > this.config.alertThresholds.memoryUsage) {
      issues.push(`内存使用率过高: ${metrics.system.memoryUsage}%`);
    }
    
    if (metrics.monitoring.avgResponseTime > this.config.alertThresholds.responseTime) {
      issues.push(`平均响应时间过长: ${metrics.monitoring.avgResponseTime}ms`);
    }
    
    if (metrics.errors.errorRate > this.config.alertThresholds.errorRate) {
      issues.push(`错误率过高: ${metrics.errors.errorRate}%`);
    }
    
    return {
      healthy: issues.length === 0,
      issues,
      metrics: {
        cpuUsage: metrics.system.cpuUsage,
        memoryUsage: metrics.system.memoryUsage,
        avgResponseTime: metrics.monitoring.avgResponseTime,
        errorRate: metrics.errors.errorRate,
        checksPerSecond: metrics.monitoring.checksPerSecond
      }
    };
  }
}

module.exports = PerformanceMonitoringService;
