/**
 * 审计告警服务类
 * 功能：
 * 1. 实现权限异常访问检测
 * 2. 添加权限提升尝试告警
 * 3. 实现可疑操作模式识别
 * 4. 添加实时安全事件通知
 * 5. 创建安全报告生成功能
 */

const EventEmitter = require('events');

class AuditAlertService extends EventEmitter {
  constructor(database) {
    super();
    this.db = database;
    
    // 告警配置
    this.config = {
      // 异常检测阈值
      maxFailedAttempts: 5,           // 最大失败尝试次数
      timeWindow: 300,                // 时间窗口（秒）
      maxRequestsPerMinute: 100,      // 每分钟最大请求数
      suspiciousIpThreshold: 10,      // 可疑IP阈值
      
      // 权限提升检测
      privilegeEscalationPatterns: [
        'role_change',
        'permission_change',
        'user_create',
        'user_delete'
      ],
      
      // 敏感操作列表
      sensitiveOperations: [
        'system.backup.create',
        'system.settings.edit',
        'user.user.delete',
        'system.permission.manage'
      ],
      
      // 告警级别
      alertLevels: {
        LOW: 'low',
        MEDIUM: 'medium',
        HIGH: 'high',
        CRITICAL: 'critical'
      },
      
      // 通知配置
      notifications: {
        email: true,
        webhook: false,
        realtime: true
      }
    };

    // 告警缓存（防止重复告警）
    this.alertCache = new Map();
    this.cacheTimeout = 300000; // 5分钟

    // 启动监控
    this.startMonitoring();
  }

  /**
   * 启动安全监控
   */
  startMonitoring() {
    console.log('🔒 启动安全监控服务...');
    
    // 定期检测异常访问
    setInterval(() => {
      this.detectAnomalousAccess();
    }, 60000); // 每分钟检测一次

    // 定期清理告警缓存
    setInterval(() => {
      this.cleanupAlertCache();
    }, 300000); // 每5分钟清理一次

    console.log('✅ 安全监控服务已启动');
  }

  /**
   * 检测异常访问模式
   */
  async detectAnomalousAccess() {
    try {
      const now = new Date();
      const timeWindow = new Date(now.getTime() - this.config.timeWindow * 1000);

      // 检测频繁失败的用户
      await this.detectFrequentFailures(timeWindow);

      // 检测异常高频访问
      await this.detectHighFrequencyAccess(timeWindow);

      // 检测可疑IP地址
      await this.detectSuspiciousIPs(timeWindow);

      // 检测权限提升尝试
      await this.detectPrivilegeEscalation(timeWindow);

      // 检测异常时间访问
      await this.detectOffHoursAccess(timeWindow);

    } catch (error) {
      console.error('异常访问检测失败:', error.message);
    }
  }

  /**
   * 检测频繁失败的用户
   */
  async detectFrequentFailures(timeWindow) {
    try {
      const [results] = await this.db.execute(`
        SELECT 
          user_id,
          u.username,
          u.role,
          COUNT(*) as failure_count,
          MAX(created_at) as last_failure,
          GROUP_CONCAT(DISTINCT ip_address) as ip_addresses
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE al.result IN ('denied', 'failure') 
          AND al.created_at >= ?
        GROUP BY user_id, u.username, u.role
        HAVING failure_count >= ?
        ORDER BY failure_count DESC
      `, [timeWindow, this.config.maxFailedAttempts]);

      for (const result of results) {
        const alertKey = `frequent_failures_${result.user_id}`;
        
        if (!this.isAlertCached(alertKey)) {
          const alert = {
            type: 'FREQUENT_FAILURES',
            level: result.failure_count > this.config.maxFailedAttempts * 2 ? 'HIGH' : 'MEDIUM',
            userId: result.user_id,
            username: result.username,
            userRole: result.role,
            details: {
              failureCount: result.failure_count,
              lastFailure: result.last_failure,
              ipAddresses: result.ip_addresses?.split(',') || [],
              timeWindow: this.config.timeWindow
            },
            timestamp: new Date().toISOString(),
            message: `用户 ${result.username} 在 ${this.config.timeWindow} 秒内失败 ${result.failure_count} 次`
          };

          await this.triggerAlert(alert);
          this.cacheAlert(alertKey);
        }
      }

    } catch (error) {
      console.error('检测频繁失败用户失败:', error.message);
    }
  }

  /**
   * 检测异常高频访问
   */
  async detectHighFrequencyAccess(timeWindow) {
    try {
      const [results] = await this.db.execute(`
        SELECT 
          user_id,
          u.username,
          u.role,
          COUNT(*) as request_count,
          MAX(created_at) as last_request,
          COUNT(DISTINCT ip_address) as ip_count
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE al.created_at >= ?
        GROUP BY user_id, u.username, u.role
        HAVING request_count >= ?
        ORDER BY request_count DESC
      `, [timeWindow, this.config.maxRequestsPerMinute * (this.config.timeWindow / 60)]);

      for (const result of results) {
        const alertKey = `high_frequency_${result.user_id}`;
        
        if (!this.isAlertCached(alertKey)) {
          const alert = {
            type: 'HIGH_FREQUENCY_ACCESS',
            level: result.request_count > this.config.maxRequestsPerMinute * 2 ? 'HIGH' : 'MEDIUM',
            userId: result.user_id,
            username: result.username,
            userRole: result.role,
            details: {
              requestCount: result.request_count,
              lastRequest: result.last_request,
              ipCount: result.ip_count,
              timeWindow: this.config.timeWindow,
              requestsPerMinute: Math.round(result.request_count / (this.config.timeWindow / 60))
            },
            timestamp: new Date().toISOString(),
            message: `用户 ${result.username} 在 ${this.config.timeWindow} 秒内请求 ${result.request_count} 次，疑似异常`
          };

          await this.triggerAlert(alert);
          this.cacheAlert(alertKey);
        }
      }

    } catch (error) {
      console.error('检测高频访问失败:', error.message);
    }
  }

  /**
   * 检测可疑IP地址
   */
  async detectSuspiciousIPs(timeWindow) {
    try {
      const [results] = await this.db.execute(`
        SELECT 
          ip_address,
          COUNT(DISTINCT user_id) as user_count,
          COUNT(*) as request_count,
          COUNT(CASE WHEN result IN ('denied', 'failure') THEN 1 END) as failure_count,
          MAX(created_at) as last_access
        FROM audit_logs
        WHERE created_at >= ?
          AND ip_address IS NOT NULL
        GROUP BY ip_address
        HAVING user_count >= 5 OR failure_count >= ?
        ORDER BY failure_count DESC, user_count DESC
      `, [timeWindow, this.config.suspiciousIpThreshold]);

      for (const result of results) {
        const alertKey = `suspicious_ip_${result.ip_address}`;
        
        if (!this.isAlertCached(alertKey)) {
          const alert = {
            type: 'SUSPICIOUS_IP',
            level: result.failure_count > this.config.suspiciousIpThreshold * 2 ? 'HIGH' : 'MEDIUM',
            ipAddress: result.ip_address,
            details: {
              userCount: result.user_count,
              requestCount: result.request_count,
              failureCount: result.failure_count,
              lastAccess: result.last_access,
              failureRate: ((result.failure_count / result.request_count) * 100).toFixed(2)
            },
            timestamp: new Date().toISOString(),
            message: `IP地址 ${result.ip_address} 存在可疑活动：${result.user_count} 个用户，${result.failure_count} 次失败`
          };

          await this.triggerAlert(alert);
          this.cacheAlert(alertKey);
        }
      }

    } catch (error) {
      console.error('检测可疑IP失败:', error.message);
    }
  }

  /**
   * 检测权限提升尝试
   */
  async detectPrivilegeEscalation(timeWindow) {
    try {
      const patterns = this.config.privilegeEscalationPatterns.map(() => '?').join(',');
      
      const [results] = await this.db.execute(`
        SELECT 
          user_id,
          u.username,
          u.role,
          action,
          resource,
          result,
          details,
          ip_address,
          created_at
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE al.created_at >= ?
          AND al.action IN (${patterns})
          AND (al.result = 'denied' OR JSON_EXTRACT(al.details, '$.sensitive') = true)
        ORDER BY al.created_at DESC
      `, [timeWindow, ...this.config.privilegeEscalationPatterns]);

      for (const result of results) {
        const alertKey = `privilege_escalation_${result.user_id}_${result.created_at}`;
        
        if (!this.isAlertCached(alertKey)) {
          const alert = {
            type: 'PRIVILEGE_ESCALATION_ATTEMPT',
            level: 'HIGH',
            userId: result.user_id,
            username: result.username,
            userRole: result.role,
            details: {
              action: result.action,
              resource: result.resource,
              result: result.result,
              ipAddress: result.ip_address,
              timestamp: result.created_at,
              additionalDetails: typeof result.details === 'string' ? JSON.parse(result.details) : result.details
            },
            timestamp: new Date().toISOString(),
            message: `用户 ${result.username} 尝试权限提升操作：${result.action} on ${result.resource}`
          };

          await this.triggerAlert(alert);
          this.cacheAlert(alertKey);
        }
      }

    } catch (error) {
      console.error('检测权限提升尝试失败:', error.message);
    }
  }

  /**
   * 检测异常时间访问
   */
  async detectOffHoursAccess(timeWindow) {
    try {
      // 定义工作时间（9:00-18:00）
      const [results] = await this.db.execute(`
        SELECT 
          user_id,
          u.username,
          u.role,
          COUNT(*) as access_count,
          GROUP_CONCAT(DISTINCT HOUR(created_at)) as access_hours,
          MAX(created_at) as last_access
        FROM audit_logs al
        LEFT JOIN users u ON al.user_id = u.id
        WHERE al.created_at >= ?
          AND (HOUR(al.created_at) < 9 OR HOUR(al.created_at) > 18)
          AND DAYOFWEEK(al.created_at) BETWEEN 2 AND 6  -- 周一到周五
        GROUP BY user_id, u.username, u.role
        HAVING access_count >= 10
        ORDER BY access_count DESC
      `, [timeWindow]);

      for (const result of results) {
        const alertKey = `off_hours_access_${result.user_id}`;
        
        if (!this.isAlertCached(alertKey)) {
          const alert = {
            type: 'OFF_HOURS_ACCESS',
            level: 'MEDIUM',
            userId: result.user_id,
            username: result.username,
            userRole: result.role,
            details: {
              accessCount: result.access_count,
              accessHours: result.access_hours?.split(',') || [],
              lastAccess: result.last_access
            },
            timestamp: new Date().toISOString(),
            message: `用户 ${result.username} 在非工作时间频繁访问系统（${result.access_count} 次）`
          };

          await this.triggerAlert(alert);
          this.cacheAlert(alertKey);
        }
      }

    } catch (error) {
      console.error('检测异常时间访问失败:', error.message);
    }
  }

  /**
   * 触发告警
   */
  async triggerAlert(alert) {
    try {
      console.log(`🚨 安全告警: [${alert.level}] ${alert.type} - ${alert.message}`);

      // 保存告警到数据库
      await this.saveAlert(alert);

      // 发送实时通知
      if (this.config.notifications.realtime) {
        this.emit('securityAlert', alert);
      }

      // 发送邮件通知（高级别告警）
      if (this.config.notifications.email && (alert.level === 'HIGH' || alert.level === 'CRITICAL')) {
        await this.sendEmailAlert(alert);
      }

      // 发送Webhook通知
      if (this.config.notifications.webhook) {
        await this.sendWebhookAlert(alert);
      }

      // 记录告警日志
      await this.logAlert(alert);

    } catch (error) {
      console.error('触发告警失败:', error.message);
    }
  }

  /**
   * 保存告警到数据库
   */
  async saveAlert(alert) {
    try {
      await this.db.execute(`
        INSERT INTO security_alerts 
        (type, level, user_id, ip_address, message, details, created_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        alert.type,
        alert.level,
        alert.userId || null,
        alert.ipAddress || null,
        alert.message,
        JSON.stringify(alert.details)
      ]);

    } catch (error) {
      // 如果表不存在，创建表
      if (error.code === 'ER_NO_SUCH_TABLE') {
        await this.createSecurityAlertsTable();
        // 重试保存
        await this.saveAlert(alert);
      } else {
        console.error('保存告警失败:', error.message);
      }
    }
  }

  /**
   * 创建安全告警表
   */
  async createSecurityAlertsTable() {
    try {
      await this.db.execute(`
        CREATE TABLE IF NOT EXISTS security_alerts (
          id BIGINT PRIMARY KEY AUTO_INCREMENT,
          type VARCHAR(100) NOT NULL,
          level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') NOT NULL,
          user_id INT,
          ip_address VARCHAR(45),
          message TEXT NOT NULL,
          details JSON,
          status ENUM('OPEN', 'INVESTIGATING', 'RESOLVED', 'FALSE_POSITIVE') DEFAULT 'OPEN',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          INDEX idx_type (type),
          INDEX idx_level (level),
          INDEX idx_user_id (user_id),
          INDEX idx_created_at (created_at),
          INDEX idx_status (status),
          FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);

      console.log('✅ 安全告警表创建成功');

    } catch (error) {
      console.error('创建安全告警表失败:', error.message);
    }
  }

  /**
   * 记录告警日志
   */
  async logAlert(alert) {
    try {
      await this.db.execute(`
        INSERT INTO audit_logs 
        (user_id, action, resource, result, details, ip_address, created_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW())
      `, [
        alert.userId || null,
        'security_alert',
        'security_monitoring',
        'alert_triggered',
        JSON.stringify({
          alertType: alert.type,
          alertLevel: alert.level,
          alertMessage: alert.message,
          alertDetails: alert.details
        }),
        alert.ipAddress || null
      ]);

    } catch (error) {
      console.error('记录告警日志失败:', error.message);
    }
  }

  /**
   * 发送邮件告警
   */
  async sendEmailAlert(alert) {
    try {
      // 这里应该集成邮件服务
      console.log(`📧 发送邮件告警: ${alert.message}`);
      
      // 模拟邮件发送
      const emailContent = {
        to: '<EMAIL>',
        subject: `[安全告警] ${alert.level} - ${alert.type}`,
        body: `
          安全告警详情：
          
          告警类型: ${alert.type}
          告警级别: ${alert.level}
          告警时间: ${alert.timestamp}
          告警消息: ${alert.message}
          
          详细信息:
          ${JSON.stringify(alert.details, null, 2)}
          
          请立即查看并处理此安全事件。
        `
      };

      // 实际实现中应该调用邮件服务API
      console.log('邮件内容:', emailContent);

    } catch (error) {
      console.error('发送邮件告警失败:', error.message);
    }
  }

  /**
   * 发送Webhook告警
   */
  async sendWebhookAlert(alert) {
    try {
      // 这里应该发送到配置的Webhook URL
      console.log(`🔗 发送Webhook告警: ${alert.message}`);
      
      const webhookPayload = {
        alertType: alert.type,
        alertLevel: alert.level,
        timestamp: alert.timestamp,
        message: alert.message,
        details: alert.details
      };

      // 实际实现中应该发送HTTP请求到Webhook URL
      console.log('Webhook载荷:', webhookPayload);

    } catch (error) {
      console.error('发送Webhook告警失败:', error.message);
    }
  }

  /**
   * 检查告警是否已缓存
   */
  isAlertCached(alertKey) {
    const cached = this.alertCache.get(alertKey);
    if (cached && Date.now() - cached < this.cacheTimeout) {
      return true;
    }
    return false;
  }

  /**
   * 缓存告警
   */
  cacheAlert(alertKey) {
    this.alertCache.set(alertKey, Date.now());
  }

  /**
   * 清理告警缓存
   */
  cleanupAlertCache() {
    const now = Date.now();
    for (const [key, timestamp] of this.alertCache.entries()) {
      if (now - timestamp > this.cacheTimeout) {
        this.alertCache.delete(key);
      }
    }
  }

  /**
   * 获取安全告警列表
   */
  async getSecurityAlerts(filters = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        level,
        type,
        status = 'OPEN',
        startDate,
        endDate
      } = filters;

      const whereConditions = ['1=1'];
      const params = [];

      if (level) {
        whereConditions.push('level = ?');
        params.push(level);
      }

      if (type) {
        whereConditions.push('type = ?');
        params.push(type);
      }

      if (status) {
        whereConditions.push('status = ?');
        params.push(status);
      }

      if (startDate) {
        whereConditions.push('created_at >= ?');
        params.push(startDate);
      }

      if (endDate) {
        whereConditions.push('created_at <= ?');
        params.push(endDate);
      }

      const whereClause = whereConditions.join(' AND ');
      const offset = (page - 1) * limit;

      // 查询总数
      const [countResult] = await this.db.execute(`
        SELECT COUNT(*) as total
        FROM security_alerts
        WHERE ${whereClause}
      `, params);

      const total = countResult[0].total;

      // 查询告警列表
      const [alerts] = await this.db.execute(`
        SELECT 
          sa.*,
          u.username,
          u.role as user_role
        FROM security_alerts sa
        LEFT JOIN users u ON sa.user_id = u.id
        WHERE ${whereClause}
        ORDER BY sa.created_at DESC
        LIMIT ? OFFSET ?
      `, [...params, parseInt(limit), parseInt(offset)]);

      return {
        alerts: alerts.map(alert => ({
          ...alert,
          details: typeof alert.details === 'string' ? JSON.parse(alert.details) : alert.details
        })),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / limit)
        }
      };

    } catch (error) {
      console.error('获取安全告警列表失败:', error.message);
      throw error;
    }
  }

  /**
   * 更新告警状态
   */
  async updateAlertStatus(alertId, status, notes = null) {
    try {
      await this.db.execute(`
        UPDATE security_alerts 
        SET status = ?, updated_at = NOW()
        WHERE id = ?
      `, [status, alertId]);

      // 记录状态变更日志
      await this.db.execute(`
        INSERT INTO audit_logs 
        (action, resource, resource_id, result, details, created_at)
        VALUES (?, ?, ?, ?, ?, NOW())
      `, [
        'alert_status_update',
        'security_alert',
        alertId.toString(),
        'success',
        JSON.stringify({
          newStatus: status,
          notes
        })
      ]);

      console.log(`告警 ${alertId} 状态已更新为: ${status}`);

    } catch (error) {
      console.error('更新告警状态失败:', error.message);
      throw error;
    }
  }

  /**
   * 生成安全报告
   */
  async generateSecurityReport(period = 'week') {
    try {
      const now = new Date();
      let startDate;

      switch (period) {
        case 'day':
          startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      }

      // 获取告警统计
      const [alertStats] = await this.db.execute(`
        SELECT 
          level,
          type,
          COUNT(*) as count
        FROM security_alerts
        WHERE created_at >= ?
        GROUP BY level, type
        ORDER BY level, count DESC
      `, [startDate]);

      // 获取用户风险统计
      const [userRiskStats] = await this.db.execute(`
        SELECT 
          u.username,
          u.role,
          COUNT(*) as alert_count,
          GROUP_CONCAT(DISTINCT sa.type) as alert_types
        FROM security_alerts sa
        LEFT JOIN users u ON sa.user_id = u.id
        WHERE sa.created_at >= ?
          AND sa.user_id IS NOT NULL
        GROUP BY u.id, u.username, u.role
        ORDER BY alert_count DESC
        LIMIT 10
      `, [startDate]);

      // 获取IP风险统计
      const [ipRiskStats] = await this.db.execute(`
        SELECT 
          ip_address,
          COUNT(*) as alert_count,
          GROUP_CONCAT(DISTINCT type) as alert_types
        FROM security_alerts
        WHERE created_at >= ?
          AND ip_address IS NOT NULL
        GROUP BY ip_address
        ORDER BY alert_count DESC
        LIMIT 10
      `, [startDate]);

      const report = {
        period,
        startDate: startDate.toISOString(),
        endDate: now.toISOString(),
        generatedAt: now.toISOString(),
        summary: {
          totalAlerts: alertStats.reduce((sum, stat) => sum + stat.count, 0),
          criticalAlerts: alertStats.filter(s => s.level === 'CRITICAL').reduce((sum, stat) => sum + stat.count, 0),
          highAlerts: alertStats.filter(s => s.level === 'HIGH').reduce((sum, stat) => sum + stat.count, 0),
          mediumAlerts: alertStats.filter(s => s.level === 'MEDIUM').reduce((sum, stat) => sum + stat.count, 0),
          lowAlerts: alertStats.filter(s => s.level === 'LOW').reduce((sum, stat) => sum + stat.count, 0)
        },
        alertStatistics: alertStats,
        userRiskStatistics: userRiskStats,
        ipRiskStatistics: ipRiskStats
      };

      return report;

    } catch (error) {
      console.error('生成安全报告失败:', error.message);
      throw error;
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    console.log('安全监控配置已更新:', this.config);
  }

  /**
   * 获取配置
   */
  getConfig() {
    return { ...this.config };
  }
}

module.exports = AuditAlertService;