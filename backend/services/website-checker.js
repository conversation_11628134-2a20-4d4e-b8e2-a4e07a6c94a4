const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const os = require('os');

/**
 * 高性能网站存活检测服务
 * 使用Worker Threads实现真正的多线程并发检测
 * 目标：30秒内检测完1000个站点
 */
class WebsiteCheckerService {
  constructor() {
    this.maxWorkers = Math.min(os.cpus().length * 2, 16); // 最多16个工作线程
    this.activeWorkers = new Set();
    this.results = [];
  }

  /**
   * 批量检测网站存活状态
   * @param {Array} websites 网站列表
   * @returns {Promise<Array>} 检测结果
   */
  async checkWebsitesBatch(websites) {
    const startTime = Date.now();
    console.log(`🚀 开始高性能批量检测 ${websites.length} 个网站，使用 ${this.maxWorkers} 个工作线程`);

    if (websites.length === 0) {
      return [];
    }

    // 将网站分配给不同的工作线程
    const chunkSize = Math.ceil(websites.length / this.maxWorkers);
    const chunks = this.chunkArray(websites, chunkSize);
    
    console.log(`📦 分成 ${chunks.length} 个批次，每批次约 ${chunkSize} 个网站`);

    const workerPromises = chunks.map((chunk, index) => {
      return this.createWorker(chunk, index);
    });

    try {
      // 等待所有工作线程完成
      const results = await Promise.allSettled(workerPromises);
      
      // 合并所有结果
      const allResults = [];
      let successCount = 0;
      let failCount = 0;

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          const workerResults = result.value;
          allResults.push(...workerResults);
          
          workerResults.forEach(r => {
            if (r.isAccessible) {
              successCount++;
            } else {
              failCount++;
            }
          });
        } else {
          console.error(`❌ 工作线程 ${index} 失败:`, result.reason);
          failCount += chunks[index].length;
        }
      });

      const totalTime = Date.now() - startTime;
      const avgTimePerSite = Math.round(totalTime / websites.length);
      const sitesPerSecond = Math.round(websites.length / (totalTime / 1000));

      console.log(`🎯 高性能批量检测完成:`);
      console.log(`   📊 总计: ${websites.length} 个网站`);
      console.log(`   ✅ 成功: ${successCount} 个`);
      console.log(`   ❌ 失败: ${failCount} 个`);
      console.log(`   ⏱️  总耗时: ${totalTime}ms (${Math.round(totalTime/1000)}秒)`);
      console.log(`   📈 平均每站点: ${avgTimePerSite}ms`);
      console.log(`   🚀 处理速度: ${sitesPerSecond} 站点/秒`);

      return allResults;

    } catch (error) {
      console.error('❌ 批量检测失败:', error);
      throw error;
    }
  }

  /**
   * 创建工作线程
   * @param {Array} websites 要检测的网站列表
   * @param {number} workerId 工作线程ID
   * @returns {Promise} 工作线程Promise
   */
  createWorker(websites, workerId) {
    return new Promise((resolve, reject) => {
      const worker = new Worker(__filename, {
        workerData: { websites, workerId }
      });

      this.activeWorkers.add(worker);

      worker.on('message', (results) => {
        console.log(`✅ 工作线程 ${workerId} 完成，检测了 ${results.length} 个网站`);
        resolve(results);
      });

      worker.on('error', (error) => {
        console.error(`❌ 工作线程 ${workerId} 错误:`, error);
        reject(error);
      });

      worker.on('exit', (code) => {
        this.activeWorkers.delete(worker);
        if (code !== 0) {
          console.error(`❌ 工作线程 ${workerId} 异常退出，代码: ${code}`);
        }
      });

      // 设置超时保护
      setTimeout(() => {
        if (this.activeWorkers.has(worker)) {
          console.warn(`⚠️  工作线程 ${workerId} 超时，强制终止`);
          worker.terminate();
          reject(new Error(`工作线程 ${workerId} 超时`));
        }
      }, 60000); // 60秒超时
    });
  }

  /**
   * 将数组分块
   * @param {Array} array 原数组
   * @param {number} chunkSize 块大小
   * @returns {Array} 分块后的数组
   */
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  /**
   * 终止所有工作线程
   */
  async terminateAllWorkers() {
    const terminatePromises = Array.from(this.activeWorkers).map(worker => {
      return worker.terminate();
    });
    
    await Promise.allSettled(terminatePromises);
    this.activeWorkers.clear();
    console.log('🛑 所有工作线程已终止');
  }
}

// Worker线程代码
if (!isMainThread) {
  const { websites, workerId } = workerData;
  
  console.log(`🔧 工作线程 ${workerId} 启动，处理 ${websites.length} 个网站`);

  // 在工作线程中执行网站检测
  (async () => {
    const results = [];
    
    // 高并发检测，每个工作线程内部也使用并发
    const concurrencyLimit = 10;
    const chunks = chunkArray(websites, concurrencyLimit);
    
    for (const chunk of chunks) {
      const promises = chunk.map(async (website) => {
        try {
          const result = await checkWebsiteAccessFast(website.siteUrl || `https://${website.domain}`);
          return {
            websiteId: website.id,
            siteName: website.siteName,
            domain: website.domain,
            url: website.siteUrl || `https://${website.domain}`,
            ...result
          };
        } catch (error) {
          return {
            websiteId: website.id,
            siteName: website.siteName,
            domain: website.domain,
            url: website.siteUrl || `https://${website.domain}`,
            statusCode: 0,
            responseTime: 0,
            isAccessible: false,
            lastCheckTime: new Date().toISOString(),
            error: error.message
          };
        }
      });
      
      const chunkResults = await Promise.allSettled(promises);
      chunkResults.forEach(result => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        }
      });
    }
    
    // 发送结果回主线程
    parentPort.postMessage(results);
  })().catch(error => {
    console.error(`❌ 工作线程 ${workerId} 执行失败:`, error);
    parentPort.postMessage([]);
  });
}

/**
 * 快速网站访问检测（魔改版 - 集成coolmonitor逻辑）
 * @param {string} url 网站URL
 * @returns {Promise} 检测结果
 */
async function checkWebsiteAccessFast(url, redirectCount = 0) {
  const https = require('https');
  const http = require('http');
  const { URL } = require('url');

  // coolmonitor风格的状态码检查函数
  function checkStatusCode(statusCode, expectedStatusCodes = '200-299,301,302') {
    const statusParts = expectedStatusCodes.split(',');

    for (const part of statusParts) {
      const trimmedPart = part.trim();

      if (trimmedPart.includes('-')) {
        const [min, max] = trimmedPart.split('-').map(s => parseInt(s));
        if (statusCode >= min && statusCode <= max) {
          return true;
        }
      } else if (parseInt(trimmedPart) === statusCode) {
        return true;
      }
    }

    return false;
  }

  return new Promise((resolve) => {
    try {
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      const startTime = Date.now();

      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: 'HEAD', // 使用HEAD请求，更快
        timeout: 8000,  // 8秒超时，给慢速网站更多时间
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; SiteManager-Monitor/2.0; +http://monitor.example.com)',
          'Accept': '*/*',
          'Connection': 'close',
          'Cache-Control': 'no-cache'
        },
        rejectUnauthorized: false, // 允许自签名证书
        maxRedirects: 3 // 允许最多3次重定向
      };

      const req = client.request(options, (res) => {
        const responseTime = Date.now() - startTime;

        // 处理重定向
        if (res.statusCode >= 300 && res.statusCode < 400 && res.headers.location) {
          res.destroy();
          // 如果是重定向，递归检查新URL（但限制递归深度）
          if (redirectCount < 3) {
            const newUrl = new URL(res.headers.location, url).href;
            return checkWebsiteAccessFast(newUrl, redirectCount + 1)
              .then(resolve)
              .catch(() => resolve({
                statusCode: res.statusCode,
                responseTime: responseTime,
                isAccessible: false,
                lastCheckTime: new Date().toISOString(),
                error: 'Redirect failed'
              }));
          }
        }

        res.destroy(); // 立即销毁响应

        // 使用coolmonitor风格的状态码检查（支持重定向）
        const isAccessible = checkStatusCode(res.statusCode, '200-299,301,302');

        resolve({
          statusCode: res.statusCode,
          responseTime: responseTime,
          isAccessible: isAccessible,
          lastCheckTime: new Date().toISOString()
        });
      });

      req.on('error', (error) => {
        const responseTime = Date.now() - startTime;

        // 如果HEAD请求失败且是第一次尝试，尝试GET请求
        if (options.method === 'HEAD' && redirectCount === 0) {
          // 直接在这里实现GET请求，避免函数作用域问题
          const getOptions = {
            ...options,
            method: 'GET',
            timeout: 10000
          };

          const getReq = client.request(getOptions, (getRes) => {
            const getResponseTime = Date.now() - startTime;
            getRes.destroy();

            const isAccessible = checkStatusCode(getRes.statusCode, '200-299,301,302');

            resolve({
              statusCode: getRes.statusCode,
              responseTime: getResponseTime,
              isAccessible: isAccessible,
              lastCheckTime: new Date().toISOString()
            });
          });

          getReq.on('error', () => {
            resolve({
              statusCode: 0,
              responseTime: responseTime,
              isAccessible: false,
              lastCheckTime: new Date().toISOString(),
              error: error.message
            });
          });

          getReq.on('timeout', () => {
            getReq.destroy();
            resolve({
              statusCode: 0,
              responseTime: responseTime,
              isAccessible: false,
              lastCheckTime: new Date().toISOString(),
              error: 'GET request timeout'
            });
          });

          return getReq.end();
        }

        resolve({
          statusCode: 0,
          responseTime: responseTime,
          isAccessible: false,
          lastCheckTime: new Date().toISOString(),
          error: error.message
        });
      });

      req.on('timeout', () => {
        req.destroy();
        const responseTime = Date.now() - startTime;
        resolve({
          statusCode: 0,
          responseTime: responseTime,
          isAccessible: false,
          lastCheckTime: new Date().toISOString(),
          error: 'Request timeout'
        });
      });

      req.end();
    } catch (error) {
      resolve({
        statusCode: 0,
        responseTime: 0,
        isAccessible: false,
        lastCheckTime: new Date().toISOString(),
        error: error.message
      });
    }
  });
}



/**
 * 工具函数：将数组分块
 */
function chunkArray(array, chunkSize) {
  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
}

module.exports = WebsiteCheckerService;
