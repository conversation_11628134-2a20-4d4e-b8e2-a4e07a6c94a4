const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');

/**
 * 权限配置服务
 * 负责加载、管理和热重载权限配置
 */
class PermissionConfigService extends EventEmitter {
  constructor() {
    super();
    this.config = null;
    this.configPath = null;
    this.watcher = null;
    this.lastModified = null;
    
    // 初始化配置
    this.initialize();
  }

  /**
   * 初始化配置服务
   */
  initialize() {
    try {
      // 确定配置文件路径
      const environment = process.env.NODE_ENV || 'development';
      const configFileName = environment === 'production' 
        ? 'permissions.production.json' 
        : 'permissions.json';
      
      this.configPath = path.join(__dirname, '../config', configFileName);
      
      // 加载配置
      this.loadConfig();
      
      // 启用热重载（仅在开发环境）
      if (environment !== 'production' && process.env.ENABLE_CONFIG_HOT_RELOAD !== 'false') {
        this.enableHotReload();
      }
      
      console.log(`权限配置服务已初始化，使用配置文件: ${configFileName}`);
    } catch (error) {
      console.error('权限配置服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 加载配置文件
   */
  loadConfig() {
    try {
      if (!fs.existsSync(this.configPath)) {
        throw new Error(`配置文件不存在: ${this.configPath}`);
      }

      const stats = fs.statSync(this.configPath);
      const configContent = fs.readFileSync(this.configPath, 'utf8');
      const rawConfig = JSON.parse(configContent);
      
      // 应用环境变量覆盖
      this.config = this.applyEnvironmentOverrides(rawConfig);
      
      // 验证配置
      this.validateConfig();
      
      this.lastModified = stats.mtime;
      
      console.log(`权限配置已加载，版本: ${this.config.version}`);
      
      // 触发配置更新事件
      this.emit('configUpdated', this.config);
      
    } catch (error) {
      console.error('加载权限配置失败:', error);
      throw error;
    }
  }

  /**
   * 应用环境变量覆盖
   */
  applyEnvironmentOverrides(config) {
    const overriddenConfig = JSON.parse(JSON.stringify(config));
    
    // 安全配置覆盖
    if (process.env.PERMISSION_STRICT_MODE !== undefined) {
      overriddenConfig.security.enableStrictMode = process.env.PERMISSION_STRICT_MODE === 'true';
    }
    
    if (process.env.PERMISSION_REQUIRE_HTTPS !== undefined) {
      overriddenConfig.security.requireHttps = process.env.PERMISSION_REQUIRE_HTTPS === 'true';
    }
    
    if (process.env.PERMISSION_SESSION_TIMEOUT) {
      overriddenConfig.security.sessionTimeout = parseInt(process.env.PERMISSION_SESSION_TIMEOUT);
    }
    
    // 缓存配置覆盖
    if (process.env.PERMISSION_CACHE_ENABLED !== undefined) {
      overriddenConfig.cache.enabled = process.env.PERMISSION_CACHE_ENABLED === 'true';
    }
    
    if (process.env.PERMISSION_CACHE_TTL) {
      overriddenConfig.cache.ttl = parseInt(process.env.PERMISSION_CACHE_TTL);
    }
    
    if (process.env.REDIS_URL) {
      overriddenConfig.cache.redisUrl = process.env.REDIS_URL;
    }
    
    // 审计配置覆盖
    if (process.env.PERMISSION_AUDIT_ENABLED !== undefined) {
      overriddenConfig.audit.enabled = process.env.PERMISSION_AUDIT_ENABLED === 'true';
    }
    
    if (process.env.PERMISSION_AUDIT_RETENTION_DAYS) {
      overriddenConfig.audit.retentionDays = parseInt(process.env.PERMISSION_AUDIT_RETENTION_DAYS);
    }
    
    // 性能配置覆盖
    if (process.env.PERMISSION_MAX_RESPONSE_TIME) {
      overriddenConfig.performance.maxResponseTime = parseInt(process.env.PERMISSION_MAX_RESPONSE_TIME);
    }
    
    if (process.env.PERMISSION_CONCURRENT_REQUESTS) {
      overriddenConfig.performance.concurrentRequests = parseInt(process.env.PERMISSION_CONCURRENT_REQUESTS);
    }
    
    return overriddenConfig;
  }

  /**
   * 验证配置
   */
  validateConfig() {
    if (!this.config) {
      throw new Error('配置为空');
    }
    
    // 验证必需字段
    const requiredFields = ['version', 'roles', 'permissions'];
    for (const field of requiredFields) {
      if (!this.config[field]) {
        throw new Error(`配置缺少必需字段: ${field}`);
      }
    }
    
    // 验证角色配置
    this.validateRoles();
    
    // 验证权限配置
    this.validatePermissions();
    
    // 验证模板配置
    if (this.config.templates) {
      this.validateTemplates();
    }
    
    console.log('权限配置验证通过');
  }

  /**
   * 验证角色配置
   */
  validateRoles() {
    const roles = this.config.roles;
    
    for (const [roleId, role] of Object.entries(roles)) {
      if (!role.name || !role.permissions) {
        throw new Error(`角色 ${roleId} 配置不完整`);
      }
      
      // 验证权限引用
      for (const permission of role.permissions) {
        if (permission !== '*' && !this.config.permissions[permission]) {
          throw new Error(`角色 ${roleId} 引用了不存在的权限: ${permission}`);
        }
      }
      
      // 验证继承关系
      if (role.inherits) {
        for (const inheritedRole of role.inherits) {
          if (!roles[inheritedRole]) {
            throw new Error(`角色 ${roleId} 继承了不存在的角色: ${inheritedRole}`);
          }
        }
      }
    }
  }

  /**
   * 验证权限配置
   */
  validatePermissions() {
    const permissions = this.config.permissions;
    
    for (const [permissionId, permission] of Object.entries(permissions)) {
      if (!permission.name || !permission.category) {
        throw new Error(`权限 ${permissionId} 配置不完整`);
      }
      
      // 验证权限命名规范
      if (!/^[a-z]+\.[a-z]+(\.[a-z]+)?$/.test(permissionId)) {
        console.warn(`权限 ${permissionId} 不符合命名规范 (模块.资源.操作)`);
      }
    }
  }

  /**
   * 验证模板配置
   */
  validateTemplates() {
    const templates = this.config.templates;
    
    for (const [templateId, template] of Object.entries(templates)) {
      if (!template.name || !template.permissions) {
        throw new Error(`模板 ${templateId} 配置不完整`);
      }
      
      // 验证权限引用
      for (const permission of template.permissions) {
        if (!this.config.permissions[permission]) {
          throw new Error(`模板 ${templateId} 引用了不存在的权限: ${permission}`);
        }
      }
    }
  }

  /**
   * 启用配置热重载
   */
  enableHotReload() {
    if (this.watcher) {
      this.watcher.close();
    }
    
    this.watcher = fs.watchFile(this.configPath, { interval: 1000 }, (curr, prev) => {
      if (curr.mtime > this.lastModified) {
        console.log('检测到权限配置文件变更，正在重新加载...');
        try {
          this.loadConfig();
          console.log('权限配置热重载成功');
        } catch (error) {
          console.error('权限配置热重载失败:', error);
          this.emit('configError', error);
        }
      }
    });
    
    console.log('权限配置热重载已启用');
  }

  /**
   * 禁用配置热重载
   */
  disableHotReload() {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
      console.log('权限配置热重载已禁用');
    }
  }

  /**
   * 获取完整配置
   */
  getConfig() {
    return this.config;
  }

  /**
   * 获取角色配置
   */
  getRoles() {
    return this.config?.roles || {};
  }

  /**
   * 获取权限配置
   */
  getPermissions() {
    return this.config?.permissions || {};
  }

  /**
   * 获取模板配置
   */
  getTemplates() {
    return this.config?.templates || {};
  }

  /**
   * 获取安全配置
   */
  getSecurityConfig() {
    return this.config?.security || {};
  }

  /**
   * 获取缓存配置
   */
  getCacheConfig() {
    return this.config?.cache || {};
  }

  /**
   * 获取审计配置
   */
  getAuditConfig() {
    return this.config?.audit || {};
  }

  /**
   * 获取性能配置
   */
  getPerformanceConfig() {
    return this.config?.performance || {};
  }

  /**
   * 获取监控配置
   */
  getMonitoringConfig() {
    return this.config?.monitoring || {};
  }

  /**
   * 重新加载配置
   */
  async reloadConfig() {
    try {
      this.loadConfig();
      return { success: true, message: '配置重新加载成功' };
    } catch (error) {
      return { success: false, message: `配置重新加载失败: ${error.message}` };
    }
  }

  /**
   * 获取配置健康状态
   */
  getHealthStatus() {
    return {
      configLoaded: !!this.config,
      configPath: this.configPath,
      lastModified: this.lastModified,
      hotReloadEnabled: !!this.watcher,
      version: this.config?.version,
      environment: process.env.NODE_ENV || 'development'
    };
  }

  /**
   * 销毁服务
   */
  destroy() {
    this.disableHotReload();
    this.removeAllListeners();
    this.config = null;
    console.log('权限配置服务已销毁');
  }
}

// 创建单例实例
const permissionConfigService = new PermissionConfigService();

module.exports = permissionConfigService;