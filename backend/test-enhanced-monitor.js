/**
 * 测试增强版网站监控服务
 */

const EnhancedWebsiteMonitor = require('./services/enhanced-website-monitor');

async function testEnhancedMonitor() {
  console.log('🚀 开始测试增强版网站监控服务...\n');

  const monitor = new EnhancedWebsiteMonitor();

  try {
    // 首先获取一些网站ID进行测试
    const connection = await monitor.getDbConnection();
    
    const [websites] = await connection.execute(
      'SELECT id, site_name, site_url FROM websites WHERE status = "active" LIMIT 3'
    );
    
    await connection.end();

    if (websites.length === 0) {
      console.log('❌ 没有找到活跃的网站进行测试');
      return;
    }

    console.log(`📋 找到 ${websites.length} 个网站进行测试:`);
    websites.forEach(site => {
      console.log(`   - ${site.site_name} (ID: ${site.id}) - ${site.site_url}`);
    });
    console.log('');

    // 测试单个网站检查
    console.log('🔍 测试单个网站检查...');
    const firstWebsite = websites[0];
    
    try {
      const result = await monitor.checkWebsite(firstWebsite.id, {
        enableHttpCheck: true,
        enableSslCheck: true,
        enableKeywordCheck: false,
        connectTimeout: 10,
        retries: 1,
        retryInterval: 5
      });

      console.log('✅ 单个网站检查结果:');
      console.log(`   网站: ${result.siteName}`);
      console.log(`   状态: ${result.status === 1 ? '✅ 正常' : '❌ 异常'}`);
      console.log(`   消息: ${result.message}`);
      console.log(`   响应时间: ${result.ping}ms`);
      console.log(`   检查时间: ${result.checkTime}`);
      
      if (result.details) {
        console.log('   详细信息:');
        if (result.details.http) {
          console.log(`     HTTP检查: ${result.details.http.status === 1 ? '✅' : '❌'} - ${result.details.http.message}`);
        }
        if (result.details.ssl) {
          console.log(`     SSL检查: ${result.details.ssl.status === 1 ? '✅' : '❌'} - ${result.details.ssl.message}`);
        }
      }
      
    } catch (error) {
      console.error(`❌ 单个网站检查失败: ${error.message}`);
    }

    console.log('\n' + '─'.repeat(60));

    // 测试批量网站检查
    if (websites.length > 1) {
      console.log('\n🔍 测试批量网站检查...');
      const websiteIds = websites.map(site => site.id);
      
      try {
        const batchResults = await monitor.checkWebsitesBatch(websiteIds, {
          concurrency: 2,
          enableHttpCheck: true,
          enableSslCheck: true,
          connectTimeout: 10
        });

        console.log('✅ 批量检查结果:');
        console.log(`   总数: ${batchResults.length}`);
        
        const successCount = batchResults.filter(r => r.status === 1).length;
        const failureCount = batchResults.length - successCount;
        
        console.log(`   成功: ${successCount} 个`);
        console.log(`   失败: ${failureCount} 个`);
        
        console.log('\n   详细结果:');
        batchResults.forEach((result, index) => {
          console.log(`     ${index + 1}. ${result.siteName || `网站ID:${result.websiteId}`}`);
          console.log(`        状态: ${result.status === 1 ? '✅ 正常' : '❌ 异常'}`);
          console.log(`        消息: ${result.message}`);
          console.log(`        响应时间: ${result.ping}ms`);
        });
        
      } catch (error) {
        console.error(`❌ 批量检查失败: ${error.message}`);
      }
    }

    console.log('\n' + '─'.repeat(60));

    // 测试监控配置获取
    console.log('\n🔍 测试监控配置获取...');
    try {
      const connection2 = await monitor.getDbConnection();
      const config = await monitor.getWebsiteMonitorConfig(firstWebsite.id, connection2);
      await connection2.end();

      console.log('✅ 监控配置:');
      console.log(`   HTTP方法: ${config.httpMethod}`);
      console.log(`   状态码: ${config.statusCodes}`);
      console.log(`   超时时间: ${config.connectTimeout}秒`);
      console.log(`   重试次数: ${config.retries}`);
      console.log(`   HTTP检查: ${config.enableHttpCheck ? '启用' : '禁用'}`);
      console.log(`   SSL检查: ${config.enableSslCheck ? '启用' : '禁用'}`);
      console.log(`   关键词检查: ${config.enableKeywordCheck ? '启用' : '禁用'}`);
      
    } catch (error) {
      console.error(`❌ 获取监控配置失败: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }

  console.log('\n🎉 增强版网站监控服务测试完成!');
}

// 运行测试
if (require.main === module) {
  testEnhancedMonitor().catch(console.error);
}

module.exports = { testEnhancedMonitor };
