/**
 * 测试简化版HTTP检查器
 */

const { <PERSON>Http<PERSON><PERSON><PERSON>, MONITOR_STATUS } = require('./services/simple-http-checker');

async function testSimpleChecker() {
  console.log('🚀 开始测试简化版HTTP检查器...');

  const checker = new SimpleHttpChecker();

  const testCases = [
    {
      name: '百度首页',
      config: {
        url: 'https://www.baidu.com',
        statusCodes: '200-299'
      }
    },
    {
      name: '腾讯首页',
      config: {
        url: 'https://www.qq.com',
        statusCodes: '200-299'
      }
    },
    {
      name: '不存在的网站',
      config: {
        url: 'https://this-domain-does-not-exist-12345.com',
        statusCodes: '200-299',
        connectTimeout: 5
      }
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📋 测试: ${testCase.name}`);
    console.log(`🔗 URL: ${testCase.config.url}`);
    
    try {
      const result = await checker.checkHttp(testCase.config);
      
      console.log(`✅ 检查结果:`);
      console.log(`   状态: ${result.status === MONITOR_STATUS.UP ? '✅ 正常' : '❌ 异常'}`);
      console.log(`   消息: ${result.message}`);
      console.log(`   响应时间: ${result.ping}ms`);
      
    } catch (error) {
      console.log(`❌ 检查失败: ${error.message}`);
    }
    
    console.log('─'.repeat(50));
  }

  console.log('\n🎉 简化版HTTP检查器测试完成!');
}

testSimpleChecker().catch(console.error);
