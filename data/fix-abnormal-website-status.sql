-- 修复异常网站状态脚本
-- 重置连续失败次数异常高的网站状态

-- 1. 重置连续失败次数超过100的网站（这些明显是异常数据）
UPDATE website_status_stats 
SET consecutive_failures = 0,
    notification_sent = 0,
    notification_count = 0,
    last_notification_time = NULL,
    updated_at = CURRENT_TIMESTAMP
WHERE consecutive_failures > 100;

-- 2. 查看重置后的统计
SELECT 
    CASE 
        WHEN consecutive_failures = 0 THEN '正常'
        WHEN consecutive_failures BETWEEN 1 AND 5 THEN '轻微异常'
        WHEN consecutive_failures BETWEEN 6 AND 20 THEN '需要关注'
        ELSE '严重异常'
    END as status_category,
    COUNT(*) as count
FROM website_status_stats 
GROUP BY 
    CASE 
        WHEN consecutive_failures = 0 THEN '正常'
        WHEN consecutive_failures BETWEEN 1 AND 5 THEN '轻微异常'
        WHEN consecutive_failures BETWEEN 6 AND 20 THEN '需要关注'
        ELSE '严重异常'
    END
ORDER BY count DESC;

-- 3. 查看当前需要通知的异常网站
SELECT 
    ws.website_id,
    w.site_name,
    w.site_url,
    ws.consecutive_failures,
    ws.current_status_code,
    ws.last_failure_time
FROM website_status_stats ws
JOIN websites w ON ws.website_id = w.id
WHERE ws.consecutive_failures >= 5
ORDER BY ws.consecutive_failures DESC
LIMIT 20;