-- 验证数据库优化效果

-- 1. 测试websites表的查询性能
EXPLAIN SELECT * FROM websites WHERE status = 'active' AND platform_id = 1 AND expire_date < NOW();

-- 2. 测试SSL相关查询性能
EXPLAIN SELECT * FROM websites WHERE ssl_status = 'valid' AND ssl_expire_date < DATE_ADD(NOW(), INTERVAL 30 DAY);

-- 3. 测试异常网站查询性能
EXPLAIN SELECT * FROM website_status_stats WHERE consecutive_failures > 5 AND notification_sent = 0;

-- 4. 测试服务器管理查询性能
EXPLAIN SELECT * FROM servers WHERE status = 'active' AND provider = 'AWS' AND location LIKE '%深圳%';

-- 5. 测试website_status_checks表查询性能
EXPLAIN SELECT * FROM website_status_checks WHERE website_id = 1 ORDER BY check_time DESC LIMIT 10;

-- 6. 检查新创建的索引
SELECT 
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    COLUMN_NAME as '列名',
    NON_UNIQUE as '非唯一'
FROM information_schema.statistics 
WHERE table_schema='sitemanager' 
    AND (INDEX_NAME LIKE '%combined%' 
         OR INDEX_NAME LIKE '%abnormal%'
         OR INDEX_NAME LIKE '%management%'
         OR INDEX_NAME LIKE '%recent%')
ORDER BY TABLE_NAME, INDEX_NAME;