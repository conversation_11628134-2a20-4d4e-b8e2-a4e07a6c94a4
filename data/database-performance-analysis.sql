-- 数据库性能分析脚本
-- 用于分析当前查询性能和索引状态

-- 1. 检查当前表的大小和记录数
SELECT 
    TABLE_NAME as '表名',
    TABLE_ROWS as '记录数',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS '大小(MB)',
    ROUND((data_length / 1024 / 1024), 2) AS '数据大小(MB)',
    ROUND((index_length / 1024 / 1024), 2) AS '索引大小(MB)'
FROM information_schema.tables 
WHERE table_schema='sitemanager'
ORDER BY (data_length + index_length) DESC;

-- 2. 检查当前索引状态
SELECT 
    TABLE_NAME as '表名',
    INDEX_NAME as '索引名',
    COLUMN_NAME as '列名',
    NON_UNIQUE as '非唯一',
    INDEX_TYPE as '索引类型'
FROM information_schema.statistics 
WHERE table_schema='sitemanager'
ORDER BY TABLE_NAME, INDEX_NAME;

-- 3. 分析websites表的查询模式
EXPLAIN SELECT * FROM websites WHERE status = 'active' LIMIT 10;
EXPLAIN SELECT * FROM websites WHERE platform_id = 1 LIMIT 10;
EXPLAIN SELECT * FROM websites WHERE expire_date < NOW() LIMIT 10;
EXPLAIN SELECT * FROM websites WHERE server_id IS NOT NULL LIMIT 10;

-- 4. 分析website_status_stats表的查询模式
EXPLAIN SELECT * FROM website_status_stats WHERE consecutive_failures > 5 LIMIT 10;
EXPLAIN SELECT * FROM website_status_stats WHERE last_failure_time > DATE_SUB(NOW(), INTERVAL 1 HOUR) LIMIT 10;

-- 5. 分析servers表的查询模式
EXPLAIN SELECT * FROM servers WHERE ip_address IS NOT NULL LIMIT 10;
EXPLAIN SELECT * FROM servers WHERE location LIKE '%深圳%' LIMIT 10;

-- 6. 检查慢查询日志状态
SHOW VARIABLES LIKE 'slow_query_log%';
SHOW VARIABLES LIKE 'long_query_time';

-- 7. 查看当前连接数和状态
SHOW STATUS LIKE 'Connections';
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Threads_running';

-- 8. 检查InnoDB状态
SHOW ENGINE INNODB STATUS\G