-- 第一阶段数据库优化脚本
-- 基于性能分析结果创建优化索引

-- 开始事务
START TRANSACTION;

-- 1. 优化websites表的复合索引
-- 1.1 网站管理页面常用的状态+平台查询
CREATE INDEX idx_websites_status_platform_combined ON websites(status, platform_id, expire_date);

-- 1.2 SSL相关查询优化
CREATE INDEX idx_websites_ssl_combined ON websites(ssl_status, ssl_expire_date, ssl_check);

-- 2. 优化website_status_stats表（这个表很重要但相对较小）
-- 2.1 异常网站查询优化
CREATE INDEX idx_status_stats_abnormal ON website_status_stats(consecutive_failures, notification_sent, last_notification_time);

-- 2.2 最近检查时间查询优化
CREATE INDEX idx_status_stats_recent_check ON website_status_stats(last_failure_time, consecutive_failures);

-- 3. 优化servers表
-- 3.1 服务器管理页面查询优化
CREATE INDEX idx_servers_management ON servers(status, provider, location);

-- 3.2 IP地址和位置查询优化
CREATE INDEX idx_servers_location_ip ON servers(location, ip_address);

-- 4. 优化website_status_checks表（最大的表，需要谨慎处理）
-- 4.1 为最常用的查询添加索引
CREATE INDEX idx_status_checks_website_recent ON website_status_checks(website_id, check_time DESC);

-- 5. 优化notification_logs表
-- 5.1 通知历史查询优化
CREATE INDEX idx_notification_logs_recent ON notification_logs(created_at DESC, status, notification_type);

-- 6. 检查索引创建结果
SELECT 
    TABLE_NAME as '表名',
    INDEX_NAME as '新索引名',
    COLUMN_NAME as '列名'
FROM information_schema.statistics 
WHERE table_schema='sitemanager' 
    AND INDEX_NAME LIKE '%combined%' 
    OR INDEX_NAME LIKE '%abnormal%'
    OR INDEX_NAME LIKE '%management%'
    OR INDEX_NAME LIKE '%recent%'
ORDER BY TABLE_NAME, INDEX_NAME;

-- 提交事务
COMMIT;

-- 7. 分析表以更新统计信息
ANALYZE TABLE websites;
ANALYZE TABLE website_status_stats;
ANALYZE TABLE servers;
ANALYZE TABLE website_status_checks;
ANALYZE TABLE notification_logs;