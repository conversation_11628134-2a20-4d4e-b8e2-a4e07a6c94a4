// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  realName?: string;
  role: 'super_admin' | 'admin' | 'user' | 'sales' | 'developer';
  status: 'active' | 'inactive' | 'suspended';
  avatar?: string;
  phone?: string;
  department?: string;
  permissions?: string[];
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
  realName?: string;
  role?: 'admin' | 'user';
  phone?: string;
  department?: string;
}



// 项目相关类型
export interface Project {
  id: number;
  projectName: string;
  projectType: 'website' | 'ecommerce' | 'app' | 'system' | 'maintenance' | 'other';
  serviceFee?: number;
  projectManagerId?: number;
  projectManager?: User;
  salesPersonId?: number;
  salesPerson?: User;
  contractFile?: string; // 合同PDF文件路径
  contractNumber?: string;
  contractSignedDate?: string;
  onlineStatus: 'planning' | 'development' | 'testing' | 'online' | 'suspended' | 'cancelled';
  onlineDate?: string;
  plannedOnlineDate?: string;
  infoCollectionForm?: string; // 信息采集表
  previewLink?: string; // 预览链接
  progressSheet?: string; // 进度表
  requirements?: string; // 需求文档
  designFiles?: string[]; // 设计文件
  technicalSpecs?: string; // 技术规格
  testingReport?: string; // 测试报告
  deliverables?: string[]; // 交付物
  milestones?: ProjectMilestone[]; // 里程碑
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// 项目里程碑
export interface ProjectMilestone {
  id: number;
  projectId: number;
  title: string;
  description?: string;
  plannedDate: string;
  actualDate?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'delayed';
  deliverables?: string[];
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// 平台类型
export interface Platform {
  id: number;
  platform_id?: number; // 新增：用于映射的平台ID
  name: string;
  description?: string;
  version?: string;
  isActive: boolean;
  isCustom: boolean; // 是否为自定义平台
  category: 'cms' | 'ecommerce' | 'framework' | 'static' | 'other';
  officialWebsite?: string;
  documentation?: string;
  supportedFeatures?: string[];
  requirements?: {
    php?: string;
    mysql?: string;
    memory?: string;
    storage?: string;
  };
  createdAt: string;
  updatedAt: string;
}

// 服务器类型
export interface Server {
  id: number;
  name: string;
  ipAddress: string;
  location: string;
  provider: string;
  department?: string;

  // 新增字段
  instanceId?: string; // 实例ID（云服务器实例标识）
  type?: string; // 服务器类型（自定义）
  region?: string; // 地区/国家
  uptime?: number; // 启用时间（秒数）

  specifications: {
    cpu: string;
    memory: string;
    storage: string;
    bandwidth: string;
    os: string;
  };
  expireDate?: string;
  renewalFee?: number;
  status: 'active' | 'inactive' | 'maintenance' | 'expired';
  loadInfo?: ServerLoadInfo;
  monitoringEnabled: boolean;
  alertThresholds?: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkUsage: number;
  };
  sshPort?: number;
  sshUsername?: string;
  sshPassword?: string; // 加密存储
  sshAuthType?: 'password' | 'key'; // SSH认证方式
  sshPrivateKey?: string; // SSH私钥内容
  sshKeyPassphrase?: string; // SSH密钥密码
  accessCredentials?: ServerCredential[];
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// 服务器负载信息
export interface ServerLoadInfo {
  cpuUsage: number; // CPU使用率 %
  memoryUsage: number; // 内存使用率 %
  diskUsage: number; // 磁盘使用率 %
  networkIn: number; // 网络入流量 MB/s
  networkOut: number; // 网络出流量 MB/s
  uptime: number; // 运行时间 秒
  loadAverage: number[]; // 负载平均值 [1min, 5min, 15min]
  processes: number; // 进程数
  lastUpdated: string;
}

// 服务器凭据
export interface ServerCredential {
  id: number;
  serverId: number;
  type: 'ssh' | 'ftp' | 'database' | 'panel' | 'other';
  username: string;
  password: string; // 加密存储
  port?: number;
  database?: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 网站类型
export interface Website {
  id: number;
  projectId?: number;
  project?: Project;
  platformId: number;
  platform?: Platform;
  serverId?: number;
  server?: Server;
  siteName: string; // 站点名称
  siteUrl: string;
  domain: string; // 保留域名字段用于兼容
  industry?: string; // 所属行业
  onlineDate?: string;
  expireDate?: string;
  projectAmount?: number; // 项目金额
  renewalFee?: number;
  accessStatusCode?: number;
  responseTime?: number;
  consecutiveFailures?: number; // 连续失败次数
  lastFailureTime?: string; // 最后失败时间
  notificationSent?: boolean; // 是否已发送通知
  lastNotificationTime?: string; // 最后通知时间
  sslInfo?: SSLInfo;
  domainInfo?: DomainInfo;
  lastCheckTime?: string;
  status: 'active' | 'inactive' | 'suspended' | 'expired' | 'maintenance';
  credentials?: WebsiteCredential[];
  backupInfo?: BackupInfo;
  securityScan?: SecurityScanResult;
  performanceMetrics?: PerformanceMetrics;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// SSL信息
export interface SSLInfo {
  issuer: string;
  subject: string;
  validFrom: string;
  validTo: string;
  daysUntilExpiry: number;
  isValid: boolean;
  certificateChain?: string[];
  lastChecked: string;
}

// 域名信息
export interface DomainInfo {
  registrar: string;
  registrationDate: string;
  expirationDate: string;
  daysUntilExpiry: number | null;
  nameServers: string[];
  whoisData?: any;
  lastChecked: string;
  status?: 'active' | 'expired' | 'suspended' | 'unknown';
  isExpired?: boolean;
  dnsResolvable?: boolean;
  whoisRawData?: any;
}

// 网站凭据
export interface WebsiteCredential {
  id: number;
  websiteId: number;
  accountType: 'admin' | 'ftp' | 'database' | 'hosting' | 'domain' | 'ssl' | 'email' | 'analytics' | 'other';
  username: string;
  password: string; // 加密存储
  email?: string;
  url?: string;
  description?: string;
  additionalInfo?: Record<string, any>;
  isActive: boolean;
  lastUsed?: string;
  createdAt: string;
  updatedAt: string;
}

// 备份信息
export interface BackupInfo {
  lastBackupDate?: string;
  backupFrequency: 'daily' | 'weekly' | 'monthly' | 'manual';
  backupLocation: string;
  backupSize?: number;
  isAutoBackup: boolean;
  retentionDays: number;
  lastRestoreDate?: string;
}

// 安全扫描结果
export interface SecurityScanResult {
  lastScanDate: string;
  vulnerabilities: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  malwareDetected: boolean;
  sslGrade: string;
  securityScore: number;
  recommendations: string[];
}

// 性能指标
export interface PerformanceMetrics {
  pageLoadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  performanceScore: number;
  mobileScore: number;
  desktopScore: number;
  lastMeasured: string;
}

// 网站账号类型
export interface WebsiteAccount {
  id: number;
  websiteId: number;
  accountType: string;
  username: string;
  password: string;
  email?: string;
  role?: string;
  loginUrl?: string;
  isActive: boolean;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// 域名类型
export interface Domain {
  id: number;
  domainName: string;
  registrar: string;
  registrationDate: string;
  expirationDate: string;
  renewalFee: number;
  autoRenewal: boolean;
  dnsProvider: string;
  status: 'active' | 'expired' | 'suspended' | 'transferred' | 'pending';
  whoisInfo?: DomainWhoisInfo;
  dnsRecords?: DNSRecord[];
  websiteId?: number;
  website?: Website;
  alertDays: number; // 到期提醒天数
  lastChecked?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// 域名WHOIS信息
export interface DomainWhoisInfo {
  registrar: string;
  registrantName: string;
  registrantEmail: string;
  registrantPhone: string;
  registrantOrganization?: string;
  adminContact?: ContactInfo;
  techContact?: ContactInfo;
  nameServers: string[];
  dnssec: boolean;
  status: string[];
  lastUpdated: string;
}

// 联系人信息
export interface ContactInfo {
  name: string;
  email: string;
  phone: string;
  organization?: string;
  address?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
}

// DNS记录
export interface DNSRecord {
  id: number;
  domainId: number;
  type: 'A' | 'AAAA' | 'CNAME' | 'MX' | 'TXT' | 'NS' | 'PTR' | 'SRV';
  name: string;
  value: string;
  ttl: number;
  priority?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface PaginatedResponse<T = any> {
  success: boolean;
  message: string;
  data: T[];
  pagination: {
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  };
  timestamp: string;
}

// 查询参数类型
export interface QueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  status?: string;
  startDate?: string;
  endDate?: string;
  [key: string]: any;
}

// 项目上线状态更新请求
export interface UpdateProjectOnlineStatusRequest {
  onlineStatus: 'planning' | 'development' | 'testing' | 'online' | 'suspended';
  onlineDate?: string;
}

// 批量更新网站上线时间请求
export interface BatchUpdateWebsiteOnlineDateRequest {
  projectId: number;
  onlineDate: string;
  websiteIds: number[];
}

// 仪表盘统计数据
export interface DashboardStats {
  totalWebsites: number;
  activeWebsites: number;
  inactiveWebsites: number;
  expiringSoon: number;
  totalProjects: number;
  onlineProjects: number;
  developmentProjects: number;
  testingProjects: number;
  totalServers: number;
  activeServers: number;
  maintenanceServers: number;
  totalDomains: number;
  activeDomains: number;
  expiredDomains: number;

  recentActivities: Activity[];
}

// 活动记录
export interface Activity {
  id: number;
  type: string;
  title: string;
  description?: string;
  time: string;
  status: string;
}

// 图表数据
export interface ChartData {
  name: string;
  value: number;
  status?: string;
}

// 表格列配置类型
export interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  fixed?: 'left' | 'right';
  sorter?: boolean;
  render?: (value: any, record: any, index: number) => React.ReactNode;
}

// 表单字段类型
export interface FormField {
  name: string;
  label: string;
  type: 'input' | 'password' | 'email' | 'number' | 'textarea' | 'select' | 'date' | 'switch' | 'upload';
  required?: boolean;
  placeholder?: string;
  options?: { label: string; value: any }[];
  rules?: any[];
  span?: number;
}

// 菜单项类型
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  path?: string;
  children?: MenuItem[];
  permission?: string[];
}

// 通知类型
export interface Notification {
  id: number;
  userId?: number;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  content: string;
  isRead: boolean;
  resourceType?: string;
  resourceId?: number;
  createdAt: string;
}

// 监控日志类型
export interface MonitorLog {
  id: number;
  resourceType: 'website' | 'server' | 'domain' | 'ssl';
  resourceId: number;
  checkType: string;
  status: 'success' | 'warning' | 'error';
  responseTime?: number;
  statusCode?: number;
  errorMessage?: string;
  checkData?: any;
  createdAt: string;
}

// 权限管理类型
export interface Permission {
  id: number;
  name: string;
  code: string;
  description?: string;
  module: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Role {
  id: number;
  name: string;
  code: string;
  description?: string;
  permissions: Permission[];
  isActive: boolean;
  isSystem: boolean; // 是否为系统角色
  createdAt: string;
  updatedAt: string;
}

export interface UserRole {
  id: number;
  userId: number;
  roleId: number;
  role?: Role;
  grantedBy: number;
  grantedAt: string;
  expiresAt?: string;
  isActive: boolean;
}

export interface UserPermission {
  id: number;
  userId: number;
  resourceType: 'website' | 'server' | 'domain' | 'project';
  resourceId?: number;
  permissionType: 'read' | 'write' | 'admin' | 'delete';
  grantedBy: number;
  grantedAt: string;
  expiresAt?: string;
  isActive: boolean;
  notes?: string;
}

// 批量权限操作
export interface BatchPermissionOperation {
  userIds: number[];
  resourceType: 'website' | 'server' | 'domain' | 'project';
  resourceIds: number[];
  permissionType: 'read' | 'write' | 'admin' | 'delete';
  operation: 'grant' | 'revoke';
  expiresAt?: string;
  notes?: string;
}

// 权限导入数据
export interface PermissionImportData {
  username: string;
  email: string;
  resourceType: string;
  resourceName: string;
  permissionType: string;
  expiresAt?: string;
  notes?: string;
}

// 统计数据类型

// 图表数据类型
export interface ChartData {
  name: string;
  value: number;
  [key: string]: any;
}

// 备份信息
export interface BackupInfo {
  id: number;
  websiteId: number;
  backupType: 'full' | 'database' | 'files' | 'incremental';
  backupProvider: 'local' | 'aws' | 'google' | 'azure' | 'dropbox' | 'other';
  backupPath: string;
  backupSize: number; // MB
  frequency: 'daily' | 'weekly' | 'monthly' | 'manual';
  retention: number; // 保留天数
  lastBackup: string;
  nextBackup?: string;
  status: 'active' | 'failed' | 'disabled';
  autoBackup: boolean;
  encryptionEnabled: boolean;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// 监控配置
export interface MonitoringConfig {
  id: number;
  websiteId: number;
  enabled: boolean;
  checkInterval: number; // 分钟
  timeout: number; // 秒
  retryCount: number;
  alertThresholds: {
    responseTime: number; // ms
    uptime: number; // %
    errorRate: number; // %
  };
  notifications: {
    email: boolean;
    sms: boolean;
    webhook: boolean;
    emailList: string[];
    webhookUrl?: string;
  };
  checkTypes: {
    http: boolean;
    ping: boolean;
    ssl: boolean;
    dns: boolean;
    port: boolean;
  };
  maintenanceMode: boolean;
  createdAt: string;
  updatedAt: string;
}

// 维护窗口
export interface MaintenanceWindow {
  id: number;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  type: 'scheduled' | 'emergency' | 'routine';
  status: 'planned' | 'active' | 'completed' | 'cancelled';
  affectedResources: {
    websites: number[];
    servers: number[];
    domains: number[];
  };
  notificationSent: boolean;
  createdBy: number;
  createdAt: string;
  updatedAt: string;
}



// 知识库
export interface KnowledgeBase {
  id: number;
  title: string;
  content: string;
  category: string;
  tags: string[];
  type: 'article' | 'faq' | 'tutorial' | 'troubleshooting';
  status: 'draft' | 'published' | 'archived';
  authorId: number;
  author: User;
  viewCount: number;
  helpful: number;
  notHelpful: number;
  relatedArticles: number[];
  attachments: string[];
  lastReviewed?: string;
  createdAt: string;
  updatedAt: string;
}


