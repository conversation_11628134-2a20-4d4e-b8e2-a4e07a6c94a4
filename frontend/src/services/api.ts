import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { message } from '../utils/message';
import { cacheManager, requestDeduplicator, performanceMark } from '../utils/performance';

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';
const API_PREFIX = '/api/v1';

// 日志配置
let API_LOGGING_ENABLED = import.meta.env.VITE_API_LOGGING === 'true';
let SILENT_MODE = false; // 完全静默模式

// 在开发环境下提供全局控制函数
if (import.meta.env.DEV) {
  (window as any).toggleApiLogging = (enabled?: boolean) => {
    API_LOGGING_ENABLED = enabled !== undefined ? enabled : !API_LOGGING_ENABLED;
    SILENT_MODE = false; // 开启日志时退出静默模式
    console.log(`API日志已${API_LOGGING_ENABLED ? '开启' : '关闭'}`);
    return API_LOGGING_ENABLED;
  };

  (window as any).setSilentMode = (enabled?: boolean) => {
    SILENT_MODE = enabled !== undefined ? enabled : !SILENT_MODE;
    if (SILENT_MODE) {
      API_LOGGING_ENABLED = false;
      console.log('🔇 静默模式已开启 - 所有API日志和警告都将被隐藏');
    } else {
      console.log('🔊 静默模式已关闭');
    }
    return SILENT_MODE;
  };

  (window as any).getApiLoggingStatus = () => {
    const status = SILENT_MODE ? '静默模式' : (API_LOGGING_ENABLED ? '开启' : '关闭');
    console.log(`API日志状态: ${status}`);
    return { logging: API_LOGGING_ENABLED, silent: SILENT_MODE };
  };
}

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}${API_PREFIX}`,
  timeout: 60000, // 增加超时时间到60秒，适应大量数据查询和网站检测时的高负载
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 性能监控开始
    const requestId = `${config.method}-${config.url}-${Date.now()}`;
    config.metadata = { requestId, startTime: Date.now() };
    performanceMark.start(requestId);

    // 添加认证token (开发模式下暂时跳过认证)
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
      if (API_LOGGING_ENABLED) {
        console.log('使用存储的token');
      }
    } else if (import.meta.env.DEV && config.headers) {
      // 开发模式下如果没有token，不添加Authorization头
      if (API_LOGGING_ENABLED) {
        console.log('开发模式：没有token，跳过认证');
      }
    }

    // 添加请求时间戳（防止缓存）
    if (config.method?.toLowerCase() === 'get') {
      if (config.params) {
        config.params._t = Date.now();
      } else {
        config.params = { _t: Date.now() };
      }
    }

    // 只在启用日志时输出
    if (API_LOGGING_ENABLED) {
      console.log('API请求:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        data: config.data,
        params: config.params,
        requestId,
      });
    }

    return config;
  },
  (error) => {
    console.error('请求拦截器错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 性能监控结束
    const { requestId, startTime } = response.config.metadata || {};
    if (requestId) {
      const duration = performanceMark.end(requestId);
      const responseTime = Date.now() - startTime;

      // 只在启用日志时输出
      if (API_LOGGING_ENABLED) {
        console.log('API响应:', {
          status: response.status,
          url: response.config.url,
          data: response.data,
          requestId,
          duration: `${duration.toFixed(2)}ms`,
          responseTime: `${responseTime}ms`,
          cached: response.data?.cached || false,
        });
      }

      // 慢请求警告（支持静默模式）
      if (duration > 1000 && !SILENT_MODE) {
        if (API_LOGGING_ENABLED) {
          console.warn(`慢请求检测: ${response.config.url} 耗时 ${duration.toFixed(2)}ms`);
        } else {
          // 简化的慢请求提示
          console.warn(`⚠️ 慢请求: ${response.config.url?.split('/').pop()} (${duration.toFixed(0)}ms)`);
        }
      }
    }

    // 检查业务状态码
    if (response.data && response.data.success === false) {
      const errorMessage = response.data.message || '请求失败';
      if (!SILENT_MODE) {
        if (API_LOGGING_ENABLED) {
          console.error('业务逻辑错误:', {
            url: response.config.url,
            status: response.status,
            data: response.data
          });
        } else {
          console.error(`❌ API错误: ${errorMessage}`);
        }
      }
      message.error(errorMessage);
      return Promise.reject(new Error(errorMessage));
    }

    return response;
  },
  (error) => {
    if (!SILENT_MODE) {
      if (API_LOGGING_ENABLED) {
        console.error('API响应错误:', error);
      } else {
        console.error(`🚫 网络错误: ${error.message || '请求失败'}`);
      }
    }

    // 处理网络错误
    if (!error.response) {
      message.error('网络连接失败，请检查网络设置');
      return Promise.reject(error);
    }

    const { status, data } = error.response;

    // 处理不同的HTTP状态码
    switch (status) {
      case 401:
        // 认证失败，清除所有缓存并重定向到登录页
        console.warn('🔐 检测到认证失败，清除缓存并重定向到登录页');
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('user');
        localStorage.removeItem('permissions');

        // 更新认证状态
        if (typeof window !== 'undefined' && (window as any).useAuthStore) {
          (window as any).useAuthStore.getState().logout();
        }
        message.error('登录已过期，请重新登录');

        // 延迟跳转，确保消息显示，但避免在登录页面重复跳转
        setTimeout(() => {
          const currentPath = window.location.pathname;
          if (!currentPath.includes('/login') && !currentPath.includes('/auth')) {
            window.location.href = '/login';
          }
        }, 1000);
        break;

      case 403:
        // 权限不足，检查是否是token过期导致的权限问题
        const errorMessage = data?.message || '权限不足，无法访问该资源';

        // 如果是用户不存在或类似的认证相关错误，清除缓存
        if (errorMessage.includes('用户不存在') ||
            errorMessage.includes('权限不足，无法创建管理员用户') ||
            errorMessage.includes('token') ||
            errorMessage.includes('认证')) {
          console.warn('🔐 检测到权限认证问题，清除缓存并重定向到登录页');
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('user');
          localStorage.removeItem('permissions');

        // 更新认证状态
        if (typeof window !== 'undefined' && (window as any).useAuthStore) {
          (window as any).useAuthStore.getState().logout();
        }
          message.error('认证信息已过期，请重新登录');

          setTimeout(() => {
            if (!window.location.pathname.includes('/login')) {
              window.location.href = '/login';
            }
          }, 1500);
        } else {
          message.error(errorMessage);
        }
        break;

      case 404:
        // 检查是否是认证相关的404错误
        const url = error.config?.url || '';
        const errorMsg = data?.message || '请求的资源不存在';

        // 只有明确的认证相关URL才触发登录跳转
        if (url.includes('/auth/me') || 
            url.includes('/auth/login') ||
            url.includes('/auth/refresh') ||
            (url.includes('/auth/') && errorMsg.includes('用户不存在'))) {
          console.warn('🔐 检测到认证相关的404错误，清除缓存并重定向到登录页');
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('user');
          localStorage.removeItem('permissions');

        // 更新认证状态
        if (typeof window !== 'undefined' && (window as any).useAuthStore) {
          (window as any).useAuthStore.getState().logout();
        }
          message.error('用户信息已失效，请重新登录');

          setTimeout(() => {
            if (!window.location.pathname.includes('/login')) {
              window.location.href = '/login';
            }
          }, 1500);
        } else {
          // 非认证相关的404错误，只显示错误消息，不跳转
          console.warn('🔍 API 404错误:', url, errorMsg);
          message.error(errorMsg);
        }
        break;

      case 409:
        message.error(data?.message || '数据冲突');
        break;

      case 422:
        message.error(data?.message || '数据验证失败');
        break;

      case 429:
        message.error('请求过于频繁，请稍后再试');
        break;

      case 500:
        message.error('服务器内部错误，请稍后再试');
        break;

      case 502:
      case 503:
      case 504:
        message.error('服务暂时不可用，请稍后再试');
        break;

      default:
        message.error(data?.message || `请求失败 (${status})`);
    }

    return Promise.reject(error);
  }
);

// 生成缓存键
const generateCacheKey = (url: string, params?: any): string => {
  const paramStr = params ? JSON.stringify(params) : '';
  return `api:${url}:${paramStr}`;
};

// 通用请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    api.get(url, config).then((res) => res.data),

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.post(url, data, config).then((res) => res.data),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.put(url, data, config).then((res) => res.data),

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> =>
    api.patch(url, data, config).then((res) => res.data),

  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> =>
    api.delete(url, config).then((res) => res.data),

  // 带缓存的GET请求
  getCached: <T = any>(
    url: string,
    config?: AxiosRequestConfig & { cacheTime?: number }
  ): Promise<T> => {
    const cacheKey = generateCacheKey(url, config?.params);
    const cached = cacheManager.get<T>(cacheKey);

    if (cached) {
      if (API_LOGGING_ENABLED) {
        console.log(`缓存命中: ${url}`);
      }
      return Promise.resolve(cached);
    }

    return requestDeduplicator.deduplicate(cacheKey, () =>
      api.get(url, config).then((res) => {
        const data = res.data;
        // 缓存成功的响应
        if (data.success) {
          cacheManager.set(cacheKey, data, config?.cacheTime || 5 * 60 * 1000);
        }
        return data;
      })
    );
  },

  // 清除缓存
  clearCache: (pattern?: string): void => {
    if (pattern) {
      // 这里可以实现模式匹配清除
      cacheManager.clear();
    } else {
      cacheManager.clear();
    }
  },
};

// 文件上传方法
export const uploadFile = (
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<any> => {
  const formData = new FormData();
  formData.append('file', file);

  return api.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    },
  }).then((res) => res.data);
};

// 下载文件方法
export const downloadFile = (url: string, filename?: string): Promise<void> => {
  return api.get(url, {
    responseType: 'blob',
  }).then((response) => {
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  });
};

// 刷新token方法
export const refreshToken = async (): Promise<string | null> => {
  try {
    const refreshTokenValue = localStorage.getItem('refreshToken');
    if (!refreshTokenValue) {
      throw new Error('No refresh token');
    }

    const response = await api.post('/auth/refresh', {
      refreshToken: refreshTokenValue,
    });

    const { token, refreshToken: newRefreshToken, user } = response.data.data;
    
    // 更新本地存储
    localStorage.setItem('token', token);
    localStorage.setItem('refreshToken', newRefreshToken);
    localStorage.setItem('user', JSON.stringify(user));

    return token;
  } catch (error) {
    // 刷新失败，清除所有认证信息
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');

    // 避免在登录页面重复跳转
    if (!window.location.pathname.includes('/login')) {
      window.location.href = '/login';
    }
    return null;
  }
};

// 检查token是否即将过期并自动刷新
export const checkAndRefreshToken = (): void => {
  const token = localStorage.getItem('token');
  if (!token) return;

  try {
    // 解析JWT token获取过期时间
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = payload.exp - currentTime;

    // 如果token在5分钟内过期，则刷新
    if (timeUntilExpiry < 300) {
      refreshToken();
    }
  } catch (error) {
    console.error('解析token失败:', error);
  }
};

// 定期检查token状态
setInterval(checkAndRefreshToken, 60000); // 每分钟检查一次

export default api;
