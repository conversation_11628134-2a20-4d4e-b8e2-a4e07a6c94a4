import { request } from './api';
import { Website, QueryParams } from '@/types';

// 网站相关API接口
export class WebsiteApi {
  // 获取网站列表（带缓存）
  static async getWebsites(params: QueryParams = {}) {
    // 如果参数中包含 _refresh，说明是强制刷新，绕过缓存
    if (params._refresh) {
      const { _refresh, ...apiParams } = params;
      return request.get('/websites', { params: apiParams });
    }

    return request.getCached('/websites', {
      params,
      cacheTime: 5 * 60 * 1000 // 缓存5分钟，减少频繁请求
    });
  }

  // 获取单个网站详情（带缓存）
  static async getWebsiteById(id: number) {
    return request.getCached(`/websites/${id}`, {
      cacheTime: 5 * 60 * 1000 // 缓存5分钟
    });
  }

  // 创建网站
  static async createWebsite(data: CreateWebsiteRequest) {
    const result = await request.post('/websites', data);
    // 清除相关缓存
    request.clearCache();
    return result;
  }

  // 更新网站
  static async updateWebsite(id: number, data: UpdateWebsiteRequest) {
    const result = await request.put(`/websites/${id}`, data);
    // 清除相关缓存
    request.clearCache();
    return result;
  }

  // 删除网站
  static async deleteWebsite(id: number) {
    const result = await request.delete(`/websites/${id}`);
    // 清除相关缓存
    request.clearCache();
    return result;
  }

  // 获取平台选项（带缓存）
  static async getPlatforms() {
    return request.getCached('/websites/options/platforms', {
      cacheTime: 60 * 60 * 1000 // 缓存1小时
    });
  }

  // 获取服务器选项（带缓存）
  static async getServers() {
    return request.getCached('/websites/options/servers', {
      cacheTime: 5 * 60 * 1000 // 缓存5分钟
    });
  }

  // 获取行业选项（带缓存）
  static async getIndustries() {
    return request.getCached('/websites/options/industries', {
      cacheTime: 60 * 60 * 1000 // 缓存1小时
    });
  }

  // 批量操作网站
  static async batchUpdate(data: {
    action: string;
    websiteIds: number[];
    data?: any;
  }) {
    const result = await request.post('/websites/batch', data);
    // 清除相关缓存
    request.clearCache();
    return result;
  }

  // 检查网站可访问性
  static async checkAccess(id: number) {
    return request.post(`/websites/${id}/access-check`);
  }

  // 获取网站统计信息
  static async getStats() {
    return request.getCached('/websites/stats', {
      cacheTime: 5 * 60 * 1000 // 缓存5分钟
    });
  }

  // 高级功能
  // SSL证书检查
  static async checkSSL(id: number) {
    return request.post(`/websites/${id}/check-ssl`);
  }

  // 域名状态检测
  static async checkDomain(id: number) {
    return request.post(`/websites/${id}/domain-check`);
  }

  // 安全扫描
  static async securityScan(id: number) {
    return request.post(`/websites/${id}/security-scan`);
  }

  // 性能测试
  static async performanceTest(id: number) {
    return request.post(`/websites/${id}/performance-test`);
  }

  // 备份管理
  static async createBackup(id: number, data: any) {
    return request.post(`/websites/${id}/backup`, data);
  }

  static async getBackups(id: number) {
    return request.get(`/websites/${id}/backups`);
  }

  static async restoreBackup(id: number, backupId: number) {
    return request.post(`/websites/${id}/restore/${backupId}`);
  }

  // 凭据管理
  static async getCredentials(id: number) {
    return request.get(`/websites/${id}/credentials`);
  }

  static async createCredential(id: number, data: any) {
    return request.post(`/websites/${id}/credentials`, data);
  }

  static async updateCredential(id: number, credentialId: number, data: any) {
    return request.put(`/websites/${id}/credentials/${credentialId}`, data);
  }

  // 网站账号管理
  static async getAccounts(id: number) {
    return request.get(`/websites/${id}/accounts`);
  }

  static async createAccount(id: number, data: any) {
    const result = await request.post(`/websites/${id}/accounts`, data);
    // 清除相关缓存
    request.clearCache();
    return result;
  }

  static async updateAccount(id: number, accountId: number, data: any) {
    const result = await request.put(`/websites/${id}/accounts/${accountId}`, data);
    // 清除相关缓存
    request.clearCache();
    return result;
  }

  static async deleteAccount(id: number, accountId: number) {
    const result = await request.delete(`/websites/${id}/accounts/${accountId}`);
    // 清除相关缓存
    request.clearCache();
    return result;
  }

  static async deleteCredential(id: number, credentialId: number) {
    return request.delete(`/websites/${id}/credentials/${credentialId}`);
  }

  // 监控设置
  static async getMonitorConfig(id: number) {
    return request.get(`/websites/${id}/monitor`);
  }

  static async updateMonitorConfig(id: number, data: any) {
    return request.put(`/websites/${id}/monitor`, data);
  }

  // 获取平台选项（备用数据，当API失败时使用）
  static async getPlatformsFallback() {
    return {
      success: true,
      data: [
        { id: 1, name: 'WordPress', description: 'WordPress CMS' },
        { id: 2, name: 'React', description: 'React应用' },
        { id: 3, name: 'Vue', description: 'Vue.js应用' },
        { id: 4, name: 'Static', description: '静态网站' }
      ]
    };
  }

  // 获取服务器选项（备用数据，当API失败时使用）
  static async getServersFallback() {
    return {
      success: true,
      data: [
        { id: 1, name: 'Server-1', ip_address: '*************', location: '北京' },
        { id: 2, name: 'Server-2', ip_address: '*************', location: '上海' },
        { id: 3, name: 'Server-3', ip_address: '*************', location: '深圳' }
      ]
    };
  }

  // 刷新网站数据（清除缓存）
  static async refreshWebsites() {
    request.clearCache();
    return this.getWebsites();
  }

  // 手动触发网站状态检测（调用定时任务）
  static async triggerAccessCheck() {
    return request.post('/websites/trigger-access-check');
  }

  // 手动触发SSL证书检测（调用定时任务）
  static async triggerSSLCheck() {
    return request.post('/websites/trigger-ssl-check');
  }
}

// 创建网站请求类型
export interface CreateWebsiteRequest {
  siteName: string;
  projectId?: number;
  platformId: number;
  serverId?: number;
  siteUrl: string;
  domain: string;
  industry?: string;
  onlineDate?: string;
  expireDate?: string;
  projectAmount?: number;
  renewalFee?: number;
  sslExpireDate?: string;
  domainExpireDate?: string;
  status?: 'active' | 'inactive' | 'suspended' | 'expired';
  notes?: string;
}

// 更新网站请求类型
export interface UpdateWebsiteRequest extends CreateWebsiteRequest {}

// 平台选项类型
export interface PlatformOption {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
}

// 服务器选项类型
export interface ServerOption {
  id: number;
  name: string;
  ip_address: string;
  location?: string;
}

export default WebsiteApi;
