import { request } from './api';

// 网站附件相关API接口
export class WebsiteAttachmentApi {
  // 获取网站附件列表
  static async getAttachments(websiteId: number, params: {
    category?: string;
    page?: number;
    limit?: number;
  } = {}) {
    return request.get(`/websites/${websiteId}/attachments`, {
      params
    });
  }

  // 获取网站附件列表（带缓存）
  static async getAttachmentsCached(websiteId: number, params: {
    category?: string;
    page?: number;
    limit?: number;
    cacheTime?: number;
    fast?: boolean;
  } = {}) {
    const { cacheTime, ...apiParams } = params;
    return request.getCached(`/websites/${websiteId}/attachments`, {
      params: apiParams,
      cacheTime: cacheTime || 10 * 60 * 1000 // 增加缓存时间到10分钟
    });
  }

  // 上传附件
  static async uploadAttachment(
    websiteId: number, 
    file: File, 
    options: {
      description?: string;
      onProgress?: (progress: number) => void;
    } = {}
  ) {
    const formData = new FormData();
    formData.append('file', file);
    if (options.description) {
      formData.append('description', options.description);
    }

    return request.post(`/websites/${websiteId}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (options.onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          options.onProgress(progress);
        }
      },
    });
  }

  // 删除附件
  static async deleteAttachment(websiteId: number, attachmentId: number) {
    return request.delete(`/websites/${websiteId}/attachments/${attachmentId}`);
  }

  // 更新附件信息
  static async updateAttachment(
    websiteId: number, 
    attachmentId: number, 
    data: {
      description?: string;
    }
  ) {
    return request.put(`/websites/${websiteId}/attachments/${attachmentId}`, data);
  }

  // 获取下载链接
  static getDownloadUrl(websiteId: number, attachmentId: number): string {
    const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    return `${baseUrl}/api/v1/websites/${websiteId}/attachments/${attachmentId}/download`;
  }

  // 获取预览链接
  static getPreviewUrl(websiteId: number, attachmentId: number): string {
    const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    return `${baseUrl}/api/v1/websites/${websiteId}/attachments/${attachmentId}/preview`;
  }

  // 批量上传附件
  static async batchUploadAttachments(
    websiteId: number,
    files: File[],
    options: {
      description?: string;
      onProgress?: (progress: number) => void;
      onFileProgress?: (fileIndex: number, progress: number) => void;
    } = {}
  ) {
    const results = [];
    const totalFiles = files.length;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      try {
        const result = await this.uploadAttachment(websiteId, file, {
          description: options.description || `批量上传 - ${file.name}`,
          onProgress: (fileProgress) => {
            if (options.onFileProgress) {
              options.onFileProgress(i, fileProgress);
            }
            
            // 计算总体进度
            if (options.onProgress) {
              const overallProgress = Math.round(((i + fileProgress / 100) / totalFiles) * 100);
              options.onProgress(overallProgress);
            }
          }
        });
        
        results.push({
          success: true,
          file: file.name,
          data: result.data
        });
      } catch (error) {
        results.push({
          success: false,
          file: file.name,
          error: error
        });
      }
    }

    return {
      success: true,
      data: results,
      summary: {
        total: totalFiles,
        success: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      }
    };
  }

  // 获取附件统计信息
  static async getAttachmentStats(websiteId: number) {
    return request.get(`/websites/${websiteId}/attachments/stats`);
  }

  // 检查文件类型是否支持
  static isSupportedFileType(file: File): boolean {
    const supportedTypes = [
      // 图片类型
      'image/jpeg',
      'image/jpg', 
      'image/png',
      'image/gif',
      'image/webp',
      'image/svg+xml',
      
      // PDF文档
      'application/pdf',
      
      // Excel表格
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      
      // Word文档
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      
      // 其他常用格式
      'text/plain',
      'application/zip',
      'application/x-rar-compressed'
    ];

    return supportedTypes.includes(file.type);
  }

  // 检查文件大小是否符合要求
  static isValidFileSize(file: File, maxSizeMB: number = 50): boolean {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return file.size <= maxSizeBytes;
  }

  // 获取文件分类
  static getFileCategory(file: File): string {
    if (file.type.startsWith('image/')) return 'image';
    if (file.type === 'application/pdf') return 'pdf';
    if (file.type.includes('spreadsheet') || file.type.includes('excel') || file.type === 'text/csv') return 'excel';
    if (file.type.includes('wordprocessing') || file.type.includes('msword')) return 'word';
    return 'other';
  }

  // 检查是否支持预览
  static isPreviewSupported(file: File): boolean {
    const previewTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png', 
      'image/gif',
      'image/webp',
      'image/svg+xml',
      'application/pdf'
    ];
    return previewTypes.includes(file.type);
  }

  // 格式化文件大小
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 验证文件
  static validateFile(file: File): {
    valid: boolean;
    error?: string;
  } {
    // 检查文件类型
    if (!this.isSupportedFileType(file)) {
      return {
        valid: false,
        error: `不支持的文件类型: ${file.type}。支持的类型: 图片、PDF、Excel、Word文档等`
      };
    }

    // 检查文件大小
    if (!this.isValidFileSize(file)) {
      return {
        valid: false,
        error: `文件大小超过限制，最大支持50MB，当前文件: ${this.formatFileSize(file.size)}`
      };
    }

    return { valid: true };
  }

  // 批量验证文件
  static validateFiles(files: File[]): {
    valid: boolean;
    errors: string[];
    validFiles: File[];
    invalidFiles: { file: File; error: string }[];
  } {
    const errors: string[] = [];
    const validFiles: File[] = [];
    const invalidFiles: { file: File; error: string }[] = [];

    files.forEach(file => {
      const validation = this.validateFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        invalidFiles.push({ file, error: validation.error! });
        errors.push(`${file.name}: ${validation.error}`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      validFiles,
      invalidFiles
    };
  }
}

// 附件相关类型定义
export interface WebsiteAttachment {
  id: number;
  websiteId: number;
  fileName: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
  mimeType: string;
  category: 'image' | 'pdf' | 'excel' | 'word' | 'other';
  description?: string;
  uploadedBy: number;
  uploadedByName: string;
  isPreviewAvailable: boolean;
  thumbnailPath?: string;
  downloadCount: number;
  lastAccessed?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AttachmentUploadOptions {
  description?: string;
  onProgress?: (progress: number) => void;
}

export interface BatchUploadResult {
  success: boolean;
  file: string;
  data?: any;
  error?: any;
}

export interface AttachmentStats {
  total: number;
  byCategory: Record<string, number>;
  totalSize: number;
  averageSize: number;
}
