import api from './api';

// 监控数据接口
export interface MonitorData {
  systemStatus: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  websiteStatus: Array<{
    id: number;
    name: string;
    url: string;
    status: 'online' | 'offline' | 'warning';
    responseTime: number;
    uptime: number;
    lastCheck: string;
  }>;
  serverStatus: Array<{
    id: number;
    name: string;
    ip: string;
    status: 'online' | 'offline' | 'maintenance';
    cpu: number;
    memory: number;
    disk: number;
    uptime: string;
  }>;
  sslStatus: Array<{
    id: number;
    domain: string;
    issuer: string;
    expiryDate: string;
    daysLeft: number;
    status: 'valid' | 'expiring' | 'expired';
  }>;
  alerts: Array<{
    id: number;
    type: 'error' | 'warning' | 'info';
    message: string;
    time: string;
    resolved: boolean;
  }>;
}

// 系统性能指标接口
export interface SystemMetrics {
  cpu: {
    usage: number;
    cores: number;
    frequency: string;
  };
  memory: {
    usage: number;
    total: string;
    available: string;
  };
  disk: {
    usage: number;
    total: string;
    available: string;
  };
  network: {
    inbound: number;
    outbound: number;
    connections: number;
  };
}

// 监控服务类
class MonitorService {
  // 获取监控数据
  async getMonitorData(): Promise<MonitorData> {
    const response = await api.get('/monitor/data');
    return response.data.data;
  }

  // 获取系统性能指标
  async getSystemMetrics(): Promise<SystemMetrics> {
    const response = await api.get('/monitor/metrics');
    return response.data.data;
  }

  // 检查网站状态
  async checkWebsiteStatus(websiteId: number): Promise<any> {
    const response = await api.post(`/monitor/website/${websiteId}/check`);
    return response.data.data;
  }

  // 处理告警
  async resolveAlert(alertId: number): Promise<void> {
    await api.post(`/monitor/alert/${alertId}/resolve`);
  }
}

export const monitorService = new MonitorService();
export default monitorService;
