import { authApi, storage } from './auth';
import { User, LoginRequest } from '../types';

// 登录超时配置
const LOGIN_TIMEOUT_CONFIG = {
  // 默认超时时间（毫秒）- 2小时
  DEFAULT_TIMEOUT: 2 * 60 * 60 * 1000,
  // 警告时间（毫秒）- 超时前10分钟警告
  WARNING_TIME: 10 * 60 * 1000,
  // 检查间隔（毫秒）- 每分钟检查一次
  CHECK_INTERVAL: 60 * 1000,
};

class EnhancedAuthManager {
  private timeoutTimer: NodeJS.Timeout | null = null;
  private warningTimer: NodeJS.Timeout | null = null;
  private checkTimer: NodeJS.Timeout | null = null;
  private onTimeout?: () => void;
  private onWarning?: (remainingTime: number) => void;

  constructor() {
    this.initializeTimeoutCheck();
  }

  // 设置超时回调
  setTimeoutCallbacks(onTimeout: () => void, onWarning?: (remainingTime: number) => void) {
    this.onTimeout = onTimeout;
    this.onWarning = onWarning;
  }

  // 登录
  async login(credentials: LoginRequest): Promise<User> {
    try {
      const response = await authApi.login(credentials);
      
      if (response.success && response.data) {
        const { token, refreshToken, user } = response.data;
        
        // 保存认证信息和登录时间
        storage.setAuth(token, refreshToken, user);
        this.setLoginTime();
        
        // 启动超时检查
        this.startTimeoutCheck();
        
        return user;
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error: any) {
      throw new Error(error.message || '登录失败');
    }
  }

  // 登出
  async logout(): Promise<void> {
    try {
      // 调用后端登出API
      await authApi.logout();
    } catch (error) {
      console.warn('后端登出失败:', error);
    } finally {
      // 清除本地数据
      this.clearAuth();
    }
  }

  // 刷新用户信息
  async refreshUser(): Promise<User> {
    try {
      const response = await authApi.getCurrentUser();
      
      if (response.success && response.data) {
        const user = response.data;
        storage.setUser(user);
        return user;
      } else {
        throw new Error('获取用户信息失败');
      }
    } catch (error: any) {
      throw new Error(error.message || '获取用户信息失败');
    }
  }

  // 设置登录时间
  private setLoginTime(): void {
    const loginTime = Date.now();
    localStorage.setItem('loginTime', loginTime.toString());
  }

  // 获取登录时间
  private getLoginTime(): number | null {
    const loginTime = localStorage.getItem('loginTime');
    return loginTime ? parseInt(loginTime, 10) : null;
  }

  // 获取剩余时间
  getRemainingTime(): number {
    const loginTime = this.getLoginTime();
    if (!loginTime) return 0;
    
    const elapsed = Date.now() - loginTime;
    const remaining = LOGIN_TIMEOUT_CONFIG.DEFAULT_TIMEOUT - elapsed;
    return Math.max(0, remaining);
  }

  // 检查是否超时
  isTimeout(): boolean {
    return this.getRemainingTime() <= 0;
  }

  // 检查是否需要警告
  shouldWarn(): boolean {
    const remaining = this.getRemainingTime();
    return remaining > 0 && remaining <= LOGIN_TIMEOUT_CONFIG.WARNING_TIME;
  }

  // 延长登录时间
  extendLogin(): void {
    this.setLoginTime();
    this.startTimeoutCheck();
  }

  // 初始化超时检查
  private initializeTimeoutCheck(): void {
    // 如果已经登录，启动检查
    if (storage.isAuthenticated()) {
      this.startTimeoutCheck();
    }
  }

  // 启动超时检查
  private startTimeoutCheck(): void {
    this.clearTimers();
    
    const remaining = this.getRemainingTime();
    
    if (remaining <= 0) {
      // 已经超时
      this.handleTimeout();
      return;
    }

    // 设置超时定时器
    this.timeoutTimer = setTimeout(() => {
      this.handleTimeout();
    }, remaining);

    // 设置警告定时器
    const warningTime = remaining - LOGIN_TIMEOUT_CONFIG.WARNING_TIME;
    if (warningTime > 0) {
      this.warningTimer = setTimeout(() => {
        this.handleWarning();
      }, warningTime);
    } else if (this.shouldWarn()) {
      // 立即警告
      this.handleWarning();
    }

    // 设置定期检查定时器
    this.checkTimer = setInterval(() => {
      this.checkLoginStatus();
    }, LOGIN_TIMEOUT_CONFIG.CHECK_INTERVAL);
  }

  // 处理超时
  private handleTimeout(): void {
    console.log('登录已超时');
    this.clearAuth();
    
    if (this.onTimeout) {
      this.onTimeout();
    }
  }

  // 处理警告
  private handleWarning(): void {
    const remaining = this.getRemainingTime();
    console.log(`登录即将超时，剩余时间: ${Math.ceil(remaining / 1000 / 60)} 分钟`);
    
    if (this.onWarning) {
      this.onWarning(remaining);
    }
  }

  // 检查登录状态
  private checkLoginStatus(): void {
    if (!storage.isAuthenticated()) {
      this.clearTimers();
      return;
    }

    if (this.isTimeout()) {
      this.handleTimeout();
    } else if (this.shouldWarn() && this.onWarning) {
      this.onWarning(this.getRemainingTime());
    }
  }

  // 清除定时器
  private clearTimers(): void {
    if (this.timeoutTimer) {
      clearTimeout(this.timeoutTimer);
      this.timeoutTimer = null;
    }
    
    if (this.warningTimer) {
      clearTimeout(this.warningTimer);
      this.warningTimer = null;
    }
    
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
    }
  }

  // 清除认证信息
  private clearAuth(): void {
    this.clearTimers();
    storage.clearAuth();
    localStorage.removeItem('loginTime');
  }

  // 格式化剩余时间
  formatRemainingTime(milliseconds: number): string {
    const minutes = Math.ceil(milliseconds / 1000 / 60);
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    
    if (hours > 0) {
      return `${hours}小时${remainingMinutes}分钟`;
    } else {
      return `${remainingMinutes}分钟`;
    }
  }
}

export const enhancedAuthManager = new EnhancedAuthManager();
export default enhancedAuthManager;
