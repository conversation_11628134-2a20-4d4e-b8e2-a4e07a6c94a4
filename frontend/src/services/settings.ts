import api from './api';

// 系统设置接口
export interface SystemSettings {
  general: {
    siteName: string;
    siteUrl: string;
    adminEmail: string;
    timezone: string;
    language: string;
    dateFormat: string;
    enableRegistration: boolean;
    enableMaintenance: boolean;
    maintenanceMessage: string;
  };
  notification: {
    emailEnabled: boolean;
    smsEnabled: boolean;
    webhookEnabled: boolean;
    emailHost: string;
    emailPort: number;
    emailUser: string;
    emailPassword: string;
    smsProvider: string;
    smsApiKey: string;
    webhookUrl: string;
    notificationTypes: string[];
  };
  security: {
    enableTwoFactor: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    passwordRequireSpecial: boolean;
    enableIpWhitelist: boolean;
    ipWhitelist: string[];
    enableAuditLog: boolean;
    auditLogRetention: number;
  };
  backup: {
    enableAutoBackup: boolean;
    backupFrequency: string;
    backupTime: string;
    backupRetention: number;
    backupLocation: string;
    enableCloudBackup: boolean;
    cloudProvider: string;
    cloudConfig: any;
  };
  api: {
    enableApi: boolean;
    apiRateLimit: number;
    apiKeyExpiration: number;
    enableApiLogging: boolean;
    allowedOrigins: string[];
    enableCors: boolean;
  };
}

// 设置服务类
class SettingsService {
  // 获取所有设置
  async getSettings(): Promise<SystemSettings> {
    const response = await api.get('/settings');
    return response.data.data;
  }

  // 获取特定分类的设置
  async getSettingsByCategory(category: string): Promise<any> {
    const response = await api.get(`/settings/${category}`);
    return response.data.data;
  }

  // 更新设置
  async updateSettings(category: string, settings: any): Promise<void> {
    await api.put('/settings', { category, settings });
  }

  // 重置设置
  async resetSettings(category: string): Promise<void> {
    await api.post(`/settings/${category}/reset`);
  }

  // 测试邮件配置
  async testEmailConfig(config: {
    emailHost: string;
    emailPort: number;
    emailUser: string;
    emailPassword: string;
  }): Promise<void> {
    await api.post('/settings/notification/test-email', config);
  }

  // 导出配置
  async exportConfig(): Promise<Blob> {
    const response = await api.get('/settings/export/config', {
      responseType: 'blob'
    });
    return response.data;
  }

  // 导入配置
  async importConfig(config: any): Promise<void> {
    await api.post('/settings/import/config', config);
  }
}

export const settingsService = new SettingsService();
export default settingsService;
