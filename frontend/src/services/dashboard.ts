import { request } from './api';
import { DashboardStats, ChartData } from '../types';

// 仪表盘相关API接口
export class DashboardApi {
  // 获取仪表盘统计数据（带缓存）
  static async getStats() {
    return request.getCached('/dashboard/stats', {
      cacheTime: 5 * 60 * 1000 // 缓存5分钟
    });
  }

  // 获取图表数据（带缓存）
  static async getCharts(type: string = 'website-status', period: string = '7d') {
    return request.getCached('/dashboard/charts', {
      params: { type, period },
      cacheTime: 10 * 60 * 1000 // 缓存10分钟
    });
  }

  // 刷新仪表盘数据（清除缓存）
  static async refreshData() {
    request.clearCache();
    return Promise.all([
      this.getStats(),
      this.getCharts('website-status'),
      this.getCharts('project-progress'),
      this.getCharts('monthly-trend'),
      this.getCharts('platform-distribution')
    ]);
  }
}

export default DashboardApi;
