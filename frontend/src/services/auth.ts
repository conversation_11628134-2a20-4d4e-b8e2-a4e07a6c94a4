import { request } from './api';
import { 
  User, 
  LoginRequest, 
  LoginResponse, 
  ApiResponse,
  CreateUserRequest 
} from '../types';

// 认证相关API
export const authApi = {
  // 用户登录
  login: (data: LoginRequest): Promise<ApiResponse<LoginResponse>> =>
    request.post('/auth/login', data),

  // 用户注册（仅超级管理员）
  register: (data: CreateUserRequest): Promise<ApiResponse<{ userId: number }>> =>
    request.post('/auth/register', data),

  // 刷新token
  refreshToken: (refreshToken: string): Promise<ApiResponse<LoginResponse>> =>
    request.post('/auth/refresh', { refreshToken }),

  // 用户登出
  logout: (): Promise<ApiResponse> =>
    request.post('/auth/logout'),

  // 获取当前用户信息
  getCurrentUser: (): Promise<ApiResponse<User>> =>
    request.get('/auth/me'),

  // 修改密码
  changePassword: (data: { currentPassword: string; newPassword: string }): Promise<ApiResponse> =>
    request.put('/auth/change-password', data),
};

// 本地存储管理
export const storage = {
  // 保存认证信息
  setAuth: (token: string, refreshToken: string, user: User): void => {
    localStorage.setItem('token', token);
    localStorage.setItem('refreshToken', refreshToken);
    localStorage.setItem('user', JSON.stringify(user));
  },

  // 获取token
  getToken: (): string | null => {
    return localStorage.getItem('token');
  },

  // 获取刷新token
  getRefreshToken: (): string | null => {
    return localStorage.getItem('refreshToken');
  },

  // 获取用户信息
  getUser: (): User | null => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        return JSON.parse(userStr);
      } catch (error) {
        console.error('解析用户信息失败:', error);
        return null;
      }
    }
    return null;
  },

  // 清除认证信息
  clearAuth: (): void => {
    localStorage.removeItem('token');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('user');
  },

  // 检查是否已登录
  isAuthenticated: (): boolean => {
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');
    return !!(token && user);
  },
};

// 权限检查工具
export const permission = {
  // 检查用户角色
  hasRole: (user: User | null, requiredRole: string | string[]): boolean => {
    if (!user) return false;

    const roleHierarchy = {
      'super_admin': 3,
      'admin': 2,
      'user': 1,
    };

    const userLevel = roleHierarchy[user.role] || 0;

    if (Array.isArray(requiredRole)) {
      return requiredRole.some(role => {
        const requiredLevel = roleHierarchy[role as keyof typeof roleHierarchy] || 0;
        return userLevel >= requiredLevel;
      });
    }

    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
    return userLevel >= requiredLevel;
  },

  // 检查是否为管理员
  isAdmin: (user: User | null): boolean => {
    return permission.hasRole(user, ['admin', 'super_admin']);
  },

  // 检查是否为超级管理员
  isSuperAdmin: (user: User | null): boolean => {
    return permission.hasRole(user, 'super_admin');
  },

  // 检查用户状态
  isActive: (user: User | null): boolean => {
    return user?.status === 'active';
  },
};

// 认证状态管理
export class AuthManager {
  private static instance: AuthManager;
  private user: User | null = null;
  private listeners: Array<(user: User | null) => void> = [];

  private constructor() {
    this.user = storage.getUser();
  }

  public static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager();
    }
    return AuthManager.instance;
  }

  // 登录
  public async login(credentials: LoginRequest): Promise<User> {
    try {
      const response = await authApi.login(credentials);
      const { user, token, refreshToken } = response.data;
      
      storage.setAuth(token, refreshToken, user);
      this.setUser(user);
      
      return user;
    } catch (error) {
      throw error;
    }
  }

  // 登出
  public async logout(): Promise<void> {
    try {
      await authApi.logout();
    } catch (error) {
      console.error('登出请求失败:', error);
    } finally {
      storage.clearAuth();
      this.setUser(null);
    }
  }

  // 获取当前用户
  public getUser(): User | null {
    return this.user;
  }

  // 设置用户
  public setUser(user: User | null): void {
    this.user = user;
    this.notifyListeners();
  }

  // 检查是否已登录
  public isAuthenticated(): boolean {
    return !!(this.user && storage.getToken());
  }

  // 刷新用户信息
  public async refreshUserInfo(): Promise<User | null> {
    try {
      const response = await authApi.getCurrentUser();
      const user = response.data;
      
      // 更新本地存储
      localStorage.setItem('user', JSON.stringify(user));
      this.setUser(user);
      
      return user;
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      return null;
    }
  }

  // 添加状态监听器
  public addListener(listener: (user: User | null) => void): void {
    this.listeners.push(listener);
  }

  // 移除状态监听器
  public removeListener(listener: (user: User | null) => void): void {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  // 通知所有监听器
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.user));
  }
}

// 导出单例实例
export const authManager = AuthManager.getInstance();
