import api from './api';
import { Server, QueryParams } from '../types';

export interface ServerListResponse {
  success: boolean;
  data: Server[];
  total: number;
  page: number;
  pageSize: number;
}

export interface ServerResponse {
  success: boolean;
  data: Server;
}

export interface CreateServerRequest {
  name: string;
  ipAddress: string;
  location: string;
  provider: string;
  specifications: {
    cpu: string;
    memory: string;
    storage: string;
    bandwidth: string;
    os: string;
  };
  expireDate?: string;
  renewalFee?: number;
  status: string;
  monitoringEnabled: boolean;
  alertThresholds?: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkUsage: number;
  };
  notes?: string;
}

export interface UpdateServerRequest extends CreateServerRequest {
  id: number;
}

export class ServerApi {
  // 获取服务器列表
  static async getServers(params?: QueryParams): Promise<ServerListResponse> {
    try {
      const response = await api.get('/servers', { params });
      return response.data;
    } catch (error) {
      console.error('获取服务器列表失败:', error);
      throw error;
    }
  }

  // 获取服务器详情
  static async getServer(id: number): Promise<ServerResponse> {
    try {
      const response = await api.get(`/servers/${id}`);
      return response.data;
    } catch (error) {
      console.error('获取服务器详情失败:', error);
      throw error;
    }
  }

  // 创建服务器
  static async createServer(data: CreateServerRequest): Promise<ServerResponse> {
    try {
      const response = await api.post('/servers', data);
      return response.data;
    } catch (error) {
      console.error('创建服务器失败:', error);
      throw error;
    }
  }

  // 更新服务器
  static async updateServer(id: number, data: UpdateServerRequest): Promise<ServerResponse> {
    try {
      const response = await api.put(`/servers/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('更新服务器失败:', error);
      throw error;
    }
  }

  // 删除服务器
  static async deleteServer(id: number): Promise<{ success: boolean }> {
    try {
      const response = await api.delete(`/servers/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除服务器失败:', error);
      throw error;
    }
  }

  // 批量删除服务器
  static async batchDeleteServers(ids: number[]): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.delete('/servers/batch', { data: { ids } });
      return response.data;
    } catch (error) {
      console.error('批量删除服务器失败:', error);
      throw error;
    }
  }

  // 批量修改服务器到期日期
  static async batchUpdateExpireDate(ids: number[], expireDate: string): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.put('/servers/batch/expire-date', { ids, expireDate });
      return response.data;
    } catch (error) {
      console.error('批量修改服务器到期日期失败:', error);
      throw error;
    }
  }

  // 获取服务器选项（用于下拉选择）
  static async getServerOptions(): Promise<{ success: boolean; data: Array<{ id: number; name: string; ipAddress: string; status: string }> }> {
    try {
      const response = await api.get('/servers/options');
      return response.data;
    } catch (error) {
      console.error('获取服务器选项失败:', error);
      throw error;
    }
  }

  // 获取服务器监控数据
  static async getServerMonitoring(id: number): Promise<{ success: boolean; data: any }> {
    try {
      const response = await api.get(`/servers/${id}/monitoring`);
      return response.data;
    } catch (error) {
      console.error('获取服务器监控数据失败:', error);
      throw error;
    }
  }

  // 批量操作服务器
  static async batchOperation(operation: string, serverIds: number[]): Promise<{ success: boolean; message: string }> {
    try {
      const response = await api.post('/servers/batch', {
        operation,
        serverIds
      });
      return response.data;
    } catch (error) {
      console.error('批量操作服务器失败:', error);
      throw error;
    }
  }
}

export default ServerApi;
