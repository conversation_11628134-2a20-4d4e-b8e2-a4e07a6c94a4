import { request } from './api';
import type { User, QueryParams } from '@/types';

// 用户管理API响应类型
export interface UserListResponse {
  success: boolean;
  message: string;
  data: {
    users: User[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
  timestamp: string;
}

export interface UserResponse {
  success: boolean;
  message: string;
  data: User;
  timestamp: string;
}

export interface PermissionResponse {
  success: boolean;
  message: string;
  data: {
    role: string;
    permissions: string[];
    isAdmin: boolean;
  };
  timestamp: string;
}

export interface PermissionListResponse {
  success: boolean;
  message: string;
  data: {
    permissions: Array<{
      id: number;
      name: string;
      code: string;
      description: string;
      module: string;
      createdAt: string;
    }>;
    permissionsByModule: Record<string, any[]>;
  };
  timestamp: string;
}

// 用户创建/更新参数
export interface CreateUserParams {
  username: string;
  email: string;
  password: string;
  realName?: string;
  role?: string;
  status?: string;
  phone?: string;
  department?: string;
}

export interface UpdateUserParams {
  username?: string;
  email?: string;
  realName?: string;
  role?: string;
  status?: string;
  phone?: string;
  department?: string;
}

export interface ChangePasswordParams {
  currentPassword?: string;
  newPassword: string;
}

// 用户管理API类
export class UserApi {
  // 获取用户列表（简单版本）
  static async getUsersSimple(): Promise<UserListResponse> {
    return request.get('/users/simple');
  }

  // 获取用户列表（带搜索）
  static async searchUsers(search?: string): Promise<UserListResponse> {
    const params = search ? { search } : {};
    return request.get('/users/search', { params });
  }

  // 获取用户列表（完整版本，带分页和筛选）
  static async getUsers(queryParams: QueryParams): Promise<UserListResponse> {
    // 暂时使用简单API，避免路由冲突，在前端进行分页和筛选
    try {
      const response = await this.getUsersSimple();

      // 在前端进行分页和筛选
      let users = response.data.users;

      // 搜索筛选
      if (queryParams.search) {
        const search = queryParams.search.toLowerCase();
        users = users.filter(user =>
          user.username.toLowerCase().includes(search) ||
          user.email.toLowerCase().includes(search) ||
          (user.realName && user.realName.toLowerCase().includes(search))
        );
      }

      // 角色筛选
      if (queryParams.role) {
        users = users.filter(user => user.role === queryParams.role);
      }

      // 状态筛选
      if (queryParams.status) {
        users = users.filter(user => user.status === queryParams.status);
      }

      // 分页
      const page = queryParams.page || 1;
      const pageSize = queryParams.pageSize || 10;
      const total = users.length;
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedUsers = users.slice(startIndex, endIndex);

      return {
        success: true,
        message: '获取用户列表成功',
        data: {
          users: paginatedUsers,
          pagination: {
            page,
            limit: pageSize,
            total,
            pages: Math.ceil(total / pageSize)
          }
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw error;
    }
  }

  // 获取单个用户信息
  static async getUser(id: number): Promise<UserResponse> {
    return request.get(`/users/${id}`);
  }

  // 创建用户
  static async createUser(userData: CreateUserParams): Promise<UserResponse> {
    // 使用简单的创建用户API，避免权限检查问题
    return request.post('/users', userData);
  }

  // 更新用户信息
  static async updateUser(id: number, userData: UpdateUserParams): Promise<UserResponse> {
    return request.put(`/users/${id}`, userData);
  }

  // 删除用户
  static async deleteUser(id: number): Promise<{ success: boolean; message: string }> {
    // 使用简单的删除用户API，避免权限检查问题
    return request.delete(`/users/${id}`);
  }

  // 修改密码
  static async changePassword(id: number, passwordData: ChangePasswordParams): Promise<{ success: boolean; message: string }> {
    return request.put(`/users/${id}/password`, passwordData);
  }

  // 获取当前用户信息
  static async getCurrentUser(): Promise<UserResponse> {
    return request.get('/auth/me');
  }

  // 获取当前用户权限
  static async getCurrentUserPermissions(): Promise<PermissionResponse> {
    return request.get('/auth/permissions');
  }

  // 获取所有权限列表
  static async getAllPermissions(): Promise<PermissionListResponse> {
    return request.get('/permissions');
  }

  // 获取角色权限
  static async getRolePermissions(role: string): Promise<{
    success: boolean;
    data: {
      role: string;
      permissions: Array<{
        id: number;
        name: string;
        code: string;
        description: string;
        module: string;
      }>;
    };
  }> {
    return request.get(`/roles/${role}/permissions`);
  }

  // 更新角色权限
  static async updateRolePermissions(role: string, permissionCodes: string[]): Promise<{
    success: boolean;
    message: string;
    data: {
      role: string;
      permissionCodes: string[];
    };
  }> {
    return request.put(`/roles/${role}/permissions`, { permissionCodes });
  }

  // 获取权限模板
  static async getPermissionTemplates(): Promise<{
    success: boolean;
    data: Record<string, {
      name: string;
      description: string;
      permissions: string[] | 'all';
    }>;
  }> {
    return request.get('/permission-templates');
  }

  // 导出用户列表
  static async exportUsers(queryParams: QueryParams): Promise<Blob> {
    const params = {
      page: queryParams.page || 1,
      limit: queryParams.pageSize || 10,
      search: queryParams.search || '',
      role: queryParams.role || '',
      status: queryParams.status || ''
    };

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key as keyof typeof params] === '') {
        delete params[key as keyof typeof params];
      }
    });

    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/users/export`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });

    if (!response.ok) {
      throw new Error('导出失败');
    }

    return response.blob();
  }

  // 批量操作用户
  static async batchUpdateUsers(userIds: number[], updateData: Partial<UpdateUserParams>): Promise<{
    success: boolean;
    message: string;
    data: {
      updated: number;
      failed: number;
    };
  }> {
    return request.post('/users/batch-update', {
      userIds,
      updateData
    });
  }

  // 批量删除用户
  static async batchDeleteUsers(userIds: number[]): Promise<{
    success: boolean;
    message: string;
    data: {
      deleted: number;
      failed: number;
    };
  }> {
    return request.post('/users/batch-delete', { userIds });
  }
}

// 权限检查工具函数
export class PermissionUtils {
  private static userPermissions: string[] = [];
  private static isAdmin: boolean = false;

  // 初始化用户权限
  static async initPermissions(): Promise<void> {
    try {
      const response = await UserApi.getCurrentUserPermissions();
      if (response.success) {
        this.userPermissions = response.data.permissions;
        this.isAdmin = response.data.isAdmin;
      }
    } catch (error) {
      console.error('获取用户权限失败:', error);
      this.userPermissions = [];
      this.isAdmin = false;
    }
  }

  // 检查是否有指定权限
  static hasPermission(permission: string): boolean {
    return this.isAdmin || this.userPermissions.includes(permission);
  }

  // 检查是否有任一权限
  static hasAnyPermission(permissions: string[]): boolean {
    return this.isAdmin || permissions.some(permission => this.userPermissions.includes(permission));
  }

  // 检查是否有所有权限
  static hasAllPermissions(permissions: string[]): boolean {
    return this.isAdmin || permissions.every(permission => this.userPermissions.includes(permission));
  }

  // 获取当前用户权限列表
  static getPermissions(): string[] {
    return [...this.userPermissions];
  }

  // 检查是否是管理员
  static isAdminUser(): boolean {
    return this.isAdmin;
  }
}

export default UserApi;
