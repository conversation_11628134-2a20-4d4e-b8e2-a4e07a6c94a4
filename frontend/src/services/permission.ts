import api from './api';

export interface Permission {
  id: number;
  name: string;
  code: string;
  description: string;
  module: string;
  createdAt: string;
}

export interface RolePermissions {
  role: string;
  permissions: Permission[];
}

export interface PermissionTemplate {
  role: string;
  name: string;
  description: string;
  permissions: string[] | 'all';
}

export interface UserWithPermissions {
  id: number;
  username: string;
  email: string;
  realName: string;
  role: string;
  status: string;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

export interface UserPermissionData {
  userId: number;
  username: string;
  role: string;
  status: string;
  rolePermissions: string[];
  customPermissions: Array<{
    permission_code: string;
    granted: boolean;
  }>;
  effectivePermissions: string[];
  deniedPermissions: string[];
  lastUpdated: string;
}

export interface PermissionCheckResult {
  userId: number;
  hasAllPermissions: boolean;
  permissionResults: Record<string, boolean>;
}

export interface AuditLog {
  id: number;
  userId: number;
  username: string;
  action: string;
  resource: string;
  resourceId?: string;
  result: 'granted' | 'denied' | 'success' | 'failure';
  details: any;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
}

class PermissionApi {
  // 获取所有权限列表
  async getAllPermissions(): Promise<{
    success: boolean;
    data: {
      permissions: Permission[];
      permissionsByModule: Record<string, Permission[]>;
    };
  }> {
    const response = await api.get('/permissions');
    return response.data;
  }

  // 获取角色权限
  async getRolePermissions(role: string): Promise<{
    success: boolean;
    data: {
      role: string;
      permissions: Permission[];
    };
  }> {
    const response = await api.get(`/roles/${role}/permissions`);
    return response.data;
  }

  // 更新角色权限
  async updateRolePermissions(role: string, permissionCodes: string[]): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await api.put(`/roles/${role}/permissions`, {
      permissionCodes
    });
    return response.data;
  }

  // 获取当前用户权限
  async getCurrentUserPermissions(): Promise<{
    success: boolean;
    data: {
      permissions: string[];
      userPermissions: string[];
    };
  }> {
    const response = await api.get('/auth/permissions');
    return response.data;
  }

  // 获取权限模板
  async getPermissionTemplates(): Promise<{
    success: boolean;
    data: {
      templates: PermissionTemplate[];
    };
  }> {
    const response = await api.get('/permission-templates');
    return response.data;
  }

  // 获取带权限的用户列表
  async getUsersWithPermissions(): Promise<{
    success: boolean;
    data: {
      users: UserWithPermissions[];
    };
  }> {
    const response = await api.get('/users/with-permissions');
    return response.data;
  }

  // 创建带权限的用户
  async createUserWithPermissions(userData: {
    username: string;
    email: string;
    password: string;
    realName: string;
    role: string;
    status: string;
    permissions?: string[];
  }): Promise<{
    success: boolean;
    data: {
      user: UserWithPermissions;
    };
    message: string;
  }> {
    const response = await api.post('/users/with-permissions', userData);
    return response.data;
  }

  // 更新用户权限
  async updateUserPermissions(userId: number, data: {
    role?: string;
    permissions?: string[];
    customPermissions?: Array<{ code: string; granted: boolean }>;
  }): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await api.put(`/users/${userId}/permissions`, data);
    return response.data;
  }

  // 删除用户
  async deleteUserWithPermissions(userId: number): Promise<{
    success: boolean;
    message: string;
  }> {
    const response = await api.delete(`/users/with-permissions/${userId}`);
    return response.data;
  }

  // 获取用户详细权限信息
  async getUserPermissions(userId: number): Promise<{
    success: boolean;
    data: UserPermissionData;
  }> {
    const response = await api.get(`/users/${userId}/permissions`);
    return response.data;
  }

  // 批量检查权限
  async checkMultiplePermissions(permissions: string[]): Promise<{
    success: boolean;
    data: PermissionCheckResult;
  }> {
    const response = await api.post('/permissions/check', { permissions });
    return response.data;
  }

  // 获取审计日志
  async getAuditLogs(params: {
    page?: number;
    limit?: number;
    userId?: number;
    action?: string;
    resource?: string;
    result?: string;
    startDate?: string;
    endDate?: string;
  } = {}): Promise<{
    success: boolean;
    data: {
      logs: AuditLog[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    };
  }> {
    const response = await api.get('/audit/logs', { params });
    return response.data;
  }

  // 获取审计统计
  async getAuditStatistics(params: {
    startDate?: string;
    endDate?: string;
    userId?: number;
  } = {}): Promise<{
    success: boolean;
    data: {
      actionStatistics: any[];
      resultStatistics: any[];
      userStatistics: any[];
      timeStatistics: any[];
    };
  }> {
    const response = await api.get('/audit/statistics', { params });
    return response.data;
  }

  // 检测异常访问
  async detectAnomalies(params: {
    timeWindow?: number;
    maxFailures?: number;
    maxRequests?: number;
  } = {}): Promise<{
    success: boolean;
    data: {
      anomalies: any[];
      detectionTime: string;
      options: any;
    };
  }> {
    const response = await api.get('/audit/anomalies', { params });
    return response.data;
  }

  // 导出审计日志
  async exportAuditLogs(params: {
    format?: 'json' | 'csv';
    userId?: number;
    action?: string;
    resource?: string;
    result?: string;
    startDate?: string;
    endDate?: string;
  } = {}): Promise<Blob> {
    const response = await api.get('/audit/export', { 
      params,
      responseType: 'blob'
    });
    return response.data;
  }

  // 获取用户操作历史
  async getUserHistory(userId: number, params: {
    limit?: number;
    startDate?: string;
    endDate?: string;
  } = {}): Promise<{
    success: boolean;
    data: {
      userId: number;
      logs: AuditLog[];
      pagination: any;
    };
  }> {
    const response = await api.get(`/audit/user/${userId}/history`, { params });
    return response.data;
  }

  // 创建审计报告
  async createAuditReport(params: {
    reportType?: string;
    startDate?: string;
    endDate?: string;
    includeUsers?: boolean;
    includeActions?: boolean;
    includeAnomalies?: boolean;
  }): Promise<{
    success: boolean;
    data: any;
  }> {
    const response = await api.post('/audit/reports', params);
    return response.data;
  }

  // 获取权限统计信息
  async getPermissionStatistics(): Promise<{
    success: boolean;
    data: {
      totalPermissions: number;
      moduleStatistics: any[];
      roleStatistics: any[];
      customPermissionStatistics: any;
    };
  }> {
    const response = await api.get('/permissions/statistics');
    return response.data;
  }

  // 获取角色模板
  async getRoleTemplates(params: {
    includeSystem?: boolean;
    createdBy?: number;
    limit?: number;
  } = {}): Promise<{
    success: boolean;
    data: any[];
  }> {
    const response = await api.get('/roles/templates', { params });
    return response.data;
  }

  // 创建角色模板
  async createRoleTemplate(templateData: {
    name: string;
    description: string;
    permissions: string[];
    isSystem?: boolean;
  }): Promise<{
    success: boolean;
    data: any;
  }> {
    const response = await api.post('/roles/templates', templateData);
    return response.data;
  }

  // 应用角色模板
  async applyRoleTemplate(role: string, templateId: number): Promise<{
    success: boolean;
    data: any;
  }> {
    const response = await api.post(`/roles/${role}/apply-template`, { templateId });
    return response.data;
  }

  // 获取权限详情
  async getPermissionDetails(permissionCode: string): Promise<{
    success: boolean;
    data: any;
  }> {
    const response = await api.get(`/permissions/${permissionCode}/details`);
    return response.data;
  }

  // 验证权限依赖
  async validatePermissionDependencies(permissions: string[]): Promise<{
    success: boolean;
    data: {
      valid: boolean;
      missingDependencies: any[];
      warnings: any[];
    };
  }> {
    const response = await api.post('/permissions/validate-dependencies', { permissions });
    return response.data;
  }
}

export const permissionApi = new PermissionApi();
export default permissionApi;
