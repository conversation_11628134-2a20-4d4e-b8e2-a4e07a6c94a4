import api from './api';

// 用户资料接口
export interface UserProfile {
  id: number;
  username: string;
  email: string;
  realName: string;
  phone: string;
  avatar: string;
  role: string;
  department: string;
  position: string;
  bio: string;
  preferences: {
    language: string;
    timezone: string;
    theme: string;
    emailNotifications: boolean;
    smsNotifications: boolean;
    desktopNotifications: boolean;
  };
  security: {
    twoFactorEnabled: boolean;
    lastPasswordChange: string;
    loginSessions: Array<{
      id: string;
      ip: string;
      location: string;
      device: string;
      lastActive: string;
      current: boolean;
    }>;
  };
  activityLog: Array<{
    id: number;
    action: string;
    description: string;
    ip: string;
    timestamp: string;
    status: 'success' | 'warning' | 'error';
  }>;
}

// 活动日志查询参数
export interface ActivityLogParams {
  page?: number;
  pageSize?: number;
}

// 活动日志响应
export interface ActivityLogResponse {
  list: UserProfile['activityLog'];
  total: number;
  page: number;
  pageSize: number;
}

// 个人资料服务类
class ProfileService {
  // 获取用户资料
  async getProfile(): Promise<UserProfile> {
    // 使用现有的 /auth/me API 获取当前用户信息
    const response = await api.get('/auth/me');
    const userData = response.data.data;

    // 将后端用户数据转换为前端期望的profile数据结构
    return {
      id: userData.id,
      username: userData.username,
      email: userData.email,
      realName: userData.realName || '',
      avatar: userData.avatar || '',
      phone: userData.phone || '',
      department: userData.department || '',
      position: '', // 后端没有这个字段，使用默认值
      bio: '', // 后端没有这个字段，使用默认值
      preferences: {
        language: 'zh-CN', // 默认值
        timezone: 'Asia/Shanghai', // 默认值
        theme: 'light', // 默认值
        emailNotifications: true, // 默认值
        smsNotifications: false, // 默认值
        desktopNotifications: true // 默认值
      },
      security: {
        twoFactorEnabled: false, // 默认值，后端没有这个字段
        lastPasswordChange: userData.updatedAt || '', // 使用更新时间作为密码修改时间
        loginSessions: [] // 默认空数组，后端没有这个数据
      },
      stats: {
        loginCount: 0, // 默认值，后端没有这个字段
        lastLoginTime: userData.lastLogin || '',
        accountCreated: userData.createdAt || ''
      }
    };
  }

  // 更新用户资料
  async updateProfile(profile: {
    realName: string;
    email: string;
    phone: string;
    department: string;
    position: string;
    bio: string;
  }): Promise<void> {
    // 首先获取当前用户ID
    const currentUser = await this.getProfile();

    // 使用现有的用户更新API
    await api.put(`/users/${currentUser.id}`, {
      realName: profile.realName,
      email: profile.email,
      phone: profile.phone,
      department: profile.department
      // 注意：position 和 bio 字段在用户表中可能不存在，需要根据实际情况调整
    });
  }

  // 修改密码
  async changePassword(passwords: {
    currentPassword: string;
    newPassword: string;
  }): Promise<void> {
    // 首先获取当前用户ID
    const currentUser = await this.getProfile();

    // 使用现有的用户更新API，只更新密码
    await api.put(`/users/${currentUser.id}`, {
      password: passwords.newPassword
      // 注意：这里简化了密码验证，实际应用中可能需要验证当前密码
    });
  }

  // 更新偏好设置
  async updatePreferences(preferences: UserProfile['preferences']): Promise<void> {
    await api.put('/profile/preferences', preferences);
  }

  // 上传头像
  async uploadAvatar(file: File): Promise<{ avatarUrl: string }> {
    const formData = new FormData();
    formData.append('avatar', file);
    
    const response = await api.post('/profile/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data.data;
  }

  // 获取活动日志
  async getActivityLog(params: ActivityLogParams = {}): Promise<ActivityLogResponse> {
    const response = await api.get('/profile/activity', { params });
    return response.data.data;
  }

  // 获取登录会话
  async getLoginSessions(): Promise<UserProfile['security']['loginSessions']> {
    const response = await api.get('/profile/sessions');
    return response.data.data;
  }

  // 终止登录会话
  async terminateSession(sessionId: string): Promise<void> {
    await api.delete(`/profile/sessions/${sessionId}`);
  }

  // 启用/禁用双因子认证
  async toggleTwoFactor(enabled: boolean): Promise<void> {
    await api.post('/profile/two-factor', { enabled });
  }
}

export const profileService = new ProfileService();
export default profileService;
