import { request } from './api';
import { QueryParams } from '@/types';
import { message } from 'antd';
import fileDownload from 'js-file-download';

// 导出格式类型
export type ExportFormat = 'excel' | 'csv' | 'json';

// 导出参数接口
export interface ExportParams extends QueryParams {
  format?: ExportFormat;
}

// 导出API类
export class ExportApi {
  // 导出用户列表
  static async exportUsers(params: ExportParams = {}): Promise<void> {
    try {
      const response = await request.get('/export/users', {
        params,
        responseType: 'blob'
      });
      
      const filename = this.getFilenameFromResponse(response) || `用户列表.${params.format || 'xlsx'}`;
      fileDownload(response.data, filename);
      
      message.success('用户列表导出成功');
    } catch (error) {
      console.error('导出用户列表失败:', error);
      message.error('导出用户列表失败');
      throw error;
    }
  }

  // 导出客户列表
  static async exportCustomers(params: ExportParams = {}): Promise<void> {
    try {
      const response = await request.get('/export/customers', {
        params,
        responseType: 'blob'
      });
      
      const filename = this.getFilenameFromResponse(response) || `客户列表.${params.format || 'xlsx'}`;
      fileDownload(response.data, filename);
      
      message.success('客户列表导出成功');
    } catch (error) {
      console.error('导出客户列表失败:', error);
      message.error('导出客户列表失败');
      throw error;
    }
  }

  // 导出项目列表
  static async exportProjects(params: ExportParams = {}): Promise<void> {
    try {
      const response = await request.get('/export/projects', {
        params,
        responseType: 'blob'
      });
      
      const filename = this.getFilenameFromResponse(response) || `项目列表.${params.format || 'xlsx'}`;
      fileDownload(response.data, filename);
      
      message.success('项目列表导出成功');
    } catch (error) {
      console.error('导出项目列表失败:', error);
      message.error('导出项目列表失败');
      throw error;
    }
  }

  // 导出网站列表
  static async exportWebsites(params: ExportParams = {}): Promise<void> {
    try {
      const response = await request.get('/export/websites', {
        params,
        responseType: 'blob'
      });
      
      const filename = this.getFilenameFromResponse(response) || `网站列表.${params.format || 'xlsx'}`;
      fileDownload(response.data, filename);
      
      message.success('网站列表导出成功');
    } catch (error) {
      console.error('导出网站列表失败:', error);
      message.error('导出网站列表失败');
      throw error;
    }
  }

  // 导出服务器列表
  static async exportServers(params: ExportParams = {}): Promise<void> {
    try {
      const response = await request.get('/export/servers', {
        params,
        responseType: 'blob'
      });
      
      const filename = this.getFilenameFromResponse(response) || `服务器列表.${params.format || 'xlsx'}`;
      fileDownload(response.data, filename);
      
      message.success('服务器列表导出成功');
    } catch (error) {
      console.error('导出服务器列表失败:', error);
      message.error('导出服务器列表失败');
      throw error;
    }
  }

  // 导出域名列表
  static async exportDomains(params: ExportParams = {}): Promise<void> {
    try {
      const response = await request.get('/export/domains', {
        params,
        responseType: 'blob'
      });
      
      const filename = this.getFilenameFromResponse(response) || `域名列表.${params.format || 'xlsx'}`;
      fileDownload(response.data, filename);
      
      message.success('域名列表导出成功');
    } catch (error) {
      console.error('导出域名列表失败:', error);
      message.error('导出域名列表失败');
      throw error;
    }
  }

  // 导出全部数据
  static async exportAll(params: {
    format?: 'excel';
    includeUsers?: boolean;
    includeCustomers?: boolean;
    includeProjects?: boolean;
    includeWebsites?: boolean;
    includeServers?: boolean;
    includeDomains?: boolean;
  } = {}): Promise<void> {
    try {
      const response = await request.get('/export/all', {
        params,
        responseType: 'blob'
      });
      
      const filename = this.getFilenameFromResponse(response) || `全部数据.xlsx`;
      fileDownload(response.data, filename);
      
      message.success('全部数据导出成功');
    } catch (error) {
      console.error('导出全部数据失败:', error);
      message.error('导出全部数据失败');
      throw error;
    }
  }

  // 从响应头中获取文件名
  private static getFilenameFromResponse(response: any): string | null {
    const contentDisposition = response.headers['content-disposition'];
    if (!contentDisposition) return null;
    
    const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
    if (!filenameMatch) return null;
    
    let filename = filenameMatch[1];
    if (filename.charAt(0) === '"' && filename.charAt(filename.length - 1) === '"') {
      filename = filename.slice(1, -1);
    }
    
    return decodeURIComponent(filename);
  }
}

// 导出格式选项
export const exportFormatOptions = [
  { label: 'Excel文件 (.xlsx)', value: 'excel' },
  { label: 'CSV文件 (.csv)', value: 'csv' },
  { label: 'JSON文件 (.json)', value: 'json' }
];

// 导出工具函数
export const exportUtils = {
  // 获取格式图标
  getFormatIcon: (format: ExportFormat): string => {
    const iconMap = {
      excel: '📊',
      csv: '📋',
      json: '📄'
    };
    return iconMap[format] || '📄';
  },

  // 获取格式描述
  getFormatDescription: (format: ExportFormat): string => {
    const descMap = {
      excel: 'Excel格式，支持多工作表和样式',
      csv: 'CSV格式，通用的表格数据格式',
      json: 'JSON格式，适合程序处理'
    };
    return descMap[format] || '';
  },

  // 验证导出参数
  validateExportParams: (params: ExportParams): string[] => {
    const errors: string[] = [];
    
    if (params.format && !['excel', 'csv', 'json'].includes(params.format)) {
      errors.push('不支持的导出格式');
    }
    
    if (params.pageSize && (params.pageSize < 1 || params.pageSize > 10000)) {
      errors.push('导出数量必须在1-10000之间');
    }
    
    return errors;
  },

  // 构建导出参数
  buildExportParams: (queryParams: QueryParams, format: ExportFormat = 'excel'): ExportParams => {
    return {
      ...queryParams,
      format,
      // 导出时移除分页限制，获取所有数据
      page: undefined,
      pageSize: undefined
    };
  }
};

export default ExportApi;
