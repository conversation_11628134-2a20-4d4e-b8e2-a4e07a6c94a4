import { request } from './api';
import { 
  UpdateProjectOnlineStatusRequest, 
  BatchUpdateWebsiteOnlineDateRequest,
  Website 
} from '../types';

// 项目相关API接口
export class ProjectApi {
  // 更新项目上线状态（自动联动网站上线时间）
  static async updateOnlineStatus(id: number, data: UpdateProjectOnlineStatusRequest) {
    const result = await request.put(`/projects/${id}/online-status`, data);
    // 清除相关缓存
    request.clearCache();
    return result;
  }

  // 获取项目关联的网站列表
  static async getProjectWebsites(id: number) {
    return request.getCached(`/projects/${id}/websites`, {
      cacheTime: 5 * 60 * 1000 // 缓存5分钟
    });
  }

  // 批量更新网站上线时间
  static async batchUpdateWebsiteOnlineDate(data: BatchUpdateWebsiteOnlineDateRequest) {
    const result = await request.post('/projects/batch-update-website-online-date', data);
    // 清除相关缓存
    request.clearCache();
    return result;
  }

  // 获取项目上线统计
  static async getOnlineStats() {
    return request.getCached('/projects/online-stats', {
      cacheTime: 10 * 60 * 1000 // 缓存10分钟
    });
  }
}

export default ProjectApi;
