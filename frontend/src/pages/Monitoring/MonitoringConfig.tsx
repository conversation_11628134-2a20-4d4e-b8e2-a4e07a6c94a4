import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Switch,
  Button,
  Space,
  message,
  Tabs,
  InputNumber,
  Select,
  Divider,
  Alert,
  Badge,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  SettingOutlined,
  MailOutlined,
  WechatOutlined,
  DingdingOutlined,
  ApiOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  TestOutlined,
  MonitorOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;
const { TextArea } = Input;

interface MonitoringConfigProps {}

const MonitoringConfig: React.FC<MonitoringConfigProps> = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [monitorStatus, setMonitorStatus] = useState<any>(null);
  const [stats, setStats] = useState<any>(null);

  // 获取监控状态
  const fetchMonitorStatus = async () => {
    try {
      const response = await fetch('http://localhost:3002/status');
      if (response.ok) {
        const data = await response.json();
        setMonitorStatus(data.data);
      }
    } catch (error) {
      console.error('获取监控状态失败:', error);
    }
  };

  // 获取监控统计
  const fetchStats = async () => {
    try {
      const response = await fetch('http://localhost:3002/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data.data);
      }
    } catch (error) {
      console.error('获取监控统计失败:', error);
    }
  };

  // 触发检测
  const triggerCheck = async () => {
    try {
      setTestLoading(true);
      const response = await fetch('http://localhost:3002/trigger-check', {
        method: 'POST'
      });
      
      if (response.ok) {
        message.success('检测已触发');
        setTimeout(fetchStats, 2000); // 2秒后刷新统计
      } else {
        message.error('触发检测失败');
      }
    } catch (error) {
      message.error('触发检测失败');
    } finally {
      setTestLoading(false);
    }
  };

  // 测试通知
  const testNotification = async (type: string = 'all') => {
    try {
      setTestLoading(true);
      const response = await fetch('http://localhost:3002/test-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ type })
      });
      
      if (response.ok) {
        message.success('测试通知已发送');
      } else {
        message.error('测试通知发送失败');
      }
    } catch (error) {
      message.error('测试通知发送失败');
    } finally {
      setTestLoading(false);
    }
  };

  useEffect(() => {
    fetchMonitorStatus();
    fetchStats();
    
    // 定时刷新状态
    const interval = setInterval(() => {
      fetchMonitorStatus();
      fetchStats();
    }, 30000); // 30秒刷新一次
    
    return () => clearInterval(interval);
  }, []);

  return (
    <div style={{ padding: 24 }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: 24 }}>
        <h1 style={{ fontSize: 28, fontWeight: 600, color: '#1f2937', margin: 0, marginBottom: 8 }}>
          <MonitorOutlined style={{ marginRight: 12 }} />
          站点监控配置
        </h1>
        <p style={{ color: '#6b7280', fontSize: 16, margin: 0 }}>
          配置站点存活检测和故障通知系统
        </p>
      </div>

      {/* 监控状态概览 */}
      <Card 
        title="监控服务状态" 
        style={{ marginBottom: 24 }}
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={() => {
                fetchMonitorStatus();
                fetchStats();
              }}
            >
              刷新
            </Button>
            <Button 
              type="primary"
              icon={<TestOutlined />}
              loading={testLoading}
              onClick={triggerCheck}
            >
              手动检测
            </Button>
          </Space>
        }
      >
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic
              title="服务状态"
              value={monitorStatus?.isRunning ? '运行中' : '已停止'}
              valueStyle={{ 
                color: monitorStatus?.isRunning ? '#52c41a' : '#ff4d4f' 
              }}
              prefix={
                monitorStatus?.isRunning ? 
                <PlayCircleOutlined style={{ color: '#52c41a' }} /> : 
                <PauseCircleOutlined style={{ color: '#ff4d4f' }} />
              }
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="运行时间"
              value={monitorStatus?.uptime ? Math.floor(monitorStatus.uptime / 3600) : 0}
              suffix="小时"
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="总检测次数"
              value={monitorStatus?.stats?.totalChecks || 0}
              valueStyle={{ color: '#722ed1' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="成功率"
              value={
                monitorStatus?.stats?.totalChecks > 0 
                  ? ((monitorStatus.stats.successfulChecks / monitorStatus.stats.totalChecks) * 100).toFixed(1)
                  : 0
              }
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
        </Row>

        {stats?.today && (
          <div style={{ marginTop: 16 }}>
            <Divider orientation="left">今日统计</Divider>
            <Row gutter={[16, 16]}>
              <Col span={6}>
                <Statistic
                  title="今日检测"
                  value={stats.today.total_checks || 0}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="成功次数"
                  value={stats.today.successful_checks || 0}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="失败次数"
                  value={stats.today.failed_checks || 0}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="平均响应时间"
                  value={stats.today.avg_response_time || 0}
                  suffix="ms"
                  precision={0}
                />
              </Col>
            </Row>
          </div>
        )}
      </Card>

      {/* 配置选项卡 */}
      <Card>
        <Tabs defaultActiveKey="basic" type="card">
          {/* 基础配置 */}
          <TabPane tab={<span><SettingOutlined />基础配置</span>} key="basic">
            <Alert
              message="监控配置说明"
              description="修改配置后需要重启监控服务才能生效。建议在低峰期进行配置修改。"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />
            
            <Form
              form={form}
              layout="vertical"
              onFinish={(values) => {
                console.log('保存配置:', values);
                message.success('配置已保存');
              }}
            >
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item
                    label="检测间隔"
                    name="checkInterval"
                    initialValue={5}
                    tooltip="站点检测的时间间隔，单位：分钟"
                  >
                    <InputNumber
                      min={1}
                      max={60}
                      addonAfter="分钟"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="请求超时"
                    name="timeout"
                    initialValue={15}
                    tooltip="HTTP请求的超时时间，单位：秒"
                  >
                    <InputNumber
                      min={5}
                      max={60}
                      addonAfter="秒"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="故障阈值"
                    name="failureThreshold"
                    initialValue={3}
                    tooltip="连续失败多少次后认为站点故障"
                  >
                    <InputNumber
                      min={1}
                      max={10}
                      addonAfter="次"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="并发数量"
                    name="concurrency"
                    initialValue={10}
                    tooltip="同时检测的站点数量"
                  >
                    <InputNumber
                      min={1}
                      max={50}
                      addonAfter="个"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </TabPane>

          {/* 邮件通知 */}
          <TabPane tab={<span><MailOutlined />邮件通知</span>} key="email">
            <Form layout="vertical">
              <Form.Item label="启用邮件通知" name="emailEnabled" valuePropName="checked">
                <Switch />
              </Form.Item>
              
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item label="SMTP服务器" name="emailHost">
                    <Input placeholder="smtp.qq.com" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="端口" name="emailPort">
                    <InputNumber placeholder={587} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="用户名" name="emailUser">
                    <Input placeholder="<EMAIL>" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="密码" name="emailPass">
                    <Input.Password placeholder="邮箱密码或授权码" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item label="收件人" name="emailTo">
                <TextArea 
                  placeholder="多个邮箱用逗号分隔，如：<EMAIL>,<EMAIL>"
                  rows={3}
                />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button type="primary">保存配置</Button>
                  <Button 
                    loading={testLoading}
                    onClick={() => testNotification('email')}
                  >
                    测试邮件
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </TabPane>

          {/* 微信通知 */}
          <TabPane tab={<span><WechatOutlined />微信通知</span>} key="wechat">
            <Form layout="vertical">
              <Form.Item label="启用微信通知" name="wechatEnabled" valuePropName="checked">
                <Switch />
              </Form.Item>
              
              <Form.Item 
                label="Webhook URL" 
                name="wechatWebhook"
                tooltip="企业微信机器人的Webhook地址"
              >
                <Input placeholder="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key" />
              </Form.Item>
              
              <Form.Item 
                label="@成员列表" 
                name="wechatMentioned"
                tooltip="需要@的成员，多个用逗号分隔"
              >
                <TextArea 
                  placeholder="@all 或具体成员，如：@zhangsan,@lisi"
                  rows={2}
                />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button type="primary">保存配置</Button>
                  <Button 
                    loading={testLoading}
                    onClick={() => testNotification('wechat')}
                  >
                    测试微信
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </TabPane>

          {/* 钉钉通知 */}
          <TabPane tab={<span><DingdingOutlined />钉钉通知</span>} key="dingtalk">
            <Form layout="vertical">
              <Form.Item label="启用钉钉通知" name="dingtalkEnabled" valuePropName="checked">
                <Switch />
              </Form.Item>
              
              <Form.Item 
                label="Webhook URL" 
                name="dingtalkWebhook"
                tooltip="钉钉机器人的Webhook地址"
              >
                <Input placeholder="https://oapi.dingtalk.com/robot/send?access_token=your-token" />
              </Form.Item>
              
              <Form.Item 
                label="签名密钥" 
                name="dingtalkSecret"
                tooltip="钉钉机器人的签名密钥（可选）"
              >
                <Input.Password placeholder="签名密钥" />
              </Form.Item>
              
              <Form.Item 
                label="@手机号" 
                name="dingtalkMobiles"
                tooltip="需要@的手机号，多个用逗号分隔"
              >
                <TextArea 
                  placeholder="如：13800138000,13900139000"
                  rows={2}
                />
              </Form.Item>
              
              <Form.Item label="@所有人" name="dingtalkAtAll" valuePropName="checked">
                <Switch />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button type="primary">保存配置</Button>
                  <Button 
                    loading={testLoading}
                    onClick={() => testNotification('dingtalk')}
                  >
                    测试钉钉
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </TabPane>

          {/* Webhook通知 */}
          <TabPane tab={<span><ApiOutlined />Webhook</span>} key="webhook">
            <Form layout="vertical">
              <Form.Item label="启用Webhook通知" name="webhookEnabled" valuePropName="checked">
                <Switch />
              </Form.Item>
              
              <Form.Item 
                label="Webhook URLs" 
                name="webhookUrls"
                tooltip="多个URL用换行分隔"
              >
                <TextArea 
                  placeholder="https://your-webhook-url.com/notify"
                  rows={4}
                />
              </Form.Item>
              
              <Form.Item 
                label="自定义Headers" 
                name="webhookHeaders"
                tooltip="JSON格式的自定义请求头"
              >
                <TextArea 
                  placeholder='{"Authorization": "Bearer your-token"}'
                  rows={3}
                />
              </Form.Item>
              
              <Form.Item>
                <Space>
                  <Button type="primary">保存配置</Button>
                  <Button 
                    loading={testLoading}
                    onClick={() => testNotification('webhook')}
                  >
                    测试Webhook
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default MonitoringConfig;
