import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Alert,
  Badge,
  Button,
  Space,
  Progress,
  Modal,
  message,
  Tabs
} from 'antd';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer
} from 'recharts';
import {
  DashboardOutlined,
  SecurityScanOutlined,
  ThunderboltOutlined,
  DatabaseOutlined,
  AlertOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { usePermissions } from '../../hooks/usePermissions';

const { TabPane } = Tabs;

interface MonitoringData {
  overview: {
    status: string;
    uptime: number;
    timestamp: string;
  };
  performance: {
    totalChecks: number;
    successRate: string;
    averageResponseTime: string;
    maxResponseTime: number;
    minResponseTime: number;
    recentChecksCount: number;
  };
  security: {
    deniedAccess: number;
    suspiciousActivity: number;
    failedLogins: number;
    privilegeEscalation: number;
    suspiciousScore: number;
  };
  cache: {
    hits: number;
    misses: number;
    hitRate: number;
    size: number;
  };
  alertsCount: number;
}

interface AlertItem {
  id: string;
  type: string;
  level: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: string;
  resolved: boolean;
  data?: any;
}

const PermissionMonitoringDashboard: React.FC = () => {
  const { hasPermission } = usePermissions();
  const [monitoringData, setMonitoringData] = useState<MonitoringData | null>(null);
  const [alerts, setAlerts] = useState<AlertItem[]>([]);
  const [performanceHistory, setPerformanceHistory] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [realTimeEnabled, setRealTimeEnabled] = useState(false);
  const eventSourceRef = useRef<EventSource | null>(null);

  // 检查权限
  const canRead = hasPermission('system.monitoring.read');
  const canManage = hasPermission('system.monitoring.manage');

  useEffect(() => {
    if (!canRead) {
      message.error('您没有权限访问监控数据');
      return;
    }

    loadMonitoringData();
    loadAlerts();
    loadPerformanceHistory();

    // 设置定时刷新
    const interval = setInterval(() => {
      if (!realTimeEnabled) {
        loadMonitoringData();
      }
    }, 30000);

    return () => {
      clearInterval(interval);
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, [canRead, realTimeEnabled]);

  const loadMonitoringData = async () => {
    try {
      const response = await fetch('/api/monitoring/overview', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.success) {
        setMonitoringData(result.data);
      } else {
        message.error('加载监控数据失败');
      }
    } catch (error) {
      console.error('加载监控数据失败:', error);
      message.error('加载监控数据失败');
    } finally {
      setLoading(false);
    }
  };

  const loadAlerts = async () => {
    try {
      const response = await fetch('/api/monitoring/alerts?limit=20', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.success) {
        setAlerts(result.data.alerts);
      }
    } catch (error) {
      console.error('加载告警数据失败:', error);
    }
  };

  const loadPerformanceHistory = async () => {
    try {
      const response = await fetch('/api/monitoring/performance', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.success) {
        setPerformanceHistory(result.data.responseTimeHistory || []);
      }
    } catch (error) {
      console.error('加载性能历史数据失败:', error);
    }
  };

  const toggleRealTime = () => {
    if (realTimeEnabled) {
      // 关闭实时监控
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
      setRealTimeEnabled(false);
      message.info('实时监控已关闭');
    } else {
      // 开启实时监控
      const eventSource = new EventSource('/api/monitoring/realtime');

      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        setMonitoringData(prev => prev ? { ...prev, ...data } : null);
      };

      eventSource.addEventListener('alert', (event) => {
        const alert = JSON.parse(event.data);
        setAlerts(prev => [alert, ...prev.slice(0, 19)]);
        message.warning(`新告警: ${alert.message}`);
      });

      eventSource.onerror = () => {
        message.error('实时监控连接失败');
        setRealTimeEnabled(false);
      };

      eventSourceRef.current = eventSource;
      setRealTimeEnabled(true);
      message.success('实时监控已开启');
    }
  };

  const resolveAlert = async (alertId: string) => {
    if (!canManage) {
      message.error('您没有权限解决告警');
      return;
    }

    try {
      const response = await fetch(`/api/monitoring/alerts/${alertId}/resolve`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const result = await response.json();
      
      if (result.success) {
        setAlerts(prev => prev.map(alert => 
          alert.id === alertId ? { ...alert, resolved: true } : alert
        ));
        message.success('告警已解决');
      } else {
        message.error('解决告警失败');
      }
    } catch (error) {
      console.error('解决告警失败:', error);
      message.error('解决告警失败');
    }
  };

  const resetMetrics = async () => {
    if (!canManage) {
      message.error('您没有权限重置指标');
      return;
    }

    Modal.confirm({
      title: '确认重置',
      content: '确定要重置所有监控指标吗？此操作不可撤销。',
      onOk: async () => {
        try {
          const response = await fetch('/api/monitoring/reset', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });
          const result = await response.json();
          
          if (result.success) {
            message.success('监控指标已重置');
            loadMonitoringData();
            loadPerformanceHistory();
          } else {
            message.error('重置指标失败');
          }
        } catch (error) {
          console.error('重置指标失败:', error);
          message.error('重置指标失败');
        }
      }
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return '#52c41a';
      case 'warning': return '#faad14';
      case 'error': return '#f5222d';
      default: return '#d9d9d9';
    }
  };

  const getAlertColor = (level: string) => {
    switch (level) {
      case 'critical': return 'red';
      case 'error': return 'orange';
      case 'warning': return 'yellow';
      default: return 'blue';
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  };

  const alertColumns = [
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      render: (level: string) => (
        <Badge color={getAlertColor(level)} text={level.toUpperCase()} />
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type'
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message',
      ellipsis: true
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (timestamp: string) => new Date(timestamp).toLocaleString()
    },
    {
      title: '状态',
      dataIndex: 'resolved',
      key: 'resolved',
      render: (resolved: boolean) => (
        <Badge 
          status={resolved ? 'success' : 'error'} 
          text={resolved ? '已解决' : '未解决'} 
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (record: AlertItem) => (
        !record.resolved && canManage ? (
          <Button 
            size="small" 
            type="link" 
            onClick={() => resolveAlert(record.id)}
          >
            解决
          </Button>
        ) : null
      )
    }
  ];

  if (!canRead) {
    return (
      <div style={{ padding: 24 }}>
        <Alert
          message="权限不足"
          description="您没有权限访问权限监控数据"
          type="error"
          showIcon
        />
      </div>
    );
  }

  if (loading) {
    return <div style={{ padding: 24, textAlign: 'center' }}>加载中...</div>;
  }

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h1>
          <DashboardOutlined /> 权限系统监控
        </h1>
        <Space>
          <Button 
            type={realTimeEnabled ? 'primary' : 'default'}
            icon={<ThunderboltOutlined />}
            onClick={toggleRealTime}
          >
            {realTimeEnabled ? '关闭实时监控' : '开启实时监控'}
          </Button>
          <Button icon={<ReloadOutlined />} onClick={loadMonitoringData}>
            刷新
          </Button>
          {canManage && (
            <Button icon={<SettingOutlined />} onClick={resetMetrics}>
              重置指标
            </Button>
          )}
        </Space>
      </div>

      {/* 系统状态概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="系统状态"
              value={monitoringData?.overview.status || 'unknown'}
              valueStyle={{ color: getStatusColor(monitoringData?.overview.status || 'unknown') }}
              prefix={<DashboardOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行时间"
              value={formatUptime(monitoringData?.overview.uptime || 0)}
              prefix={<ThunderboltOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="权限检查总数"
              value={monitoringData?.performance.totalChecks || 0}
              prefix={<SecurityScanOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="未解决告警"
              value={alerts.filter(a => !a.resolved).length}
              valueStyle={{ color: alerts.filter(a => !a.resolved).length > 0 ? '#f5222d' : '#52c41a' }}
              prefix={<AlertOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Tabs defaultActiveKey="performance">
        <TabPane tab="性能监控" key="performance">
          <Row gutter={16}>
            <Col span={12}>
              <Card title="权限检查性能" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic
                      title="成功率"
                      value={monitoringData?.performance.successRate || '0'}
                      suffix="%"
                      valueStyle={{ color: '#52c41a' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="平均响应时间"
                      value={monitoringData?.performance.averageResponseTime || '0'}
                      suffix="ms"
                      valueStyle={{ 
                        color: parseFloat(monitoringData?.performance.averageResponseTime || '0') > 50 ? '#faad14' : '#52c41a' 
                      }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="最大响应时间"
                      value={monitoringData?.performance.maxResponseTime || 0}
                      suffix="ms"
                      valueStyle={{ 
                        color: (monitoringData?.performance.maxResponseTime || 0) > 100 ? '#f5222d' : '#52c41a' 
                      }}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="缓存性能" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="命中率"
                      value={(monitoringData?.cache.hitRate || 0) * 100}
                      suffix="%"
                      precision={1}
                      valueStyle={{ 
                        color: (monitoringData?.cache.hitRate || 0) > 0.8 ? '#52c41a' : '#faad14' 
                      }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="缓存大小"
                      value={monitoringData?.cache.size || 0}
                      prefix={<DatabaseOutlined />}
                    />
                  </Col>
                </Row>
                <div style={{ marginTop: 16 }}>
                  <Progress
                    percent={(monitoringData?.cache.hitRate || 0) * 100}
                    status={(monitoringData?.cache.hitRate || 0) > 0.8 ? 'success' : 'active'}
                    strokeColor={(monitoringData?.cache.hitRate || 0) > 0.8 ? '#52c41a' : '#faad14'}
                  />
                </div>
              </Card>
            </Col>
          </Row>

          {/* 响应时间趋势图 */}
          <Card title="响应时间趋势" style={{ marginBottom: 16 }}>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={performanceHistory.slice(-50)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="timestamp" 
                  tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                />
                <YAxis />
                <RechartsTooltip 
                  labelFormatter={(value) => new Date(value).toLocaleString()}
                  formatter={(value: any) => [`${value}ms`, '响应时间']}
                />
                <Line 
                  type="monotone" 
                  dataKey="time" 
                  stroke="#1890ff" 
                  strokeWidth={2}
                  dot={{ fill: '#1890ff', strokeWidth: 2, r: 3 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </TabPane>

        <TabPane tab="安全监控" key="security">
          <Row gutter={16}>
            <Col span={12}>
              <Card title="安全指标" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Statistic
                      title="拒绝访问次数"
                      value={monitoringData?.security.deniedAccess || 0}
                      valueStyle={{ color: '#f5222d' }}
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic
                      title="登录失败次数"
                      value={monitoringData?.security.failedLogins || 0}
                      valueStyle={{ color: '#faad14' }}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="可疑活动评分" style={{ marginBottom: 16 }}>
                <Progress
                  type="circle"
                  percent={monitoringData?.security.suspiciousScore || 0}
                  status={
                    (monitoringData?.security.suspiciousScore || 0) > 80 ? 'exception' :
                    (monitoringData?.security.suspiciousScore || 0) > 50 ? 'active' : 'success'
                  }
                  strokeColor={
                    (monitoringData?.security.suspiciousScore || 0) > 80 ? '#f5222d' :
                    (monitoringData?.security.suspiciousScore || 0) > 50 ? '#faad14' : '#52c41a'
                  }
                />
                <div style={{ textAlign: 'center', marginTop: 16 }}>
                  <span style={{ 
                    color: (monitoringData?.security.suspiciousScore || 0) > 80 ? '#f5222d' :
                           (monitoringData?.security.suspiciousScore || 0) > 50 ? '#faad14' : '#52c41a'
                  }}>
                    {(monitoringData?.security.suspiciousScore || 0) > 80 ? '高风险' :
                     (monitoringData?.security.suspiciousScore || 0) > 50 ? '中风险' : '低风险'}
                  </span>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="告警管理" key="alerts">
          <Card 
            title="系统告警" 
            extra={
              <Space>
                <Button icon={<ReloadOutlined />} onClick={loadAlerts}>
                  刷新
                </Button>
              </Space>
            }
          >
            <Table
              columns={alertColumns}
              dataSource={alerts}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              size="small"
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default PermissionMonitoringDashboard;