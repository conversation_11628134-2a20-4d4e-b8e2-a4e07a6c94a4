import React, { useState } from 'react';
import { Form, Input, Button, Checkbox, Alert, Space } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '@/store/auth';
import { LoginRequest } from '@/types';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [form] = Form.useForm();
  
  const { login, isLoading, error, clearError } = useAuthStore();
  const [rememberMe, setRememberMe] = useState(false);

  // 处理登录
  const handleLogin = async (values: LoginRequest) => {
    try {
      clearError();
      await login(values);
      
      // 登录成功后跳转
      const from = (location.state as any)?.from || '/dashboard';
      navigate(from, { replace: true });
    } catch (error) {
      // 错误已经在store中处理
      console.error('登录失败:', error);
    }
  };

  // 处理表单提交失败
  const handleLoginFailed = (errorInfo: any) => {
    console.log('表单验证失败:', errorInfo);
  };

  return (
    <div>
      {/* 错误提示 */}
      {error && (
        <Alert
          message="登录失败"
          description={error}
          type="error"
          showIcon
          closable
          onClose={clearError}
          style={{ marginBottom: 24 }}
        />
      )}

      {/* 登录表单 */}
      <Form
        form={form}
        name="login"
        size="large"
        onFinish={handleLogin}
        onFinishFailed={handleLoginFailed}
        autoComplete="off"
        layout="vertical"
      >
        <Form.Item
          name="username"
          rules={[
            { required: true, message: '请输入用户名或邮箱!' },
            { min: 3, message: '用户名至少3个字符!' },
          ]}
        >
          <Input
            prefix={<UserOutlined />}
            placeholder="用户名或邮箱"
            autoComplete="username"
          />
        </Form.Item>

        <Form.Item
          name="password"
          rules={[
            { required: true, message: '请输入密码!' },
            { min: 6, message: '密码至少6个字符!' },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="密码"
            autoComplete="current-password"
          />
        </Form.Item>

        <Form.Item>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Checkbox
              checked={rememberMe}
              onChange={(e) => setRememberMe(e.target.checked)}
            >
              记住我
            </Checkbox>
            <Button type="link" style={{ padding: 0 }}>
              忘记密码?
            </Button>
          </Space>
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={isLoading}
            block
            style={{ height: 48 }}
          >
            登录
          </Button>
        </Form.Item>
      </Form>

      {/* 演示账号信息 */}
      <div style={{
        marginTop: 24,
        padding: 16,
        background: '#f5f5f5',
        borderRadius: 8,
        fontSize: 12,
      }}>
        <div style={{ marginBottom: 8, fontWeight: 'bold' }}>演示账号:</div>
        <div>管理员: admin / admin123</div>
        <div>普通用户: user / user123</div>
      </div>
    </div>
  );
};

export default Login;
