import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Tooltip,
  message,
  Row,
  Col,
  Statistic,
  Badge,
  Input,
  Popconfirm,
  Modal
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  DesktopOutlined,
  MonitorOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  DatabaseOutlined,
  CloudOutlined,
  ThunderboltOutlined,
  SafetyOutlined,
  CalendarOutlined,
  DollarOutlined,
  SearchOutlined,
  DownloadOutlined,
  UploadOutlined,
  ImportOutlined,
  KeyOutlined,
  CodeOutlined
} from '@ant-design/icons';
import { Server, ServerLoadInfo, QueryParams } from '../../types';
import { ServerApi } from '../../services/server';
import { request } from '../../services/api';
import ServerForm from '../../components/Server/ServerForm';
import ServerDetail from '../../components/Server/ServerDetail';
import ServerMonitor from '../../components/Server/ServerMonitor';
import BatchSshConfig from '../../components/Server/BatchSshConfig';
import BatchExpireDateModal from '../../components/Server/BatchExpireDateModal';
import BatchTerminalManager from '../../components/Server/BatchTerminalManager';

interface SimpleServerManagementProps {}

const SimpleServerManagement: React.FC<SimpleServerManagementProps> = () => {
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(false);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10
  });

  // 表单相关状态
  const [formVisible, setFormVisible] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [currentServer, setCurrentServer] = useState<Server | undefined>();

  // 详情和监控相关状态
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailServer, setDetailServer] = useState<Server | null>(null);
  const [monitorVisible, setMonitorVisible] = useState(false);
  const [monitorServer, setMonitorServer] = useState<Server | null>(null);

  // 搜索相关状态
  const [searchText, setSearchText] = useState('');
  const [filteredServers, setFilteredServers] = useState<Server[]>([]);

  // 批量操作相关状态
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedServers, setSelectedServers] = useState<Server[]>([]);
  const [batchExpireDateVisible, setBatchExpireDateVisible] = useState(false);
  const [batchSshConfigVisible, setBatchSshConfigVisible] = useState(false);
  const [batchTerminalVisible, setBatchTerminalVisible] = useState(false);

  // 状态配置
  const statusConfig = {
    active: { label: '正常', color: 'green', icon: <CheckCircleOutlined /> },
    inactive: { label: '停用', color: 'red', icon: <ExclamationCircleOutlined /> },
    maintenance: { label: '维护中', color: 'orange', icon: <SettingOutlined /> },
    expired: { label: '已过期', color: 'red', icon: <ExclamationCircleOutlined /> }
  };

  // 获取服务器数据
  const fetchServers = async () => {
    setLoading(true);
    try {
      const result = await request.get(`/servers?page=${queryParams.page}&limit=${queryParams.pageSize}`);

      if (result.success) {
        setServers(result.data.servers || []);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('获取服务器数据失败:', error);
      message.error('获取服务器数据失败');

      // 如果API失败，显示错误信息而不是使用模拟数据
      setServers([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServers();
  }, [queryParams]);

  // 当服务器列表更新时，重新应用搜索过滤
  useEffect(() => {
    if (searchText.trim()) {
      handleSearch(searchText);
    } else {
      setFilteredServers(servers);
    }
  }, [servers, searchText]);

  // 搜索功能
  const handleSearch = (value: string) => {
    setSearchText(value);
    if (!value.trim()) {
      setFilteredServers(servers);
      return;
    }

    const searchLower = value.toLowerCase();
    const filtered = servers.filter(server => {
      return (
        (server.name && server.name.toLowerCase().includes(searchLower)) ||
        (server.ipAddress && server.ipAddress.toLowerCase().includes(searchLower)) ||
        (server.location && server.location.toLowerCase().includes(searchLower)) ||
        (server.provider && server.provider.toLowerCase().includes(searchLower)) ||
        (server.department && server.department.toLowerCase().includes(searchLower)) ||
        (server.notes && server.notes.toLowerCase().includes(searchLower)) ||
        (server.status && server.status.toLowerCase().includes(searchLower))
      );
    });
    setFilteredServers(filtered);
  };

  // 处理新建服务器
  const handleCreate = () => {
    setFormMode('create');
    setCurrentServer(undefined);
    setFormVisible(true);
  };

  // 处理编辑服务器
  const handleEdit = (server: Server) => {
    setFormMode('edit');
    setCurrentServer(server);
    setFormVisible(true);
  };

  // 处理删除服务器
  const handleDelete = async (server: Server) => {
    try {
      await ServerApi.deleteServer(server.id);
      setServers(prev => prev.filter(s => s.id !== server.id));
      message.success('服务器删除成功！');
    } catch (error) {
      console.error('删除服务器失败:', error);
      message.error('删除服务器失败');
    }
  };

  // 查看详情
  const handleViewDetail = (server: Server) => {
    setDetailServer(server);
    setDetailVisible(true);
  };

  // 查看监控
  const handleViewMonitor = (server: Server) => {
    setMonitorServer(server);
    setMonitorVisible(true);
  };

  // 处理表单成功
  const handleFormSuccess = (data: any) => {
    if (formMode === 'create') {
      const newServer = {
        ...data,
        id: Date.now(), // 简单的ID生成
        createdAt: new Date().toISOString().split('T')[0],
        updatedAt: new Date().toISOString().split('T')[0]
      };
      setServers(prev => [...prev, newServer]);
    } else {
      setServers(prev => prev.map(s =>
        s.id === currentServer?.id ? { ...s, ...data, updatedAt: new Date().toISOString().split('T')[0] } : s
      ));
    }
    setFormVisible(false);
    fetchServers(); // 重新获取数据
  };

  // 处理表单取消
  const handleFormCancel = () => {
    setFormVisible(false);
  };

  // 处理行选择
  const handleRowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[], newSelectedRows: Server[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
      setSelectedServers(newSelectedRows);
    },
    onSelectAll: (selected: boolean, selectedRows: Server[], changeRows: Server[]) => {
      if (selected) {
        const allKeys = (searchText ? filteredServers : servers).map(server => server.id);
        setSelectedRowKeys(allKeys);
        setSelectedServers(searchText ? filteredServers : servers);
      } else {
        setSelectedRowKeys([]);
        setSelectedServers([]);
      }
    },
  };

  // 清空选择
  const handleClearSelection = () => {
    setSelectedRowKeys([]);
    setSelectedServers([]);
  };

  // 批量操作成功后的回调
  const handleBatchSuccess = () => {
    fetchServers();
    handleClearSelection();
  };

  // 批量删除服务器
  const handleBatchDelete = () => {
    if (selectedServers.length === 0) {
      message.warning('请先选择要删除的服务器');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedServers.length} 台服务器吗？此操作不可恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 批量删除API调用
          const ids = selectedServers.map(server => server.id);
          const result = await ServerApi.batchDeleteServers(ids);

          if (result.success) {
            message.success(result.data?.message || `成功删除 ${selectedServers.length} 台服务器`);
            handleBatchSuccess();
          } else {
            message.error('批量删除失败');
          }
        } catch (error) {
          console.error('批量删除失败:', error);
          message.error('批量删除失败');
        }
      }
    });
  };

  // 批量修改到期日期
  const handleBatchUpdateExpireDate = async (expireDate: string) => {
    if (selectedServers.length === 0) {
      message.warning('请先选择要修改的服务器');
      return;
    }

    try {
      // 批量更新到期日期API调用
      const ids = selectedServers.map(server => server.id);
      const result = await ServerApi.batchUpdateExpireDate(ids, expireDate);

      if (result.success) {
        message.success(result.data?.message || `成功修改 ${selectedServers.length} 台服务器的到期日期`);
        setBatchExpireDateVisible(false);
        handleBatchSuccess();
      } else {
        message.error('批量修改到期日期失败');
      }
    } catch (error) {
      console.error('批量修改到期日期失败:', error);
      message.error('批量修改到期日期失败');
    }
  };

  // 批量刷新配置信息
  const handleBatchRefreshConfig = async () => {
    if (selectedServers.length === 0) {
      message.warning('请先选择要刷新配置的服务器');
      return;
    }

    Modal.confirm({
      title: '确认批量刷新配置',
      content: (
        <div>
          <p>确定要刷新选中的 <strong>{selectedServers.length}</strong> 台服务器的配置信息吗？</p>
          <p>此操作将通过SSH连接获取最新的系统信息。</p>
          <p style={{ color: '#1890ff', marginTop: 12 }}>
            ℹ️ 确认后将在后台执行，您可以继续其他操作
          </p>
        </div>
      ),
      okText: '确认刷新',
      cancelText: '取消',
      onOk: async () => {
        // 立即显示开始消息并关闭确认窗口
        const serverNames = selectedServers.map(s => s.name).join('、');
        message.loading({
          content: `正在后台刷新 ${selectedServers.length} 台服务器配置信息...`,
          key: 'batch-refresh',
          duration: 3
        });

        // 启动后台任务
        startBatchRefreshTask();
      }
    });
  };

  // 启动后台批量刷新任务
  const startBatchRefreshTask = async () => {
    const serverIds = selectedServers.map(server => server.id);
    const serverNames = selectedServers.map(s => s.name);
    const totalCount = selectedServers.length;

    // 显示进度通知
    const showProgressNotification = () => {
      message.info({
        content: (
          <div>
            <div>🔄 正在后台刷新服务器配置...</div>
            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
              共 {totalCount} 台服务器：{serverNames.slice(0, 3).join('、')}
              {serverNames.length > 3 && ` 等${serverNames.length}台`}
            </div>
          </div>
        ),
        key: 'batch-refresh-progress',
        duration: 8
      });
    };

    showProgressNotification();

    try {
      // 发起后台任务
      const result = await request.post('/servers/batch-refresh-config', { serverIds });

      if (result.success) {
        // 显示完成消息
        message.success({
          content: (
            <div>
              <div>✅ 批量刷新配置完成！</div>
              <div style={{ fontSize: '12px', marginTop: 4 }}>
                成功: {result.data.success} 台，失败: {result.data.failed} 台
              </div>
            </div>
          ),
          key: 'batch-refresh-progress',
          duration: 6
        });

        // 如果有失败的，显示详细结果
        if (result.data.failed > 0) {
          setTimeout(() => {
            const failedItems = result.data.results.filter((r: any) => !r.success);

            Modal.info({
              title: '批量刷新结果详情',
              width: 700,
              content: (
                <div>
                  <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
                    <div>✅ <strong>成功刷新: {result.data.success} 台</strong></div>
                  </div>
                  <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#fff2f0', border: '1px solid #ffccc7', borderRadius: 6 }}>
                    <div>❌ <strong>刷新失败: {result.data.failed} 台</strong></div>
                  </div>
                  {failedItems.length > 0 && (
                    <div>
                      <p style={{ marginBottom: 12, fontWeight: 'bold' }}>失败详情:</p>
                      <div style={{ maxHeight: 300, overflow: 'auto' }}>
                        {failedItems.map((item: any, index: number) => (
                          <div key={index} style={{
                            marginBottom: 12,
                            padding: 12,
                            backgroundColor: '#fafafa',
                            border: '1px solid #f0f0f0',
                            borderRadius: 6,
                            borderLeft: '4px solid #ff4d4f'
                          }}>
                            <div style={{ fontWeight: 'bold', marginBottom: 4 }}>{item.serverName}</div>
                            <div style={{ color: '#ff4d4f', fontSize: '13px', lineHeight: '1.4' }}>
                              {item.error}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ),
            });
          }, 1500);
        }

        // 刷新服务器列表
        setTimeout(() => {
          handleBatchSuccess();
        }, 2000);
      } else {
        message.error({
          content: (
            <div>
              <div>❌ 批量刷新配置失败</div>
              <div style={{ fontSize: '12px', marginTop: 4 }}>
                {result.message || '请检查网络连接和服务器配置'}
              </div>
            </div>
          ),
          key: 'batch-refresh-progress',
          duration: 8
        });
      }
    } catch (error) {
      console.error('批量刷新配置失败:', error);
      message.error({
        content: (
          <div>
            <div>❌ 批量刷新配置失败</div>
            <div style={{ fontSize: '12px', marginTop: 4 }}>
              网络连接错误，请检查后端服务是否正常
            </div>
          </div>
        ),
        key: 'batch-refresh-progress',
        duration: 8
      });
    }
  };

  // 导出服务器列表
  const handleExport = async () => {
    try {
      // 使用带认证的fetch请求
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:3001/api/v1/servers/export', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('导出失败');
      }

      // 获取文件名
      const contentDisposition = response.headers.get('Content-Disposition');
      const filename = contentDisposition
        ? decodeURIComponent(contentDisposition.split('filename="')[1].split('"')[0])
        : `服务器列表_${new Date().toISOString().slice(0, 10)}.xlsx`;

      // 下载文件
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      message.success('服务器列表导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    }
  };

  // 导入服务器列表
  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      try {
        // 动态导入xlsx库
        const XLSX = await import('xlsx');

        const reader = new FileReader();
        reader.onload = async (event) => {
          try {
            const data = new Uint8Array(event.target?.result as ArrayBuffer);
            const workbook = XLSX.read(data, { type: 'array' });
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            // 转换数据格式，过滤掉IP地址为空的行
            const serversData = jsonData
              .filter((row: any) => {
                // 必须有IP地址才能处理
                const ipAddress = row['IP地址'] || row['ipAddress'];
                return ipAddress !== undefined && ipAddress !== null && ipAddress !== '';
              })
              .map((row: any) => {
                const serverData: any = {};

                // 只有当字段值不为空时才添加到数据中
                const addFieldIfNotEmpty = (field: string, value: any) => {
                  if (value !== undefined && value !== null && value !== '') {
                    serverData[field] = value;
                  }
                };

                // IP地址是必需的，用于判断是更新还是新增
                const ipAddress = row['IP地址'] || row['ipAddress'];
                serverData.ipAddress = ipAddress;

                // 基本信息字段
                addFieldIfNotEmpty('name', row['服务器名称'] || row['name']);
                addFieldIfNotEmpty('ipAddress', row['IP地址'] || row['ipAddress']);
                addFieldIfNotEmpty('location', row['机房位置'] || row['location']);
                addFieldIfNotEmpty('provider', row['服务商'] || row['provider']);
                addFieldIfNotEmpty('department', row['所属部门'] || row['department']);

                // 新增字段
                addFieldIfNotEmpty('instanceId', row['实例ID'] || row['instanceId']);
                addFieldIfNotEmpty('type', row['服务器类型'] || row['type']);
                addFieldIfNotEmpty('region', row['地区/国家'] || row['region']);
                addFieldIfNotEmpty('uptime', row['启用时间(秒)'] || row['uptime']);

                // 规格配置
                addFieldIfNotEmpty('cpu', row['CPU配置'] || row['cpu']);
                addFieldIfNotEmpty('memory', row['内存配置'] || row['memory']);
                addFieldIfNotEmpty('storage', row['存储配置'] || row['storage']);
                addFieldIfNotEmpty('bandwidth', row['带宽配置'] || row['bandwidth']);
                addFieldIfNotEmpty('os', row['操作系统'] || row['os']);

                // 其他字段（排除SSH配置，保护现有SSH设置）
                addFieldIfNotEmpty('expireDate', row['到期时间'] || row['expireDate']);
                addFieldIfNotEmpty('renewalFee', row['续费金额'] || row['renewalFee']);
                addFieldIfNotEmpty('status', row['状态'] || row['status']);
                addFieldIfNotEmpty('notes', row['备注'] || row['notes']);

                // SSH配置字段在Excel导入时不处理，保护现有配置
                // addFieldIfNotEmpty('sshPort', row['SSH端口'] || row['sshPort']);
                // addFieldIfNotEmpty('sshUsername', row['SSH用户名'] || row['sshUsername']);
                // addFieldIfNotEmpty('sshAuthType', row['SSH认证方式'] || row['sshAuthType']);
                // addFieldIfNotEmpty('sshPassword', row['SSH密码'] || row['sshPassword']);
                // addFieldIfNotEmpty('sshPrivateKey', row['SSH私钥'] || row['sshPrivateKey']);
                // addFieldIfNotEmpty('sshKeyPassphrase', row['SSH密钥密码'] || row['sshKeyPassphrase']);

                // 监控相关字段
                if (row['启用监控'] !== undefined) {
                  serverData.monitoringEnabled = row['启用监控'] === '是' || row['启用监控'] === true;
                }
                addFieldIfNotEmpty('cpuThreshold', row['CPU阈值'] || row['cpuThreshold']);
                addFieldIfNotEmpty('memoryThreshold', row['内存阈值'] || row['memoryThreshold']);
                addFieldIfNotEmpty('diskThreshold', row['磁盘阈值'] || row['diskThreshold']);
                addFieldIfNotEmpty('networkThreshold', row['网络阈值'] || row['networkThreshold']);

                return serverData;
              });

            console.log('解析的服务器数据:', serversData);

            // 调用导入API
            const result = await request.post('/servers/import', { servers: serversData });

            if (result.success) {
              message.success(`导入完成: 成功 ${result.data.success} 台，失败 ${result.data.failed} 台`);

              // 显示详细结果
              if (result.data.failed > 0) {
                const failedItems = result.data.results.filter((r: any) => !r.success);
                console.log('导入失败的项目:', failedItems);

                Modal.info({
                  title: '导入结果详情',
                  width: 600,
                  content: (
                    <div>
                      <p>成功导入: {result.data.success} 台</p>
                      <p>导入失败: {result.data.failed} 台</p>
                      {failedItems.length > 0 && (
                        <div>
                          <p style={{ marginTop: 16, fontWeight: 'bold' }}>失败详情:</p>
                          <ul style={{ maxHeight: 200, overflow: 'auto' }}>
                            {failedItems.map((item: any, index: number) => (
                              <li key={index} style={{ marginBottom: 8 }}>
                                <strong>{item.data.name || item.data.ipAddress}</strong>: {item.error}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ),
                });
              }

              // 刷新服务器列表
              fetchServers();
            } else {
              throw new Error(result.message);
            }
          } catch (error) {
            console.error('解析Excel文件失败:', error);
            message.error('解析Excel文件失败: ' + (error as Error).message);
          }
        };
        reader.readAsArrayBuffer(file);
      } catch (error) {
        console.error('导入失败:', error);
        message.error('导入失败: ' + (error as Error).message);
      }
    };
    input.click();
  };

  // 获取负载状态
  const getLoadStatus = (usage: number, threshold: number) => {
    if (usage >= threshold) {
      return { status: 'exception', color: 'red' };
    } else if (usage >= threshold * 0.8) {
      return { status: 'active', color: 'orange' };
    } else {
      return { status: 'success', color: 'green' };
    }
  };

  // 格式化运行时间
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    return `${days}天${hours}小时`;
  };

  // 计算到期天数
  const getDaysUntilExpiry = (expireDate: string) => {
    const today = new Date();
    const expire = new Date(expireDate);
    const diffTime = expire.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // 表格列定义
  const columns = [
    {
      title: '服务器信息',
      dataIndex: 'name',
      key: 'name',
      width: 220,
      render: (name: string, record: Server) => (
        <div>
          <div className="font-medium text-gray-900">{name}</div>
          <div className="text-sm text-gray-500">{record.ipAddress}</div>
          <div className="text-xs text-gray-400">{record.location}</div>
          {record.instanceId && (
            <div className="text-xs text-blue-500">实例: {record.instanceId}</div>
          )}
          {record.type && (
            <div className="text-xs text-green-600">类型: {record.type}</div>
          )}
          {record.region && (
            <div className="text-xs text-purple-600">地区: {record.region}</div>
          )}
          {record.department && (
            <div className="text-xs text-blue-600 mt-1">
              <Tag size="small" color="blue">{record.department}</Tag>
            </div>
          )}
        </div>
      )
    },
    {
      title: '配置规格',
      dataIndex: 'specifications',
      key: 'specifications',
      width: 200,
      render: (specs: any) => (
        <div className="text-sm">
          <div>CPU: {specs.cpu}</div>
          <div>内存: {specs.memory}</div>
          <div>存储: {specs.storage}</div>
          <div>带宽: {specs.bandwidth}</div>
        </div>
      )
    },
    {
      title: '负载状态',
      dataIndex: 'loadInfo',
      key: 'loadInfo',
      width: 200,
      render: (loadInfo: ServerLoadInfo, record: Server) => {
        if (!loadInfo) {
          return <span className="text-gray-400">未监控</span>;
        }

        const cpuStatus = getLoadStatus(loadInfo.cpuUsage, record.alertThresholds?.cpuUsage || 80);
        const memStatus = getLoadStatus(loadInfo.memoryUsage, record.alertThresholds?.memoryUsage || 85);
        const diskStatus = getLoadStatus(loadInfo.diskUsage, record.alertThresholds?.diskUsage || 90);

        return (
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs">CPU</span>
              <Progress 
                percent={loadInfo.cpuUsage} 
                size="small" 
                strokeColor={cpuStatus.color}
                showInfo={false}
                className="flex-1 mx-2"
              />
              <span className="text-xs w-8">{loadInfo.cpuUsage}%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs">内存</span>
              <Progress 
                percent={loadInfo.memoryUsage} 
                size="small" 
                strokeColor={memStatus.color}
                showInfo={false}
                className="flex-1 mx-2"
              />
              <span className="text-xs w-8">{loadInfo.memoryUsage}%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs">磁盘</span>
              <Progress 
                percent={loadInfo.diskUsage} 
                size="small" 
                strokeColor={diskStatus.color}
                showInfo={false}
                className="flex-1 mx-2"
              />
              <span className="text-xs w-8">{loadInfo.diskUsage}%</span>
            </div>
          </div>
        );
      }
    },
    {
      title: '运行状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string, record: Server) => {
        const config = statusConfig[status as keyof typeof statusConfig];
        const daysLeft = getDaysUntilExpiry(record.expireDate);
        const isExpiringSoon = daysLeft <= 30;
        
        return (
          <div>
            <Tag color={config?.color} icon={config?.icon}>
              {config?.label}
            </Tag>
            {isExpiringSoon && (
              <div className="mt-1">
                <Tag color="orange" size="small">即将到期</Tag>
              </div>
            )}
            {record.loadInfo && (
              <div className="text-xs text-gray-500 mt-1">
                运行 {formatUptime(record.loadInfo.uptime)}
              </div>
            )}
          </div>
        );
      }
    },
    {
      title: '到期时间',
      dataIndex: 'expireDate',
      key: 'expireDate',
      width: 120,
      render: (date: string, record: Server) => {
        if (!date) return '-';
        
        const daysLeft = getDaysUntilExpiry(date);
        const isExpiring = daysLeft <= 30;
        
        return (
          <div>
            <div className={isExpiring ? 'text-red-500 font-medium' : ''}>
              {new Date(date).toLocaleDateString()}
            </div>
            <div className="text-xs text-gray-500">
              {daysLeft > 0 ? `${daysLeft}天后到期` : '已过期'}
            </div>
            {record.renewalFee && (
              <div className="text-xs text-green-600">
                续费: ¥{record.renewalFee}
              </div>
            )}
          </div>
        );
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (_, record: Server) => (
        <Space size="small">
          <Tooltip title="监控详情">
            <Button
              type="text"
              icon={<MonitorOutlined />}
              onClick={() => handleViewMonitor(record)}
              disabled={!record.monitoringEnabled}
            />
          </Tooltip>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确认删除"
              description={`确定要删除服务器 "${record.name}" 吗？`}
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div style={{ padding: 24, backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      <style>
        {`
          .table-row-light {
            background-color: #ffffff;
          }
          .table-row-dark {
            background-color: #f9fafb;
          }
          .table-row-light:hover,
          .table-row-dark:hover {
            background-color: #f3f4f6 !important;
          }
          .ant-table-thead > tr > th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
            padding: 16px;
          }
          .ant-table-tbody > tr > td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
          }
        `}
      </style>

      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        {/* 标题和操作按钮 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 20 }}>
          <div>
            <h1 style={{ fontSize: 28, fontWeight: 600, color: '#1f2937', margin: 0, marginBottom: 8 }}>
              服务器台账管理
            </h1>
            <p style={{ color: '#6b7280', fontSize: 16, margin: 0 }}>
              管理服务器资源、监控负载状态和到期提醒
            </p>
          </div>
          <Space size="middle">
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchServers}
              style={{ height: 40, borderRadius: 8 }}
            >
              刷新数据
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={handleImport}
              style={{
                height: 40,
                borderRadius: 8
              }}
            >
              Excel导入
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExport}
              style={{ height: 40, borderRadius: 8 }}
            >
              导出Excel
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
              style={{
                height: 40,
                borderRadius: 8,
                backgroundColor: '#3b82f6',
                borderColor: '#3b82f6',
                boxShadow: '0 2px 4px rgba(59, 130, 246, 0.2)'
              }}
            >
              新增服务器
            </Button>
          </Space>
        </div>

        {/* 搜索和筛选区域 */}
        <Card
          style={{
            borderRadius: 12,
            border: '1px solid #e5e7eb',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}
          bodyStyle={{ padding: '20px 24px' }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <div style={{ flex: 1, maxWidth: 500 }}>
              <Input.Search
                placeholder="搜索服务器名称、IP地址、位置、服务商、部门或备注..."
                value={searchText}
                onChange={(e) => handleSearch(e.target.value)}
                onSearch={handleSearch}
                allowClear
                size="large"
                style={{ borderRadius: 8 }}
                enterButton={
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    style={{
                      borderRadius: '0 8px 8px 0',
                      backgroundColor: '#3b82f6',
                      borderColor: '#3b82f6'
                    }}
                  >
                    搜索
                  </Button>
                }
              />
            </div>
            {searchText && (
              <div style={{
                padding: '8px 16px',
                backgroundColor: '#eff6ff',
                borderRadius: 8,
                color: '#1e40af',
                fontSize: 14,
                fontWeight: 500
              }}>
                找到 {filteredServers.length} 个结果
              </div>
            )}
          </div>
        </Card>

        {/* 批量操作提示 */}
        {selectedServers.length > 0 && (
          <Card
            style={{
              marginTop: 16,
              borderRadius: 12,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              backgroundColor: '#f8fafc'
            }}
            styles={{ body: { padding: '16px 24px' } }}
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <Badge count={selectedServers.length} style={{ backgroundColor: '#3b82f6' }}>
                  <DesktopOutlined style={{ fontSize: 20, color: '#3b82f6' }} />
                </Badge>
                <span style={{ color: '#374151', fontSize: 16, fontWeight: 500 }}>
                  已选择 {selectedServers.length} 台服务器
                </span>
              </div>
              <Space>
                <Button
                  icon={<KeyOutlined />}
                  style={{
                    borderColor: '#3b82f6',
                    color: '#3b82f6'
                  }}
                  onClick={() => setBatchSshConfigVisible(true)}
                >
                  批量SSH配置
                </Button>
                <Button
                  icon={<CalendarOutlined />}
                  style={{
                    borderColor: '#10b981',
                    color: '#10b981'
                  }}
                  onClick={() => setBatchExpireDateVisible(true)}
                >
                  批量修改到期日期
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  style={{
                    borderColor: '#f59e0b',
                    color: '#f59e0b'
                  }}
                  onClick={handleBatchRefreshConfig}
                >
                  批量刷新配置
                </Button>
                <Button
                  icon={<CodeOutlined />}
                  style={{
                    borderColor: '#8b5cf6',
                    color: '#8b5cf6'
                  }}
                  onClick={() => setBatchTerminalVisible(true)}
                >
                  批量终端管理
                </Button>
                <Button
                  icon={<DeleteOutlined />}
                  danger
                  onClick={() => handleBatchDelete()}
                >
                  批量删除
                </Button>
                <Button
                  onClick={handleClearSelection}
                  style={{ color: '#6b7280' }}
                >
                  取消选择
                </Button>
              </Space>
            </div>
          </Card>
        )}
      </div>



      {/* 统计信息栏 */}
      <Card
        style={{
          marginBottom: 16,
          borderRadius: 12,
          border: '1px solid #e5e7eb',
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}
        styles={{ body: { padding: '16px 24px' } }}
      >
        <Row gutter={[24, 16]} align="middle">
          <Col flex="auto">
            <Space size={32}>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <DesktopOutlined style={{ fontSize: 16, color: '#3b82f6' }} />
                <span style={{ color: '#6b7280', fontSize: 14 }}>总服务器:</span>
                <span style={{ color: '#1f2937', fontSize: 16, fontWeight: 600 }}>
                  {searchText ? filteredServers.length : servers.length}
                  {searchText && <span style={{ color: '#3b82f6', fontSize: 12, marginLeft: 4 }}>(已过滤)</span>}
                </span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <CheckCircleOutlined style={{ fontSize: 16, color: '#10b981' }} />
                <span style={{ color: '#6b7280', fontSize: 14 }}>正常运行:</span>
                <span style={{ color: '#10b981', fontSize: 16, fontWeight: 600 }}>
                  {(searchText ? filteredServers : servers).filter(s => s.status === 'active').length}
                </span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <WarningOutlined style={{ fontSize: 16, color: '#f59e0b' }} />
                <span style={{ color: '#6b7280', fontSize: 14 }}>即将到期:</span>
                <span style={{ color: '#f59e0b', fontSize: 16, fontWeight: 600 }}>
                  {(searchText ? filteredServers : servers).filter(s => getDaysUntilExpiry(s.expireDate) <= 30).length}
                </span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <DollarOutlined style={{ fontSize: 16, color: '#8b5cf6' }} />
                <span style={{ color: '#6b7280', fontSize: 14 }}>月续费总额:</span>
                <span style={{ color: '#8b5cf6', fontSize: 16, fontWeight: 600 }}>
                  ¥{(searchText ? filteredServers : servers)
                    .filter(s => getDaysUntilExpiry(s.expireDate) <= 30)
                    .reduce((sum, s) => sum + (parseFloat(s.renewalFee) || 0), 0)
                    .toLocaleString()}
                </span>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card
        style={{
          borderRadius: 12,
          border: '1px solid #e5e7eb',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)'
        }}
        styles={{ body: { padding: 0 } }}
      >
        <Table
          columns={columns}
          dataSource={searchText ? filteredServers : servers}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          style={{ borderRadius: 12, overflow: 'hidden' }}
          rowSelection={handleRowSelection}
          rowClassName={(record, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: searchText ? filteredServers.length : servers.length,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            showTotal: (total) => (
              <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                <span style={{ color: '#6b7280', fontSize: 14 }}>
                  共 <span style={{ color: '#1f2937', fontWeight: 500 }}>{total}</span> 条记录
                  {searchText && <span style={{ color: '#3b82f6' }}> (已过滤)</span>}
                </span>
                <Button
                  size="small"
                  type={queryParams.pageSize >= 1000 ? "primary" : "default"}
                  onClick={() => {
                    setQueryParams(prev => ({
                      ...prev,
                      page: 1,
                      pageSize: queryParams.pageSize >= 1000 ? 10 : 1000
                    }));
                  }}
                  style={{ fontSize: 12 }}
                >
                  {queryParams.pageSize >= 1000 ? "分页显示" : "显示全部"}
                </Button>
              </div>
            ),
            onChange: (page, pageSize) => {
              setQueryParams(prev => ({
                ...prev,
                page,
                pageSize: pageSize || prev.pageSize
              }));
            },
            onShowSizeChange: (current, size) => {
              setQueryParams(prev => ({
                ...prev,
                page: 1,
                pageSize: size
              }));
            },
            style: { padding: '16px 24px', borderTop: '1px solid #f3f4f6' }
          }}
        />
      </Card>

      {/* 服务器表单 */}
      <ServerForm
        visible={formVisible}
        mode={formMode}
        initialValues={currentServer}
        onSuccess={handleFormSuccess}
        onCancel={handleFormCancel}
      />

      {/* 服务器详情 */}
      <ServerDetail
        visible={detailVisible}
        server={detailServer}
        onClose={() => setDetailVisible(false)}
        onEdit={(server) => {
          setDetailVisible(false);
          handleEdit(server);
        }}
      />

      {/* 服务器监控 */}
      <ServerMonitor
        visible={monitorVisible}
        server={monitorServer}
        onClose={() => setMonitorVisible(false)}
      />

      {/* 批量SSH配置 */}
      <BatchSshConfig
        selectedServers={selectedServers}
        onSuccess={handleBatchSuccess}
        onClearSelection={handleClearSelection}
        visible={batchSshConfigVisible}
        onCancel={() => setBatchSshConfigVisible(false)}
      />

      {/* 批量修改到期日期 */}
      <BatchExpireDateModal
        visible={batchExpireDateVisible}
        selectedServers={selectedServers}
        onOk={handleBatchUpdateExpireDate}
        onCancel={() => setBatchExpireDateVisible(false)}
      />

      {/* 批量终端管理 */}
      <BatchTerminalManager
        visible={batchTerminalVisible}
        selectedServers={selectedServers}
        onCancel={() => setBatchTerminalVisible(false)}
        onClearSelection={handleClearSelection}
      />
    </div>
  );
};

export default SimpleServerManagement;
