import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Tooltip,
  message,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Row,
  Col,
  Statistic,
  Alert,
  Badge,
  Descriptions,
  Timeline,
  Tabs,
  Switch,
  Slider,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CloudServerOutlined,
  MonitorOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  DatabaseOutlined,
  CloudOutlined,
  ThunderboltOutlined,
  ShieldOutlined,
  CalendarOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import dayjs from 'dayjs';
import { Server, ServerLoadInfo, ServerCredential, QueryParams } from '../../types';
import { ServerApi } from '../../services/server';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

interface ServerManagementProps {}

const ServerManagement: React.FC<ServerManagementProps> = () => {
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [monitorVisible, setMonitorVisible] = useState(false);
  const [currentServer, setCurrentServer] = useState<Server | undefined>();
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10
  });
  const [form] = Form.useForm();

  // 状态配置
  const statusConfig = {
    active: { label: '正常', color: 'green', icon: <CheckCircleOutlined /> },
    inactive: { label: '停用', color: 'red', icon: <ExclamationCircleOutlined /> },
    maintenance: { label: '维护中', color: 'orange', icon: <SettingOutlined /> },
    expired: { label: '已过期', color: 'red', icon: <ExclamationCircleOutlined /> }
  };

  // 获取服务器数据
  const fetchServers = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockData: Server[] = [
        {
          id: 1,
          name: 'Web-Server-01',
          ipAddress: '*************',
          location: '北京-阿里云',
          provider: '阿里云',
          specifications: {
            cpu: '4核 Intel Xeon',
            memory: '8GB DDR4',
            storage: '100GB SSD',
            bandwidth: '10Mbps',
            os: 'Ubuntu 20.04 LTS'
          },
          expireDate: '2024-12-31',
          renewalFee: 3600,
          status: 'active',
          loadInfo: {
            cpuUsage: 45,
            memoryUsage: 68,
            diskUsage: 35,
            networkIn: 2.5,
            networkOut: 1.8,
            uptime: 2592000, // 30天
            loadAverage: [0.8, 0.9, 1.1],
            processes: 156,
            lastUpdated: '2024-06-15T10:00:00Z'
          },
          monitoringEnabled: true,
          alertThresholds: {
            cpuUsage: 80,
            memoryUsage: 85,
            diskUsage: 90,
            networkUsage: 80
          },
          sshPort: 22,
          sshUsername: 'root',
          accessCredentials: [
            {
              id: 1,
              serverId: 1,
              type: 'ssh',
              username: 'root',
              password: 'encrypted_password',
              port: 22,
              description: 'SSH root access',
              isActive: true,
              createdAt: '2024-01-01',
              updatedAt: '2024-01-01'
            }
          ],
          notes: '主要Web服务器，运行WordPress站点',
          createdAt: '2024-01-01',
          updatedAt: '2024-06-15'
        },
        {
          id: 2,
          name: 'DB-Server-01',
          ipAddress: '*************',
          location: '上海-腾讯云',
          provider: '腾讯云',
          specifications: {
            cpu: '8核 Intel Xeon',
            memory: '16GB DDR4',
            storage: '500GB SSD',
            bandwidth: '20Mbps',
            os: 'CentOS 8'
          },
          expireDate: '2024-08-15',
          renewalFee: 7200,
          status: 'active',
          loadInfo: {
            cpuUsage: 25,
            memoryUsage: 72,
            diskUsage: 58,
            networkIn: 1.2,
            networkOut: 0.8,
            uptime: 1728000, // 20天
            loadAverage: [0.5, 0.6, 0.7],
            processes: 89,
            lastUpdated: '2024-06-15T10:00:00Z'
          },
          monitoringEnabled: true,
          alertThresholds: {
            cpuUsage: 75,
            memoryUsage: 80,
            diskUsage: 85,
            networkUsage: 75
          },
          notes: '数据库服务器，MySQL主库',
          createdAt: '2024-01-01',
          updatedAt: '2024-06-15'
        }
      ];
      setServers(mockData);
    } catch (error) {
      message.error('获取服务器数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServers();
  }, [queryParams]);

  // 处理新建
  const handleCreate = () => {
    setFormMode('create');
    setCurrentServer(undefined);
    form.resetFields();
    setFormVisible(true);
  };

  // 处理编辑
  const handleEdit = (record: Server) => {
    setFormMode('edit');
    setCurrentServer(record);
    form.setFieldsValue({
      ...record,
      expireDate: record.expireDate ? dayjs(record.expireDate) : null
    });
    setFormVisible(true);
  };

  // 处理查看详情
  const handleDetail = (record: Server) => {
    setCurrentServer(record);
    setDetailVisible(true);
  };

  // 处理监控
  const handleMonitor = (record: Server) => {
    setCurrentServer(record);
    setMonitorVisible(true);
  };

  // 获取负载状态
  const getLoadStatus = (usage: number, threshold: number) => {
    if (usage >= threshold) {
      return { status: 'exception', color: 'red' };
    } else if (usage >= threshold * 0.8) {
      return { status: 'active', color: 'orange' };
    } else {
      return { status: 'success', color: 'green' };
    }
  };

  // 格式化运行时间
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    return `${days}天${hours}小时`;
  };

  // 表格列定义
  const columns = [
    {
      title: '服务器信息',
      dataIndex: 'name',
      key: 'name',
      width: 220,
      render: (name: string, record: Server) => (
        <div>
          <div className="font-medium text-gray-900">{name}</div>
          <div className="text-sm text-gray-500">{record.ipAddress}</div>
          <div className="text-xs text-gray-400">{record.location}</div>
          {record.department && (
            <div className="text-xs text-blue-600 mt-1">
              <Tag size="small" color="blue">{record.department}</Tag>
            </div>
          )}
        </div>
      )
    },
    {
      title: '配置规格',
      dataIndex: 'specifications',
      key: 'specifications',
      width: 200,
      render: (specs: any) => (
        <div className="text-sm">
          <div>CPU: {specs.cpu}</div>
          <div>内存: {specs.memory}</div>
          <div>存储: {specs.storage}</div>
          <div>带宽: {specs.bandwidth}</div>
        </div>
      )
    },
    {
      title: '负载状态',
      dataIndex: 'loadInfo',
      key: 'loadInfo',
      width: 200,
      render: (loadInfo: ServerLoadInfo, record: Server) => {
        if (!loadInfo) {
          return <span className="text-gray-400">未监控</span>;
        }

        const cpuStatus = getLoadStatus(loadInfo.cpuUsage, record.alertThresholds?.cpuUsage || 80);
        const memStatus = getLoadStatus(loadInfo.memoryUsage, record.alertThresholds?.memoryUsage || 85);
        const diskStatus = getLoadStatus(loadInfo.diskUsage, record.alertThresholds?.diskUsage || 90);

        return (
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-xs">CPU</span>
              <Progress 
                percent={loadInfo.cpuUsage} 
                size="small" 
                strokeColor={cpuStatus.color}
                showInfo={false}
                className="flex-1 mx-2"
              />
              <span className="text-xs w-8">{loadInfo.cpuUsage}%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs">内存</span>
              <Progress 
                percent={loadInfo.memoryUsage} 
                size="small" 
                strokeColor={memStatus.color}
                showInfo={false}
                className="flex-1 mx-2"
              />
              <span className="text-xs w-8">{loadInfo.memoryUsage}%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-xs">磁盘</span>
              <Progress 
                percent={loadInfo.diskUsage} 
                size="small" 
                strokeColor={diskStatus.color}
                showInfo={false}
                className="flex-1 mx-2"
              />
              <span className="text-xs w-8">{loadInfo.diskUsage}%</span>
            </div>
          </div>
        );
      }
    },
    {
      title: '运行状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string, record: Server) => {
        const config = statusConfig[status as keyof typeof statusConfig];
        const isExpiringSoon = record.expireDate && dayjs(record.expireDate).diff(dayjs(), 'days') <= 30;
        
        return (
          <div>
            <Tag color={config?.color} icon={config?.icon}>
              {config?.label}
            </Tag>
            {isExpiringSoon && (
              <div className="mt-1">
                <Tag color="orange" size="small">即将到期</Tag>
              </div>
            )}
            {record.loadInfo && (
              <div className="text-xs text-gray-500 mt-1">
                运行 {formatUptime(record.loadInfo.uptime)}
              </div>
            )}
          </div>
        );
      }
    },
    {
      title: '到期时间',
      dataIndex: 'expireDate',
      key: 'expireDate',
      width: 120,
      render: (date: string, record: Server) => {
        if (!date) return '-';
        
        const daysLeft = dayjs(date).diff(dayjs(), 'days');
        const isExpiring = daysLeft <= 30;
        
        return (
          <div>
            <div className={isExpiring ? 'text-red-500 font-medium' : ''}>
              {dayjs(date).format('YYYY-MM-DD')}
            </div>
            <div className="text-xs text-gray-500">
              {daysLeft > 0 ? `${daysLeft}天后到期` : '已过期'}
            </div>
            {record.renewalFee && (
              <div className="text-xs text-green-600">
                续费: ¥{record.renewalFee}
              </div>
            )}
          </div>
        );
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 180,
      fixed: 'right' as const,
      render: (_, record: Server) => (
        <Space size="small">
          <Tooltip title="监控详情">
            <Button
              type="text"
              icon={<MonitorOutlined />}
              onClick={() => handleMonitor(record)}
            />
          </Tooltip>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确认删除"
              description={`确定要删除服务器 "${record.name}" 吗？此操作不可恢复。`}
              onConfirm={async () => {
                try {
                  await ServerApi.deleteServer(record.id);
                  message.success('服务器删除成功');
                  fetchServers();
                } catch (error) {
                  console.error('删除服务器失败:', error);
                  message.error('删除服务器失败');
                }
              }}
              okText="确定"
              cancelText="取消"
              okType="danger"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6"
    >
      {/* 页面标题和操作 */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">服务器台账管理</h1>
            <p className="text-gray-600 mt-1">管理服务器资源、监控负载状态和到期提醒</p>
          </div>
          <Space>
            <Button icon={<ReloadOutlined />} onClick={fetchServers}>
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
              className="bg-blue-500 hover:bg-blue-600"
            >
              新增服务器
            </Button>
          </Space>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="总服务器数"
              value={servers.length}
              prefix={<CloudServerOutlined className="text-blue-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="正常运行"
              value={servers.filter(s => s.status === 'active').length}
              prefix={<CheckCircleOutlined className="text-green-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="即将到期"
              value={servers.filter(s => s.expireDate && dayjs(s.expireDate).diff(dayjs(), 'days') <= 30).length}
              prefix={<WarningOutlined className="text-orange-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="月续费总额"
              value={servers.filter(s => s.expireDate && dayjs(s.expireDate).diff(dayjs(), 'days') <= 30).reduce((sum, s) => sum + (s.renewalFee || 0), 0)}
              prefix={<DollarOutlined className="text-red-500" />}
              formatter={(value) => `¥${value.toLocaleString()}`}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={servers}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: servers.length,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 监控详情模态框 */}
      <Modal
        title="服务器监控详情"
        open={monitorVisible}
        onCancel={() => setMonitorVisible(false)}
        footer={null}
        width={1000}
      >
        {currentServer && currentServer.loadInfo && (
          <div>
            <Alert
              message="实时监控数据"
              description={`最后更新时间: ${dayjs(currentServer.loadInfo.lastUpdated).format('YYYY-MM-DD HH:mm:ss')}`}
              type="info"
              showIcon
              className="mb-4"
            />
            
            <Row gutter={16} className="mb-6">
              <Col span={6}>
                <Card>
                  <Statistic
                    title="CPU使用率"
                    value={currentServer.loadInfo.cpuUsage}
                    suffix="%"
                    valueStyle={{ color: currentServer.loadInfo.cpuUsage > 80 ? '#cf1322' : '#3f8600' }}
                  />
                  <Progress 
                    percent={currentServer.loadInfo.cpuUsage} 
                    strokeColor={currentServer.loadInfo.cpuUsage > 80 ? '#cf1322' : '#3f8600'}
                    className="mt-2"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="内存使用率"
                    value={currentServer.loadInfo.memoryUsage}
                    suffix="%"
                    valueStyle={{ color: currentServer.loadInfo.memoryUsage > 85 ? '#cf1322' : '#3f8600' }}
                  />
                  <Progress 
                    percent={currentServer.loadInfo.memoryUsage} 
                    strokeColor={currentServer.loadInfo.memoryUsage > 85 ? '#cf1322' : '#3f8600'}
                    className="mt-2"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="磁盘使用率"
                    value={currentServer.loadInfo.diskUsage}
                    suffix="%"
                    valueStyle={{ color: currentServer.loadInfo.diskUsage > 90 ? '#cf1322' : '#3f8600' }}
                  />
                  <Progress 
                    percent={currentServer.loadInfo.diskUsage} 
                    strokeColor={currentServer.loadInfo.diskUsage > 90 ? '#cf1322' : '#3f8600'}
                    className="mt-2"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="运行时间"
                    value={formatUptime(currentServer.loadInfo.uptime)}
                  />
                  <div className="text-sm text-gray-500 mt-2">
                    进程数: {currentServer.loadInfo.processes}
                  </div>
                </Card>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Card title="网络流量" size="small">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>入流量:</span>
                      <span className="text-green-600">{currentServer.loadInfo.networkIn} MB/s</span>
                    </div>
                    <div className="flex justify-between">
                      <span>出流量:</span>
                      <span className="text-blue-600">{currentServer.loadInfo.networkOut} MB/s</span>
                    </div>
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="负载平均值" size="small">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>1分钟:</span>
                      <span>{currentServer.loadInfo.loadAverage[0]}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>5分钟:</span>
                      <span>{currentServer.loadInfo.loadAverage[1]}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>15分钟:</span>
                      <span>{currentServer.loadInfo.loadAverage[2]}</span>
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </motion.div>
  );
};

export default ServerManagement;
