import React from 'react';
import { <PERSON>, Typography, Button, Space } from 'antd';
import { PlusOutlined, DatabaseOutlined } from '@ant-design/icons';
import { PermissionGuard, PermissionAwareButton } from '../../components/Permission/PermissionGuard';

const { Title, Paragraph } = Typography;

const ServerList: React.FC = () => {
  return (
    <div>
      <Title level={2}>服务器管理</Title>
      
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <DatabaseOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={4} type="secondary">服务器管理功能开发中</Title>
          <Paragraph type="secondary">
            此功能将包含服务器监控、负载管理、到期提醒等功能
          </Paragraph>
          <Space>
            <PermissionAwareButton
              type="primary"
              icon={<PlusOutlined />}
              permissions={['server.server.create']}
              hideWhenNoPermission={false}
            >
              新建服务器
            </PermissionAwareButton>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default ServerList;
