import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Typography, Button, Space } from 'antd';
import { ArrowLeftOutlined, DatabaseOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const ServerDetail: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/servers')}
        >
          返回
        </Button>
        <Title level={2} style={{ margin: 0 }}>
          服务器详情
        </Title>
      </Space>
      
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <DatabaseOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={4} type="secondary">服务器详情页面开发中</Title>
          <Paragraph type="secondary">
            此页面将显示服务器的详细信息、监控数据、性能指标等
          </Paragraph>
        </div>
      </Card>
    </div>
  );
};

export default ServerDetail;
