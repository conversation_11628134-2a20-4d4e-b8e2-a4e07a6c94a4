import React from 'react';
import { Card, Typography, Tabs, Alert, But<PERSON>, Space } from 'antd';
import { SettingOutlined, KeyOutlined, PlusOutlined, DownloadOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

const SimpleSettings: React.FC = () => {
  return (
    <div style={{ padding: 24 }}>
      <Card>
        <Title level={2}>系统设置</Title>

        <Tabs defaultActiveKey="general">
          <TabPane tab={
            <span>
              <SettingOutlined />
              基础设置
            </span>
          } key="general">
            <Card title="基础配置">
              <p>这是基础设置页面，用于配置系统的基本参数。</p>
            </Card>
          </TabPane>

          <TabPane tab={
            <span>
              <KeyOutlined />
              权限模板
            </span>
          } key="permission-templates">
            <Card title="权限模板管理">
              <div style={{ marginBottom: '16px' }}>
                <Alert
                  message="权限模板管理"
                  description="在这里您可以创建、编辑和管理权限模板，包括行业标准权限预设。权限模板可以快速应用到用户或角色，提高权限配置效率。"
                  type="info"
                  showIcon
                  style={{ marginBottom: '16px' }}
                />
              </div>

              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Title level={4}>权限模板功能</Title>
                <Paragraph>
                  权限模板功能正在开发中，即将为您提供：
                </Paragraph>
                <ul style={{ textAlign: 'left', maxWidth: '400px', margin: '0 auto' }}>
                  <li>权限模板创建和编辑</li>
                  <li>行业标准权限预设</li>
                  <li>模板导入导出功能</li>
                  <li>模板使用统计分析</li>
                  <li>一键应用到用户权限</li>
                </ul>
                <div style={{ marginTop: '20px' }}>
                  <Space>
                    <Button type="primary" icon={<PlusOutlined />} disabled>
                      创建权限模板
                    </Button>
                    <Button icon={<DownloadOutlined />} disabled>
                      导入模板
                    </Button>
                  </Space>
                </div>
                <div style={{ marginTop: '16px' }}>
                  <Alert
                    message="开发进度"
                    description="权限模板功能的后端API已完成，前端界面正在开发中。预计下个版本将提供完整的权限模板管理功能。"
                    type="warning"
                    showIcon
                  />
                </div>
              </div>
            </Card>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default SimpleSettings;