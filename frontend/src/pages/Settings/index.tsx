import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Switch,
  Button,
  Space,
  message,
  Divider,
  Select,
  InputNumber,
  Row,
  Col,
  Alert,
  Upload,
  Typography,
  Tag,
  Table,
  Modal,
  TimePicker,
  Statistic,
  Tooltip
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  UploadOutlined,
  DownloadOutlined,
  BellOutlined,
  SecurityScanOutlined,
  DatabaseOutlined,
  CloudOutlined,
  MailOutlined,
  ApiOutlined,
  ExclamationCircleOutlined,
  CloudServerOutlined,
  GlobalOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined,
  LockOutlined,
  RobotOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { settingsService } from '../../services/settings';
import NotificationSettings from '../../components/NotificationSettings';
// 权限控制相关导入
import { PermissionGuard, RequirePermission, PermissionButton, PermissionAwareButton } from '../../components/Permission/PermissionGuard';
import { usePermissions } from '../../contexts/PermissionContext';
import NotificationLogs from '../../components/NotificationLogs';


const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Title, Paragraph } = Typography;
const { RangePicker } = TimePicker;

interface SystemSettings {
  general: {
    siteName: string;
    siteUrl: string;
    adminEmail: string;
    timezone: string;
    language: string;
    dateFormat: string;
    enableRegistration: boolean;
    enableMaintenance: boolean;
    maintenanceMessage: string;
  };
  notification: {
    emailEnabled: boolean;
    smsEnabled: boolean;
    feishuEnabled: boolean;
    webhookEnabled: boolean;
    emailHost: string;
    emailPort: number;
    emailUser: string;
    emailPassword: string;
    smsProvider: string;
    smsApiKey: string;
    webhookUrl: string;
    feishuWebhookUrl: string;
    feishuBotName: string;
    feishuNotificationThreshold: number;
    notificationTypes: string[];
  };
  security: {
    enableTwoFactor: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordMinLength: number;
    passwordRequireSpecial: boolean;
    enableIpWhitelist: boolean;
    ipWhitelist: string[];
    enableAuditLog: boolean;
    auditLogRetention: number;
  };
  backup: {
    enableAutoBackup: boolean;
    backupFrequency: string;
    backupTime: string;
    backupRetention: number;
    backupLocation: string;
    enableCloudBackup: boolean;
    cloudProvider: string;
    cloudConfig: any;
  };
  api: {
    enableApi: boolean;
    apiRateLimit: number;
    apiKeyExpiration: number;
    enableApiLogging: boolean;
    allowedOrigins: string[];
    enableCors: boolean;
  };
}

const Settings: React.FC = () => {
  // 权限检查Hook - 使用我们实现的权限系统
  const { hasPermission, hasRole, userInfo } = usePermissions();
  
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [sshConfigs, setSshConfigs] = useState<any[]>([]);
  const [sshModalVisible, setSshModalVisible] = useState(false);
  const [editingSshConfig, setEditingSshConfig] = useState<any>(null);
  const [sshForm] = Form.useForm();

  // 部门类型管理状态
  const [departments, setDepartments] = useState<string[]>(['技术部', '运维部', '产品部', '市场部', '财务部', '人事部', '行政部']);
  const [departmentModalVisible, setDepartmentModalVisible] = useState(false);
  const [editingDepartment, setEditingDepartment] = useState<string | null>(null);
  const [departmentForm] = Form.useForm();

  // 网站设置状态
  const [platforms, setPlatforms] = useState<string[]>([]);
  const [industries, setIndustries] = useState<string[]>(['电商', '企业官网', '博客', '论坛', '新闻资讯', '教育培训', '医疗健康', '金融服务']);
  const [platformModalVisible, setPlatformModalVisible] = useState(false);
  const [industryModalVisible, setIndustryModalVisible] = useState(false);
  const [editingPlatform, setEditingPlatform] = useState<string | null>(null);
  const [editingIndustry, setEditingIndustry] = useState<string | null>(null);
  const [platformForm] = Form.useForm();
  const [industryForm] = Form.useForm();

  // 智能分类相关状态
  const [autoClassifyLoading, setAutoClassifyLoading] = useState(false);
  const [autoClassifyResult, setAutoClassifyResult] = useState<any>(null);

  const [settings, setSettings] = useState<SystemSettings>({
    general: {
      siteName: 'WordPress站点管理系统',
      siteUrl: 'http://localhost:3000',
      adminEmail: '<EMAIL>',
      timezone: 'Asia/Shanghai',
      language: 'zh-CN',
      dateFormat: 'YYYY-MM-DD',
      enableRegistration: false,
      enableMaintenance: false,
      maintenanceMessage: '系统维护中，请稍后访问'
    },
    notification: {
      emailEnabled: true,
      smsEnabled: false,
      feishuEnabled: false,
      webhookEnabled: false,
      emailHost: 'smtp.qq.com',
      emailPort: 587,
      emailUser: '',
      emailPassword: '',
      smsProvider: 'aliyun',
      smsApiKey: '',
      webhookUrl: '',
      feishuWebhookUrl: '',
      feishuBotName: '网站监控机器人',
      feishuNotificationThreshold: 5,
      notificationTypes: ['ssl_expiry', 'domain_expiry', 'server_alert', 'website_offline']
    },
    security: {
      enableTwoFactor: false,
      sessionTimeout: 24,
      maxLoginAttempts: 5,
      passwordMinLength: 8,
      passwordRequireSpecial: true,
      enableIpWhitelist: false,
      ipWhitelist: [],
      enableAuditLog: true,
      auditLogRetention: 90
    },
    backup: {
      enableAutoBackup: true,
      backupFrequency: 'daily',
      backupTime: '02:00',
      backupRetention: 30,
      backupLocation: '/backup',
      enableCloudBackup: false,
      cloudProvider: 'aliyun',
      cloudConfig: {}
    },
    api: {
      enableApi: true,
      apiRateLimit: 1000,
      apiKeyExpiration: 365,
      enableApiLogging: true,
      allowedOrigins: ['http://localhost:3000'],
      enableCors: true
    }
  });

  const [form] = Form.useForm();

  // 加载设置
  const loadSettings = async () => {
    setLoading(true);
    try {
      // const systemSettings = await settingsService.getSettings();
      // setSettings(systemSettings);
      // form.setFieldsValue(systemSettings);

      // 加载SSH配置
      // if (systemSettings.server?.sshConfigs) {
      //   setSshConfigs(systemSettings.server.sshConfigs);
      // }

      // 加载部门类型
      // if (systemSettings.server?.departments) {
      //   setDepartments(systemSettings.server.departments);
      // }
    } catch (error) {
      message.error('加载设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载SSH配置列表
  const loadSshConfigs = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/ssh-configs');
      const result = await response.json();
      if (result.success) {
        setSshConfigs(result.data);
      }
    } catch (error) {
      console.error('加载SSH配置失败:', error);
    }
  };

  // 保存SSH配置
  const saveSshConfig = async (values: any) => {
    try {
      const url = editingSshConfig
        ? `http://localhost:3001/api/v1/settings/ssh-configs/${editingSshConfig.id}`
        : 'http://localhost:3001/api/v1/settings/ssh-configs';

      const method = editingSshConfig ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(values),
      });

      const result = await response.json();
      if (result.success) {
        message.success(editingSshConfig ? 'SSH配置更新成功' : 'SSH配置创建成功');
        setSshModalVisible(false);
        setEditingSshConfig(null);
        sshForm.resetFields();
        loadSshConfigs();
      } else {
        message.error(result.message || '保存失败');
      }
    } catch (error) {
      message.error('保存SSH配置失败');
    }
  };

  // 删除SSH配置
  const deleteSshConfig = async (id: number) => {
    try {
      const response = await fetch(`http://localhost:3001/api/v1/settings/ssh-configs/${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      if (result.success) {
        message.success('SSH配置删除成功');
        loadSshConfigs();
      } else {
        message.error(result.message || '删除失败');
      }
    } catch (error) {
      message.error('删除SSH配置失败');
    }
  };

  // 编辑SSH配置
  const editSshConfig = async (config: any) => {
    try {
      // 获取完整的SSH配置信息（包含敏感信息）
      const response = await fetch(`http://localhost:3001/api/v1/settings/ssh-configs/${config.id}`);
      const result = await response.json();

      if (result.success) {
        setEditingSshConfig(result.data);
        sshForm.setFieldsValue({
          name: result.data.name,
          username: result.data.username,
          authType: result.data.authType,
          password: result.data.password,
          privateKey: result.data.privateKey,
          keyPassphrase: result.data.keyPassphrase,
          description: result.data.description,
          isActive: result.data.isActive
        });
        setSshModalVisible(true);
      }
    } catch (error) {
      message.error('获取SSH配置详情失败');
    }
  };

  // 加载部门类型
  const loadDepartments = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/departments');
      const result = await response.json();
      if (result.success) {
        setDepartments(result.data);
      }
    } catch (error) {
      console.error('加载部门类型失败:', error);
    }
  };

  // 保存部门类型
  const saveDepartments = async (newDepartments: string[]) => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/departments', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ departments: newDepartments }),
      });

      const result = await response.json();
      if (result.success) {
        setDepartments(newDepartments);
        message.success('部门类型保存成功');
      } else {
        message.error(result.message || '保存失败');
      }
    } catch (error) {
      message.error('保存部门类型失败');
    }
  };

  // 添加部门
  const addDepartment = (departmentName: string) => {
    if (!departmentName.trim()) {
      message.error('部门名称不能为空');
      return;
    }
    if (departments.includes(departmentName.trim())) {
      message.error('部门已存在');
      return;
    }
    const newDepartments = [...departments, departmentName.trim()];
    saveDepartments(newDepartments);
  };

  // 编辑部门
  const editDepartment = (oldName: string, newName: string) => {
    if (!newName.trim()) {
      message.error('部门名称不能为空');
      return;
    }
    if (newName !== oldName && departments.includes(newName.trim())) {
      message.error('部门已存在');
      return;
    }
    const newDepartments = departments.map(dept => dept === oldName ? newName.trim() : dept);
    saveDepartments(newDepartments);
  };

  // 删除部门
  const deleteDepartment = (departmentName: string) => {
    const newDepartments = departments.filter(dept => dept !== departmentName);
    saveDepartments(newDepartments);
  };

  // 平台类型管理函数
  const loadPlatforms = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/platforms');
      const result = await response.json();
      if (result.success) {
        // 只显示活跃的平台
        const activePlatforms = result.data
          .filter((platform: any) => platform.isActive)
          .map((platform: any) => platform.name);
        setPlatforms(activePlatforms);
      }
    } catch (error) {
      console.error('加载平台类型失败:', error);
    }
  };



  const addPlatform = async (platformName: string) => {
    if (!platformName.trim()) {
      message.error('平台名称不能为空');
      return;
    }

    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/platforms/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: platformName.trim() }),
      });

      const result = await response.json();
      if (result.success) {
        message.success('平台添加成功');
        loadPlatforms(); // 重新加载平台列表
      } else {
        message.error(result.message || '添加失败');
      }
    } catch (error) {
      message.error('添加平台失败');
    }
  };

  const editPlatform = async (oldName: string, newName: string) => {
    if (!newName.trim()) {
      message.error('平台名称不能为空');
      return;
    }

    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/platforms/edit', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ oldName, newName: newName.trim() }),
      });

      const result = await response.json();
      if (result.success) {
        message.success('平台编辑成功');
        loadPlatforms(); // 重新加载平台列表
      } else {
        message.error(result.message || '编辑失败');
      }
    } catch (error) {
      message.error('编辑平台失败');
    }
  };

  const deletePlatform = async (platformName: string) => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/platforms/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: platformName }),
      });

      const result = await response.json();
      if (result.success) {
        message.success('平台删除成功');
        loadPlatforms(); // 重新加载平台列表
      } else {
        message.error(result.message || '删除失败');
      }
    } catch (error) {
      message.error('删除平台失败');
    }
  };

  // 同步网站中的平台数据
  const syncPlatformsFromWebsites = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/platforms/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();
      if (result.success) {
        loadPlatforms(); // 重新加载平台列表
        message.success(`平台数据同步成功，新增 ${result.data.added.length} 个平台类型`);
        if (result.data.added.length > 0) {
          message.info(`新增平台：${result.data.added.join(', ')}`);
        }
      } else {
        message.error(result.message || '同步失败');
      }
    } catch (error) {
      message.error('同步平台数据失败');
      console.error('同步平台数据失败:', error);
    }
  };

  // 行业类型管理函数
  const loadIndustries = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/industries');
      const result = await response.json();
      if (result.success) {
        setIndustries(result.data);
      }
    } catch (error) {
      console.error('加载行业类型失败:', error);
    }
  };

  const saveIndustries = async (newIndustries: string[]) => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/industries', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ industries: newIndustries }),
      });

      const result = await response.json();
      if (result.success) {
        setIndustries(newIndustries);
        message.success('行业类型保存成功');
      } else {
        message.error(result.message || '保存失败');
      }
    } catch (error) {
      message.error('保存行业类型失败');
    }
  };

  const addIndustry = (industryName: string) => {
    if (!industryName.trim()) {
      message.error('行业名称不能为空');
      return;
    }
    if (industries.includes(industryName.trim())) {
      message.error('行业已存在');
      return;
    }
    const newIndustries = [...industries, industryName.trim()];
    saveIndustries(newIndustries);
  };

  const editIndustry = (oldName: string, newName: string) => {
    if (!newName.trim()) {
      message.error('行业名称不能为空');
      return;
    }
    if (newName !== oldName && industries.includes(newName.trim())) {
      message.error('行业已存在');
      return;
    }
    const newIndustries = industries.map(industry => industry === oldName ? newName.trim() : industry);
    saveIndustries(newIndustries);
  };

  const deleteIndustry = (industryName: string) => {
    const newIndustries = industries.filter(industry => industry !== industryName);
    saveIndustries(newIndustries);
  };

  // 智能平台分类
  const handleAutoClassifyPlatforms = async () => {
    try {
      setAutoClassifyLoading(true);
      const response = await fetch('http://localhost:3001/api/v1/websites/auto-classify-platforms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();
      if (result.success) {
        setAutoClassifyResult(result.data);
        message.success(`智能分类完成，更新了 ${result.data.updatedCount} 个网站`);
      } else {
        message.error(result.message || '智能分类失败');
      }
    } catch (error) {
      message.error('智能分类失败');
      console.error('智能分类失败:', error);
    } finally {
      setAutoClassifyLoading(false);
    }
  };

  // 保存设置
  const saveSettings = async (values: any) => {
    setSaving(true);
    try {
      await settingsService.updateSettings(activeTab, values);
      setSettings({ ...settings, [activeTab]: values });
      message.success('设置保存成功');
    } catch (error) {
      message.error('保存设置失败');
    } finally {
      setSaving(false);
    }
  };

  // 测试飞书通知
  const testFeishuNotification = async () => {
    try {
      const formValues = form.getFieldsValue();
      const webhookUrl = formValues.feishuWebhookUrl;

      if (!webhookUrl) {
        message.error('请先配置飞书机器人Webhook URL');
        return;
      }

      const response = await fetch('http://localhost:3001/api/v1/notifications/test-feishu', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          webhookUrl: webhookUrl,
          botName: formValues.feishuBotName || '网站监控机器人'
        }),
      });

      const result = await response.json();
      if (result.success) {
        message.success('飞书测试通知发送成功');
      } else {
        message.error(`飞书测试通知发送失败: ${result.message}`);
      }
    } catch (error) {
      message.error('测试飞书通知失败');
    }
  };

  useEffect(() => {
    loadSettings();
    loadSshConfigs();
    loadDepartments();
    loadPlatforms();
    loadIndustries();
  }, []);

  // 通知类型选项
  const notificationTypeOptions = [
    { label: 'SSL证书到期提醒', value: 'ssl_expiry' },
    { label: '域名到期提醒', value: 'domain_expiry' },
    { label: '服务器告警', value: 'server_alert' },
    { label: '网站离线告警', value: 'website_offline' },
    { label: '备份完成通知', value: 'backup_complete' },
    { label: '系统更新通知', value: 'system_update' }
  ];

  return (
    <RequirePermission permission={['system.settings.view', 'system.settings.edit']}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="p-6"
      >
      {/* 页面标题和操作 */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
            <p className="text-gray-600 mt-1">配置系统参数和功能选项</p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadSettings}
              loading={loading}
            >
              重新加载
            </Button>
            <PermissionAwareButton
              permissions={['system.settings.export']}
              icon={<DownloadOutlined />}
              hideWhenNoPermission={false}
            >
              导出配置
            </PermissionAwareButton>
            <PermissionAwareButton
              permissions={['system.settings.import']}
              icon={<UploadOutlined />}
              hideWhenNoPermission={false}
            >
              导入配置
            </PermissionAwareButton>
          </Space>
        </div>
      </div>

      {/* 设置标签页 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        className="mb-6"
      >
        <TabPane tab={
          <span>
            <SettingOutlined />
            基础设置
          </span>
        } key="general">
          <Card>
            <Form
              form={form}
              layout="vertical"
              initialValues={settings.general}
              onFinish={saveSettings}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="站点名称"
                    name="siteName"
                    rules={[{ required: true, message: '请输入站点名称' }]}
                  >
                    <Input placeholder="请输入站点名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="站点URL"
                    name="siteUrl"
                    rules={[{ required: true, message: '请输入站点URL' }]}
                  >
                    <Input placeholder="http://localhost:3000" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="管理员邮箱"
                    name="adminEmail"
                    rules={[
                      { required: true, message: '请输入管理员邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input placeholder="<EMAIL>" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="时区"
                    name="timezone"
                  >
                    <Select placeholder="选择时区">
                      <Option value="Asia/Shanghai">Asia/Shanghai</Option>
                      <Option value="UTC">UTC</Option>
                      <Option value="America/New_York">America/New_York</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="语言"
                    name="language"
                  >
                    <Select placeholder="选择语言">
                      <Option value="zh-CN">简体中文</Option>
                      <Option value="en-US">English</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="日期格式"
                    name="dateFormat"
                  >
                    <Select placeholder="选择日期格式">
                      <Option value="YYYY-MM-DD">YYYY-MM-DD</Option>
                      <Option value="MM/DD/YYYY">MM/DD/YYYY</Option>
                      <Option value="DD/MM/YYYY">DD/MM/YYYY</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="允许用户注册"
                    name="enableRegistration"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="维护模式"
                    name="enableMaintenance"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="维护提示信息"
                name="maintenanceMessage"
              >
                <TextArea
                  rows={3}
                  placeholder="系统维护中，请稍后访问"
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <PermissionButton
                    permissions={['system.settings.edit']}
                    type="primary"
                    htmlType="submit"
                    loading={saving}
                    icon={<SaveOutlined />}
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    保存设置
                  </PermissionButton>
                  <Button onClick={() => form.resetFields()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <BellOutlined />
            通知配置
          </span>
        } key="notification">
          <NotificationSettings />
        </TabPane>

        <TabPane tab={
          <span>
            <BellOutlined />
            通知历史
          </span>
        } key="notification-logs">
          <NotificationLogs />
        </TabPane>

        <TabPane tab={
          <span>
            <KeyOutlined />
            权限模板
          </span>
        } key="permission-templates">
          <RequirePermission permission="system.permission.manage">
            <Card title="权限模板管理" className="mb-6">
              <div style={{ marginBottom: '16px' }}>
                <Alert
                  message="权限模板管理"
                  description="在这里您可以创建、编辑和管理权限模板，包括行业标准权限预设。权限模板可以快速应用到用户或角色，提高权限配置效率。"
                  type="info"
                  showIcon
                  style={{ marginBottom: '16px' }}
                />
              </div>

              <div style={{ textAlign: 'center', padding: '40px 0' }}>
                <Title level={4}>权限模板功能</Title>
                <Paragraph>
                  权限模板功能正在开发中，即将为您提供：
                </Paragraph>
                <ul style={{ textAlign: 'left', maxWidth: '400px', margin: '0 auto' }}>
                  <li>权限模板创建和编辑</li>
                  <li>行业标准权限预设</li>
                  <li>模板导入导出功能</li>
                  <li>模板使用统计分析</li>
                  <li>一键应用到用户权限</li>
                </ul>
                <div style={{ marginTop: '20px' }}>
                  <Button type="primary" icon={<PlusOutlined />} disabled>
                    创建权限模板
                  </Button>
                  <Button style={{ marginLeft: '8px' }} icon={<DownloadOutlined />} disabled>
                    导入模板
                  </Button>
                </div>
              </div>
            </Card>
          </RequirePermission>
        </TabPane>

        <TabPane tab={
          <span>
            <SecurityScanOutlined />
            安全设置
          </span>
        } key="security">
          <Card>
            <Form
              layout="vertical"
              initialValues={settings.security}
              onFinish={saveSettings}
            >
              <Alert
                message="安全提醒"
                description="安全设置直接影响系统安全性，请谨慎配置"
                type="warning"
                showIcon
                className="mb-6"
              />

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="启用双因子认证"
                    name="enableTwoFactor"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="启用审计日志"
                    name="enableAuditLog"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="会话超时时间（小时）"
                    name="sessionTimeout"
                  >
                    <InputNumber
                      min={1}
                      max={168}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="最大登录尝试次数"
                    name="maxLoginAttempts"
                  >
                    <InputNumber
                      min={3}
                      max={10}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="密码最小长度"
                    name="passwordMinLength"
                  >
                    <InputNumber
                      min={6}
                      max={20}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="密码需要特殊字符"
                    name="passwordRequireSpecial"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="审计日志保留天数"
                name="auditLogRetention"
              >
                <InputNumber
                  min={30}
                  max={365}
                  style={{ width: '100%' }}
                  addonAfter="天"
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={saving}
                    icon={<SaveOutlined />}
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    保存设置
                  </Button>
                  <Button danger>
                    重置安全设置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <DatabaseOutlined />
            备份设置
          </span>
        } key="backup">
          <Card>
            <Form
              layout="vertical"
              initialValues={settings.backup}
              onFinish={saveSettings}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="启用自动备份"
                    name="enableAutoBackup"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="启用云备份"
                    name="enableCloudBackup"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="备份频率"
                    name="backupFrequency"
                  >
                    <Select>
                      <Option value="daily">每日</Option>
                      <Option value="weekly">每周</Option>
                      <Option value="monthly">每月</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="备份时间"
                    name="backupTime"
                  >
                    <Input placeholder="02:00" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="备份保留天数"
                    name="backupRetention"
                  >
                    <InputNumber
                      min={7}
                      max={365}
                      style={{ width: '100%' }}
                      addonAfter="天"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="备份位置"
                    name="backupLocation"
                  >
                    <Input placeholder="/backup" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={saving}
                    icon={<SaveOutlined />}
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    保存设置
                  </Button>
                  <Button>
                    立即备份
                  </Button>
                  <Button>
                    查看备份历史
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <ApiOutlined />
            API设置
          </span>
        } key="api">
          <Card>
            <Form
              layout="vertical"
              initialValues={settings.api}
              onFinish={saveSettings}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="启用API"
                    name="enableApi"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="启用CORS"
                    name="enableCors"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="API速率限制（每小时）"
                    name="apiRateLimit"
                  >
                    <InputNumber
                      min={100}
                      max={10000}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="API密钥有效期（天）"
                    name="apiKeyExpiration"
                  >
                    <InputNumber
                      min={30}
                      max={365}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="允许的来源"
                name="allowedOrigins"
              >
                <Select
                  mode="tags"
                  placeholder="输入允许的域名"
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={saving}
                    icon={<SaveOutlined />}
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    保存设置
                  </Button>
                  <Button>
                    生成API密钥
                  </Button>
                  <Button>
                    查看API文档
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <GlobalOutlined />
            网站设置
          </span>
        } key="website">
          <div className="space-y-6">
            {/* 平台类型管理 */}
            <Card>
              <div className="mb-4">
                <div className="flex justify-between items-center">
                  <div>
                    <Title level={4}>平台类型管理</Title>
                    <Paragraph type="secondary">
                      管理网站平台类型，可在编辑网站时选择使用
                    </Paragraph>
                  </div>
                  <Space>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={syncPlatformsFromWebsites}
                      title="从现有网站数据中同步平台类型"
                    >
                      同步平台
                    </Button>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => {
                        setEditingPlatform(null);
                        platformForm.resetFields();
                        setPlatformModalVisible(true);
                      }}
                      className="bg-blue-500 hover:bg-blue-600"
                    >
                      添加平台
                    </Button>
                  </Space>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {platforms.map((platform, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
                  >
                    <div className="flex items-center space-x-2">
                      <Tag color="blue">{platform}</Tag>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Button
                        type="text"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => {
                          setEditingPlatform(platform);
                          platformForm.setFieldsValue({ name: platform });
                          setPlatformModalVisible(true);
                        }}
                      />
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => {
                          Modal.confirm({
                            title: '确认删除',
                            content: `确定要删除平台"${platform}"吗？`,
                            onOk: () => deletePlatform(platform)
                          });
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* 行业类型管理 */}
            <Card>
              <div className="mb-4">
                <div className="flex justify-between items-center">
                  <div>
                    <Title level={4}>行业类型管理</Title>
                    <Paragraph type="secondary">
                      管理网站所属行业类型，可在编辑网站时选择使用
                    </Paragraph>
                  </div>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setEditingIndustry(null);
                      industryForm.resetFields();
                      setIndustryModalVisible(true);
                    }}
                    className="bg-green-500 hover:bg-green-600"
                  >
                    添加行业
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {industries.map((industry, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-green-300 transition-colors"
                  >
                    <div className="flex items-center space-x-2">
                      <Tag color="green">{industry}</Tag>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Button
                        type="text"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => {
                          setEditingIndustry(industry);
                          industryForm.setFieldsValue({ name: industry });
                          setIndustryModalVisible(true);
                        }}
                      />
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => {
                          Modal.confirm({
                            title: '确认删除',
                            content: `确定要删除行业"${industry}"吗？`,
                            onOk: () => deleteIndustry(industry)
                          });
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* 平台智能分类 */}
            <Card>
              <div className="mb-4">
                <div className="flex justify-between items-center">
                  <div>
                    <Title level={4}>
                      <RobotOutlined className="mr-2" />
                      平台智能分类
                    </Title>
                    <Paragraph type="secondary">
                      系统将根据网站名称、域名等特征自动识别平台类型，支持易营宝、多谷、巨旺、弗米乐、领动等平台
                    </Paragraph>
                  </div>
                  <Button
                    type="primary"
                    icon={<RobotOutlined />}
                    onClick={handleAutoClassifyPlatforms}
                    loading={autoClassifyLoading}
                  >
                    执行智能分类
                  </Button>
                </div>
              </div>

              {autoClassifyResult && (
                <Alert
                  message="智能分类完成"
                  description={
                    <div>
                      <p>总网站数: {autoClassifyResult.totalWebsites}</p>
                      <p>更新数量: {autoClassifyResult.updatedCount}</p>
                      <p>检测统计: {Object.entries(autoClassifyResult.detectedStats || {}).map(([platform, count]) =>
                        `${platform}: ${count}个`
                      ).join(', ')}</p>
                    </div>
                  }
                  type="success"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
              )}

              <div className="text-gray-600">
                <p><strong>支持的平台识别规则:</strong></p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>易营宝: 根据站点名称包含"易营宝"或"ehai"关键词识别</li>
                  <li>多谷: 根据站点名称包含"多谷"或"duogu"关键词识别</li>
                  <li>巨旺: 根据站点名称包含"巨旺"或"juwan"关键词识别</li>
                  <li>弗米乐: 根据站点名称包含"弗米乐"或"fumile"关键词识别</li>
                  <li>领动: 根据站点名称包含"领动"或"lingdong"关键词识别</li>
                  <li>Shopify: 根据域名包含".shopify.com"识别</li>
                  <li>WordPress: 根据域名包含".wordpress.com"或URL路径包含"/wp-content/"识别</li>
                </ul>
              </div>
            </Card>
          </div>
        </TabPane>

        <TabPane tab={
          <span>
            <CloudServerOutlined />
            服务器设置
          </span>
        } key="server">
          <div className="space-y-6">
            {/* 部门类型管理 */}
            <Card>
              <div className="mb-4">
                <div className="flex justify-between items-center">
                  <div>
                    <Title level={4}>部门类型管理</Title>
                    <Paragraph type="secondary">
                      管理服务器所属部门类型，可在编辑服务器时选择使用
                    </Paragraph>
                  </div>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setEditingDepartment(null);
                      departmentForm.resetFields();
                      setDepartmentModalVisible(true);
                    }}
                    className="bg-green-500 hover:bg-green-600"
                  >
                    添加部门
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {departments.map((dept, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors"
                  >
                    <div className="flex items-center space-x-2">
                      <Tag color="blue">{dept}</Tag>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Button
                        type="text"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => {
                          setEditingDepartment(dept);
                          departmentForm.setFieldsValue({ name: dept });
                          setDepartmentModalVisible(true);
                        }}
                      />
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => {
                          Modal.confirm({
                            title: '确认删除',
                            content: `确定要删除部门"${dept}"吗？`,
                            onOk: () => deleteDepartment(dept)
                          });
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* SSH配置管理 */}
            <Card>
              <div className="mb-4">
                <div className="flex justify-between items-center">
                  <div>
                    <Title level={4}>SSH配置管理</Title>
                    <Paragraph type="secondary">
                      管理服务器SSH连接配置，可在编辑服务器时直接选择使用
                    </Paragraph>
                  </div>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => {
                      setEditingSshConfig(null);
                      sshForm.resetFields();
                      setSshModalVisible(true);
                    }}
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    新增SSH配置
                  </Button>
                </div>
              </div>

            <Table
              dataSource={sshConfigs}
              rowKey="id"
              pagination={false}
              columns={[
                {
                  title: '配置名称',
                  dataIndex: 'name',
                  key: 'name',
                  render: (text, record) => (
                    <Space>
                      {record.auth_type === 'key' ? <KeyOutlined /> : <LockOutlined />}
                      <span>{text}</span>
                      {!record.is_active && <Tag color="red">已禁用</Tag>}
                    </Space>
                  )
                },
                {
                  title: '用户名',
                  dataIndex: 'username',
                  key: 'username'
                },
                {
                  title: '认证方式',
                  dataIndex: 'auth_type',
                  key: 'auth_type',
                  render: (type) => (
                    <Tag color={type === 'key' ? 'blue' : 'green'}>
                      {type === 'key' ? '密钥认证' : '密码认证'}
                    </Tag>
                  )
                },
                {
                  title: '描述',
                  dataIndex: 'description',
                  key: 'description',
                  ellipsis: true
                },
                {
                  title: '创建时间',
                  dataIndex: 'created_at',
                  key: 'created_at',
                  render: (date) => new Date(date).toLocaleString()
                },
                {
                  title: '操作',
                  key: 'action',
                  render: (_, record) => (
                    <Space>
                      <Button
                        type="link"
                        icon={<EditOutlined />}
                        onClick={() => editSshConfig(record)}
                      >
                        编辑
                      </Button>
                      <Button
                        type="link"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => {
                          Modal.confirm({
                            title: '确认删除',
                            content: `确定要删除SSH配置"${record.name}"吗？`,
                            onOk: () => deleteSshConfig(record.id)
                          });
                        }}
                      >
                        删除
                      </Button>
                    </Space>
                  )
                }
              ]}
            />
            </Card>
          </div>
        </TabPane>
      </Tabs>

      {/* 部门编辑弹窗 */}
      <Modal
        title={editingDepartment ? '编辑部门' : '添加部门'}
        open={departmentModalVisible}
        onOk={() => departmentForm.submit()}
        onCancel={() => {
          setDepartmentModalVisible(false);
          setEditingDepartment(null);
          departmentForm.resetFields();
        }}
        width={400}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={departmentForm}
          layout="vertical"
          onFinish={(values) => {
            if (editingDepartment) {
              editDepartment(editingDepartment, values.name);
            } else {
              addDepartment(values.name);
            }
            setDepartmentModalVisible(false);
            setEditingDepartment(null);
            departmentForm.resetFields();
          }}
        >
          <Form.Item
            name="name"
            label="部门名称"
            rules={[
              { required: true, message: '请输入部门名称' },
              { max: 20, message: '部门名称不能超过20个字符' }
            ]}
          >
            <Input placeholder="请输入部门名称" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 平台类型编辑弹窗 */}
      <Modal
        title={editingPlatform ? '编辑平台类型' : '添加平台类型'}
        open={platformModalVisible}
        onOk={() => platformForm.submit()}
        onCancel={() => {
          setPlatformModalVisible(false);
          setEditingPlatform(null);
          platformForm.resetFields();
        }}
        width={400}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={platformForm}
          layout="vertical"
          onFinish={(values) => {
            if (editingPlatform) {
              editPlatform(editingPlatform, values.name);
            } else {
              addPlatform(values.name);
            }
            setPlatformModalVisible(false);
            setEditingPlatform(null);
            platformForm.resetFields();
          }}
        >
          <Form.Item
            name="name"
            label="平台名称"
            rules={[
              { required: true, message: '请输入平台名称' },
              { max: 20, message: '平台名称不能超过20个字符' }
            ]}
          >
            <Input placeholder="请输入平台名称，如：WordPress" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 行业类型编辑弹窗 */}
      <Modal
        title={editingIndustry ? '编辑行业类型' : '添加行业类型'}
        open={industryModalVisible}
        onOk={() => industryForm.submit()}
        onCancel={() => {
          setIndustryModalVisible(false);
          setEditingIndustry(null);
          industryForm.resetFields();
        }}
        width={400}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={industryForm}
          layout="vertical"
          onFinish={(values) => {
            if (editingIndustry) {
              editIndustry(editingIndustry, values.name);
            } else {
              addIndustry(values.name);
            }
            setIndustryModalVisible(false);
            setEditingIndustry(null);
            industryForm.resetFields();
          }}
        >
          <Form.Item
            name="name"
            label="行业名称"
            rules={[
              { required: true, message: '请输入行业名称' },
              { max: 20, message: '行业名称不能超过20个字符' }
            ]}
          >
            <Input placeholder="请输入行业名称，如：电商" />
          </Form.Item>
        </Form>
      </Modal>

      {/* SSH配置编辑弹窗 */}
      <Modal
        title={editingSshConfig ? '编辑SSH配置' : '新增SSH配置'}
        open={sshModalVisible}
        onOk={() => sshForm.submit()}
        onCancel={() => {
          setSshModalVisible(false);
          setEditingSshConfig(null);
          sshForm.resetFields();
        }}
        width={600}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={sshForm}
          layout="vertical"
          onFinish={saveSshConfig}
          initialValues={{
            authType: 'password',
            isActive: true
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="配置名称"
                rules={[{ required: true, message: '请输入配置名称' }]}
              >
                <Input placeholder="例如：生产服务器SSH" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="username"
                label="SSH用户名"
                rules={[{ required: true, message: '请输入SSH用户名' }]}
              >
                <Input placeholder="root" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="authType"
            label="认证方式"
            rules={[{ required: true, message: '请选择认证方式' }]}
          >
            <Select>
              <Option value="password">密码认证</Option>
              <Option value="key">密钥认证</Option>
            </Select>
          </Form.Item>

          <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
            prevValues.authType !== currentValues.authType
          }>
            {({ getFieldValue }) => {
              const authType = getFieldValue('authType');

              if (authType === 'password') {
                return (
                  <Form.Item
                    name="password"
                    label="SSH密码"
                    rules={[{ required: true, message: '请输入SSH密码' }]}
                  >
                    <Input.Password placeholder="请输入SSH密码" />
                  </Form.Item>
                );
              } else if (authType === 'key') {
                return (
                  <>
                    <Form.Item
                      name="privateKey"
                      label="SSH私钥"
                      rules={[{ required: true, message: '请输入SSH私钥内容' }]}
                    >
                      <TextArea
                        rows={6}
                        placeholder="请粘贴SSH私钥内容"
                        style={{ fontFamily: 'monospace', fontSize: '12px' }}
                      />
                    </Form.Item>
                    <Form.Item
                      name="keyPassphrase"
                      label="密钥密码"
                      extra="如果您的SSH密钥设置了密码保护，请在此输入"
                    >
                      <Input.Password placeholder="如果密钥有密码保护，请输入密码" />
                    </Form.Item>
                  </>
                );
              }
              return null;
            }}
          </Form.Item>

          <Form.Item
            name="description"
            label="配置描述"
          >
            <TextArea
              rows={3}
              placeholder="请输入配置描述，例如：用于生产环境服务器的SSH连接"
            />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="启用状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </motion.div>
    </RequirePermission>
  );
};

export default Settings;
