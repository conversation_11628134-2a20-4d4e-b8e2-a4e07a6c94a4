import React, { useState } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Switch,
  Button,
  Space,
  message,
  Row,
  Col,
  Select
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface BasicSystemSettings {
  general: {
    siteName: string;
    siteUrl: string;
    adminEmail: string;
    timezone: string;
    language: string;
    enableRegistration: boolean;
    enableMaintenance: boolean;
    maintenanceMessage: string;
  };
}

const BasicSettings: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  
  const [settings, setSettings] = useState<BasicSystemSettings>({
    general: {
      siteName: 'WordPress站点管理系统',
      siteUrl: 'http://localhost:3000',
      adminEmail: '<EMAIL>',
      timezone: 'Asia/Shanghai',
      language: 'zh-CN',
      enableRegistration: false,
      enableMaintenance: false,
      maintenanceMessage: '系统维护中，请稍后访问'
    }
  });

  const [form] = Form.useForm();

  // 保存设置
  const saveSettings = async (values: any) => {
    setSaving(true);
    try {
      // 这里应该调用API保存设置
      console.log('保存设置:', values);
      setSettings({ ...settings, [activeTab]: values });
      message.success('设置保存成功');
    } catch (error) {
      message.error('保存设置失败');
    } finally {
      setSaving(false);
    }
  };

  return (
    <div style={{ padding: 24 }}>
      {/* 页面标题和操作 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{ fontSize: 24, fontWeight: 'bold', margin: 0 }}>系统设置</h1>
            <p style={{ color: '#666', marginTop: 4, marginBottom: 0 }}>配置系统参数和功能选项</p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              loading={loading}
            >
              重新加载
            </Button>
          </Space>
        </div>
      </div>

      {/* 设置标签页 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
      >
        <TabPane tab={
          <span>
            <SettingOutlined />
            基础设置
          </span>
        } key="general">
          <Card>
            <Form
              form={form}
              layout="vertical"
              initialValues={settings.general}
              onFinish={saveSettings}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="站点名称"
                    name="siteName"
                    rules={[{ required: true, message: '请输入站点名称' }]}
                  >
                    <Input placeholder="请输入站点名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="站点URL"
                    name="siteUrl"
                    rules={[{ required: true, message: '请输入站点URL' }]}
                  >
                    <Input placeholder="http://localhost:3000" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="管理员邮箱"
                    name="adminEmail"
                    rules={[
                      { required: true, message: '请输入管理员邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input placeholder="<EMAIL>" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="时区"
                    name="timezone"
                  >
                    <Select placeholder="选择时区">
                      <Option value="Asia/Shanghai">Asia/Shanghai</Option>
                      <Option value="UTC">UTC</Option>
                      <Option value="America/New_York">America/New_York</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="语言"
                    name="language"
                  >
                    <Select placeholder="选择语言">
                      <Option value="zh-CN">简体中文</Option>
                      <Option value="en-US">English</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="允许用户注册"
                    name="enableRegistration"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="维护模式"
                    name="enableMaintenance"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="维护提示信息"
                name="maintenanceMessage"
              >
                <TextArea
                  rows={3}
                  placeholder="系统维护中，请稍后访问"
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={saving}
                    icon={<SaveOutlined />}
                  >
                    保存设置
                  </Button>
                  <Button onClick={() => form.resetFields()}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default BasicSettings;