import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  message,
  Row,
  Col,
  Space,
  Divider,
  Alert,
  Tooltip,
  Switch
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  LockOutlined,
  PhoneOutlined,
  TeamOutlined,
  InfoCircleOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from '@ant-design/icons';
import { User } from '@/types';
import { UserApi, PermissionUtils } from '@/services/user';

const { Option } = Select;
const { TextArea } = Input;

interface UserFormProps {
  visible: boolean;
  mode: 'create' | 'edit';
  user?: User;
  onCancel: () => void;
  onSuccess: () => void;
}

const UserForm: React.FC<UserFormProps> = ({
  visible,
  mode,
  user,
  onCancel,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);

  // 重置表单
  useEffect(() => {
    if (visible) {
      if (mode === 'edit' && user) {
        form.setFieldsValue({
          username: user.username,
          email: user.email,
          realName: user.realName,
          role: user.role,
          status: user.status,
          phone: user.phone,
          department: user.department
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, mode, user, form]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      console.log('🚀 提交用户表单:', { mode, values });

      if (mode === 'create') {
        const response = await UserApi.createUser(values);
        if (response.success) {
          message.success('用户创建成功');
          onSuccess();
        } else {
          throw new Error(response.message || '创建用户失败');
        }
      } else if (mode === 'edit' && user) {
        // 编辑模式下，移除密码字段（如果为空）
        const updateData = { ...values };
        if (!updateData.password) {
          delete updateData.password;
        }

        const response = await UserApi.updateUser(user.id, updateData);
        if (response.success) {
          message.success('用户更新成功');
          onSuccess();
        } else {
          throw new Error(response.message || '更新用户失败');
        }
      }
    } catch (error: any) {
      console.error('❌ 用户表单提交失败:', error);

      // 处理表单验证错误
      if (error.errorFields && Array.isArray(error.errorFields)) {
        const firstError = error.errorFields[0];
        if (firstError && firstError.errors && firstError.errors.length > 0) {
          message.error(firstError.errors[0]);
        } else {
          message.error('表单验证失败，请检查输入信息');
        }
      } else {
        // 处理API调用错误
        message.error(error.message || '操作失败');
      }
    } finally {
      setLoading(false);
    }
  };

  // 角色选项
  // 状态管理：当前用户角色
  const [currentUserRole, setCurrentUserRole] = useState<string>('user');
  const [roleLoading, setRoleLoading] = useState<boolean>(false);

  // 获取当前用户角色
  const fetchCurrentUser = async () => {
    setRoleLoading(true);
    try {
      const response = await UserApi.getCurrentUser();
      if (response.success) {
        setCurrentUserRole(response.data.role);
        console.log('✅ 获取当前用户角色成功:', response.data.role);
        message.success(`当前角色: ${response.data.role === 'super_admin' ? '超级管理员' : response.data.role === 'admin' ? '管理员' : '普通用户'}`);
      }
    } catch (error) {
      console.error('获取当前用户信息失败:', error);

      // 检查是否是认证相关错误，如果是，API拦截器会自动处理
      const isAuthError = error.response?.status === 401 ||
                         error.response?.status === 404 ||
                         (error.response?.status === 403 &&
                          error.response?.data?.message?.includes('用户不存在'));

      if (isAuthError) {
        console.log('🔐 检测到认证错误，等待自动重定向...');
        // API拦截器会自动处理认证错误，这里不需要额外操作
        return;
      }

      // 非认证错误的降级策略
      try {
        const userData = localStorage.getItem('user');
        if (userData) {
          const user = JSON.parse(userData);
          const role = user.role || 'user';
          setCurrentUserRole(role);
          console.log('📦 使用localStorage中的角色:', role);
          message.warning('无法获取最新权限信息，使用缓存数据');
        } else {
          setCurrentUserRole('user');
          message.warning('权限获取失败，仅允许创建普通用户');
        }
      } catch {
        setCurrentUserRole('user');
        message.warning('权限获取失败，仅允许创建普通用户');
      }
    } finally {
      setRoleLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchCurrentUser();
    }
  }, [visible]);

  // 角色选项：只有超级管理员可以创建管理员和超级管理员
  const roleOptions = [
    { value: 'user', label: '普通用户', description: '基本的查看权限' },
    ...(currentUserRole === 'super_admin' ? [
      { value: 'admin', label: '管理员', description: '大部分管理权限' },
      { value: 'super_admin', label: '超级管理员', description: '所有权限' }
    ] : [])
  ];

  // 状态选项
  const statusOptions = [
    { value: 'active', label: '正常', color: 'green' },
    { value: 'inactive', label: '禁用', color: 'red' },
    { value: 'suspended', label: '暂停', color: 'orange' }
  ];

  // 检查是否可以修改角色
  const canManageRole = PermissionUtils.hasPermission('user.manage_role');
  const isCurrentUser = user?.id === JSON.parse(localStorage.getItem('user') || '{}').id;

  return (
    <Modal
      title={
        <Space>
          <UserOutlined />
          {mode === 'create' ? '新建用户' : '编辑用户'}
          {mode === 'create' && (
            <Button
              type="link"
              size="small"
              loading={roleLoading}
              onClick={fetchCurrentUser}
              title="刷新权限信息"
            >
              🔄 刷新权限
            </Button>
          )}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          {mode === 'create' ? '创建' : '更新'}
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          role: 'user',
          status: 'active'
        }}
      >
        <Alert
          message={mode === 'create' ? '创建新用户账户' : '编辑用户信息'}
          description={
            mode === 'create' 
              ? '请填写完整的用户信息。用户名和邮箱必须唯一。'
              : '修改用户信息。留空密码字段表示不修改密码。'
          }
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="username"
              label="用户名"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3个字符' },
                { max: 20, message: '用户名最多20个字符' },
                { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
              ]}
            >
              <Input
                prefix={<UserOutlined />}
                placeholder="请输入用户名"
                disabled={mode === 'edit'} // 编辑模式下用户名不可修改
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="email"
              label="邮箱"
              rules={[
                { required: true, message: '请输入邮箱' },
                { type: 'email', message: '请输入有效的邮箱地址' }
              ]}
            >
              <Input
                prefix={<MailOutlined />}
                placeholder="请输入邮箱"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="realName"
              label="真实姓名"
              rules={[
                { max: 50, message: '姓名最多50个字符' }
              ]}
            >
              <Input placeholder="请输入真实姓名" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="phone"
              label="手机号"
              rules={[
                { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
              ]}
            >
              <Input
                prefix={<PhoneOutlined />}
                placeholder="请输入手机号"
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="department"
          label="部门"
        >
          <Input
            prefix={<TeamOutlined />}
            placeholder="请输入所属部门"
          />
        </Form.Item>

        <Divider />

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="role"
              label={
                <Space>
                  角色
                  <Tooltip title="角色决定用户的权限范围">
                    <InfoCircleOutlined />
                  </Tooltip>
                </Space>
              }
              rules={[{ required: true, message: '请选择角色' }]}
            >
              <Select
                placeholder="请选择角色"
                disabled={!canManageRole || isCurrentUser}
              >
                {roleOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    <div>
                      <div>{option.label}</div>
                      <div style={{ fontSize: '12px', color: '#999' }}>
                        {option.description}
                      </div>
                    </div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="status"
              label="状态"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <Select
                placeholder="请选择状态"
                disabled={isCurrentUser} // 不能修改自己的状态
              >
                {statusOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="password"
          label={mode === 'create' ? '密码' : '新密码（留空表示不修改）'}
          rules={mode === 'create' ? [
            { required: true, message: '请输入密码' },
            { min: 6, message: '密码至少6个字符' },
            { max: 50, message: '密码最多50个字符' }
          ] : [
            { min: 6, message: '密码至少6个字符' },
            { max: 50, message: '密码最多50个字符' }
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder={mode === 'create' ? '请输入密码' : '留空表示不修改密码'}
            iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
          />
        </Form.Item>

        {mode === 'create' && (
          <Form.Item
            name="confirmPassword"
            label="确认密码"
            dependencies={['password']}
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                }
              })
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请再次输入密码"
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>
        )}

        {(!canManageRole || isCurrentUser) && (
          <Alert
            message="权限提示"
            description={
              !canManageRole 
                ? '您没有权限修改用户角色'
                : '不能修改自己的角色和状态'
            }
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </Form>
    </Modal>
  );
};

export default UserForm;
