import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Avatar,
  Popconfirm,
  message,
  Typography,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SecurityScanOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { User, QueryParams } from '@/types';
import { useAuthStore } from '@/store/auth';
import ExportButton from '@/components/Export/ExportButton';
import { ExportApi } from '@/services/export';
import { UserApi, PermissionUtils } from '@/services/user';
import UserForm from './UserForm';
import { PermissionGuard, PermissionButton, PermissionAwareButton } from '../../components/Permission/PermissionGuard';
import { usePermissions } from '../../contexts/PermissionContext';
import UserPermissionEditor from '../../components/Permission/UserPermissionEditor';
import EnhancedUserPermissionEditor from '../../components/Permission/EnhancedUserPermissionEditor';
import { Context7EnhancedPermissionEditor } from '../../components/Permission/Context7EnhancedPermissionEditor';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

const UserList: React.FC = () => {
  const { user: authUser } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10,
    search: '',
    role: '',
    status: '',
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  // 权限上下文
  const { hasPermission } = usePermissions();

  // 表单状态
  const [formVisible, setFormVisible] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [editingUser, setEditingUser] = useState<User | undefined>();
  
  // 权限编辑状态
  const [permissionEditorVisible, setPermissionEditorVisible] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);

  // 权限初始化已通过PermissionProvider处理，这里不需要额外初始化

  // 获取用户列表
  const fetchUsers = async () => {
    setLoading(true);
    try {
      console.log('🔍 前端调用用户列表API，参数:', queryParams);

      // 调用真实API
      const response = await UserApi.getUsers(queryParams);

      if (response.success) {
        setUsers(response.data.users);
        setTotal(response.data.pagination.total);
        console.log('✅ 用户列表获取成功:', response.data);
      } else {
        throw new Error(response.message || '获取用户列表失败');
      }
    } catch (error: any) {
      console.error('❌ 获取用户列表失败:', error);
      message.error(error.message || '获取用户列表失败');

      // 如果API失败，回退到简单API
      try {
        console.log('🔄 尝试使用简单API...');
        const fallbackResponse = await UserApi.getUsersSimple();
        if (fallbackResponse.success) {
          setUsers(fallbackResponse.data.users);
          setTotal(fallbackResponse.data.users.length);
          message.warning('使用简化模式显示用户列表');
        }
      } catch (fallbackError) {
        console.error('❌ 简单API也失败:', fallbackError);
        setUsers([]);
        setTotal(0);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [queryParams]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  // 处理筛选
  const handleFilter = (key: string, value: string) => {
    setQueryParams(prev => ({
      ...prev,
      [key]: value,
      page: 1,
    }));
  };

  // 处理分页
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setQueryParams(prev => ({
      ...prev,
      page: pagination.current,
      pageSize: pagination.pageSize,
      sortBy: sorter.field || 'created_at',
      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc',
    }));
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    try {
      console.log('🗑️ 删除用户:', id);
      const response = await UserApi.deleteUser(id);

      if (response.success) {
        message.success(response.message || '用户删除成功');
        fetchUsers(); // 重新获取用户列表
      } else {
        throw new Error(response.message || '删除用户失败');
      }
    } catch (error: any) {
      console.error('❌ 删除用户失败:', error);
      message.error(error.message || '删除用户失败');
    }
  };

  // 处理新建用户
  const handleCreate = () => {
    setFormMode('create');
    setEditingUser(undefined);
    setFormVisible(true);
  };

  // 处理编辑用户
  const handleEdit = (user: User) => {
    setFormMode('edit');
    setEditingUser(user);
    setFormVisible(true);
  };

  // 处理权限编辑
  const handleEditPermissions = (user: User) => {
    setSelectedUserId(user.id);
    setPermissionEditorVisible(true);
  };

  // 权限编辑成功回调 - Context7版本
  const handlePermissionEditSuccess = () => {
    message.success('用户权限更新成功');
    setPermissionEditorVisible(false);
    setSelectedUserId(null);
    fetchUsers(); // 重新获取用户列表
  };

  // 表单成功回调
  const handleFormSuccess = () => {
    setFormVisible(false);
    setEditingUser(undefined);
    fetchUsers(); // 重新获取用户列表
  };

  // 表单取消回调
  const handleFormCancel = () => {
    setFormVisible(false);
    setEditingUser(undefined);
  };

  // 获取角色标签
  const getRoleTag = (role: string) => {
    const roleMap = {
      super_admin: { color: 'red', text: '超级管理员' },
      admin: { color: 'blue', text: '管理员' },
      user: { color: 'green', text: '普通用户' },
    };
    const config = roleMap[role as keyof typeof roleMap] || { color: 'default', text: role };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: 'success', text: '正常' },
      inactive: { color: 'default', text: '禁用' },
      suspended: { color: 'warning', text: '暂停' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列配置
  const columns: ColumnsType<User> = [
    {
      title: '用户',
      dataIndex: 'username',
      key: 'username',
      render: (_, record) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} src={record.avatar} />
          <div>
            <div>{record.realName || record.username}</div>
            <div style={{ fontSize: 12, color: '#999' }}>{record.email}</div>
          </div>
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => getRoleTag(role),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status),
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      render: (date) => date ? new Date(date).toLocaleString() : '-',
      sorter: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleString(),
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <PermissionGuard
            permissions={['user.user.edit']}
            fallback={record.id === authUser?.id ? (
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              >
                编辑
              </Button>
            ) : null}
          >
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
          </PermissionGuard>
          
          <PermissionGuard permissions={['system.permission.manage']}>
            <Button
              type="link"
              icon={<SecurityScanOutlined />}
              onClick={() => handleEditPermissions(record)}
            >
              权限
            </Button>
          </PermissionGuard>
          
          <PermissionGuard permissions={['user.user.delete']}>
            <Popconfirm
              title="确定要删除这个用户吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
              disabled={record.id === authUser?.id}
            >
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                disabled={record.id === authUser?.id}
              >
                删除
              </Button>
            </Popconfirm>
          </PermissionGuard>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>用户管理</Title>
      
      <Card>
        {/* 工具栏 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索用户名、邮箱或姓名"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              placeholder="角色"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilter('role', value || '')}
            >
              <Option value="super_admin">超级管理员</Option>
              <Option value="admin">管理员</Option>
              <Option value="user">普通用户</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              placeholder="状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilter('status', value || '')}
            >
              <Option value="active">正常</Option>
              <Option value="inactive">禁用</Option>
              <Option value="suspended">暂停</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Space>
              <PermissionButton permission="user.create">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreate}
                >
                  新建用户
                </Button>
              </PermissionButton>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchUsers}
              >
                刷新
              </Button>
              <ExportButton
                exportType="users"
                queryParams={queryParams}
                onExport={ExportApi.exportUsers}
                totalCount={total}
              />
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 用户表单 */}
      <UserForm
        visible={formVisible}
        mode={formMode}
        user={editingUser}
        onCancel={handleFormCancel}
        onSuccess={handleFormSuccess}
      />

      {/* Context7增强版权限编辑器 */}
      {selectedUserId && (
        <Context7EnhancedPermissionEditor
          userId={selectedUserId}
          visible={permissionEditorVisible}
          onCancel={() => {
            setPermissionEditorVisible(false);
            setSelectedUserId(null);
          }}
          onSuccess={handlePermissionEditSuccess}
          mode="edit"
          filterLevel="advanced"
        />
      )}
    </div>
  );
};

export default UserList;
