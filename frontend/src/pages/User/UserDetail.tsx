import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Avatar,
  Tag,
  Typography,
  Divider,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  UserOutlined,
} from '@ant-design/icons';

const { Title } = Typography;

const UserDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // 模拟用户数据
  const user = {
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    realName: '系统管理员',
    role: 'super_admin',
    status: 'active',
    phone: '13800138000',
    department: '技术部',
    lastLogin: '2024-01-10T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T10:30:00Z',
  };

  const getRoleTag = (role: string) => {
    const roleMap = {
      super_admin: { color: 'red', text: '超级管理员' },
      admin: { color: 'blue', text: '管理员' },
      user: { color: 'green', text: '普通用户' },
    };
    const config = roleMap[role as keyof typeof roleMap] || { color: 'default', text: role };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: 'success', text: '正常' },
      inactive: { color: 'default', text: '禁用' },
      suspended: { color: 'warning', text: '暂停' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/users')}
        >
          返回
        </Button>
        <Title level={2} style={{ margin: 0 }}>
          用户详情
        </Title>
      </Space>

      <Card>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
          <Avatar size={64} icon={<UserOutlined />} src={user.avatar} />
          <div style={{ marginLeft: 16 }}>
            <Title level={3} style={{ margin: 0 }}>
              {user.realName || user.username}
            </Title>
            <div style={{ marginTop: 8 }}>
              {getRoleTag(user.role)}
              {getStatusTag(user.status)}
            </div>
          </div>
          <div style={{ marginLeft: 'auto' }}>
            <Button
              type="primary"
              icon={<EditOutlined />}
              onClick={() => navigate(`/users/${id}/edit`)}
            >
              编辑用户
            </Button>
          </div>
        </div>

        <Divider />

        <Descriptions title="基本信息" column={2}>
          <Descriptions.Item label="用户名">{user.username}</Descriptions.Item>
          <Descriptions.Item label="邮箱">{user.email}</Descriptions.Item>
          <Descriptions.Item label="真实姓名">{user.realName}</Descriptions.Item>
          <Descriptions.Item label="手机号">{user.phone}</Descriptions.Item>
          <Descriptions.Item label="部门">{user.department}</Descriptions.Item>
          <Descriptions.Item label="角色">{getRoleTag(user.role)}</Descriptions.Item>
          <Descriptions.Item label="状态">{getStatusTag(user.status)}</Descriptions.Item>
          <Descriptions.Item label="最后登录">
            {user.lastLogin ? new Date(user.lastLogin).toLocaleString() : '从未登录'}
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {new Date(user.createdAt).toLocaleString()}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {new Date(user.updatedAt).toLocaleString()}
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  );
};

export default UserDetail;
