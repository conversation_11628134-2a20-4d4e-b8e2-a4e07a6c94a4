import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  message,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Statistic,
  Badge,
  Avatar,
  Dropdown,
  Menu,
  Progress,
  Alert,
  Tabs,
  Descriptions,
  Timeline,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  GlobalOutlined,
  SafetyCertificateOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  KeyOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  SecurityScanOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  MoreOutlined,
  LinkOutlined,
  SafetyCertificateOutlined,
  UploadOutlined
} from '@ant-design/icons';
// import { motion, AnimatePresence } from 'framer-motion';
// import dayjs from 'dayjs';
import { Website, Platform, Server, WebsiteCredential, SSLInfo, DomainInfo, QueryParams } from '../../types';
import WebsiteImport from '../../components/Website/WebsiteImport';
import { usePermissions } from '../../contexts/PermissionContext';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

interface EnhancedWebsiteListProps {}

const EnhancedWebsiteList: React.FC<EnhancedWebsiteListProps> = () => {
  const { hasPermission } = usePermissions();
  const [websites, setWebsites] = useState<Website[]>([]);
  const [platforms, setPlatforms] = useState<Platform[]>([]);
  const [platformOptions, setPlatformOptions] = useState<string[]>([]);
  const [servers, setServers] = useState<Server[]>([]);
  const [loading, setLoading] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [credentialVisible, setCredentialVisible] = useState(false);
  const [importVisible, setImportVisible] = useState(false);
  const [currentWebsite, setCurrentWebsite] = useState<Website | undefined>();
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10
  });
  const [searchParams] = useSearchParams();
  const [form] = Form.useForm();
  const [credentialForm] = Form.useForm();

  // 状态配置
  const statusConfig = {
    active: { label: '正常', color: 'green', icon: <CheckCircleOutlined /> },
    inactive: { label: '停用', color: 'red', icon: <ExclamationCircleOutlined /> },
    suspended: { label: '暂停', color: 'orange', icon: <ClockCircleOutlined /> },
    expired: { label: '过期', color: 'red', icon: <ExclamationCircleOutlined /> },
    maintenance: { label: '维护', color: 'blue', icon: <SettingOutlined /> }
  };

  // 获取网站数据
  const fetchWebsites = async () => {
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:3001/api/v1/websites?page=${queryParams.page}&limit=${queryParams.pageSize}`);
      const result = await response.json();

      if (result.success) {
        setWebsites(result.data.websites || []);
      } else {
        // 不使用模拟数据，直接显示错误
        console.error('API调用失败:', result.message);
        setWebsites([]);
        message.error('获取网站数据失败: ' + result.message);
      }
    } catch (error) {
      console.error('获取网站数据失败:', error);
      message.error('获取网站数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取平台选项
  const fetchPlatforms = async () => {
    try {
      // 从设置API获取平台类型
      const response = await fetch('http://localhost:3001/api/v1/settings/platforms');
      const result = await response.json();
      if (result.success) {
        // 处理新的API数据格式：对象数组转换为字符串数组
        const platforms = result.data || [];
        if (platforms.length > 0 && typeof platforms[0] === 'object') {
          // 新格式：对象数组，只取活跃的平台
          const activePlatforms = platforms
            .filter((platform: any) => platform.isActive)
            .map((platform: any) => platform.name);
          setPlatformOptions(activePlatforms);
        } else {
          // 旧格式：字符串数组
          setPlatformOptions(platforms);
        }
      } else {
        console.error('获取平台选项失败:', result.message);
        // 使用默认平台
        setPlatformOptions(['WordPress', 'Shopify', 'Magento', 'WooCommerce', '自定义']);
      }
    } catch (error) {
      console.error('获取平台选项失败:', error);
      // 使用默认平台
      setPlatformOptions(['WordPress', 'Shopify', 'Magento', 'WooCommerce', '自定义']);
    }
  };

  useEffect(() => {
    fetchWebsites();
    fetchPlatforms();
    console.log('增强网站管理页面已加载，导入功能可用');
  }, [queryParams]);

  // 处理URL参数高亮
  useEffect(() => {
    const websiteId = searchParams.get('id');
    if (websiteId && websites.length > 0) {
      // 延迟执行以确保表格已渲染
      setTimeout(() => {
        const targetRow = document.querySelector(`[data-row-key="${websiteId}"]`);
        if (targetRow) {
          targetRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
          targetRow.classList.add('highlighted-row');
          // 3秒后移除高亮
          setTimeout(() => {
            targetRow.classList.remove('highlighted-row');
          }, 3000);
        }
      }, 500);
    }
  }, [searchParams, websites]);

  // 检查SSL证书
  const checkSSL = async (websiteId: number) => {
    try {
      message.loading('正在检查SSL证书...', 0);

      const response = await fetch(`http://localhost:3001/api/v1/websites/${websiteId}/check-ssl`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();
      message.destroy();

      if (result.success) {
        message.success('SSL证书检查完成');
        fetchWebsites();
      } else {
        message.error(result.message || 'SSL检查失败');
      }
    } catch (error) {
      message.destroy();
      message.error('SSL检查失败');
    }
  };

  // 检查域名信息
  const checkDomain = async (websiteId: number) => {
    // 防重复点击保护
    const website = websites.find(w => w.id === websiteId);
    const domainName = website?.domain || `ID:${websiteId}`;

    try {
      const loadingMessage = message.loading(`正在查询域名信息 ${domainName}...`, 0);

      const response = await fetch(`http://localhost:3001/api/v1/websites/${websiteId}/domain-check`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();
      loadingMessage();

      if (result.success) {
        message.success(`✅ ${domainName} 域名信息更新完成`);
        fetchWebsites();
      } else {
        message.error(`❌ ${domainName} 域名查询失败: ${result.message || '未知错误'}`);
      }
    } catch (error) {
      console.error(`❌ 域名查询异常: ${domainName}`, error);

      // 检查是否是429错误（重复请求）
      if (error.response?.status === 429) {
        message.warning(`⚠️ ${domainName} 域名检测正在进行中，请稍候...`);
      } else {
        message.error(`❌ ${domainName} 域名查询失败: 网络错误`);
      }
    }
  };

  // 性能测试
  const performanceTest = async (websiteId: number) => {
    try {
      message.loading('正在进行性能测试...', 0);

      const response = await fetch(`http://localhost:3001/api/v1/websites/${websiteId}/check-performance`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();
      message.destroy();

      if (result.success) {
        message.success('性能测试完成');
        fetchWebsites();
      } else {
        message.error(result.message || '性能测试失败');
      }
    } catch (error) {
      message.destroy();
      message.error('性能测试失败');
    }
  };

  // 安全扫描
  const securityScan = async (websiteId: number) => {
    try {
      message.loading('正在进行安全扫描...', 0);
      setTimeout(() => {
        message.destroy();
        message.success('安全扫描完成');
        fetchWebsites();
      }, 5000);
    } catch (error) {
      message.error('安全扫描失败');
    }
  };

  // 处理凭据管理
  const handleCredentials = (website: Website) => {
    setCurrentWebsite(website);
    setCredentialVisible(true);
  };

  // 获取SSL状态
  const getSSLStatus = (sslInfo?: SSLInfo) => {
    if (!sslInfo) {
      return <Tag color="red">无SSL</Tag>;
    }
    
    if (sslInfo.daysUntilExpiry <= 7) {
      return <Tag color="red">即将过期</Tag>;
    } else if (sslInfo.daysUntilExpiry <= 30) {
      return <Tag color="orange">即将过期</Tag>;
    } else {
      return <Tag color="green">正常</Tag>;
    }
  };

  // 获取域名状态
  const getDomainStatus = (domainInfo?: DomainInfo) => {
    if (!domainInfo) {
      return <Tag color="red">未知</Tag>;
    }

    // 检查是否过期 - 基于DNS服务器
    const expiredDnsPattern = /EXPIRENS\d*\.HICHINA\.COM/i;
    const hasExpiredDns = domainInfo.nameServers?.some(ns => expiredDnsPattern.test(ns));

    if (hasExpiredDns || (domainInfo.daysUntilExpiry !== null && domainInfo.daysUntilExpiry <= 0)) {
      return <Tag color="red">已过期</Tag>;
    } else if (domainInfo.daysUntilExpiry <= 30) {
      return <Tag color="red">即将过期</Tag>;
    } else if (domainInfo.daysUntilExpiry <= 90) {
      return <Tag color="orange">即将过期</Tag>;
    } else {
      return <Tag color="green">正常</Tag>;
    }
  };

  // 显示域名详细信息弹窗
  const showDomainDetails = (domainInfo?: DomainInfo, websiteName?: string) => {
    if (!domainInfo) {
      message.warning('暂无域名信息');
      return;
    }

    // 检查是否过期
    const expiredDnsPattern = /EXPIRENS\d*\.HICHINA\.COM/i;
    const hasExpiredDns = domainInfo.nameServers?.some(ns => expiredDnsPattern.test(ns));
    const isExpired = hasExpiredDns || (domainInfo.daysUntilExpiry !== null && domainInfo.daysUntilExpiry <= 0);

    Modal.info({
      title: `域名信息 - ${websiteName || domainInfo.registrar}`,
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <div style={{ marginBottom: 16 }}>
            <strong>注册商：</strong> {domainInfo.registrar}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>注册日期：</strong> {domainInfo.registrationDate}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>到期日期：</strong>
            <span style={{ color: isExpired ? '#ff4d4f' : domainInfo.daysUntilExpiry <= 90 ? '#fa8c16' : '#52c41a', marginLeft: 8 }}>
              {domainInfo.expirationDate}
            </span>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>距离到期：</strong>
            <span style={{ color: isExpired ? '#ff4d4f' : domainInfo.daysUntilExpiry <= 90 ? '#fa8c16' : '#52c41a', marginLeft: 8 }}>
              {isExpired ? '已过期' : `${domainInfo.daysUntilExpiry}天`}
            </span>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>DNS服务器：</strong>
            <div style={{ marginTop: 8 }}>
              {domainInfo.nameServers?.map((ns, index) => (
                <div key={index} style={{
                  padding: '4px 8px',
                  backgroundColor: expiredDnsPattern.test(ns) ? '#fff2f0' : '#f6ffed',
                  border: `1px solid ${expiredDnsPattern.test(ns) ? '#ffccc7' : '#d9f7be'}`,
                  borderRadius: 4,
                  marginBottom: 4,
                  color: expiredDnsPattern.test(ns) ? '#ff4d4f' : '#52c41a'
                }}>
                  {ns}
                  {expiredDnsPattern.test(ns) && <span style={{ marginLeft: 8, fontSize: '12px' }}>(过期DNS)</span>}
                </div>
              )) || <span style={{ color: '#999' }}>暂无DNS服务器信息</span>}
            </div>
          </div>
          {hasExpiredDns && (
            <div style={{
              padding: 12,
              backgroundColor: '#fff2f0',
              border: '1px solid #ffccc7',
              borderRadius: 6,
              marginTop: 16
            }}>
              <div style={{ color: '#ff4d4f', fontWeight: 'bold', marginBottom: 8 }}>
                ⚠️ 域名已过期
              </div>
              <div style={{ fontSize: '14px', color: '#666' }}>
                检测到DNS服务器包含过期标识（EXPIRENS*.HICHINA.COM），域名已被注册商暂停解析。
                请及时续费以恢复域名服务。
              </div>
            </div>
          )}
        </div>
      ),
      okText: '确定'
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '网站信息',
      dataIndex: 'siteUrl',
      key: 'siteUrl',
      width: 200,
      render: (url: string, record: Website) => {
        // 获取访问状态显示
        const getAccessStatusDisplay = () => {
          const statusCode = record.accessStatusCode;
          if (!statusCode || statusCode === 0) {
            return <Tag size="small" color="gray">未检测</Tag>;
          } else if (statusCode >= 200 && statusCode < 300) {
            return <Tag size="small" color="green">正常</Tag>;
          } else {
            return <Tag size="small" color="red">{statusCode}</Tag>;
          }
        };

        return (
          <div>
            <div className="flex items-center">
              <Avatar
                size="small"
                src={`https://www.google.com/s2/favicons?domain=${record.domain}`}
                className="mr-2"
              />
              <a href={url} target="_blank" rel="noopener noreferrer" className="font-medium">
                {record.domain}
              </a>
            </div>
            <div className="text-sm text-gray-500 mt-1 flex items-center space-x-2">
              <Tag size="small" color="blue">{record.platform?.name}</Tag>
              {getAccessStatusDisplay()}
            </div>
          </div>
        );
      }
    },
    {
      title: '服务器',
      dataIndex: 'server',
      key: 'server',
      width: 150,
      render: (server: Server) => (
        <div>
          <div className="font-medium">{server?.name}</div>
          <div className="text-sm text-gray-500">{server?.location}</div>
          <div className="text-xs text-gray-400">{server?.ipAddress}</div>
        </div>
      )
    },
    {
      title: '访问状态',
      dataIndex: 'accessStatusCode',
      key: 'accessStatusCode',
      width: 120,
      render: (statusCode: number, record: Website) => {
        // 根据数据库中的访问状态码显示
        const getStatusBadge = () => {
          if (!statusCode || statusCode === 0) {
            return <Badge status="default" text="未检测" />;
          } else if (statusCode >= 200 && statusCode < 300) {
            return <Badge status="success" text="正常" />;
          } else if (statusCode >= 300 && statusCode < 400) {
            return <Badge status="warning" text={`${statusCode}`} />;
          } else if (statusCode >= 400 && statusCode < 500) {
            return <Badge status="error" text={`${statusCode}`} />;
          } else if (statusCode >= 500) {
            return <Badge status="error" text={`${statusCode}`} />;
          } else {
            return <Badge status="default" text={`${statusCode}`} />;
          }
        };

        return (
          <div>
            {getStatusBadge()}
            {record.responseTime && (
              <div className="text-xs text-gray-500">
                {record.responseTime}ms
              </div>
            )}
            {record.lastCheckTime && (
              <div className="text-xs text-gray-400">
                {new Date(record.lastCheckTime).toLocaleString('zh-CN', {
                  month: 'numeric',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
            )}
          </div>
        );
      }
    },
    {
      title: 'SSL证书',
      dataIndex: 'sslStatus',
      key: 'sslStatus',
      width: 120,
      render: (sslStatus: string, record: Website) => {
        // 根据数据库中的SSL状态显示
        const getSSLStatusDisplay = () => {
          if (sslStatus === 'valid') {
            return <Tag color="green">有效</Tag>;
          } else if (sslStatus === 'expired') {
            return <Tag color="red">已过期</Tag>;
          } else if (sslStatus === 'expiring_soon') {
            return <Tag color="orange">即将过期</Tag>;
          } else if (sslStatus === 'invalid') {
            return <Tag color="red">无效</Tag>;
          } else if (sslStatus === 'unknown') {
            return <Tag color="gray">未检测</Tag>;
          } else {
            return <Tag color="gray">未检测</Tag>;
          }
        };

        // 计算剩余天数
        const getDaysUntilExpiry = () => {
          if (record.sslExpireDate) {
            const expireDate = new Date(record.sslExpireDate);
            const now = new Date();
            const diffTime = expireDate.getTime() - now.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            return diffDays;
          }
          return null;
        };

        const daysUntilExpiry = getDaysUntilExpiry();

        return (
          <div>
            {getSSLStatusDisplay()}
            {daysUntilExpiry !== null && (
              <div className="text-xs text-gray-500">
                {daysUntilExpiry > 0 ? `${daysUntilExpiry}天后过期` : '已过期'}
              </div>
            )}
            {record.sslIssuer && (
              <div className="text-xs text-gray-400 truncate" title={record.sslIssuer}>
                {record.sslIssuer}
              </div>
            )}
            <Button
              type="link"
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => checkSSL(record.id)}
            >
              检查
            </Button>
          </div>
        );
      }
    },
    {
      title: '域名状态',
      dataIndex: 'domainInfo',
      key: 'domainInfo',
      width: 140,
      render: (domainInfo: DomainInfo, record: Website) => (
        <div>
          <div
            style={{ cursor: 'pointer' }}
            onClick={() => showDomainDetails(domainInfo, record.siteName)}
          >
            {getDomainStatus(domainInfo)}
            {domainInfo && (
              <div className="text-xs text-gray-500">
                {domainInfo.daysUntilExpiry !== null ? (
                  domainInfo.daysUntilExpiry <= 0 ? '已过期' : `${domainInfo.daysUntilExpiry}天后过期`
                ) : '点击查看详情'}
              </div>
            )}
          </div>
          <Button
            type="link"
            size="small"
            icon={<ReloadOutlined />}
            onClick={() => checkDomain(record.id)}
            style={{ padding: '0 4px', marginTop: 4 }}
          >
            刷新
          </Button>
        </div>
      )
    },
    {
      title: '性能评分',
      dataIndex: 'performanceScore',
      key: 'performanceScore',
      width: 120,
      render: (performanceScore: number, record: Website) => {
        return (
          <div>
            {performanceScore ? (
              <>
                <Progress
                  type="circle"
                  size={40}
                  percent={performanceScore}
                  strokeColor={performanceScore >= 80 ? '#52c41a' : performanceScore >= 60 ? '#faad14' : '#ff4d4f'}
                />
                <div className="text-xs text-gray-500 mt-1">
                  {record.pageLoadTime ? `${record.pageLoadTime}s` : ''}
                </div>
                {record.performanceLastCheck && (
                  <div className="text-xs text-gray-400">
                    {new Date(record.performanceLastCheck).toLocaleString('zh-CN', {
                      month: 'numeric',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                )}
              </>
            ) : (
              <Button
                type="link"
                size="small"
                icon={<ThunderboltOutlined />}
                onClick={() => performanceTest(record.id)}
              >
                测试
              </Button>
            )}
          </div>
        );
      }
    },
    {
      title: '安全评分',
      dataIndex: 'securityScan',
      key: 'securityScan',
      width: 120,
      render: (scan: any, record: Website) => (
        <div>
          {scan ? (
            <>
              <div className="flex items-center">
                <ShieldOutlined 
                  className={`mr-1 ${scan.securityScore >= 80 ? 'text-green-500' : scan.securityScore >= 60 ? 'text-yellow-500' : 'text-red-500'}`}
                />
                <span className="font-medium">{scan.securityScore}</span>
              </div>
              <div className="text-xs text-gray-500">
                {scan.vulnerabilities.critical + scan.vulnerabilities.high > 0 ? '有风险' : '安全'}
              </div>
            </>
          ) : (
            <Button
              type="link"
              size="small"
              icon={<SecurityScanOutlined />}
              onClick={() => securityScan(record.id)}
            >
              扫描
            </Button>
          )}
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right' as const,
      render: (_, record: Website) => {
        const menu = (
          <Menu>
            {hasPermission('site.website.credential') && (
              <Menu.Item key="credentials" icon={<KeyOutlined />} onClick={() => handleCredentials(record)}>
                账号密码
              </Menu.Item>
            )}
            <Menu.Item key="ssl" icon={<SafetyCertificateOutlined />} onClick={() => checkSSL(record.id)}>
              检查SSL
            </Menu.Item>
            <Menu.Item key="domain" icon={<GlobalOutlined />} onClick={() => checkDomain(record.id)}>
              查询域名
            </Menu.Item>
            <Menu.Item key="performance" icon={<ThunderboltOutlined />} onClick={() => performanceTest(record.id)}>
              性能测试
            </Menu.Item>
            <Menu.Item key="security" icon={<SecurityScanOutlined />} onClick={() => securityScan(record.id)}>
              安全扫描
            </Menu.Item>
          </Menu>
        );

        return (
          <Space size="small">
            <Tooltip title="查看详情">
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => setDetailVisible(true)}
              />
            </Tooltip>
            {hasPermission('site.website.edit') && (
              <Tooltip title="编辑">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => setFormVisible(true)}
                />
              </Tooltip>
            )}
            <Dropdown overlay={menu} trigger={['click']}>
              <Button
                type="text"
                icon={<MoreOutlined />}
              />
            </Dropdown>
          </Space>
        );
      }
    }
  ];

  return (
    <div className="p-6">
      <style>
        {`
          .highlighted-row {
            background-color: #fff7e6 !important;
            border: 2px solid #ffa940 !important;
            transition: all 0.3s ease;
          }
          .highlighted-row:hover {
            background-color: #fff7e6 !important;
          }
        `}
      </style>
      {/* 页面标题和操作 */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">网站管理</h1>
            <p className="text-gray-600 mt-1">管理网站状态、SSL证书、域名信息和性能监控</p>
          </div>
          <Space>
            <Button
              type="default"
              icon={<UploadOutlined />}
              onClick={() => setImportVisible(true)}
              style={{ backgroundColor: '#f0f0f0', borderColor: '#d9d9d9' }}
            >
              Excel导入
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setFormVisible(true)}
              className="bg-blue-500 hover:bg-blue-600"
            >
              新建网站
            </Button>
          </Space>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="总网站数"
              value={websites.length}
              prefix={<GlobalOutlined className="text-blue-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="SSL即将过期"
              value={websites.filter(w => w.sslInfo && w.sslInfo.daysUntilExpiry <= 30).length}
              prefix={<WarningOutlined className="text-orange-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="域名即将过期"
              value={websites.filter(w => w.domainInfo && w.domainInfo.daysUntilExpiry <= 90).length}
              prefix={<ExclamationCircleOutlined className="text-red-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均性能评分"
              value={Math.round(websites.filter(w => w.performanceMetrics).reduce((sum, w) => sum + (w.performanceMetrics?.performanceScore || 0), 0) / websites.filter(w => w.performanceMetrics).length) || 0}
              prefix={<ThunderboltOutlined className="text-green-500" />}
            />
          </Card>
        </Col>
      </Row>

      {/* 筛选区域 */}
      <Card className="mb-4">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} md={6}>
            <Input.Search
              placeholder="搜索网站名称、URL、备注"
              allowClear
              onSearch={(value) => {
                setQueryParams(prev => ({ ...prev, search: value, page: 1 }));
              }}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={8} sm={4} md={3}>
            <Select
              placeholder="网站状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => {
                setQueryParams(prev => ({ ...prev, status: value || '', page: 1 }));
              }}
            >
              <Option value="">显示全部</Option>
              <Option value="active">正常</Option>
              <Option value="inactive">停用</Option>
              <Option value="suspended">暂停</Option>
              <Option value="expired">过期</Option>
            </Select>
          </Col>
          <Col xs={8} sm={4} md={3}>
            <Select
              placeholder="平台类型"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => {
                setQueryParams(prev => ({ ...prev, platform: value || '', page: 1 }));
              }}
            >
              {platformOptions.map(platform => (
                <Option key={platform} value={platform}>{platform}</Option>
              ))}
            </Select>
          </Col>
          <Col xs={8} sm={4} md={3}>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                fetchWebsites();
                fetchPlatforms();
              }}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={websites}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: websites.length,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            pageSizeOptions: ['20', '50', '100', '200', '500', '1000', '10000'],
            defaultPageSize: 50,
            showSizeChangerLabel: (current, size) => {
              if (size >= 10000) return '显示全部';
              return `${size}条/页`;
            }
          }}
        />
      </Card>

      {/* 网站导入弹窗 */}
      <WebsiteImport
        visible={importVisible}
        onCancel={() => setImportVisible(false)}
        onSuccess={() => {
          setImportVisible(false);
          fetchWebsites();
        }}
      />

      {/* 凭据管理模态框 */}
      <Modal
        title="网站账号密码管理"
        open={credentialVisible}
        onCancel={() => setCredentialVisible(false)}
        footer={null}
        width={800}
      >
        {currentWebsite && (
          <div>
            <Alert
              message="安全提示"
              description="所有密码均采用AES-256加密存储，仅授权人员可查看"
              type="info"
              showIcon
              className="mb-4"
            />
            
            <Tabs defaultActiveKey="list">
              <TabPane tab="账号列表" key="list">
                <Table
                  dataSource={currentWebsite.credentials}
                  rowKey="id"
                  size="small"
                  columns={[
                    {
                      title: '账号类型',
                      dataIndex: 'accountType',
                      render: (type: string) => {
                        const typeMap: Record<string, { label: string; color: string }> = {
                          admin: { label: '管理员', color: 'red' },
                          ftp: { label: 'FTP', color: 'blue' },
                          database: { label: '数据库', color: 'green' },
                          hosting: { label: '主机', color: 'orange' },
                          domain: { label: '域名', color: 'purple' },
                          ssl: { label: 'SSL', color: 'cyan' },
                          email: { label: '邮箱', color: 'pink' },
                          other: { label: '其他', color: 'gray' }
                        };
                        const config = typeMap[type] || typeMap.other;
                        return <Tag color={config.color}>{config.label}</Tag>;
                      }
                    },
                    { title: '用户名', dataIndex: 'username' },
                    { 
                      title: '密码', 
                      dataIndex: 'password',
                      render: () => <span>••••••••</span>
                    },
                    { title: '登录地址', dataIndex: 'url' },
                    { title: '描述', dataIndex: 'description' },
                    {
                      title: '操作',
                      render: () => (
                        <Space>
                          <Button type="link" size="small">查看</Button>
                          <Button type="link" size="small">编辑</Button>
                        </Space>
                      )
                    }
                  ]}
                />
              </TabPane>
              <TabPane tab="添加账号" key="add">
                <Form layout="vertical">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="账号类型" required>
                        <Select placeholder="请选择账号类型">
                          <Option value="admin">管理员</Option>
                          <Option value="ftp">FTP</Option>
                          <Option value="database">数据库</Option>
                          <Option value="hosting">主机</Option>
                          <Option value="domain">域名</Option>
                          <Option value="ssl">SSL</Option>
                          <Option value="email">邮箱</Option>
                          <Option value="other">其他</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="用户名" required>
                        <Input placeholder="请输入用户名" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="密码" required>
                        <Input.Password placeholder="请输入密码" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="登录地址">
                        <Input placeholder="请输入登录地址" />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Form.Item label="描述">
                    <TextArea rows={3} placeholder="请输入描述信息" />
                  </Form.Item>
                  <Form.Item>
                    <Button type="primary">添加账号</Button>
                  </Form.Item>
                </Form>
              </TabPane>
            </Tabs>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default EnhancedWebsiteList;
