import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Typography,
  Divider,
  Row,
  Col,
  List,
  Avatar,
  Badge,
  Alert,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  GlobalOutlined,
  LinkOutlined,
  SafetyCertificateOutlined,
  DatabaseOutlined,
  UserOutlined,
  WarningOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

const WebsiteDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // 模拟网站数据
  const website = {
    id: 1,
    projectId: 1,
    project: {
      id: 1,
      projectName: '企业官网建设',
      customer: {
        name: '张三',
        company: '北京科技有限公司',
      },
    },
    platformId: 1,
    platform: { id: 1, name: 'WordPress', description: 'WordPress内容管理系统' },
    serverId: 1,
    server: { 
      id: 1, 
      name: 'Server-01', 
      ipAddress: '*************',
      location: '北京',
      provider: '阿里云',
      status: 'active',
    },
    siteUrl: 'https://example.com',
    domain: 'example.com',
    onlineDate: '2024-01-20',
    expireDate: '2025-01-20',
    renewalFee: 1200,
    accessStatusCode: 200,
    sslExpireDate: '2024-12-20',
    domainExpireDate: '2025-01-20',
    lastCheckTime: '2024-01-22T10:30:00Z',
    status: 'active',
    notes: '企业官网，运行正常，定期备份',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-22T10:30:00Z',
  };

  // 模拟网站账号数据
  const websiteAccounts = [
    {
      id: 1,
      accountType: '管理员',
      username: 'admin',
      email: '<EMAIL>',
      role: 'Administrator',
      loginUrl: 'https://example.com/wp-admin',
      isActive: true,
    },
    {
      id: 2,
      accountType: '编辑',
      username: 'editor',
      email: '<EMAIL>',
      role: 'Editor',
      loginUrl: 'https://example.com/wp-admin',
      isActive: true,
    },
  ];

  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: 'success', text: '正常' },
      inactive: { color: 'default', text: '停用' },
      suspended: { color: 'warning', text: '暂停' },
      expired: { color: 'error', text: '过期' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getAccessStatus = (statusCode?: number) => {
    if (!statusCode) return <Tag color="default">未检测</Tag>;
    
    if (statusCode === 200) {
      return <Tag color="success" icon={<CheckCircleOutlined />}>正常访问</Tag>;
    } else if (statusCode >= 400) {
      return <Tag color="error" icon={<WarningOutlined />}>访问异常</Tag>;
    } else {
      return <Tag color="warning">访问警告</Tag>;
    }
  };

  // 检查SSL是否即将过期
  const checkSSLExpiry = (sslExpireDate?: string) => {
    if (!sslExpireDate) return null;
    
    const expireTime = new Date(sslExpireDate).getTime();
    const now = new Date().getTime();
    const daysLeft = Math.ceil((expireTime - now) / (1000 * 60 * 60 * 24));
    
    if (daysLeft <= 7) {
      return { status: 'error', text: `${daysLeft}天后过期`, alert: true };
    } else if (daysLeft <= 30) {
      return { status: 'warning', text: `${daysLeft}天后过期`, alert: true };
    }
    return { status: 'success', text: '正常', alert: false };
  };

  const sslStatus = checkSSLExpiry(website.sslExpireDate);

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/websites')}
        >
          返回
        </Button>
        <Title level={2} style={{ margin: 0 }}>
          网站详情
        </Title>
      </Space>

      {/* SSL过期警告 */}
      {sslStatus?.alert && (
        <Alert
          message="SSL证书即将过期"
          description={`网站 ${website.domain} 的SSL证书将在 ${sslStatus.text}，请及时续费。`}
          type={sslStatus.status as any}
          showIcon
          closable
          style={{ marginBottom: 16 }}
        />
      )}

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
              <GlobalOutlined style={{ fontSize: 48, color: '#1890ff', marginRight: 16 }} />
              <div style={{ flex: 1 }}>
                <Title level={3} style={{ margin: 0 }}>
                  {website.domain}
                </Title>
                <Text type="secondary">{website.platform.name}</Text>
                <div style={{ marginTop: 8 }}>
                  {getStatusTag(website.status)}
                  {getAccessStatus(website.accessStatusCode)}
                </div>
              </div>
              <div>
                <Space>
                  <Button
                    icon={<LinkOutlined />}
                    onClick={() => window.open(website.siteUrl, '_blank')}
                  >
                    访问网站
                  </Button>
                  <Button
                    type="primary"
                    icon={<EditOutlined />}
                    onClick={() => navigate(`/websites/${id}/edit`)}
                  >
                    编辑网站
                  </Button>
                </Space>
              </div>
            </div>

            <Divider />

            <Descriptions title="基本信息" column={2}>
              <Descriptions.Item label="网站域名">{website.domain}</Descriptions.Item>
              <Descriptions.Item label="网站URL">{website.siteUrl}</Descriptions.Item>
              <Descriptions.Item label="平台类型">{website.platform.name}</Descriptions.Item>
              <Descriptions.Item label="网站状态">{getStatusTag(website.status)}</Descriptions.Item>
              <Descriptions.Item label="上线日期">{website.onlineDate}</Descriptions.Item>
              <Descriptions.Item label="到期日期">{website.expireDate}</Descriptions.Item>
              <Descriptions.Item label="续费金额">¥{website.renewalFee.toLocaleString()}</Descriptions.Item>
              <Descriptions.Item label="访问状态">{getAccessStatus(website.accessStatusCode)}</Descriptions.Item>
            </Descriptions>

            <Divider />

            <Descriptions title="SSL证书信息" column={2}>
              <Descriptions.Item label="SSL状态">
                <Space>
                  <SafetyCertificateOutlined style={{ color: '#1890ff' }} />
                  <Badge status={sslStatus?.status as any} text={sslStatus?.text} />
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="SSL到期时间">{website.sslExpireDate}</Descriptions.Item>
            </Descriptions>

            <Divider />

            <Descriptions title="域名信息" column={2}>
              <Descriptions.Item label="域名">{website.domain}</Descriptions.Item>
              <Descriptions.Item label="域名到期">{website.domainExpireDate}</Descriptions.Item>
            </Descriptions>

            <Divider />

            <Descriptions title="服务器信息" column={2}>
              <Descriptions.Item label="服务器名称">
                <Space>
                  <DatabaseOutlined />
                  {website.server.name}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="IP地址">{website.server.ipAddress}</Descriptions.Item>
              <Descriptions.Item label="服务器位置">{website.server.location}</Descriptions.Item>
              <Descriptions.Item label="服务商">{website.server.provider}</Descriptions.Item>
            </Descriptions>

            <Divider />

            <Descriptions title="关联项目" column={2}>
              <Descriptions.Item label="项目名称">{website.project?.projectName}</Descriptions.Item>
              <Descriptions.Item label="客户信息">
                {website.project?.customer.name} - {website.project?.customer.company}
              </Descriptions.Item>
            </Descriptions>

            {website.notes && (
              <>
                <Divider />
                <div>
                  <Title level={5}>备注信息</Title>
                  <Text>{website.notes}</Text>
                </div>
              </>
            )}

            <Divider />

            <Descriptions title="其他信息" column={2}>
              <Descriptions.Item label="最后检查">
                {website.lastCheckTime ? new Date(website.lastCheckTime).toLocaleString() : '未检查'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(website.createdAt).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {new Date(website.updatedAt).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="网站账号" extra={
            <Button type="link" onClick={() => {/* 管理账号 */}}>
              管理
            </Button>
          }>
            <List
              dataSource={websiteAccounts}
              renderItem={(account) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />}
                    title={
                      <Space>
                        <span>{account.username}</span>
                        <Tag size="small">{account.accountType}</Tag>
                        {account.isActive ? (
                          <Badge status="success" />
                        ) : (
                          <Badge status="default" />
                        )}
                      </Space>
                    }
                    description={
                      <Space direction="vertical" size="small">
                        <Text type="secondary">{account.email}</Text>
                        <Text type="secondary">角色: {account.role}</Text>
                        <Button
                          type="link"
                          size="small"
                          icon={<LinkOutlined />}
                          onClick={() => window.open(account.loginUrl, '_blank')}
                        >
                          登录后台
                        </Button>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>

          <Card title="监控信息" style={{ marginTop: 16 }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="响应时间">150ms</Descriptions.Item>
              <Descriptions.Item label="可用性">99.9%</Descriptions.Item>
              <Descriptions.Item label="最后故障">无</Descriptions.Item>
              <Descriptions.Item label="备份状态">正常</Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default WebsiteDetail;
