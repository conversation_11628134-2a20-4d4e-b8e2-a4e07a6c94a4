import React, { useState } from 'react';
import { Card, Button, Table, Tag, Progress, Statistic, Row, Col, Switch, message, Space, Typography } from 'antd';
import { PlayCircleOutlined, ThunderboltOutlined, ClockCircleOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import axios from 'axios';

const { Title, Text } = Typography;

interface CheckResult {
  websiteId: number;
  siteName: string;
  domain: string;
  url: string;
  statusCode: number;
  responseTime: number;
  isAccessible: boolean;
  lastCheckTime: string;
  error?: string;
}

interface BatchCheckData {
  total: number;
  successCount: number;
  failCount: number;
  totalTime: number;
  avgTimePerSite: number;
  sitesPerSecond: number;
  useWorkerThreads: boolean;
  results: CheckResult[];
}

const BatchCheck: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [useWorkerThreads, setUseWorkerThreads] = useState(true);
  const [checkData, setCheckData] = useState<BatchCheckData | null>(null);

  const handleBatchCheck = async () => {
    setLoading(true);
    try {
      const response = await axios.post('/api/v1/websites/batch-check', {
        useWorkerThreads
      });

      if (response.data.success) {
        setCheckData(response.data.data);
        message.success(`批量检测完成！检测了 ${response.data.data.total} 个网站`);
      } else {
        message.error(response.data.message || '批量检测失败');
      }
    } catch (error: any) {
      console.error('批量检测失败:', error);
      message.error('批量检测失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: '网站名称',
      dataIndex: 'siteName',
      key: 'siteName',
      width: 200,
    },
    {
      title: '域名',
      dataIndex: 'domain',
      key: 'domain',
      width: 200,
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (record: CheckResult) => (
        <Tag color={record.isAccessible ? 'green' : 'red'} icon={record.isAccessible ? <CheckCircleOutlined /> : <CloseCircleOutlined />}>
          {record.isAccessible ? '正常' : `${record.statusCode || '无响应'}`}
        </Tag>
      ),
    },
    {
      title: '响应时间',
      dataIndex: 'responseTime',
      key: 'responseTime',
      width: 120,
      render: (time: number) => (
        <Text type={time > 3000 ? 'danger' : time > 1000 ? 'warning' : 'success'}>
          {time}ms
        </Text>
      ),
    },
    {
      title: '检测时间',
      dataIndex: 'lastCheckTime',
      key: 'lastCheckTime',
      width: 180,
      render: (time: string) => new Date(time).toLocaleString(),
    },
    {
      title: '错误信息',
      dataIndex: 'error',
      key: 'error',
      render: (error: string) => error ? <Text type="danger">{error}</Text> : '-',
    },
  ];

  const getPerformanceColor = (sitesPerSecond: number) => {
    if (sitesPerSecond >= 30) return '#52c41a'; // 绿色 - 优秀
    if (sitesPerSecond >= 20) return '#faad14'; // 黄色 - 良好
    if (sitesPerSecond >= 10) return '#fa8c16'; // 橙色 - 一般
    return '#f5222d'; // 红色 - 较慢
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Title level={2}>
            <ThunderboltOutlined /> 高性能网站批量检测
          </Title>
          <Text type="secondary">
            测试多线程网站存活检测功能，目标：30秒内检测完1000个站点
          </Text>
        </div>

        <div style={{ marginBottom: '24px' }}>
          <Space size="large">
            <div>
              <Text strong>检测模式：</Text>
              <Switch
                checked={useWorkerThreads}
                onChange={setUseWorkerThreads}
                checkedChildren="Worker Threads"
                unCheckedChildren="传统并发"
                disabled={loading}
              />
            </div>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              loading={loading}
              onClick={handleBatchCheck}
              size="large"
            >
              开始批量检测
            </Button>
          </Space>
        </div>

        {checkData && (
          <>
            <Row gutter={16} style={{ marginBottom: '24px' }}>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="总网站数"
                    value={checkData.total}
                    prefix={<ClockCircleOutlined />}
                  />
                </Card>
              </Col>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="成功检测"
                    value={checkData.successCount}
                    valueStyle={{ color: '#3f8600' }}
                    prefix={<CheckCircleOutlined />}
                  />
                </Card>
              </Col>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="检测失败"
                    value={checkData.failCount}
                    valueStyle={{ color: '#cf1322' }}
                    prefix={<CloseCircleOutlined />}
                  />
                </Card>
              </Col>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="总耗时"
                    value={Math.round(checkData.totalTime / 1000)}
                    suffix="秒"
                    valueStyle={{ color: checkData.totalTime <= 30000 ? '#3f8600' : '#cf1322' }}
                  />
                </Card>
              </Col>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="平均耗时"
                    value={checkData.avgTimePerSite}
                    suffix="ms/站点"
                  />
                </Card>
              </Col>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="处理速度"
                    value={checkData.sitesPerSecond}
                    suffix="站点/秒"
                    valueStyle={{ color: getPerformanceColor(checkData.sitesPerSecond) }}
                    prefix={<ThunderboltOutlined />}
                  />
                </Card>
              </Col>
            </Row>

            <Row gutter={16} style={{ marginBottom: '24px' }}>
              <Col span={12}>
                <Card title="成功率">
                  <Progress
                    percent={Math.round((checkData.successCount / checkData.total) * 100)}
                    status={checkData.successCount === checkData.total ? 'success' : 'active'}
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card title="性能评估">
                  <div>
                    <Tag color={checkData.useWorkerThreads ? 'blue' : 'orange'}>
                      {checkData.useWorkerThreads ? 'Worker Threads模式' : '传统并发模式'}
                    </Tag>
                    <br />
                    <Text>
                      {checkData.sitesPerSecond >= 30 ? '🚀 性能优秀' :
                       checkData.sitesPerSecond >= 20 ? '⚡ 性能良好' :
                       checkData.sitesPerSecond >= 10 ? '🔥 性能一般' : '🐌 性能较慢'}
                    </Text>
                    <br />
                    <Text type="secondary">
                      {checkData.totalTime <= 30000 ? 
                        `✅ 达成目标：${Math.round(checkData.totalTime / 1000)}秒内完成检测` :
                        `❌ 未达目标：耗时${Math.round(checkData.totalTime / 1000)}秒`
                      }
                    </Text>
                  </div>
                </Card>
              </Col>
            </Row>

            <Card title="检测结果详情" style={{ marginTop: '24px' }}>
              <Table
                columns={columns}
                dataSource={checkData.results}
                rowKey="websiteId"
                pagination={{
                  pageSize: 20,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                }}
                scroll={{ x: 1200 }}
                size="small"
              />
            </Card>
          </>
        )}
      </Card>
    </div>
  );
};

export default BatchCheck;
