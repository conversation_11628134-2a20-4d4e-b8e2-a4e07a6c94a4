import React, { useState } from 'react';
import { Card, Button, Space, Tag, Modal, message } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

// 模拟域名信息数据
const mockDomainData = [
  {
    id: 1,
    siteName: '正常域名网站',
    domain: 'pcmannequins.com',
    domainInfo: {
      registrar: 'GoDaddy',
      registrationDate: '2020-01-01',
      expirationDate: '2026-01-01',
      daysUntilExpiry: 163,
      nameServers: ['dns17.hichina.com', 'dns18.hichina.com'],
      status: 'active',
      isExpired: false,
      lastChecked: '2025-07-21T08:45:44.788Z'
    }
  },
  {
    id: 2,
    siteName: '过期域名网站',
    domain: 'expired-test.com',
    domainInfo: {
      registrar: 'GoDaddy',
      registrationDate: '2020-01-01',
      expirationDate: '2026-01-01',
      daysUntilExpiry: 163,
      nameServers: ['EXPIRENS3.HICHINA.COM', 'EXPIRENS4.HICHINA.COM'],
      status: 'expired',
      isExpired: true,
      lastChecked: '2025-07-21T08:45:44.788Z'
    }
  },
  {
    id: 3,
    siteName: '即将过期域名',
    domain: 'expiring-soon.com',
    domainInfo: {
      registrar: 'Namecheap',
      registrationDate: '2023-01-01',
      expirationDate: '2025-08-15',
      daysUntilExpiry: 25,
      nameServers: ['dns1.namecheap.com', 'dns2.namecheap.com'],
      status: 'active',
      isExpired: false,
      lastChecked: '2025-07-21T08:45:44.788Z'
    }
  }
];

interface DomainInfo {
  registrar: string;
  registrationDate: string;
  expirationDate: string;
  daysUntilExpiry: number | null;
  nameServers: string[];
  status?: 'active' | 'expired' | 'suspended' | 'unknown';
  isExpired?: boolean;
  lastChecked: string;
}

const DomainStatusTest: React.FC = () => {
  const [loading, setLoading] = useState(false);

  // 获取域名状态标签
  const getDomainStatus = (domainInfo?: DomainInfo) => {
    if (!domainInfo) {
      return <Tag color="red">未知</Tag>;
    }
    
    // 检查是否过期 - 基于DNS服务器
    const expiredDnsPattern = /EXPIRENS\d*\.HICHINA\.COM/i;
    const hasExpiredDns = domainInfo.nameServers?.some(ns => expiredDnsPattern.test(ns));
    
    if (hasExpiredDns || (domainInfo.daysUntilExpiry !== null && domainInfo.daysUntilExpiry <= 0)) {
      return <Tag color="red">已过期</Tag>;
    } else if (domainInfo.daysUntilExpiry !== null && domainInfo.daysUntilExpiry <= 30) {
      return <Tag color="red">即将过期</Tag>;
    } else if (domainInfo.daysUntilExpiry !== null && domainInfo.daysUntilExpiry <= 90) {
      return <Tag color="orange">即将过期</Tag>;
    } else {
      return <Tag color="green">正常</Tag>;
    }
  };

  // 显示域名详细信息弹窗
  const showDomainDetails = (domainInfo?: DomainInfo, websiteName?: string) => {
    if (!domainInfo) {
      message.warning('暂无域名信息');
      return;
    }

    // 检查是否过期
    const expiredDnsPattern = /EXPIRENS\d*\.HICHINA\.COM/i;
    const hasExpiredDns = domainInfo.nameServers?.some(ns => expiredDnsPattern.test(ns));
    const isExpired = hasExpiredDns || (domainInfo.daysUntilExpiry !== null && domainInfo.daysUntilExpiry <= 0);

    Modal.info({
      title: `域名信息 - ${websiteName || domainInfo.registrar}`,
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <div style={{ marginBottom: 16 }}>
            <strong>注册商：</strong> {domainInfo.registrar}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>注册日期：</strong> {domainInfo.registrationDate}
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>到期日期：</strong> 
            <span style={{ color: isExpired ? '#ff4d4f' : domainInfo.daysUntilExpiry && domainInfo.daysUntilExpiry <= 90 ? '#fa8c16' : '#52c41a', marginLeft: 8 }}>
              {domainInfo.expirationDate}
            </span>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>距离到期：</strong> 
            <span style={{ color: isExpired ? '#ff4d4f' : domainInfo.daysUntilExpiry && domainInfo.daysUntilExpiry <= 90 ? '#fa8c16' : '#52c41a', marginLeft: 8 }}>
              {isExpired ? '已过期' : `${domainInfo.daysUntilExpiry}天`}
            </span>
          </div>
          <div style={{ marginBottom: 16 }}>
            <strong>DNS服务器：</strong>
            <div style={{ marginTop: 8 }}>
              {domainInfo.nameServers?.map((ns, index) => (
                <div key={index} style={{ 
                  padding: '4px 8px', 
                  backgroundColor: expiredDnsPattern.test(ns) ? '#fff2f0' : '#f6ffed',
                  border: `1px solid ${expiredDnsPattern.test(ns) ? '#ffccc7' : '#d9f7be'}`,
                  borderRadius: 4,
                  marginBottom: 4,
                  color: expiredDnsPattern.test(ns) ? '#ff4d4f' : '#52c41a'
                }}>
                  {ns}
                  {expiredDnsPattern.test(ns) && <span style={{ marginLeft: 8, fontSize: '12px' }}>(过期DNS)</span>}
                </div>
              )) || <span style={{ color: '#999' }}>暂无DNS服务器信息</span>}
            </div>
          </div>
          {hasExpiredDns && (
            <div style={{ 
              padding: 12, 
              backgroundColor: '#fff2f0', 
              border: '1px solid #ffccc7', 
              borderRadius: 6,
              marginTop: 16
            }}>
              <div style={{ color: '#ff4d4f', fontWeight: 'bold', marginBottom: 8 }}>
                ⚠️ 域名已过期
              </div>
              <div style={{ fontSize: '14px', color: '#666' }}>
                检测到DNS服务器包含过期标识（EXPIRENS*.HICHINA.COM），域名已被注册商暂停解析。
                请及时续费以恢复域名服务。
              </div>
            </div>
          )}
        </div>
      ),
      okText: '确定'
    });
  };

  const checkDomain = async (id: number) => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('域名检查完成');
    } catch (error) {
      message.error('域名检查失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <h2>域名状态测试页面</h2>
      <p>这个页面用于测试域名状态显示和点击弹窗功能</p>
      
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {mockDomainData.map(website => (
          <Card key={website.id} title={website.siteName} style={{ width: 600 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <div style={{ marginBottom: 8 }}>
                  <strong>域名：</strong> {website.domain}
                </div>
                <div 
                  style={{ cursor: 'pointer' }}
                  onClick={() => showDomainDetails(website.domainInfo, website.siteName)}
                >
                  {getDomainStatus(website.domainInfo)}
                  {website.domainInfo && (
                    <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                      {website.domainInfo.daysUntilExpiry !== null ? (
                        website.domainInfo.daysUntilExpiry <= 0 ? '已过期' : `${website.domainInfo.daysUntilExpiry}天后过期`
                      ) : '点击查看详情'}
                    </div>
                  )}
                </div>
              </div>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={() => checkDomain(website.id)}
                loading={loading}
              >
                检查域名
              </Button>
            </div>
          </Card>
        ))}
      </Space>
    </div>
  );
};

export default DomainStatusTest;
