import React, { useState } from 'react';
import { Card, Button, Space, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import WebsiteForm from '../../components/Website/WebsiteForm';
import { Website } from '../../types';

const TestWebsiteForm: React.FC = () => {
  const [formVisible, setFormVisible] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [currentWebsite, setCurrentWebsite] = useState<Website | undefined>();

  const handleCreate = () => {
    setFormMode('create');
    setCurrentWebsite(undefined);
    setFormVisible(true);
  };

  const handleFormSuccess = (data: any) => {
    console.log('表单提交成功:', data);
    message.success('网站创建成功！');
    setFormVisible(false);
  };

  const handleFormCancel = () => {
    setFormVisible(false);
  };

  return (
    <div className="p-6">
      <Card title="测试新建网站功能">
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            新建网站
          </Button>
        </Space>

        <div className="mt-4">
          <h3>说明：</h3>
          <ul>
            <li>点击"新建网站"按钮打开表单</li>
            <li>表单中的"域名"字段已改为"站点名称"</li>
            <li>站点名称示例：企业官网、电商平台、技术博客等</li>
            <li>填写完成后点击提交测试功能</li>
          </ul>
        </div>

        {/* 网站表单 */}
        <WebsiteForm
          visible={formVisible}
          mode={formMode}
          initialValues={currentWebsite}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      </Card>
    </div>
  );
};

export default TestWebsiteForm;
