import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  App,
  Row,
  Col,
  Statistic,
  Badge,
  Avatar,
  Progress,
  Modal,
  Descriptions,
  Popconfirm,
  Dropdown,
  Menu,
  Typography,
  Alert,
  Spin,
  Input
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  GlobalOutlined,
  SafetyCertificateOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  KeyOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  SecurityScanOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  MoreOutlined,
  ScanOutlined,
  DashboardOutlined,
  LinkOutlined,
  InfoCircleOutlined,
  SyncOutlined,
  UploadOutlined,
  DownloadOutlined,
  SearchOutlined,
  ClearOutlined
} from '@ant-design/icons';
import { Website, Platform, Server, WebsiteCredential, SSLInfo, DomainInfo, QueryParams } from '../../types';
import WebsiteForm from '../../components/Website/WebsiteForm';
import PasswordManager from '../../components/Website/PasswordManager';
import WebsiteImport from '../../components/Website/WebsiteImport';
import { WebsiteApi } from '../../services/website';
import { ServerApi } from '../../services/server';
import useAdvancedSearch from '../../hooks/useAdvancedSearch';
import AdvancedSearchInput from '../../components/AdvancedSearch/AdvancedSearchInput';
import { usePermissions } from '../../contexts/PermissionContext';

interface SimpleEnhancedWebsiteListProps {}

const SimpleEnhancedWebsiteList: React.FC<SimpleEnhancedWebsiteListProps> = () => {
  const { message } = App.useApp();
  const { hasPermission } = usePermissions();
  const [websites, setWebsites] = useState<Website[]>([]);
  const [loading, setLoading] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [currentWebsite, setCurrentWebsite] = useState<Website | undefined>();
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailWebsite, setDetailWebsite] = useState<Website | undefined>();
  const [passwordManagerVisible, setPasswordManagerVisible] = useState(false);
  const [importVisible, setImportVisible] = useState(false);
  const [currentPasswordWebsite, setCurrentPasswordWebsite] = useState<Website | undefined>();
  // 高级搜索配置
  const searchFields = [
    'siteName',
    'domain',
    'siteUrl',
    'notes',
    'platform.name',
    'server.name',
    'server.location',
    'server.provider',
    'status'
  ];

  const {
    searchText,
    updateSearchText,
    clearSearch,
    searchResult,
    highlightText,
    applySuggestion,
    isSearching
  } = useAdvancedSearch(websites, searchFields, {
    multiKeyword: true,
    keywordLogic: 'AND',
    caseSensitive: false,
    debounceMs: 300,
    highlightMatch: true
  });
  const [servers, setServers] = useState<any[]>([]);
  const [platforms, setPlatforms] = useState<any[]>([]);
  const [sslChecking, setSslChecking] = useState<Record<number, boolean>>({});
  const [domainChecking, setDomainChecking] = useState<Record<number, boolean>>({});
  const [performanceChecking, setPerformanceChecking] = useState<Record<number, boolean>>({});
  const [checkingAccess, setCheckingAccess] = useState(false);
  const [checkingSSL, setCheckingSSL] = useState(false);
  const [accessChecking, setAccessChecking] = useState<Record<number, boolean>>({});
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10
  });

  // 状态配置
  const statusConfig = {
    active: { label: '正常', color: 'green', icon: <CheckCircleOutlined /> },
    inactive: { label: '停用', color: 'red', icon: <ExclamationCircleOutlined /> },
    suspended: { label: '暂停', color: 'orange', icon: <ClockCircleOutlined /> },
    expired: { label: '过期', color: 'red', icon: <ExclamationCircleOutlined /> },
    maintenance: { label: '维护', color: 'blue', icon: <SettingOutlined /> }
  };

  // 获取网站数据
  const fetchWebsites = async () => {
    setLoading(true);
    try {
      // 从API获取真实数据
      const response = await WebsiteApi.getWebsites(queryParams);
      console.log('API响应:', response);

      // 正确解析API响应数据 - 根据后端API格式
      let websitesData = [];
      if (response.data && response.data.websites && Array.isArray(response.data.websites)) {
        websitesData = response.data.websites;
      } else {
        console.warn('API响应格式不正确:', response);
        websitesData = [];
      }

      console.log('解析后的网站数据:', websitesData);

      // 确保websitesData是数组
      if (!Array.isArray(websitesData)) {
        console.warn('网站数据不是数组格式:', websitesData);
        websitesData = [];
      }

      // 直接使用数据库中的真实数据，不添加模拟数据
      const enhancedWebsites = websitesData;

      setWebsites(enhancedWebsites);
    } catch (error) {
      console.error('获取网站数据失败:', error);
      message.error('获取网站数据失败');
      // 如果API失败，使用空数组
      setWebsites([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取服务器选项
  const fetchServers = async () => {
    try {
      const response = await ServerApi.getServerOptions();
      if (response.success) {
        setServers(response.data);
      }
    } catch (error) {
      console.error('获取服务器选项失败:', error);
      // 使用模拟数据
      setServers([
        { id: 1, name: '阿里云ECS-1', location: '华东1', ipAddress: '************', provider: '阿里云', status: 'active' },
        { id: 2, name: '腾讯云CVM-1', location: '华南1', ipAddress: '*************', provider: '腾讯云', status: 'active' },
        { id: 3, name: '华为云ECS-1', location: '华北1', ipAddress: '121.36.345.67', provider: '华为云', status: 'active' },
        { id: 4, name: 'Web-Server-01', location: '北京-阿里云', ipAddress: '*************', provider: '阿里云', status: 'active' }
      ]);
    }
  };

  // 获取平台选项
  const fetchPlatforms = async () => {
    try {
      const response = await WebsiteApi.getPlatforms();
      if (response.success) {
        setPlatforms(response.data);
      }
    } catch (error) {
      console.error('获取平台选项失败:', error);
      // 使用模拟数据
      setPlatforms([
        { id: 1, name: 'WordPress' },
        { id: 2, name: 'WooCommerce' },
        { id: 3, name: 'Shopify' },
        { id: 4, name: 'Laravel' },
        { id: 5, name: 'Next.js' }
      ]);
    }
  };

  useEffect(() => {
    fetchWebsites();
    fetchServers();
    fetchPlatforms();
  }, [queryParams]);

  // 搜索过滤现在由 useAdvancedSearch Hook 自动处理

  // 处理新建网站
  const handleCreate = () => {
    setFormMode('create');
    setCurrentWebsite(undefined);
    setFormVisible(true);
  };

  // 处理表单成功
  const handleFormSuccess = async (formData?: any) => {
    // 由于使用模拟数据，我们需要手动更新本地数据
    if (formData && formMode === 'edit' && currentWebsite) {
      // 编辑模式：更新现有网站数据
      const updatedWebsites = websites.map(website => {
        if (website.id === currentWebsite.id) {
          // 构建更新后的网站对象
          const updatedWebsite = {
            ...website,
            siteName: formData.siteName || website.siteName,
            siteUrl: formData.siteUrl || website.siteUrl,
            industry: formData.industry || website.industry,
            status: formData.status || website.status,
            onlineDate: formData.onlineDate || website.onlineDate,
            expireDate: formData.expireDate || website.expireDate,
            projectAmount: formData.projectAmount || website.projectAmount,
            renewalFee: formData.renewalFee || website.renewalFee,
            notes: formData.notes || website.notes,
            serverId: formData.serverId || website.serverId,
            platformId: formData.platformId || website.platformId,
            updatedAt: new Date().toISOString()
          };

          // 如果服务器ID发生变化，需要更新服务器信息
          if (formData.serverId && formData.serverId !== website.serverId) {
            // 从真实服务器数据中查找对应的服务器
            const selectedServer = servers.find(s => s.id === formData.serverId);
            if (selectedServer) {
              updatedWebsite.server = selectedServer;
            }
          }

          // 如果平台ID发生变化，需要更新平台信息
          if (formData.platformId && formData.platformId !== website.platformId) {
            // 从真实平台数据中查找对应的平台
            const selectedPlatform = platforms.find(p => p.id === formData.platformId);
            if (selectedPlatform) {
              updatedWebsite.platform = selectedPlatform;
            }
          }

          return updatedWebsite;
        }
        return website;
      });
      setWebsites(updatedWebsites);
    }

    setFormVisible(false);
  };

  // 处理表单取消
  const handleFormCancel = () => {
    setFormVisible(false);
  };

  // 处理编辑网站
  const handleEdit = (website: Website) => {
    setFormMode('edit');
    setCurrentWebsite(website);
    setFormVisible(true);
  };

  // 处理删除网站
  const handleDelete = async (website: Website) => {
    try {
      await WebsiteApi.deleteWebsite(website.id);
      message.success('网站删除成功！');
      fetchWebsites(); // 重新获取数据
    } catch (error) {
      console.error('删除网站失败:', error);
      message.error('删除网站失败');
    }
  };

  // 查看详情
  const handleViewDetail = (website: Website) => {
    setDetailWebsite(website);
    setDetailVisible(true);
  };

  // 密码管理
  const handlePasswordManagement = (website: Website) => {
    setCurrentPasswordWebsite(website);
    setPasswordManagerVisible(true);
  };

  // 搜索功能现在由 useAdvancedSearch Hook 处理

  // 导出网站列表
  const handleExport = async () => {
    try {
      // 使用request服务以包含认证token
      const token = localStorage.getItem('token');
      const headers: Record<string, string> = {};

      if (token) {
        headers.Authorization = `Bearer ${token}`;
      } else if (import.meta.env.DEV) {
        headers.Authorization = `Bearer dev-token`;
      }

      const response = await fetch('http://localhost:3001/api/v1/export/websites', {
        headers
      });

      if (!response.ok) {
        throw new Error('导出失败');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `网站列表_${new Date().toISOString().slice(0, 10)}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      message.success('网站列表导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    }
  };

  // 手动触发网站状态检测
  const handleTriggerAccessCheck = async () => {
    try {
      setCheckingAccess(true);
      const response = await WebsiteApi.triggerAccessCheck();

      if (response.success) {
        message.success('网站状态检测任务已触发，正在后台执行...');
        message.info('请等待1-2分钟后刷新页面查看最新状态', 3);

        // 5秒后自动刷新数据
        setTimeout(() => {
          fetchWebsites();
        }, 5000);
      } else {
        message.error(response.message || '触发检测失败');
      }
    } catch (error: any) {
      console.error('触发网站状态检测失败:', error);
      message.error(error.message || '触发检测失败，请稍后重试');
    } finally {
      setCheckingAccess(false);
    }
  };

  // 手动触发SSL证书检测
  const handleTriggerSSLCheck = async () => {
    try {
      setCheckingSSL(true);
      const response = await WebsiteApi.triggerSSLCheck();

      if (response.success) {
        message.success('SSL证书检测任务已触发，正在后台执行...');
        message.info('请等待1-2分钟后刷新页面查看最新状态', 3);

        // 5秒后自动刷新数据
        setTimeout(() => {
          fetchWebsites();
        }, 5000);
      } else {
        message.error(response.message || '触发SSL检测失败');
      }
    } catch (error: any) {
      console.error('触发SSL检测失败:', error);
      message.error(error.message || '触发SSL检测失败，请稍后重试');
    } finally {
      setCheckingSSL(false);
    }
  };

  // SSL检测
  const handleSSLCheck = async (website: Website) => {
    // 防重复点击保护
    if (sslChecking[website.id]) {
      message.warning('SSL检测正在进行中，请稍候...');
      return;
    }

    setSslChecking(prev => ({ ...prev, [website.id]: true }));
    try {
      console.log(`开始SSL检测: ${website.domain} (ID: ${website.id})`);

      // 调用真实的SSL检测API - 使用request服务以包含认证token
      const result = await WebsiteApi.checkSSL(website.id);

      if (result.success) {
        // 更新SSL信息
        const updatedWebsites = websites.map(w =>
          w.id === website.id
            ? {
                ...w,
                sslInfo: result.data,
                sslExpireDate: result.data.validTo
              }
            : w
        );
        setWebsites(updatedWebsites);
        message.success(`${website.domain} SSL检测完成`);
        console.log(`SSL检测完成: ${website.domain} (ID: ${website.id})`);
      } else {
        message.error(`${website.domain} SSL检测失败: ${result.message || '未知错误'}`);
        console.error(`SSL检测失败: ${website.domain} (ID: ${website.id})`, result.message);
      }
    } catch (error) {
      console.error(`SSL检测异常: ${website.domain} (ID: ${website.id})`, error);
      message.error(`${website.domain} SSL检测失败: 网络错误`);
    } finally {
      setSslChecking(prev => ({ ...prev, [website.id]: false }));
      console.log(`SSL检测结束: ${website.domain} (ID: ${website.id})`);
    }
  };

  // 域名状态检测
  const handleDomainCheck = async (website: Website) => {
    // 防重复点击保护
    if (domainChecking[website.id]) {
      message.warning(`${website.domain} 域名检测正在进行中，请稍候...`);
      console.log(`⚠️ 重复点击被阻止: ${website.domain} (ID: ${website.id})`);
      return;
    }

    setDomainChecking(prev => ({ ...prev, [website.id]: true }));

    // 显示加载提示
    const loadingMessage = message.loading(`正在检测域名 ${website.domain}...`, 0);

    try {
      console.log(`🔍 开始域名检测: ${website.domain} (ID: ${website.id})`);

      // 调用真实的域名检测API - 使用request服务以包含认证token
      const result = await WebsiteApi.checkDomain(website.id);

      // 关闭加载提示
      loadingMessage();

      if (result.success) {
        // 更新域名信息
        const updatedWebsites = websites.map(w =>
          w.id === website.id
            ? {
                ...w,
                domainInfo: result.data,
                domainExpireDate: result.data.expirationDate
              }
            : w
        );
        setWebsites(updatedWebsites);
        message.success(`✅ ${website.domain} 域名检测完成`);
        console.log(`✅ 域名检测完成: ${website.domain} (ID: ${website.id})`);
      } else {
        message.error(`❌ ${website.domain} 域名检测失败: ${result.message || '未知错误'}`);
        console.error(`❌ 域名检测失败: ${website.domain} (ID: ${website.id})`, result.message);
      }
    } catch (error) {
      // 关闭加载提示
      loadingMessage();

      console.error(`❌ 域名检测异常: ${website.domain} (ID: ${website.id})`, error);

      // 检查是否是429错误（重复请求）
      if (error.response?.status === 429) {
        message.warning(`⚠️ ${website.domain} 域名检测正在进行中，请稍候...`);
      } else {
        message.error(`❌ ${website.domain} 域名检测失败: ${error.message || '网络错误'}`);
      }
    } finally {
      setDomainChecking(prev => ({ ...prev, [website.id]: false }));
      console.log(`🔚 域名检测结束: ${website.domain} (ID: ${website.id})`);
    }
  };

  // 性能检测
  const handlePerformanceCheck = async (website: Website) => {
    // 防重复点击保护
    if (performanceChecking[website.id]) {
      message.warning('性能检测正在进行中，请稍候...');
      return;
    }

    setPerformanceChecking(prev => ({ ...prev, [website.id]: true }));
    try {
      console.log(`开始性能检测: ${website.domain} (ID: ${website.id})`);

      // 调用真实的性能检测API - 使用request服务以包含认证token
      const result = await WebsiteApi.performanceTest(website.id);

      if (result.success) {
        // 更新性能指标
        const updatedWebsites = websites.map(w =>
          w.id === website.id
            ? {
                ...w,
                performanceMetrics: {
                  ...result.data,
                  // 转换单位：毫秒转秒
                  pageLoadTime: (result.data.pageLoadTime / 1000).toFixed(1),
                  firstContentfulPaint: (result.data.firstContentfulPaint / 1000).toFixed(1),
                  largestContentfulPaint: (result.data.largestContentfulPaint / 1000).toFixed(1)
                }
              }
            : w
        );
        setWebsites(updatedWebsites);
        message.success(`${website.domain} 性能检测完成`);
        console.log(`性能检测完成: ${website.domain} (ID: ${website.id})`);
      } else {
        message.error(`${website.domain} 性能检测失败: ${result.message || '未知错误'}`);
        console.error(`性能检测失败: ${website.domain} (ID: ${website.id})`, result.message);
      }
    } catch (error) {
      console.error(`性能检测异常: ${website.domain} (ID: ${website.id})`, error);
      message.error(`${website.domain} 性能检测失败: 网络错误`);
    } finally {
      setPerformanceChecking(prev => ({ ...prev, [website.id]: false }));
      console.log(`性能检测结束: ${website.domain} (ID: ${website.id})`);
    }
  };

  // 访问状态检测
  const handleAccessCheck = async (website: Website) => {
    // 防重复点击保护
    if (accessChecking[website.id]) {
      message.warning('访问状态检测正在进行中，请稍候...');
      return;
    }

    setAccessChecking(prev => ({ ...prev, [website.id]: true }));
    try {
      console.log(`开始访问状态检测: ${website.domain} (ID: ${website.id})`);

      // 调用真实的访问状态检测API - 使用request服务以包含认证token
      const result = await WebsiteApi.checkAccess(website.id);

      if (result.success) {
        // 更新访问状态信息
        const updatedWebsites = websites.map(w =>
          w.id === website.id
            ? {
                ...w,
                accessStatusCode: result.data.statusCode,
                responseTime: result.data.responseTime,
                lastCheckTime: result.data.lastCheckTime,
                isAccessible: result.data.isAccessible
              }
            : w
        );
        setWebsites(updatedWebsites);
        message.success(`${website.domain} 访问状态检测完成`);
        console.log(`访问状态检测完成: ${website.domain} (ID: ${website.id})`);
      } else {
        message.error(`${website.domain} 访问状态检测失败: ${result.message || '未知错误'}`);
        console.error(`访问状态检测失败: ${website.domain} (ID: ${website.id})`, result.message);
      }
    } catch (error) {
      console.error(`访问状态检测异常: ${website.domain} (ID: ${website.id})`, error);
      message.error(`${website.domain} 访问状态检测失败: 网络错误`);
    } finally {
      setAccessChecking(prev => ({ ...prev, [website.id]: false }));
      console.log(`访问状态检测结束: ${website.domain} (ID: ${website.id})`);
    }
  };

  // 获取SSL状态
  const getSSLStatus = (sslInfo?: SSLInfo) => {
    if (!sslInfo) {
      return <Tag color="red">无SSL</Tag>;
    }

    // 检查SSL状态
    if (sslInfo.status === 'invalid') {
      return <Tag color="red">无效</Tag>;
    }

    if (sslInfo.status === 'unknown') {
      return <Tag color="default">未知</Tag>;
    }

    // 检查到期时间
    if (sslInfo.daysUntilExpiry !== undefined && sslInfo.daysUntilExpiry !== null) {
      if (sslInfo.daysUntilExpiry <= 0) {
        return <Tag color="red">已过期</Tag>;
      } else if (sslInfo.daysUntilExpiry <= 7) {
        return <Tag color="red">{sslInfo.daysUntilExpiry}天后过期</Tag>;
      } else if (sslInfo.daysUntilExpiry <= 30) {
        return <Tag color="orange">{sslInfo.daysUntilExpiry}天后过期</Tag>;
      } else {
        return <Tag color="green">{sslInfo.daysUntilExpiry}天后过期</Tag>;
      }
    }

    // 如果有SSL信息但没有到期时间，根据状态显示
    if (sslInfo.status === 'valid') {
      return <Tag color="green">有效</Tag>;
    }

    return <Tag color="default">未检测</Tag>;
  };

  // 获取域名状态
  const getDomainStatus = (domainInfo?: DomainInfo) => {
    if (!domainInfo) {
      return <Tag color="red">未知</Tag>;
    }

    // 检查DNS可解析性
    if (!domainInfo.dnsResolvable) {
      return <Tag color="red">无法解析</Tag>;
    }

    // 检查到期时间
    if (domainInfo.daysUntilExpiry !== null && domainInfo.daysUntilExpiry !== undefined) {
      if (domainInfo.daysUntilExpiry < 0) {
        return <Tag color="red">已过期</Tag>;
      } else if (domainInfo.daysUntilExpiry <= 30) {
        return <Tag color="red">即将过期</Tag>;
      } else if (domainInfo.daysUntilExpiry <= 90) {
        return <Tag color="orange">即将过期</Tag>;
      } else {
        return <Tag color="green">正常</Tag>;
      }
    }

    // 如果没有到期时间信息，但DNS可解析，则显示为活跃
    return <Tag color="blue">活跃</Tag>;
  };

  // 获取访问状态
  const getAccessStatus = (statusCode: number) => {
    if (!statusCode || statusCode === 0) {
      return { status: 'error' as const, text: '无法访问' };
    }

    if (statusCode >= 200 && statusCode < 300) {
      return { status: 'success' as const, text: `${statusCode} 正常` };
    } else if (statusCode >= 300 && statusCode < 400) {
      return { status: 'warning' as const, text: `${statusCode} 重定向` };
    } else if (statusCode >= 400 && statusCode < 500) {
      return { status: 'error' as const, text: `${statusCode} 客户端错误` };
    } else if (statusCode >= 500) {
      return { status: 'error' as const, text: `${statusCode} 服务器错误` };
    } else {
      return { status: 'default' as const, text: `${statusCode} 未知` };
    }
  };

  // 检查是否需要标红显示（已过期、本月或次月到期）
  const shouldShowRed = (expireDate: string) => {
    if (!expireDate) return false;

    const today = new Date();
    const expire = new Date(expireDate);

    // 已过期
    if (expire < today) {
      return true;
    }

    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();
    const expireMonth = expire.getMonth();
    const expireYear = expire.getFullYear();

    // 本月到期
    if (expireYear === currentYear && expireMonth === currentMonth) {
      return true;
    }

    // 次月到期
    const nextMonth = currentMonth === 11 ? 0 : currentMonth + 1;
    const nextYear = currentMonth === 11 ? currentYear + 1 : currentYear;

    if (expireYear === nextYear && expireMonth === nextMonth) {
      return true;
    }

    return false;
  };

  // 表格列定义
  const columns = [
    {
      title: '网站信息',
      dataIndex: 'siteName',
      key: 'siteName',
      width: 200,
      render: (siteName: string, record: Website) => (
        <div>
          <div className="flex items-center">
            <Avatar
              size="small"
              src={`https://www.google.com/s2/favicons?domain=${record.domain}`}
              className="mr-2"
              onError={(e) => {
                // 当favicon加载失败时，使用默认图标
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                return false;
              }}
            />
            <span className="font-medium">
              {siteName || record.domain} {/* 兼容旧数据 */}
            </span>
          </div>
          <div className="text-sm text-gray-500 mt-1">
            <Tag size="small" color="blue">{record.platform?.name}</Tag>
            <span className="ml-1">({record.domain})</span>
          </div>
        </div>
      )
    },
    {
      title: '服务器',
      dataIndex: 'server',
      key: 'server',
      width: 150,
      render: (server: Server) => (
        <div>
          <div className="font-medium">{server?.name}</div>
          <div className="text-sm text-gray-500">{server?.location}</div>
          <div className="text-xs text-gray-400">{server?.ipAddress}</div>
          {server?.status && (
            <div className="mt-1">
              <Tag
                color={server.status === 'active' ? 'green' : server.status === 'maintenance' ? 'orange' : 'red'}
                size="small"
              >
                {server.status === 'active' ? '正常' : server.status === 'maintenance' ? '维护中' : '停用'}
              </Tag>
            </div>
          )}
        </div>
      )
    },
    {
      title: '访问状态',
      dataIndex: 'accessStatusCode',
      key: 'accessStatusCode',
      width: 120,
      render: (statusCode: number, record: Website) => (
        <div>
          {accessChecking[record.id] ? (
            <div className="flex items-center">
              <Spin size="small" className="mr-1" />
              <span className="text-xs">检测中...</span>
            </div>
          ) : (
            <>
              <Badge
                status={getAccessStatus(statusCode).status}
                text={getAccessStatus(statusCode).text}
              />
              {record.responseTime && (
                <div className="text-xs text-gray-500">
                  {record.responseTime}ms
                </div>
              )}
            </>
          )}
        </div>
      )
    },
    {
      title: 'SSL证书',
      dataIndex: 'sslInfo',
      key: 'sslInfo',
      width: 120,
      render: (sslInfo: SSLInfo, record: Website) => (
        <div>
          {sslChecking[record.id] ? (
            <div className="flex items-center">
              <Spin size="small" className="mr-1" />
              <span className="text-xs">检测中...</span>
            </div>
          ) : (
            <>
              {getSSLStatus(sslInfo)}
              {sslInfo && sslInfo.issuer && (
                <div className="text-xs text-gray-500">
                  {sslInfo.issuer}
                </div>
              )}
            </>
          )}
        </div>
      )
    },
    {
      title: '域名状态',
      dataIndex: 'domainInfo',
      key: 'domainInfo',
      width: 120,
      render: (domainInfo: DomainInfo, record: Website) => (
        <div>
          {domainChecking[record.id] ? (
            <div className="flex items-center">
              <Spin size="small" className="mr-1" />
              <span className="text-xs">检测中...</span>
            </div>
          ) : (
            <>
              {getDomainStatus(domainInfo)}
              {domainInfo && domainInfo.daysUntilExpiry !== null && domainInfo.daysUntilExpiry !== undefined && (
                <div className="text-xs text-gray-500">
                  {domainInfo.daysUntilExpiry < 0
                    ? `已过期${Math.abs(domainInfo.daysUntilExpiry)}天`
                    : `${domainInfo.daysUntilExpiry}天后过期`
                  }
                </div>
              )}
            </>
          )}
        </div>
      )
    },
    {
      title: '性能评分',
      dataIndex: 'performanceMetrics',
      key: 'performanceMetrics',
      width: 120,
      render: (metrics: any, record: Website) => (
        <div>
          {performanceChecking[record.id] ? (
            <div className="flex flex-col items-center">
              <Spin size="small" />
              <span className="text-xs mt-1">检测中...</span>
            </div>
          ) : metrics ? (
            <>
              <Progress
                type="circle"
                size={40}
                percent={metrics.performanceScore}
                strokeColor={metrics.performanceScore >= 80 ? '#52c41a' : metrics.performanceScore >= 60 ? '#faad14' : '#ff4d4f'}
              />
              <div className="text-xs text-gray-500 mt-1">
                {metrics.pageLoadTime}s
              </div>
            </>
          ) : (
            <span>-</span>
          )}
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (_, record: Website) => {
        const moreMenuItems = [
          // 监控相关功能 - 需要 site.website.monitor 权限
          ...(hasPermission('site.website.monitor') ? [
            {
              key: 'access-check',
              icon: <LinkOutlined />,
              label: '访问状态检测',
              onClick: () => handleAccessCheck(record)
            },
            {
              key: 'ssl-check',
              icon: <SafetyCertificateOutlined />,
              label: 'SSL检测',
              onClick: () => handleSSLCheck(record)
            },
            {
              key: 'domain-check',
              icon: <GlobalOutlined />,
              label: '域名检测',
              onClick: () => handleDomainCheck(record)
            },
            {
              key: 'performance-check',
              icon: <ThunderboltOutlined />,
              label: '性能检测',
              onClick: () => handlePerformanceCheck(record)
            },
            {
              key: 'security-scan',
              icon: <SecurityScanOutlined />,
              label: '安全扫描',
              onClick: () => message.info('安全扫描功能开发中...')
            },
            {
              key: 'divider-1',
              type: 'divider'
            }
          ] : []),
          // 基础功能 - 所有用户都可以访问
          {
            key: 'visit',
            icon: <EyeOutlined />,
            label: '访问网站',
            onClick: () => window.open(record.siteUrl || `https://${record.domain}`, '_blank')
          }
        ];

        return (
          <Space size="small">
            <Tooltip title="查看详情">
              <Button
                type="text"
                icon={<EyeOutlined />}
                onClick={() => handleViewDetail(record)}
              />
            </Tooltip>
            {hasPermission('site.website.credential') ? (
              <Tooltip title="密码管理">
                <Button
                  type="text"
                  icon={<KeyOutlined />}
                  onClick={() => handlePasswordManagement(record)}
                  className="text-blue-500 hover:text-blue-600"
                />
              </Tooltip>
            ) : (
              <Tooltip title="权限不足">
                <Button
                  type="text"
                  icon={<KeyOutlined />}
                  disabled
                  className="text-gray-400"
                />
              </Tooltip>
            )}
            {hasPermission('site.website.edit') ? (
              <Tooltip title="编辑">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(record)}
                />
              </Tooltip>
            ) : (
              <Tooltip title="权限不足">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  disabled
                  className="text-gray-400"
                />
              </Tooltip>
            )}
            {hasPermission('site.website.delete') ? (
              <Popconfirm
                title="确定要删除这个网站吗？"
                description="删除后无法恢复，请谨慎操作。"
                onConfirm={() => handleDelete(record)}
                okText="确定"
                cancelText="取消"
              >
                <Tooltip title="删除">
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                  />
                </Tooltip>
              </Popconfirm>
            ) : (
              <Tooltip title="权限不足">
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  disabled
                  className="text-gray-400"
                />
              </Tooltip>
            )}
            {moreMenuItems.length > 1 && (
              <Dropdown
                menu={{
                  items: moreMenuItems,
                  onClick: ({ key }) => {
                    const item = moreMenuItems.find(item => item.key === key);
                    if (item && item.onClick) {
                      item.onClick();
                    }
                  }
                }}
                trigger={['click']}
              >
                <Tooltip title="更多操作">
                  <Button type="text" icon={<MoreOutlined />} />
                </Tooltip>
              </Dropdown>
            )}
          </Space>
        );
      }
    }
  ];

  return (
    <div style={{ padding: 24, backgroundColor: '#f8fafc', minHeight: '100vh' }}>
      <style>
        {`
          .table-row-light {
            background-color: #ffffff;
          }
          .table-row-dark {
            background-color: #f9fafb;
          }
          .table-row-light:hover,
          .table-row-dark:hover {
            background-color: #f3f4f6 !important;
          }
          .ant-table-thead > tr > th {
            background-color: #f8fafc;
            border-bottom: 2px solid #e5e7eb;
            font-weight: 600;
            color: #374151;
            padding: 16px;
          }
          .ant-table-tbody > tr > td {
            padding: 12px 16px;
            border-bottom: 1px solid #f3f4f6;
          }
          .ant-statistic-title {
            margin-bottom: 8px;
          }
          .ant-statistic-content {
            display: flex;
            align-items: center;
            gap: 8px;
          }
        `}
      </style>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        {/* 标题和操作按钮 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: 20 }}>
          <div>
            <h1 style={{ fontSize: 28, fontWeight: 600, color: '#1f2937', margin: 0, marginBottom: 8 }}>
              网站管理 (增强版)
            </h1>
            <p style={{ color: '#6b7280', fontSize: 16, margin: 0 }}>
              管理网站状态、SSL证书、域名信息和性能监控
            </p>
          </div>
          <Space size="middle">
            <Button
              icon={<SyncOutlined />}
              onClick={handleTriggerAccessCheck}
              loading={checkingAccess}
              style={{
                height: 40,
                borderRadius: 8,
                backgroundColor: '#10b981',
                borderColor: '#10b981',
                color: '#fff'
              }}
            >
              一键检查状态
            </Button>
            <Button
              icon={<SafetyCertificateOutlined />}
              onClick={handleTriggerSSLCheck}
              loading={checkingSSL}
              style={{
                height: 40,
                borderRadius: 8,
                backgroundColor: '#f59e0b',
                borderColor: '#f59e0b',
                color: '#fff'
              }}
            >
              一键检查SSL
            </Button>
            {hasPermission('site.list.view') && (
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
                style={{ height: 40, borderRadius: 8 }}
              >
                导出Excel
              </Button>
            )}
            {hasPermission('site.website.create') && (
              <Button
                icon={<UploadOutlined />}
                onClick={() => setImportVisible(true)}
                style={{ height: 40, borderRadius: 8, backgroundColor: '#f8fafc', borderColor: '#e2e8f0' }}
              >
                Excel导入
              </Button>
            )}
            {hasPermission('site.website.create') && (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreate}
                style={{
                  height: 40,
                  borderRadius: 8,
                  backgroundColor: '#3b82f6',
                  borderColor: '#3b82f6',
                  boxShadow: '0 2px 4px rgba(59, 130, 246, 0.2)'
                }}
              >
                新建网站
              </Button>
            )}
          </Space>
        </div>

        {/* 搜索和筛选区域 */}
        <Card
          style={{
            borderRadius: 12,
            border: '1px solid #e5e7eb',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
          }}
          bodyStyle={{ padding: '20px 24px' }}
        >
          <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
            <div style={{ flex: 1, maxWidth: 500 }}>
              <AdvancedSearchInput
                placeholder="搜索网站名称、域名、URL、平台、服务器或备注..."
                value={searchText}
                onChange={updateSearchText}
                onSearch={updateSearchText}
                size="large"
                allowClear
                loading={isSearching}
                searchResult={searchResult}
                suggestions={searchResult.suggestions}
                onSuggestionSelect={applySuggestion}
                showStats={true}
                showSettings={true}
                multiKeyword={true}
                keywordLogic="AND"
                style={{ borderRadius: 8 }}
              />
            </div>
          </div>
        </Card>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card
            style={{
              borderRadius: 12,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
            }}
            styles={{ body: { padding: '20px' } }}
          >
            <Statistic
              title={
                <span style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: 14 }}>
                  {searchText ? "搜索结果" : "总网站数"}
                </span>
              }
              value={searchResult.filtered}
              valueStyle={{ color: '#fff', fontSize: 28, fontWeight: 600 }}
              prefix={<GlobalOutlined style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: 20 }} />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card
            style={{
              borderRadius: 12,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
            }}
            styles={{ body: { padding: '20px' } }}
          >
            <Statistic
              title={
                <span style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: 14 }}>
                  SSL即将过期
                </span>
              }
              value={(searchText ? filteredWebsites : websites).filter(w => w.sslInfo && w.sslInfo.daysUntilExpiry <= 30).length}
              valueStyle={{ color: '#fff', fontSize: 28, fontWeight: 600 }}
              prefix={<WarningOutlined style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: 20 }} />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card
            style={{
              borderRadius: 12,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
            }}
            bodyStyle={{ padding: '20px' }}
          >
            <Statistic
              title={
                <span style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: 14 }}>
                  域名即将过期
                </span>
              }
              value={(searchText ? filteredWebsites : websites).filter(w => w.domainInfo && w.domainInfo.daysUntilExpiry <= 90).length}
              valueStyle={{ color: '#fff', fontSize: 28, fontWeight: 600 }}
              prefix={<ExclamationCircleOutlined style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: 20 }} />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card
            style={{
              borderRadius: 12,
              border: '1px solid #e5e7eb',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
            }}
            styles={{ body: { padding: '20px' } }}
          >
            <Statistic
              title={
                <span style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: 14 }}>
                  平均性能评分
                </span>
              }
              value={(() => {
                const dataSource = searchResult.data;
                const withMetrics = dataSource.filter(w => w.performanceMetrics);
                return Math.round(withMetrics.reduce((sum, w) => sum + (w.performanceMetrics?.performanceScore || 0), 0) / withMetrics.length) || 0;
              })()}
              valueStyle={{ color: '#fff', fontSize: 28, fontWeight: 600 }}
              prefix={<ThunderboltOutlined style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: 20 }} />}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据表格 */}
      <Card
        style={{
          borderRadius: 12,
          border: '1px solid #e5e7eb',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.05)'
        }}
        styles={{ body: { padding: 0 } }}
      >
        <Table
          columns={columns}
          dataSource={searchResult.data}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1200 }}
          style={{ borderRadius: 12, overflow: 'hidden' }}
          rowClassName={(record, index) =>
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: searchResult.filtered,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => (
              <span style={{ color: '#6b7280', fontSize: 14 }}>
                共 <span style={{ color: '#1f2937', fontWeight: 500 }}>{total}</span> 条记录
                {searchResult.searchText && <span style={{ color: '#3b82f6' }}> (已过滤)</span>}
              </span>
            ),
            style: { padding: '16px 24px', borderTop: '1px solid #f3f4f6' }
          }}
        />
      </Card>

      {/* 网站表单 */}
      <WebsiteForm
        visible={formVisible}
        mode={formMode}
        initialValues={currentWebsite}
        onSuccess={handleFormSuccess}
        onCancel={handleFormCancel}
      />

      {/* 密码管理 */}
      {currentPasswordWebsite && (
        <PasswordManager
          visible={passwordManagerVisible}
          onClose={() => setPasswordManagerVisible(false)}
          websiteId={currentPasswordWebsite.id}
          websiteName={currentPasswordWebsite.siteName || currentPasswordWebsite.domain}
        />
      )}

      {/* 网站导入弹窗 */}
      <WebsiteImport
        visible={importVisible}
        onCancel={() => setImportVisible(false)}
        onSuccess={() => {
          setImportVisible(false);
          fetchWebsites();
        }}
      />

      {/* 密码管理 */}
      {currentPasswordWebsite && (
        <PasswordManager
          visible={passwordManagerVisible}
          onClose={() => setPasswordManagerVisible(false)}
          websiteId={currentPasswordWebsite.id}
          websiteName={currentPasswordWebsite.siteName || currentPasswordWebsite.domain}
        />
      )}

      {/* 详情模态框 */}
      <Modal
        title={
          <div className="flex items-center">
            <InfoCircleOutlined className="mr-2 text-blue-500" />
            网站详情
          </div>
        }
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
          <Button
            key="edit"
            type="primary"
            icon={<EditOutlined />}
            onClick={() => {
              setDetailVisible(false);
              handleEdit(detailWebsite!);
            }}
          >
            编辑
          </Button>
        ]}
        width={800}
      >
        {detailWebsite && (
          <div>
            {/* 基本信息 */}
            <Descriptions title="基本信息" bordered column={2} className="mb-6">
              <Descriptions.Item label="网站名称">{detailWebsite.siteName}</Descriptions.Item>
              <Descriptions.Item label="域名">{detailWebsite.domain}</Descriptions.Item>
              <Descriptions.Item label="网站URL">
                <a href={detailWebsite.siteUrl} target="_blank" rel="noopener noreferrer">
                  {detailWebsite.siteUrl}
                </a>
              </Descriptions.Item>
              <Descriptions.Item label="平台">{detailWebsite.platform?.name}</Descriptions.Item>
              <Descriptions.Item label="服务器">{detailWebsite.server?.name}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={statusConfig[detailWebsite.status as keyof typeof statusConfig]?.color}>
                  {statusConfig[detailWebsite.status as keyof typeof statusConfig]?.label}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="上线时间">{detailWebsite.onlineDate}</Descriptions.Item>
              <Descriptions.Item label="到期时间">
                <span style={{
                  color: shouldShowRed(detailWebsite.expireDate || '') ? '#ff4d4f' : '#52c41a',
                  fontWeight: 500
                }}>
                  {detailWebsite.expireDate || '-'}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="项目金额">
                {detailWebsite.projectAmount ? `¥${detailWebsite.projectAmount.toLocaleString()}` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="续费金额">
                {detailWebsite.renewalFee ? `¥${detailWebsite.renewalFee.toLocaleString()}` : '-'}
              </Descriptions.Item>
            </Descriptions>

            {/* SSL证书信息 */}
            {detailWebsite.sslInfo && (
              <Descriptions title="SSL证书信息" bordered column={2} className="mb-6">
                <Descriptions.Item label="颁发者">{detailWebsite.sslInfo.issuer}</Descriptions.Item>
                <Descriptions.Item label="主题">{detailWebsite.sslInfo.subject}</Descriptions.Item>
                <Descriptions.Item label="有效期从">{detailWebsite.sslInfo.validFrom}</Descriptions.Item>
                <Descriptions.Item label="有效期至">{detailWebsite.sslInfo.validTo}</Descriptions.Item>
                <Descriptions.Item label="剩余天数">
                  <span className={detailWebsite.sslInfo.daysUntilExpiry <= 30 ? 'text-red-500' : 'text-green-500'}>
                    {detailWebsite.sslInfo.daysUntilExpiry} 天
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  {getSSLStatus(detailWebsite.sslInfo)}
                </Descriptions.Item>
                <Descriptions.Item label="最后检查" span={2}>
                  {new Date(detailWebsite.sslInfo.lastChecked).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            )}

            {/* 域名信息 */}
            {detailWebsite.domainInfo && (
              <Descriptions title="域名信息" bordered column={2} className="mb-6">
                <Descriptions.Item label="注册商">{detailWebsite.domainInfo.registrar}</Descriptions.Item>
                <Descriptions.Item label="注册时间">{detailWebsite.domainInfo.registrationDate}</Descriptions.Item>
                <Descriptions.Item label="到期时间">{detailWebsite.domainInfo.expirationDate}</Descriptions.Item>
                <Descriptions.Item label="剩余天数">
                  <span className={detailWebsite.domainInfo.daysUntilExpiry <= 90 ? 'text-red-500' : 'text-green-500'}>
                    {detailWebsite.domainInfo.daysUntilExpiry} 天
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="DNS服务器" span={2}>
                  {detailWebsite.domainInfo.nameServers?.join(', ')}
                </Descriptions.Item>
                <Descriptions.Item label="最后检查" span={2}>
                  {new Date(detailWebsite.domainInfo.lastChecked).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            )}

            {/* 性能指标 */}
            {detailWebsite.performanceMetrics && (
              <Descriptions title="性能指标" bordered column={2} className="mb-6">
                <Descriptions.Item label="性能评分">
                  <Progress
                    percent={detailWebsite.performanceMetrics.performanceScore}
                    size="small"
                    strokeColor={detailWebsite.performanceMetrics.performanceScore >= 80 ? '#52c41a' : detailWebsite.performanceMetrics.performanceScore >= 60 ? '#faad14' : '#ff4d4f'}
                  />
                </Descriptions.Item>
                <Descriptions.Item label="页面加载时间">{detailWebsite.performanceMetrics.pageLoadTime}s</Descriptions.Item>
                <Descriptions.Item label="首次内容绘制">{detailWebsite.performanceMetrics.firstContentfulPaint}s</Descriptions.Item>
                <Descriptions.Item label="最大内容绘制">{detailWebsite.performanceMetrics.largestContentfulPaint}s</Descriptions.Item>
                <Descriptions.Item label="移动端评分">{detailWebsite.performanceMetrics.mobileScore}</Descriptions.Item>
                <Descriptions.Item label="桌面端评分">{detailWebsite.performanceMetrics.desktopScore}</Descriptions.Item>
                <Descriptions.Item label="最后测量" span={2}>
                  {new Date(detailWebsite.performanceMetrics.lastMeasured).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            )}

            {/* 备注信息 */}
            {detailWebsite.notes && (
              <Descriptions title="备注信息" bordered className="mb-6">
                <Descriptions.Item label="备注" span={3}>
                  <div style={{ whiteSpace: 'pre-wrap' }}>{detailWebsite.notes}</div>
                </Descriptions.Item>
              </Descriptions>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SimpleEnhancedWebsiteList;
