import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Modal,
  Form,
  Input,
  Select,
  Row,
  Col,
  Statistic,
  Badge,
  Avatar,
  Progress,
  Tabs,
  Alert,
  Popconfirm,
  Drawer,
  Descriptions,
  Timeline,
  App
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  GlobalOutlined,
  SafetyCertificateOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  KeyOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  SecurityScanOutlined,
  ThunderboltOutlined,
  SettingOutlined,
  MoreOutlined,
  ShieldOutlined,
  BackupOutlined,
  MonitorOutlined,
  FileTextOutlined,
  LinkOutlined,
  CopyOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { Website, Platform, Server, WebsiteCredential, SSLInfo, DomainInfo, QueryParams } from '../../types';
import WebsiteForm from '../../components/Website/WebsiteForm';
import { WebsiteApi } from '../../services/website';
import AttachmentPreview from '../../components/Website/AttachmentPreview';
import { cacheManager } from '../../utils/performance';

const { TabPane } = Tabs;
const { TextArea } = Input;

interface AdvancedWebsiteManagementProps {}

const AdvancedWebsiteManagement: React.FC<AdvancedWebsiteManagementProps> = () => {
  const { message, modal } = App.useApp();
  const [websites, setWebsites] = useState<Website[]>([]);
  const [loading, setLoading] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [credentialVisible, setCredentialVisible] = useState(false);
  const [backupVisible, setBackupVisible] = useState(false);
  const [monitorVisible, setMonitorVisible] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [currentWebsite, setCurrentWebsite] = useState<Website | undefined>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10
  });

  // 获取网站数据
  const fetchWebsites = async (forceRefresh = false) => {
    setLoading(true);
    try {
      const params = forceRefresh
        ? { ...queryParams, _refresh: Date.now() }
        : queryParams;
      const result = await WebsiteApi.getWebsites(params);
      setWebsites(result.data || []);
    } catch (error) {
      message.error('获取网站数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWebsites();
  }, [queryParams]);

  // 处理新建
  const handleCreate = () => {
    setFormMode('create');
    setCurrentWebsite(undefined);
    setFormVisible(true);
  };

  // 处理编辑
  const handleEdit = (record: Website) => {
    setFormMode('edit');
    setCurrentWebsite(record);
    setFormVisible(true);
  };

  // 处理删除
  const handleDelete = async (record: Website) => {
    try {
      await WebsiteApi.deleteWebsite(record.id);
      message.success('网站删除成功');
      fetchWebsites();
    } catch (error) {
      message.error('网站删除失败');
    }
  };

  // 批量删除
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的网站');
      return;
    }

    modal.confirm({
      title: '批量删除确认',
      content: `确定要删除选中的 ${selectedRowKeys.length} 个网站吗？此操作不可恢复。`,
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await Promise.all(
            selectedRowKeys.map(id => WebsiteApi.deleteWebsite(Number(id)))
          );
          message.success(`成功删除 ${selectedRowKeys.length} 个网站`);
          setSelectedRowKeys([]);
          fetchWebsites();
        } catch (error) {
          message.error('批量删除失败');
        }
      }
    });
  };

  // 处理查看详情
  const handleDetail = (record: Website) => {
    setCurrentWebsite(record);
    setDetailVisible(true);
  };

  // 处理凭据管理
  const handleCredentials = (record: Website) => {
    setCurrentWebsite(record);
    setCredentialVisible(true);
  };

  // 处理备份管理
  const handleBackup = (record: Website) => {
    setCurrentWebsite(record);
    setBackupVisible(true);
  };

  // 处理监控设置
  const handleMonitor = (record: Website) => {
    setCurrentWebsite(record);
    setMonitorVisible(true);
  };

  // 处理SSL检查
  const handleSSLCheck = async (record: Website) => {
    try {
      message.loading('正在检查SSL证书...', 0);
      await WebsiteApi.checkSSL(record.id);
      message.destroy();
      message.success('SSL证书检查完成');
      fetchWebsites();
    } catch (error) {
      message.destroy();
      message.error('SSL证书检查失败');
    }
  };

  // 处理访问检查
  const handleAccessCheck = async (record: Website) => {
    try {
      message.loading('正在检查网站访问...', 0);
      await WebsiteApi.checkAccess(record.id);
      message.destroy();
      message.success('网站访问检查完成');
      fetchWebsites();
    } catch (error) {
      message.destroy();
      message.error('网站访问检查失败');
    }
  };

  // 复制URL
  const handleCopyUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    message.success('URL已复制到剪贴板');
  };

  // 获取SSL状态
  const getSSLStatus = (sslInfo?: SSLInfo) => {
    if (!sslInfo) {
      return <Tag color="red">无SSL</Tag>;
    }
    
    if (sslInfo.daysUntilExpiry <= 7) {
      return <Tag color="red">即将过期</Tag>;
    } else if (sslInfo.daysUntilExpiry <= 30) {
      return <Tag color="orange">即将过期</Tag>;
    } else {
      return <Tag color="green">正常</Tag>;
    }
  };

  // 获取域名状态
  const getDomainStatus = (domainInfo?: DomainInfo) => {
    if (!domainInfo) {
      return <Tag color="red">未知</Tag>;
    }
    
    if (domainInfo.daysUntilExpiry <= 30) {
      return <Tag color="red">即将过期</Tag>;
    } else if (domainInfo.daysUntilExpiry <= 90) {
      return <Tag color="orange">即将过期</Tag>;
    } else {
      return <Tag color="green">正常</Tag>;
    }
  };

  // 计算统计数据
  const getStats = () => {
    return {
      total: websites.length,
      active: websites.filter(w => w.status === 'active').length,
      sslExpiring: websites.filter(w => w.sslInfo && w.sslInfo.daysUntilExpiry <= 30).length,
      domainExpiring: websites.filter(w => w.domainInfo && w.domainInfo.daysUntilExpiry <= 90).length,
      avgPerformance: Math.round(
        websites.filter(w => w.performanceMetrics).reduce((sum, w) => 
          sum + (w.performanceMetrics?.performanceScore || 0), 0
        ) / websites.filter(w => w.performanceMetrics).length
      ) || 0
    };
  };

  const stats = getStats();

  // 表格列定义
  const columns = [
    {
      title: '网站信息',
      dataIndex: 'siteName',
      key: 'siteName',
      width: 200,
      render: (siteName: string, record: Website) => (
        <div>
          <div className="flex items-center mb-1">
            <Avatar 
              size="small" 
              src={`https://www.google.com/s2/favicons?domain=${record.domain}`}
              className="mr-2"
            />
            <span className="font-medium">
              {siteName || record.domain}
            </span>
          </div>
          <div className="text-sm text-gray-500">
            <Tag size="small" color="blue">{record.platform?.name}</Tag>
            <span className="ml-1">({record.domain})</span>
          </div>
        </div>
      )
    },
    {
      title: '站点URL',
      dataIndex: 'siteUrl',
      key: 'siteUrl',
      width: 200,
      render: (url: string, record: Website) => (
        <div>
          <div className="flex items-center mb-1">
            <a 
              href={url} 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-500 hover:text-blue-700"
            >
              {url}
            </a>
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopyUrl(url)}
              className="ml-1"
            />
          </div>
          <div className="text-sm">
            <Badge 
              status={record.accessStatusCode === 200 ? 'success' : 'error'} 
              text={`${record.accessStatusCode || 'N/A'} - ${record.responseTime || 0}ms`}
            />
          </div>
        </div>
      )
    },
    {
      title: '服务器',
      dataIndex: 'server',
      key: 'server',
      width: 150,
      render: (server: Server) => (
        <div>
          {server ? (
            <>
              <div className="font-medium">{server.name}</div>
              <div className="text-sm text-gray-500">{server.location}</div>
              <div className="text-xs text-gray-400">{server.ipAddress}</div>
            </>
          ) : (
            <span className="text-gray-400">未分配</span>
          )}
        </div>
      )
    },
    {
      title: 'SSL/域名状态',
      key: 'security',
      width: 150,
      render: (_, record: Website) => (
        <div>
          <div className="mb-1">
            <span className="text-xs text-gray-500">SSL: </span>
            {getSSLStatus(record.sslInfo)}
          </div>
          <div>
            <span className="text-xs text-gray-500">域名: </span>
            {getDomainStatus(record.domainInfo)}
          </div>
        </div>
      )
    },
    {
      title: '性能/安全',
      key: 'metrics',
      width: 120,
      render: (_, record: Website) => (
        <div>
          {record.performanceMetrics ? (
            <div className="mb-1">
              <Progress
                type="circle"
                size={40}
                percent={record.performanceMetrics.performanceScore}
                strokeColor={
                  record.performanceMetrics.performanceScore >= 80 ? '#52c41a' :
                  record.performanceMetrics.performanceScore >= 60 ? '#faad14' : '#ff4d4f'
                }
              />
            </div>
          ) : null}
          {record.securityScan && (
            <div className="text-xs">
              <Badge
                count={record.securityScan.vulnerabilities.critical + record.securityScan.vulnerabilities.high}
                showZero
                style={{ backgroundColor: '#ff4d4f' }}
              />
              <span className="ml-1">漏洞</span>
            </div>
          )}
        </div>
      )
    },
    {
      title: '附件',
      key: 'attachments',
      width: 120,
      render: (_, record: Website) => (
        <AttachmentPreview
          websiteId={record.id}
          maxDisplay={2}
          showCount={false}
        />
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (_, record: Website) => (
        <Space size="small" wrap>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="凭据管理">
            <Button
              type="text"
              icon={<KeyOutlined />}
              onClick={() => handleCredentials(record)}
            />
          </Tooltip>
          <Tooltip title="备份管理">
            <Button
              type="text"
              icon={<BackupOutlined />}
              onClick={() => handleBackup(record)}
            />
          </Tooltip>
          <Tooltip title="监控设置">
            <Button
              type="text"
              icon={<MonitorOutlined />}
              onClick={() => handleMonitor(record)}
            />
          </Tooltip>
          <Tooltip title="SSL检查">
            <Button
              type="text"
              icon={<SafetyCertificateOutlined />}
              onClick={() => handleSSLCheck(record)}
            />
          </Tooltip>
          <Tooltip title="访问检查">
            <Button
              type="text"
              icon={<SyncOutlined />}
              onClick={() => handleAccessCheck(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个网站吗？"
            description="此操作不可恢复，请谨慎操作。"
            onConfirm={() => handleDelete(record)}
            okText="确定删除"
            cancelText="取消"
            okType="danger"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="p-6">
      {/* 页面标题和操作 */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">高级网站管理</h1>
            <p className="text-gray-600 mt-1">完整的网站管理功能，包含SSL监控、性能分析、安全扫描等</p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={async () => {
                // 清除前端缓存并强制刷新
                try {
                  // 清除缓存
                  cacheManager.clear();

                  // 重新获取数据（强制刷新）
                  await fetchWebsites(true);
                  message.success('数据已刷新');
                } catch (error) {
                  message.error('刷新失败，请重试');
                }
              }}
              loading={loading}
            >
              刷新
            </Button>
            {selectedRowKeys.length > 0 && (
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={handleBatchDelete}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            )}
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
              className="bg-blue-500 hover:bg-blue-600"
            >
              新建网站
            </Button>
          </Space>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="网站总数"
              value={stats.total}
              prefix={<GlobalOutlined className="text-blue-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="正常运行"
              value={stats.active}
              prefix={<CheckCircleOutlined className="text-green-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="SSL即将过期"
              value={stats.sslExpiring}
              prefix={<WarningOutlined className="text-orange-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均性能评分"
              value={stats.avgPerformance}
              prefix={<ThunderboltOutlined className="text-purple-500" />}
            />
          </Card>
        </Col>
      </Row>

      {/* 告警信息 */}
      {(stats.sslExpiring > 0 || stats.domainExpiring > 0) && (
        <Alert
          message="安全提醒"
          description={
            <div>
              {stats.sslExpiring > 0 && <div>• {stats.sslExpiring} 个网站的SSL证书即将过期</div>}
              {stats.domainExpiring > 0 && <div>• {stats.domainExpiring} 个域名即将过期</div>}
            </div>
          }
          type="warning"
          showIcon
          className="mb-6"
        />
      )}

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={websites}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            type: 'checkbox'
          }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: websites.length,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 网站表单 */}
      <WebsiteForm
        visible={formVisible}
        mode={formMode}
        initialValues={currentWebsite}
        onSuccess={() => {
          setFormVisible(false);
          fetchWebsites();
        }}
        onCancel={() => setFormVisible(false)}
      />

      {/* 网站详情抽屉 */}
      <Drawer
        title={
          <Space>
            <GlobalOutlined />
            <span>网站详情</span>
          </Space>
        }
        width={800}
        open={detailVisible}
        onClose={() => setDetailVisible(false)}
        destroyOnClose
      >
        {currentWebsite && (
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Descriptions column={2} bordered>
                <Descriptions.Item label="站点名称" span={2}>
                  {currentWebsite.siteName || currentWebsite.domain}
                </Descriptions.Item>
                <Descriptions.Item label="站点URL" span={2}>
                  <a href={currentWebsite.siteUrl} target="_blank" rel="noopener noreferrer">
                    {currentWebsite.siteUrl}
                  </a>
                </Descriptions.Item>
                <Descriptions.Item label="域名">
                  {currentWebsite.domain}
                </Descriptions.Item>
                <Descriptions.Item label="平台">
                  <Tag color="blue">{currentWebsite.platform?.name}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="服务器">
                  {currentWebsite.server ? (
                    <div>
                      <div>{currentWebsite.server.name}</div>
                      <div className="text-sm text-gray-500">{currentWebsite.server.ipAddress}</div>
                    </div>
                  ) : '未分配'}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Badge
                    status={currentWebsite.status === 'active' ? 'success' : 'error'}
                    text={currentWebsite.status === 'active' ? '正常' : '停用'}
                  />
                </Descriptions.Item>
                <Descriptions.Item label="上线时间">
                  {currentWebsite.onlineDate || '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="到期时间">
                  {currentWebsite.expireDate || '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="续费金额">
                  {currentWebsite.renewalFee ? `¥${currentWebsite.renewalFee}` : '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="最后检查">
                  {currentWebsite.lastCheckTime || '从未检查'}
                </Descriptions.Item>
                <Descriptions.Item label="备注" span={2}>
                  {currentWebsite.notes || '无'}
                </Descriptions.Item>
              </Descriptions>
            </TabPane>

            <TabPane tab="SSL证书" key="ssl">
              {currentWebsite.sslInfo ? (
                <Descriptions column={2} bordered>
                  <Descriptions.Item label="颁发者" span={2}>
                    {currentWebsite.sslInfo.issuer}
                  </Descriptions.Item>
                  <Descriptions.Item label="主题" span={2}>
                    {currentWebsite.sslInfo.subject}
                  </Descriptions.Item>
                  <Descriptions.Item label="有效期开始">
                    {currentWebsite.sslInfo.validFrom}
                  </Descriptions.Item>
                  <Descriptions.Item label="有效期结束">
                    {currentWebsite.sslInfo.validTo}
                  </Descriptions.Item>
                  <Descriptions.Item label="剩余天数">
                    <Tag color={currentWebsite.sslInfo.daysUntilExpiry <= 30 ? 'red' : 'green'}>
                      {currentWebsite.sslInfo.daysUntilExpiry} 天
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="证书状态">
                    <Badge
                      status={currentWebsite.sslInfo.isValid ? 'success' : 'error'}
                      text={currentWebsite.sslInfo.isValid ? '有效' : '无效'}
                    />
                  </Descriptions.Item>
                </Descriptions>
              ) : (
                <Alert message="未检测到SSL证书信息" type="warning" />
              )}
            </TabPane>

            <TabPane tab="域名信息" key="domain">
              {currentWebsite.domainInfo ? (
                <Descriptions column={2} bordered>
                  <Descriptions.Item label="注册商" span={2}>
                    {currentWebsite.domainInfo.registrar}
                  </Descriptions.Item>
                  <Descriptions.Item label="注册时间">
                    {currentWebsite.domainInfo.registrationDate}
                  </Descriptions.Item>
                  <Descriptions.Item label="到期时间">
                    {currentWebsite.domainInfo.expirationDate}
                  </Descriptions.Item>
                  <Descriptions.Item label="剩余天数">
                    <Tag color={currentWebsite.domainInfo.daysUntilExpiry <= 90 ? 'red' : 'green'}>
                      {currentWebsite.domainInfo.daysUntilExpiry} 天
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="DNS服务器" span={2}>
                    {currentWebsite.domainInfo.nameServers.join(', ')}
                  </Descriptions.Item>
                </Descriptions>
              ) : (
                <Alert message="未检测到域名信息" type="warning" />
              )}
            </TabPane>

            <TabPane tab="性能指标" key="performance">
              {currentWebsite.performanceMetrics ? (
                <div>
                  <Row gutter={16} className="mb-4">
                    <Col span={8}>
                      <Card>
                        <Statistic
                          title="性能评分"
                          value={currentWebsite.performanceMetrics.performanceScore}
                          suffix="/ 100"
                          valueStyle={{
                            color: currentWebsite.performanceMetrics.performanceScore >= 80 ? '#3f8600' :
                                   currentWebsite.performanceMetrics.performanceScore >= 60 ? '#cf1322' : '#cf1322'
                          }}
                        />
                      </Card>
                    </Col>
                    <Col span={8}>
                      <Card>
                        <Statistic
                          title="移动端评分"
                          value={currentWebsite.performanceMetrics.mobileScore}
                          suffix="/ 100"
                        />
                      </Card>
                    </Col>
                    <Col span={8}>
                      <Card>
                        <Statistic
                          title="桌面端评分"
                          value={currentWebsite.performanceMetrics.desktopScore}
                          suffix="/ 100"
                        />
                      </Card>
                    </Col>
                  </Row>
                  <Descriptions column={2} bordered>
                    <Descriptions.Item label="页面加载时间">
                      {currentWebsite.performanceMetrics.pageLoadTime}ms
                    </Descriptions.Item>
                    <Descriptions.Item label="首次内容绘制">
                      {currentWebsite.performanceMetrics.firstContentfulPaint}ms
                    </Descriptions.Item>
                    <Descriptions.Item label="最大内容绘制">
                      {currentWebsite.performanceMetrics.largestContentfulPaint}ms
                    </Descriptions.Item>
                    <Descriptions.Item label="累积布局偏移">
                      {currentWebsite.performanceMetrics.cumulativeLayoutShift}
                    </Descriptions.Item>
                    <Descriptions.Item label="最后测量时间" span={2}>
                      {currentWebsite.performanceMetrics.lastMeasured}
                    </Descriptions.Item>
                  </Descriptions>
                </div>
              ) : (
                <Alert message="暂无性能数据" type="info" />
              )}
            </TabPane>

            <TabPane tab="安全扫描" key="security">
              {currentWebsite.securityScan ? (
                <div>
                  <Row gutter={16} className="mb-4">
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="严重漏洞"
                          value={currentWebsite.securityScan.vulnerabilities.critical}
                          valueStyle={{ color: '#cf1322' }}
                        />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="高危漏洞"
                          value={currentWebsite.securityScan.vulnerabilities.high}
                          valueStyle={{ color: '#fa8c16' }}
                        />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="中危漏洞"
                          value={currentWebsite.securityScan.vulnerabilities.medium}
                          valueStyle={{ color: '#fadb14' }}
                        />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="低危漏洞"
                          value={currentWebsite.securityScan.vulnerabilities.low}
                          valueStyle={{ color: '#52c41a' }}
                        />
                      </Card>
                    </Col>
                  </Row>
                  <Descriptions column={2} bordered>
                    <Descriptions.Item label="恶意软件检测">
                      <Badge
                        status={currentWebsite.securityScan.malwareDetected ? 'error' : 'success'}
                        text={currentWebsite.securityScan.malwareDetected ? '检测到恶意软件' : '未检测到恶意软件'}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="SSL等级">
                      <Tag color="blue">{currentWebsite.securityScan.sslGrade}</Tag>
                    </Descriptions.Item>
                    <Descriptions.Item label="安全评分">
                      {currentWebsite.securityScan.securityScore}/100
                    </Descriptions.Item>
                    <Descriptions.Item label="最后扫描时间">
                      {currentWebsite.securityScan.lastScanDate}
                    </Descriptions.Item>
                    <Descriptions.Item label="安全建议" span={2}>
                      <ul>
                        {currentWebsite.securityScan.recommendations.map((rec, index) => (
                          <li key={index}>{rec}</li>
                        ))}
                      </ul>
                    </Descriptions.Item>
                  </Descriptions>
                </div>
              ) : (
                <Alert message="暂无安全扫描数据" type="info" />
              )}
            </TabPane>

            <TabPane tab="备份信息" key="backup">
              {currentWebsite.backupInfo ? (
                <Descriptions column={2} bordered>
                  <Descriptions.Item label="最后备份时间">
                    {currentWebsite.backupInfo.lastBackupDate || '从未备份'}
                  </Descriptions.Item>
                  <Descriptions.Item label="备份频率">
                    {currentWebsite.backupInfo.backupFrequency}
                  </Descriptions.Item>
                  <Descriptions.Item label="备份位置" span={2}>
                    {currentWebsite.backupInfo.backupLocation}
                  </Descriptions.Item>
                  <Descriptions.Item label="备份大小">
                    {currentWebsite.backupInfo.backupSize ? `${currentWebsite.backupInfo.backupSize}MB` : '未知'}
                  </Descriptions.Item>
                  <Descriptions.Item label="自动备份">
                    <Badge
                      status={currentWebsite.backupInfo.isAutoBackup ? 'success' : 'default'}
                      text={currentWebsite.backupInfo.isAutoBackup ? '已启用' : '已禁用'}
                    />
                  </Descriptions.Item>
                  <Descriptions.Item label="保留天数">
                    {currentWebsite.backupInfo.retentionDays} 天
                  </Descriptions.Item>
                  <Descriptions.Item label="最后恢复时间">
                    {currentWebsite.backupInfo.lastRestoreDate || '从未恢复'}
                  </Descriptions.Item>
                </Descriptions>
              ) : (
                <Alert message="暂无备份信息" type="warning" />
              )}
            </TabPane>
          </Tabs>
        )}
      </Drawer>
    </div>
  );
};

export default AdvancedWebsiteManagement;
