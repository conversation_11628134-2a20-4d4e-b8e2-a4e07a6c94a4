import React, { useState, useEffect } from 'react';
import { Card, Table, Tag, Progress, Statistic, Row, Col, Button, message, Space, Typography, Tooltip, Modal, Timeline } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  ExclamationCircleOutlined,
  ReloadOutlined,
  HistoryOutlined,
  BellOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import axios from 'axios';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface WebsiteStatus {
  id: number;
  site_name: string;
  domain: string;
  site_url: string;
  server_name: string;
  consecutive_failures: number;
  total_checks: number;
  success_checks: number;
  last_success_time: string;
  last_failure_time: string;
  first_failure_time: string;
  current_status_code: number;
  current_response_time: number;
  notification_sent: boolean;
  notification_count: number;
  last_notification_time: string;
  status_display: string;
  status_type: 'success' | 'warning' | 'error';
  error_duration_minutes: number;
  success_rate: number;
}

interface CheckHistory {
  check_time: string;
  status_code: number;
  response_time: number;
  is_accessible: boolean;
  error_message: string;
  check_type: string;
}

const StatusMonitor: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [statusData, setStatusData] = useState<WebsiteStatus[]>([]);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [historyData, setHistoryData] = useState<CheckHistory[]>([]);
  const [selectedWebsite, setSelectedWebsite] = useState<WebsiteStatus | null>(null);

  useEffect(() => {
    fetchStatusData();
    // 每30秒自动刷新
    const interval = setInterval(fetchStatusData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchStatusData = async () => {
    setLoading(true);
    try {
      const response = await axios.get('/api/v1/websites/status-stats');
      if (response.data.success) {
        setStatusData(response.data.data);
      } else {
        message.error(response.data.message || '获取状态数据失败');
      }
    } catch (error: any) {
      console.error('获取状态数据失败:', error);
      message.error('获取状态数据失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  const showHistory = async (website: WebsiteStatus) => {
    setSelectedWebsite(website);
    try {
      const response = await axios.get(`/api/v1/websites/${website.id}/check-history?limit=50`);
      if (response.data.success) {
        setHistoryData(response.data.data);
        setHistoryVisible(true);
      } else {
        message.error('获取检测历史失败');
      }
    } catch (error: any) {
      console.error('获取检测历史失败:', error);
      message.error('获取检测历史失败');
    }
  };

  const getStatusColor = (statusType: string) => {
    switch (statusType) {
      case 'success': return 'green';
      case 'warning': return 'orange';
      case 'error': return 'red';
      default: return 'default';
    }
  };

  const getStatusIcon = (statusType: string) => {
    switch (statusType) {
      case 'success': return <CheckCircleOutlined />;
      case 'warning': return <ExclamationCircleOutlined />;
      case 'error': return <CloseCircleOutlined />;
      default: return null;
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}分钟`;
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${hours}小时${mins > 0 ? mins + '分钟' : ''}`;
    } else {
      const days = Math.floor(minutes / 1440);
      const hours = Math.floor((minutes % 1440) / 60);
      return `${days}天${hours > 0 ? hours + '小时' : ''}`;
    }
  };

  const columns = [
    {
      title: '网站名称',
      dataIndex: 'site_name',
      key: 'site_name',
      width: 200,
      render: (text: string, record: WebsiteStatus) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>{record.domain}</Text>
        </div>
      ),
    },
    {
      title: '当前状态',
      key: 'status',
      width: 120,
      render: (record: WebsiteStatus) => (
        <Tag 
          color={getStatusColor(record.status_type)} 
          icon={getStatusIcon(record.status_type)}
        >
          {record.status_display}
        </Tag>
      ),
    },
    {
      title: '连续失败',
      dataIndex: 'consecutive_failures',
      key: 'consecutive_failures',
      width: 100,
      render: (failures: number) => (
        <Text type={failures >= 5 ? 'danger' : failures > 0 ? 'warning' : 'success'}>
          {failures}次
        </Text>
      ),
    },
    {
      title: '成功率',
      dataIndex: 'success_rate',
      key: 'success_rate',
      width: 120,
      render: (rate: number) => (
        <Progress 
          percent={rate} 
          size="small" 
          status={rate >= 95 ? 'success' : rate >= 80 ? 'normal' : 'exception'}
          format={percent => `${percent}%`}
        />
      ),
    },
    {
      title: '响应时间',
      dataIndex: 'current_response_time',
      key: 'current_response_time',
      width: 100,
      render: (time: number) => (
        <Text type={time > 3000 ? 'danger' : time > 1000 ? 'warning' : 'success'}>
          {time}ms
        </Text>
      ),
    },
    {
      title: '错误持续时长',
      dataIndex: 'error_duration_minutes',
      key: 'error_duration_minutes',
      width: 120,
      render: (minutes: number, record: WebsiteStatus) => {
        if (minutes === 0 || record.status_type === 'success') {
          return <Text type="success">-</Text>;
        }
        return (
          <Text type="danger">
            {formatDuration(minutes)}
          </Text>
        );
      },
    },
    {
      title: '通知状态',
      key: 'notification',
      width: 100,
      render: (record: WebsiteStatus) => (
        <div>
          {record.notification_sent ? (
            <Tooltip title={`已发送${record.notification_count}次通知`}>
              <Tag color="red" icon={<BellOutlined />}>
                已通知
              </Tag>
            </Tooltip>
          ) : (
            <Tag color="default">未通知</Tag>
          )}
        </div>
      ),
    },
    {
      title: '最后检测',
      key: 'last_check',
      width: 150,
      render: (record: WebsiteStatus) => {
        const lastTime = record.last_failure_time || record.last_success_time;
        return lastTime ? (
          <Tooltip title={moment(lastTime).format('YYYY-MM-DD HH:mm:ss')}>
            <Text>{moment(lastTime).fromNow()}</Text>
          </Tooltip>
        ) : '-';
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (record: WebsiteStatus) => (
        <Button
          type="link"
          icon={<HistoryOutlined />}
          onClick={() => showHistory(record)}
          size="small"
        >
          历史
        </Button>
      ),
    },
  ];

  // 统计数据
  const totalWebsites = statusData.length;
  const normalWebsites = statusData.filter(w => w.status_type === 'success').length;
  const warningWebsites = statusData.filter(w => w.status_type === 'warning').length;
  const errorWebsites = statusData.filter(w => w.status_type === 'error').length;
  const notificationWebsites = statusData.filter(w => w.notification_sent).length;

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Title level={2}>
            <ClockCircleOutlined /> 网站状态监控
          </Title>
          <Text type="secondary">
            实时监控网站存活状态，每分钟自动检测，连续失败5次自动发送通知
          </Text>
        </div>

        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={4}>
            <Card>
              <Statistic
                title="总网站数"
                value={totalWebsites}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="正常网站"
                value={normalWebsites}
                valueStyle={{ color: '#3f8600' }}
                prefix={<CheckCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="警告网站"
                value={warningWebsites}
                valueStyle={{ color: '#fa8c16' }}
                prefix={<ExclamationCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="异常网站"
                value={errorWebsites}
                valueStyle={{ color: '#cf1322' }}
                prefix={<CloseCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="已发通知"
                value={notificationWebsites}
                valueStyle={{ color: '#722ed1' }}
                prefix={<BellOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                loading={loading}
                onClick={fetchStatusData}
                block
              >
                刷新数据
              </Button>
            </Card>
          </Col>
        </Row>

        <Card title="网站状态详情" style={{ marginTop: '24px' }}>
          <Table
            columns={columns}
            dataSource={statusData}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            scroll={{ x: 1200 }}
            size="small"
            rowClassName={(record) => {
              if (record.status_type === 'error') return 'error-row';
              if (record.status_type === 'warning') return 'warning-row';
              return '';
            }}
          />
        </Card>
      </Card>

      {/* 检测历史弹窗 */}
      <Modal
        title={`检测历史 - ${selectedWebsite?.site_name}`}
        open={historyVisible}
        onCancel={() => setHistoryVisible(false)}
        footer={null}
        width={800}
      >
        <Timeline
          items={historyData.map((item, index) => ({
            color: item.is_accessible ? 'green' : 'red',
            children: (
              <div key={index}>
                <div style={{ fontWeight: 'bold' }}>
                  {moment(item.check_time).format('MM-DD HH:mm:ss')}
                  <Tag 
                    color={item.is_accessible ? 'green' : 'red'} 
                    style={{ marginLeft: '8px' }}
                  >
                    {item.is_accessible ? '正常' : `${item.status_code || '无响应'}`}
                  </Tag>
                  <Text type="secondary">({item.response_time}ms)</Text>
                </div>
                {item.error_message && (
                  <Text type="danger" style={{ fontSize: '12px' }}>
                    {item.error_message}
                  </Text>
                )}
              </div>
            ),
          }))}
        />
      </Modal>

      <style jsx>{`
        .error-row {
          background-color: #fff2f0;
        }
        .warning-row {
          background-color: #fff7e6;
        }
      `}</style>
    </div>
  );
};

export default StatusMonitor;
