import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Input,
  Select,
  Tag,
  message,
  Typography,
  Row,
  Col,
  Tooltip,
  Badge,
  Checkbox,
  Alert,
  Statistic,
  Modal,
  Switch,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  KeyOutlined,
  ReloadOutlined,
  GlobalOutlined,
  LinkOutlined,
  SafetyCertificateOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  DeleteOutlined,

  Bar<PERSON>hartOutlined,
  ExportOutlined,
  FilterOutlined,
  SettingOutlined,
  FileImageOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { Website, QueryParams } from '../../types';
import ExportButton from '../../components/Export/ExportButton';
import { ExportApi } from '../../services/export';
import WebsiteForm from '../../components/Website/WebsiteForm';
import { WebsiteApi } from '../../services/website';
import BatchOperations from '../../components/Website/BatchOperations';
import AttachmentIcon from '../../components/Website/AttachmentIcon';
import PasswordManager from '../../components/Website/PasswordManager';
import ResizableTable from '../../components/Table/ResizableTable';
import { cacheManager } from '../../utils/performance';
// 权限控制相关导入
import { PermissionGuard, RequirePermission, PermissionButton, PermissionAwareButton } from '../../components/Permission/PermissionGuard';
import { usePermissions } from '../../contexts/PermissionContext';


const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

const WebsiteList: React.FC = () => {
  // 权限检查Hook
  const { hasPermission } = usePermissions();
  
  const [loading, setLoading] = useState(false);
  const [websites, setWebsites] = useState<Website[]>([]);
  const [filteredWebsites, setFilteredWebsites] = useState<Website[]>([]);
  const [total, setTotal] = useState(0);
  const [formVisible, setFormVisible] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [currentWebsite, setCurrentWebsite] = useState<Website | undefined>();
  const [selectedWebsites, setSelectedWebsites] = useState<Website[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [stats, setStats] = useState<any>({});
  const [statsVisible, setStatsVisible] = useState(false);
  const [industries, setIndustries] = useState<string[]>([]);
  const [platformOptions, setPlatformOptions] = useState<string[]>([]);

  // 密码管理相关状态
  const [passwordManagerVisible, setPasswordManagerVisible] = useState(false);
  const [currentPasswordWebsite, setCurrentPasswordWebsite] = useState<Website | null>(null);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 50, // 合理的分页大小
    search: '',
    status: 'active', // 默认显示状态正常的网站
    industry: '',
    sortBy: 'siteId', // 默认按site_id排序
    sortOrder: 'desc', // 降序
  });

  // 基础页面完全从数据库读取数据，不使用模拟数据


  // 获取网站列表
  const fetchWebsites = async (retryCount = 0, forceRefresh = false) => {
    setLoading(true);
    try {
      // 使用真正的分页和后端搜索
      const apiParams = {
        page: queryParams.page,
        limit: queryParams.pageSize,
        search: queryParams.search,
        status: queryParams.status,
        platform: queryParams.platform,
        server: '',
        industry: queryParams.industry,
        sortBy: queryParams.sortBy,
        sortOrder: queryParams.sortOrder,
        // 强制刷新时添加时间戳绕过缓存
        ...(forceRefresh && { _refresh: Date.now() })
      };
      const response = await WebsiteApi.getWebsites(apiParams);
      console.log('基础页面API响应:', response);

      // 正确解析API响应数据 - 与增强页面保持一致
      let websitesData = [];
      if (response.data && response.data.websites && Array.isArray(response.data.websites)) {
        websitesData = response.data.websites;
      } else {
        console.warn('API响应格式不正确:', response);
        websitesData = [];
      }

      // 确保数据格式一致
      const formattedWebsites = websitesData.map((website: any) => {
        const formatted = {
          ...website,
          // 确保所有必要字段都存在
          siteName: website.siteName || website.domain,
          siteUrl: website.siteUrl || `https://${website.domain}`,
          projectAmount: website.projectAmount || null,
          renewalFee: website.renewalFee || null,
          notes: website.notes || null,

          // 访问状态相关字段
          accessStatusCode: website.accessStatusCode || null,
          accessStatus: website.accessStatus || null,
          consecutiveFailures: website.consecutiveFailures || 0,
          notificationSent: website.notificationSent || false,
          responseTime: website.responseTime || null,
          lastCheckTime: website.lastCheckTime || null,
          // 兼容SSL和域名到期时间字段
          sslExpireDate: website.sslExpireDate || website.sslInfo?.validTo,
          domainExpireDate: website.domainExpireDate || website.domainInfo?.expirationDate
        };



        return formatted;
      });


      setWebsites(formattedWebsites);
      setFilteredWebsites(formattedWebsites); // 直接使用后端返回的数据
      setTotal(response.data.pagination?.total || formattedWebsites.length);
    } catch (error: any) {
      console.error('获取网站列表失败:', error);

      // 如果是超时错误且重试次数少于2次，则自动重试
      if (error.code === 'ECONNABORTED' && retryCount < 2) {
        console.log(`网络超时，正在进行第${retryCount + 1}次重试...`);
        setTimeout(() => {
          fetchWebsites(retryCount + 1);
        }, 2000 * (retryCount + 1)); // 递增延迟重试
        return;
      }

      // 不使用模拟数据，直接显示错误
      setWebsites([]);
      setTotal(0);

      // 显示用户友好的错误信息
      if (error.code === 'ECONNABORTED') {
        message.error('网络请求超时，请检查网络连接或稍后重试');
      } else {
        message.error('获取网站列表失败，请检查网络连接');
      }
    } finally {
      setLoading(false);
    }
  };



  // 获取行业选项
  const fetchIndustries = async () => {
    try {
      // 优先从设置API获取行业类型
      const response = await fetch('http://localhost:3001/api/v1/settings/industries');
      const result = await response.json();
      if (result.success) {
        setIndustries(result.data || []);
      } else {
        // 回退到原有API
        const fallbackResponse = await WebsiteApi.getIndustries();
        setIndustries(fallbackResponse.data || []);
      }
    } catch (error) {
      console.error('获取行业选项失败:', error);
      // 使用默认行业
      setIndustries(['电商', '企业官网', '博客', '论坛', '新闻资讯', '教育培训', '医疗健康', '金融服务']);
    }
  };

  // 获取平台选项（从设置API获取）
  const fetchPlatforms = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/platforms', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || 'dev-token'}`
        }
      });
      const result = await response.json();
      if (result.success) {
        // 处理新的API数据格式：对象数组转换为字符串数组
        const platforms = result.data || [];
        if (platforms.length > 0 && typeof platforms[0] === 'object') {
          // 新格式：对象数组，只取活跃的平台
          const activePlatforms = platforms
            .filter((platform: any) => platform.isActive)
            .map((platform: any) => platform.name);
          setPlatformOptions(activePlatforms);
        } else {
          // 旧格式：字符串数组
          setPlatformOptions(platforms);
        }
      } else {
        console.error('获取平台类型失败:', result.message);
        // 使用默认平台
        setPlatformOptions(['WordPress', 'Shopify', 'Magento', 'WooCommerce', '自定义']);
      }
    } catch (error) {
      console.error('获取平台类型失败:', error);
      // 使用默认平台
      setPlatformOptions(['WordPress', 'Shopify', 'Magento', 'WooCommerce', '自定义']);
    }
  };

  useEffect(() => {
    // 检查认证状态
    const token = localStorage.getItem('token');
    if (!token) {
      message.error('请先登录');
      window.location.href = '/login';
      return;
    }
    
    fetchIndustries();
    fetchPlatforms();
  }, []);

  // 当筛选条件变化时，重新获取数据
  useEffect(() => {
    console.log('WebsiteList useEffect 触发，queryParams:', queryParams);
    // 清除缓存并强制刷新
    cacheManager.clear();
    fetchWebsites(0, true); // 强制刷新
  }, [queryParams.page, queryParams.pageSize, queryParams.search, queryParams.status, queryParams.platform, queryParams.industry, queryParams.sortBy, queryParams.sortOrder]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  // 处理筛选
  const handleFilter = (key: string, value: string) => {
    setQueryParams(prev => ({
      ...prev,
      [key]: value,
      page: 1,
    }));
  };

  // 处理分页
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setQueryParams(prev => ({
      ...prev,
      page: pagination.current,
      pageSize: pagination.pageSize,
      sortBy: sorter.field || 'created_at',
      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc',
    }));
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    try {
      await WebsiteApi.deleteWebsite(id);
      message.success('网站删除成功');
      fetchWebsites();
    } catch (error) {
      console.error('删除网站失败:', error);
      message.error('删除网站失败');
    }
  };

  // 从当前网站数据计算统计信息
  const calculateStats = (websites: Website[]) => {
    const total = websites.length;

    // 按状态统计
    const byStatus = [
      { status: 'active', count: websites.filter(w => w.status === 'active').length },
      { status: 'inactive', count: websites.filter(w => w.status === 'inactive').length },
      { status: 'suspended', count: websites.filter(w => w.status === 'suspended').length },
      { status: 'expired', count: websites.filter(w => w.status === 'expired').length },
    ];

    // 按平台统计 - 基于平台筛选选项
    const platformCounts = new Map<string, number>();

    // 初始化所有平台选项的计数为0
    platformOptions.forEach(platform => {
      platformCounts.set(platform, 0);
    });

    // 统计每个平台的网站数量
    websites.forEach(website => {
      let platform = '其他';
      if (website.platform) {
        if (typeof website.platform === 'string') {
          platform = website.platform;
        } else if (website.platform.name) {
          platform = website.platform.name;
        }
      }

      // 如果平台在选项中，则计数；否则归类为"其他"
      if (platformOptions.includes(platform)) {
        platformCounts.set(platform, (platformCounts.get(platform) || 0) + 1);
      } else {
        platformCounts.set('其他', (platformCounts.get('其他') || 0) + 1);
      }
    });

    // 添加"其他"分类（如果有的话）
    if (!platformOptions.includes('其他') && platformCounts.get('其他') && platformCounts.get('其他')! > 0) {
      platformCounts.set('其他', platformCounts.get('其他') || 0);
    }

    // 只显示有网站的平台（计数大于0）
    const byPlatform = Array.from(platformCounts.entries())
      .filter(([name, count]) => count > 0)
      .map(([name, count]) => ({ name, count }));

    // 按服务器统计
    const serverCounts = new Map<string, number>();
    websites.forEach(website => {
      let server = '未知服务器';
      if (website.server?.name) {
        server = website.server.name;
      }
      serverCounts.set(server, (serverCounts.get(server) || 0) + 1);
    });
    const byServer = Array.from(serverCounts.entries()).map(([name, count]) => ({ name, count }));

    // 即将到期的网站（30天内）
    const now = new Date();
    const thirtyDaysLater = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    const expiringSoon = websites.filter(website => {
      if (!website.expireDate) return false;
      const expireDate = new Date(website.expireDate);
      return expireDate >= now && expireDate <= thirtyDaysLater;
    }).length;

    return {
      total,
      byStatus,
      byPlatform,
      byServer,
      expiringSoon
    };
  };

  // 处理行选择
  const handleRowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[], newSelectedRows: Website[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
      setSelectedWebsites(newSelectedRows);
    },
    onSelectAll: (selected: boolean, selectedRows: Website[], changeRows: Website[]) => {
      if (selected) {
        setSelectedWebsites([...selectedWebsites, ...changeRows]);
      } else {
        const changeIds = changeRows.map(row => row.id);
        setSelectedWebsites(selectedWebsites.filter(item => !changeIds.includes(item.id)));
      }
    },
  };

  // 清除选择
  const clearSelection = () => {
    setSelectedRowKeys([]);
    setSelectedWebsites([]);
  };

  // 批量操作成功回调
  const handleBatchSuccess = () => {
    fetchWebsites();
    clearSelection();
  };

  // 处理新建网站
  const handleCreate = () => {
    setFormMode('create');
    setCurrentWebsite(undefined);
    setFormVisible(true);
  };

  // 处理编辑网站
  const handleEdit = (website: Website) => {
    setFormMode('edit');
    setCurrentWebsite(website);
    setFormVisible(true);
  };

  // 处理密码管理
  const handlePasswordManagement = (website: Website) => {
    setCurrentPasswordWebsite(website);
    setPasswordManagerVisible(true);
  };



  // 处理表单提交成功
  const handleFormSuccess = () => {
    setFormVisible(false);
    setCurrentWebsite(undefined);
    fetchWebsites();
    message.success(formMode === 'create' ? '网站创建成功' : '网站更新成功');
  };

  // 处理表单取消
  const handleFormCancel = () => {
    setFormVisible(false);
    setCurrentWebsite(undefined);
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: 'success', text: '正常' },
      inactive: { color: 'default', text: '停用' },
      suspended: { color: 'warning', text: '暂停' },
      expired: { color: 'error', text: '过期' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取访问状态
  const getAccessStatus = (statusCode?: number, consecutiveFailures?: number, notificationSent?: boolean) => {
    // 如果没有状态码或状态码为0，显示未检测
    if (!statusCode || statusCode === 0 || statusCode === null || statusCode === undefined) {
      return <Tag color="default">未检测</Tag>;
    }

    // 正常状态 (200-399都算正常)
    if (statusCode >= 200 && statusCode < 400) {
      return <Tag color="success" icon={<CheckCircleOutlined />}>正常</Tag>;
    }

    // 异常状态 (400+)
    const failureText = consecutiveFailures && consecutiveFailures > 0
      ? `${statusCode} (${consecutiveFailures}次)`
      : statusCode.toString();

    return (
      <Tag
        color="error"
        icon={<WarningOutlined />}
        title={consecutiveFailures && consecutiveFailures > 0
          ? `连续失败 ${consecutiveFailures} 次${notificationSent ? '，已发送通知' : ''}`
          : `HTTP状态码: ${statusCode}`
        }
      >
        {failureText}
      </Tag>
    );
  };



  // 检查SSL状态（基于数据库真实数据）
  const checkSSLStatus = (website: Website) => {
    // 如果有SSL信息，使用SSL信息
    if (website.sslInfo) {
      // 检查SSL状态
      if (website.sslInfo.status === 'invalid') {
        return <Badge status="error" text="无效" />;
      }

      // 如果状态是unknown，显示未知
      if (website.sslInfo.status === 'unknown') {
        return <Badge status="default" text="未知" />;
      }

      // 检查到期时间
      if (website.sslInfo.expiryDate || website.sslInfo.validTo) {
        const expireDate = website.sslInfo.expiryDate || website.sslInfo.validTo;
        const expireTime = new Date(expireDate).getTime();
        const now = new Date().getTime();
        const daysLeft = Math.ceil((expireTime - now) / (1000 * 60 * 60 * 24));

        if (daysLeft <= 0) {
          return <Badge status="error" text="已过期" />;
        } else if (daysLeft <= 7) {
          return <Badge status="error" text={`${daysLeft}天后过期`} />;
        } else if (daysLeft <= 30) {
          return <Badge status="warning" text={`${daysLeft}天后过期`} />;
        } else {
          // 正常状态也显示剩余天数
          return <Badge status="success" text={`${daysLeft}天后过期`} />;
        }
      }

      // 如果有SSL信息但没有到期时间，根据状态显示
      if (website.sslInfo.status === 'valid') {
        return <Badge status="success" text="有效" />;
      }
    }

    return <Badge status="default" text="未检测" />;
  };

  // 获取行业信息（从数据库读取）
  const getIndustryInfo = (industry?: string) => {
    return industry || '未分类';
  };

  // 计算到期天数
  const getDaysUntilExpiry = (expireDate: string) => {
    if (!expireDate) return null;
    const today = new Date();
    const expire = new Date(expireDate);
    const diffTime = expire.getTime() - today.getTime();
    const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return days;
  };

  // 检查是否需要标红显示（已过期、本月或次月到期）
  const shouldShowRed = (expireDate: string) => {
    if (!expireDate) return false;

    const today = new Date();
    const expire = new Date(expireDate);

    // 已过期
    if (expire < today) {
      return true;
    }

    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();
    const expireMonth = expire.getMonth();
    const expireYear = expire.getFullYear();

    // 本月到期
    if (expireYear === currentYear && expireMonth === currentMonth) {
      return true;
    }

    // 次月到期
    const nextMonth = currentMonth === 11 ? 0 : currentMonth + 1;
    const nextYear = currentMonth === 11 ? currentYear + 1 : currentYear;

    if (expireYear === nextYear && expireMonth === nextMonth) {
      return true;
    }

    return false;
  };

  // 表格列配置
  const columns: ColumnsType<Website> = [
    {
      title: '站点名称',
      dataIndex: 'siteName',
      key: 'siteName',
      width: 280, // 进一步增加宽度以更好利用空间
      render: (siteName, record) => (
        <div style={{ minWidth: 0, width: '100%' }}>
          <div style={{
            fontWeight: 500,
            marginBottom: 4,
            fontSize: 13,
            display: 'flex',
            alignItems: 'center',
            gap: 4,
            minWidth: 0
          }}>
            <GlobalOutlined style={{ color: '#1890ff', fontSize: 12, flexShrink: 0 }} />
            <span
              title={siteName || record.domain}
              style={{
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                minWidth: 0,
                flex: 1
              }}
            >
              {siteName || record.domain}
            </span>
          </div>
          <div style={{
            fontSize: 11,
            color: '#666',
            display: 'flex',
            gap: 4,
            flexWrap: 'wrap',
            minWidth: 0
          }}>
            <Tag color="blue" style={{ fontSize: 10, padding: '0 4px', margin: 0 }}>
              {record.platform?.name}
            </Tag>
            {record.industry && (
              <Tag color="geekblue" style={{ fontSize: 10, padding: '0 4px', margin: 0 }}>
                {record.industry}
              </Tag>
            )}
          </div>
        </div>
      ),
      sorter: (a, b) => {
        const aName = a.siteName || a.domain || '';
        const bName = b.siteName || b.domain || '';
        return aName.localeCompare(bName);
      },
      sortDirections: ['ascend', 'descend'],
    },
    {
      title: '站点URL',
      dataIndex: 'siteUrl',
      key: 'siteUrl',
      width: 160,
      render: (url, record) => {
        // 简化URL显示，去掉协议和www
        const displayUrl = url?.replace(/^https?:\/\/(www\.)?/, '') || '';
        const isInactive = record.status === 'inactive';

        return (
          <div>
            <div style={{ marginBottom: 4 }}>
              <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  color: isInactive ? '#999' : '#1890ff',
                  textDecoration: isInactive ? 'line-through' : 'none',
                  fontSize: 13
                }}
                title={`${url}${isInactive ? ' (站点已停用，不再维护)' : ''}`}
              >
                {displayUrl.length > 20 ? displayUrl.substring(0, 20) + '...' : displayUrl}
              </a>
            </div>
            <div style={{ fontSize: 12, display: 'flex', gap: 4, flexWrap: 'wrap' }}>
              {getAccessStatus(record.accessStatusCode, record.consecutiveFailures, record.notificationSent)}
              {checkSSLStatus(record)}
            </div>
          </div>
        );
      },
    },
    {
      title: '上线时间',
      dataIndex: 'onlineDate',
      key: 'onlineDate',
      width: 85, // 减少宽度
      render: (date, record) => {
        if (!date) {
          return (
            <div style={{ textAlign: 'center' }}>
              <Text type="secondary">未上线</Text>
              {record.project?.onlineStatus === 'online' && record.project?.onlineDate && (
                <div style={{ fontSize: 12, color: '#1890ff' }}>
                  项目已上线
                </div>
              )}
            </div>
          );
        }
        return (
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontWeight: 500 }}>
              {new Date(date).toLocaleDateString()}
            </div>
            <div style={{ fontSize: 12, color: '#52c41a' }}>
              已上线
            </div>
          </div>
        );
      },
      sorter: true,
    },
    {
      title: '到期时间',
      dataIndex: 'expireDate',
      key: 'expireDate',
      width: 95, // 减少宽度
      render: (date) => {
        if (!date) {
          return <Text type="secondary">-</Text>;
        }

        const siteDays = getDaysUntilExpiry(date);
        const showRed = shouldShowRed(date);

        return (
          <div style={{ textAlign: 'center' }}>
            <div style={{
              color: showRed ? '#ff4d4f' : '#52c41a',
              fontWeight: 500,
              fontSize: 14
            }}>
              {new Date(date).toLocaleDateString()}
            </div>
            {siteDays !== null && (
              <div style={{
                fontSize: 12,
                color: showRed ? '#ff4d4f' : '#52c41a',
                marginTop: 2
              }}>
                {siteDays > 0 ? `${siteDays}天后到期` : siteDays === 0 ? '今天到期' : '已过期'}
              </div>
            )}
          </div>
        );
      },
      sorter: true,
    },
    {
      title: '项目金额',
      dataIndex: 'projectAmount',
      key: 'projectAmount',
      width: 100,
      render: (amount) => (
        <div style={{ textAlign: 'center' }}>
          {amount ? (
            <span style={{ fontWeight: 500, color: '#1890ff', fontSize: 14 }}>
              ¥{amount.toLocaleString()}
            </span>
          ) : (
            <Text type="secondary">-</Text>
          )}
        </div>
      ),
      sorter: true,
    },
    {
      title: '续费金额',
      dataIndex: 'renewalFee',
      key: 'renewalFee',
      width: 100,
      render: (fee) => (
        <div style={{ textAlign: 'center' }}>
          {fee ? (
            <span style={{ fontWeight: 500, color: '#52c41a', fontSize: 14 }}>
              ¥{fee.toLocaleString()}
            </span>
          ) : (
            <Text type="secondary">-</Text>
          )}
        </div>
      ),
      sorter: true,
    },
    {
      title: 'Onboard',
      dataIndex: 'hasOnboard',
      key: 'hasOnboard',
      width: 100,
      align: 'center',
      render: (hasOnboard) => (
        <Switch
          checked={hasOnboard}
          disabled
          size="small"
          checkedChildren="已安装"
          unCheckedChildren="未安装"
        />
      ),
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
      width: 120,
      render: (notes) => (
        <div style={{ maxWidth: 100 }}>
          {notes ? (
            <Tooltip title={notes} placement="topLeft">
              <Text
                ellipsis
                style={{
                  fontSize: 12,
                  color: '#666',
                  cursor: 'pointer',
                  display: 'block',
                  maxWidth: '100%'
                }}
              >
                {notes.length > 15 ? notes.substring(0, 15) + '...' : notes}
              </Text>
            </Tooltip>
          ) : (
            <Text type="secondary" style={{ fontSize: 12 }}>-</Text>
          )}
        </div>
      ),
    },
    {
      title: '附件',
      key: 'attachments',
      width: 80, // 减少宽度
      render: (_, record) => (
        <AttachmentIcon websiteId={record.id} />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record) => (
        <Space size={4}>
          <PermissionAwareButton
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            size="small"
            style={{ padding: '0 4px' }}
            permissions={['site.website.edit']}
            hideWhenNoPermission={false}
          >
            编辑
          </PermissionAwareButton>
          <PermissionAwareButton
            type="link"
            icon={<KeyOutlined />}
            onClick={() => handlePasswordManagement(record)}
            size="small"
            style={{ padding: '0 4px' }}
            title="密码管理"
            permissions={['site.website.credential']}
            hideWhenNoPermission={false}
          >
            密码
          </PermissionAwareButton>

        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>网站管理</Title>
      
      <Card>
        {/* 工具栏 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={8} md={6}>
            <Search
              placeholder="搜索网站名称、URL、备注"
              allowClear
              value={queryParams.search}
              onChange={(e) => handleSearch(e.target.value)}
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={8} sm={4} md={3}>
            <Select
              placeholder="网站状态"
              value={queryParams.status || ''}
              style={{ width: '100%' }}
              onChange={(value) => handleFilter('status', value || '')}
            >
              <Option value="active">正常</Option>
              <Option value="">显示全部</Option>
              <Option value="inactive">停用</Option>
              <Option value="suspended">暂停</Option>
              <Option value="expired">过期</Option>
            </Select>
          </Col>
          <Col xs={8} sm={4} md={3}>
            <Select
              placeholder="平台类型"
              allowClear
              value={queryParams.platform || undefined}
              style={{ width: '100%' }}
              onChange={(value) => handleFilter('platform', value || '')}
            >
              {platformOptions.map(platform => (
                <Option key={platform} value={platform}>{platform}</Option>
              ))}
            </Select>
          </Col>
          <Col xs={8} sm={4} md={3}>
            <Select
              placeholder="所属行业"
              allowClear
              value={queryParams.industry || undefined}
              style={{ width: '100%' }}
              onChange={(value) => handleFilter('industry', value || '')}
            >
              {industries.map(industry => (
                <Option key={industry} value={industry}>
                  {industry}
                </Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={8} md={9}>
            <Space wrap>
              <PermissionAwareButton
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreate}
                permissions={['site.website.create']}
                hideWhenNoPermission={false}
              >
                新建网站
              </PermissionAwareButton>
              <Button
                icon={<ReloadOutlined />}
                onClick={async () => {
                  setLoading(true);
                  try {
                    console.log('刷新按钮被点击');
                    // 清除前端缓存
                    cacheManager.clear();

                    // 强制刷新网站数据（绕过缓存）
                    await fetchWebsites(0, true); // 添加强制刷新参数

                    // 只在必要时刷新配置数据
                    if (industries.length === 0) {
                      await fetchIndustries();
                    }
                    if (platformOptions.length === 0) {
                      await fetchPlatforms();
                    }

                    message.success('数据已刷新');
                  } catch (error) {
                    console.error('刷新失败:', error);
                    message.error('刷新失败，请重试');
                  } finally {
                    setLoading(false);
                  }
                }}
                loading={loading}
              >
                刷新
              </Button>

              <Button
                icon={<BarChartOutlined />}
                onClick={() => {
                  const currentStats = calculateStats(websites);
                  setStats(currentStats);
                  setStatsVisible(true);
                }}
              >
                统计
              </Button>

              <ExportButton
                exportType="websites"
                queryParams={queryParams}
                onExport={ExportApi.exportWebsites}
                totalCount={total}
              />
            </Space>
          </Col>
        </Row>

        {/* 筛选状态提示 */}
        {(queryParams.search || (queryParams.status && queryParams.status !== 'active') || queryParams.platform || queryParams.industry) && (
          <Alert
            message={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8, flexWrap: 'wrap' }}>
                <span>当前筛选条件：</span>
                {queryParams.search && (
                  <Tag color="blue">搜索: {queryParams.search}</Tag>
                )}
                {queryParams.status && queryParams.status !== 'active' && (
                  <Tag color="green">状态: {queryParams.status === '' ? '显示全部' : queryParams.status}</Tag>
                )}
                {queryParams.platform && (
                  <Tag color="orange">平台: {queryParams.platform}</Tag>
                )}
                {queryParams.industry && (
                  <Tag color="purple">行业: {queryParams.industry}</Tag>
                )}
                <span>共找到 {filteredWebsites.length} 个结果</span>
                <Button
                  type="link"
                  size="small"
                  onClick={() => {
                    setQueryParams({
                      page: 1,
                      pageSize: 50,
                      search: '',
                      status: '', // 清除筛选时显示全部
                      platform: '', // 清除平台筛选
                      industry: '',
                      sortBy: 'siteId',
                      sortOrder: 'desc',
                    });
                  }}
                  style={{ padding: 0, height: 'auto' }}
                >
                  清除筛选
                </Button>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {/* 选择提示 */}
        {selectedWebsites.length > 0 && (
          <Alert
            message={`已选择 ${selectedWebsites.length} 个网站`}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
            action={
              <Button size="small" onClick={clearSelection}>
                取消选择
              </Button>
            }
          />
        )}

        {/* 数据统计 */}
        <div style={{ marginBottom: 16, padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
          <Text type="secondary">
            共 <Text strong style={{ color: '#1890ff' }}>{total}</Text> 个网站
            {queryParams.status === '' && ' (显示全部状态)'}
            {queryParams.status === 'inactive' && ' (仅显示停用状态)'}
            {queryParams.status === 'suspended' && ' (仅显示暂停状态)'}
            {queryParams.status === 'expired' && ' (仅显示过期状态)'}
            {(queryParams.search || queryParams.industry || queryParams.platform) &&
              ` (已筛选，当前页显示 ${filteredWebsites.length} 条)`
            }
          </Text>
        </div>

        {/* 表格 */}
        <ResizableTable
          columns={columns}
          dataSource={filteredWebsites}
          rowKey="id"
          loading={loading}
          rowSelection={handleRowSelection}
          scroll={{ x: 'max-content', y: 600 }}
          bordered
          size="small"
          resizable={true}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            pageSizeOptions: ['20', '50', '100', '200', '500', '1000', '10000'],
            defaultPageSize: 50,
            showSizeChangerLabel: (current, size) => {
              if (size >= 10000) return '显示全部';
              return `${size}条/页`;
            }
          }}
          onChange={handleTableChange}
        />
      </Card>

      {/* 批量操作组件 */}
      <BatchOperations
        selectedWebsites={selectedWebsites}
        onSuccess={handleBatchSuccess}
        onClearSelection={clearSelection}
      />

      {/* 网站表单 */}
      <WebsiteForm
        visible={formVisible}
        mode={formMode}
        initialValues={currentWebsite}
        onSuccess={handleFormSuccess}
        onCancel={handleFormCancel}
      />

      {/* 统计模态框 */}
      <Modal
        title="网站统计"
        open={statsVisible}
        onCancel={() => setStatsVisible(false)}
        footer={null}
        width={800}
      >
        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Statistic
              title="网站总数"
              value={stats.total || 0}
              prefix={<GlobalOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="正常网站"
              value={stats.byStatus?.find((s: any) => s.status === 'active')?.count || 0}
              valueStyle={{ color: '#52c41a' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="停用网站"
              value={stats.byStatus?.find((s: any) => s.status === 'inactive')?.count || 0}
              valueStyle={{ color: '#faad14' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="即将到期"
              value={stats.expiringSoon || 0}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Col>
        </Row>

        <div style={{ marginTop: 24 }}>
          <Typography.Title level={5}>平台分布</Typography.Title>
          <Row gutter={[16, 16]}>
            {stats.byPlatform?.map((platform: any) => (
              <Col span={8} key={platform.name}>
                <Card size="small">
                  <Statistic
                    title={platform.name}
                    value={platform.count}
                    suffix="个网站"
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>

        <div style={{ marginTop: 24 }}>
          <Typography.Title level={5}>服务器分布</Typography.Title>
          <Row gutter={[16, 16]}>
            {stats.byServer?.map((server: any) => (
              <Col span={8} key={server.name}>
                <Card size="small">
                  <Statistic
                    title={server.name}
                    value={server.count}
                    suffix="个网站"
                  />
                </Card>
              </Col>
            ))}
          </Row>
        </div>
      </Modal>

      {/* 密码管理模态框 */}
      {currentPasswordWebsite && (
        <PasswordManager
          visible={passwordManagerVisible}
          onClose={() => {
            setPasswordManagerVisible(false);
            setCurrentPasswordWebsite(null);
          }}
          websiteId={currentPasswordWebsite.id}
          websiteName={currentPasswordWebsite.siteName || currentPasswordWebsite.domain}
        />
      )}
    </div>
  );
};

export default WebsiteList;
