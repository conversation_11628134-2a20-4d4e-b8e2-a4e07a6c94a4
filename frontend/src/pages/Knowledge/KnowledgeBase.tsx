import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  message,
  Modal,
  Form,
  Input,
  Select,
  Row,
  Col,
  Statistic,
  Badge,
  Avatar,
  List,
  Tabs,
  Rate,
  Typography,
  Divider,
  Tree
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  FileTextOutlined,
  UserOutlined,
  BookOutlined,
  QuestionCircleOutlined,
  BulbOutlined,
  ToolOutlined,
  SearchOutlined,
  LikeOutlined,
  DislikeOutlined,
  ShareAltOutlined,
  FolderOutlined,
  TagOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { KnowledgeBase as KB, User, QueryParams } from '../../types';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Title, Paragraph, Text } = Typography;
const { Search } = Input;

interface KnowledgeBaseProps {}

const KnowledgeBase: React.FC<KnowledgeBaseProps> = () => {
  const [articles, setArticles] = useState<KB[]>([]);
  const [loading, setLoading] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentArticle, setCurrentArticle] = useState<KB | undefined>();
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  const [searchText, setSearchText] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10
  });
  const [form] = Form.useForm();

  // 文章类型配置
  const typeConfig = {
    article: { label: '技术文章', color: 'blue', icon: <FileTextOutlined /> },
    faq: { label: '常见问题', color: 'green', icon: <QuestionCircleOutlined /> },
    tutorial: { label: '操作教程', color: 'orange', icon: <BulbOutlined /> },
    troubleshooting: { label: '故障排除', color: 'red', icon: <ToolOutlined /> }
  };

  // 状态配置
  const statusConfig = {
    draft: { label: '草稿', color: 'gray' },
    published: { label: '已发布', color: 'green' },
    archived: { label: '已归档', color: 'orange' }
  };

  // 分类树数据
  const categoryTree = [
    {
      title: '网站管理',
      key: 'website',
      icon: <FolderOutlined />,
      children: [
        { title: 'WordPress', key: 'website-wordpress' },
        { title: 'SSL证书', key: 'website-ssl' },
        { title: '域名管理', key: 'website-domain' },
        { title: '性能优化', key: 'website-performance' }
      ]
    },
    {
      title: '服务器运维',
      key: 'server',
      icon: <FolderOutlined />,
      children: [
        { title: 'Linux系统', key: 'server-linux' },
        { title: '数据库', key: 'server-database' },
        { title: '监控告警', key: 'server-monitoring' },
        { title: '备份恢复', key: 'server-backup' }
      ]
    },
    {
      title: '客户服务',
      key: 'service',
      icon: <FolderOutlined />,
      children: [
        { title: '工单处理', key: 'service-ticket' },
        { title: '客户沟通', key: 'service-communication' },
        { title: '问题解答', key: 'service-qa' }
      ]
    }
  ];

  // 获取知识库数据
  const fetchArticles = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockData: KB[] = [
        {
          id: 1,
          title: 'WordPress网站性能优化指南',
          content: '本文详细介绍了WordPress网站性能优化的各种方法和技巧...',
          category: 'website-wordpress',
          tags: ['WordPress', '性能优化', '缓存'],
          type: 'tutorial',
          status: 'published',
          authorId: 1,
          author: { 
            id: 1, 
            username: 'admin', 
            email: '<EMAIL>', 
            realName: '系统管理员', 
            role: 'admin', 
            status: 'active', 
            createdAt: '2024-01-01', 
            updatedAt: '2024-01-01' 
          },
          viewCount: 245,
          helpful: 18,
          notHelpful: 2,
          relatedArticles: [2, 3],
          attachments: [],
          lastReviewed: '2024-06-01',
          createdAt: '2024-05-15T10:00:00Z',
          updatedAt: '2024-06-01T14:30:00Z'
        },
        {
          id: 2,
          title: 'SSL证书安装和配置',
          content: '详细说明如何在不同服务器环境下安装和配置SSL证书...',
          category: 'website-ssl',
          tags: ['SSL', '证书', '安全'],
          type: 'tutorial',
          status: 'published',
          authorId: 2,
          author: { 
            id: 2, 
            username: 'dev001', 
            email: '<EMAIL>', 
            realName: '李开发', 
            role: 'developer', 
            status: 'active', 
            createdAt: '2024-01-01', 
            updatedAt: '2024-01-01' 
          },
          viewCount: 189,
          helpful: 15,
          notHelpful: 1,
          relatedArticles: [1, 4],
          attachments: [],
          lastReviewed: '2024-05-20',
          createdAt: '2024-05-10T09:00:00Z',
          updatedAt: '2024-05-20T16:00:00Z'
        },
        {
          id: 3,
          title: '常见网站故障排除方法',
          content: '总结了网站运行中常见的故障现象和对应的排除方法...',
          category: 'website',
          tags: ['故障排除', '网站维护', '问题解决'],
          type: 'troubleshooting',
          status: 'published',
          authorId: 1,
          author: { 
            id: 1, 
            username: 'admin', 
            email: '<EMAIL>', 
            realName: '系统管理员', 
            role: 'admin', 
            status: 'active', 
            createdAt: '2024-01-01', 
            updatedAt: '2024-01-01' 
          },
          viewCount: 156,
          helpful: 12,
          notHelpful: 3,
          relatedArticles: [1, 2],
          attachments: [],
          createdAt: '2024-05-05T11:00:00Z',
          updatedAt: '2024-05-25T10:00:00Z'
        },
        {
          id: 4,
          title: '如何备份网站数据？',
          content: '介绍网站数据备份的重要性和具体操作步骤...',
          category: 'server-backup',
          tags: ['备份', '数据安全', '恢复'],
          type: 'faq',
          status: 'published',
          authorId: 2,
          author: { 
            id: 2, 
            username: 'dev001', 
            email: '<EMAIL>', 
            realName: '李开发', 
            role: 'developer', 
            status: 'active', 
            createdAt: '2024-01-01', 
            updatedAt: '2024-01-01' 
          },
          viewCount: 98,
          helpful: 8,
          notHelpful: 1,
          relatedArticles: [3],
          attachments: [],
          createdAt: '2024-04-20T14:00:00Z',
          updatedAt: '2024-04-25T09:00:00Z'
        }
      ];
      setArticles(mockData);
    } catch (error) {
      message.error('获取知识库数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchArticles();
  }, [queryParams]);

  // 过滤文章
  const filteredArticles = articles.filter(article => {
    const matchSearch = !searchText || 
      article.title.toLowerCase().includes(searchText.toLowerCase()) ||
      article.content.toLowerCase().includes(searchText.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchText.toLowerCase()));
    
    const matchCategory = !selectedCategory || article.category === selectedCategory;
    
    return matchSearch && matchCategory;
  });

  // 处理新建
  const handleCreate = () => {
    setFormMode('create');
    setCurrentArticle(undefined);
    form.resetFields();
    setFormVisible(true);
  };

  // 处理编辑
  const handleEdit = (record: KB) => {
    setFormMode('edit');
    setCurrentArticle(record);
    form.setFieldsValue(record);
    setFormVisible(true);
  };

  // 处理查看详情
  const handleDetail = (record: KB) => {
    setCurrentArticle(record);
    setDetailVisible(true);
  };

  // 计算统计数据
  const getStats = () => {
    return {
      total: articles.length,
      published: articles.filter(a => a.status === 'published').length,
      draft: articles.filter(a => a.status === 'draft').length,
      totalViews: articles.reduce((sum, a) => sum + a.viewCount, 0),
      avgHelpful: articles.length > 0 ? 
        Math.round(articles.reduce((sum, a) => sum + a.helpful, 0) / articles.length * 100) / 100 : 0
    };
  };

  const stats = getStats();

  // 表格列定义
  const columns = [
    {
      title: '文章信息',
      dataIndex: 'title',
      key: 'title',
      width: 250,
      render: (title: string, record: KB) => (
        <div>
          <div className="font-medium text-gray-900 mb-1">
            <Space>
              {typeConfig[record.type]?.icon}
              <span className="cursor-pointer hover:text-blue-500" onClick={() => handleDetail(record)}>
                {title}
              </span>
            </Space>
          </div>
          <div className="text-sm text-gray-500">
            <Space size="small">
              {record.tags.slice(0, 2).map(tag => (
                <Tag key={tag} size="small">{tag}</Tag>
              ))}
              {record.tags.length > 2 && <span>+{record.tags.length - 2}</span>}
            </Space>
          </div>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => {
        const config = typeConfig[type as keyof typeof typeConfig];
        return <Tag color={config?.color}>{config?.label}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => {
        const config = statusConfig[status as keyof typeof statusConfig];
        return <Tag color={config?.color}>{config?.label}</Tag>;
      }
    },
    {
      title: '作者',
      dataIndex: 'author',
      key: 'author',
      width: 120,
      render: (author: User) => (
        <div className="flex items-center">
          <Avatar size="small" icon={<UserOutlined />} className="mr-2" />
          <span>{author.realName}</span>
        </div>
      )
    },
    {
      title: '统计',
      dataIndex: 'viewCount',
      key: 'stats',
      width: 120,
      render: (viewCount: number, record: KB) => (
        <div className="text-sm">
          <div>浏览: {viewCount}</div>
          <div className="flex items-center">
            <LikeOutlined className="text-green-500 mr-1" />
            <span className="mr-2">{record.helpful}</span>
            <DislikeOutlined className="text-red-500 mr-1" />
            <span>{record.notHelpful}</span>
          </div>
        </div>
      )
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      width: 120,
      render: (time: string) => new Date(time).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right' as const,
      render: (_, record: KB) => (
        <Space size="small">
          <Tooltip title="查看">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="分享">
            <Button
              type="text"
              icon={<ShareAltOutlined />}
              onClick={() => message.success('分享链接已复制')}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                Modal.confirm({
                  title: '确认删除',
                  content: '确定要删除这篇文章吗？',
                  onOk: () => {
                    message.success('删除成功');
                    fetchArticles();
                  }
                });
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div className="p-6">
      {/* 页面标题和操作 */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">知识库</h1>
            <p className="text-gray-600 mt-1">管理技术文档和常见问题解答</p>
          </div>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
              className="bg-blue-500 hover:bg-blue-600"
            >
              新建文章
            </Button>
          </Space>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="文章总数"
              value={stats.total}
              prefix={<BookOutlined className="text-blue-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已发布"
              value={stats.published}
              prefix={<FileTextOutlined className="text-green-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总浏览量"
              value={stats.totalViews}
              prefix={<EyeOutlined className="text-orange-500" />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均好评"
              value={stats.avgHelpful}
              prefix={<LikeOutlined className="text-red-500" />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        {/* 左侧分类树 */}
        <Col span={6}>
          <Card title="文章分类" size="small">
            <div className="mb-4">
              <Search
                placeholder="搜索文章..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: '100%' }}
              />
            </div>
            <Tree
              treeData={categoryTree}
              defaultExpandAll
              onSelect={(keys) => {
                setSelectedCategory(keys[0] as string || '');
              }}
            />
          </Card>
        </Col>

        {/* 右侧文章列表 */}
        <Col span={18}>
          <Card>
            <Table
              columns={columns}
              dataSource={filteredArticles}
              rowKey="id"
              loading={loading}
              scroll={{ x: 1000 }}
              pagination={{
                current: queryParams.page,
                pageSize: queryParams.pageSize,
                total: filteredArticles.length,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 文章详情模态框 */}
      <Modal
        title="文章详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={800}
      >
        {currentArticle && (
          <div>
            <Title level={3}>{currentArticle.title}</Title>
            <div className="mb-4">
              <Space>
                <Tag color={typeConfig[currentArticle.type]?.color}>
                  {typeConfig[currentArticle.type]?.label}
                </Tag>
                <Tag color={statusConfig[currentArticle.status]?.color}>
                  {statusConfig[currentArticle.status]?.label}
                </Tag>
                <Text type="secondary">
                  作者: {currentArticle.author.realName}
                </Text>
                <Text type="secondary">
                  更新: {new Date(currentArticle.updatedAt).toLocaleDateString()}
                </Text>
              </Space>
            </div>
            <Divider />
            <Paragraph>{currentArticle.content}</Paragraph>
            <Divider />
            <div className="flex justify-between items-center">
              <Space>
                <Text>标签:</Text>
                {currentArticle.tags.map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </Space>
              <Space>
                <Text>浏览: {currentArticle.viewCount}</Text>
                <Button icon={<LikeOutlined />} size="small">
                  {currentArticle.helpful}
                </Button>
                <Button icon={<DislikeOutlined />} size="small">
                  {currentArticle.notHelpful}
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default KnowledgeBase;
