import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Popconfirm,
  message,
  Typography,
  Row,
  Col,
  Progress,
  Tooltip,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ProjectOutlined,
  EyeOutlined,
  LinkOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { Project, QueryParams } from '../../types';
import ExportButton from '../../components/Export/ExportButton';
import { ExportApi } from '../../services/export';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

const ProjectList: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10,
    search: '',
    status: '',
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  // 模拟数据
  const mockProjects: Project[] = [
    {
      id: 1,
      customerId: 1,
      customer: {
        id: 1,
        name: '张三',
        company: '北京科技有限公司',
        status: 'confirmed',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      },
      projectName: '企业官网建设',
      projectType: 'WordPress',
      serviceFee: 15000,
      projectManagerId: 1,
      salesPersonId: 1,
      contractNumber: 'HT2024001',
      contractSignedDate: '2024-01-05',
      onlineStatus: 'online',
      onlineDate: '2024-01-20',
      plannedOnlineDate: '2024-01-18',
      previewLink: 'https://preview.example.com',
      notes: '企业官网项目，包含响应式设计',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-20T10:30:00Z',
    },
    {
      id: 2,
      customerId: 2,
      customer: {
        id: 2,
        name: '李四',
        company: '上海贸易公司',
        status: 'confirmed',
        createdAt: '2024-01-02T00:00:00Z',
        updatedAt: '2024-01-02T00:00:00Z',
      },
      projectName: '电商平台开发',
      projectType: 'WooCommerce',
      serviceFee: 35000,
      projectManagerId: 2,
      salesPersonId: 1,
      contractNumber: 'HT2024002',
      contractSignedDate: '2024-01-10',
      onlineStatus: 'development',
      plannedOnlineDate: '2024-02-15',
      previewLink: 'https://dev.example.com',
      notes: '电商平台，包含支付系统集成',
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-15T14:20:00Z',
    },
    {
      id: 3,
      customerId: 3,
      customer: {
        id: 3,
        name: '王五',
        company: '深圳制造企业',
        status: 'confirmed',
        createdAt: '2024-01-03T00:00:00Z',
        updatedAt: '2024-01-03T00:00:00Z',
      },
      projectName: '产品展示网站',
      projectType: 'Custom',
      serviceFee: 8000,
      projectManagerId: 1,
      salesPersonId: 2,
      contractNumber: 'HT2024003',
      contractSignedDate: '2024-01-12',
      onlineStatus: 'testing',
      plannedOnlineDate: '2024-01-25',
      previewLink: 'https://test.example.com',
      notes: '产品展示网站，重点突出产品特色',
      createdAt: '2024-01-03T00:00:00Z',
      updatedAt: '2024-01-18T09:15:00Z',
    },
  ];

  // 获取项目列表
  const fetchProjects = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      setTimeout(() => {
        setProjects(mockProjects);
        setTotal(mockProjects.length);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('获取项目列表失败');
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, [queryParams]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setQueryParams(prev => ({
      ...prev,
      search: value,
      page: 1,
    }));
  };

  // 处理筛选
  const handleFilter = (key: string, value: string) => {
    setQueryParams(prev => ({
      ...prev,
      [key]: value,
      page: 1,
    }));
  };

  // 处理分页
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setQueryParams(prev => ({
      ...prev,
      page: pagination.current,
      pageSize: pagination.pageSize,
      sortBy: sorter.field || 'created_at',
      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc',
    }));
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    try {
      message.success('项目删除成功');
      fetchProjects();
    } catch (error) {
      message.error('删除项目失败');
    }
  };

  // 获取状态标签和进度
  const getStatusInfo = (status: string, onlineDate?: string, plannedDate?: string) => {
    const statusMap = {
      planning: { color: 'default', text: '规划中', percent: 10 },
      development: { color: 'processing', text: '开发中', percent: 50 },
      testing: { color: 'warning', text: '测试中', percent: 80 },
      online: { color: 'success', text: '已上线', percent: 100 },
      suspended: { color: 'error', text: '已暂停', percent: 0 },
    };
    
    const config = statusMap[status as keyof typeof statusMap] || { 
      color: 'default', 
      text: status, 
      percent: 0 
    };
    
    // 检查是否延期
    const isDelayed = plannedDate && !onlineDate && new Date(plannedDate) < new Date();
    
    return {
      tag: <Tag color={isDelayed ? 'red' : config.color}>{config.text}</Tag>,
      progress: (
        <Progress 
          percent={config.percent} 
          size="small" 
          status={isDelayed ? 'exception' : 'active'}
          showInfo={false}
        />
      )
    };
  };

  // 表格列配置
  const columns: ColumnsType<Project> = [
    {
      title: '项目信息',
      dataIndex: 'projectName',
      key: 'projectName',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>{record.projectName}</div>
          <div style={{ fontSize: 12, color: '#999' }}>
            {record.customer?.company} | {record.projectType}
          </div>
        </div>
      ),
    },
    {
      title: '合同信息',
      dataIndex: 'contract',
      key: 'contract',
      render: (_, record) => (
        <div>
          <div>{record.contractNumber}</div>
          <div style={{ fontSize: 12, color: '#999' }}>
            ¥{record.serviceFee?.toLocaleString()}
          </div>
        </div>
      ),
    },
    {
      title: '项目状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      render: (status, record) => {
        const statusInfo = getStatusInfo(status, record.onlineDate, record.plannedOnlineDate);
        return (
          <div>
            {statusInfo.tag}
            <div style={{ marginTop: 4 }}>
              {statusInfo.progress}
            </div>
          </div>
        );
      },
    },
    {
      title: '时间安排',
      dataIndex: 'timeline',
      key: 'timeline',
      render: (_, record) => (
        <div style={{ fontSize: 12 }}>
          <div>计划: {record.plannedOnlineDate || '-'}</div>
          <div>实际: {record.onlineDate || '-'}</div>
        </div>
      ),
    },
    {
      title: '链接',
      dataIndex: 'links',
      key: 'links',
      render: (_, record) => (
        <Space>
          {record.previewLink && (
            <Tooltip title="预览链接">
              <Button
                type="link"
                size="small"
                icon={<LinkOutlined />}
                onClick={() => window.open(record.previewLink, '_blank')}
              />
            </Tooltip>
          )}
          {record.infoCollectionForm && (
            <Tooltip title="信息采集表">
              <Button
                type="link"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => window.open(record.infoCollectionForm, '_blank')}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => {/* 编辑项目 */}}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个项目吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Title level={2}>建站管理</Title>
      
      <Card>
        {/* 工具栏 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索项目名称、客户或合同号"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              placeholder="项目状态"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilter('onlineStatus', value || '')}
            >
              <Option value="planning">规划中</Option>
              <Option value="development">开发中</Option>
              <Option value="testing">测试中</Option>
              <Option value="online">已上线</Option>
              <Option value="suspended">已暂停</Option>
            </Select>
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              placeholder="项目类型"
              allowClear
              style={{ width: '100%' }}
              onChange={(value) => handleFilter('projectType', value || '')}
            >
              <Option value="WordPress">WordPress</Option>
              <Option value="WooCommerce">WooCommerce</Option>
              <Option value="Shopify">Shopify</Option>
              <Option value="Custom">自定义开发</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {/* 新建项目 */}}
              >
                新建项目
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchProjects}
              >
                刷新
              </Button>
              <ExportButton
                exportType="projects"
                queryParams={queryParams}
                onExport={ExportApi.exportProjects}
                totalCount={total}
              />
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={projects}
          rowKey="id"
          loading={loading}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          onChange={handleTableChange}
        />
      </Card>
    </div>
  );
};

export default ProjectList;
