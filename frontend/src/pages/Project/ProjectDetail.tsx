import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Typography,
  Divider,
  Row,
  Col,
  Progress,
  Timeline,
  Statistic,
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  ProjectOutlined,
  LinkOutlined,
  EyeOutlined,
  FileTextOutlined,
  CalendarOutlined,
} from '@ant-design/icons';

const { Title, Text } = Typography;

const ProjectDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // 模拟项目数据
  const project = {
    id: 1,
    customerId: 1,
    customer: {
      id: 1,
      name: '张三',
      company: '北京科技有限公司',
      phone: '13800138000',
      email: '<EMAIL>',
    },
    projectName: '企业官网建设',
    projectType: 'WordPress',
    serviceFee: 15000,
    projectManagerId: 1,
    projectManager: {
      id: 1,
      realName: '项目经理A',
      username: 'pm1',
    },
    salesPersonId: 1,
    salesPerson: {
      id: 1,
      realName: '销售A',
      username: 'sales1',
    },
    contractNumber: 'HT2024001',
    contractSignedDate: '2024-01-05',
    onlineStatus: 'online',
    onlineDate: '2024-01-20',
    plannedOnlineDate: '2024-01-18',
    infoCollectionForm: 'https://form.example.com',
    previewLink: 'https://preview.example.com',
    progressSheet: 'https://progress.example.com',
    notes: '企业官网项目，包含响应式设计，SEO优化，多语言支持',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T10:30:00Z',
  };

  // 模拟项目时间线
  const timeline = [
    {
      date: '2024-01-01',
      title: '项目创建',
      description: '项目立项，分配项目经理',
      status: 'completed',
    },
    {
      date: '2024-01-05',
      title: '合同签订',
      description: '客户签订合同，项目正式启动',
      status: 'completed',
    },
    {
      date: '2024-01-08',
      title: '需求调研',
      description: '完成客户需求调研和分析',
      status: 'completed',
    },
    {
      date: '2024-01-12',
      title: '设计阶段',
      description: '完成UI/UX设计稿',
      status: 'completed',
    },
    {
      date: '2024-01-15',
      title: '开发阶段',
      description: '前端和后端开发',
      status: 'completed',
    },
    {
      date: '2024-01-18',
      title: '测试阶段',
      description: '功能测试和性能优化',
      status: 'completed',
    },
    {
      date: '2024-01-20',
      title: '项目上线',
      description: '项目正式上线运行',
      status: 'completed',
    },
  ];

  const getStatusTag = (status: string) => {
    const statusMap = {
      planning: { color: 'default', text: '规划中' },
      development: { color: 'processing', text: '开发中' },
      testing: { color: 'warning', text: '测试中' },
      online: { color: 'success', text: '已上线' },
      suspended: { color: 'error', text: '已暂停' },
    };
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getProgressPercent = (status: string) => {
    const progressMap = {
      planning: 10,
      development: 50,
      testing: 80,
      online: 100,
      suspended: 0,
    };
    return progressMap[status as keyof typeof progressMap] || 0;
  };

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/projects')}
        >
          返回
        </Button>
        <Title level={2} style={{ margin: 0 }}>
          项目详情
        </Title>
      </Space>

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 24 }}>
              <ProjectOutlined style={{ fontSize: 48, color: '#1890ff', marginRight: 16 }} />
              <div style={{ flex: 1 }}>
                <Title level={3} style={{ margin: 0 }}>
                  {project.projectName}
                </Title>
                <Text type="secondary">{project.customer.company}</Text>
                <div style={{ marginTop: 8 }}>
                  {getStatusTag(project.onlineStatus)}
                  <Tag>{project.projectType}</Tag>
                </div>
              </div>
              <div>
                <Button
                  type="primary"
                  icon={<EditOutlined />}
                  onClick={() => navigate(`/projects/${id}/edit`)}
                >
                  编辑项目
                </Button>
              </div>
            </div>

            <div style={{ marginBottom: 24 }}>
              <Text strong>项目进度</Text>
              <Progress 
                percent={getProgressPercent(project.onlineStatus)} 
                status="active"
                style={{ marginTop: 8 }}
              />
            </div>

            <Divider />

            <Descriptions title="项目信息" column={2}>
              <Descriptions.Item label="项目名称">{project.projectName}</Descriptions.Item>
              <Descriptions.Item label="项目类型">{project.projectType}</Descriptions.Item>
              <Descriptions.Item label="合同编号">{project.contractNumber}</Descriptions.Item>
              <Descriptions.Item label="服务费用">¥{project.serviceFee.toLocaleString()}</Descriptions.Item>
              <Descriptions.Item label="签订时间">{project.contractSignedDate}</Descriptions.Item>
              <Descriptions.Item label="计划上线">{project.plannedOnlineDate}</Descriptions.Item>
              <Descriptions.Item label="实际上线">{project.onlineDate || '未上线'}</Descriptions.Item>
              <Descriptions.Item label="项目状态">{getStatusTag(project.onlineStatus)}</Descriptions.Item>
            </Descriptions>

            <Divider />

            <Descriptions title="团队信息" column={2}>
              <Descriptions.Item label="项目经理">
                {project.projectManager?.realName}
              </Descriptions.Item>
              <Descriptions.Item label="销售负责人">
                {project.salesPerson?.realName}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Descriptions title="客户信息" column={2}>
              <Descriptions.Item label="客户姓名">{project.customer.name}</Descriptions.Item>
              <Descriptions.Item label="公司名称">{project.customer.company}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{project.customer.phone}</Descriptions.Item>
              <Descriptions.Item label="邮箱地址">{project.customer.email}</Descriptions.Item>
            </Descriptions>

            <Divider />

            <div>
              <Title level={5}>项目链接</Title>
              <Space wrap>
                {project.previewLink && (
                  <Button
                    icon={<LinkOutlined />}
                    onClick={() => window.open(project.previewLink, '_blank')}
                  >
                    预览链接
                  </Button>
                )}
                {project.infoCollectionForm && (
                  <Button
                    icon={<FileTextOutlined />}
                    onClick={() => window.open(project.infoCollectionForm, '_blank')}
                  >
                    信息采集表
                  </Button>
                )}
                {project.progressSheet && (
                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => window.open(project.progressSheet, '_blank')}
                  >
                    进度表
                  </Button>
                )}
              </Space>
            </div>

            {project.notes && (
              <>
                <Divider />
                <div>
                  <Title level={5}>项目备注</Title>
                  <Text>{project.notes}</Text>
                </div>
              </>
            )}
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="项目统计" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="项目天数"
                  value={Math.ceil((new Date().getTime() - new Date(project.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
                  suffix="天"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="完成度"
                  value={getProgressPercent(project.onlineStatus)}
                  suffix="%"
                />
              </Col>
            </Row>
          </Card>

          <Card title="项目时间线">
            <Timeline
              items={timeline.map(item => ({
                dot: <CalendarOutlined />,
                children: (
                  <div>
                    <div style={{ fontWeight: 500 }}>{item.title}</div>
                    <div style={{ fontSize: 12, color: '#999', marginBottom: 4 }}>
                      {item.date}
                    </div>
                    <div style={{ fontSize: 12 }}>{item.description}</div>
                  </div>
                ),
                color: item.status === 'completed' ? 'green' : 'blue',
              }))}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ProjectDetail;
