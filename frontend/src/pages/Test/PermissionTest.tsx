import React from 'react';
import { Card, Row, Col, Tag, Typography, Space, Alert } from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  UserOutlined,
  SafetyCertificateOutlined 
} from '@ant-design/icons';
import { usePermissions, PermissionWrapper } from '../../hooks/usePermissions';

const { Title, Text } = Typography;

const PermissionTest: React.FC = () => {
  const { 
    user, 
    hasPermission, 
    canAccessPage, 
    getUserPermissions, 
    getRoleInfo,
    isAdmin,
    isSuperAdmin 
  } = usePermissions();

  const testPermissions = [
    'dashboard',
    'users', 
    'websites',
    'websites.enhanced',
    'servers',
    'monitoring', 
    'builder',
    'permissions',
    'profile'
  ];

  const roleInfo = getRoleInfo();
  const userPermissions = getUserPermissions();

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>
        <SafetyCertificateOutlined /> 权限测试页面
      </Title>

      <Alert
        message="权限测试说明"
        description="此页面用于测试当前用户的权限控制是否正常工作。不同角色的用户应该看到不同的权限结果。"
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <Row gutter={[16, 16]}>
        {/* 用户信息 */}
        <Col span={24}>
          <Card title="当前用户信息">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <UserOutlined /> <strong>用户名:</strong> {user?.username || '未登录'}
              </div>
              <div>
                <strong>角色:</strong> 
                <Tag color={user?.role === 'super_admin' ? 'red' : user?.role === 'admin' ? 'orange' : 'blue'}>
                  {roleInfo?.label || user?.role}
                </Tag>
              </div>
              <div>
                <strong>角色描述:</strong> {roleInfo?.description || '无描述'}
              </div>
              <div>
                <strong>管理员权限:</strong> 
                {isAdmin() ? (
                  <Tag color="green"><CheckCircleOutlined /> 是</Tag>
                ) : (
                  <Tag color="red"><CloseCircleOutlined /> 否</Tag>
                )}
              </div>
              <div>
                <strong>超级管理员:</strong> 
                {isSuperAdmin() ? (
                  <Tag color="red"><CheckCircleOutlined /> 是</Tag>
                ) : (
                  <Tag color="default"><CloseCircleOutlined /> 否</Tag>
                )}
              </div>
            </Space>
          </Card>
        </Col>

        {/* 页面访问权限测试 */}
        <Col span={12}>
          <Card title="页面访问权限测试">
            <Space direction="vertical" style={{ width: '100%' }}>
              {testPermissions.map(permission => {
                const hasAccess = canAccessPage(permission);
                return (
                  <div key={permission} style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                    borderBottom: '1px solid #f0f0f0'
                  }}>
                    <Text>{permission}</Text>
                    {hasAccess ? (
                      <Tag color="green">
                        <CheckCircleOutlined /> 允许访问
                      </Tag>
                    ) : (
                      <Tag color="red">
                        <CloseCircleOutlined /> 禁止访问
                      </Tag>
                    )}
                  </div>
                );
              })}
            </Space>
          </Card>
        </Col>

        {/* 权限包装器测试 */}
        <Col span={12}>
          <Card title="权限包装器测试">
            <Space direction="vertical" style={{ width: '100%' }}>
              
              <PermissionWrapper permission="users">
                <Alert 
                  message="用户管理权限" 
                  description="只有拥有用户管理权限的用户才能看到这条消息"
                  type="success"
                  showIcon
                />
              </PermissionWrapper>

              <PermissionWrapper permission="websites.enhanced">
                <Alert 
                  message="增强网站管理权限" 
                  description="只有拥有增强网站管理权限的用户才能看到这条消息"
                  type="warning"
                  showIcon
                />
              </PermissionWrapper>

              <PermissionWrapper permission="servers">
                <Alert 
                  message="服务器管理权限" 
                  description="只有拥有服务器管理权限的用户才能看到这条消息"
                  type="error"
                  showIcon
                />
              </PermissionWrapper>

              <PermissionWrapper permission="permissions">
                <Alert 
                  message="权限管理权限" 
                  description="只有超级管理员才能看到这条消息"
                  type="info"
                  showIcon
                />
              </PermissionWrapper>

              <PermissionWrapper 
                permissions={['monitoring', 'builder']}
                fallback={
                  <Alert 
                    message="权限不足" 
                    description="您没有监控中心或建站管理权限"
                    type="warning"
                    showIcon
                  />
                }
              >
                <Alert 
                  message="高级功能权限" 
                  description="您拥有监控中心或建站管理权限"
                  type="success"
                  showIcon
                />
              </PermissionWrapper>

            </Space>
          </Card>
        </Col>

        {/* 用户权限列表 */}
        <Col span={24}>
          <Card title="用户权限列表">
            <div style={{ marginBottom: '16px' }}>
              <Text strong>权限总数: </Text>
              <Tag color="blue">{userPermissions.length} 项权限</Tag>
            </div>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
              {userPermissions.map(permission => (
                <Tag key={permission} color="green">
                  {permission}
                </Tag>
              ))}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default PermissionTest;
