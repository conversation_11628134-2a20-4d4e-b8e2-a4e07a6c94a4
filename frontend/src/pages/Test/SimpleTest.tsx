import React, { useState } from 'react';
import { Card, Typography, Space, Button, message } from 'antd';
import { CheckCircleOutlined } from '@ant-design/icons';
import { WebsiteApi } from '../../services/website';

const { Title, Text, Paragraph } = Typography;

const SimpleTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testDirectApi = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('开始直接测试API...');

      const response = await fetch('http://localhost:3001/api/v1/websites');
      const data = await response.json();

      console.log('直接API响应:', data);
      setResult(data);
      message.success('直接API调用成功');

    } catch (err: any) {
      console.error('直接API调用失败:', err);
      setError(err.message);
      message.error('直接API调用失败');
    } finally {
      setLoading(false);
    }
  };

  const testWebsiteApi = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('开始测试WebsiteApi...');

      const response = await WebsiteApi.getWebsites();
      console.log('WebsiteApi响应:', response);
      setResult(response);
      message.success('WebsiteApi调用成功');

    } catch (err: any) {
      console.error('WebsiteApi调用失败:', err);
      setError(err.message);
      message.error('WebsiteApi调用失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: 24 }}>
      <Card>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div style={{ textAlign: 'center' }}>
            <CheckCircleOutlined
              style={{
                fontSize: 64,
                color: '#52c41a',
                marginBottom: 16
              }}
            />
            <Title level={2}>API连接测试</Title>
            <Text type="secondary">
              测试前后端API连接是否正常
            </Text>
          </div>

          <div>
            <Title level={4}>测试选项</Title>
            <Space>
              <Button
                type="primary"
                onClick={testDirectApi}
                loading={loading}
              >
                直接调用API
              </Button>
              <Button
                onClick={testWebsiteApi}
                loading={loading}
              >
                通过WebsiteApi调用
              </Button>
            </Space>
          </div>

          {error && (
            <Card title="错误信息" type="inner">
              <Text type="danger">{error}</Text>
            </Card>
          )}

          {result && (
            <Card title="API响应结果" type="inner">
              <Paragraph>
                <Text strong>成功:</Text> {result.success ? '是' : '否'}
              </Paragraph>
              <Paragraph>
                <Text strong>消息:</Text> {result.message}
              </Paragraph>
              <Paragraph>
                <Text strong>数据条数:</Text> {result.data?.websites?.length || 0}
              </Paragraph>
              <Paragraph>
                <Text strong>完整响应:</Text>
              </Paragraph>
              <pre style={{
                background: '#f5f5f5',
                padding: 16,
                borderRadius: 4,
                overflow: 'auto',
                maxHeight: 400
              }}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </Card>
          )}

          <div style={{ textAlign: 'center' }}>
            <Space>
              <Button onClick={() => window.location.href = '/websites'}>
                访问网站管理
              </Button>
              <Button onClick={() => window.location.href = '/dashboard'}>
                访问仪表盘
              </Button>
              <Button onClick={() => window.location.href = '/servers'}>
                访问服务器管理
              </Button>
            </Space>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default SimpleTest;
