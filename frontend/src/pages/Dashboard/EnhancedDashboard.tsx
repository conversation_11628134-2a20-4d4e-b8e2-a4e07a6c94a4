import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Alert,
  Timeline,
  List,
  Avatar,
  Badge,
  Tabs,
  Button,
  Space,
  Tooltip,
  Select,
  DatePicker
} from 'antd';
import {
  GlobalOutlined,
  CloudServerOutlined,
  UserOutlined,
  DollarOutlined,
  TrophyOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ThunderboltOutlined,
  SafetyCertificateOutlined,
  FileTextOutlined,
  CalendarOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ReloadOutlined,
  SettingOutlined,
  BellOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

interface EnhancedDashboardProps {}

const EnhancedDashboard: React.FC<EnhancedDashboardProps> = () => {
  const [loading, setLoading] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');
  const [dashboardData, setDashboardData] = useState<any>({});

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockData = {
        overview: {
          totalWebsites: 45,
          activeWebsites: 42,
          totalServers: 12,
          totalDomains: 38,
          totalProjects: 28,
          onlineProjects: 25,
          totalCustomers: 35,
          monthlyRevenue: 125000,
          growthRate: 12.5
        },
        alerts: [
          { id: 1, type: 'warning', message: 'SSL证书即将过期', count: 3, priority: 'high' },
          { id: 2, type: 'error', message: '网站访问异常', count: 1, priority: 'urgent' },
          { id: 3, type: 'info', message: '服务器负载较高', count: 2, priority: 'medium' },
          { id: 4, type: 'warning', message: '域名即将到期', count: 5, priority: 'high' }
        ],
        recentActivities: [
          { id: 1, type: 'website', action: '新建网站', target: 'company-xyz.com', user: '张三', time: '2分钟前' },
          { id: 2, type: 'project', action: '项目上线', target: '电商平台项目', user: '李四', time: '15分钟前' },
          { id: 3, type: 'server', action: '服务器重启', target: 'Server-01', user: '王五', time: '1小时前' },
          { id: 4, type: 'domain', action: '域名续费', target: 'example.com', user: '赵六', time: '2小时前' }
        ],
        performance: {
          avgResponseTime: 245,
          uptime: 99.8,
          totalRequests: 1250000,
          errorRate: 0.02
        },
        topWebsites: [
          { id: 1, name: 'company-abc.com', visits: 15420, uptime: 99.9, responseTime: 180 },
          { id: 2, name: 'shop-fashion.com', visits: 12350, uptime: 99.7, responseTime: 220 },
          { id: 3, name: 'tech-blog.com', visits: 8960, uptime: 99.8, responseTime: 195 },
          { id: 4, name: 'crm-system.com', visits: 6780, uptime: 99.6, responseTime: 280 },
          { id: 5, name: 'electronics-store.com', visits: 5420, uptime: 98.9, responseTime: 350 }
        ],
        expiringItems: [
          { type: 'ssl', name: 'shop-fashion.com', daysLeft: 7, priority: 'urgent' },
          { type: 'domain', name: 'company-xyz.com', daysLeft: 15, priority: 'high' },
          { type: 'server', name: 'Server-03', daysLeft: 22, priority: 'medium' },
          { type: 'ssl', name: 'tech-blog.com', daysLeft: 28, priority: 'medium' }
        ],
        tickets: {
          open: 8,
          inProgress: 12,
          resolved: 45,
          avgResolutionTime: 4.2
        },
        revenue: {
          thisMonth: 125000,
          lastMonth: 118000,
          growth: 5.9,
          breakdown: [
            { category: '网站服务', amount: 65000, percentage: 52 },
            { category: '服务器租赁', amount: 35000, percentage: 28 },
            { category: '域名服务', amount: 15000, percentage: 12 },
            { category: '其他服务', amount: 10000, percentage: 8 }
          ]
        }
      };
      setDashboardData(mockData);
    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, [timeRange]);

  // 获取告警图标
  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'error': return <ExclamationCircleOutlined className="text-red-500" />;
      case 'warning': return <WarningOutlined className="text-orange-500" />;
      case 'info': return <BellOutlined className="text-blue-500" />;
      default: return <BellOutlined className="text-gray-500" />;
    }
  };

  // 获取活动图标
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'website': return <GlobalOutlined className="text-blue-500" />;
      case 'project': return <FileTextOutlined className="text-green-500" />;
      case 'server': return <CloudServerOutlined className="text-purple-500" />;
      case 'domain': return <CalendarOutlined className="text-orange-500" />;
      default: return <BellOutlined className="text-gray-500" />;
    }
  };

  // 到期项目表格列
  const expiringColumns = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          ssl: { label: 'SSL证书', color: 'blue' },
          domain: { label: '域名', color: 'green' },
          server: { label: '服务器', color: 'purple' },
          website: { label: '网站', color: 'orange' }
        };
        const config = typeMap[type as keyof typeof typeMap];
        return <Tag color={config?.color}>{config?.label}</Tag>;
      }
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '剩余天数',
      dataIndex: 'daysLeft',
      key: 'daysLeft',
      render: (days: number, record: any) => (
        <span className={days <= 7 ? 'text-red-500 font-bold' : days <= 30 ? 'text-orange-500' : ''}>
          {days} 天
        </span>
      )
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority: string) => {
        const priorityMap = {
          urgent: { label: '紧急', color: 'red' },
          high: { label: '高', color: 'orange' },
          medium: { label: '中', color: 'blue' },
          low: { label: '低', color: 'green' }
        };
        const config = priorityMap[priority as keyof typeof priorityMap];
        return <Tag color={config?.color}>{config?.label}</Tag>;
      }
    }
  ];

  return (
    <div className="p-6">
      {/* 页面标题和控制 */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">仪表盘</h1>
            <p className="text-gray-600 mt-1">系统运行状态和关键指标概览</p>
          </div>
          <Space>
            <Select
              value={timeRange}
              onChange={setTimeRange}
              style={{ width: 120 }}
            >
              <Select.Option value="1d">今天</Select.Option>
              <Select.Option value="7d">最近7天</Select.Option>
              <Select.Option value="30d">最近30天</Select.Option>
              <Select.Option value="90d">最近90天</Select.Option>
            </Select>
            <Button icon={<ReloadOutlined />} onClick={fetchDashboardData} loading={loading}>
              刷新
            </Button>
            <Button icon={<SettingOutlined />}>
              设置
            </Button>
          </Space>
        </div>
      </div>

      {/* 告警信息 */}
      {dashboardData.alerts && dashboardData.alerts.length > 0 && (
        <Alert
          message="系统告警"
          description={
            <div className="space-y-2">
              {dashboardData.alerts.map((alert: any) => (
                <div key={alert.id} className="flex items-center justify-between">
                  <div className="flex items-center">
                    {getAlertIcon(alert.type)}
                    <span className="ml-2">{alert.message}</span>
                    <Badge count={alert.count} className="ml-2" />
                  </div>
                  <Tag color={alert.priority === 'urgent' ? 'red' : alert.priority === 'high' ? 'orange' : 'blue'}>
                    {alert.priority === 'urgent' ? '紧急' : alert.priority === 'high' ? '重要' : '一般'}
                  </Tag>
                </div>
              ))}
            </div>
          }
          type="warning"
          showIcon
          className="mb-6"
        />
      )}

      {/* 核心指标卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="网站总数"
              value={dashboardData.overview?.totalWebsites || 0}
              prefix={<GlobalOutlined className="text-blue-500" />}
              suffix={
                <div className="text-sm">
                  <span className="text-green-500">
                    <ArrowUpOutlined /> {dashboardData.overview?.growthRate || 0}%
                  </span>
                </div>
              }
            />
            <div className="mt-2">
              <Progress 
                percent={Math.round((dashboardData.overview?.activeWebsites || 0) / (dashboardData.overview?.totalWebsites || 1) * 100)} 
                size="small" 
                strokeColor="#52c41a"
                format={() => `${dashboardData.overview?.activeWebsites || 0} 正常运行`}
              />
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="服务器"
              value={dashboardData.overview?.totalServers || 0}
              prefix={<CloudServerOutlined className="text-purple-500" />}
            />
            <div className="mt-2 text-sm text-gray-500">
              平均负载: 45% | 正常运行: 100%
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="项目进展"
              value={dashboardData.overview?.onlineProjects || 0}
              suffix={`/ ${dashboardData.overview?.totalProjects || 0}`}
              prefix={<FileTextOutlined className="text-green-500" />}
            />
            <div className="mt-2">
              <Progress 
                percent={Math.round((dashboardData.overview?.onlineProjects || 0) / (dashboardData.overview?.totalProjects || 1) * 100)} 
                size="small" 
                strokeColor="#1890ff"
              />
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="月度收入"
              value={dashboardData.overview?.monthlyRevenue || 0}
              prefix={<DollarOutlined className="text-red-500" />}
              formatter={(value) => `¥${value.toLocaleString()}`}
              suffix={
                <div className="text-sm">
                  <span className="text-green-500">
                    <ArrowUpOutlined /> {dashboardData.revenue?.growth || 0}%
                  </span>
                </div>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 性能指标 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="平均响应时间"
              value={dashboardData.performance?.avgResponseTime || 0}
              suffix="ms"
              prefix={<ThunderboltOutlined className="text-orange-500" />}
              valueStyle={{ fontSize: 16 }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="系统可用性"
              value={dashboardData.performance?.uptime || 0}
              suffix="%"
              prefix={<CheckCircleOutlined className="text-green-500" />}
              valueStyle={{ fontSize: 16 }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="工单处理"
              value={dashboardData.tickets?.avgResolutionTime || 0}
              suffix="小时"
              prefix={<ClockCircleOutlined className="text-blue-500" />}
              valueStyle={{ fontSize: 16 }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card size="small">
            <Statistic
              title="错误率"
              value={dashboardData.performance?.errorRate || 0}
              suffix="%"
              prefix={<ShieldOutlined className="text-red-500" />}
              valueStyle={{ fontSize: 16 }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细信息标签页 */}
      <Tabs defaultActiveKey="websites" className="mb-6">
        <TabPane tab="网站状态" key="websites">
          <Card title="网站性能排行" size="small">
            <Table
              dataSource={dashboardData.topWebsites || []}
              rowKey="id"
              size="small"
              pagination={false}
              columns={[
                { title: '网站', dataIndex: 'name', key: 'name' },
                { 
                  title: '访问量', 
                  dataIndex: 'visits', 
                  key: 'visits',
                  render: (visits: number) => visits.toLocaleString()
                },
                { 
                  title: '可用性', 
                  dataIndex: 'uptime', 
                  key: 'uptime',
                  render: (uptime: number) => (
                    <span className={uptime >= 99.5 ? 'text-green-500' : uptime >= 99 ? 'text-orange-500' : 'text-red-500'}>
                      {uptime}%
                    </span>
                  )
                },
                { 
                  title: '响应时间', 
                  dataIndex: 'responseTime', 
                  key: 'responseTime',
                  render: (time: number) => `${time}ms`
                }
              ]}
            />
          </Card>
        </TabPane>
        
        <TabPane tab="到期提醒" key="expiring">
          <Card title="即将到期项目" size="small">
            <Table
              dataSource={dashboardData.expiringItems || []}
              rowKey="name"
              size="small"
              pagination={false}
              columns={expiringColumns}
            />
          </Card>
        </TabPane>
        
        <TabPane tab="最近活动" key="activities">
          <Card title="系统活动" size="small">
            <List
              dataSource={dashboardData.recentActivities || []}
              renderItem={(item: any) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={getActivityIcon(item.type)} />}
                    title={`${item.action} - ${item.target}`}
                    description={`${item.user} • ${item.time}`}
                  />
                </List.Item>
              )}
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default EnhancedDashboard;
