import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Button,
  Space,
  App,
  Spin,
} from 'antd';
import {
  ReloadOutlined,
  FullscreenOutlined,
  GlobalOutlined,
  DatabaseOutlined,
  ProjectOutlined,
  UserOutlined,
} from '@ant-design/icons';

const { Title } = Typography;

interface SimpleDashboardStats {
  totalWebsites: number;
  activeWebsites: number;
  expiringSoon: number;
  totalServers: number;
  totalDomains: number;
  totalProjects: number;
  onlineProjects: number;
}

const Dashboard: React.FC = () => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<SimpleDashboardStats>({
    totalWebsites: 0,
    activeWebsites: 0,
    expiringSoon: 0,
    totalServers: 0,
    totalDomains: 0,
    totalProjects: 0,
    onlineProjects: 0,
  });

  // 模拟数据加载
  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 设置模拟数据
      setStats({
        totalWebsites: 25,
        activeWebsites: 22,
        expiringSoon: 3,
        totalServers: 8,
        totalDomains: 15,
        totalProjects: 12,
        onlineProjects: 10,
      });

      message.success('仪表盘数据加载成功');
    } catch (error) {
      message.error('加载仪表盘数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const handleRefresh = () => {
    loadDashboardData();
  };

  const handleFullscreen = () => {
    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      document.documentElement.requestFullscreen();
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px'
      }}>
        <div>
          <Title level={2} style={{ margin: 0 }}>
            系统仪表盘
          </Title>
          <div style={{ color: '#8c8c8c', marginTop: 4 }}>
            欢迎使用WordPress站点管理系统
          </div>
        </div>
        <Space>
          <Button
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            刷新数据
          </Button>
          <Button
            icon={<FullscreenOutlined />}
            onClick={handleFullscreen}
          >
            全屏显示
          </Button>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Spin spinning={loading}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="网站总数"
                value={stats.totalWebsites}
                prefix={<GlobalOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="活跃网站"
                value={stats.activeWebsites}
                prefix={<GlobalOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="即将到期"
                value={stats.expiringSoon}
                prefix={<GlobalOutlined />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="服务器数量"
                value={stats.totalServers}
                prefix={<DatabaseOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="域名总数"
                value={stats.totalDomains}
                prefix={<GlobalOutlined />}
                valueStyle={{ color: '#13c2c2' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="项目总数"
                value={stats.totalProjects}
                prefix={<ProjectOutlined />}
                valueStyle={{ color: '#eb2f96' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="在线项目"
                value={stats.onlineProjects}
                prefix={<ProjectOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="系统状态"
                value="正常"
                prefix={<UserOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 快速操作 */}
        <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
          <Col span={24}>
            <Card title="快速操作" extra={<Button type="link">更多</Button>}>
              <Space wrap>
                <Button type="primary">
                  添加网站
                </Button>
                <Button>
                  服务器管理
                </Button>
                <Button>
                  域名管理
                </Button>
                <Button>
                  系统设置
                </Button>
                <Button>
                  用户管理
                </Button>
                <Button>
                  数据备份
                </Button>
              </Space>
            </Card>
          </Col>
        </Row>

        {/* 系统信息 */}
        <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
          <Col xs={24} lg={12}>
            <Card title="系统信息">
              <div style={{ lineHeight: '2' }}>
                <div><strong>系统版本:</strong> v2.0.0</div>
                <div><strong>运行时间:</strong> 15天 8小时 32分钟</div>
                <div><strong>最后更新:</strong> 2024-06-30 23:20:00</div>
                <div><strong>数据库状态:</strong> <span style={{ color: '#52c41a' }}>正常</span></div>
              </div>
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="最近活动">
              <div style={{ lineHeight: '2' }}>
                <div>• 用户 admin 登录系统</div>
                <div>• 添加了新网站 example.com</div>
                <div>• 更新了服务器配置</div>
                <div>• 执行了数据备份</div>
                <div>• 系统自动检查完成</div>
              </div>
            </Card>
          </Col>
        </Row>
      </Spin>
    </div>
  );
};

export default Dashboard;
