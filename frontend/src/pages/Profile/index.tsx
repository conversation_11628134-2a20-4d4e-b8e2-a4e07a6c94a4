import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Button,
  Space,
  message,
  Avatar,
  Upload,
  Row,
  Col,
  Divider,
  Select,
  Switch,
  Table,
  Tag,
  Timeline,
  Alert,
  Modal,
  Progress
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  LockOutlined,
  SettingOutlined,
  HistoryOutlined,
  CameraOutlined,
  SaveOutlined,
  EyeOutlined,
  SecurityScanOutlined,
  BellOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { profileService, UserProfile } from '../../services/profile';

// const { TabPane } = Tabs; // 已废弃，使用 items 属性
const { Option } = Select;
const { TextArea } = Input;

interface UserProfile {
  id: number;
  username: string;
  email: string;
  realName: string;
  phone: string;
  avatar: string;
  role: string;
  department: string;
  position: string;
  bio: string;
  preferences: {
    language: string;
    timezone: string;
    theme: string;
    emailNotifications: boolean;
    smsNotifications: boolean;
    desktopNotifications: boolean;
  };
  security: {
    twoFactorEnabled: boolean;
    lastPasswordChange: string;
    loginSessions: Array<{
      id: string;
      ip: string;
      location: string;
      device: string;
      lastActive: string;
      current: boolean;
    }>;
  };
  activityLog: Array<{
    id: number;
    action: string;
    description: string;
    ip: string;
    timestamp: string;
    status: 'success' | 'warning' | 'error';
  }>;
}

const Profile: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [profile, setProfile] = useState<UserProfile>({
    id: 1,
    username: 'admin',
    email: '<EMAIL>',
    realName: '系统管理员',
    phone: '13800138000',
    avatar: '',
    role: 'admin',
    department: '技术部',
    position: '系统管理员',
    bio: '负责系统维护和管理工作',
    preferences: {
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      theme: 'light',
      emailNotifications: true,
      smsNotifications: false,
      desktopNotifications: true
    },
    security: {
      twoFactorEnabled: false,
      lastPasswordChange: '2024-05-15',
      loginSessions: [
        {
          id: '1',
          ip: '*************',
          location: '北京市',
          device: 'Chrome on Windows',
          lastActive: '2024-06-16 15:30:00',
          current: true
        },
        {
          id: '2',
          ip: '*************',
          location: '上海市',
          device: 'Safari on macOS',
          lastActive: '2024-06-15 09:20:00',
          current: false
        }
      ]
    },
    activityLog: [
      {
        id: 1,
        action: '登录系统',
        description: '用户成功登录系统',
        ip: '*************',
        timestamp: '2024-06-16 15:30:00',
        status: 'success'
      },
      {
        id: 2,
        action: '修改网站信息',
        description: '更新了阿里巴巴官网的SSL证书信息',
        ip: '*************',
        timestamp: '2024-06-16 14:25:00',
        status: 'success'
      },
      {
        id: 3,
        action: '删除用户',
        description: '删除了用户 test001',
        ip: '*************',
        timestamp: '2024-06-16 11:15:00',
        status: 'warning'
      }
    ]
  });

  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [preferencesForm] = Form.useForm();

  // 加载用户资料
  const loadProfile = async () => {
    setLoading(true);
    try {
      const userProfile = await profileService.getProfile();
      setProfile(userProfile);
      profileForm.setFieldsValue(userProfile);
      preferencesForm.setFieldsValue(userProfile.preferences);
    } catch (error) {
      message.error('加载用户资料失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存用户资料
  const saveProfile = async (values: any) => {
    setSaving(true);
    try {
      await profileService.updateProfile(values);
      setProfile({ ...profile, ...values });
      message.success('个人资料保存成功');
    } catch (error) {
      message.error('保存个人资料失败');
    } finally {
      setSaving(false);
    }
  };

  // 修改密码
  const changePassword = async (values: any) => {
    setSaving(true);
    try {
      await profileService.changePassword(values);
      message.success('密码修改成功');
      setPasswordModalVisible(false);
      passwordForm.resetFields();
    } catch (error) {
      message.error('密码修改失败');
    } finally {
      setSaving(false);
    }
  };

  // 保存偏好设置
  const savePreferences = async (values: any) => {
    setSaving(true);
    try {
      await profileService.updatePreferences(values);
      setProfile({ ...profile, preferences: values });
      message.success('偏好设置保存成功');
    } catch (error) {
      message.error('保存偏好设置失败');
    } finally {
      setSaving(false);
    }
  };

  useEffect(() => {
    loadProfile();
  }, []);

  // 头像上传
  const handleAvatarUpload = (info: any) => {
    if (info.file.status === 'done') {
      message.success('头像上传成功');
      // 更新头像URL
    } else if (info.file.status === 'error') {
      message.error('头像上传失败');
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6"
    >
      {/* 页面标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">个人资料</h1>
        <p className="text-gray-600 mt-1">管理您的个人信息和账户设置</p>
      </div>

      {/* 个人资料标签页 */}
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        className="mb-6"
        items={[
          {
            key: 'profile',
            label: (
              <span>
                <UserOutlined />
                基本信息
              </span>
            ),
            children: (
          <Row gutter={16}>
            <Col span={8}>
              <Card>
                <div className="text-center">
                  <div className="relative inline-block">
                    <Avatar
                      size={120}
                      src={profile.avatar}
                      icon={<UserOutlined />}
                      className="mb-4"
                    />
                    <Upload
                      showUploadList={false}
                      onChange={handleAvatarUpload}
                      className="absolute bottom-0 right-0"
                    >
                      <Button
                        type="primary"
                        shape="circle"
                        size="small"
                        icon={<CameraOutlined />}
                        className="bg-blue-500 hover:bg-blue-600"
                      />
                    </Upload>
                  </div>
                  <h3 className="text-lg font-medium">{profile.realName}</h3>
                  <p className="text-gray-500">{profile.position}</p>
                  <Tag color="blue">{profile.role === 'admin' ? '管理员' : '普通用户'}</Tag>
                </div>
              </Card>
            </Col>
            <Col span={16}>
              <Card title="个人信息" extra={
                <Button type="link" icon={<EditOutlined />}>
                  编辑
                </Button>
              }>
                <Form
                  form={profileForm}
                  layout="vertical"
                  onFinish={saveProfile}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="真实姓名"
                        name="realName"
                        rules={[{ required: true, message: '请输入真实姓名' }]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="用户名"
                        name="username"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input disabled />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="邮箱"
                        name="email"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱地址' }
                        ]}
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="手机号"
                        name="phone"
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        label="部门"
                        name="department"
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="职位"
                        name="position"
                      >
                        <Input />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    label="个人简介"
                    name="bio"
                  >
                    <TextArea rows={3} />
                  </Form.Item>

                  <Form.Item>
                    <Space>
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={saving}
                        icon={<SaveOutlined />}
                        className="bg-blue-500 hover:bg-blue-600"
                      >
                        保存信息
                      </Button>
                      <Button
                        onClick={() => setPasswordModalVisible(true)}
                        icon={<LockOutlined />}
                      >
                        修改密码
                      </Button>
                    </Space>
                  </Form.Item>
                </Form>
              </Card>
            </Col>
          </Row>
            )
          },
          {
            key: 'preferences',
            label: (
              <span>
                <SettingOutlined />
                偏好设置
              </span>
            ),
            children: (
          <Card>
            <Form
              form={preferencesForm}
              layout="vertical"
              onFinish={savePreferences}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="语言"
                    name="language"
                  >
                    <Select>
                      <Option value="zh-CN">简体中文</Option>
                      <Option value="en-US">English</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="时区"
                    name="timezone"
                  >
                    <Select>
                      <Option value="Asia/Shanghai">Asia/Shanghai</Option>
                      <Option value="UTC">UTC</Option>
                      <Option value="America/New_York">America/New_York</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                label="主题"
                name="theme"
              >
                <Select>
                  <Option value="light">浅色主题</Option>
                  <Option value="dark">深色主题</Option>
                  <Option value="auto">跟随系统</Option>
                </Select>
              </Form.Item>

              <Divider orientation="left">通知设置</Divider>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    label="邮件通知"
                    name="emailNotifications"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="短信通知"
                    name="smsNotifications"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="桌面通知"
                    name="desktopNotifications"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={saving}
                  icon={<SaveOutlined />}
                  className="bg-blue-500 hover:bg-blue-600"
                >
                  保存偏好
                </Button>
              </Form.Item>
            </Form>
          </Card>
            )
          },
          {
            key: 'security',
            label: (
              <span>
                <SecurityScanOutlined />
                安全设置
              </span>
            ),
            children: (
          <Row gutter={16}>
            <Col span={12}>
              <Card title="安全状态" className="mb-4">
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>双因子认证</span>
                    <div>
                      {profile.security.twoFactorEnabled ? (
                        <Tag color="green">已启用</Tag>
                      ) : (
                        <Tag color="red">未启用</Tag>
                      )}
                      <Button size="small" type="link">
                        {profile.security.twoFactorEnabled ? '关闭' : '启用'}
                      </Button>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>最后修改密码</span>
                    <span className="text-gray-500">{profile.security.lastPasswordChange}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>登录会话</span>
                    <span>{profile.security.loginSessions.length} 个活跃会话</span>
                  </div>
                </div>
              </Card>

              <Card title="密码强度">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>密码强度</span>
                    <span className="text-green-500">强</span>
                  </div>
                  <Progress percent={85} strokeColor="#52c41a" />
                  <div className="text-sm text-gray-500">
                    建议定期更换密码以确保账户安全
                  </div>
                </div>
              </Card>
            </Col>

            <Col span={12}>
              <Card title="登录会话">
                <div className="space-y-3">
                  {profile.security.loginSessions.map(session => (
                    <div key={session.id} className="border rounded p-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium flex items-center">
                            {session.device}
                            {session.current && (
                              <Tag color="green" size="small" className="ml-2">当前</Tag>
                            )}
                          </div>
                          <div className="text-sm text-gray-500">
                            {session.ip} • {session.location}
                          </div>
                          <div className="text-sm text-gray-500">
                            最后活跃: {session.lastActive}
                          </div>
                        </div>
                        {!session.current && (
                          <Button size="small" danger>
                            终止
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                <div className="mt-4">
                  <Button danger>终止所有其他会话</Button>
                </div>
              </Card>
            </Col>
          </Row>
            )
          },
          {
            key: 'activity',
            label: (
              <span>
                <HistoryOutlined />
                活动日志
              </span>
            ),
            children: (
          <Card>
            <Table
              dataSource={profile.activityLog}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              columns={[
                {
                  title: '操作',
                  dataIndex: 'action',
                  key: 'action',
                  render: (action: string, record: any) => (
                    <div>
                      <div className="font-medium">{action}</div>
                      <div className="text-sm text-gray-500">{record.description}</div>
                    </div>
                  )
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  render: (status: string) => {
                    const colors = {
                      success: 'green',
                      warning: 'orange',
                      error: 'red'
                    };
                    const labels = {
                      success: '成功',
                      warning: '警告',
                      error: '错误'
                    };
                    return (
                      <Tag color={colors[status as keyof typeof colors]}>
                        {labels[status as keyof typeof labels]}
                      </Tag>
                    );
                  }
                },
                {
                  title: 'IP地址',
                  dataIndex: 'ip',
                  key: 'ip'
                },
                {
                  title: '时间',
                  dataIndex: 'timestamp',
                  key: 'timestamp',
                  render: (time: string) => new Date(time).toLocaleString()
                }
              ]}
            />
          </Card>
            )
          }
        ]}
      />

      {/* 修改密码模态框 */}
      <Modal
        title="修改密码"
        open={passwordModalVisible}
        onCancel={() => setPasswordModalVisible(false)}
        footer={null}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={changePassword}
        >
          <Form.Item
            label="当前密码"
            name="currentPassword"
            rules={[{ required: true, message: '请输入当前密码' }]}
          >
            <Input.Password />
          </Form.Item>

          <Form.Item
            label="新密码"
            name="newPassword"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 8, message: '密码长度至少8位' }
            ]}
          >
            <Input.Password />
          </Form.Item>

          <Form.Item
            label="确认新密码"
            name="confirmPassword"
            dependencies={['newPassword']}
            rules={[
              { required: true, message: '请确认新密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('newPassword') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={saving}
                className="bg-blue-500 hover:bg-blue-600"
              >
                修改密码
              </Button>
              <Button onClick={() => setPasswordModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </motion.div>
  );
};

export default Profile;
