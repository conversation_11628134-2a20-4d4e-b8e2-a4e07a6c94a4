import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Button,
  Space,
  Alert,
  Tabs,
  Timeline,
  Badge,
  Tooltip,
  message,
  Spin
} from 'antd';
import {
  ReloadOutlined,
  MonitorOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  CloudServerOutlined,
  GlobalOutlined,
  SafetyCertificateOutlined,
  DashboardOutlined,
  EyeOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { monitorService, MonitorData } from '../../services/monitor';

const { TabPane } = Tabs;

interface MonitorData {
  systemStatus: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  websiteStatus: Array<{
    id: number;
    name: string;
    url: string;
    status: 'online' | 'offline' | 'warning';
    responseTime: number;
    uptime: number;
    lastCheck: string;
  }>;
  serverStatus: Array<{
    id: number;
    name: string;
    ip: string;
    status: 'online' | 'offline' | 'maintenance';
    cpu: number;
    memory: number;
    disk: number;
    uptime: string;
  }>;
  sslStatus: Array<{
    id: number;
    domain: string;
    issuer: string;
    expiryDate: string;
    daysLeft: number;
    status: 'valid' | 'expiring' | 'expired';
  }>;
  alerts: Array<{
    id: number;
    type: 'error' | 'warning' | 'info';
    message: string;
    time: string;
    resolved: boolean;
  }>;
}

const MonitorDashboard: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<MonitorData>({
    systemStatus: {
      cpu: 0,
      memory: 0,
      disk: 0,
      network: 0
    },
    websiteStatus: [],
    serverStatus: [],
    sslStatus: [],
    alerts: []
  });

  // 获取监控数据
  const fetchMonitorData = async () => {
    setLoading(true);
    try {
      const monitorData = await monitorService.getMonitorData();
      console.log('监控数据获取成功:', monitorData);
      setData(monitorData);
    } catch (error) {
      console.error('获取监控数据失败:', error);
      message.error('获取监控数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('监控仪表盘组件挂载，开始获取数据');
    fetchMonitorData();
    // 设置定时刷新
    const interval = setInterval(fetchMonitorData, 30000); // 30秒刷新一次
    return () => {
      console.log('监控仪表盘组件卸载，清理定时器');
      clearInterval(interval);
    };
  }, []);

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'valid':
        return 'green';
      case 'warning':
      case 'expiring':
        return 'orange';
      case 'offline':
      case 'expired':
      case 'error':
        return 'red';
      case 'maintenance':
        return 'blue';
      default:
        return 'gray';
    }
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
      case 'valid':
        return <CheckCircleOutlined />;
      case 'warning':
      case 'expiring':
        return <WarningOutlined />;
      case 'offline':
      case 'expired':
      case 'error':
        return <ExclamationCircleOutlined />;
      case 'maintenance':
        return <ClockCircleOutlined />;
      default:
        return <MonitorOutlined />;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6"
    >
      {/* 页面标题和操作 */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">监控中心</h1>
            <p className="text-gray-600 mt-1">实时监控系统状态和网站运行情况</p>
          </div>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchMonitorData}
              loading={loading}
            >
              刷新数据
            </Button>
            <Button
              type="primary"
              icon={<SettingOutlined />}
              className="bg-blue-500 hover:bg-blue-600"
            >
              监控设置
            </Button>
          </Space>
        </div>
      </div>

      {/* 告警信息 */}
      {data.alerts.filter(alert => !alert.resolved).length > 0 && (
        <Alert
          message="系统告警"
          description={`当前有 ${data.alerts.filter(alert => !alert.resolved).length} 个未处理的告警，请及时处理！`}
          type="error"
          showIcon
          className="mb-6"
          action={
            <Button size="small" danger>
              查看详情
            </Button>
          }
        />
      )}

      {/* 系统状态概览 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="CPU使用率"
              value={data.systemStatus.cpu}
              suffix="%"
              prefix={<DashboardOutlined className="text-blue-500" />}
              valueStyle={{
                color: data.systemStatus.cpu > 80 ? '#ff4d4f' : data.systemStatus.cpu > 60 ? '#faad14' : '#52c41a'
              }}
            />
            <Progress
              percent={data.systemStatus.cpu}
              size="small"
              strokeColor={data.systemStatus.cpu > 80 ? '#ff4d4f' : data.systemStatus.cpu > 60 ? '#faad14' : '#52c41a'}
              className="mt-2"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="内存使用率"
              value={data.systemStatus.memory}
              suffix="%"
              prefix={<CloudServerOutlined className="text-green-500" />}
              valueStyle={{
                color: data.systemStatus.memory > 80 ? '#ff4d4f' : data.systemStatus.memory > 60 ? '#faad14' : '#52c41a'
              }}
            />
            <Progress
              percent={data.systemStatus.memory}
              size="small"
              strokeColor={data.systemStatus.memory > 80 ? '#ff4d4f' : data.systemStatus.memory > 60 ? '#faad14' : '#52c41a'}
              className="mt-2"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="磁盘使用率"
              value={data.systemStatus.disk}
              suffix="%"
              prefix={<MonitorOutlined className="text-orange-500" />}
              valueStyle={{
                color: data.systemStatus.disk > 80 ? '#ff4d4f' : data.systemStatus.disk > 60 ? '#faad14' : '#52c41a'
              }}
            />
            <Progress
              percent={data.systemStatus.disk}
              size="small"
              strokeColor={data.systemStatus.disk > 80 ? '#ff4d4f' : data.systemStatus.disk > 60 ? '#faad14' : '#52c41a'}
              className="mt-2"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="网络使用率"
              value={data.systemStatus.network}
              suffix="%"
              prefix={<GlobalOutlined className="text-purple-500" />}
              valueStyle={{
                color: data.systemStatus.network > 80 ? '#ff4d4f' : data.systemStatus.network > 60 ? '#faad14' : '#52c41a'
              }}
            />
            <Progress
              percent={data.systemStatus.network}
              size="small"
              strokeColor={data.systemStatus.network > 80 ? '#ff4d4f' : data.systemStatus.network > 60 ? '#faad14' : '#52c41a'}
              className="mt-2"
            />
          </Card>
        </Col>
      </Row>

      {/* 详细监控信息 */}
      <Tabs defaultActiveKey="websites" className="mb-6">
        <TabPane tab={
          <span>
            <GlobalOutlined />
            网站监控 ({data.websiteStatus.length})
          </span>
        } key="websites">
          <Card>
            <Table
              dataSource={data.websiteStatus}
              rowKey="id"
              size="small"
              pagination={false}
              loading={loading}
              columns={[
                {
                  title: '网站名称',
                  dataIndex: 'name',
                  key: 'name',
                  render: (name: string, record: any) => (
                    <div>
                      <div className="font-medium">{name}</div>
                      <div className="text-sm text-gray-500">{record.url}</div>
                    </div>
                  )
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  render: (status: string) => (
                    <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
                      {status === 'online' ? '在线' : status === 'offline' ? '离线' : '警告'}
                    </Tag>
                  )
                },
                {
                  title: '响应时间',
                  dataIndex: 'responseTime',
                  key: 'responseTime',
                  render: (time: number) => (
                    <span className={time > 1000 ? 'text-red-500' : time > 500 ? 'text-orange-500' : 'text-green-500'}>
                      {time}ms
                    </span>
                  )
                },
                {
                  title: '可用性',
                  dataIndex: 'uptime',
                  key: 'uptime',
                  render: (uptime: number) => (
                    <div>
                      <span className={uptime >= 99.5 ? 'text-green-500' : uptime >= 99 ? 'text-orange-500' : 'text-red-500'}>
                        {uptime}%
                      </span>
                      <Progress
                        percent={uptime}
                        size="small"
                        strokeColor={uptime >= 99.5 ? '#52c41a' : uptime >= 99 ? '#faad14' : '#ff4d4f'}
                        className="mt-1"
                      />
                    </div>
                  )
                },
                {
                  title: '最后检查',
                  dataIndex: 'lastCheck',
                  key: 'lastCheck',
                  render: (time: string) => new Date(time).toLocaleString()
                },
                {
                  title: '操作',
                  key: 'actions',
                  render: (_, record: any) => (
                    <Space size="small">
                      <Tooltip title="查看详情">
                        <Button type="text" icon={<EyeOutlined />} />
                      </Tooltip>
                      <Tooltip title="立即检查">
                        <Button type="text" icon={<ReloadOutlined />} />
                      </Tooltip>
                    </Space>
                  )
                }
              ]}
            />
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <CloudServerOutlined />
            服务器监控 ({data.serverStatus.length})
          </span>
        } key="servers">
          <Card>
            <Table
              dataSource={data.serverStatus}
              rowKey="id"
              size="small"
              pagination={false}
              loading={loading}
              columns={[
                {
                  title: '服务器信息',
                  dataIndex: 'name',
                  key: 'name',
                  render: (name: string, record: any) => (
                    <div>
                      <div className="font-medium">{name}</div>
                      <div className="text-sm text-gray-500">{record.ip}</div>
                    </div>
                  )
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  render: (status: string) => (
                    <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
                      {status === 'online' ? '在线' : status === 'offline' ? '离线' : '维护中'}
                    </Tag>
                  )
                },
                {
                  title: 'CPU',
                  dataIndex: 'cpu',
                  key: 'cpu',
                  render: (cpu: number) => (
                    <div>
                      <span className={cpu > 80 ? 'text-red-500' : cpu > 60 ? 'text-orange-500' : 'text-green-500'}>
                        {cpu}%
                      </span>
                      <Progress
                        percent={cpu}
                        size="small"
                        strokeColor={cpu > 80 ? '#ff4d4f' : cpu > 60 ? '#faad14' : '#52c41a'}
                        className="mt-1"
                      />
                    </div>
                  )
                },
                {
                  title: '内存',
                  dataIndex: 'memory',
                  key: 'memory',
                  render: (memory: number) => (
                    <div>
                      <span className={memory > 80 ? 'text-red-500' : memory > 60 ? 'text-orange-500' : 'text-green-500'}>
                        {memory}%
                      </span>
                      <Progress
                        percent={memory}
                        size="small"
                        strokeColor={memory > 80 ? '#ff4d4f' : memory > 60 ? '#faad14' : '#52c41a'}
                        className="mt-1"
                      />
                    </div>
                  )
                },
                {
                  title: '磁盘',
                  dataIndex: 'disk',
                  key: 'disk',
                  render: (disk: number) => (
                    <div>
                      <span className={disk > 80 ? 'text-red-500' : disk > 60 ? 'text-orange-500' : 'text-green-500'}>
                        {disk}%
                      </span>
                      <Progress
                        percent={disk}
                        size="small"
                        strokeColor={disk > 80 ? '#ff4d4f' : disk > 60 ? '#faad14' : '#52c41a'}
                        className="mt-1"
                      />
                    </div>
                  )
                },
                {
                  title: '运行时间',
                  dataIndex: 'uptime',
                  key: 'uptime'
                }
              ]}
            />
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <SafetyCertificateOutlined />
            SSL监控 ({data.sslStatus.length})
          </span>
        } key="ssl">
          <Card>
            <Table
              dataSource={data.sslStatus}
              rowKey="id"
              size="small"
              pagination={false}
              loading={loading}
              columns={[
                {
                  title: '域名',
                  dataIndex: 'domain',
                  key: 'domain',
                  render: (domain: string) => (
                    <span className="font-medium">{domain}</span>
                  )
                },
                {
                  title: '颁发机构',
                  dataIndex: 'issuer',
                  key: 'issuer'
                },
                {
                  title: '到期时间',
                  dataIndex: 'expiryDate',
                  key: 'expiryDate',
                  render: (date: string) => new Date(date).toLocaleDateString()
                },
                {
                  title: '剩余天数',
                  dataIndex: 'daysLeft',
                  key: 'daysLeft',
                  render: (days: number) => (
                    <span className={days < 0 ? 'text-red-500' : days <= 30 ? 'text-orange-500' : 'text-green-500'}>
                      {days < 0 ? `已过期 ${Math.abs(days)} 天` : `${days} 天`}
                    </span>
                  )
                },
                {
                  title: '状态',
                  dataIndex: 'status',
                  key: 'status',
                  render: (status: string) => (
                    <Tag color={getStatusColor(status)} icon={getStatusIcon(status)}>
                      {status === 'valid' ? '有效' : status === 'expiring' ? '即将过期' : '已过期'}
                    </Tag>
                  )
                }
              ]}
            />
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <ExclamationCircleOutlined />
            告警记录 ({data.alerts.filter(alert => !alert.resolved).length})
          </span>
        } key="alerts">
          <Card>
            <Timeline>
              {data.alerts.map(alert => (
                <Timeline.Item
                  key={alert.id}
                  color={alert.type === 'error' ? 'red' : alert.type === 'warning' ? 'orange' : 'blue'}
                  dot={
                    alert.type === 'error' ? <ExclamationCircleOutlined /> :
                    alert.type === 'warning' ? <WarningOutlined /> : <CheckCircleOutlined />
                  }
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="font-medium">{alert.message}</div>
                      <div className="text-sm text-gray-500">{alert.time}</div>
                    </div>
                    <div>
                      {alert.resolved ? (
                        <Tag color="green">已处理</Tag>
                      ) : (
                        <Button size="small" type="primary">
                          处理
                        </Button>
                      )}
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        </TabPane>
      </Tabs>
    </motion.div>
  );
};

export default MonitorDashboard;
