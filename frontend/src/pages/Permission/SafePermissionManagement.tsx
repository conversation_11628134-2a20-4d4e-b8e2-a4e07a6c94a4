import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  message, 
  Typography, 
  Modal,
  Form,
  Select,
  Checkbox,
  Row,
  Col,
  Divider,
  Alert
} from 'antd';
import { 
  EditOutlined, 
  SafetyCertificateOutlined, 
  CheckCircleOutlined,
  UserOutlined,
  DiffOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { UserApi } from '../../services/user';

const { Title, Text } = Typography;
const { Option } = Select;

// 安全的权限定义
const SAFE_PERMISSIONS = {
  user: {
    'user.create': { label: '创建用户', description: '创建新用户账户' },
    'user.edit': { label: '编辑用户', description: '修改用户信息' },
    'user.delete': { label: '删除用户', description: '删除用户账户' },
    'user.list': { label: '查看用户列表', description: '查看所有用户' },
    'user.view': { label: '查看用户详情', description: '查看用户详细信息' },
    'user.manage_role': { label: '管理用户角色', description: '分配和管理用户角色' },
    'user.reset_password': { label: '重置密码', description: '重置用户密码' }
  },
  site: {
    'site.create': { label: '创建网站', description: '创建新的网站' },
    'site.edit': { label: '编辑网站', description: '修改网站配置' },
    'site.delete': { label: '删除网站', description: '删除网站' },
    'site.list': { label: '查看网站列表', description: '查看所有网站' },
    'site.view': { label: '查看网站详情', description: '查看网站详细信息' },
    'site.monitor': { label: '网站监控', description: '监控网站状态' }
  },
  server: {
    'server.create': { label: '创建服务器', description: '创建新服务器' },
    'server.edit': { label: '编辑服务器', description: '修改服务器配置' },
    'server.delete': { label: '删除服务器', description: '删除服务器' },
    'server.list': { label: '查看服务器列表', description: '查看所有服务器' },
    'server.view': { label: '查看服务器详情', description: '查看服务器详细信息' },
    'server.monitor': { label: '服务器监控', description: '监控服务器状态' }
  },
  system: {
    'system.settings': { label: '系统设置', description: '修改系统配置' },
    'system.logs': { label: '查看日志', description: '查看系统日志' },
    'system.backup': { label: '数据备份', description: '执行数据备份' },
    'system.restore': { label: '数据恢复', description: '执行数据恢复' }
  }
};

// 安全的角色权限定义
const SAFE_ROLE_PERMISSIONS = {
  super_admin: {
    label: '超级管理员',
    description: '拥有所有权限',
    permissions: [
      'user.create', 'user.edit', 'user.delete', 'user.list', 'user.view', 'user.manage_role', 'user.reset_password',
      'site.create', 'site.edit', 'site.delete', 'site.list', 'site.view', 'site.monitor',
      'server.create', 'server.edit', 'server.delete', 'server.list', 'server.view', 'server.monitor',
      'system.settings', 'system.logs', 'system.backup', 'system.restore'
    ]
  },
  admin: {
    label: '管理员',
    description: '拥有大部分管理权限',
    permissions: [
      'user.create', 'user.edit', 'user.list', 'user.view', 'user.manage_role',
      'site.create', 'site.edit', 'site.list', 'site.view', 'site.monitor',
      'server.create', 'server.edit', 'server.list', 'server.view', 'server.monitor',
      'system.logs'
    ]
  },
  user: {
    label: '普通用户',
    description: '基本访问权限',
    permissions: [
      'site.view', 'site.list'
    ]
  }
};

const SafePermissionManagement: React.FC = () => {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  // 安全获取角色权限
  const getRolePermissions = (role: string): string[] => {
    try {
      const roleConfig = SAFE_ROLE_PERMISSIONS[role as keyof typeof SAFE_ROLE_PERMISSIONS];
      return roleConfig?.permissions || [];
    } catch (error) {
      console.error('获取角色权限失败:', error);
      return [];
    }
  };

  // 安全获取角色标签
  const getRoleLabel = (role: string): string => {
    try {
      const roleConfig = SAFE_ROLE_PERMISSIONS[role as keyof typeof SAFE_ROLE_PERMISSIONS];
      return roleConfig?.label || role;
    } catch (error) {
      console.error('获取角色标签失败:', error);
      return role;
    }
  };

  // 加载用户列表
  const loadUsers = async () => {
    setLoading(true);
    try {
      const response = await UserApi.getUsersSimple();
      if (response.success) {
        setUsers(response.data.users);
      } else {
        message.error('加载用户列表失败: ' + response.message);
      }
    } catch (error) {
      console.error('加载用户列表错误:', error);
      message.error('加载用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, []);

  // 编辑用户权限
  const handleEditPermissions = (user: any) => {
    try {
      setCurrentUser(user);
      const rolePermissions = getRolePermissions(user.role);
      
      form.setFieldsValue({
        role: user.role,
        customPermissions: rolePermissions
      });
      setEditModalVisible(true);
    } catch (error) {
      console.error('编辑权限失败:', error);
      message.error('编辑权限失败');
    }
  };

  // 保存权限更改
  const handleSavePermissions = async () => {
    try {
      const values = await form.validateFields();
      const updateData = {
        role: values.role,
        permissions: values.customPermissions || []
      };

      const response = await UserApi.updateUser(currentUser.id, updateData);
      
      if (response.success) {
        message.success('权限更新成功');
        setEditModalVisible(false);
        loadUsers();
      } else {
        message.error('权限更新失败: ' + response.message);
      }
    } catch (error) {
      console.error('保存权限失败:', error);
      message.error('保存权限失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text: string) => (
        <Space>
          <UserOutlined />
          {text}
        </Space>
      ),
    },
    {
      title: '真实姓名',
      dataIndex: 'realName',
      key: 'realName',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => {
        const label = getRoleLabel(role);
        const color = role === 'super_admin' ? 'red' : role === 'admin' ? 'orange' : 'blue';
        return <Tag color={color}>{label}</Tag>;
      },
    },
    {
      title: '权限数量',
      key: 'permissions',
      render: (record: any) => {
        const permissionCount = getRolePermissions(record.role).length;
        return (
          <Space>
            <CheckCircleOutlined style={{ color: '#52c41a' }} />
            <Text>{permissionCount} 项权限</Text>
          </Space>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '正常' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: any) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditPermissions(record)}
          >
            编辑权限
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <Title level={2} style={{ margin: 0 }}>
          <SafetyCertificateOutlined /> 权限管理
        </Title>
        <Button
          type="primary"
          icon={<DiffOutlined />}
          onClick={() => navigate('/permissions/comparison')}
        >
          查看系统优化对比
        </Button>
      </div>

      <Alert
        message="权限系统已优化"
        description="当前使用的是优化后的权限管理系统，支持细粒度权限控制、缓存优化和完整的审计功能。点击右上角按钮可查看优化前后的详细对比。"
        type="success"
        showIcon
        style={{ marginBottom: '16px' }}
      />
      
      <Card>
        <Table
          columns={columns}
          dataSource={users}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个用户`,
          }}
        />
      </Card>

      {/* 编辑权限模态框 */}
      <Modal
        title={`编辑用户权限 - ${currentUser?.username}`}
        open={editModalVisible}
        onOk={handleSavePermissions}
        onCancel={() => setEditModalVisible(false)}
        width={800}
        okText="保存"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="用户角色"
            name="role"
            rules={[{ required: true, message: '请选择用户角色' }]}
          >
            <Select placeholder="请选择角色">
              {Object.entries(SAFE_ROLE_PERMISSIONS).map(([key, value]) => (
                <Option key={key} value={key}>
                  {value.label} - {value.description}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="自定义权限"
            name="customPermissions"
            tooltip="可以在角色基础上添加或移除特定权限"
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <Row gutter={[16, 8]}>
                {Object.entries(SAFE_PERMISSIONS).map(([module, modulePermissions]) => (
                  <React.Fragment key={module}>
                    <Col span={24} style={{ marginTop: module !== 'user' ? '16px' : '0' }}>
                      <Title level={5}>
                        {module === 'user' && '用户管理权限'}
                        {module === 'site' && '网站管理权限'}
                        {module === 'server' && '服务器管理权限'}
                        {module === 'system' && '系统管理权限'}
                      </Title>
                    </Col>
                    {Object.entries(modulePermissions).map(([key, value]) => (
                      <Col span={12} key={key}>
                        <Checkbox value={key}>
                          {value.label}
                          <br />
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {value.description}
                          </Text>
                        </Checkbox>
                      </Col>
                    ))}
                  </React.Fragment>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SafePermissionManagement;
