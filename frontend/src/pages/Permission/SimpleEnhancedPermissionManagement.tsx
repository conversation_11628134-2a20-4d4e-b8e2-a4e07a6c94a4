/**
 * 简化的增强权限管理界面
 * 功能完整但避免复杂依赖
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Select,
  Space,
  Modal,
  Form,
  Input,
  Checkbox,
  Tag,
  Alert,
  Tabs,
  Row,
  Col,
  Statistic,
  Divider,
  App
} from 'antd';
import {
  UserOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  EditOutlined,
  PlusOutlined,
  ExportOutlined,
  ReloadOutlined,
  EyeOutlined
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { apiCall, apiGet, apiPost, apiPut } from '../../utils/apiConfig';

const { Option } = Select;

// 简化的类型定义
interface SimplePermission {
  id: number;
  name: string;
  code: string;
  description: string;
  module: string;
}

interface SimpleUser {
  id: number;
  username: string;
  email: string;
  role: string;
  status: string;
  permissions: string[];
  // 详细权限信息（编辑时获取）
  rolePermissions?: string[];
  customPermissions?: Array<{
    permission_code: string;
    granted: number;
  }>;
  effectivePermissions?: string[];
  deniedPermissions?: string[];
}

// 权限模板类型定义
interface PermissionTemplate {
  id: number;
  name: string;
  description: string;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
  isDefault: boolean;
}

/**
 * 简化的增强权限管理组件
 */
const SimpleEnhancedPermissionManagement: React.FC = () => {
  const { message } = App.useApp();
  const [permissions, setPermissions] = useState<SimplePermission[]>([]);
  const [users, setUsers] = useState<SimpleUser[]>([]);
  const [templates, setTemplates] = useState<PermissionTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<SimpleUser | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<PermissionTemplate | null>(null);
  const [form] = Form.useForm();
  const [templateForm] = Form.useForm();

  // 获取权限列表
  const fetchPermissions = async () => {
    try {
      setLoading(true);
      console.log('🔍 开始获取权限列表');

      const response = await apiGet('/permissions');
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ 权限列表获取成功:', data);
        
        if (data.success && data.data.permissions) {
          setPermissions(data.data.permissions);
        } else {
          console.warn('⚠️ 权限数据格式异常:', data);
          message.warning('权限数据格式异常');
        }
      } else {
        console.error('❌ 权限列表获取失败:', response.status, response.statusText);
        message.error(`获取权限列表失败: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ 权限列表获取异常:', error);
      message.error('获取权限列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      console.log('🔍 开始获取用户列表');

      const response = await apiGet('/users/with-permissions');
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ 用户列表获取成功:', data);
        
        if (data.success && data.data.users) {
          // 转换数据格式以匹配组件需求
          const formattedUsers = data.data.users.map((user: any) => ({
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            status: user.status,
            permissions: user.effectivePermissions || []
          }));
          setUsers(formattedUsers);
        } else {
          console.warn('⚠️ 用户数据格式异常:', data);
          message.warning('用户数据格式异常');
        }
      } else {
        console.error('❌ 用户列表获取失败:', response.status, response.statusText);
        message.error(`获取用户列表失败: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ 用户列表获取异常:', error);
      message.error('获取用户列表失败');
    }
  };

  // 获取权限模板列表
  const fetchTemplates = async () => {
    try {
      console.log('🔍 开始获取权限模板列表');

      const response = await apiGet('/permission-templates');
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ 权限模板列表获取成功:', data);
        
        if (data.success && data.data) {
          // 修复数据格式：API直接返回templates数组在data字段中
          const templatesData = Array.isArray(data.data) ? data.data : [];
          const formattedTemplates = templatesData.map((template: any) => ({
            id: template.id,
            name: template.name,
            description: template.description,
            permissions: Array.isArray(template.permissions) ? template.permissions : [],
            createdAt: template.created_at,
            updatedAt: template.updated_at,
            isDefault: template.is_system || false
          }));
          setTemplates(formattedTemplates);
        } else {
          console.warn('⚠️ 权限模板数据格式异常:', data);
          message.warning('权限模板数据格式异常');
        }
      } else {
        console.error('❌ 权限模板列表获取失败:', response.status, response.statusText);
        message.error(`获取权限模板列表失败: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ 权限模板列表获取异常:', error);
      message.error('获取权限模板列表失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchPermissions();
    fetchUsers();
    fetchTemplates();
  }, []);

  // 权限表格列定义
  const permissionColumns: TableColumnsType<SimplePermission> = [
    {
      title: '权限名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.code}</div>
        </div>
      )
    },
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module',
      render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewPermission(record)}
          >
            查看
          </Button>
        </Space>
      )
    }
  ];

  // 用户表格列定义
  const userColumns: TableColumnsType<SimpleUser> = [
    {
      title: '用户',
      key: 'user',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            <UserOutlined style={{ marginRight: 8 }} />
            {record.username}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.email}</div>
        </div>
      )
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => {
        const colorMap: Record<string, string> = {
          'super_admin': 'red',
          'admin': 'orange',
          'user': 'blue'
        };
        return <Tag color={colorMap[role] || 'default'}>{role}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '活跃' : '非活跃'}
        </Tag>
      )
    },
    {
      title: '权限数量',
      key: 'permissionCount',
      render: (_, record) => (
        <div>{record.permissions.length} 项权限</div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewUser(record)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditUser(record)}
          >
            编辑
          </Button>
        </Space>
      )
    }
  ];

  // 权限模板表格列定义
  const templateColumns: TableColumnsType<PermissionTemplate> = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            {text}
            {record.isDefault && <Tag color="gold" style={{ marginLeft: 8 }}>默认</Tag>}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.description}</div>
        </div>
      )
    },
    {
      title: '权限数量',
      key: 'permissionCount',
      render: (_, record) => (
        <div>{record.permissions.length} 项权限</div>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text) => new Date(text).toLocaleDateString()
    },
    {
      title: '更新时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (text) => new Date(text).toLocaleDateString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewTemplate(record)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditTemplate(record)}
          >
            编辑
          </Button>
          <Button
            type="text"
            danger
            onClick={() => handleDeleteTemplate(record)}
            disabled={record.isDefault}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  // 查看权限详情
  const handleViewPermission = (permission: SimplePermission) => {
    Modal.info({
      title: '权限详情',
      content: (
        <div>
          <p><strong>权限名称:</strong> {permission.name}</p>
          <p><strong>权限代码:</strong> {permission.code}</p>
          <p><strong>所属模块:</strong> {permission.module}</p>
          <p><strong>权限描述:</strong> {permission.description}</p>
        </div>
      )
    });
  };

  // 查看用户详情
  const handleViewUser = (user: SimpleUser) => {
    Modal.info({
      title: '用户权限详情',
      width: 600,
      content: (
        <div>
          <p><strong>用户名:</strong> {user.username}</p>
          <p><strong>邮箱:</strong> {user.email}</p>
          <p><strong>角色:</strong> {user.role}</p>
          <p><strong>状态:</strong> {user.status}</p>
          <Divider />
          <p><strong>拥有权限:</strong></p>
          <div>
            {user.permissions.map(permission => (
              <Tag key={permission} style={{ marginBottom: '4px' }}>
                {permission}
              </Tag>
            ))}
          </div>
        </div>
      )
    });
  };

  // 编辑用户权限
  const handleEditUser = async (user: SimpleUser) => {
    try {
      console.log('🔍 开始获取用户详细权限:', user.id);
      setLoading(true);

      // 获取用户的详细权限信息
      const response = await apiGet(`/users/${user.id}/permissions`);

      if (response.ok) {
        const data = await response.json();
        console.log('✅ 用户权限获取成功:', data);

        if (data.success && data.data) {
          const userPermissions = data.data;

          // 更新选中的用户信息，包含详细权限
          const updatedUser = {
            ...user,
            rolePermissions: userPermissions.rolePermissions || [],
            customPermissions: userPermissions.customPermissions || [],
            effectivePermissions: userPermissions.effectivePermissions || [],
            deniedPermissions: userPermissions.deniedPermissions || []
          };

          setSelectedUser(updatedUser);
          form.setFieldsValue({
            username: user.username,
            email: user.email,
            role: user.role,
            permissions: userPermissions.effectivePermissions || []
          });
          setEditModalVisible(true);
        } else {
          console.warn('⚠️ 用户权限数据格式异常:', data);
          message.warning('获取用户权限失败');
        }
      } else {
        console.error('❌ 用户权限获取失败:', response.status, response.statusText);
        message.error(`获取用户权限失败: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ 用户权限获取异常:', error);
      message.error('获取用户权限失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存用户权限
  const handleSaveUser = async () => {
    try {
      const values = await form.validateFields();
      
      if (selectedUser) {
        console.log('🔍 开始保存用户权限:', { userId: selectedUser.id, values });
        
        // 调用后端API更新用户权限
        const response = await apiPut(`/users/${selectedUser.id}/permissions`, {
          permissions: values.permissions || []
        });
        
        if (response.ok) {
          const data = await response.json();
          console.log('✅ 用户权限保存成功:', data);
          
          // 更新本地用户数据
          const updatedUsers = users.map(user => 
            user.id === selectedUser.id 
              ? { ...user, ...values }
              : user
          );
          setUsers(updatedUsers);
          message.success('用户权限更新成功');
        } else {
          console.error('❌ 用户权限保存失败:', response.status, response.statusText);
          message.error(`保存失败: ${response.status}`);
        }
      }
      
      setEditModalVisible(false);
      setSelectedUser(null);
      form.resetFields();
    } catch (error) {
      console.error('❌ 用户权限保存异常:', error);
      message.error('保存失败');
    }
  };

  // 查看权限模板详情
  const handleViewTemplate = (template: PermissionTemplate) => {
    Modal.info({
      title: '权限模板详情',
      width: 600,
      content: (
        <div>
          <p><strong>模板名称:</strong> {template.name}</p>
          <p><strong>模板描述:</strong> {template.description}</p>
          <p><strong>是否默认:</strong> {template.isDefault ? '是' : '否'}</p>
          <p><strong>创建时间:</strong> {new Date(template.createdAt).toLocaleString()}</p>
          <p><strong>更新时间:</strong> {new Date(template.updatedAt).toLocaleString()}</p>
          <Divider />
          <p><strong>包含权限:</strong></p>
          <div>
            {template.permissions.map(permission => (
              <Tag key={permission} style={{ marginBottom: '4px' }}>
                {permission}
              </Tag>
            ))}
          </div>
        </div>
      )
    });
  };

  // 编辑权限模板
  const handleEditTemplate = (template: PermissionTemplate) => {
    setSelectedTemplate(template);
    templateForm.setFieldsValue({
      name: template.name,
      description: template.description,
      permissions: template.permissions,
      isDefault: template.isDefault
    });
    setTemplateModalVisible(true);
  };

  // 新增权限模板
  const handleAddTemplate = () => {
    setSelectedTemplate(null);
    templateForm.resetFields();
    setTemplateModalVisible(true);
  };

  // 保存权限模板
  const handleSaveTemplate = async () => {
    try {
      const values = await templateForm.validateFields();
      
      console.log('🔍 开始保存权限模板:', values);
      
      const url = selectedTemplate 
        ? `/api/v1/permission-templates/${selectedTemplate.id}`
        : '/api/v1/permission-templates';
      
      const method = selectedTemplate ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(values)
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ 权限模板保存成功:', data);
        
        // 刷新模板列表
        await fetchTemplates();
        message.success(selectedTemplate ? '权限模板更新成功' : '权限模板创建成功');
      } else {
        console.error('❌ 权限模板保存失败:', response.status, response.statusText);
        message.error(`保存失败: ${response.status}`);
      }
      
      setTemplateModalVisible(false);
      setSelectedTemplate(null);
      templateForm.resetFields();
    } catch (error) {
      console.error('❌ 权限模板保存异常:', error);
      message.error('保存失败');
    }
  };

  // 删除权限模板
  const handleDeleteTemplate = (template: PermissionTemplate) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除权限模板 "${template.name}" 吗？此操作不可恢复。`,
      onOk: async () => {
        try {
          console.log('🔍 开始删除权限模板:', template.id);
          
          const response = await fetch(`/api/v1/permission-templates/${template.id}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (response.ok) {
            console.log('✅ 权限模板删除成功');
            
            // 更新本地模板列表
            const updatedTemplates = templates.filter(t => t.id !== template.id);
            setTemplates(updatedTemplates);
            message.success('权限模板删除成功');
          } else {
            console.error('❌ 权限模板删除失败:', response.status, response.statusText);
            message.error(`删除失败: ${response.status}`);
          }
        } catch (error) {
          console.error('❌ 权限模板删除异常:', error);
          message.error('删除失败');
        }
      }
    });
  };

  // 应用权限模板到用户
  const handleApplyTemplate = (templateId: number) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      console.log('🔍 应用权限模板:', template.name, template.permissions);
      
      // 更新表单中的权限选择
      const currentValues = form.getFieldsValue();
      form.setFieldsValue({
        ...currentValues,
        permissions: template.permissions
      });
      
      message.success(`已应用权限模板: ${template.name}`);
    }
  };

  // 导出权限配置
  const handleExport = () => {
    const data = {
      permissions,
      users,
      exportTime: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `permissions_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    message.success('权限配置导出成功');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Statistic
                title="总权限数"
                value={permissions.length}
                prefix={<SecurityScanOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="用户数"
                value={users.length}
                prefix={<UserOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="模块数"
                value={new Set(permissions.map(p => p.module)).size}
                prefix={<SettingOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="活跃用户"
                value={users.filter(u => u.status === 'active').length}
                prefix={<UserOutlined />}
              />
            </Col>
          </Row>
        </div>

        <Alert
          message="增强权限管理系统"
          description="这是一个功能完整的权限管理系统，支持权限查看、用户管理、权限分配等功能。"
          type="success"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Tabs 
          defaultActiveKey="permissions"
          items={[
            {
              key: 'permissions',
              label: '权限管理',
              children: (
                <>
                  <div style={{ marginBottom: '16px' }}>
                    <Space>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          console.log('🔄 手动刷新权限列表');
                          fetchPermissions();
                        }}
                        loading={loading}
                      >
                        刷新
                      </Button>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={() => message.info('新增权限功能开发中')}
                      >
                        新增权限
                      </Button>
                      <Button
                        icon={<ExportOutlined />}
                        onClick={handleExport}
                      >
                        导出配置
                      </Button>
                    </Space>
                  </div>

                  <Table
                    columns={permissionColumns}
                    dataSource={permissions}
                    rowKey="id"
                    loading={loading}
                    pagination={{
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 条记录`
                    }}
                  />
                </>
              )
            },
            {
              key: 'users',
              label: '用户权限',
              children: (
                <>
                  <div style={{ marginBottom: '16px' }}>
                    <Space>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          console.log('🔄 手动刷新用户列表');
                          fetchUsers();
                        }}
                        loading={loading}
                      >
                        刷新
                      </Button>
                      <Button
                        type="primary"
                        icon={<EditOutlined />}
                        onClick={() => message.info('批量操作功能开发中')}
                      >
                        批量操作
                      </Button>
                    </Space>
                  </div>

                  <Table
                    columns={userColumns}
                    dataSource={users}
                    rowKey="id"
                    loading={loading}
                    pagination={{
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 条记录`
                    }}
                  />
                </>
              )
            },
            {
              key: 'templates',
              label: '权限模板',
              children: (
                <>
                  <div style={{ marginBottom: '16px' }}>
                    <Space>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => {
                          console.log('🔄 手动刷新权限模板列表');
                          fetchTemplates();
                        }}
                        loading={loading}
                      >
                        刷新
                      </Button>
                      <Button
                        type="primary"
                        icon={<PlusOutlined />}
                        onClick={handleAddTemplate}
                      >
                        新增模板
                      </Button>
                      <Button
                        icon={<ExportOutlined />}
                        onClick={handleExport}
                      >
                        导出配置
                      </Button>
                    </Space>
                  </div>

                  <Table
                    columns={templateColumns}
                    dataSource={templates}
                    rowKey="id"
                    loading={loading}
                    pagination={{
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total) => `共 ${total} 条记录`
                    }}
                  />
                </>
              )
            }
          ]}
        />
      </Card>

      {/* 编辑用户权限模态框 */}
      <Modal
        title={`编辑用户权限 - ${selectedUser?.username || ''}`}
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setSelectedUser(null);
          form.resetFields();
        }}
        onOk={handleSaveUser}
        width={800}
        style={{ top: 20 }}
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="username" label="用户名">
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="email" label="邮箱">
                <Input disabled />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="role" label="角色">
            <Select disabled>
              <Option value="super_admin">超级管理员</Option>
              <Option value="admin">管理员</Option>
              <Option value="user">普通用户</Option>
            </Select>
          </Form.Item>

          {/* 权限信息展示 */}
          {selectedUser && (
            <div style={{ marginBottom: 16 }}>
              <Divider orientation="left">权限详情</Divider>

              {/* 角色权限 */}
              <div style={{ marginBottom: 16 }}>
                <h4 style={{ color: '#1890ff', marginBottom: 8 }}>
                  角色权限 ({selectedUser.rolePermissions?.length || 0} 项)
                </h4>
                <div style={{
                  background: '#f6ffed',
                  border: '1px solid #b7eb8f',
                  borderRadius: 4,
                  padding: 8,
                  maxHeight: 120,
                  overflowY: 'auto'
                }}>
                  {selectedUser.rolePermissions?.length ? (
                    <Row gutter={[8, 4]}>
                      {selectedUser.rolePermissions.map(perm => (
                        <Col span={12} key={perm}>
                          <span style={{ fontSize: '12px', color: '#52c41a' }}>
                            ✓ {permissions.find(p => p.code === perm)?.name || perm}
                          </span>
                        </Col>
                      ))}
                    </Row>
                  ) : (
                    <span style={{ color: '#999' }}>该角色暂无权限</span>
                  )}
                </div>
              </div>

              {/* 自定义权限 */}
              <div style={{ marginBottom: 16 }}>
                <h4 style={{ color: '#fa8c16', marginBottom: 8 }}>
                  自定义权限 ({selectedUser.customPermissions?.length || 0} 项)
                </h4>
                <div style={{
                  background: '#fff7e6',
                  border: '1px solid #ffd591',
                  borderRadius: 4,
                  padding: 8,
                  maxHeight: 120,
                  overflowY: 'auto'
                }}>
                  {selectedUser.customPermissions?.length ? (
                    <Row gutter={[8, 4]}>
                      {selectedUser.customPermissions.map(perm => (
                        <Col span={12} key={perm.permission_code}>
                          <span style={{
                            fontSize: '12px',
                            color: perm.granted ? '#fa8c16' : '#ff4d4f'
                          }}>
                            {perm.granted ? '✓' : '✗'} {permissions.find(p => p.code === perm.permission_code)?.name || perm.permission_code}
                          </span>
                        </Col>
                      ))}
                    </Row>
                  ) : (
                    <span style={{ color: '#999' }}>暂无自定义权限</span>
                  )}
                </div>
              </div>
            </div>
          )}

          <Form.Item label="快速应用权限模板">
            <Select
              placeholder="选择权限模板快速应用"
              allowClear
              onChange={handleApplyTemplate}
              style={{ width: '100%' }}
            >
              {templates.map(template => (
                <Option key={template.id} value={template.id}>
                  {template.name} ({template.permissions.length} 项权限)
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="permissions" label="有效权限（可编辑）">
            <Checkbox.Group style={{ width: '100%' }}>
              <Row gutter={[16, 8]}>
                {permissions.map(permission => (
                  <Col span={12} key={permission.code}>
                    <Checkbox value={permission.code}>
                      <span style={{ fontSize: '12px' }}>
                        {permission.name}
                        <br />
                        <span style={{ color: '#999' }}>{permission.code}</span>
                      </span>
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>

      {/* 权限模板编辑模态框 */}
      <Modal
        title={selectedTemplate ? '编辑权限模板' : '新增权限模板'}
        open={templateModalVisible}
        onCancel={() => {
          setTemplateModalVisible(false);
          setSelectedTemplate(null);
          templateForm.resetFields();
        }}
        onOk={handleSaveTemplate}
        width={700}
      >
        <Form form={templateForm} layout="vertical">
          <Form.Item 
            name="name" 
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="请输入权限模板名称" />
          </Form.Item>
          
          <Form.Item 
            name="description" 
            label="模板描述"
            rules={[{ required: true, message: '请输入模板描述' }]}
          >
            <Input.TextArea 
              placeholder="请输入权限模板描述" 
              rows={3}
            />
          </Form.Item>
          
          <Form.Item name="isDefault" valuePropName="checked" label="设为默认模板">
            <Checkbox>
              设为默认权限模板（新用户将自动应用此模板）
            </Checkbox>
          </Form.Item>
          
          <Form.Item 
            name="permissions" 
            label="包含权限"
            rules={[{ required: true, message: '请选择至少一个权限' }]}
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <Row gutter={[16, 8]}>
                {permissions.map(permission => (
                  <Col span={12} key={permission.code}>
                    <Checkbox value={permission.code}>
                      <div>
                        <div style={{ fontWeight: 500 }}>{permission.name}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          {permission.module} - {permission.description}
                        </div>
                      </div>
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SimpleEnhancedPermissionManagement;