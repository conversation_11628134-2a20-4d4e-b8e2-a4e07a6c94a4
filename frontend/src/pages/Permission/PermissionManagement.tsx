import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  message,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Tabs,
  Upload,
  Alert,
  Tree,
  Transfer,
  Checkbox,
  Radio,
  Divider,
  Avatar,
  Badge,
  Timeline,
  Steps
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UserOutlined,
  TeamOutlined,
  SafetyCertificateOutlined,
  KeyOutlined,
  UploadOutlined,
  DownloadOutlined,
  ImportOutlined,
  ExportOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  FileExcelOutlined,
  GlobalOutlined,
  DatabaseOutlined,
  CloudServerOutlined,
  ProjectOutlined
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import dayjs from 'dayjs';
import { User, Role, Permission, UserPermission, BatchPermissionOperation, PermissionImportData, QueryParams } from '../../types';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Dragger } = Upload;

interface PermissionManagementProps {}

const PermissionManagement: React.FC<PermissionManagementProps> = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [userPermissions, setUserPermissions] = useState<UserPermission[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('users');
  
  // 模态框状态
  const [userFormVisible, setUserFormVisible] = useState(false);
  const [roleFormVisible, setRoleFormVisible] = useState(false);
  const [permissionFormVisible, setPermissionFormVisible] = useState(false);
  const [batchPermissionVisible, setBatchPermissionVisible] = useState(false);
  const [importVisible, setImportVisible] = useState(false);
  
  const [currentUser, setCurrentUser] = useState<User | undefined>();
  const [currentRole, setCurrentRole] = useState<Role | undefined>();
  const [formMode, setFormMode] = useState<'create' | 'edit'>('create');
  
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10
  });

  const [userForm] = Form.useForm();
  const [roleForm] = Form.useForm();
  const [permissionForm] = Form.useForm();
  const [batchForm] = Form.useForm();
  const [importForm] = Form.useForm();

  // 权限模块配置
  const permissionModules = [
    { key: 'website', label: '网站管理', icon: <GlobalOutlined /> },
    { key: 'server', label: '服务器管理', icon: <CloudServerOutlined /> },
    { key: 'domain', label: '域名管理', icon: <DatabaseOutlined /> },
    { key: 'project', label: '项目管理', icon: <ProjectOutlined /> },
    { key: 'customer', label: '客户管理', icon: <TeamOutlined /> },
    { key: 'presales', label: '售前管理', icon: <UserOutlined /> },
    { key: 'system', label: '系统管理', icon: <SettingOutlined /> }
  ];

  // 权限类型配置
  const permissionTypes = [
    { key: 'read', label: '查看', color: 'blue' },
    { key: 'write', label: '编辑', color: 'green' },
    { key: 'admin', label: '管理', color: 'orange' },
    { key: 'delete', label: '删除', color: 'red' }
  ];

  // 角色配置
  const roleConfig = {
    super_admin: { label: '超级管理员', color: 'red' },
    admin: { label: '管理员', color: 'orange' },
    user: { label: '普通用户', color: 'blue' },
    sales: { label: '销售', color: 'green' },
    developer: { label: '开发者', color: 'purple' }
  };

  // 获取用户数据
  const fetchUsers = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockUsers: User[] = [
        {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          realName: '系统管理员',
          role: 'super_admin',
          status: 'active',
          permissions: ['website:admin', 'server:admin', 'user:admin'],
          department: '技术部',
          phone: '13800138000',
          lastLogin: '2024-06-15T10:00:00Z',
          createdAt: '2024-01-01',
          updatedAt: '2024-06-15'
        },
        {
          id: 2,
          username: 'sales001',
          email: '<EMAIL>',
          realName: '张销售',
          role: 'sales',
          status: 'active',
          permissions: ['customer:admin', 'presales:admin', 'website:read'],
          department: '销售部',
          phone: '13900139000',
          lastLogin: '2024-06-15T09:30:00Z',
          createdAt: '2024-01-01',
          updatedAt: '2024-06-15'
        }
      ];
      setUsers(mockUsers);
    } catch (error) {
      message.error('获取用户数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取角色数据
  const fetchRoles = async () => {
    try {
      const mockRoles: Role[] = [
        {
          id: 1,
          name: '超级管理员',
          code: 'super_admin',
          description: '拥有系统所有权限',
          permissions: [],
          isActive: true,
          isSystem: true,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        {
          id: 2,
          name: '销售经理',
          code: 'sales_manager',
          description: '销售部门管理权限',
          permissions: [],
          isActive: true,
          isSystem: false,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        }
      ];
      setRoles(mockRoles);
    } catch (error) {
      message.error('获取角色数据失败');
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, [queryParams]);

  // 处理批量权限操作
  const handleBatchPermission = () => {
    setBatchPermissionVisible(true);
    batchForm.resetFields();
  };

  // 处理权限导入
  const handleImport = () => {
    setImportVisible(true);
    importForm.resetFields();
  };

  // 处理权限导出
  const handleExport = () => {
    // 模拟导出
    const exportData = userPermissions.map(up => ({
      username: users.find(u => u.id === up.userId)?.username,
      resourceType: up.resourceType,
      permissionType: up.permissionType,
      grantedAt: up.grantedAt,
      expiresAt: up.expiresAt
    }));
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `permissions_${dayjs().format('YYYY-MM-DD')}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    message.success('权限数据导出成功');
  };

  // 上传配置
  const uploadProps = {
    name: 'file',
    accept: '.xlsx,.xls,.csv',
    beforeUpload: (file: any) => {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                     file.type === 'application/vnd.ms-excel' ||
                     file.type === 'text/csv';
      if (!isExcel) {
        message.error('只能上传Excel或CSV文件!');
      }
      return isExcel;
    },
    onChange: (info: any) => {
      if (info.file.status === 'done') {
        message.success('文件上传成功，正在解析...');
        // 这里处理文件解析逻辑
      }
    }
  };

  // 用户表格列
  const userColumns = [
    {
      title: '用户信息',
      dataIndex: 'username',
      key: 'username',
      width: 200,
      render: (username: string, record: User) => (
        <div className="flex items-center">
          <Avatar 
            size="small" 
            icon={<UserOutlined />} 
            src={record.avatar}
            className="mr-3"
          />
          <div>
            <div className="font-medium">{record.realName}</div>
            <div className="text-sm text-gray-500">{username}</div>
            <div className="text-xs text-gray-400">{record.email}</div>
          </div>
        </div>
      )
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      render: (role: string) => {
        const config = roleConfig[role as keyof typeof roleConfig];
        return <Tag color={config?.color}>{config?.label}</Tag>;
      }
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
      width: 100
    },
    {
      title: '权限数量',
      dataIndex: 'permissions',
      key: 'permissions',
      width: 100,
      render: (permissions: string[]) => (
        <Badge count={permissions?.length || 0} showZero color="blue" />
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '正常' : '停用'}
        </Tag>
      )
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      width: 150,
      render: (time: string) => time ? dayjs(time).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (_, record: User) => (
        <Space size="small">
          <Tooltip title="查看权限">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setCurrentUser(record);
                setPermissionFormVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="编辑用户">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => {
                setFormMode('edit');
                setCurrentUser(record);
                userForm.setFieldsValue(record);
                setUserFormVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="权限设置">
            <Button
              type="text"
              icon={<KeyOutlined />}
              onClick={() => {
                setCurrentUser(record);
                setBatchPermissionVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                Modal.confirm({
                  title: '确认删除',
                  content: '确定要删除这个用户吗？',
                  onOk: () => {
                    message.success('删除成功');
                    fetchUsers();
                  }
                });
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="p-6"
    >
      {/* 页面标题和操作 */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">权限管理</h1>
            <p className="text-gray-600 mt-1">管理用户权限、角色分配和资源访问控制</p>
          </div>
          <Space>
            <Button icon={<ImportOutlined />} onClick={handleImport}>
              导入权限
            </Button>
            <Button icon={<ExportOutlined />} onClick={handleExport}>
              导出权限
            </Button>
            <Button icon={<KeyOutlined />} onClick={handleBatchPermission}>
              批量授权
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setFormMode('create');
                setCurrentUser(undefined);
                userForm.resetFields();
                setUserFormVisible(true);
              }}
              className="bg-blue-500 hover:bg-blue-600"
            >
              新建用户
            </Button>
          </Space>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <div className="flex items-center">
              <div className="flex-1">
                <div className="text-sm text-gray-500">总用户数</div>
                <div className="text-2xl font-bold text-gray-900">{users.length}</div>
              </div>
              <UserOutlined className="text-3xl text-blue-500" />
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div className="flex items-center">
              <div className="flex-1">
                <div className="text-sm text-gray-500">活跃用户</div>
                <div className="text-2xl font-bold text-green-600">
                  {users.filter(u => u.status === 'active').length}
                </div>
              </div>
              <CheckCircleOutlined className="text-3xl text-green-500" />
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div className="flex items-center">
              <div className="flex-1">
                <div className="text-sm text-gray-500">角色数量</div>
                <div className="text-2xl font-bold text-purple-600">{roles.length}</div>
              </div>
              <TeamOutlined className="text-3xl text-purple-500" />
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div className="flex items-center">
              <div className="flex-1">
                <div className="text-sm text-gray-500">权限规则</div>
                <div className="text-2xl font-bold text-orange-600">{userPermissions.length}</div>
              </div>
              <SafetyCertificateOutlined className="text-3xl text-orange-500" />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'users',
              label: '用户管理',
              children: (
                <Table
                  columns={userColumns}
                  dataSource={users}
                  rowKey="id"
                  loading={loading}
                  scroll={{ x: 1000 }}
                  pagination={{
                    current: queryParams.page,
                    pageSize: queryParams.pageSize,
                    total: users.length,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条记录`
                  }}
                />
              )
            },
          
            {
              key: 'roles',
              label: '角色管理',
              children: (
                <>
                  <div className="mb-4">
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => setRoleFormVisible(true)}
                    >
                      新建角色
                    </Button>
                  </div>
                  <Table
                    dataSource={roles}
                    rowKey="id"
                    columns={[
                      { title: '角色名称', dataIndex: 'name', key: 'name' },
                      { title: '角色代码', dataIndex: 'code', key: 'code' },
                      { title: '描述', dataIndex: 'description', key: 'description' },
                      {
                        title: '状态',
                        dataIndex: 'isActive',
                        key: 'isActive',
                        render: (active: boolean) => (
                          <Tag color={active ? 'green' : 'red'}>
                            {active ? '启用' : '禁用'}
                          </Tag>
                        )
                      },
                      {
                        title: '操作',
                        key: 'actions',
                        render: (_, record: Role) => (
                          <Space>
                            <Button type="link" icon={<EditOutlined />}>编辑</Button>
                            <Button type="link" icon={<KeyOutlined />}>权限</Button>
                            {!record.isSystem && (
                              <Button type="link" danger icon={<DeleteOutlined />}>删除</Button>
                            )}
                          </Space>
                        )
                      }
                    ]}
                  />
                </>
              )
            },
            {
              key: 'permissions',
              label: '权限规则',
              children: (
                <>
                  <Alert
                    message="权限规则说明"
                    description="这里显示所有用户的具体权限分配情况，包括资源访问权限和操作权限"
                    type="info"
                    showIcon
                    className="mb-4"
                  />
                  {/* 权限规则表格 */}
                </>
              )
            }
          ]}
        />
      </Card>

      {/* 批量权限操作模态框 */}
      <Modal
        title="批量权限操作"
        open={batchPermissionVisible}
        onCancel={() => setBatchPermissionVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={batchForm}
          layout="vertical"
          onFinish={(values) => {
            console.log('批量权限操作:', values);
            message.success('批量权限操作成功');
            setBatchPermissionVisible(false);
          }}
        >
          <Alert
            message="批量权限操作"
            description="可以批量为多个用户分配或撤销特定资源的访问权限"
            type="info"
            showIcon
            className="mb-4"
          />
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="operation"
                label="操作类型"
                rules={[{ required: true, message: '请选择操作类型' }]}
              >
                <Radio.Group>
                  <Radio value="grant">授予权限</Radio>
                  <Radio value="revoke">撤销权限</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="resourceType"
                label="资源类型"
                rules={[{ required: true, message: '请选择资源类型' }]}
              >
                <Select placeholder="请选择资源类型">
                  {permissionModules.map(module => (
                    <Option key={module.key} value={module.key}>
                      {module.icon} {module.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="permissionType"
            label="权限类型"
            rules={[{ required: true, message: '请选择权限类型' }]}
          >
            <Checkbox.Group>
              {permissionTypes.map(type => (
                <Checkbox key={type.key} value={type.key}>
                  <Tag color={type.color}>{type.label}</Tag>
                </Checkbox>
              ))}
            </Checkbox.Group>
          </Form.Item>

          <Form.Item
            name="userIds"
            label="选择用户"
            rules={[{ required: true, message: '请选择用户' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择用户"
              optionFilterProp="children"
            >
              {users.map(user => (
                <Option key={user.id} value={user.id}>
                  <Avatar size="small" icon={<UserOutlined />} className="mr-2" />
                  {user.realName} ({user.username})
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="expiresAt"
            label="过期时间"
          >
            <DatePicker 
              style={{ width: '100%' }} 
              placeholder="选择过期时间（可选）"
              showTime
            />
          </Form.Item>

          <Form.Item
            name="notes"
            label="备注"
          >
            <TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>

          <Form.Item className="mb-0 text-right">
            <Space>
              <Button onClick={() => setBatchPermissionVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                执行操作
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 权限导入模态框 */}
      <Modal
        title="权限导入"
        open={importVisible}
        onCancel={() => setImportVisible(false)}
        footer={null}
        width={600}
      >
        <Alert
          message="导入说明"
          description="支持Excel和CSV格式文件，请确保文件包含：用户名、邮箱、资源类型、资源名称、权限类型等字段"
          type="info"
          showIcon
          className="mb-4"
        />
        
        <Form
          form={importForm}
          layout="vertical"
          onFinish={(values) => {
            message.success('权限导入成功');
            setImportVisible(false);
          }}
        >
          <Form.Item
            name="file"
            label="选择文件"
            rules={[{ required: true, message: '请选择要导入的文件' }]}
          >
            <Dragger {...uploadProps}>
              <p className="ant-upload-drag-icon">
                <FileExcelOutlined className="text-4xl text-green-500" />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持Excel (.xlsx, .xls) 和 CSV 格式文件
              </p>
            </Dragger>
          </Form.Item>

          <Form.Item className="mb-0 text-right">
            <Space>
              <Button onClick={() => setImportVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                开始导入
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </motion.div>
  );
};

export default PermissionManagement;
