/**
 * 原始的权限管理界面（优化前）
 * 这是一个简化的权限管理页面，展示优化前的功能
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Select,
  Space,
  Modal,
  Form,
  Input,
  message
} from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';

const { Option } = Select;

// 简单的类型定义
interface SimpleUser {
  id: number;
  username: string;
  email: string;
  role: string;
}

interface SimpleRole {
  id: number;
  name: string;
  permissions: string[];
}

/**
 * 原始权限管理组件（优化前）
 */
const OriginalPermissionManagement: React.FC = () => {
  const [users, setUsers] = useState<SimpleUser[]>([]);
  const [roles] = useState<SimpleRole[]>([
    { id: 1, name: 'admin', permissions: ['all'] },
    { id: 2, name: 'user', permissions: ['read'] }
  ]);
  const [loading, setLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<SimpleUser | null>(null);
  const [form] = Form.useForm();

  // 模拟获取用户数据
  useEffect(() => {
    setUsers([
      { id: 1, username: 'admin', email: '<EMAIL>', role: 'admin' },
      { id: 2, username: 'user1', email: '<EMAIL>', role: 'user' },
      { id: 3, username: 'user2', email: '<EMAIL>', role: 'user' }
    ]);
  }, []);

  // 简单的表格列定义
  const columns: TableColumnsType<SimpleUser> = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username'
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => (
        <span style={{ 
          color: role === 'admin' ? 'red' : 'blue',
          fontWeight: role === 'admin' ? 'bold' : 'normal'
        }}>
          {role}
        </span>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  // 编辑用户
  const handleEdit = (user: SimpleUser) => {
    setSelectedUser(user);
    form.setFieldsValue(user);
    setEditModalVisible(true);
  };

  // 删除用户
  const handleDelete = (userId: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个用户吗？',
      onOk: () => {
        setUsers(prev => prev.filter(u => u.id !== userId));
        message.success('删除成功');
      }
    });
  };

  // 保存用户
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      if (selectedUser) {
        // 更新用户
        setUsers(prev => prev.map(u => 
          u.id === selectedUser.id ? { ...u, ...values } : u
        ));
        message.success('更新成功');
      } else {
        // 新增用户
        const newUser = {
          id: Date.now(),
          ...values
        };
        setUsers(prev => [...prev, newUser]);
        message.success('添加成功');
      }
      
      setEditModalVisible(false);
      setSelectedUser(null);
      form.resetFields();
    } catch (error) {
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card title="用户权限管理">
        <div style={{ marginBottom: '16px' }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setSelectedUser(null);
              form.resetFields();
              setEditModalVisible(true);
            }}
          >
            添加用户
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={false}
        />
      </Card>

      {/* 编辑用户模态框 */}
      <Modal
        title={selectedUser ? "编辑用户" : "添加用户"}
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setSelectedUser(null);
          form.resetFields();
        }}
        onOk={handleSave}
        confirmLoading={loading}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="选择角色">
              {roles.map(role => (
                <Option key={role.id} value={role.name}>
                  {role.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default OriginalPermissionManagement;