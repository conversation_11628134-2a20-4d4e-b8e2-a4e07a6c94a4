import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  message,
  Row,
  Col,
  Tabs,
  Badge,
  Avatar
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UserOutlined,
  TeamOutlined,
  SafetyCertificateOutlined,
  KeyOutlined,
  ImportOutlined,
  ExportOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { User, Role, QueryParams } from '../../types';

const { TabPane } = Tabs;

interface SimplePermissionManagementProps {}

const SimplePermissionManagement: React.FC<SimplePermissionManagementProps> = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('users');
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10
  });

  // 角色配置
  const roleConfig = {
    super_admin: { label: '超级管理员', color: 'red' },
    admin: { label: '管理员', color: 'orange' },
    user: { label: '普通用户', color: 'blue' },
    sales: { label: '销售', color: 'green' },
    developer: { label: '开发者', color: 'purple' }
  };

  // 获取用户数据
  const fetchUsers = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockUsers: User[] = [
        {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          realName: '系统管理员',
          role: 'super_admin',
          status: 'active',
          permissions: ['website:admin', 'server:admin', 'user:admin'],
          department: '技术部',
          phone: '13800138000',
          lastLogin: '2024-06-15T10:00:00Z',
          createdAt: '2024-01-01',
          updatedAt: '2024-06-15'
        },
        {
          id: 2,
          username: 'sales001',
          email: '<EMAIL>',
          realName: '张销售',
          role: 'sales',
          status: 'active',
          permissions: ['customer:admin', 'presales:admin', 'website:read'],
          department: '销售部',
          phone: '13900139000',
          lastLogin: '2024-06-15T09:30:00Z',
          createdAt: '2024-01-01',
          updatedAt: '2024-06-15'
        },
        {
          id: 3,
          username: 'dev001',
          email: '<EMAIL>',
          realName: '李开发',
          role: 'developer',
          status: 'active',
          permissions: ['website:admin', 'server:read', 'project:admin'],
          department: '技术部',
          phone: '13700137000',
          lastLogin: '2024-06-15T08:45:00Z',
          createdAt: '2024-01-01',
          updatedAt: '2024-06-15'
        }
      ];
      setUsers(mockUsers);
    } catch (error) {
      message.error('获取用户数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取角色数据
  const fetchRoles = async () => {
    try {
      const mockRoles: Role[] = [
        {
          id: 1,
          name: '超级管理员',
          code: 'super_admin',
          description: '拥有系统所有权限',
          permissions: [],
          isActive: true,
          isSystem: true,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        {
          id: 2,
          name: '销售经理',
          code: 'sales_manager',
          description: '销售部门管理权限',
          permissions: [],
          isActive: true,
          isSystem: false,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        {
          id: 3,
          name: '开发工程师',
          code: 'developer',
          description: '技术开发相关权限',
          permissions: [],
          isActive: true,
          isSystem: false,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        }
      ];
      setRoles(mockRoles);
    } catch (error) {
      message.error('获取角色数据失败');
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, [queryParams]);

  // 用户表格列
  const userColumns = [
    {
      title: '用户信息',
      dataIndex: 'username',
      key: 'username',
      width: 200,
      render: (username: string, record: User) => (
        <div className="flex items-center">
          <Avatar 
            size="small" 
            icon={<UserOutlined />} 
            src={record.avatar}
            className="mr-3"
          />
          <div>
            <div className="font-medium">{record.realName}</div>
            <div className="text-sm text-gray-500">{username}</div>
            <div className="text-xs text-gray-400">{record.email}</div>
          </div>
        </div>
      )
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      render: (role: string) => {
        const config = roleConfig[role as keyof typeof roleConfig];
        return <Tag color={config?.color}>{config?.label}</Tag>;
      }
    },
    {
      title: '部门',
      dataIndex: 'department',
      key: 'department',
      width: 100
    },
    {
      title: '权限数量',
      dataIndex: 'permissions',
      key: 'permissions',
      width: 100,
      render: (permissions: string[]) => (
        <Badge count={permissions?.length || 0} showZero color="blue" />
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '正常' : '停用'}
        </Tag>
      )
    },
    {
      title: '最后登录',
      dataIndex: 'lastLogin',
      key: 'lastLogin',
      width: 150,
      render: (time: string) => time ? new Date(time).toLocaleDateString() : '-'
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: () => (
        <Space size="small">
          <Tooltip title="查看权限">
            <Button type="text" icon={<EyeOutlined />} />
          </Tooltip>
          <Tooltip title="编辑用户">
            <Button type="text" icon={<EditOutlined />} />
          </Tooltip>
          <Tooltip title="权限设置">
            <Button type="text" icon={<KeyOutlined />} />
          </Tooltip>
          <Tooltip title="删除">
            <Button type="text" danger icon={<DeleteOutlined />} />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 角色表格列
  const roleColumns = [
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '角色代码',
      dataIndex: 'code',
      key: 'code'
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (active: boolean) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Role) => (
        <Space>
          <Button type="link" icon={<EditOutlined />}>编辑</Button>
          <Button type="link" icon={<KeyOutlined />}>权限</Button>
          {!record.isSystem && (
            <Button type="link" danger icon={<DeleteOutlined />}>删除</Button>
          )}
        </Space>
      )
    }
  ];

  return (
    <div className="p-6">
      {/* 页面标题和操作 */}
      <div className="mb-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">权限管理</h1>
            <p className="text-gray-600 mt-1">管理用户权限、角色分配和资源访问控制</p>
          </div>
          <Space>
            <Button icon={<ImportOutlined />}>
              导入权限
            </Button>
            <Button icon={<ExportOutlined />}>
              导出权限
            </Button>
            <Button icon={<KeyOutlined />}>
              批量授权
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              className="bg-blue-500 hover:bg-blue-600"
            >
              新建用户
            </Button>
          </Space>
        </div>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <div className="flex items-center">
              <div className="flex-1">
                <div className="text-sm text-gray-500">总用户数</div>
                <div className="text-2xl font-bold text-gray-900">{users.length}</div>
              </div>
              <UserOutlined className="text-3xl text-blue-500" />
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div className="flex items-center">
              <div className="flex-1">
                <div className="text-sm text-gray-500">活跃用户</div>
                <div className="text-2xl font-bold text-green-600">
                  {users.filter(u => u.status === 'active').length}
                </div>
              </div>
              <CheckCircleOutlined className="text-3xl text-green-500" />
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div className="flex items-center">
              <div className="flex-1">
                <div className="text-sm text-gray-500">角色数量</div>
                <div className="text-2xl font-bold text-purple-600">{roles.length}</div>
              </div>
              <TeamOutlined className="text-3xl text-purple-500" />
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div className="flex items-center">
              <div className="flex-1">
                <div className="text-sm text-gray-500">权限规则</div>
                <div className="text-2xl font-bold text-orange-600">
                  {users.reduce((sum, user) => sum + (user.permissions?.length || 0), 0)}
                </div>
              </div>
              <SafetyCertificateOutlined className="text-3xl text-orange-500" />
            </div>
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={[
            {
              key: 'users',
              label: '用户管理',
              children: (
                <Table
                  columns={userColumns}
                  dataSource={users}
                  rowKey="id"
                  loading={loading}
                  scroll={{ x: 1000 }}
                  pagination={{
                    current: queryParams.page,
                    pageSize: queryParams.pageSize,
                    total: users.length,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total) => `共 ${total} 条记录`
                  }}
                />
              )
            },
            {
              key: 'roles',
              label: '角色管理',
              children: (
                <>
                  <div className="mb-4">
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                    >
                      新建角色
                    </Button>
                  </div>
                  <Table
                    dataSource={roles}
                    rowKey="id"
                    columns={roleColumns}
                  />
                </>
              )
            },
            {
              key: 'permissions',
              label: '权限规则',
              children: (
                <div className="text-center py-8">
                  <SafetyCertificateOutlined className="text-6xl text-gray-300 mb-4" />
                  <p className="text-gray-500">权限规则管理功能开发中...</p>
                </div>
              )
            }
          ]}
        />
      </Card>
    </div>
  );
};

export default SimplePermissionManagement;
