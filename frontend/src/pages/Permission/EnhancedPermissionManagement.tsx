/**
 * 增强的权限管理界面
 * 功能：
 * 1. 实现权限树形结构选择界面
 * 2. 添加权限搜索和筛选功能
 * 3. 实现权限批量操作界面
 * 4. 添加权限变更预览和确认机制
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Table,
  Tree,
  Input,
  Button,
  Select,
  Space,
  Modal,
  Form,
  Checkbox,
  Tag,
  Tooltip,
  Alert,
  Drawer,
  Tabs,
  Row,
  Col,
  Statistic,
  Progress,
  message,
  Popconfirm,
  Badge,
  Divider
} from 'antd';
import {
  SearchOutlined,
  UserOutlined,
  SettingOutlined,
  SecurityScanOutlined,
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  ExportOutlined,
  ImportOutlined,
  ReloadOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import type { TreeDataNode, TableColumnsType } from 'antd';
import { usePermissions } from '../../contexts/PermissionContext';
import { permissionApi } from '../../services/permission';
import { PermissionGuard } from '../../components/Permission/PermissionGuard';

const { Search } = Input;
const { Option } = Select;

// 类型定义
interface Permission {
  id: number;
  name: string;
  code: string;
  description: string;
  module: string;
  resource?: string;
  action?: string;
  createdAt: string;
}

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  status: string;
  effectivePermissions: string[];
  customPermissions: Array<{
    permission_code: string;
    granted: boolean;
  }>;
}

interface PermissionChange {
  type: 'add' | 'remove';
  permission: string;
  user: string;
}

/**
 * 增强的权限管理组件
 */
const EnhancedPermissionManagement: React.FC = () => {
  // 状态管理
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [searchText, setSearchText] = useState('');
  const [filterModule, setFilterModule] = useState<string>('');
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [pendingChanges, setPendingChanges] = useState<PermissionChange[]>([]);
  
  // 模态框状态
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  
  // 表单实例
  const [form] = Form.useForm();
  
  // 权限上下文
  const { hasPermission } = usePermissions();

  // 获取权限列表
  const fetchPermissions = useCallback(async () => {
    try {
      setLoading(true);
      const response = await permissionApi.getAllPermissions();
      if (response.success) {
        setPermissions(response.data.permissions || []);
      }
    } catch (error) {
      message.error('获取权限列表失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取用户列表
  const fetchUsers = useCallback(async () => {
    try {
      const response = await permissionApi.getUsersWithPermissions();
      if (response.success) {
        setUsers(response.data.users || []);
      }
    } catch (error) {
      message.error('获取用户列表失败');
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    fetchPermissions();
    fetchUsers();
  }, [fetchPermissions, fetchUsers]);

  // 构建权限树数据
  const permissionTreeData = useMemo(() => {
    const moduleMap = new Map<string, TreeDataNode>();
    
    permissions.forEach(permission => {
      if (!moduleMap.has(permission.module)) {
        moduleMap.set(permission.module, {
          title: permission.module,
          key: permission.module,
          children: []
        });
      }
      
      const moduleNode = moduleMap.get(permission.module)!;
      moduleNode.children!.push({
        title: (
          <div>
            <span>{permission.name}</span>
            <Tag size="small" style={{ marginLeft: 8 }}>
              {permission.code}
            </Tag>
          </div>
        ),
        key: permission.code,
        isLeaf: true
      });
    });
    
    return Array.from(moduleMap.values());
  }, [permissions]);

  // 过滤权限数据
  const filteredPermissions = useMemo(() => {
    return permissions.filter(permission => {
      const matchesSearch = !searchText || 
        permission.name.toLowerCase().includes(searchText.toLowerCase()) ||
        permission.code.toLowerCase().includes(searchText.toLowerCase()) ||
        permission.description?.toLowerCase().includes(searchText.toLowerCase());
      
      const matchesModule = !filterModule || permission.module === filterModule;
      
      return matchesSearch && matchesModule;
    });
  }, [permissions, searchText, filterModule]);

  // 获取模块列表
  const modules = useMemo(() => {
    return Array.from(new Set(permissions.map(p => p.module)));
  }, [permissions]);

  // 权限表格列定义
  const permissionColumns: TableColumnsType<Permission> = [
    {
      title: '权限名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.code}</div>
        </div>
      )
    },
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module',
      width: 100,
      render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '资源',
      dataIndex: 'resource',
      key: 'resource',
      width: 100,
      render: (text) => text ? <Tag color="green">{text}</Tag> : '-'
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 100,
      render: (text) => text ? <Tag color="orange">{text}</Tag> : '-'
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewPermissionDetail(record)}
            />
          </Tooltip>
          <PermissionGuard permissions={['system.permission.manage']}>
            <Tooltip title="编辑权限">
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEditPermission(record)}
              />
            </Tooltip>
          </PermissionGuard>
        </Space>
      )
    }
  ];

  // 用户表格列定义
  const userColumns: TableColumnsType<User> = [
    {
      title: '用户',
      key: 'user',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            <UserOutlined style={{ marginRight: 8 }} />
            {record.username}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.email}</div>
        </div>
      )
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 100,
      render: (role) => {
        const colorMap: Record<string, string> = {
          'super_admin': 'red',
          'admin': 'orange',
          'user': 'blue'
        };
        return <Tag color={colorMap[role] || 'default'}>{role}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Badge 
          status={status === 'active' ? 'success' : 'default'} 
          text={status === 'active' ? '活跃' : '非活跃'} 
        />
      )
    },
    {
      title: '权限数量',
      key: 'permissionCount',
      width: 120,
      render: (_, record) => (
        <div>
          <div>有效: {record.effectivePermissions?.length || 0}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            自定义: {record.customPermissions?.length || 0}
          </div>
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewUserPermissions(record)}
          >
            查看
          </Button>
          <PermissionGuard permissions={['system.permission.manage']}>
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditUserPermissions(record)}
            >
              编辑
            </Button>
          </PermissionGuard>
        </Space>
      )
    }
  ];

  // 处理权限选择
  const handlePermissionSelect = (selectedKeys: React.Key[]) => {
    setSelectedPermissions(selectedKeys as string[]);
  };

  // 查看权限详情
  const handleViewPermissionDetail = (permission: Permission) => {
    // 实现权限详情查看
    Modal.info({
      title: '权限详情',
      width: 600,
      content: (
        <div>
          <p><strong>权限名称:</strong> {permission.name}</p>
          <p><strong>权限代码:</strong> {permission.code}</p>
          <p><strong>所属模块:</strong> {permission.module}</p>
          <p><strong>资源类型:</strong> {permission.resource || '无'}</p>
          <p><strong>操作类型:</strong> {permission.action || '无'}</p>
          <p><strong>权限描述:</strong> {permission.description}</p>
          <p><strong>创建时间:</strong> {permission.createdAt}</p>
        </div>
      )
    });
  };

  // 编辑权限
  const handleEditPermission = (permission: Permission) => {
    form.setFieldsValue(permission);
    setEditModalVisible(true);
  };

  // 查看用户权限
  const handleViewUserPermissions = (user: User) => {
    setSelectedUser(user);
    setDetailDrawerVisible(true);
  };

  // 编辑用户权限
  const handleEditUserPermissions = (user: User) => {
    setSelectedUser(user);
    setSelectedPermissions(user.effectivePermissions || []);
    setEditModalVisible(true);
  };

  // 批量操作
  const handleBatchOperation = () => {
    setBatchModalVisible(true);
  };

  // 预览变更
  const handlePreviewChanges = () => {
    setPreviewModalVisible(true);
  };

  // 保存权限变更
  const handleSaveChanges = async () => {
    try {
      setLoading(true);
      
      if (selectedUser) {
        await permissionApi.updateUserPermissions(selectedUser.id, {
          permissions: selectedPermissions
        });
        message.success('权限更新成功');
        await fetchUsers();
        setEditModalVisible(false);
        setSelectedUser(null);
      }
    } catch (error) {
      message.error('权限更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出权限配置
  const handleExportPermissions = async () => {
    try {
      const blob = await permissionApi.exportAuditLogs({ format: 'csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `permissions_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      message.success('权限配置导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Statistic
                title="总权限数"
                value={permissions.length}
                prefix={<SecurityScanOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="用户数"
                value={users.length}
                prefix={<UserOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="模块数"
                value={modules.length}
                prefix={<SettingOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="待处理变更"
                value={pendingChanges.length}
                prefix={<ExclamationCircleOutlined />}
              />
            </Col>
          </Row>
        </div>

        <Tabs 
          defaultActiveKey="permissions"
          items={[
            {
              key: 'permissions',
              label: '权限管理',
              children: (
                <>
                  <div style={{ marginBottom: '16px' }}>
                    <Row gutter={[16, 16]}>
                      <Col span={8}>
                        <Search
                          placeholder="搜索权限名称、代码或描述"
                          value={searchText}
                          onChange={(e) => setSearchText(e.target.value)}
                          onSearch={setSearchText}
                        />
                      </Col>
                      <Col span={4}>
                        <Select
                          placeholder="选择模块"
                          value={filterModule}
                          onChange={setFilterModule}
                          allowClear
                          style={{ width: '100%' }}
                        >
                          {modules.map(module => (
                            <Option key={module} value={module}>{module}</Option>
                          ))}
                        </Select>
                      </Col>
                      <Col span={12}>
                        <Space>
                          <Button
                            icon={<ReloadOutlined />}
                            onClick={fetchPermissions}
                          >
                            刷新
                          </Button>
                          <PermissionGuard permissions={['system.permission.manage']}>
                            <Button
                              type="primary"
                              icon={<PlusOutlined />}
                              onClick={() => setEditModalVisible(true)}
                            >
                              新增权限
                            </Button>
                            <Button
                              icon={<ExportOutlined />}
                              onClick={handleExportPermissions}
                            >
                              导出配置
                            </Button>
                            <Button
                              icon={<ImportOutlined />}
                            >
                              导入配置
                            </Button>
                          </PermissionGuard>
                        </Space>
                      </Col>
                    </Row>
                  </div>

                  <Table
                    columns={permissionColumns}
                    dataSource={permissions}
                    rowKey="key"
                    pagination={{
                      showTotal: (total) => `共 ${total} 条记录`
                    }}
                  />
                </>
              )
            },
            {
              key: 'users',
              label: '用户权限',
              children: (
                <>
            <div style={{ marginBottom: '16px' }}>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchUsers}
                >
                  刷新
                </Button>
                <PermissionGuard permissions={['system.permission.manage']}>
                  <Button
                    type="primary"
                    icon={<EditOutlined />}
                    onClick={handleBatchOperation}
                  >
                    批量操作
                  </Button>
                </PermissionGuard>
              </Space>
            </div>

            <Table
              columns={userColumns}
              dataSource={users}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
                </>
              )
            },
            {
              key: 'tree',
              label: '权限树',
              children: (
                <>
            <Row gutter={24}>
              <Col span={12}>
                <Card title="权限树结构" size="small">
                  <Tree
                    checkable
                    treeData={permissionTreeData}
                    checkedKeys={selectedPermissions}
                    onCheck={handlePermissionSelect}
                    height={400}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card title="已选权限" size="small">
                  <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                    {selectedPermissions.map(permission => (
                      <Tag
                        key={permission}
                        closable
                        onClose={() => {
                          setSelectedPermissions(prev => 
                            prev.filter(p => p !== permission)
                          );
                        }}
                        style={{ marginBottom: '8px' }}
                      >
                        {permission}
                      </Tag>
                    ))}
                  </div>
                  {selectedPermissions.length > 0 && (
                    <div style={{ marginTop: '16px' }}>
                      <Button
                        type="primary"
                        onClick={handlePreviewChanges}
                      >
                        预览变更
                      </Button>
                    </div>
                  )}
                </Card>
              </Col>
            </Row>
                </>
              )
            }
          ]}
        />
      </Card>

      {/* 编辑权限模态框 */}
      <Modal
        title={selectedUser ? "编辑用户权限" : "编辑权限"}
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setSelectedUser(null);
          form.resetFields();
        }}
        onOk={handleSaveChanges}
        confirmLoading={loading}
        width={800}
      >
        <Form form={form} layout="vertical">
          {selectedUser ? (
            <div>
              <Alert
                message={`正在编辑用户 ${selectedUser.username} 的权限`}
                type="info"
                style={{ marginBottom: '16px' }}
              />
              <Tree
                checkable
                treeData={permissionTreeData}
                checkedKeys={selectedPermissions}
                onCheck={handlePermissionSelect}
                height={300}
              />
            </div>
          ) : (
            <>
              <Form.Item name="name" label="权限名称" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
              <Form.Item name="code" label="权限代码" rules={[{ required: true }]}>
                <Input />
              </Form.Item>
              <Form.Item name="module" label="所属模块" rules={[{ required: true }]}>
                <Select>
                  {modules.map(module => (
                    <Option key={module} value={module}>{module}</Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item name="description" label="权限描述">
                <Input.TextArea rows={3} />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>

      {/* 用户权限详情抽屉 */}
      <Drawer
        title="用户权限详情"
        placement="right"
        width={600}
        open={detailDrawerVisible}
        onClose={() => {
          setDetailDrawerVisible(false);
          setSelectedUser(null);
        }}
      >
        {selectedUser && (
          <div>
            <Card size="small" style={{ marginBottom: '16px' }}>
              <Statistic
                title="用户信息"
                value={selectedUser.username}
                prefix={<UserOutlined />}
              />
              <div style={{ marginTop: '8px' }}>
                <Tag color="blue">{selectedUser.role}</Tag>
                <Badge 
                  status={selectedUser.status === 'active' ? 'success' : 'default'} 
                  text={selectedUser.status} 
                />
              </div>
            </Card>

            <Card size="small" title="有效权限">
              <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                {selectedUser.effectivePermissions?.map(permission => (
                  <Tag key={permission} style={{ marginBottom: '4px' }}>
                    {permission}
                  </Tag>
                ))}
              </div>
            </Card>

            {selectedUser.customPermissions && selectedUser.customPermissions.length > 0 && (
              <Card size="small" title="自定义权限" style={{ marginTop: '16px' }}>
                <div style={{ maxHeight: '200px', overflowY: 'auto' }}>
                  {selectedUser.customPermissions.map(cp => (
                    <Tag 
                      key={cp.permission_code}
                      color={cp.granted ? 'green' : 'red'}
                      style={{ marginBottom: '4px' }}
                    >
                      {cp.granted ? '+' : '-'} {cp.permission_code}
                    </Tag>
                  ))}
                </div>
              </Card>
            )}
          </div>
        )}
      </Drawer>
    </div>
  );
};

export default EnhancedPermissionManagement;