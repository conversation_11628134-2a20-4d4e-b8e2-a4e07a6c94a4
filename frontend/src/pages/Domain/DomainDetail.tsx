import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, Typography, Button, Space } from 'antd';
import { ArrowLeftOutlined, CloudOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const DomainDetail: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/domains')}
        >
          返回
        </Button>
        <Title level={2} style={{ margin: 0 }}>
          域名详情
        </Title>
      </Space>
      
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <CloudOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={4} type="secondary">域名详情页面开发中</Title>
          <Paragraph type="secondary">
            此页面将显示域名的详细信息、WHOIS数据、DNS记录等
          </Paragraph>
        </div>
      </Card>
    </div>
  );
};

export default DomainDetail;
