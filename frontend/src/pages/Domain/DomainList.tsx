import React from 'react';
import { <PERSON>, Typography, Button, Space } from 'antd';
import { PlusOutlined, CloudOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const DomainList: React.FC = () => {
  return (
    <div>
      <Title level={2}>域名管理</Title>
      
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <CloudOutlined style={{ fontSize: 64, color: '#d9d9d9', marginBottom: 16 }} />
          <Title level={4} type="secondary">域名管理功能开发中</Title>
          <Paragraph type="secondary">
            此功能将包含域名监控、到期提醒、WHOIS查询、DNS管理等功能
          </Paragraph>
          <Space>
            <Button type="primary" icon={<PlusOutlined />}>
              新建域名
            </Button>
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default DomainList;
