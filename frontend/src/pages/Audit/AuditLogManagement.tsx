/**
 * 审计日志管理界面
 * 功能：
 * 1. 实现审计日志查询和筛选界面
 * 2. 添加日志详情查看和分析功能
 * 3. 实现日志导出和报表生成
 * 4. 添加异常访问告警和通知
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Table,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  Tag,
  Modal,
  Drawer,
  Row,
  Col,
  Statistic,
  Alert,
  Tooltip,
  Badge,
  Timeline,
  Descriptions,
  Progress,
  message,
  Tabs,
  Form,
  Switch,
  Divider
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ExportOutlined,
  EyeOutlined,
  WarningOutlined,
  SecurityScanOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  BarChartOutlined,
  ReloadOutlined,
  DownloadOutlined,
  BellOutlined
} from '@ant-design/icons';
import type { TableColumnsType, TableProps } from 'antd';
import dayjs from 'dayjs';
import { permissionApi } from '../../services/permission';
import { PermissionGuard } from '../../components/Permission/PermissionGuard';
import { usePermissions } from '../../contexts/PermissionContext';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

// 类型定义
interface AuditLog {
  id: number;
  userId: number;
  username: string;
  userRole: string;
  action: string;
  resource: string;
  resourceId?: string;
  result: 'granted' | 'denied' | 'success' | 'failure';
  details: any;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
}

interface AuditStatistics {
  actionStatistics: Array<{
    action: string;
    count: number;
    granted: number;
    denied: number;
    errors: number;
  }>;
  resultStatistics: Array<{
    result: string;
    count: number;
  }>;
  userStatistics: Array<{
    user_id: number;
    username: string;
    role: string;
    count: number;
  }>;
  timeStatistics: Array<{
    date: string;
    count: number;
  }>;
}

interface FilterParams {
  userId?: number;
  action?: string;
  resource?: string;
  result?: string;
  startDate?: string;
  endDate?: string;
  ipAddress?: string;
  page: number;
  limit: number;
}

/**
 * 审计日志管理组件
 */
const AuditLogManagement: React.FC = () => {
  // 权限检查
  const { hasPermission } = usePermissions();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [total, setTotal] = useState(0);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [statistics, setStatistics] = useState<AuditStatistics | null>(null);
  const [anomalies, setAnomalies] = useState<any[]>([]);
  
  // 界面状态
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [statsModalVisible, setStatsModalVisible] = useState(false);
  const [anomalyModalVisible, setAnomalyModalVisible] = useState(false);
  const [exportModalVisible, setExportModalVisible] = useState(false);
  
  // 筛选参数
  const [filterParams, setFilterParams] = useState<FilterParams>({
    page: 1,
    limit: 20
  });
  
  // 表单实例
  const [form] = Form.useForm();

  // 获取审计日志
  const fetchAuditLogs = useCallback(async () => {
    try {
      setLoading(true);
      const response = await permissionApi.getAuditLogs(filterParams);
      
      if (response.success) {
        setLogs(response.data.logs || []);
        setTotal(response.data.pagination?.total || 0);
      } else {
        message.error('获取审计日志失败');
      }
    } catch (error) {
      console.error('获取审计日志失败:', error);
      message.error('获取审计日志失败');
    } finally {
      setLoading(false);
    }
  }, [filterParams]);

  // 获取审计统计
  const fetchAuditStatistics = useCallback(async () => {
    try {
      const response = await permissionApi.getAuditStatistics({
        startDate: filterParams.startDate,
        endDate: filterParams.endDate,
        userId: filterParams.userId
      });
      
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('获取审计统计失败:', error);
    }
  }, [filterParams.startDate, filterParams.endDate, filterParams.userId]);

  // 检测异常访问
  const fetchAnomalies = useCallback(async () => {
    try {
      const response = await permissionApi.detectAnomalies({
        timeWindow: 3600, // 1小时
        maxFailures: 10,
        maxRequests: 1000
      });
      
      if (response.success) {
        setAnomalies(response.data.anomalies || []);
      }
    } catch (error) {
      console.error('检测异常访问失败:', error);
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    fetchAuditLogs();
    fetchAuditStatistics();
    fetchAnomalies();
  }, [fetchAuditLogs, fetchAuditStatistics, fetchAnomalies]);

  // 处理筛选参数变更
  const handleFilterChange = (key: string, value: any) => {
    setFilterParams(prev => ({
      ...prev,
      [key]: value,
      page: 1 // 重置页码
    }));
  };

  // 处理分页变更
  const handleTableChange: TableProps<AuditLog>['onChange'] = (pagination) => {
    setFilterParams(prev => ({
      ...prev,
      page: pagination.current || 1,
      limit: pagination.pageSize || 20
    }));
  };

  // 查看日志详情
  const handleViewDetail = (log: AuditLog) => {
    setSelectedLog(log);
    setDetailDrawerVisible(true);
  };

  // 导出审计日志
  const handleExport = async (format: 'json' | 'csv' = 'csv') => {
    try {
      const blob = await permissionApi.exportAuditLogs({
        format,
        ...filterParams
      });
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audit_logs_${dayjs().format('YYYY-MM-DD')}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      message.success('审计日志导出成功');
      setExportModalVisible(false);
    } catch (error) {
      message.error('导出审计日志失败');
    }
  };

  // 获取结果标签颜色
  const getResultColor = (result: string) => {
    const colorMap: Record<string, string> = {
      'granted': 'green',
      'success': 'green',
      'denied': 'red',
      'failure': 'red',
      'error': 'orange'
    };
    return colorMap[result] || 'default';
  };

  // 获取操作类型标签颜色
  const getActionColor = (action: string) => {
    const colorMap: Record<string, string> = {
      'login': 'blue',
      'logout': 'cyan',
      'permission_check': 'purple',
      'role_change': 'orange',
      'permission_change': 'red',
      'sensitive_operation': 'magenta'
    };
    return colorMap[action] || 'default';
  };

  // 表格列定义
  const columns: TableColumnsType<AuditLog> = [
    {
      title: '时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text) => (
        <div>
          <div>{dayjs(text).format('MM-DD HH:mm:ss')}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {dayjs(text).format('YYYY')}
          </div>
        </div>
      ),
      sorter: true
    },
    {
      title: '用户',
      key: 'user',
      width: 150,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            <UserOutlined style={{ marginRight: 4 }} />
            {record.username}
          </div>
          <Tag size="small" color="blue">{record.userRole}</Tag>
        </div>
      )
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 120,
      render: (text) => (
        <Tag color={getActionColor(text)}>{text}</Tag>
      ),
      filters: [
        { text: '登录', value: 'login' },
        { text: '权限检查', value: 'permission_check' },
        { text: '角色变更', value: 'role_change' },
        { text: '权限变更', value: 'permission_change' },
        { text: '敏感操作', value: 'sensitive_operation' }
      ]
    },
    {
      title: '资源',
      dataIndex: 'resource',
      key: 'resource',
      width: 120,
      render: (text, record) => (
        <div>
          <div>{text}</div>
          {record.resourceId && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              ID: {record.resourceId}
            </div>
          )}
        </div>
      )
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      width: 100,
      render: (text) => {
        const icon = text === 'granted' || text === 'success' 
          ? <CheckCircleOutlined />
          : <CloseCircleOutlined />;
        return (
          <Tag color={getResultColor(text)} icon={icon}>
            {text}
          </Tag>
        );
      },
      filters: [
        { text: '成功', value: 'success' },
        { text: '授予', value: 'granted' },
        { text: '拒绝', value: 'denied' },
        { text: '失败', value: 'failure' }
      ]
    },
    {
      title: 'IP地址',
      dataIndex: 'ipAddress',
      key: 'ipAddress',
      width: 120,
      render: (text) => (
        <code style={{ fontSize: '12px' }}>{text}</code>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 80,
      render: (_, record) => (
        <Button
          type="text"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetail(record)}
          size="small"
        >
          详情
        </Button>
      )
    }
  ];

  // 统计卡片数据
  const statisticsCards = useMemo(() => {
    if (!statistics) return [];
    
    const totalLogs = statistics.resultStatistics.reduce((sum, item) => sum + item.count, 0);
    const successCount = statistics.resultStatistics
      .filter(item => item.result === 'success' || item.result === 'granted')
      .reduce((sum, item) => sum + item.count, 0);
    const failureCount = totalLogs - successCount;
    const successRate = totalLogs > 0 ? ((successCount / totalLogs) * 100).toFixed(1) : '0';

    return [
      {
        title: '总日志数',
        value: totalLogs,
        icon: <BarChartOutlined />,
        color: '#1890ff'
      },
      {
        title: '成功操作',
        value: successCount,
        icon: <CheckCircleOutlined />,
        color: '#52c41a'
      },
      {
        title: '失败操作',
        value: failureCount,
        icon: <CloseCircleOutlined />,
        color: '#ff4d4f'
      },
      {
        title: '成功率',
        value: `${successRate}%`,
        icon: <SecurityScanOutlined />,
        color: successRate === '100.0' ? '#52c41a' : '#faad14'
      }
    ];
  }, [statistics]);

  return (
    <PermissionGuard permissions={['system.audit.view']}>
      <div style={{ padding: '24px' }}>
        <Card>
          <div style={{ marginBottom: '24px' }}>
            <Row gutter={[16, 16]}>
              {statisticsCards.map((card, index) => (
                <Col span={6} key={index}>
                  <Statistic
                    title={card.title}
                    value={card.value}
                    prefix={card.icon}
                    valueStyle={{ color: card.color }}
                  />
                </Col>
              ))}
            </Row>
          </div>

          {/* 异常告警 */}
          {anomalies.length > 0 && (
            <Alert
              message={`检测到 ${anomalies.length} 个异常访问模式`}
              description="发现可疑的访问行为，建议立即查看详情"
              type="warning"
              icon={<WarningOutlined />}
              action={
                <Button
                  size="small"
                  type="primary"
                  onClick={() => setAnomalyModalVisible(true)}
                >
                  查看详情
                </Button>
              }
              style={{ marginBottom: '16px' }}
            />
          )}

          <Tabs defaultActiveKey="logs">
            <TabPane tab="审计日志" key="logs">
              {/* 筛选工具栏 */}
              <div style={{ marginBottom: '16px' }}>
                <Row gutter={[16, 16]}>
                  <Col span={6}>
                    <Search
                      placeholder="搜索用户名、IP地址"
                      allowClear
                      onSearch={(value) => handleFilterChange('search', value)}
                    />
                  </Col>
                  <Col span={4}>
                    <Select
                      placeholder="操作类型"
                      allowClear
                      style={{ width: '100%' }}
                      onChange={(value) => handleFilterChange('action', value)}
                    >
                      <Option value="login">登录</Option>
                      <Option value="permission_check">权限检查</Option>
                      <Option value="role_change">角色变更</Option>
                      <Option value="permission_change">权限变更</Option>
                      <Option value="sensitive_operation">敏感操作</Option>
                    </Select>
                  </Col>
                  <Col span={4}>
                    <Select
                      placeholder="操作结果"
                      allowClear
                      style={{ width: '100%' }}
                      onChange={(value) => handleFilterChange('result', value)}
                    >
                      <Option value="success">成功</Option>
                      <Option value="granted">授予</Option>
                      <Option value="denied">拒绝</Option>
                      <Option value="failure">失败</Option>
                    </Select>
                  </Col>
                  <Col span={6}>
                    <RangePicker
                      style={{ width: '100%' }}
                      onChange={(dates) => {
                        if (dates) {
                          handleFilterChange('startDate', dates[0]?.toISOString());
                          handleFilterChange('endDate', dates[1]?.toISOString());
                        } else {
                          handleFilterChange('startDate', undefined);
                          handleFilterChange('endDate', undefined);
                        }
                      }}
                    />
                  </Col>
                  <Col span={4}>
                    <Space>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={fetchAuditLogs}
                      >
                        刷新
                      </Button>
                      <Button
                        icon={<ExportOutlined />}
                        onClick={() => setExportModalVisible(true)}
                      >
                        导出
                      </Button>
                      <Button
                        icon={<BarChartOutlined />}
                        onClick={() => setStatsModalVisible(true)}
                      >
                        统计
                      </Button>
                    </Space>
                  </Col>
                </Row>
              </div>

              {/* 审计日志表格 */}
              <Table
                columns={columns}
                dataSource={logs}
                rowKey="id"
                loading={loading}
                pagination={{
                  current: filterParams.page,
                  pageSize: filterParams.limit,
                  total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`
                }}
                onChange={handleTableChange}
                size="small"
              />
            </TabPane>

            <TabPane tab="实时监控" key="monitor">
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <BellOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                <div style={{ marginTop: '16px', fontSize: '16px', color: '#666' }}>
                  实时监控功能开发中
                </div>
                <div style={{ marginTop: '8px', color: '#999' }}>
                  将提供实时权限访问监控和告警功能
                </div>
              </div>
            </TabPane>
          </Tabs>
        </Card>

        {/* 日志详情抽屉 */}
        <Drawer
          title="审计日志详情"
          placement="right"
          width={600}
          open={detailDrawerVisible}
          onClose={() => {
            setDetailDrawerVisible(false);
            setSelectedLog(null);
          }}
        >
          {selectedLog && (
            <div>
              <Descriptions column={1} bordered>
                <Descriptions.Item label="日志ID">
                  {selectedLog.id}
                </Descriptions.Item>
                <Descriptions.Item label="操作时间">
                  {dayjs(selectedLog.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
                <Descriptions.Item label="用户信息">
                  <div>
                    <div>{selectedLog.username} (ID: {selectedLog.userId})</div>
                    <Tag color="blue">{selectedLog.userRole}</Tag>
                  </div>
                </Descriptions.Item>
                <Descriptions.Item label="操作类型">
                  <Tag color={getActionColor(selectedLog.action)}>
                    {selectedLog.action}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="操作资源">
                  <div>
                    <div>{selectedLog.resource}</div>
                    {selectedLog.resourceId && (
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        资源ID: {selectedLog.resourceId}
                      </div>
                    )}
                  </div>
                </Descriptions.Item>
                <Descriptions.Item label="操作结果">
                  <Tag color={getResultColor(selectedLog.result)} icon={
                    selectedLog.result === 'granted' || selectedLog.result === 'success' 
                      ? <CheckCircleOutlined />
                      : <CloseCircleOutlined />
                  }>
                    {selectedLog.result}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="IP地址">
                  <code>{selectedLog.ipAddress}</code>
                </Descriptions.Item>
                <Descriptions.Item label="用户代理">
                  <div style={{ fontSize: '12px', wordBreak: 'break-all' }}>
                    {selectedLog.userAgent}
                  </div>
                </Descriptions.Item>
                <Descriptions.Item label="详细信息">
                  <pre style={{ 
                    fontSize: '12px', 
                    background: '#f5f5f5', 
                    padding: '8px', 
                    borderRadius: '4px',
                    maxHeight: '200px',
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(selectedLog.details, null, 2)}
                  </pre>
                </Descriptions.Item>
              </Descriptions>
            </div>
          )}
        </Drawer>

        {/* 统计模态框 */}
        <Modal
          title="审计统计分析"
          open={statsModalVisible}
          onCancel={() => setStatsModalVisible(false)}
          width={800}
          footer={null}
        >
          {statistics && (
            <Tabs defaultActiveKey="actions">
              <TabPane tab="操作统计" key="actions">
                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                  {statistics.actionStatistics.map((item, index) => (
                    <div key={index} style={{ marginBottom: '16px' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                        <span>{item.action}</span>
                        <span>{item.count} 次</span>
                      </div>
                      <Progress
                        percent={item.count > 0 ? (item.granted / item.count * 100) : 0}
                        strokeColor="#52c41a"
                        trailColor="#ff4d4f"
                        size="small"
                      />
                      <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                        成功: {item.granted} | 失败: {item.denied} | 错误: {item.errors}
                      </div>
                    </div>
                  ))}
                </div>
              </TabPane>
              <TabPane tab="用户统计" key="users">
                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                  {statistics.userStatistics.map((item, index) => (
                    <div key={index} style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      padding: '8px 0',
                      borderBottom: '1px solid #f0f0f0'
                    }}>
                      <div>
                        <div>{item.username}</div>
                        <Tag size="small" color="blue">{item.role}</Tag>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <div style={{ fontWeight: 500 }}>{item.count} 次</div>
                      </div>
                    </div>
                  ))}
                </div>
              </TabPane>
              <TabPane tab="时间趋势" key="time">
                <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                  <Timeline>
                    {statistics.timeStatistics.map((item, index) => (
                      <Timeline.Item key={index}>
                        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                          <span>{item.date}</span>
                          <Badge count={item.count} style={{ backgroundColor: '#52c41a' }} />
                        </div>
                      </Timeline.Item>
                    ))}
                  </Timeline>
                </div>
              </TabPane>
            </Tabs>
          )}
        </Modal>

        {/* 异常访问模态框 */}
        <Modal
          title="异常访问检测"
          open={anomalyModalVisible}
          onCancel={() => setAnomalyModalVisible(false)}
          width={800}
          footer={null}
        >
          <div>
            {anomalies.map((anomaly, index) => (
              <Alert
                key={index}
                message={`${anomaly.type}: ${anomaly.count} 次`}
                description={
                  <div>
                    <div>用户: {anomaly.username || anomaly.ipAddress}</div>
                    <div>最后发生: {dayjs(anomaly.lastOccurrence).format('YYYY-MM-DD HH:mm:ss')}</div>
                    <div>严重程度: <Tag color={anomaly.severity === 'high' ? 'red' : 'orange'}>{anomaly.severity}</Tag></div>
                  </div>
                }
                type="warning"
                style={{ marginBottom: '16px' }}
              />
            ))}
          </div>
        </Modal>

        {/* 导出模态框 */}
        <Modal
          title="导出审计日志"
          open={exportModalVisible}
          onCancel={() => setExportModalVisible(false)}
          footer={[
            <Button key="cancel" onClick={() => setExportModalVisible(false)}>
              取消
            </Button>,
            <Button key="csv" onClick={() => handleExport('csv')}>
              导出 CSV
            </Button>,
            <Button key="json" type="primary" onClick={() => handleExport('json')}>
              导出 JSON
            </Button>
          ]}
        >
          <div>
            <p>将根据当前筛选条件导出审计日志</p>
            <div style={{ background: '#f5f5f5', padding: '12px', borderRadius: '4px' }}>
              <div>筛选条件:</div>
              <div>• 时间范围: {filterParams.startDate ? `${dayjs(filterParams.startDate).format('YYYY-MM-DD')} 至 ${dayjs(filterParams.endDate).format('YYYY-MM-DD')}` : '全部'}</div>
              <div>• 操作类型: {filterParams.action || '全部'}</div>
              <div>• 操作结果: {filterParams.result || '全部'}</div>
              <div>• 预计记录数: {total} 条</div>
            </div>
          </div>
        </Modal>
      </div>
    </PermissionGuard>
  );
};

export default AuditLogManagement;