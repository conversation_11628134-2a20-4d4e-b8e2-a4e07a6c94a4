import { theme } from 'antd';

// 主题配置
export const customTheme = {
  algorithm: theme.defaultAlgorithm,
  token: {
    // 主色调
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    
    // 中性色
    colorTextBase: '#000000',
    colorBgBase: '#ffffff',
    colorBgContainer: '#ffffff',
    colorBgElevated: '#ffffff',
    colorBgLayout: '#f5f5f5',
    colorBgSpotlight: '#ffffff',
    colorBgMask: 'rgba(0, 0, 0, 0.45)',
    
    // 边框
    colorBorder: '#d9d9d9',
    colorBorderSecondary: '#f0f0f0',
    
    // 文字
    colorText: 'rgba(0, 0, 0, 0.88)',
    colorTextSecondary: 'rgba(0, 0, 0, 0.65)',
    colorTextTertiary: 'rgba(0, 0, 0, 0.45)',
    colorTextQuaternary: 'rgba(0, 0, 0, 0.25)',
    
    // 字体
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
    fontSize: 14,
    fontSizeHeading1: 38,
    fontSizeHeading2: 30,
    fontSizeHeading3: 24,
    fontSizeHeading4: 20,
    fontSizeHeading5: 16,
    fontSizeLG: 16,
    fontSizeSM: 12,
    fontSizeXL: 20,
    
    // 圆角
    borderRadius: 6,
    borderRadiusLG: 8,
    borderRadiusSM: 4,
    borderRadiusXS: 2,
    
    // 间距
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    paddingXXS: 4,
    
    margin: 16,
    marginLG: 24,
    marginSM: 12,
    marginXS: 8,
    marginXXS: 4,
    
    // 阴影
    boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
    boxShadowSecondary: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
    boxShadowTertiary: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
    
    // 动画
    motionDurationFast: '0.1s',
    motionDurationMid: '0.2s',
    motionDurationSlow: '0.3s',
    motionEaseInOut: 'cubic-bezier(0.645, 0.045, 0.355, 1)',
    motionEaseInOutCirc: 'cubic-bezier(0.78, 0.14, 0.15, 0.86)',
    motionEaseOut: 'cubic-bezier(0.215, 0.61, 0.355, 1)',
    motionEaseInQuint: 'cubic-bezier(0.755, 0.05, 0.855, 0.06)',
    motionEaseOutQuint: 'cubic-bezier(0.23, 1, 0.32, 1)',
    
    // 控制高度
    controlHeight: 32,
    controlHeightLG: 40,
    controlHeightSM: 24,
    controlHeightXS: 16,
    
    // 线条宽度
    lineWidth: 1,
    lineWidthBold: 2,
    
    // Z-index
    zIndexBase: 0,
    zIndexPopupBase: 1000,
    zIndexModal: 1000,
    zIndexMessage: 1010,
    zIndexNotification: 1010,
    zIndexTooltip: 1070
  },
  components: {
    // 按钮组件
    Button: {
      borderRadius: 6,
      controlHeight: 36,
      controlHeightLG: 44,
      controlHeightSM: 28,
      fontWeight: 500,
      primaryShadow: '0 2px 0 rgba(5, 145, 255, 0.1)',
      dangerShadow: '0 2px 0 rgba(255, 77, 79, 0.1)',
      defaultShadow: '0 2px 0 rgba(0, 0, 0, 0.02)',
    },
    
    // 卡片组件
    Card: {
      borderRadius: 8,
      boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
      headerBg: '#fafafa',
      headerHeight: 56,
      headerHeightSM: 48,
      paddingLG: 24,
    },
    
    // 表格组件
    Table: {
      borderRadius: 8,
      headerBg: '#fafafa',
      headerColor: 'rgba(0, 0, 0, 0.88)',
      headerSortActiveBg: '#f0f0f0',
      headerSortHoverBg: '#fafafa',
      bodySortBg: '#fafafa',
      rowHoverBg: '#fafafa',
      rowSelectedBg: '#e6f7ff',
      rowSelectedHoverBg: '#bae7ff',
      rowExpandedBg: '#fbfbfb',
      cellPaddingBlock: 16,
      cellPaddingInline: 16,
      cellPaddingBlockMD: 12,
      cellPaddingInlineMD: 12,
      cellPaddingBlockSM: 8,
      cellPaddingInlineSM: 8,
    },
    
    // 表单组件
    Form: {
      labelFontSize: 14,
      labelColor: 'rgba(0, 0, 0, 0.88)',
      labelRequiredMarkColor: '#ff4d4f',
      itemMarginBottom: 24,
      verticalLabelPadding: '0 0 8px',
    },
    
    // 输入框组件
    Input: {
      borderRadius: 6,
      controlHeight: 36,
      controlHeightLG: 44,
      controlHeightSM: 28,
      paddingBlock: 8,
      paddingInline: 12,
      paddingBlockLG: 12,
      paddingInlineLG: 16,
      paddingBlockSM: 4,
      paddingInlineSM: 8,
      activeBorderColor: '#1890ff',
      hoverBorderColor: '#40a9ff',
      activeShadow: '0 0 0 2px rgba(24, 144, 255, 0.2)',
      errorActiveShadow: '0 0 0 2px rgba(255, 77, 79, 0.2)',
      warningActiveShadow: '0 0 0 2px rgba(250, 173, 20, 0.2)',
    },
    
    // 选择器组件
    Select: {
      borderRadius: 6,
      controlHeight: 36,
      controlHeightLG: 44,
      controlHeightSM: 28,
      optionSelectedBg: '#e6f7ff',
      optionActiveBg: '#f5f5f5',
      optionPadding: '8px 12px',
      optionFontSize: 14,
    },
    
    // 模态框组件
    Modal: {
      borderRadius: 8,
      headerBg: '#ffffff',
      contentBg: '#ffffff',
      titleFontSize: 16,
      titleLineHeight: 1.5,
      titleColor: 'rgba(0, 0, 0, 0.88)',
      footerBg: 'transparent',
      maskBg: 'rgba(0, 0, 0, 0.45)',
    },
    
    // 消息组件
    Message: {
      borderRadius: 6,
      contentBg: '#ffffff',
      contentPadding: '10px 16px',
      fontSize: 14,
    },
    
    // 通知组件
    Notification: {
      borderRadius: 8,
      width: 384,
      paddingInline: 24,
      paddingBlock: 16,
    },
    
    // 标签组件
    Tag: {
      borderRadius: 4,
      defaultBg: '#fafafa',
      defaultColor: 'rgba(0, 0, 0, 0.65)',
      fontSizeSM: 12,
      lineHeightSM: 1.5,
      paddingInlineSM: 8,
    },
    
    // 徽章组件
    Badge: {
      indicatorHeight: 20,
      indicatorHeightSM: 16,
      dotSize: 6,
      textFontSize: 12,
      textFontSizeSM: 10,
      textFontWeight: 'normal',
    },
    
    // 进度条组件
    Progress: {
      defaultColor: '#f5f5f5',
      remainingColor: '#f5f5f5',
      circleTextColor: 'rgba(0, 0, 0, 0.88)',
      lineBorderRadius: 100,
      circleIconFontSize: 14,
    },
    
    // 统计数值组件
    Statistic: {
      titleFontSize: 14,
      contentFontSize: 24,
      contentFontWeight: 600,
      titleColor: 'rgba(0, 0, 0, 0.45)',
      contentColor: 'rgba(0, 0, 0, 0.88)',
    },
    
    // 时间轴组件
    Timeline: {
      tailColor: '#f0f0f0',
      tailWidth: 2,
      dotBorderWidth: 2,
      dotBg: '#ffffff',
      itemPaddingBottom: 20,
    },
    
    // 步骤条组件
    Steps: {
      navArrowColor: 'rgba(0, 0, 0, 0.25)',
      navContentMaxWidth: 'auto',
      customIconSize: 32,
      customIconTop: 0,
      customIconFontSize: 24,
      iconSize: 32,
      iconSizeSM: 24,
      dotSize: 8,
      dotCurrentSize: 10,
      titleLineHeight: 1.5,
      descriptionMaxWidth: 140,
      waitIconColor: 'rgba(0, 0, 0, 0.25)',
      finishIconColor: '#1890ff',
      errorIconColor: '#ff4d4f',
    },
    
    // 标签页组件
    Tabs: {
      titleFontSize: 14,
      titleFontSizeLG: 16,
      titleFontSizeSM: 12,
      inkBarColor: '#1890ff',
      activeColor: '#1890ff',
      hoverColor: '#40a9ff',
      itemColor: 'rgba(0, 0, 0, 0.65)',
      itemSelectedColor: '#1890ff',
      itemHoverColor: '#40a9ff',
      itemActiveColor: '#096dd9',
      cardBg: '#fafafa',
      cardHeight: 40,
      cardPadding: '8px 16px',
      cardPaddingSM: '6px 12px',
      cardPaddingLG: '12px 20px',
      horizontalMargin: '0 0 0 32px',
      horizontalItemGutter: 32,
      horizontalItemMargin: '0 0 16px 0',
      horizontalItemMarginRTL: '0 0 16px 0',
      horizontalItemPadding: '12px 0',
      horizontalItemPaddingSM: '8px 0',
      horizontalItemPaddingLG: '16px 0',
      verticalItemPadding: '8px 24px',
      verticalItemMargin: '0 0 16px 0',
    }
  }
};

// 暗色主题配置
export const darkTheme = {
  algorithm: theme.darkAlgorithm,
  token: {
    ...customTheme.token,
    colorBgBase: '#141414',
    colorBgContainer: '#1f1f1f',
    colorBgElevated: '#262626',
    colorBgLayout: '#000000',
    colorText: 'rgba(255, 255, 255, 0.85)',
    colorTextSecondary: 'rgba(255, 255, 255, 0.65)',
    colorTextTertiary: 'rgba(255, 255, 255, 0.45)',
    colorTextQuaternary: 'rgba(255, 255, 255, 0.25)',
    colorBorder: '#434343',
    colorBorderSecondary: '#303030',
  },
  components: {
    ...customTheme.components,
    Card: {
      ...customTheme.components.Card,
      headerBg: '#262626',
    },
    Table: {
      ...customTheme.components.Table,
      headerBg: '#262626',
      rowHoverBg: '#262626',
      rowSelectedBg: '#111b26',
      rowSelectedHoverBg: '#0e1d2b',
    }
  }
};

// 响应式断点
export const breakpoints = {
  xs: '480px',
  sm: '576px',
  md: '768px',
  lg: '992px',
  xl: '1200px',
  xxl: '1600px'
};

// 动画配置
export const animations = {
  // 淡入淡出
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.3 }
  },
  
  // 滑入滑出
  slideIn: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3, ease: 'easeOut' }
  },
  
  // 缩放
  scale: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
    transition: { duration: 0.2, ease: 'easeOut' }
  },
  
  // 弹性动画
  bounce: {
    initial: { opacity: 0, scale: 0.3 },
    animate: { 
      opacity: 1, 
      scale: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 20
      }
    },
    exit: { opacity: 0, scale: 0.3 }
  },
  
  // 列表项动画
  listItem: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 },
    transition: { duration: 0.2 }
  },
  
  // 页面切换动画
  pageTransition: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.4, ease: 'easeInOut' }
  }
};

// 自定义CSS变量
export const cssVariables = {
  '--primary-color': '#1890ff',
  '--success-color': '#52c41a',
  '--warning-color': '#faad14',
  '--error-color': '#ff4d4f',
  '--info-color': '#1890ff',
  '--text-color': 'rgba(0, 0, 0, 0.88)',
  '--text-color-secondary': 'rgba(0, 0, 0, 0.65)',
  '--border-color': '#d9d9d9',
  '--background-color': '#ffffff',
  '--background-color-light': '#fafafa',
  '--box-shadow': '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
  '--border-radius': '6px',
  '--border-radius-lg': '8px',
  '--font-family': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  '--font-size': '14px',
  '--font-size-lg': '16px',
  '--font-size-sm': '12px',
  '--line-height': '1.5715',
  '--transition-duration': '0.3s',
  '--transition-timing-function': 'cubic-bezier(0.645, 0.045, 0.355, 1)'
};

// 工具函数：应用CSS变量
export const applyCSSVariables = () => {
  const root = document.documentElement;
  Object.entries(cssVariables).forEach(([key, value]) => {
    root.style.setProperty(key, value);
  });
};

export default customTheme;
