import React, { useState, useRef, useEffect } from 'react';
import { Table } from 'antd';
import type { TableProps } from 'antd';
import './ResizableTable.css';

interface ResizableTableProps extends TableProps<any> {
  resizable?: boolean;
}

const ResizableTable: React.FC<ResizableTableProps> = ({ 
  columns = [], 
  resizable = true, 
  ...props 
}) => {
  const [tableColumns, setTableColumns] = useState(columns);
  const resizingRef = useRef<{
    columnIndex: number;
    startX: number;
    startWidth: number;
  } | null>(null);

  useEffect(() => {
    setTableColumns(columns);
  }, [columns]);

  const handleMouseDown = (e: React.MouseEvent, columnIndex: number) => {
    if (!resizable) return;

    e.preventDefault();
    e.stopPropagation();

    const startX = e.clientX;
    const th = (e.target as HTMLElement).closest('th');
    const startWidth = th?.offsetWidth || 0;

    resizingRef.current = {
      columnIndex,
      startX,
      startWidth,
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!resizingRef.current) return;

      const { columnIndex, startX, startWidth } = resizingRef.current;
      const deltaX = e.clientX - startX;
      const newWidth = Math.max(50, startWidth + deltaX); // 最小宽度50px

      setTableColumns(prev =>
        prev.map((col, index) =>
          index === columnIndex
            ? { ...col, width: newWidth }
            : col
        )
      );
    };

    const handleMouseUp = () => {
      resizingRef.current = null;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  };



  // 为每列添加拖拽手柄
  const enhancedColumns = tableColumns.map((col, index) => ({
    ...col,
    title: (
      <div className="resizable-header">
        <span>{col.title}</span>
        {resizable && (
          <div
            className="resize-handle"
            onMouseDown={(e) => handleMouseDown(e, index)}
          />
        )}
      </div>
    ),
  }));

  return (
    <div className="resizable-table-container">
      <Table
        {...props}
        columns={enhancedColumns}
        className={`${props.className || ''} ${resizable ? 'resizable-table' : ''}`}
      />
    </div>
  );
};

export default ResizableTable;
