.resizable-table-container {
  overflow-x: auto;
}

.resizable-table .ant-table-thead > tr > th {
  position: relative;
  padding-right: 12px;
  overflow: hidden;
}

.resizable-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  width: 100%;
  min-width: 0;
}

.resizable-header > span {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.resize-handle {
  position: absolute;
  right: -2px;
  top: 0;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: transparent;
  border-right: 2px solid transparent;
  transition: border-color 0.2s;
  z-index: 10;
}

.resize-handle:hover {
  border-right-color: #1890ff;
}

.resize-handle:active {
  border-right-color: #096dd9;
}

/* 表格边框样式 */
.resizable-table.ant-table-bordered .ant-table-thead > tr > th,
.resizable-table.ant-table-bordered .ant-table-tbody > tr > td {
  border-right: 1px solid #f0f0f0;
}

/* 确保表格内容不会溢出 */
.resizable-table .ant-table-tbody > tr > td {
  padding: 8px;
  vertical-align: top;
}

.resizable-table .ant-table-tbody > tr > td > div {
  min-width: 0;
  width: 100%;
}

/* 调整表格头部样式 */
.resizable-table .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

/* 鼠标拖拽时的样式 */
.resizable-table-container.resizing {
  user-select: none;
}

.resizable-table-container.resizing * {
  cursor: col-resize !important;
}
