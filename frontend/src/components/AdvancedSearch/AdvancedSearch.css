/* 高级搜索组件样式 */

/* 搜索高亮样式 */
.search-highlight {
  background-color: #fff3cd;
  color: #856404;
  padding: 1px 3px;
  border-radius: 3px;
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 搜索输入框样式 */
.advanced-search-input {
  position: relative;
}

.advanced-search-input .ant-input-search {
  border-radius: 8px;
}

.advanced-search-input .ant-input-search-button {
  border-radius: 0 8px 8px 0;
}

/* 搜索建议下拉样式 */
.advanced-search-suggestions {
  max-height: 300px;
  overflow-y: auto;
}

.advanced-search-suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.advanced-search-suggestion-item:hover {
  background-color: #f5f5f5;
}

/* 关键词标签样式 */
.search-keywords {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.search-keyword-tag {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
}

.search-logic-tag {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border: none;
  font-size: 11px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 10px;
}

/* 搜索统计信息样式 */
.search-stats {
  margin-top: 8px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 6px;
  font-size: 12px;
  color: #666;
  border: 1px solid #e9ecef;
}

.search-stats-item {
  display: inline-flex;
  align-items: center;
  margin-right: 16px;
}

.search-stats-value {
  font-weight: 600;
  margin: 0 2px;
}

.search-stats-high {
  color: #52c41a;
}

.search-stats-medium {
  color: #faad14;
}

.search-stats-low {
  color: #ff4d4f;
}

/* 搜索设置下拉菜单样式 */
.search-settings-menu {
  min-width: 200px;
}

.search-settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
}

/* 搜索加载状态 */
.search-loading {
  position: relative;
}

.search-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 40px;
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1890ff;
  border-radius: 50%;
  animation: search-spin 1s linear infinite;
}

@keyframes search-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-keywords {
    margin-top: 4px;
  }
  
  .search-stats {
    font-size: 11px;
    padding: 6px 8px;
  }
  
  .search-stats-item {
    margin-right: 8px;
    margin-bottom: 2px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .search-highlight {
    background-color: #3c3c3c;
    color: #ffd700;
  }
  
  .search-stats {
    background: linear-gradient(135deg, #2c2c2c 0%, #3c3c3c 100%);
    color: #ccc;
    border-color: #444;
  }
  
  .advanced-search-suggestion-item:hover {
    background-color: #3c3c3c;
  }
}

/* 动画效果 */
.search-fade-in {
  animation: searchFadeIn 0.3s ease-in-out;
}

@keyframes searchFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-slide-down {
  animation: searchSlideDown 0.2s ease-out;
}

@keyframes searchSlideDown {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 搜索结果高亮表格行 */
.search-result-row {
  transition: background-color 0.2s;
}

.search-result-row:hover {
  background-color: #f0f8ff !important;
}

/* 搜索无结果状态 */
.search-no-results {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.search-no-results-icon {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 16px;
}

.search-no-results-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.search-no-results-hint {
  font-size: 14px;
  color: #bbb;
}
