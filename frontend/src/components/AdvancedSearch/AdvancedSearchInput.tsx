import React, { useState, useRef, useEffect } from 'react';
import { Input, AutoComplete, Tag, Tooltip, Space, Button, Dropdown, Menu } from 'antd';
import { SearchOutlined, ClearOutlined, SettingOutlined, InfoCircleOutlined } from '@ant-design/icons';
import type { SearchResult } from '../../hooks/useAdvancedSearch';
import './AdvancedSearch.css';

export interface AdvancedSearchInputProps {
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  placeholder?: string;
  size?: 'small' | 'middle' | 'large';
  allowClear?: boolean;
  loading?: boolean;
  searchResult?: SearchResult<any>;
  suggestions?: string[];
  onSuggestionSelect?: (suggestion: string) => void;
  showStats?: boolean;
  showSettings?: boolean;
  multiKeyword?: boolean;
  onMultiKeywordChange?: (enabled: boolean) => void;
  keywordLogic?: 'AND' | 'OR';
  onKeywordLogicChange?: (logic: 'AND' | 'OR') => void;
  style?: React.CSSProperties;
  className?: string;
}

const AdvancedSearchInput: React.FC<AdvancedSearchInputProps> = ({
  value = '',
  onChange,
  onSearch,
  placeholder = '搜索...',
  size = 'middle',
  allowClear = true,
  loading = false,
  searchResult,
  suggestions = [],
  onSuggestionSelect,
  showStats = true,
  showSettings = false,
  multiKeyword = true,
  onMultiKeywordChange,
  keywordLogic = 'AND',
  onKeywordLogicChange,
  style,
  className
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<any>(null);

  // 处理输入变化
  const handleChange = (val: string) => {
    onChange?.(val);
    setShowSuggestions(val.trim().length > 0 && suggestions.length > 0);
  };

  // 处理搜索
  const handleSearch = (val: string) => {
    onSearch?.(val);
    setShowSuggestions(false);
  };

  // 处理建议选择
  const handleSuggestionSelect = (suggestion: string) => {
    onSuggestionSelect?.(suggestion);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  // 清除搜索
  const handleClear = () => {
    onChange?.('');
    onSearch?.('');
    setShowSuggestions(false);
  };

  // 设置菜单
  const settingsMenu = (
    <Menu>
      <Menu.Item key="multiKeyword">
        <Space>
          <span>多关键词搜索</span>
          <Button
            type={multiKeyword ? 'primary' : 'default'}
            size="small"
            onClick={() => onMultiKeywordChange?.(!multiKeyword)}
          >
            {multiKeyword ? '开启' : '关闭'}
          </Button>
        </Space>
      </Menu.Item>
      {multiKeyword && (
        <Menu.Item key="keywordLogic">
          <Space>
            <span>关键词逻辑</span>
            <Button.Group size="small">
              <Button
                type={keywordLogic === 'AND' ? 'primary' : 'default'}
                onClick={() => onKeywordLogicChange?.('AND')}
              >
                AND
              </Button>
              <Button
                type={keywordLogic === 'OR' ? 'primary' : 'default'}
                onClick={() => onKeywordLogicChange?.('OR')}
              >
                OR
              </Button>
            </Button.Group>
          </Space>
        </Menu.Item>
      )}
    </Menu>
  );

  // 渲染关键词标签
  const renderKeywords = () => {
    if (!searchResult?.keywords.length) return null;

    return (
      <div style={{ marginTop: 8 }}>
        <Space size={4}>
          <span style={{ fontSize: 12, color: '#666' }}>关键词:</span>
          {searchResult.keywords.map((keyword, index) => (
            <Tag key={index} size="small" color="blue">
              {keyword}
            </Tag>
          ))}
          {searchResult.keywords.length > 1 && (
            <Tag size="small" color={keywordLogic === 'AND' ? 'green' : 'orange'}>
              {keywordLogic}
            </Tag>
          )}
        </Space>
      </div>
    );
  };

  // 渲染统计信息
  const renderStats = () => {
    if (!showStats || !searchResult || !searchResult.searchText) return null;

    return (
      <div style={{ 
        marginTop: 8, 
        padding: '8px 12px', 
        backgroundColor: '#f8f9fa', 
        borderRadius: 6,
        fontSize: 12,
        color: '#666'
      }}>
        <Space split={<span style={{ color: '#d9d9d9' }}>|</span>}>
          <span>
            找到 <strong style={{ color: '#1890ff' }}>{searchResult.filtered}</strong> 条结果
          </span>
          <span>
            共 <strong>{searchResult.total}</strong> 条记录
          </span>
          <span>
            匹配率 <strong style={{ color: searchResult.matchRate > 50 ? '#52c41a' : '#faad14' }}>
              {searchResult.matchRate}%
            </strong>
          </span>
        </Space>
      </div>
    );
  };

  // AutoComplete 选项
  const autoCompleteOptions = suggestions.map(suggestion => ({
    value: suggestion,
    label: (
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <span>{suggestion}</span>
        <Tag size="small" color="blue">建议</Tag>
      </div>
    )
  }));

  return (
    <div style={style} className={className}>
      <AutoComplete
        value={value}
        options={showSuggestions ? autoCompleteOptions : []}
        onSelect={handleSuggestionSelect}
        onSearch={handleChange}
        open={showSuggestions}
        onDropdownVisibleChange={setShowSuggestions}
        style={{ width: '100%' }}
      >
        <Input.Search
          ref={inputRef}
          placeholder={placeholder}
          size={size}
          loading={loading}
          allowClear={allowClear}
          onSearch={handleSearch}
          onChange={(e) => handleChange(e.target.value)}
          enterButton={
            <Space>
              <SearchOutlined />
              {showSettings && (
                <Dropdown overlay={settingsMenu} trigger={['click']}>
                  <SettingOutlined style={{ cursor: 'pointer' }} />
                </Dropdown>
              )}
            </Space>
          }
          suffix={
            value && (
              <Space>
                {multiKeyword && (
                  <Tooltip title={`多关键词搜索 (${keywordLogic})`}>
                    <Tag size="small" color={keywordLogic === 'AND' ? 'green' : 'orange'}>
                      {keywordLogic}
                    </Tag>
                  </Tooltip>
                )}
                <Tooltip title="清除搜索">
                  <ClearOutlined 
                    style={{ cursor: 'pointer', color: '#999' }}
                    onClick={handleClear}
                  />
                </Tooltip>
              </Space>
            )
          }
        />
      </AutoComplete>
      
      {renderKeywords()}
      {renderStats()}
    </div>
  );
};

export default AdvancedSearchInput;
