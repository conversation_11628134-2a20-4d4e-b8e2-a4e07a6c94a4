import React, { useState, useEffect } from 'react';
import {
  Select,
  Spin,
  Empty,
  Tag,
  Typography,
  Tooltip,
  Button,
  message
} from 'antd';
import {
  DesktopOutlined,
  EnvironmentOutlined,
  CloudOutlined,
  ReloadOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { ServerApi } from '../../services/server';

const { Text } = Typography;
const { Option } = Select;

interface ServerOption {
  id: number;
  name: string;
  ipAddress: string;
  location: string;
  provider: string;
  status: string;
}

interface ServerSelectProps {
  value?: number;
  onChange?: (value: number, option?: ServerOption) => void;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  style?: React.CSSProperties;
  onAddServer?: () => void; // 新增服务器回调
}

const ServerSelect: React.FC<ServerSelectProps> = ({
  value,
  onChange,
  placeholder = "请选择服务器",
  allowClear = true,
  disabled = false,
  style,
  onAddServer
}) => {
  const [servers, setServers] = useState<ServerOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchValue, setSearchValue] = useState('');

  // 获取服务器选项
  const fetchServers = async () => {
    setLoading(true);
    try {
      const response = await ServerApi.getServerOptions();
      if (response.success) {
        // 转换数据格式以匹配ServerOption接口
        const formattedServers = response.data.map((server: any) => ({
          id: server.id,
          name: server.name,
          ipAddress: server.ipAddress,
          location: server.location || '未知',
          provider: server.provider || '未知',
          status: server.status
        }));
        setServers(formattedServers);
      }
    } catch (error) {
      console.error('获取服务器选项失败:', error);
      message.error('获取服务器列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServers();
  }, []);

  // 处理选择变化
  const handleChange = (selectedValue: number) => {
    const selectedServer = servers.find(server => server.id === selectedValue);
    onChange?.(selectedValue, selectedServer);
  };

  // 过滤服务器选项
  const filteredServers = servers.filter(server => {
    if (!searchValue) return true;
    const searchLower = searchValue.toLowerCase();
    return (
      server.name.toLowerCase().includes(searchLower) ||
      server.ipAddress.toLowerCase().includes(searchLower) ||
      server.location.toLowerCase().includes(searchLower) ||
      server.provider.toLowerCase().includes(searchLower)
    );
  });

  // 获取状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'maintenance': return 'orange';
      case 'inactive': return 'red';
      default: return 'default';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '正常';
      case 'maintenance': return '维护中';
      case 'inactive': return '停用';
      default: return '未知';
    }
  };

  // 自定义下拉渲染
  const dropdownRender = (menu: React.ReactElement) => (
    <div>
      {/* 刷新按钮 */}
      <div style={{
        padding: '8px',
        borderBottom: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <span style={{ fontSize: 12, color: '#666' }}>
          {filteredServers.length} 个可用服务器
        </span>
        <Tooltip title="刷新服务器列表">
          <Button
            type="text"
            size="small"
            icon={<ReloadOutlined />}
            onClick={fetchServers}
            loading={loading}
          >
            刷新
          </Button>
        </Tooltip>
      </div>

      {menu}

      {onAddServer && (
        <div style={{ padding: '8px', borderTop: '1px solid #f0f0f0' }}>
          <Button
            type="dashed"
            icon={<PlusOutlined />}
            onClick={onAddServer}
            style={{ width: '100%' }}
          >
            新增服务器
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <Select
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      allowClear={allowClear}
      disabled={disabled}
      style={style}
      showSearch
      searchValue={searchValue}
      onSearch={setSearchValue}
      loading={loading}
      filterOption={false}
      dropdownRender={dropdownRender}
      dropdownStyle={{
        maxHeight: 400,
        overflow: 'auto'
      }}
      optionLabelProp="label"
      notFoundContent={
        loading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <Spin size="small" />
            <div style={{ marginTop: 8 }}>加载中...</div>
          </div>
        ) : filteredServers.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              searchValue ? `未找到匹配"${searchValue}"的服务器` : '暂无可用服务器'
            }
          />
        ) : null
      }
    >
      {filteredServers.map(server => (
        <Option
          key={server.id}
          value={server.id}
          label={`${server.name} (${server.ipAddress})`}
        >
          <div style={{ padding: '8px 0' }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: 8,
              marginBottom: 6
            }}>
              <DesktopOutlined style={{ color: '#1890ff', fontSize: 14 }} />
              <Text strong style={{ fontSize: 14 }}>{server.name}</Text>
              <Tag color={getStatusColor(server.status)}>
                {getStatusText(server.status)}
              </Tag>
            </div>
            <div style={{
              paddingLeft: 22,
              fontSize: 12,
              color: '#666',
              lineHeight: '18px'
            }}>
              <div style={{ marginBottom: 4 }}>
                <Text type="secondary" style={{ fontSize: 12 }}>IP: </Text>
                <span style={{ fontSize: 12, fontFamily: 'monospace' }}>{server.ipAddress}</span>
              </div>
              <div style={{ display: 'flex', gap: 16 }}>
                <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                  <EnvironmentOutlined style={{ fontSize: 12, color: '#999' }} />
                  <Text type="secondary" style={{ fontSize: 12 }}>{server.location}</Text>
                </span>
                <span style={{ display: 'flex', alignItems: 'center', gap: 4 }}>
                  <CloudOutlined style={{ fontSize: 12, color: '#999' }} />
                  <Text type="secondary" style={{ fontSize: 12 }}>{server.provider}</Text>
                </span>
              </div>
            </div>
          </div>
        </Option>
      ))}
    </Select>
  );
};

export default ServerSelect;
