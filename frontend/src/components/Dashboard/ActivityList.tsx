import React from 'react';
import { Card, List, Avatar, Tag, Button, Empty, Typography } from 'antd';
import {
  GlobalOutlined,
  ProjectOutlined,
  DatabaseOutlined,
  CloudOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const { Text } = Typography;

interface Activity {
  id: number;
  type: string;
  title: string;
  description?: string;
  time: string;
  status: string;
}

interface ActivityListProps {
  activities: Activity[];
  loading?: boolean;
  onViewAll?: () => void;
}

const ActivityList: React.FC<ActivityListProps> = ({
  activities = [],
  loading = false,
  onViewAll
}) => {
  // 获取活动类型图标
  const getActivityIcon = (type: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      website: <GlobalOutlined />,
      project: <ProjectOutlined />,
      server: <DatabaseOutlined />,
      domain: <CloudOutlined />,
    };
    return iconMap[type] || <InfoCircleOutlined />;
  };

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    const iconMap: { [key: string]: React.ReactNode } = {
      success: <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      warning: <WarningOutlined style={{ color: '#faad14' }} />,
      error: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />,
      info: <InfoCircleOutlined style={{ color: '#1890ff' }} />,
    };
    return iconMap[status] || <InfoCircleOutlined />;
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap: { [key: string]: { color: string; text: string } } = {
      success: { color: 'success', text: '成功' },
      warning: { color: 'warning', text: '警告' },
      error: { color: 'error', text: '错误' },
      info: { color: 'processing', text: '信息' },
    };
    const config = statusMap[status] || { color: 'default', text: '未知' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 格式化时间
  const formatTime = (time: string) => {
    return dayjs(time).fromNow();
  };

  return (
    <Card 
      title="最近活动" 
      loading={loading}
      extra={
        <Button type="link" onClick={onViewAll}>
          查看全部
        </Button>
      }
    >
      {activities.length === 0 ? (
        <Empty 
          description="暂无活动记录"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <List
          dataSource={activities}
          renderItem={(item) => (
            <List.Item
              actions={[getStatusTag(item.status)]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar 
                    icon={getActivityIcon(item.type)}
                    style={{ 
                      backgroundColor: getAvatarColor(item.status),
                      border: 'none'
                    }}
                  />
                }
                title={
                  <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                    <span>{item.title}</span>
                    {getStatusIcon(item.status)}
                  </div>
                }
                description={
                  <div>
                    {item.description && (
                      <Text type="secondary" style={{ display: 'block', marginBottom: 4 }}>
                        {item.description}
                      </Text>
                    )}
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {formatTime(item.time)}
                    </Text>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      )}
    </Card>
  );
};

// 获取头像背景色
const getAvatarColor = (status: string): string => {
  const colorMap: { [key: string]: string } = {
    success: '#f6ffed',
    warning: '#fff7e6',
    error: '#fff2f0',
    info: '#e6f7ff',
  };
  return colorMap[status] || '#f5f5f5';
};

export default ActivityList;
