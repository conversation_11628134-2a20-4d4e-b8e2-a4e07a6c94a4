import React from 'react';
import { Row, Col, Card, Statistic, Progress, theme } from 'antd';
import {
  GlobalOutlined,
  DatabaseOutlined,
  CloudOutlined,
  ProjectOutlined,
  TeamOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { DashboardStats } from '../../types';

interface StatsCardsProps {
  stats: DashboardStats;
  loading?: boolean;
}

const StatsCards: React.FC<StatsCardsProps> = ({ stats, loading = false }) => {
  const { token } = theme.useToken();

  // 计算百分比
  const websiteActiveRate = stats.totalWebsites > 0 
    ? Math.round((stats.activeWebsites / stats.totalWebsites) * 100) 
    : 0;
  
  const projectOnlineRate = stats.totalProjects > 0 
    ? Math.round((stats.onlineProjects / stats.totalProjects) * 100) 
    : 0;

  return (
    <>
      {/* 主要统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="网站总数"
              value={stats.totalWebsites}
              prefix={<GlobalOutlined />}
              valueStyle={{ color: token.colorPrimary }}
              suffix={
                <div style={{ fontSize: 12, color: token.colorTextSecondary }}>
                  活跃: {stats.activeWebsites}
                </div>
              }
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="在线网站"
              value={stats.activeWebsites}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: token.colorSuccess }}
              suffix={
                <div style={{ fontSize: 12, color: token.colorTextSecondary }}>
                  占比: {websiteActiveRate}%
                </div>
              }
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="服务器"
              value={stats.totalServers}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: token.colorInfo }}
              suffix={
                <div style={{ fontSize: 12, color: token.colorTextSecondary }}>
                  活跃: {stats.activeServers}
                </div>
              }
            />
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="域名"
              value={stats.totalDomains}
              prefix={<CloudOutlined />}
              valueStyle={{ color: token.colorWarning }}
              suffix={
                <div style={{ fontSize: 12, color: token.colorTextSecondary }}>
                  活跃: {stats.activeDomains}
                </div>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 详细统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={8}>
          <Card title="项目进度" loading={loading}>
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>项目完成率</span>
                <span>{stats.onlineProjects}/{stats.totalProjects}</span>
              </div>
              <Progress 
                percent={projectOnlineRate} 
                status="active"
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
              />
            </div>
            
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="总项目"
                  value={stats.totalProjects}
                  prefix={<ProjectOutlined />}
                  size="small"
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="客户数"
                  value={stats.totalCustomers}
                  prefix={<TeamOutlined />}
                  size="small"
                />
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="网站状态" loading={loading}>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    <CheckCircleOutlined style={{ color: token.colorSuccess, marginRight: 8 }} />
                    正常运行
                  </span>
                  <span style={{ fontWeight: 600, color: token.colorSuccess }}>
                    {stats.activeWebsites}
                  </span>
                </div>
              </Col>
              
              <Col span={24}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    <ExclamationCircleOutlined style={{ color: token.colorWarning, marginRight: 8 }} />
                    停用/暂停
                  </span>
                  <span style={{ fontWeight: 600, color: token.colorWarning }}>
                    {stats.inactiveWebsites}
                  </span>
                </div>
              </Col>
              
              <Col span={24}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    <WarningOutlined style={{ color: token.colorError, marginRight: 8 }} />
                    即将到期
                  </span>
                  <span style={{ fontWeight: 600, color: token.colorError }}>
                    {stats.expiringSoon}
                  </span>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="项目状态" loading={loading}>
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    <CheckCircleOutlined style={{ color: token.colorSuccess, marginRight: 8 }} />
                    已上线
                  </span>
                  <span style={{ fontWeight: 600, color: token.colorSuccess }}>
                    {stats.onlineProjects}
                  </span>
                </div>
              </Col>
              
              <Col span={24}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 12 }}>
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    <ClockCircleOutlined style={{ color: token.colorPrimary, marginRight: 8 }} />
                    开发中
                  </span>
                  <span style={{ fontWeight: 600, color: token.colorPrimary }}>
                    {stats.developmentProjects}
                  </span>
                </div>
              </Col>
              
              <Col span={24}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ display: 'flex', alignItems: 'center' }}>
                    <ExclamationCircleOutlined style={{ color: token.colorWarning, marginRight: 8 }} />
                    测试中
                  </span>
                  <span style={{ fontWeight: 600, color: token.colorWarning }}>
                    {stats.testingProjects}
                  </span>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default StatsCards;
