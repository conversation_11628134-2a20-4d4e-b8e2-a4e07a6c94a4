import React from 'react';
import { Card, Row, Col, Select, Spin } from 'antd';
import { Pie, Column, Line, Bar } from '@ant-design/charts';
import { ChartData } from '../../types';

const { Option } = Select;

interface ChartsProps {
  loading?: boolean;
  websiteStatusData?: ChartData[];
  projectProgressData?: ChartData[];
  monthlyTrendData?: any[];
  platformDistributionData?: ChartData[];
  onPeriodChange?: (period: string) => void;
}

const Charts: React.FC<ChartsProps> = ({
  loading = false,
  websiteStatusData = [],
  projectProgressData = [],
  monthlyTrendData = [],
  platformDistributionData = [],
  onPeriodChange
}) => {
  // 网站状态饼图配置
  const websiteStatusConfig = {
    data: websiteStatusData,
    angleField: 'value',
    colorField: 'name',
    radius: 0.8,
    interactions: [
      {
        type: 'element-active',
      },
    ],
    color: ['#52c41a', '#faad14', '#ff4d4f', '#d9d9d9'],
    legend: {
      position: 'bottom',
    },
  };

  // 项目进度饼图配置
  const projectProgressConfig = {
    data: projectProgressData,
    angleField: 'value',
    colorField: 'name',
    radius: 0.8,
    interactions: [
      {
        type: 'element-active',
      },
    ],
    color: ['#52c41a', '#1890ff', '#faad14', '#ff4d4f', '#d9d9d9'],
    legend: {
      position: 'bottom',
    },
  };

  // 月度趋势线图配置
  const monthlyTrendConfig = {
    data: monthlyTrendData,
    xField: 'date',
    yField: 'value',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
    color: ['#1890ff', '#52c41a'],
    legend: {
      position: 'top',
    },
    point: {
      size: 5,
      shape: 'diamond',
    },
  };

  // 平台分布柱状图配置
  const platformDistributionConfig = {
    data: platformDistributionData,
    xField: 'count',
    yField: 'name',
    seriesField: 'name',
    color: '#1890ff',
    meta: {
      name: {
        alias: '平台',
      },
      count: {
        alias: '网站数量',
      },
    },
  };

  // 处理月度趋势数据格式
  const formatTrendData = (data: any[]) => {
    const result: any[] = [];
    data.forEach(item => {
      result.push(
        { date: item.date, value: item.websites, type: '网站' },
        { date: item.date, value: item.projects, type: '项目' }
      );
    });
    return result;
  };

  return (
    <Spin spinning={loading}>
      <Row gutter={[16, 16]}>
        {/* 网站状态分布 */}
        <Col xs={24} lg={12}>
          <Card title="网站状态分布" size="small">
            <div style={{ height: 300 }}>
              <Pie {...websiteStatusConfig} />
            </div>
          </Card>
        </Col>

        {/* 项目进度分布 */}
        <Col xs={24} lg={12}>
          <Card title="项目进度分布" size="small">
            <div style={{ height: 300 }}>
              <Pie {...projectProgressConfig} />
            </div>
          </Card>
        </Col>

        {/* 月度趋势 */}
        <Col xs={24}>
          <Card 
            title="月度趋势" 
            size="small"
            extra={
              <Select
                defaultValue="7d"
                style={{ width: 120 }}
                onChange={onPeriodChange}
              >
                <Option value="7d">最近7天</Option>
                <Option value="30d">最近30天</Option>
                <Option value="90d">最近90天</Option>
              </Select>
            }
          >
            <div style={{ height: 300 }}>
              <Line {...{
                ...monthlyTrendConfig,
                data: formatTrendData(monthlyTrendData)
              }} />
            </div>
          </Card>
        </Col>

        {/* 平台分布 */}
        <Col xs={24}>
          <Card title="平台分布" size="small">
            <div style={{ height: 300 }}>
              <Bar {...platformDistributionConfig} />
            </div>
          </Card>
        </Col>
      </Row>
    </Spin>
  );
};

export default Charts;
