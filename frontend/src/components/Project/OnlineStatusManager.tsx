import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Select,
  DatePicker,
  Button,
  Space,
  Alert,
  Table,
  Checkbox,
  Typography,
  Tag,
  message,
  Divider
} from 'antd';
import { 
  CheckCircleOutlined, 
  ClockCircleOutlined, 
  ExclamationCircleOutlined,
  GlobalOutlined 
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { ProjectApi } from '../../services/project';
import { 
  Project, 
  Website, 
  UpdateProjectOnlineStatusRequest,
  BatchUpdateWebsiteOnlineDateRequest 
} from '../../types';

const { Option } = Select;
const { Text } = Typography;

interface OnlineStatusManagerProps {
  visible: boolean;
  project: Project;
  onCancel: () => void;
  onSuccess: () => void;
}

const OnlineStatusManager: React.FC<OnlineStatusManagerProps> = ({
  visible,
  project,
  onCancel,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [websites, setWebsites] = useState<Website[]>([]);
  const [selectedWebsites, setSelectedWebsites] = useState<number[]>([]);
  const [websitesLoading, setWebsitesLoading] = useState(false);

  // 获取项目关联的网站
  const fetchProjectWebsites = async () => {
    if (!project.id) return;
    
    setWebsitesLoading(true);
    try {
      const response = await ProjectApi.getProjectWebsites(project.id);
      if (response.success) {
        setWebsites(response.data || []);
        // 默认选择所有未上线的网站
        const unOnlineWebsites = (response.data || [])
          .filter((site: Website) => !site.onlineDate)
          .map((site: Website) => site.id);
        setSelectedWebsites(unOnlineWebsites);
      }
    } catch (error) {
      console.error('获取项目网站失败:', error);
    } finally {
      setWebsitesLoading(false);
    }
  };

  useEffect(() => {
    if (visible && project.id) {
      // 初始化表单
      form.setFieldsValue({
        onlineStatus: project.onlineStatus,
        onlineDate: project.onlineDate ? dayjs(project.onlineDate) : undefined
      });
      
      fetchProjectWebsites();
    }
  }, [visible, project, form]);

  // 状态选项
  const statusOptions = [
    { value: 'planning', label: '规划中', color: 'default' },
    { value: 'development', label: '开发中', color: 'processing' },
    { value: 'testing', label: '测试中', color: 'warning' },
    { value: 'online', label: '已上线', color: 'success' },
    { value: 'suspended', label: '已暂停', color: 'error' }
  ];

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const option = statusOptions.find(opt => opt.value === status);
    return option ? (
      <Tag color={option.color} icon={
        status === 'online' ? <CheckCircleOutlined /> :
        status === 'suspended' ? <ExclamationCircleOutlined /> :
        <ClockCircleOutlined />
      }>
        {option.label}
      </Tag>
    ) : status;
  };

  // 网站表格列配置
  const websiteColumns = [
    {
      title: '选择',
      dataIndex: 'id',
      key: 'select',
      width: 60,
      render: (id: number, record: Website) => (
        <Checkbox
          checked={selectedWebsites.includes(id)}
          disabled={!!record.onlineDate} // 已上线的网站不能选择
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedWebsites([...selectedWebsites, id]);
            } else {
              setSelectedWebsites(selectedWebsites.filter(siteId => siteId !== id));
            }
          }}
        />
      )
    },
    {
      title: '网站信息',
      dataIndex: 'domain',
      key: 'domain',
      render: (domain: string, record: Website) => (
        <div>
          <div style={{ fontWeight: 500, marginBottom: 4 }}>
            <Space>
              <GlobalOutlined />
              {domain}
            </Space>
          </div>
          <div style={{ fontSize: 12, color: '#999' }}>
            {record.platform?.name}
          </div>
        </div>
      )
    },
    {
      title: '当前状态',
      dataIndex: 'onlineDate',
      key: 'onlineDate',
      render: (onlineDate: string) => (
        onlineDate ? (
          <Tag color="success" icon={<CheckCircleOutlined />}>
            已上线 ({dayjs(onlineDate).format('YYYY-MM-DD')})
          </Tag>
        ) : (
          <Tag color="default" icon={<ClockCircleOutlined />}>
            未上线
          </Tag>
        )
      )
    }
  ];

  // 提交处理
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 更新项目状态
      const updateData: UpdateProjectOnlineStatusRequest = {
        onlineStatus: values.onlineStatus,
        onlineDate: values.onlineDate ? values.onlineDate.format('YYYY-MM-DD') : undefined
      };

      await ProjectApi.updateOnlineStatus(project.id, updateData);

      // 如果状态变为已上线且选择了网站，批量更新网站上线时间
      if (values.onlineStatus === 'online' && values.onlineDate && selectedWebsites.length > 0) {
        const batchUpdateData: BatchUpdateWebsiteOnlineDateRequest = {
          projectId: project.id,
          onlineDate: values.onlineDate.format('YYYY-MM-DD'),
          websiteIds: selectedWebsites
        };

        await ProjectApi.batchUpdateWebsiteOnlineDate(batchUpdateData);
        message.success(`项目状态更新成功，同时更新了 ${selectedWebsites.length} 个网站的上线时间`);
      } else {
        message.success('项目状态更新成功');
      }

      onSuccess();
    } catch (error) {
      console.error('更新失败:', error);
      message.error('更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="项目上线状态管理"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
          确定更新
        </Button>
      ]}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="onlineStatus"
          label="项目状态"
          rules={[{ required: true, message: '请选择项目状态' }]}
        >
          <Select placeholder="请选择项目状态">
            {statusOptions.map(option => (
              <Option key={option.value} value={option.value}>
                <Space>
                  {getStatusTag(option.value)}
                </Space>
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="onlineDate"
          label="上线时间"
          dependencies={['onlineStatus']}
          rules={[
            ({ getFieldValue }) => ({
              required: getFieldValue('onlineStatus') === 'online',
              message: '项目已上线时必须填写上线时间'
            })
          ]}
        >
          <DatePicker 
            style={{ width: '100%' }}
            placeholder="请选择上线时间"
            disabledDate={(current) => current && current > dayjs().endOf('day')}
          />
        </Form.Item>
      </Form>

      {websites.length > 0 && (
        <>
          <Divider />
          <div style={{ marginBottom: 16 }}>
            <Text strong>关联网站管理</Text>
            <div style={{ marginTop: 8 }}>
              <Alert
                message="提示"
                description="当项目状态设置为已上线时，可以同时更新选中网站的上线时间。已上线的网站将不能被选择。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            </div>
          </div>

          <Table
            columns={websiteColumns}
            dataSource={websites}
            rowKey="id"
            loading={websitesLoading}
            pagination={false}
            size="small"
            scroll={{ y: 300 }}
          />

          {selectedWebsites.length > 0 && (
            <div style={{ marginTop: 12, padding: 12, backgroundColor: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
              <Text type="success">
                已选择 {selectedWebsites.length} 个网站，将同步更新上线时间
              </Text>
            </div>
          )}
        </>
      )}
    </Modal>
  );
};

export default OnlineStatusManager;
