import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '@/store/auth';
import { storage } from '@/services/auth';

interface PublicRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

const PublicRoute: React.FC<PublicRouteProps> = ({
  children,
  redirectTo = '/dashboard'
}) => {
  const location = useLocation();
  const { isAuthenticated, user, isLoading } = useAuthStore();

  // 正在加载时不做重定向决定
  if (isLoading) {
    return <>{children}</>;
  }

  // 严格的认证检查：store状态 + localStorage + 用户对象
  const token = storage.getToken();
  const storedUser = storage.getUser();
  const isFullyAuthenticated = isAuthenticated && user && token && storedUser;

  console.log('🔍 PublicRoute: 认证状态检查', {
    isAuthenticated,
    hasUser: !!user,
    hasToken: !!token,
    hasStoredUser: !!storedUser,
    isFullyAuthenticated,
    isLoading
  });

  // 如果完全认证，重定向到指定页面或来源页面
  if (isFullyAuthenticated) {
    const from = (location.state as any)?.from || redirectTo;
    console.log('✅ PublicRoute: 用户已认证，重定向到', from);
    return <Navigate to={from} replace />;
  }

  console.log('🔓 PublicRoute: 用户未认证，显示公共页面');
  return <>{children}</>;
};

export default PublicRoute;
