import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuthStore } from '@/store/auth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string | string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole
}) => {
  const location = useLocation();
  const {
    isAuthenticated,
    user,
    isLoading,
    refreshUser
  } = useAuthStore();



  useEffect(() => {
    // 如果已认证但没有用户信息，尝试刷新用户信息
    if (isAuthenticated && !user && !isLoading) {
      refreshUser();
    }
  }, [isAuthenticated, user, isLoading, refreshUser]);

  // 正在加载中
  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    );
  }



  // 未认证，重定向到登录页
  if (!isAuthenticated || !user) {
    return (
      <Navigate
        to="/login"
        state={{ from: location.pathname }}
        replace
      />
    );
  }

  // 检查用户状态
  if (user.status !== 'active') {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column'
      }}>
        <h2>账户已被禁用</h2>
        <p>请联系管理员激活您的账户</p>
      </div>
    );
  }

  // 检查角色权限
  if (requiredRole) {
    const hasPermission = checkRolePermission(user.role, requiredRole);
    if (!hasPermission) {
      return (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column'
        }}>
          <h2>权限不足</h2>
          <p>您没有访问此页面的权限</p>
        </div>
      );
    }
  }

  return <>{children}</>;
};

// 检查角色权限
const checkRolePermission = (
  userRole: string, 
  requiredRole: string | string[]
): boolean => {
  const roleHierarchy = {
    'super_admin': 3,
    'admin': 2,
    'user': 1,
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;

  if (Array.isArray(requiredRole)) {
    return requiredRole.some(role => {
      const requiredLevel = roleHierarchy[role as keyof typeof roleHierarchy] || 0;
      return userLevel >= requiredLevel;
    });
  }

  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
  return userLevel >= requiredLevel;
};

export default ProtectedRoute;
