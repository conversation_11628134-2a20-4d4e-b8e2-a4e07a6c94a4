import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuthStore } from '@/store/auth';
import { storage } from '@/services/auth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string | string[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole
}) => {
  const location = useLocation();
  const {
    isAuthenticated,
    user,
    isLoading,
    refreshUser
  } = useAuthStore();



  useEffect(() => {
    // 安全检查：验证localStorage中的认证信息
    const token = storage.getToken();
    const storedUser = storage.getUser();

    // 如果localStorage中没有认证信息但store认为已认证，这是不安全的状态
    if (isAuthenticated && (!token || !storedUser)) {
      console.warn('🚨 安全警告: 认证状态不一致，清除认证状态');
      // 这种情况下应该登出用户
      // 注意：这里不直接调用logout，因为可能导致无限循环
      // 让AuthInitializer处理这种情况
    }

    // 如果已认证但没有用户信息，尝试刷新用户信息
    if (isAuthenticated && !user && !isLoading && token && storedUser) {
      refreshUser();
    }
  }, [isAuthenticated, user, isLoading, refreshUser]);

  // 正在加载中
  if (isLoading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh' 
      }}>
        <Spin size="large" />
      </div>
    );
  }



  // 严格的认证检查
  const token = storage.getToken();
  const storedUser = storage.getUser();

  // 多重认证检查：store状态 + localStorage + 用户对象
  if (!isAuthenticated || !user || !token || !storedUser) {
    console.log('🔒 ProtectedRoute: 认证检查失败，重定向到登录页', {
      isAuthenticated,
      hasUser: !!user,
      hasToken: !!token,
      hasStoredUser: !!storedUser
    });

    return (
      <Navigate
        to="/login"
        state={{ from: location.pathname }}
        replace
      />
    );
  }

  // 检查用户状态
  if (user.status !== 'active') {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column'
      }}>
        <h2>账户已被禁用</h2>
        <p>请联系管理员激活您的账户</p>
      </div>
    );
  }

  // 检查角色权限
  if (requiredRole) {
    const hasPermission = checkRolePermission(user.role, requiredRole);
    if (!hasPermission) {
      return (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column'
        }}>
          <h2>权限不足</h2>
          <p>您没有访问此页面的权限</p>
        </div>
      );
    }
  }

  return <>{children}</>;
};

// 检查角色权限
const checkRolePermission = (
  userRole: string, 
  requiredRole: string | string[]
): boolean => {
  const roleHierarchy = {
    'super_admin': 3,
    'admin': 2,
    'user': 1,
  };

  const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;

  if (Array.isArray(requiredRole)) {
    return requiredRole.some(role => {
      const requiredLevel = roleHierarchy[role as keyof typeof roleHierarchy] || 0;
      return userLevel >= requiredLevel;
    });
  }

  const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;
  return userLevel >= requiredLevel;
};

export default ProtectedRoute;
