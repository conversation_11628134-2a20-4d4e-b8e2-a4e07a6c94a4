import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthStore } from '../../store/auth';
import { storage } from '../../services/auth';

const RootRedirect: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();

  // 严格的认证检查
  const token = storage.getToken();
  const storedUser = storage.getUser();

  // 多重认证检查：store状态 + localStorage + 用户对象
  const isFullyAuthenticated = isAuthenticated && user && token && storedUser;

  console.log('🔍 RootRedirect: 认证状态检查', {
    isAuthenticated,
    hasUser: !!user,
    hasToken: !!token,
    hasStoredUser: !!storedUser,
    isFullyAuthenticated
  });

  // 如果完全认证，重定向到dashboard
  if (isFullyAuthenticated) {
    console.log('✅ RootRedirect: 用户已认证，重定向到dashboard');
    return <Navigate to="/dashboard" replace />;
  }

  // 否则重定向到登录页
  console.log('🔒 RootRedirect: 用户未认证，重定向到登录页');
  return <Navigate to="/login" replace />;
};

export default RootRedirect;
