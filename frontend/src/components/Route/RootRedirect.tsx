import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthStore } from '../../store/auth';

const RootRedirect: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();

  // 如果已认证且有用户信息，重定向到dashboard
  if (isAuthenticated && user) {
    return <Navigate to="/dashboard" replace />;
  }

  // 否则重定向到登录页
  return <Navigate to="/login" replace />;
};

export default RootRedirect;
