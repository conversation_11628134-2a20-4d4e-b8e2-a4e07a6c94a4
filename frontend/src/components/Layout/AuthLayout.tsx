import React from 'react';
import { Layout, Space, Typography, theme } from 'antd';
import { GlobalOutlined } from '@ant-design/icons';

const { Content } = Layout;
const { Title, Text } = Typography;

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const { token } = theme.useToken();

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Content style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${token.colorPrimary}15 0%, ${token.colorPrimary}25 100%)`,
        padding: 24,
      }}>
        <div style={{
          width: '100%',
          maxWidth: 400,
          background: token.colorBgContainer,
          borderRadius: token.borderRadiusLG,
          padding: 32,
          boxShadow: token.boxShadowTertiary,
        }}>
          {/* Logo和标题 */}
          <div style={{
            textAlign: 'center',
            marginBottom: 32,
          }}>
            <Space direction="vertical" size="small">
              <GlobalOutlined style={{ 
                fontSize: 48, 
                color: token.colorPrimary 
              }} />
              <Title level={2} style={{ margin: 0 }}>
                WordPress站点管理
              </Title>
              <Text type="secondary">
                现代化的站点管理后台系统
              </Text>
            </Space>
          </div>

          {/* 表单内容 */}
          {children}

          {/* 底部信息 */}
          <div style={{
            textAlign: 'center',
            marginTop: 24,
            paddingTop: 24,
            borderTop: `1px solid ${token.colorBorder}`,
          }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              © 2024 WordPress站点管理系统. All rights reserved.
            </Text>
          </div>
        </div>
      </Content>
    </Layout>
  );
};

export default AuthLayout;
