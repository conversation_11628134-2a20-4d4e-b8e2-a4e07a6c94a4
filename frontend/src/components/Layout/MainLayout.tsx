import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import {
  Layout,
  Menu,
  Button,
  Avatar,
  Dropdown,
  Badge,
  Space,
  Typography,
  theme,
  Spin,
  Skeleton,
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UserOutlined,

  ProjectOutlined,
  GlobalOutlined,
  DatabaseOutlined,
  CloudOutlined,
  MonitorOutlined,
  SettingOutlined,
  BellOutlined,
  LogoutOutlined,
  ProfileOutlined,
  DownloadOutlined,
  SafetyCertificateOutlined,

  BookOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../store/auth';
import { usePermissions } from '../../contexts/PermissionContext';
import { useAppStore } from '../../store/app';
import BatchExportModal from '../Export/BatchExportModal';
import { useLoginTimeout } from '../Auth/LoginTimeoutWarning';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const MainLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { token } = theme.useToken();

  const { user, logout } = useAuthStore();
  const { collapsed, setCollapsed } = useAppStore();
  const { canAccessPage, loading: permissionsLoading, permissions, roles } = usePermissions();
  const [batchExportVisible, setBatchExportVisible] = useState(false);
  const { LoginTimeoutWarning } = useLoginTimeout();

  // 开发环境默认用户
  const isDevelopment = import.meta.env.DEV;
  const devUser = {
    id: 1,
    username: 'dev-user',
    email: '<EMAIL>',
    role: 'super_admin' as const,
    status: 'active' as const,
    realName: '开发用户',
    avatar: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  const currentUser = isDevelopment ? (user || devUser) : user;

  // 菜单项配置
  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表盘',
      permission: 'system.monitor.view', // 仪表盘需要监控权限
      children: [
        {
          key: '/dashboard',
          label: '基础仪表盘',
          permission: 'system.monitor.view',
        },
        {
          key: '/dashboard/enhanced',
          label: '增强仪表盘',
          permission: 'system.monitor.view',
        },
      ],
    },

    {
      key: '/projects',
      icon: <ProjectOutlined />,
      label: '建站管理',
      permission: 'project.list.view', // 使用正确的权限名称
    },
    {
      key: 'websites-menu',
      icon: <GlobalOutlined />,
      label: '网站管理',
      permission: 'site.list.view', // 使用正确的权限名称
      children: [
        {
          key: '/websites',
          label: '基础管理',
          permission: 'site.list.view', // 基础管理：只需要查看权限
        },
        {
          key: '/websites/enhanced',
          label: '高级管理',
          permission: 'site.website.advanced', // 高级管理：需要高级管理权限
        },
      ],
    },
    {
      key: 'servers-menu',
      icon: <DatabaseOutlined />,
      label: '服务器管理',
      permission: 'server.list.view', // 使用正确的权限名称
      children: [
        {
          key: '/servers',
          label: '服务器列表',
          permission: 'server.list.view',
        },
        {
          key: '/servers/list',
          label: '台账管理',
          permission: 'server.list.view',
        },
      ],
    },
    {
      key: '/domains',
      icon: <CloudOutlined />,
      label: '域名管理',
      permission: 'system.settings.view', // 域名管理需要系统设置权限
    },

    {
      key: '/knowledge',
      icon: <BookOutlined />,
      label: '知识库',
      permission: 'system.settings.view', // 知识库需要系统设置权限
    },

    {
      key: '/monitor',
      icon: <MonitorOutlined />,
      label: '监控中心',
      permission: 'system.monitor.view', // 使用正确的权限名称
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: '用户管理',
      permission: 'user.list.view', // 使用正确的权限名称
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      permission: 'system.settings.view', // 使用正确的权限名称
    },
  ];

  // 过滤有权限的菜单项
  const filteredMenuItems = menuItems.filter(item => {
    if (!item.permission) return true;

    // 如果是旧的权限格式（数组），使用旧的检查方式
    if (Array.isArray(item.permission)) {
      return checkPermission(currentUser?.role, item.permission);
    }

    // 使用新的权限系统（字符串格式）
    return canAccessPage(item.permission);
  }).map(item => {
    // 递归过滤子菜单
    if (item.children) {
      const filteredChildren = item.children.filter(child => {
        if (!child.permission) return true;

        if (Array.isArray(child.permission)) {
          return checkPermission(currentUser?.role, child.permission);
        }

        // 临时放宽权限检查
        if (currentUser?.role === 'admin' || currentUser?.role === 'super_admin') {
          return true;
        }

        return canAccessPage(child.permission);
      });

      return { ...item, children: filteredChildren };
    }

    return item;
  });

  // 权限检查
  function checkPermission(userRole?: string, requiredRoles?: string[]): boolean {
    if (!userRole || !requiredRoles) return true;
    
    const roleHierarchy = {
      'super_admin': 3,
      'admin': 2,
      'user': 1,
    };
    
    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    return requiredRoles.some(role => {
      const requiredLevel = roleHierarchy[role as keyof typeof roleHierarchy] || 0;
      return userLevel >= requiredLevel;
    });
  }

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: '个人资料',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '个人设置',
      onClick: () => navigate('/profile/settings'),
    },
    {
      key: 'export',
      icon: <DownloadOutlined />,
      label: '批量导出',
      onClick: () => setBatchExportVisible(true),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  // 处理登出
  async function handleLogout() {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('登出失败:', error);
    }
  }

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    // 处理父级菜单点击，导航到默认子路由
    if (key === 'websites-menu') {
      navigate('/websites');
      return;
    }
    if (key === 'servers-menu') {
      navigate('/servers');
      return;
    }

    // 只有当key是有效路径时才导航
    if (key.startsWith('/')) {
      navigate(key);
    }
  };

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    // 直接返回当前路径，让Antd自动匹配
    return [path];
  };

  const selectedKeys = getSelectedKeys();

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        style={{
          background: token.colorBgContainer,
          borderRight: `1px solid ${token.colorBorder}`,
        }}
      >
        {/* Logo */}
        <div style={{
          height: 64,
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'flex-start',
          padding: collapsed ? 0 : '0 24px',
          borderBottom: `1px solid ${token.colorBorder}`,
        }}>
          {collapsed ? (
            <GlobalOutlined style={{ fontSize: 24, color: token.colorPrimary }} />
          ) : (
            <Space>
              <GlobalOutlined style={{ fontSize: 24, color: token.colorPrimary }} />
              <Text strong style={{ fontSize: 16 }}>站点管理</Text>
            </Space>
          )}
        </div>

        {/* 菜单 */}
        {permissionsLoading ? (
          <div style={{ padding: '16px' }}>
            <Spin
              size="small"
              style={{
                display: 'block',
                textAlign: 'center',
                marginBottom: '16px'
              }}
            />
            <Skeleton
              active
              paragraph={{ rows: 8, width: ['80%', '70%', '90%', '60%', '85%', '75%', '65%', '80%'] }}
              title={false}
            />
          </div>
        ) : (
          <Menu
            mode="inline"
            selectedKeys={selectedKeys}
            items={filteredMenuItems}
            onClick={handleMenuClick}
            style={{ border: 'none' }}
          />
        )}
      </Sider>

      <Layout>
        {/* 头部 */}
        <Header style={{
          padding: '0 24px',
          background: token.colorBgContainer,
          borderBottom: `1px solid ${token.colorBorder}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          {/* 左侧 */}
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: 16 }}
            />
          </Space>

          {/* 右侧 */}
          <Space size="middle">
            {/* 通知 */}
            <Badge count={0} size="small">
              <Button
                type="text"
                icon={<BellOutlined />}
                style={{ fontSize: 16 }}
              />
            </Badge>

            {/* 用户信息 */}
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  src={currentUser?.avatar}
                />
                <Text>{currentUser?.realName || currentUser?.username}</Text>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* 内容区域 */}
        <Content style={{
          margin: 24,
          padding: 24,
          background: token.colorBgContainer,
          borderRadius: token.borderRadius,
          minHeight: 'calc(100vh - 112px)',
        }}>
          <Outlet />
        </Content>
      </Layout>

      {/* 批量导出对话框 */}
      {/* 登录超时警告 */}
      <LoginTimeoutWarning />

      <BatchExportModal
        visible={batchExportVisible}
        onCancel={() => setBatchExportVisible(false)}
      />
    </Layout>
  );
};

export default MainLayout;
