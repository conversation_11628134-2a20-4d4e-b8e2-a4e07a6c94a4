/**
 * 角色模板管理组件
 * 功能：
 * 1. 实现角色模板创建和编辑
 * 2. 添加权限模板应用功能
 * 3. 实现模板导入导出功能
 * 4. 添加模板使用统计和分析
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Tree,
  Space,
  Tag,
  Tooltip,
  Popconfirm,
  Alert,
  Select,
  Row,
  Col,
  Statistic,
  Progress,
  Upload,
  message,
  Drawer,
  Descriptions,
  Timeline
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  ExportOutlined,
  ImportOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  UploadOutlined,
  DownloadOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import type { TreeDataNode, TableColumnsType, UploadProps } from 'antd';
import { permissionApi } from '../../services/permission';

const { TextArea } = Input;
const { Option } = Select;

// 类型定义
interface RoleTemplate {
  id: number;
  name: string;
  description: string;
  permissions: string[];
  isSystem: boolean;
  createdBy: number;
  creatorUsername?: string;
  createdAt: string;
  updatedAt: string;
  usageCount?: number;
}

interface Permission {
  id: number;
  name: string;
  code: string;
  description: string;
  module: string;
}

// 组件属性
interface RoleTemplateManagerProps {
  visible: boolean;
  onClose: () => void;
}

/**
 * 角色模板管理组件
 */
const RoleTemplateManager: React.FC<RoleTemplateManagerProps> = ({
  visible,
  onClose
}) => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [templates, setTemplates] = useState<RoleTemplate[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<RoleTemplate | null>(null);
  
  // 模态框状态
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [applyModalVisible, setApplyModalVisible] = useState(false);
  const [statsModalVisible, setStatsModalVisible] = useState(false);
  
  // 表单和选择状态
  const [form] = Form.useForm();
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [selectedRole, setSelectedRole] = useState<string>('');

  // 获取角色模板列表
  const fetchTemplates = useCallback(async () => {
    try {
      setLoading(true);
      const response = await permissionApi.getRoleTemplates({ includeSystem: true });
      if (response.success) {
        setTemplates(response.data || []);
      }
    } catch (error) {
      message.error('获取角色模板失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 获取权限列表
  const fetchPermissions = useCallback(async () => {
    try {
      const response = await permissionApi.getAllPermissions();
      if (response.success) {
        setPermissions(response.data.permissions || []);
      }
    } catch (error) {
      message.error('获取权限列表失败');
    }
  }, []);

  // 初始化数据
  useEffect(() => {
    if (visible) {
      fetchTemplates();
      fetchPermissions();
    }
  }, [visible, fetchTemplates, fetchPermissions]);

  // 构建权限树数据
  const permissionTreeData = React.useMemo(() => {
    const moduleMap = new Map<string, TreeDataNode>();
    
    permissions.forEach(permission => {
      if (!moduleMap.has(permission.module)) {
        moduleMap.set(permission.module, {
          title: permission.module,
          key: permission.module,
          children: []
        });
      }
      
      const moduleNode = moduleMap.get(permission.module)!;
      moduleNode.children!.push({
        title: (
          <div>
            <span>{permission.name}</span>
            <Tag size="small" style={{ marginLeft: 8 }}>
              {permission.code}
            </Tag>
          </div>
        ),
        key: permission.code,
        isLeaf: true
      });
    });
    
    return Array.from(moduleMap.values());
  }, [permissions]);

  // 表格列定义
  const columns: TableColumnsType<RoleTemplate> = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            {text}
            {record.isSystem && (
              <Tag color="blue" size="small" style={{ marginLeft: 8 }}>
                系统
              </Tag>
            )}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.description}
          </div>
        </div>
      )
    },
    {
      title: '权限数量',
      key: 'permissionCount',
      width: 100,
      render: (_, record) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '16px', fontWeight: 500 }}>
            {record.permissions.length}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            个权限
          </div>
        </div>
      )
    },
    {
      title: '使用次数',
      dataIndex: 'usageCount',
      key: 'usageCount',
      width: 100,
      render: (count = 0) => (
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '16px', fontWeight: 500 }}>
            {count}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            次应用
          </div>
        </div>
      )
    },
    {
      title: '创建信息',
      key: 'creator',
      width: 150,
      render: (_, record) => (
        <div>
          <div>{record.creatorUsername || '系统'}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {new Date(record.createdAt).toLocaleDateString()}
          </div>
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewTemplate(record)}
            />
          </Tooltip>
          <Tooltip title="应用模板">
            <Button
              type="text"
              icon={<CheckCircleOutlined />}
              onClick={() => handleApplyTemplate(record)}
            />
          </Tooltip>
          <Tooltip title="复制模板">
            <Button
              type="text"
              icon={<CopyOutlined />}
              onClick={() => handleCopyTemplate(record)}
            />
          </Tooltip>
          {!record.isSystem && (
            <>
              <Tooltip title="编辑模板">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => handleEditTemplate(record)}
                />
              </Tooltip>
              <Popconfirm
                title="确定要删除这个模板吗？"
                onConfirm={() => handleDeleteTemplate(record.id)}
              >
                <Tooltip title="删除模板">
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                  />
                </Tooltip>
              </Popconfirm>
            </>
          )}
        </Space>
      )
    }
  ];

  // 处理权限选择
  const handlePermissionSelect = (selectedKeys: React.Key[]) => {
    setSelectedPermissions(selectedKeys as string[]);
  };

  // 查看模板详情
  const handleViewTemplate = (template: RoleTemplate) => {
    setSelectedTemplate(template);
    setDetailDrawerVisible(true);
  };

  // 编辑模板
  const handleEditTemplate = (template: RoleTemplate) => {
    setSelectedTemplate(template);
    form.setFieldsValue({
      name: template.name,
      description: template.description
    });
    setSelectedPermissions(template.permissions);
    setEditModalVisible(true);
  };

  // 复制模板
  const handleCopyTemplate = (template: RoleTemplate) => {
    form.setFieldsValue({
      name: `${template.name} - 副本`,
      description: template.description
    });
    setSelectedPermissions(template.permissions);
    setSelectedTemplate(null);
    setEditModalVisible(true);
  };

  // 应用模板
  const handleApplyTemplate = (template: RoleTemplate) => {
    setSelectedTemplate(template);
    setApplyModalVisible(true);
  };

  // 删除模板
  const handleDeleteTemplate = async (templateId: number) => {
    try {
      // 这里应该调用删除API
      message.success('模板删除成功');
      await fetchTemplates();
    } catch (error) {
      message.error('删除模板失败');
    }
  };

  // 保存模板
  const handleSaveTemplate = async () => {
    try {
      const values = await form.validateFields();
      
      const templateData = {
        name: values.name,
        description: values.description,
        permissions: selectedPermissions,
        isSystem: false
      };

      if (selectedTemplate) {
        // 更新模板
        message.success('模板更新成功');
      } else {
        // 创建模板
        await permissionApi.createRoleTemplate(templateData);
        message.success('模板创建成功');
      }

      setEditModalVisible(false);
      setSelectedTemplate(null);
      form.resetFields();
      setSelectedPermissions([]);
      await fetchTemplates();
      
    } catch (error) {
      message.error('保存模板失败');
    }
  };

  // 应用模板到角色
  const handleApplyToRole = async () => {
    if (!selectedTemplate || !selectedRole) return;

    try {
      await permissionApi.applyRoleTemplate(selectedRole, selectedTemplate.id);
      message.success(`模板已应用到角色 ${selectedRole}`);
      setApplyModalVisible(false);
      setSelectedRole('');
    } catch (error) {
      message.error('应用模板失败');
    }
  };

  // 导出模板
  const handleExportTemplate = (template: RoleTemplate) => {
    const exportData = {
      name: template.name,
      description: template.description,
      permissions: template.permissions,
      exportTime: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `role_template_${template.name}.json`;
    document.body.appendChild(a);
    a.click();
    URL.revokeObjectURL(url);
    document.body.removeChild(a);
    
    message.success('模板导出成功');
  };

  // 导入模板
  const handleImportTemplate: UploadProps['customRequest'] = async (options) => {
    const { file } = options;
    
    try {
      const text = await (file as File).text();
      const templateData = JSON.parse(text);
      
      // 验证模板数据格式
      if (!templateData.name || !templateData.permissions) {
        throw new Error('模板格式不正确');
      }

      await permissionApi.createRoleTemplate({
        name: `${templateData.name} (导入)`,
        description: templateData.description || '',
        permissions: templateData.permissions,
        isSystem: false
      });

      message.success('模板导入成功');
      await fetchTemplates();
      
    } catch (error) {
      message.error('模板导入失败');
    }
  };

  return (
    <Modal
      title="角色模板管理"
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={null}
    >
      <div style={{ marginBottom: '16px' }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="总模板数"
              value={templates.length}
              prefix={<BarChartOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="系统模板"
              value={templates.filter(t => t.isSystem).length}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="自定义模板"
              value={templates.filter(t => !t.isSystem).length}
              prefix={<EditOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="总使用次数"
              value={templates.reduce((sum, t) => sum + (t.usageCount || 0), 0)}
              prefix={<CopyOutlined />}
            />
          </Col>
        </Row>
      </div>

      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setSelectedTemplate(null);
              form.resetFields();
              setSelectedPermissions([]);
              setEditModalVisible(true);
            }}
          >
            新建模板
          </Button>
          <Upload
            accept=".json"
            showUploadList={false}
            customRequest={handleImportTemplate}
          >
            <Button icon={<ImportOutlined />}>
              导入模板
            </Button>
          </Upload>
          <Button
            icon={<BarChartOutlined />}
            onClick={() => setStatsModalVisible(true)}
          >
            使用统计
          </Button>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={templates}
        rowKey="id"
        loading={loading}
        pagination={{
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 个模板`
        }}
      />

      {/* 编辑模板模态框 */}
      <Modal
        title={selectedTemplate ? "编辑模板" : "新建模板"}
        open={editModalVisible}
        onCancel={() => {
          setEditModalVisible(false);
          setSelectedTemplate(null);
          form.resetFields();
          setSelectedPermissions([]);
        }}
        onOk={handleSaveTemplate}
        width={800}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="请输入模板名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="模板描述"
          >
            <TextArea rows={3} placeholder="请输入模板描述" />
          </Form.Item>
          <Form.Item label="权限配置">
            <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '8px' }}>
              <Tree
                checkable
                treeData={permissionTreeData}
                checkedKeys={selectedPermissions}
                onCheck={handlePermissionSelect}
                height={300}
              />
            </div>
            <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
              已选择 {selectedPermissions.length} 个权限
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 应用模板模态框 */}
      <Modal
        title="应用模板到角色"
        open={applyModalVisible}
        onCancel={() => {
          setApplyModalVisible(false);
          setSelectedRole('');
        }}
        onOk={handleApplyToRole}
      >
        {selectedTemplate && (
          <div>
            <Alert
              message={`将要应用模板: ${selectedTemplate.name}`}
              description={`包含 ${selectedTemplate.permissions.length} 个权限`}
              type="info"
              style={{ marginBottom: '16px' }}
            />
            <Form.Item label="选择角色">
              <Select
                value={selectedRole}
                onChange={setSelectedRole}
                placeholder="请选择要应用的角色"
                style={{ width: '100%' }}
              >
                <Option value="admin">管理员</Option>
                <Option value="user">普通用户</Option>
              </Select>
            </Form.Item>
          </div>
        )}
      </Modal>

      {/* 模板详情抽屉 */}
      <Drawer
        title="模板详情"
        placement="right"
        width={600}
        open={detailDrawerVisible}
        onClose={() => {
          setDetailDrawerVisible(false);
          setSelectedTemplate(null);
        }}
      >
        {selectedTemplate && (
          <div>
            <Descriptions column={1} bordered>
              <Descriptions.Item label="模板名称">
                {selectedTemplate.name}
                {selectedTemplate.isSystem && (
                  <Tag color="blue" style={{ marginLeft: 8 }}>系统模板</Tag>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="模板描述">
                {selectedTemplate.description || '无'}
              </Descriptions.Item>
              <Descriptions.Item label="权限数量">
                {selectedTemplate.permissions.length} 个
              </Descriptions.Item>
              <Descriptions.Item label="使用次数">
                {selectedTemplate.usageCount || 0} 次
              </Descriptions.Item>
              <Descriptions.Item label="创建者">
                {selectedTemplate.creatorUsername || '系统'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {new Date(selectedTemplate.createdAt).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>

            <Divider>权限列表</Divider>
            <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
              {selectedTemplate.permissions.map(permission => (
                <Tag key={permission} style={{ marginBottom: '4px' }}>
                  {permission}
                </Tag>
              ))}
            </div>

            <div style={{ marginTop: '16px' }}>
              <Space>
                <Button
                  icon={<ExportOutlined />}
                  onClick={() => handleExportTemplate(selectedTemplate)}
                >
                  导出模板
                </Button>
                <Button
                  type="primary"
                  icon={<CheckCircleOutlined />}
                  onClick={() => {
                    setDetailDrawerVisible(false);
                    handleApplyTemplate(selectedTemplate);
                  }}
                >
                  应用模板
                </Button>
              </Space>
            </div>
          </div>
        )}
      </Drawer>
    </Modal>
  );
};

export default RoleTemplateManager;