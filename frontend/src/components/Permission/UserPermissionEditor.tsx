/**
 * 用户权限编辑组件
 * 功能：
 * 1. 实现用户权限可视化编辑
 * 2. 添加角色权限继承显示
 * 3. 实现权限冲突检测和提示
 * 4. 添加权限变更历史查看
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Card,
  Tree,
  Table,
  Button,
  Space,
  Tag,
  Alert,
  Tabs,
  Row,
  Col,
  Statistic,
  Timeline,
  Modal,
  Form,
  Select,
  Input,
  Switch,
  Tooltip,
  Popover,
  Badge,
  Divider,
  message,
  Spin
} from 'antd';
import {
  UserOutlined,
  SecurityScanOutlined,
  HistoryOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  EditOutlined,
  SaveOutlined,
  UndoOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import type { TreeDataNode, TableColumnsType } from 'antd';
import { permissionApi } from '../../services/permission';

// const { TabPane } = Tabs; // 已废弃，使用 items 属性
const { Option } = Select;

// 类型定义
interface UserPermissionData {
  userId: number;
  username: string;
  role: string;
  status: string;
  rolePermissions: string[];
  customPermissions: Array<{
    permission_code: string;
    granted: boolean;
  }>;
  effectivePermissions: string[];
  deniedPermissions: string[];
  lastUpdated: string;
}

interface Permission {
  id: number;
  name: string;
  code: string;
  description: string;
  module: string;
  resource?: string;
  action?: string;
}

interface PermissionChange {
  type: 'grant' | 'revoke';
  permission: string;
  reason?: string;
}

interface PermissionConflict {
  permission: string;
  conflict: 'role_granted_custom_denied' | 'role_denied_custom_granted';
  description: string;
}

// 组件属性
interface UserPermissionEditorProps {
  userId: number;
  visible: boolean;
  onClose: () => void;
  onSave?: (changes: PermissionChange[]) => void;
}

/**
 * 用户权限编辑组件
 */
const UserPermissionEditor: React.FC<UserPermissionEditorProps> = ({
  userId,
  visible,
  onClose,
  onSave
}) => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [userPermissions, setUserPermissions] = useState<UserPermissionData | null>(null);
  const [allPermissions, setAllPermissions] = useState<Permission[]>([]);
  const [customPermissions, setCustomPermissions] = useState<Map<string, boolean>>(new Map());
  const [pendingChanges, setPendingChanges] = useState<PermissionChange[]>([]);
  const [conflicts, setConflicts] = useState<PermissionConflict[]>([]);
  const [history, setHistory] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('permissions');
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [templates, setTemplates] = useState<any[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [applyingTemplate, setApplyingTemplate] = useState(false);

  // 获取权限模板列表
  const fetchTemplates = useCallback(async () => {
    try {
      const response = await fetch('/api/v1/permission-templates', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      if (response.ok) {
        const data = await response.json();
        setTemplates(data.data || []);
      }
    } catch (error) {
      console.error('获取权限模板失败:', error);
    }
  }, []);

  // 获取用户权限数据
  const fetchUserPermissions = useCallback(async () => {
    if (!userId) return;
    
    try {
      setLoading(true);
      const response = await permissionApi.getUserPermissions(userId);
      if (response.success) {
        setUserPermissions(response.data);
        
        // 初始化自定义权限状态
        const customMap = new Map<string, boolean>();
        response.data.customPermissions.forEach(cp => {
          // 将后端返回的数字类型转换为布尔类型
          const granted = typeof cp.granted === 'number' ? Boolean(cp.granted) : cp.granted;
          customMap.set(cp.permission_code, granted);
        });
        setCustomPermissions(customMap);
      }
    } catch (error) {
      message.error('获取用户权限失败');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  // 获取所有权限列表
  const fetchAllPermissions = useCallback(async () => {
    try {
      const response = await permissionApi.getAllPermissions();
      if (response.success) {
        setAllPermissions(response.data.permissions || []);
      }
    } catch (error) {
      message.error('获取权限列表失败');
    }
  }, []);

  // 获取用户权限历史
  const fetchUserHistory = useCallback(async () => {
    if (!userId) return;
    
    try {
      const response = await permissionApi.getUserHistory(userId, { limit: 20 });
      if (response.success) {
        setHistory(response.data.logs || []);
      }
    } catch (error) {
      console.error('获取用户历史失败:', error);
    }
  }, [userId]);

  // 初始化数据
  useEffect(() => {
    if (visible && userId) {
      fetchUserPermissions();
      fetchAllPermissions();
      fetchUserHistory();
    }
  }, [visible, userId, fetchUserPermissions, fetchAllPermissions, fetchUserHistory]);

  // 检测权限覆盖情况
  const detectPermissionOverrides = useCallback(() => {
    if (!userPermissions) return;

    const newOverrides: PermissionConflict[] = [];
    const rolePermissions = new Set(userPermissions.rolePermissions);

    customPermissions.forEach((granted, permission) => {
      if (rolePermissions.has(permission) && !granted) {
        newOverrides.push({
          permission,
          conflict: 'role_granted_custom_denied',
          description: `自定义权限撤销了角色权限中的 ${permission}`
        });
      } else if (!rolePermissions.has(permission) && granted) {
        newOverrides.push({
          permission,
          conflict: 'role_denied_custom_granted',
          description: `自定义权限额外授予了 ${permission}`
        });
      }
    });

    setConflicts(newOverrides);
  }, [userPermissions, customPermissions]);

  // 监听权限变更，检测覆盖情况
  useEffect(() => {
    detectPermissionOverrides();
  }, [detectPermissionOverrides]);

  // 构建权限树数据
  const permissionTreeData = useMemo(() => {
    const moduleMap = new Map<string, TreeDataNode>();
    
    allPermissions.forEach(permission => {
      if (!moduleMap.has(permission.module)) {
        moduleMap.set(permission.module, {
          title: permission.module,
          key: permission.module,
          children: []
        });
      }
      
      const moduleNode = moduleMap.get(permission.module)!;
      const isRolePermission = userPermissions?.rolePermissions.includes(permission.code);
      const customPermission = customPermissions.get(permission.code);
      
      let titleColor = '#000';
      let titleSuffix = '';
      
      if (isRolePermission && customPermission === false) {
        titleColor = '#ff4d4f'; // 冲突：角色授予但自定义拒绝
        titleSuffix = ' (冲突)';
      } else if (!isRolePermission && customPermission === true) {
        titleColor = '#52c41a'; // 自定义授予
        titleSuffix = ' (自定义)';
      } else if (isRolePermission) {
        titleColor = '#1890ff'; // 角色权限
        titleSuffix = ' (角色)';
      }

      moduleNode.children!.push({
        title: (
          <div style={{ color: titleColor }}>
            <span>{permission.name}</span>
            <Tag size="small" style={{ marginLeft: 8 }}>
              {permission.code}
            </Tag>
            {titleSuffix && (
              <span style={{ fontSize: '12px', marginLeft: 4 }}>
                {titleSuffix}
              </span>
            )}
          </div>
        ),
        key: permission.code,
        isLeaf: true,
        disabled: false
      });
    });
    
    return Array.from(moduleMap.values());
  }, [allPermissions, userPermissions, customPermissions]);

  // 计算有效权限
  const effectivePermissions = useMemo(() => {
    if (!userPermissions) return [];
    
    const effective = new Set(userPermissions.rolePermissions);
    
    customPermissions.forEach((granted, permission) => {
      if (granted) {
        effective.add(permission);
      } else {
        effective.delete(permission);
      }
    });
    
    return Array.from(effective);
  }, [userPermissions, customPermissions]);

  // 处理权限变更
  const handlePermissionChange = (permission: string, granted: boolean | string | undefined) => {
    const newCustomPermissions = new Map(customPermissions);

    if (granted === undefined || granted === 'default') {
      // 移除自定义权限设置
      newCustomPermissions.delete(permission);
    } else {
      // 设置自定义权限
      newCustomPermissions.set(permission, granted as boolean);
    }

    setCustomPermissions(newCustomPermissions);

    // 记录变更
    const change: PermissionChange = {
      type: (granted === true) ? 'grant' : (granted === false) ? 'revoke' : 'reset',
      permission,
      reason: granted === undefined ? '恢复角色默认' : (granted ? '自定义授予' : '自定义拒绝')
    };
    
    setPendingChanges(prev => {
      const filtered = prev.filter(c => c.permission !== permission);
      return granted === undefined ? filtered : [...filtered, change];
    });
  };

  // 保存权限变更
  const handleSave = async () => {
    if (!userPermissions) return;

    try {
      setSaving(true);

      const customPermissionsList = Array.from(customPermissions.entries()).map(([code, granted]) => ({
        code,
        granted
      }));

      console.log('🔍 前端开始保存用户权限:', {
        userId: userPermissions.userId,
        customPermissionsList,
        pendingChanges
      });

      const response = await permissionApi.updateUserPermissions(userPermissions.userId, {
        permissions: {
          customPermissions: customPermissionsList
        }
      });

      console.log('✅ 前端权限保存API响应:', response);

      message.success('权限更新成功');
      onSave?.(pendingChanges);
      setPendingChanges([]);
      await fetchUserPermissions(); // 重新获取最新数据

    } catch (error) {
      console.error('❌ 前端权限保存失败:', error);
      message.error('权限更新失败');
    } finally {
      setSaving(false);
    }
  };

  // 重置变更
  const handleReset = () => {
    if (!userPermissions) return;

    const customMap = new Map<string, boolean>();
    userPermissions.customPermissions.forEach(cp => {
      // 将后端返回的数字类型转换为布尔类型
      const granted = typeof cp.granted === 'number' ? Boolean(cp.granted) : cp.granted;
      customMap.set(cp.permission_code, granted);
    });
    setCustomPermissions(customMap);
    setPendingChanges([]);
    message.info('已重置所有变更');
  };

  // 应用权限模板
  const handleApplyTemplate = async () => {
    if (!selectedTemplate) {
      message.warning('请选择要应用的权限模板');
      return;
    }

    setApplyingTemplate(true);
    try {
      const response = await fetch(`/api/v1/permission-templates/${selectedTemplate}/apply`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          targetType: 'user',
          targetId: userId
        })
      });

      if (response.ok) {
        message.success('权限模板应用成功');
        setTemplateModalVisible(false);
        setSelectedTemplate(null);
        // 重新获取用户权限数据
        await fetchUserPermissions();
      } else {
        const error = await response.json();
        message.error(error.message || '应用权限模板失败');
      }
    } catch (error) {
      console.error('应用权限模板失败:', error);
      message.error('应用权限模板失败');
    } finally {
      setApplyingTemplate(false);
    }
  };

  // 打开权限模板选择弹窗
  const handleOpenTemplateModal = () => {
    fetchTemplates();
    setTemplateModalVisible(true);
  };

  // 权限表格列定义
  const permissionColumns: TableColumnsType<Permission> = [
    {
      title: '权限',
      key: 'permission',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.code}</div>
        </div>
      )
    },
    {
      title: '角色权限',
      key: 'rolePermission',
      width: 100,
      render: (_, record) => {
        const hasRolePermission = userPermissions?.rolePermissions.includes(record.code);
        return hasRolePermission ? (
          <CheckCircleOutlined style={{ color: '#52c41a' }} />
        ) : (
          <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
        );
      }
    },
    {
      title: '自定义权限',
      key: 'customPermission',
      width: 150,
      render: (_, record) => {
        const customValue = customPermissions.get(record.code);
        return (
          <Select
            size="small"
            style={{ width: '100%' }}
            value={customValue}
            onChange={(value) => handlePermissionChange(record.code, value)}
            placeholder="默认"
            allowClear
          >
            <Option value="default">默认</Option>
            <Option value={true}>授予</Option>
            <Option value={false}>拒绝</Option>
          </Select>
        );
      }
    },
    {
      title: '最终结果',
      key: 'effective',
      width: 100,
      render: (_, record) => {
        const isEffective = effectivePermissions.includes(record.code);
        const hasConflict = conflicts.some(c => c.permission === record.code);
        
        return (
          <div>
            {isEffective ? (
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
            ) : (
              <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
            )}
            {hasConflict && (
              <Tooltip title="权限已被自定义设置覆盖">
                <ExclamationCircleOutlined
                  style={{ color: '#1890ff', marginLeft: 4 }}
                />
              </Tooltip>
            )}
          </div>
        );
      }
    }
  ];

  if (!userPermissions) {
    return (
      <Modal
        title="用户权限编辑"
        open={visible}
        onCancel={onClose}
        footer={null}
        width={1000}
      >
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
        </div>
      </Modal>
    );
  }

  return (
    <Modal
      title={
        <div>
          <UserOutlined style={{ marginRight: 8 }} />
          编辑用户权限 - {userPermissions.username}
        </div>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button onClick={handleOpenTemplateModal}>
            <FileTextOutlined />
            应用模板
          </Button>
          <Button onClick={handleReset} disabled={pendingChanges.length === 0}>
            <UndoOutlined />
            重置
          </Button>
          <Button
            type="primary"
            onClick={handleSave}
            loading={saving}
            disabled={pendingChanges.length === 0}
          >
            <SaveOutlined />
            保存变更 ({pendingChanges.length})
          </Button>
        </Space>
      }
    >
      <div style={{ marginBottom: '16px' }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="角色权限"
              value={userPermissions.rolePermissions.length}
              prefix={<SecurityScanOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="自定义权限"
              value={customPermissions.size}
              prefix={<EditOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="有效权限"
              value={effectivePermissions.length}
              prefix={<CheckCircleOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="权限覆盖"
              value={conflicts.length}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: conflicts.length > 0 ? '#1890ff' : undefined }}
            />
          </Col>
        </Row>
      </div>

      {conflicts.length > 0 && (
        <Alert
          message="权限覆盖说明"
          description={
            <div>
              <div style={{ marginBottom: '8px' }}>
                以下权限通过自定义设置进行了调整：
              </div>
              {conflicts.map((conflict, index) => (
                <div key={index} style={{ marginBottom: '4px' }}>
                  <Tag color="blue">{conflict.permission}</Tag>
                  {conflict.description}
                </div>
              ))}
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      {pendingChanges.length > 0 && (
        <Alert
          message={`有 ${pendingChanges.length} 个待保存的权限变更`}
          description={
            <div>
              {pendingChanges.map((change, index) => (
                <Tag key={index} color={change.type === 'grant' ? 'green' : 'red'}>
                  {change.type === 'grant' ? '+' : '-'} {change.permission}
                </Tag>
              ))}
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'permissions',
            label: '权限列表',
            children: (
              <Table
                columns={permissionColumns}
                dataSource={allPermissions}
                rowKey="id"
                size="small"
                pagination={{
                  pageSize: 10,
                  showSizeChanger: true,
                  showQuickJumper: true
                }}
              />
            )
          },
          {
            key: 'tree',
            label: '权限树',
            children: (
              <Row gutter={16}>
                <Col span={16}>
                  <Card title="权限树结构" size="small">
                    <div style={{ height: '400px', overflowY: 'auto' }}>
                      <Tree
                        treeData={permissionTreeData}
                        defaultExpandAll
                      />
                    </div>
                  </Card>
                </Col>
                <Col span={8}>
                  <Card title="权限说明" size="small">
                    <div style={{ fontSize: '12px' }}>
                      <div style={{ marginBottom: '8px' }}>
                        <span style={{ color: '#1890ff' }}>蓝色</span> - 角色权限
                      </div>
                      <div style={{ marginBottom: '8px' }}>
                        <span style={{ color: '#52c41a' }}>绿色</span> - 自定义授予
                      </div>
                      <div style={{ marginBottom: '8px' }}>
                        <span style={{ color: '#1890ff' }}>蓝色</span> - 权限覆盖
                      </div>
                    </div>
                  </Card>
                </Col>
              </Row>
            )
          },
          {
            key: 'history',
            label: '变更历史',
            children: (
              <Timeline>
                {history.map((item, index) => (
                  <Timeline.Item
                    key={index}
                    color={item.result === 'success' ? 'green' : 'red'}
                    dot={<HistoryOutlined />}
                  >
                    <div>
                      <div style={{ fontWeight: 500 }}>{item.action}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {item.createdAt} - {item.ipAddress}
                      </div>
                      {item.details && (
                        <div style={{ fontSize: '12px', marginTop: '4px' }}>
                          {JSON.stringify(item.details)}
                        </div>
                      )}
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            )
          }
        ]}
      />

      {/* 权限模板选择弹窗 */}
      <Modal
        title="应用权限模板"
        open={templateModalVisible}
        onCancel={() => {
          setTemplateModalVisible(false);
          setSelectedTemplate(null);
        }}
        onOk={handleApplyTemplate}
        confirmLoading={applyingTemplate}
        okText="应用模板"
        cancelText="取消"
        width={600}
      >
        <div style={{ marginBottom: '16px' }}>
          <Alert
            message="权限模板应用"
            description="选择一个权限模板应用到当前用户。应用模板将覆盖用户的自定义权限设置。"
            type="info"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        </div>

        <Form layout="vertical">
          <Form.Item label="选择权限模板" required>
            <Select
              placeholder="请选择要应用的权限模板"
              value={selectedTemplate}
              onChange={setSelectedTemplate}
              style={{ width: '100%' }}
            >
              {templates.map(template => (
                <Select.Option key={template.id} value={template.id}>
                  <div>
                    <div style={{ fontWeight: 500 }}>{template.name}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {template.description}
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {selectedTemplate && (
            <Form.Item label="模板信息">
              {(() => {
                const template = templates.find(t => t.id === selectedTemplate);
                if (!template) return null;

                return (
                  <Card size="small">
                    <div style={{ marginBottom: '8px' }}>
                      <strong>模板名称：</strong>{template.name}
                    </div>
                    <div style={{ marginBottom: '8px' }}>
                      <strong>描述：</strong>{template.description}
                    </div>
                    <div style={{ marginBottom: '8px' }}>
                      <strong>权限数量：</strong>{template.permissions?.length || 0} 个
                    </div>
                    <div>
                      <strong>创建时间：</strong>{template.created_at}
                    </div>
                  </Card>
                );
              })()}
            </Form.Item>
          )}
        </Form>
      </Modal>
    </Modal>
  );
};

export default UserPermissionEditor;