/**
 * 受保护的路由组件
 * 功能：
 * 1. 实现路由级权限保护
 * 2. 支持权限、角色检查
 * 3. 提供重定向和错误页面
 * 4. 支持路由参数权限检查
 */

import React, { ReactNode, useEffect } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { usePermissionCheck, useRoleCheck } from '../../hooks/usePermissions';
import { Result, Spin } from 'antd';
import { LockOutlined } from '@ant-design/icons';

// 受保护路由组件属性
interface ProtectedRouteProps {
  // 权限要求
  permissions?: string | string[];
  roles?: string | string[];
  requireAll?: boolean;
  
  // 路由控制
  children: ReactNode;
  redirectTo?: string;           // 权限不足时重定向路径
  loginPath?: string;           // 未登录时重定向路径
  
  // 错误处理
  showErrorPage?: boolean;      // 是否显示错误页面
  errorTitle?: string;          // 错误页面标题
  errorSubTitle?: string;       // 错误页面副标题
  
  // 加载控制
  loadingComponent?: ReactNode; // 自定义加载组件
  
  // 回调函数
  onPermissionDenied?: (location: any) => void;
  onPermissionGranted?: (location: any) => void;
}

/**
 * 受保护的路由组件
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  permissions = [],
  roles = [],
  requireAll = false,
  children,
  redirectTo = '/403',
  loginPath = '/login',
  showErrorPage = true,
  errorTitle = '权限不足',
  errorSubTitle = '您没有权限访问此页面',
  loadingComponent = <Spin size="large" style={{ display: 'block', textAlign: 'center', marginTop: '100px' }} />,
  onPermissionDenied,
  onPermissionGranted
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // 权限检查
  const permissionCheck = usePermissionCheck(permissions, { requireAll });
  const roleCheck = useRoleCheck(roles);

  // 检查用户是否已登录
  const isAuthenticated = !!localStorage.getItem('token');

  // 加载状态
  if (permissionCheck.loading || roleCheck.loading) {
    return <>{loadingComponent}</>;
  }

  // 未登录处理
  if (!isAuthenticated) {
    return <Navigate to={loginPath} state={{ from: location }} replace />;
  }

  // 计算权限结果
  const hasPermissions = permissions.length === 0 || permissionCheck.hasPermission;
  const hasRoles = roles.length === 0 || roleCheck.hasRole;
  const hasAccess = hasPermissions && hasRoles;

  // 权限检查结果回调
  useEffect(() => {
    if (hasAccess) {
      onPermissionGranted?.(location);
    } else {
      onPermissionDenied?.(location);
    }
  }, [hasAccess, location, onPermissionGranted, onPermissionDenied]);

  // 权限不足处理
  if (!hasAccess) {
    if (showErrorPage) {
      return (
        <Result
          status="403"
          title={errorTitle}
          subTitle={errorSubTitle}
          icon={<LockOutlined />}
          extra={
            <div>
              <p>需要的权限: {Array.isArray(permissions) ? permissions.join(', ') : permissions}</p>
              {roles.length > 0 && (
                <p>需要的角色: {Array.isArray(roles) ? roles.join(', ') : roles}</p>
              )}
            </div>
          }
        />
      );
    } else {
      return <Navigate to={redirectTo} state={{ from: location }} replace />;
    }
  }

  // 权限验证通过，渲染子组件
  return <>{children}</>;
};

/**
 * 需要权限的路由组件
 */
export const RequirePermissionRoute: React.FC<{
  permission: string | string[];
  children: ReactNode;
  redirectTo?: string;
}> = ({ permission, children, redirectTo }) => {
  return (
    <ProtectedRoute permissions={permission} redirectTo={redirectTo}>
      {children}
    </ProtectedRoute>
  );
};

/**
 * 需要角色的路由组件
 */
export const RequireRoleRoute: React.FC<{
  role: string | string[];
  children: ReactNode;
  redirectTo?: string;
}> = ({ role, children, redirectTo }) => {
  return (
    <ProtectedRoute roles={role} redirectTo={redirectTo}>
      {children}
    </ProtectedRoute>
  );
};

/**
 * 管理员路由组件
 */
export const AdminRoute: React.FC<{
  children: ReactNode;
  redirectTo?: string;
}> = ({ children, redirectTo }) => {
  return (
    <ProtectedRoute roles={['admin', 'super_admin']} redirectTo={redirectTo}>
      {children}
    </ProtectedRoute>
  );
};

/**
 * 超级管理员路由组件
 */
export const SuperAdminRoute: React.FC<{
  children: ReactNode;
  redirectTo?: string;
}> = ({ children, redirectTo }) => {
  return (
    <ProtectedRoute roles="super_admin" redirectTo={redirectTo}>
      {children}
    </ProtectedRoute>
  );
};

/**
 * 路由权限配置类型
 */
export interface RoutePermissionConfig {
  path: string;
  permissions?: string[];
  roles?: string[];
  requireAll?: boolean;
  redirectTo?: string;
  component: React.ComponentType<any>;
}

/**
 * 批量创建受保护路由的工具函数
 */
export const createProtectedRoutes = (configs: RoutePermissionConfig[]) => {
  return configs.map(config => ({
    path: config.path,
    element: (
      <ProtectedRoute
        permissions={config.permissions}
        roles={config.roles}
        requireAll={config.requireAll}
        redirectTo={config.redirectTo}
      >
        <config.component />
      </ProtectedRoute>
    )
  }));
};

/**
 * 路由权限守卫Hook
 */
export const useRouteGuard = (
  permissions: string[] = [],
  roles: string[] = [],
  options: {
    requireAll?: boolean;
    onAccessDenied?: () => void;
    redirectTo?: string;
  } = {}
) => {
  const { requireAll = false, onAccessDenied, redirectTo = '/403' } = options;
  const navigate = useNavigate();
  const location = useLocation();
  
  const permissionCheck = usePermissionCheck(permissions, { requireAll });
  const roleCheck = useRoleCheck(roles);

  const hasPermissions = permissions.length === 0 || permissionCheck.hasPermission;
  const hasRoles = roles.length === 0 || roleCheck.hasRole;
  const hasAccess = hasPermissions && hasRoles;

  useEffect(() => {
    if (!permissionCheck.loading && !roleCheck.loading && !hasAccess) {
      if (onAccessDenied) {
        onAccessDenied();
      } else {
        navigate(redirectTo, { state: { from: location }, replace: true });
      }
    }
  }, [hasAccess, permissionCheck.loading, roleCheck.loading, navigate, redirectTo, location, onAccessDenied]);

  return {
    hasAccess,
    loading: permissionCheck.loading || roleCheck.loading,
    hasPermissions,
    hasRoles
  };
};

/**
 * 动态路由权限检查Hook
 */
export const useDynamicRoutePermission = (
  getPermissions: (params: any) => string[],
  getRoles: (params: any) => string[] = () => []
) => {
  const location = useLocation();
  const [permissions, setPermissions] = React.useState<string[]>([]);
  const [roles, setRoles] = React.useState<string[]>([]);

  useEffect(() => {
    // 从路由参数中提取权限要求
    const pathParams = new URLSearchParams(location.search);
    const params = Object.fromEntries(pathParams.entries());
    
    setPermissions(getPermissions(params));
    setRoles(getRoles(params));
  }, [location, getPermissions, getRoles]);

  return useRouteGuard(permissions, roles);
};

export default ProtectedRoute;