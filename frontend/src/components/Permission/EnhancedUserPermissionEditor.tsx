import React, { useState, useEffect } from 'react';
import {
  Modal,
  Tabs,
  Transfer,
  Table,
  Switch,
  Button,
  Space,
  Card,
  Typography,
  message,
  Spin,
  Checkbox,
  Row,
  Col,
  Divider,
  Tag,
  Tooltip
} from 'antd';
import {
  UserOutlined,
  GlobalOutlined,
  ServerOutlined,
  SettingOutlined,
  EyeOutlined,
  EditOutlined,
  KeyOutlined,
  SafetyOutlined
} from '@ant-design/icons';
import type { TransferDirection, TransferItem } from 'antd/es/transfer';
import type { ColumnsType } from 'antd/es/table';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 权限项接口定义
interface PermissionItem extends TransferItem {
  key: string;
  title: string;
  description?: string;
  category: string;
  icon?: React.ReactNode;
  parentCode?: string;
}

// 网站权限接口定义
interface WebsitePermission {
  id: number;
  name: string;
  domain: string;
  access_granted: boolean;
  edit_granted: boolean;
  view_credentials: boolean;
  manage_ssl: boolean;
  manage_backup: boolean;
}

// 服务器权限接口定义
interface ServerPermission {
  id: number;
  name: string;
  ip: string;
  access_granted: boolean;
  edit_granted: boolean;
  connect_granted: boolean;
  view_credentials: boolean;
}

// 组件属性接口
interface EnhancedUserPermissionEditorProps {
  visible: boolean;
  userId: number | null;
  userName?: string;
  onClose: () => void;
  onSave: (permissions: any) => Promise<void>;
}

const EnhancedUserPermissionEditor: React.FC<EnhancedUserPermissionEditorProps> = ({
  visible,
  userId,
  userName,
  onClose,
  onSave
}) => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('page');
  
  // 页面权限相关状态
  const [pagePermissions, setPagePermissions] = useState<PermissionItem[]>([]);
  const [userPagePermissions, setUserPagePermissions] = useState<string[]>([]);
  const [pageTargetKeys, setPageTargetKeys] = useState<string[]>([]);
  const [pageSelectedKeys, setPageSelectedKeys] = useState<string[]>([]);
  
  // 网站权限相关状态
  const [websitePermissions, setWebsitePermissions] = useState<WebsitePermission[]>([]);
  const [selectedWebsites, setSelectedWebsites] = useState<number[]>([]);
  
  // 服务器权限相关状态
  const [serverPermissions, setServerPermissions] = useState<ServerPermission[]>([]);
  const [selectedServers, setSelectedServers] = useState<number[]>([]);

  // 获取权限图标
  const getPermissionIcon = (category: string) => {
    switch (category) {
      case 'dashboard':
        return <UserOutlined />;
      case 'website':
        return <GlobalOutlined />;
      case 'server':
        return <ServerOutlined />;
      case 'user':
        return <UserOutlined />;
      case 'system':
        return <SettingOutlined />;
      default:
        return <SettingOutlined />;
    }
  };

  // 加载用户权限数据
  const loadUserPermissions = async () => {
    if (!userId) return;
    
    setLoading(true);
    try {
      // 加载页面权限
      const pageResponse = await fetch(`/api/v1/permissions/page/all`);
      const pageData = await pageResponse.json();
      
      if (pageData.success) {
        const formattedPermissions: PermissionItem[] = pageData.data.map((item: any) => ({
          key: item.permission_code,
          title: item.permission_name,
          description: item.description,
          category: item.permission_code.split('.')[0],
          icon: getPermissionIcon(item.permission_code.split('.')[0]),
          parentCode: item.parent_code
        }));
        setPagePermissions(formattedPermissions);
      }

      // 加载用户当前页面权限
      const userPageResponse = await fetch(`/api/v1/permissions/user/${userId}/page`);
      const userPageData = await userPageResponse.json();
      
      if (userPageData.success) {
        const userPermissions = userPageData.data
          .filter((item: any) => item.granted)
          .map((item: any) => item.permission_code);
        setUserPagePermissions(userPermissions);
        setPageTargetKeys(userPermissions);
      }

      // 加载网站权限
      const websiteResponse = await fetch(`/api/v1/permissions/user/${userId}/websites`);
      const websiteData = await websiteResponse.json();
      
      if (websiteData.success) {
        setWebsitePermissions(websiteData.data);
        setSelectedWebsites(websiteData.data
          .filter((item: WebsitePermission) => item.access_granted)
          .map((item: WebsitePermission) => item.id)
        );
      }

      // 加载服务器权限
      const serverResponse = await fetch(`/api/v1/permissions/user/${userId}/servers`);
      const serverData = await serverResponse.json();
      
      if (serverData.success) {
        setServerPermissions(serverData.data);
        setSelectedServers(serverData.data
          .filter((item: ServerPermission) => item.access_granted)
          .map((item: ServerPermission) => item.id)
        );
      }

    } catch (error) {
      console.error('加载用户权限失败:', error);
      message.error('加载用户权限失败');
    } finally {
      setLoading(false);
    }
  };

  // 页面权限Transfer变更处理
  const handlePagePermissionChange = (
    newTargetKeys: string[],
    direction: TransferDirection,
    moveKeys: string[]
  ) => {
    setPageTargetKeys(newTargetKeys);
  };

  // 页面权限选择变更处理
  const handlePagePermissionSelectChange = (
    sourceSelectedKeys: string[],
    targetSelectedKeys: string[]
  ) => {
    setPageSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
  };

  // 网站权限表格列定义
  const websiteColumns: ColumnsType<WebsitePermission> = [
    {
      title: '网站名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: WebsitePermission) => (
        <Space>
          <GlobalOutlined />
          <div>
            <div>{text}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>{record.domain}</Text>
          </div>
        </Space>
      ),
    },
    {
      title: '访问权限',
      dataIndex: 'access_granted',
      key: 'access_granted',
      width: 100,
      render: (granted: boolean, record: WebsitePermission) => (
        <Switch
          checked={granted}
          onChange={(checked) => updateWebsitePermission(record.id, 'access_granted', checked)}
        />
      ),
    },
    {
      title: '编辑权限',
      dataIndex: 'edit_granted',
      key: 'edit_granted',
      width: 100,
      render: (granted: boolean, record: WebsitePermission) => (
        <Switch
          checked={granted}
          disabled={!record.access_granted}
          onChange={(checked) => updateWebsitePermission(record.id, 'edit_granted', checked)}
        />
      ),
    },
    {
      title: '查看密码',
      dataIndex: 'view_credentials',
      key: 'view_credentials',
      width: 100,
      render: (granted: boolean, record: WebsitePermission) => (
        <Tooltip title="允许查看网站登录密码">
          <Switch
            checked={granted}
            disabled={!record.access_granted}
            onChange={(checked) => updateWebsitePermission(record.id, 'view_credentials', checked)}
          />
        </Tooltip>
      ),
    },
    {
      title: 'SSL管理',
      dataIndex: 'manage_ssl',
      key: 'manage_ssl',
      width: 100,
      render: (granted: boolean, record: WebsitePermission) => (
        <Tooltip title="允许管理SSL证书">
          <Switch
            checked={granted}
            disabled={!record.access_granted}
            onChange={(checked) => updateWebsitePermission(record.id, 'manage_ssl', checked)}
          />
        </Tooltip>
      ),
    },
    {
      title: '备份管理',
      dataIndex: 'manage_backup',
      key: 'manage_backup',
      width: 100,
      render: (granted: boolean, record: WebsitePermission) => (
        <Tooltip title="允许管理网站备份">
          <Switch
            checked={granted}
            disabled={!record.access_granted}
            onChange={(checked) => updateWebsitePermission(record.id, 'manage_backup', checked)}
          />
        </Tooltip>
      ),
    },
  ];

  // 服务器权限表格列定义
  const serverColumns: ColumnsType<ServerPermission> = [
    {
      title: '服务器名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: ServerPermission) => (
        <Space>
          <ServerOutlined />
          <div>
            <div>{text}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>{record.ip}</Text>
          </div>
        </Space>
      ),
    },
    {
      title: '访问权限',
      dataIndex: 'access_granted',
      key: 'access_granted',
      width: 100,
      render: (granted: boolean, record: ServerPermission) => (
        <Switch
          checked={granted}
          onChange={(checked) => updateServerPermission(record.id, 'access_granted', checked)}
        />
      ),
    },
    {
      title: '编辑权限',
      dataIndex: 'edit_granted',
      key: 'edit_granted',
      width: 100,
      render: (granted: boolean, record: ServerPermission) => (
        <Switch
          checked={granted}
          disabled={!record.access_granted}
          onChange={(checked) => updateServerPermission(record.id, 'edit_granted', checked)}
        />
      ),
    },
    {
      title: 'SSH连接',
      dataIndex: 'connect_granted',
      key: 'connect_granted',
      width: 100,
      render: (granted: boolean, record: ServerPermission) => (
        <Tooltip title="允许SSH连接服务器">
          <Switch
            checked={granted}
            disabled={!record.access_granted}
            onChange={(checked) => updateServerPermission(record.id, 'connect_granted', checked)}
          />
        </Tooltip>
      ),
    },
    {
      title: '查看密码',
      dataIndex: 'view_credentials',
      key: 'view_credentials',
      width: 100,
      render: (granted: boolean, record: ServerPermission) => (
        <Tooltip title="允许查看服务器连接密码">
          <Switch
            checked={granted}
            disabled={!record.access_granted}
            onChange={(checked) => updateServerPermission(record.id, 'view_credentials', checked)}
          />
        </Tooltip>
      ),
    },
  ];

  // 更新网站权限
  const updateWebsitePermission = (websiteId: number, field: keyof WebsitePermission, value: boolean) => {
    setWebsitePermissions(prev => 
      prev.map(item => 
        item.id === websiteId ? { ...item, [field]: value } : item
      )
    );
  };

  // 更新服务器权限
  const updateServerPermission = (serverId: number, field: keyof ServerPermission, value: boolean) => {
    setServerPermissions(prev => 
      prev.map(item => 
        item.id === serverId ? { ...item, [field]: value } : item
      )
    );
  };

  // 批量设置网站权限
  const batchSetWebsitePermissions = (field: keyof WebsitePermission, value: boolean) => {
    setWebsitePermissions(prev => 
      prev.map(item => ({ ...item, [field]: value }))
    );
  };

  // 批量设置服务器权限
  const batchSetServerPermissions = (field: keyof ServerPermission, value: boolean) => {
    setServerPermissions(prev => 
      prev.map(item => ({ ...item, [field]: value }))
    );
  };

  // 保存权限设置
  const handleSave = async () => {
    setLoading(true);
    try {
      const permissionData = {
        userId,
        pagePermissions: pageTargetKeys,
        websitePermissions,
        serverPermissions
      };

      await onSave(permissionData);
      message.success('权限设置保存成功');
      onClose();
    } catch (error) {
      console.error('保存权限失败:', error);
      message.error('保存权限失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    if (visible && userId) {
      loadUserPermissions();
    }
  }, [visible, userId]);

  return (
    <Modal
      title={
        <Space>
          <UserOutlined />
          <span>用户权限管理 - {userName}</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" loading={loading} onClick={handleSave}>
            保存权限设置
          </Button>
        </Space>
      }
    >
      <Spin spinning={loading}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {/* 页面权限标签页 */}
          <TabPane tab={<span><SettingOutlined />页面权限</span>} key="page">
            <Card>
              <Title level={5}>页面访问权限</Title>
              <Text type="secondary">控制用户可以访问哪些页面和功能模块</Text>
              <Divider />
              <Transfer
                dataSource={pagePermissions}
                targetKeys={pageTargetKeys}
                selectedKeys={pageSelectedKeys}
                onChange={handlePagePermissionChange}
                onSelectChange={handlePagePermissionSelectChange}
                titles={['可用权限', '已授权限']}
                render={(item) => (
                  <Space>
                    {item.icon}
                    <div>
                      <div>{item.title}</div>
                      {item.description && (
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {item.description}
                        </Text>
                      )}
                    </div>
                  </Space>
                )}
                listStyle={{
                  width: 400,
                  height: 400,
                }}
                showSearch
                searchPlaceholder="搜索权限"
                locale={{
                  itemUnit: '项',
                  itemsUnit: '项',
                  searchPlaceholder: '搜索权限',
                  notFoundContent: '暂无数据'
                }}
              />
            </Card>
          </TabPane>

          {/* 网站权限标签页 */}
          <TabPane tab={<span><GlobalOutlined />网站权限</span>} key="website">
            <Card>
              <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
                <Col>
                  <Title level={5}>网站访问权限</Title>
                  <Text type="secondary">控制用户对具体网站的访问和操作权限</Text>
                </Col>
                <Col>
                  <Space>
                    <Button size="small" onClick={() => batchSetWebsitePermissions('access_granted', true)}>
                      全部授权访问
                    </Button>
                    <Button size="small" onClick={() => batchSetWebsitePermissions('edit_granted', true)}>
                      全部授权编辑
                    </Button>
                    <Button size="small" onClick={() => batchSetWebsitePermissions('view_credentials', true)}>
                      全部授权查看密码
                    </Button>
                  </Space>
                </Col>
              </Row>
              <Table
                columns={websiteColumns}
                dataSource={websitePermissions}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                size="small"
              />
            </Card>
          </TabPane>

          {/* 服务器权限标签页 */}
          <TabPane tab={<span><ServerOutlined />服务器权限</span>} key="server">
            <Card>
              <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
                <Col>
                  <Title level={5}>服务器访问权限</Title>
                  <Text type="secondary">控制用户对具体服务器的访问和操作权限</Text>
                </Col>
                <Col>
                  <Space>
                    <Button size="small" onClick={() => batchSetServerPermissions('access_granted', true)}>
                      全部授权访问
                    </Button>
                    <Button size="small" onClick={() => batchSetServerPermissions('edit_granted', true)}>
                      全部授权编辑
                    </Button>
                    <Button size="small" onClick={() => batchSetServerPermissions('connect_granted', true)}>
                      全部授权连接
                    </Button>
                  </Space>
                </Col>
              </Row>
              <Table
                columns={serverColumns}
                dataSource={serverPermissions}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                size="small"
              />
            </Card>
          </TabPane>
        </Tabs>
      </Spin>
    </Modal>
  );
};

export default EnhancedUserPermissionEditor;
