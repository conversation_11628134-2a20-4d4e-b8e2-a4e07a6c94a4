import React, { ReactNode, useEffect, useState } from 'react';
import { Al<PERSON>, Spin } from 'antd';
import { PermissionUtils } from '@/services/user';

interface PermissionWrapperProps {
  children: ReactNode;
  permission?: string | string[];
  fallback?: ReactNode;
  requireAll?: boolean; // 是否需要所有权限（默认false，即有任一权限即可）
  adminOnly?: boolean; // 是否仅管理员可见
  loading?: boolean;
}

/**
 * 权限控制包装组件
 * 根据用户权限决定是否显示子组件
 */
const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  children,
  permission,
  fallback = null,
  requireAll = false,
  adminOnly = false,
  loading = false
}) => {
  const [hasPermission, setHasPermission] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkPermission = async () => {
      try {
        // 初始化权限（如果还没有初始化）
        if (PermissionUtils.getPermissions().length === 0) {
          await PermissionUtils.initPermissions();
        }

        let permitted = false;

        if (adminOnly) {
          // 仅管理员可见
          permitted = PermissionUtils.isAdminUser();
        } else if (permission) {
          // 检查特定权限
          if (Array.isArray(permission)) {
            if (requireAll) {
              permitted = PermissionUtils.hasAllPermissions(permission);
            } else {
              permitted = PermissionUtils.hasAnyPermission(permission);
            }
          } else {
            permitted = PermissionUtils.hasPermission(permission);
          }
        } else {
          // 没有权限要求，默认显示
          permitted = true;
        }

        setHasPermission(permitted);
      } catch (error) {
        console.error('权限检查失败:', error);
        setHasPermission(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkPermission();
  }, [permission, requireAll, adminOnly]);

  if (loading || isLoading) {
    return <Spin size="small" />;
  }

  if (!hasPermission) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * 权限检查Hook
 */
export const usePermission = (permission?: string | string[], requireAll = false) => {
  const [hasPermission, setHasPermission] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkPermission = async () => {
      try {
        if (PermissionUtils.getPermissions().length === 0) {
          await PermissionUtils.initPermissions();
        }

        let permitted = false;

        if (permission) {
          if (Array.isArray(permission)) {
            if (requireAll) {
              permitted = PermissionUtils.hasAllPermissions(permission);
            } else {
              permitted = PermissionUtils.hasAnyPermission(permission);
            }
          } else {
            permitted = PermissionUtils.hasPermission(permission);
          }
        } else {
          permitted = true;
        }

        setHasPermission(permitted);
      } catch (error) {
        console.error('权限检查失败:', error);
        setHasPermission(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkPermission();
  }, [permission, requireAll]);

  return { hasPermission, isLoading };
};

/**
 * 权限按钮组件
 */
interface PermissionButtonProps {
  permission?: string | string[];
  requireAll?: boolean;
  adminOnly?: boolean;
  children: ReactNode;
  fallback?: ReactNode;
  disabled?: boolean;
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  permission,
  requireAll = false,
  adminOnly = false,
  children,
  fallback = null,
  disabled = false
}) => {
  const { hasPermission, isLoading } = usePermission(permission, requireAll);

  if (isLoading) {
    return <Spin size="small" />;
  }

  if (adminOnly && !PermissionUtils.isAdminUser()) {
    return <>{fallback}</>;
  }

  if (!hasPermission || disabled) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

/**
 * 权限菜单项组件
 */
interface PermissionMenuItemProps {
  permission?: string | string[];
  requireAll?: boolean;
  adminOnly?: boolean;
  children: ReactNode;
}

export const PermissionMenuItem: React.FC<PermissionMenuItemProps> = ({
  permission,
  requireAll = false,
  adminOnly = false,
  children
}) => {
  const { hasPermission } = usePermission(permission, requireAll);

  if (adminOnly && !PermissionUtils.isAdminUser()) {
    return null;
  }

  if (!hasPermission) {
    return null;
  }

  return <>{children}</>;
};

/**
 * 权限提示组件
 */
interface PermissionAlertProps {
  permission?: string | string[];
  requireAll?: boolean;
  adminOnly?: boolean;
  message?: string;
  type?: 'info' | 'warning' | 'error';
  showIcon?: boolean;
}

export const PermissionAlert: React.FC<PermissionAlertProps> = ({
  permission,
  requireAll = false,
  adminOnly = false,
  message = '您没有权限访问此功能',
  type = 'warning',
  showIcon = true
}) => {
  const { hasPermission } = usePermission(permission, requireAll);

  if (adminOnly && !PermissionUtils.isAdminUser()) {
    return (
      <Alert
        message="权限不足"
        description="此功能仅限管理员访问"
        type={type}
        showIcon={showIcon}
      />
    );
  }

  if (!hasPermission) {
    return (
      <Alert
        message="权限不足"
        description={message}
        type={type}
        showIcon={showIcon}
      />
    );
  }

  return null;
};

/**
 * 权限路由组件
 */
interface PermissionRouteProps {
  permission?: string | string[];
  requireAll?: boolean;
  adminOnly?: boolean;
  children: ReactNode;
  fallback?: ReactNode;
}

export const PermissionRoute: React.FC<PermissionRouteProps> = ({
  permission,
  requireAll = false,
  adminOnly = false,
  children,
  fallback = (
    <Alert
      message="访问被拒绝"
      description="您没有权限访问此页面"
      type="error"
      showIcon
    />
  )
}) => {
  return (
    <PermissionWrapper
      permission={permission}
      requireAll={requireAll}
      adminOnly={adminOnly}
      fallback={fallback}
    >
      {children}
    </PermissionWrapper>
  );
};

export default PermissionWrapper;
