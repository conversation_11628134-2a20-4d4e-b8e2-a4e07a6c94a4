/**
 * 优化的权限保护组件
 * 基于Context7最佳实践实现：
 * 1. React条件渲染最佳实践
 * 2. 性能优化和缓存
 * 3. 权限属性过滤
 * 4. 多种条件渲染模式
 */

import React, { ReactNode, memo, useMemo } from 'react';
import { useAdvancedPermissionCheck } from '../../contexts/PermissionContext';
import { Alert, Spin, Result } from 'antd';
import { LockOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

// 权限保护组件属性
interface OptimizedPermissionGuardProps {
  // 权限要求
  permissions?: string | string[];
  roles?: string | string[];
  
  // 逻辑控制
  requireAll?: boolean;
  logic?: 'AND' | 'OR';
  
  // 属性过滤
  attributes?: string[];
  
  // 渲染控制
  children: ReactNode;
  fallback?: ReactNode;
  loadingComponent?: ReactNode;
  
  // 行为控制
  mode?: 'hide' | 'disable' | 'show-error';
  errorType?: 'alert' | 'result' | 'custom';
  
  // 性能优化
  skipRender?: boolean; // 跳过渲染，仅用于权限检查
  
  // 回调函数
  onPermissionChange?: (granted: boolean, reason: string) => void;
  
  // 样式控制
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 优化的权限保护组件
 * 使用memo避免不必要的重新渲染
 */
export const OptimizedPermissionGuard: React.FC<OptimizedPermissionGuardProps> = memo(({
  permissions = [],
  roles = [],
  requireAll = false,
  logic = 'OR',
  attributes = ['*'],
  children,
  fallback = null,
  loadingComponent = <Spin size="small" />,
  mode = 'hide',
  errorType = 'alert',
  skipRender = false,
  onPermissionChange,
  className,
  style
}) => {
  // 使用增强的权限检查Hook
  const permissionCheck = useAdvancedPermissionCheck({
    permissions,
    roles,
    requireAll,
    logic,
    attributes
  });

  const { granted, loading, reason, ConditionalRender } = permissionCheck;

  // 权限变化回调
  React.useEffect(() => {
    if (!loading) {
      onPermissionChange?.(granted, reason);
    }
  }, [granted, reason, loading, onPermissionChange]);

  // 如果跳过渲染，返回null
  if (skipRender) {
    return null;
  }

  // 加载状态
  if (loading) {
    return <div className={className} style={style}>{loadingComponent}</div>;
  }

  // 权限不足时的处理
  if (!granted) {
    switch (mode) {
      case 'hide':
        return fallback ? <div className={className} style={style}>{fallback}</div> : null;
      
      case 'disable':
        // 禁用模式：渲染子组件但设为禁用状态
        return (
          <div className={className} style={{ ...style, opacity: 0.5, pointerEvents: 'none' }}>
            {children}
          </div>
        );
      
      case 'show-error':
        return (
          <div className={className} style={style}>
            {renderErrorContent(errorType, permissions, roles, reason)}
          </div>
        );
      
      default:
        return null;
    }
  }

  // 权限验证通过，使用ConditionalRender组件
  return (
    <ConditionalRender fallback={fallback}>
      <div className={className} style={style}>
        {children}
      </div>
    </ConditionalRender>
  );
});

OptimizedPermissionGuard.displayName = 'OptimizedPermissionGuard';

/**
 * 渲染错误内容
 */
const renderErrorContent = (
  errorType: 'alert' | 'result' | 'custom',
  permissions: string | string[],
  roles: string | string[],
  reason: string
) => {
  const permissionList = Array.isArray(permissions) ? permissions : [permissions];
  const roleList = Array.isArray(roles) ? roles : [roles];
  
  const generateMessage = () => {
    if (reason) return reason;
    
    const parts = [];
    if (permissionList.length > 0) {
      parts.push(`需要权限: ${permissionList.join(', ')}`);
    }
    if (roleList.length > 0) {
      parts.push(`需要角色: ${roleList.join(', ')}`);
    }
    return parts.join(' 或 ') || '权限不足';
  };

  switch (errorType) {
    case 'alert':
      return (
        <Alert
          message="权限不足"
          description={generateMessage()}
          type="warning"
          icon={<LockOutlined />}
          showIcon
        />
      );
      
    case 'result':
      return (
        <Result
          status="403"
          title="权限不足"
          subTitle={generateMessage()}
          icon={<LockOutlined />}
        />
      );
      
    default:
      return (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <ExclamationCircleOutlined style={{ fontSize: '24px', color: '#faad14' }} />
          <p style={{ marginTop: '8px', color: '#666' }}>
            {generateMessage()}
          </p>
        </div>
      );
  }
};

/**
 * 权限条件渲染Hook
 * 基于React条件渲染最佳实践
 */
export const useConditionalRender = (config: {
  permissions?: string | string[];
  roles?: string | string[];
  requireAll?: boolean;
  logic?: 'AND' | 'OR';
}) => {
  const permissionCheck = useAdvancedPermissionCheck(config);
  
  return useMemo(() => ({
    // 使用逻辑AND操作符的条件渲染
    renderIf: (component: ReactNode) => 
      permissionCheck.granted && component,
    
    // 使用三元操作符的条件渲染
    renderIfElse: (component: ReactNode, fallback: ReactNode = null) =>
      permissionCheck.granted ? component : fallback,
    
    // 返回null隐藏组件
    renderOrNull: (component: ReactNode) =>
      permissionCheck.granted ? component : null,
    
    // 条件样式
    conditionalStyle: (baseStyle: React.CSSProperties = {}, disabledStyle: React.CSSProperties = {}) => ({
      ...baseStyle,
      ...(permissionCheck.granted ? {} : disabledStyle)
    }),
    
    // 条件类名
    conditionalClassName: (baseClass: string = '', disabledClass: string = '') =>
      permissionCheck.granted ? baseClass : `${baseClass} ${disabledClass}`.trim(),
    
    // 权限状态
    granted: permissionCheck.granted,
    loading: permissionCheck.loading,
    reason: permissionCheck.reason
  }), [permissionCheck]);
};

/**
 * 简化的权限检查组件
 * 基于React最佳实践的简洁API
 */
export const PermissionRender: React.FC<{
  permission: string | string[];
  children: ReactNode;
  fallback?: ReactNode;
  mode?: 'if' | 'ifElse' | 'orNull';
}> = memo(({ permission, children, fallback = null, mode = 'if' }) => {
  const { renderIf, renderIfElse, renderOrNull } = useConditionalRender({
    permissions: permission
  });

  switch (mode) {
    case 'ifElse':
      return <>{renderIfElse(children, fallback)}</>;
    case 'orNull':
      return <>{renderOrNull(children)}</>;
    default:
      return <>{renderIf(children)}</>;
  }
});

PermissionRender.displayName = 'PermissionRender';

export default OptimizedPermissionGuard;
