/**
 * 权限保护组件
 * 功能：
 * 1. 实现条件渲染权限保护组件
 * 2. 支持权限、角色、组合条件检查
 * 3. 提供友好的权限不足提示界面
 * 4. 支持加载状态和错误处理
 */

import React, { ReactNode } from 'react';
import { usePermissions } from '../../contexts/PermissionContext';
import { Alert, Spin, Button, Result } from 'antd';
import { LockOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

// 权限保护组件属性
interface PermissionGuardProps {
  // 权限要求
  permissions?: string | string[];
  roles?: string | string[];
  
  // 逻辑控制
  requireAll?: boolean;        // 是否需要所有权限/角色
  logic?: 'AND' | 'OR';       // 权限和角色之间的逻辑关系
  
  // 渲染控制
  children: ReactNode;
  fallback?: ReactNode;       // 权限不足时显示的内容
  loadingComponent?: ReactNode;        // 加载时显示的内容
  
  // 行为控制
  redirect?: string;          // 权限不足时重定向的路径
  showError?: boolean;        // 是否显示错误信息
  errorType?: 'alert' | 'result' | 'custom'; // 错误显示类型
  
  // 回调函数
  onPermissionDenied?: () => void;
  onPermissionGranted?: () => void;
  
  // 样式控制
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 权限保护组件
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permissions = [],
  roles = [],
  requireAll = false,
  logic = 'OR',
  children,
  fallback = null,
  loadingComponent = <Spin size="small" />,
  redirect,
  showError = true,
  errorType = 'alert',
  onPermissionDenied,
  onPermissionGranted,
  className,
  style
}) => {
  // 权限检查
  const { hasPermission, hasAnyPermission, hasAllPermissions, hasRole, loading } = usePermissions();

  // 计算权限结果
  const permissionsArray = Array.isArray(permissions) ? permissions : [permissions].filter(Boolean);
  const rolesArray = Array.isArray(roles) ? roles : [roles].filter(Boolean);

  const hasPermissions = permissionsArray.length === 0 ||
    (requireAll ? hasAllPermissions(permissionsArray) : hasAnyPermission(permissionsArray));
  const hasRoles = rolesArray.length === 0 ||
    rolesArray.some(role => hasRole(role));

  let hasAccess = false;
  if (logic === 'AND') {
    hasAccess = hasPermissions && hasRoles;
  } else {
    hasAccess = hasPermissions || hasRoles;
  }

  // 权限检查结果回调 - 必须在所有条件判断之前调用
  React.useEffect(() => {
    if (!loading) {
      if (hasAccess) {
        onPermissionGranted?.();
      } else {
        onPermissionDenied?.();
      }
    }
  }, [hasAccess, onPermissionGranted, onPermissionDenied, loading]);

  // 加载状态
  if (loading) {
    return <div className={className} style={style}>{loadingComponent}</div>;
  }

  // 权限不足时的处理
  if (!hasAccess) {
    // 重定向处理
    if (redirect) {
      window.location.href = redirect;
      return null;
    }

    // 自定义fallback
    if (fallback !== null) {
      return <div className={className} style={style}>{fallback}</div>;
    }

    // 显示错误信息
    if (showError) {
      return (
        <div className={className} style={style}>
          {renderErrorContent(errorType, permissions, roles, requireAll, logic)}
        </div>
      );
    }

    // 不显示任何内容
    return null;
  }

  // 权限验证通过，渲染子组件
  return <div className={className} style={style}>{children}</div>;
};

/**
 * 渲染错误内容
 */
const renderErrorContent = (
  errorType: 'alert' | 'result' | 'custom',
  permissions: string | string[],
  roles: string | string[],
  requireAll: boolean,
  logic: 'AND' | 'OR'
) => {
  const permissionList = Array.isArray(permissions) ? permissions : [permissions];
  const roleList = Array.isArray(roles) ? roles : [roles];
  
  const generateMessage = () => {
    const parts = [];
    
    if (permissionList.length > 0) {
      const permText = requireAll ? '所有权限' : '以下权限之一';
      parts.push(`需要${permText}: ${permissionList.join(', ')}`);
    }
    
    if (roleList.length > 0) {
      const roleText = '以下角色之一';
      parts.push(`需要${roleText}: ${roleList.join(', ')}`);
    }
    
    return parts.join(logic === 'AND' ? ' 且 ' : ' 或 ');
  };

  switch (errorType) {
    case 'alert':
      return (
        <Alert
          message="权限不足"
          description={generateMessage()}
          type="warning"
          icon={<LockOutlined />}
          showIcon
        />
      );
      
    case 'result':
      return (
        <Result
          status="403"
          title="权限不足"
          subTitle={generateMessage()}
          icon={<LockOutlined />}
        />
      );
      
    default:
      return (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <ExclamationCircleOutlined style={{ fontSize: '24px', color: '#faad14' }} />
          <p style={{ marginTop: '8px', color: '#666' }}>
            {generateMessage()}
          </p>
        </div>
      );
  }
};

/**
 * 简化的权限检查组件
 */
export const RequirePermission: React.FC<{
  permission: string | string[];
  children: ReactNode;
  fallback?: ReactNode;
}> = ({ permission, children, fallback }) => {
  return (
    <PermissionGuard permissions={permission} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
};

/**
 * 简化的角色检查组件
 */
export const RequireRole: React.FC<{
  role: string | string[];
  children: ReactNode;
  fallback?: ReactNode;
}> = ({ role, children, fallback }) => {
  return (
    <PermissionGuard roles={role} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
};

/**
 * 管理员权限检查组件
 */
export const RequireAdmin: React.FC<{
  children: ReactNode;
  fallback?: ReactNode;
}> = ({ children, fallback }) => {
  return (
    <PermissionGuard roles={['admin', 'super_admin']} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
};

/**
 * 超级管理员权限检查组件
 */
export const RequireSuperAdmin: React.FC<{
  children: ReactNode;
  fallback?: ReactNode;
}> = ({ children, fallback }) => {
  return (
    <PermissionGuard roles="super_admin" fallback={fallback}>
      {children}
    </PermissionGuard>
  );
};

/**
 * 权限按钮组件 - 根据权限控制按钮的显示和禁用状态
 * 注意：此组件作为权限包装器，不渲染自己的Button，而是修改子Button的props
 */
export const PermissionButton: React.FC<{
  permissions?: string | string[];
  roles?: string | string[];
  requireAll?: boolean;
  children: ReactNode;
  disabled?: boolean;
  hideWhenNoPermission?: boolean;
}> = ({
  permissions = [],
  roles = [],
  requireAll = false,
  children,
  disabled = false,
  hideWhenNoPermission = false,
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, hasRole } = usePermissions();

  // 计算权限结果
  const permissionsArray = Array.isArray(permissions) ? permissions : [permissions].filter(Boolean);
  const rolesArray = Array.isArray(roles) ? roles : [roles].filter(Boolean);

  const hasPermissions = permissionsArray.length === 0 ||
    (requireAll ? hasAllPermissions(permissionsArray) : hasAnyPermission(permissionsArray));
  const hasRoles = rolesArray.length === 0 ||
    rolesArray.some(role => hasRole(role));
  const hasAccess = hasPermissions && hasRoles;

  if (hideWhenNoPermission && !hasAccess) {
    return null;
  }

  // 检查children是否为有效的React元素
  if (!React.isValidElement(children)) {
    console.error('PermissionButton: children must be a valid React element');
    return null;
  }

  // 克隆子元素并注入权限相关的props
  return React.cloneElement(children, {
    disabled: disabled || !hasAccess,
    title: !hasAccess ? '权限不足' : children.props?.title,
  });
};

/**
 * 权限感知按钮组件 - 直接渲染带权限控制的Button
 * 用于不需要嵌套Button的场景
 */
export const PermissionAwareButton: React.FC<{
  permissions?: string | string[];
  roles?: string | string[];
  requireAll?: boolean;
  children: ReactNode;
  hideWhenNoPermission?: boolean;
  [key: string]: any;
}> = ({
  permissions = [],
  roles = [],
  requireAll = false,
  children,
  hideWhenNoPermission = false,
  ...buttonProps
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, hasRole } = usePermissions();

  // 计算权限结果
  const permissionsArray = Array.isArray(permissions) ? permissions : [permissions].filter(Boolean);
  const rolesArray = Array.isArray(roles) ? roles : [roles].filter(Boolean);

  const hasPermissions = permissionsArray.length === 0 ||
    (requireAll ? hasAllPermissions(permissionsArray) : hasAnyPermission(permissionsArray));
  const hasRoles = rolesArray.length === 0 ||
    rolesArray.some(role => hasRole(role));
  const hasAccess = hasPermissions && hasRoles;

  if (hideWhenNoPermission && !hasAccess) {
    return null;
  }

  return (
    <Button
      {...buttonProps}
      disabled={buttonProps.disabled || !hasAccess}
      title={!hasAccess ? '权限不足' : buttonProps.title}
    >
      {children}
    </Button>
  );
};

/**
 * 权限链接组件 - 根据权限控制链接的显示和点击
 */
export const PermissionLink: React.FC<{
  permissions?: string | string[];
  roles?: string | string[];
  requireAll?: boolean;
  children: ReactNode;
  href?: string;
  onClick?: () => void;
  hideWhenNoPermission?: boolean;
  className?: string;
  style?: React.CSSProperties;
}> = ({
  permissions = [],
  roles = [],
  requireAll = false,
  children,
  href,
  onClick,
  hideWhenNoPermission = false,
  className,
  style
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, hasRole } = usePermissions();

  // 计算权限结果
  const permissionsArray = Array.isArray(permissions) ? permissions : [permissions].filter(Boolean);
  const rolesArray = Array.isArray(roles) ? roles : [roles].filter(Boolean);

  const hasPermissions = permissionsArray.length === 0 ||
    (requireAll ? hasAllPermissions(permissionsArray) : hasAnyPermission(permissionsArray));
  const hasRoles = rolesArray.length === 0 ||
    rolesArray.some(role => hasRole(role));
  const hasAccess = hasPermissions && hasRoles;

  if (hideWhenNoPermission && !hasAccess) {
    return null;
  }

  const handleClick = (e: React.MouseEvent) => {
    if (!hasAccess) {
      e.preventDefault();
      return;
    }
    onClick?.();
  };

  return (
    <a
      href={hasAccess ? href : undefined}
      onClick={handleClick}
      className={className}
      style={{
        ...style,
        cursor: hasAccess ? 'pointer' : 'not-allowed',
        opacity: hasAccess ? 1 : 0.5
      }}
      title={!hasAccess ? '权限不足' : undefined}
    >
      {children}
    </a>
  );
};

export default PermissionGuard;