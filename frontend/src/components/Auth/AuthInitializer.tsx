import React, { useEffect, useState } from 'react';
import { Spin } from 'antd';
import { useAuthStore } from '@/store/auth';
import { authApi, storage } from '@/services/auth';

interface AuthInitializerProps {
  children: React.ReactNode;
}

/**
 * 认证初始化组件
 * 在应用启动时验证token有效性，确保安全性
 */
const AuthInitializer: React.FC<AuthInitializerProps> = ({ children }) => {
  const [isInitializing, setIsInitializing] = useState(true);
  const { user, isAuthenticated, logout, refreshUser } = useAuthStore();

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // 检查是否有存储的认证信息
        const token = storage.getToken();
        const storedUser = storage.getUser();

        if (token && storedUser) {
          console.log('🔍 检测到存储的认证信息，验证token有效性...');

          try {
            // 验证token有效性 - 调用后端API
            const response = await authApi.getCurrentUser();

            if (response.success && response.data) {
              console.log('✅ Token验证成功，用户认证有效');
              // Token有效，刷新用户信息
              await refreshUser();
            } else {
              console.warn('⚠️ Token验证失败，清除认证状态');
              // Token无效，等待logout完全完成
              await logout();
              console.log('🔒 认证状态已清除');
            }
          } catch (error: any) {
            console.error('❌ Token验证失败:', error.message);

            // 如果是401错误，说明token已过期或无效
            if (error.response?.status === 401 || error.response?.status === 403) {
              console.log('🔒 Token已过期或无效，清除认证状态');
              // 等待logout完全完成
              await logout();
              console.log('🔒 认证状态已清除');
            } else {
              // 其他错误可能是网络问题，保持当前状态但记录错误
              console.warn('🌐 网络错误，保持当前认证状态');
            }
          }
        } else {
          console.log('ℹ️ 未检测到认证信息，用户未登录');
          // 确保store状态与localStorage一致
          if (isAuthenticated) {
            console.log('🔧 清理不一致的认证状态');
            await logout();
          }
        }
      } catch (error) {
        console.error('💥 认证初始化过程中发生错误:', error);
        // 发生严重错误时，为安全起见清除认证状态
        await logout();
        console.log('🔒 认证状态已清除');
      } finally {
        // 确保在所有异步操作完成后才设置初始化完成
        console.log('✅ 认证初始化完成');
        setIsInitializing(false);
      }
    };

    initializeAuth();
  }, [logout, refreshUser, isAuthenticated]);

  // 显示加载状态
  if (isInitializing) {
    return (
      <div 
        style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '100vh',
          flexDirection: 'column',
          gap: '16px'
        }}
      >
        <Spin size="large" />
        <div style={{ color: '#666', fontSize: '14px' }}>
          正在验证认证状态...
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthInitializer;
