import React, { useState, useEffect } from 'react';
import { Modal, Button, Progress, Typography, Space, Alert } from 'antd';
import { ExclamationCircleOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { enhancedAuthManager } from '../../services/enhancedAuth';
import { useAuthStore } from '../../store/auth';

// 自定义进度条样式
const progressStyles = {
  container: {
    marginBottom: '16px',
    padding: '8px',
    borderRadius: '12px',
    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08), inset 0 1px 0 rgba(255, 255, 255, 0.5)',
    border: '1px solid rgba(0, 0, 0, 0.06)',
    position: 'relative' as const,
    overflow: 'hidden' as const,
  },
  progressBar: {
    height: '12px',
    borderRadius: '8px',
    overflow: 'hidden',
    background: 'linear-gradient(90deg, #f0f0f0 0%, #e6e6e6 100%)',
    boxShadow: 'inset 0 2px 4px rgba(0, 0, 0, 0.1)',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  },
  shimmer: {
    position: 'absolute' as const,
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent)',
    animation: 'shimmer 2s infinite',
    pointerEvents: 'none' as const,
  }
};

// 添加CSS动画样式
const shimmerKeyframes = `
  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
  }
`;

// 将样式注入到页面中
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = shimmerKeyframes;
  if (!document.head.querySelector('style[data-login-timeout-styles]')) {
    styleElement.setAttribute('data-login-timeout-styles', 'true');
    document.head.appendChild(styleElement);
  }
}

const { Text, Title } = Typography;

interface LoginTimeoutWarningProps {
  visible: boolean;
  remainingTime: number;
  onExtend: () => void;
  onLogout: () => void;
}

const LoginTimeoutWarning: React.FC<LoginTimeoutWarningProps> = ({
  visible,
  remainingTime,
  onExtend,
  onLogout
}) => {
  const [countdown, setCountdown] = useState(remainingTime);
  const [initialTime, setInitialTime] = useState(remainingTime);

  useEffect(() => {
    setCountdown(remainingTime);
    setInitialTime(remainingTime);
  }, [remainingTime]);

  useEffect(() => {
    if (!visible) return;

    const timer = setInterval(() => {
      setCountdown(prev => {
        const newValue = prev - 1000;
        if (newValue <= 0) {
          clearInterval(timer);
          onLogout();
          return 0;
        }
        return newValue;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [visible, onLogout]);

  const formatTime = (ms: number): string => {
    const minutes = Math.floor(ms / 1000 / 60);
    const seconds = Math.floor((ms / 1000) % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getProgressPercent = (): number => {
    // 使用实际的初始时间作为基准，而不是固定的10分钟
    // 这样可以正确处理警告时间少于10分钟的情况
    if (initialTime <= 0) return 100;

    // 计算已经过去的时间百分比：进度条从0%（开始警告）到100%（超时）
    // countdown是当前剩余时间，initialTime是开始警告时的剩余时间
    const elapsedTime = initialTime - countdown;
    const progressPercent = (elapsedTime / initialTime) * 100;
    return Math.min(100, Math.max(0, progressPercent));
  };

  const getProgressStatus = (): "normal" | "active" | "exception" => {
    const progressPercent = getProgressPercent();
    if (progressPercent >= 80) return "exception"; // 进度超过80%时显示为危险状态
    if (progressPercent >= 50) return "active"; // 进度超过50%时显示为活跃状态
    return "normal";
  };

  return (
    <Modal
      title={
        <Space>
          <ExclamationCircleOutlined style={{ color: '#faad14' }} />
          <span>登录即将超时</span>
        </Space>
      }
      open={visible}
      closable={false}
      maskClosable={false}
      footer={[
        <Button key="logout" onClick={onLogout}>
          立即登出
        </Button>,
        <Button key="extend" type="primary" onClick={onExtend}>
          延长登录时间
        </Button>
      ]}
      width={480}
    >
      <div style={{ textAlign: 'center', padding: '20px 0' }}>
        <Alert
          message="会话即将过期"
          description="为了保护您的账户安全，系统将在指定时间后自动登出。"
          type="warning"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <div style={{ marginBottom: '24px' }}>
          <ClockCircleOutlined style={{ fontSize: '48px', color: '#faad14', marginBottom: '16px' }} />
          <Title level={2} style={{ margin: 0, color: '#faad14' }}>
            {formatTime(countdown)}
          </Title>
          <Text type="secondary">剩余时间</Text>
        </div>

        <div style={progressStyles.container}>
          <div style={progressStyles.shimmer}></div>
          <Progress
            percent={getProgressPercent()}
            status={getProgressStatus()}
            showInfo={false}
            strokeColor={{
              '0%': '#52c41a',
              '25%': '#1890ff',
              '50%': '#13c2c2',
              '75%': '#faad14',
              '100%': '#ff4d4f',
            }}
            strokeLinecap="round"
            size={14}
            trailColor="rgba(0, 0, 0, 0.06)"
            style={{
              ...progressStyles.progressBar,
              animation: getProgressPercent() > 80 ? 'pulse 1s infinite' : 'none',
            }}
          />
        </div>

        <div style={{ textAlign: 'left' }}>
          <Text strong>您可以选择：</Text>
          <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>
            <li>
              <Text>点击"延长登录时间"继续使用系统</Text>
            </li>
            <li>
              <Text>点击"立即登出"安全退出系统</Text>
            </li>
            <li>
              <Text type="secondary">或等待系统自动登出</Text>
            </li>
          </ul>
        </div>
      </div>
    </Modal>
  );
};

// 登录超时管理Hook
export const useLoginTimeout = () => {
  const [warningVisible, setWarningVisible] = useState(false);
  const [remainingTime, setRemainingTime] = useState(0);
  const { logout } = useAuthStore();

  useEffect(() => {
    // 设置超时回调
    enhancedAuthManager.setTimeoutCallbacks(
      // 超时回调
      () => {
        setWarningVisible(false);
        logout();
      },
      // 警告回调
      (remaining: number) => {
        setRemainingTime(remaining);
        setWarningVisible(true);
      }
    );

    return () => {
      // 清理
    };
  }, [logout]);

  const handleExtendLogin = () => {
    enhancedAuthManager.extendLogin();
    setWarningVisible(false);
  };

  const handleLogout = () => {
    setWarningVisible(false);
    logout();
  };

  return {
    warningVisible,
    remainingTime,
    handleExtendLogin,
    handleLogout,
    LoginTimeoutWarning: () => (
      <LoginTimeoutWarning
        visible={warningVisible}
        remainingTime={remainingTime}
        onExtend={handleExtendLogin}
        onLogout={handleLogout}
      />
    )
  };
};

export default LoginTimeoutWarning;
