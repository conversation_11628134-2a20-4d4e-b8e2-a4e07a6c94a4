import React, { useState } from 'react';
import { Modal, Form, DatePicker, Space, Typography, List, Avatar } from 'antd';
import { CalendarOutlined, DesktopOutlined } from '@ant-design/icons';
import { Server } from '../../types';
import dayjs from 'dayjs';

const { Text, Title } = Typography;

interface BatchExpireDateModalProps {
  visible: boolean;
  selectedServers: Server[];
  onOk: (expireDate: string) => void;
  onCancel: () => void;
}

const BatchExpireDateModal: React.FC<BatchExpireDateModalProps> = ({
  visible,
  selectedServers,
  onOk,
  onCancel
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const expireDate = values.expireDate.format('YYYY-MM-DD');
      await onOk(expireDate);
      
      form.resetFields();
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <CalendarOutlined style={{ color: '#10b981' }} />
          <span>批量修改到期日期</span>
        </div>
      }
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={600}
      okText="确认修改"
      cancelText="取消"
      destroyOnHidden
    >
      <div style={{ marginBottom: 24 }}>
        <Title level={5} style={{ marginBottom: 16 }}>
          <DesktopOutlined style={{ marginRight: 8, color: '#3b82f6' }} />
          选中的服务器 ({selectedServers.length} 台)
        </Title>
        
        <div style={{ 
          maxHeight: 200, 
          overflowY: 'auto',
          border: '1px solid #f0f0f0',
          borderRadius: 6,
          padding: 8
        }}>
          <List
            size="small"
            dataSource={selectedServers}
            renderItem={(server) => (
              <List.Item style={{ padding: '8px 12px' }}>
                <List.Item.Meta
                  avatar={
                    <Avatar 
                      size="small" 
                      icon={<DesktopOutlined />} 
                      style={{ backgroundColor: '#3b82f6' }}
                    />
                  }
                  title={
                    <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                      <Text strong>{server.name}</Text>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {server.ipAddress}
                      </Text>
                    </div>
                  }
                  description={
                    <div style={{ fontSize: 12 }}>
                      <Text type="secondary">
                        当前到期: {server.expireDate || '未设置'}
                      </Text>
                      {server.location && (
                        <Text type="secondary" style={{ marginLeft: 12 }}>
                          位置: {server.location}
                        </Text>
                      )}
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        </div>
      </div>

      <Form
        form={form}
        layout="vertical"
        requiredMark={false}
      >
        <Form.Item
          name="expireDate"
          label={
            <span style={{ fontWeight: 500 }}>
              <CalendarOutlined style={{ marginRight: 8, color: '#10b981' }} />
              新的到期日期
            </span>
          }
          rules={[
            { required: true, message: '请选择到期日期' },
            {
              validator: (_, value) => {
                if (value && value.isBefore(dayjs(), 'day')) {
                  return Promise.reject(new Error('到期日期不能早于今天'));
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <DatePicker
            style={{ width: '100%' }}
            placeholder="请选择到期日期"
            disabledDate={(current) => current && current < dayjs().startOf('day')}
            showToday
            format="YYYY-MM-DD"
          />
        </Form.Item>

        <div style={{ 
          background: '#f8fafc', 
          padding: 16, 
          borderRadius: 6,
          border: '1px solid #e2e8f0'
        }}>
          <Text type="secondary" style={{ fontSize: 13 }}>
            <strong>注意事项：</strong>
            <br />
            • 此操作将同时修改所有选中服务器的到期日期
            <br />
            • 修改后的到期日期将立即生效
            <br />
            • 请确认日期无误后再进行操作
          </Text>
        </div>
      </Form>
    </Modal>
  );
};

export default BatchExpireDateModal;
