import React, { useState, useEffect } from 'react';
import {
  Modal,
  Card,
  Input,
  Button,
  Space,
  Table,
  Tag,
  Progress,
  Alert,
  Collapse,
  Typography,
  Spin,
  message,
  Tooltip,
  Row,
  Col,
  Statistic,
  Divider
} from 'antd';
import {
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  CodeOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  DeleteOutlined,
  CopyOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { Server } from '../../types';
import { request } from '../../services/api';

const { TextArea } = Input;
const { Panel } = Collapse;
const { Text, Title } = Typography;

interface BatchTerminalManagerProps {
  visible: boolean;
  selectedServers: Server[];
  onCancel: () => void;
  onClearSelection: () => void;
}

interface CommandResult {
  serverId: number;
  serverName: string;
  serverIp: string;
  status: 'pending' | 'running' | 'success' | 'error' | 'timeout';
  output: string;
  error?: string;
  executionTime?: number;
  timestamp?: string;
}

interface BatchExecutionStats {
  total: number;
  pending: number;
  running: number;
  success: number;
  error: number;
  timeout: number;
}

const BatchTerminalManager: React.FC<BatchTerminalManagerProps> = ({
  visible,
  selectedServers,
  onCancel,
  onClearSelection
}) => {
  const [command, setCommand] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [results, setResults] = useState<CommandResult[]>([]);
  const [executionId, setExecutionId] = useState<string | null>(null);
  const [stats, setStats] = useState<BatchExecutionStats>({
    total: 0,
    pending: 0,
    running: 0,
    success: 0,
    error: 0,
    timeout: 0
  });

  // 重置状态
  const resetState = () => {
    setCommand('');
    setIsExecuting(false);
    setResults([]);
    setExecutionId(null);
    setStats({
      total: 0,
      pending: 0,
      running: 0,
      success: 0,
      error: 0,
      timeout: 0
    });
  };

  // 当弹窗关闭时重置状态
  useEffect(() => {
    if (!visible) {
      resetState();
    }
  }, [visible]);

  // 初始化结果状态
  useEffect(() => {
    if (selectedServers.length > 0) {
      const initialResults: CommandResult[] = selectedServers.map(server => ({
        serverId: server.id,
        serverName: server.name,
        serverIp: server.ipAddress,
        status: 'pending',
        output: '',
        timestamp: new Date().toISOString()
      }));
      setResults(initialResults);
      updateStats(initialResults);
    }
  }, [selectedServers]);

  // 更新统计信息
  const updateStats = (currentResults: CommandResult[]) => {
    const newStats = currentResults.reduce((acc, result) => {
      acc.total++;
      acc[result.status]++;
      return acc;
    }, {
      total: 0,
      pending: 0,
      running: 0,
      success: 0,
      error: 0,
      timeout: 0
    });
    setStats(newStats);
  };

  // 执行批量命令
  const handleExecuteCommand = async () => {
    if (!command.trim()) {
      message.warning('请输入要执行的命令');
      return;
    }

    if (selectedServers.length === 0) {
      message.warning('请选择要执行命令的服务器');
      return;
    }

    setIsExecuting(true);
    const newExecutionId = `batch_${Date.now()}`;
    setExecutionId(newExecutionId);

    try {
      // 初始化所有服务器状态为运行中
      const runningResults = results.map(result => ({
        ...result,
        status: 'running' as const,
        output: '',
        error: undefined,
        executionTime: undefined,
        timestamp: new Date().toISOString()
      }));
      setResults(runningResults);
      updateStats(runningResults);

      // 调用后端批量执行API
      const response = await request.post('/servers/batch-execute-command', {
        serverIds: selectedServers.map(s => s.id),
        command: command.trim(),
        executionId: newExecutionId,
        timeout: 30000 // 30秒超时
      });

      if (response.success) {
        message.success(`批量命令执行已启动，正在 ${selectedServers.length} 台服务器上执行...`);
        
        // 开始轮询结果
        pollExecutionResults(newExecutionId);
      } else {
        throw new Error(response.message || '批量命令执行启动失败');
      }

    } catch (error) {
      console.error('批量命令执行失败:', error);
      message.error('批量命令执行失败: ' + (error instanceof Error ? error.message : '未知错误'));
      
      // 将所有状态设置为错误
      const errorResults = results.map(result => ({
        ...result,
        status: 'error' as const,
        error: error instanceof Error ? error.message : '未知错误',
        timestamp: new Date().toISOString()
      }));
      setResults(errorResults);
      updateStats(errorResults);
      setIsExecuting(false);
    }
  };

  // 轮询执行结果
  const pollExecutionResults = async (execId: string) => {
    const maxPolls = 60; // 最多轮询60次（30秒）
    let pollCount = 0;

    const poll = async () => {
      try {
        const response = await request.get(`/servers/batch-execution-status/${execId}`);
        
        if (response.success && response.data) {
          const { results: serverResults, completed } = response.data;
          
          // 更新结果
          const updatedResults = results.map(result => {
            const serverResult = serverResults.find((r: any) => r.serverId === result.serverId);
            if (serverResult) {
              return {
                ...result,
                status: serverResult.status,
                output: serverResult.output || '',
                error: serverResult.error,
                executionTime: serverResult.executionTime,
                timestamp: serverResult.timestamp || new Date().toISOString()
              };
            }
            return result;
          });
          
          setResults(updatedResults);
          updateStats(updatedResults);

          // 如果执行完成或达到最大轮询次数，停止轮询
          if (completed || pollCount >= maxPolls) {
            setIsExecuting(false);
            if (completed) {
              const successCount = updatedResults.filter(r => r.status === 'success').length;
              const errorCount = updatedResults.filter(r => r.status === 'error').length;
              message.success(`批量命令执行完成！成功: ${successCount} 台，失败: ${errorCount} 台`);
            } else {
              message.warning('批量命令执行超时，部分服务器可能仍在执行中');
            }
            return;
          }

          // 继续轮询
          pollCount++;
          setTimeout(poll, 500); // 每500ms轮询一次
        }
      } catch (error) {
        console.error('轮询执行结果失败:', error);
        setIsExecuting(false);
        message.error('获取执行结果失败');
      }
    };

    poll();
  };

  // 停止执行
  const handleStopExecution = async () => {
    if (!executionId) return;

    try {
      await request.post(`/servers/batch-execution-stop/${executionId}`);
      setIsExecuting(false);
      message.info('已发送停止执行请求');
    } catch (error) {
      console.error('停止执行失败:', error);
      message.error('停止执行失败');
    }
  };

  // 清空结果
  const handleClearResults = () => {
    resetState();
    if (selectedServers.length > 0) {
      const initialResults: CommandResult[] = selectedServers.map(server => ({
        serverId: server.id,
        serverName: server.name,
        serverIp: server.ipAddress,
        status: 'pending',
        output: '',
        timestamp: new Date().toISOString()
      }));
      setResults(initialResults);
      updateStats(initialResults);
    }
  };

  // 复制输出内容
  const handleCopyOutput = (output: string) => {
    navigator.clipboard.writeText(output).then(() => {
      message.success('输出内容已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  // 导出结果
  const handleExportResults = () => {
    const exportData = results.map(result => ({
      服务器名称: result.serverName,
      服务器IP: result.serverIp,
      执行状态: result.status,
      执行时间: result.executionTime ? `${result.executionTime}ms` : '-',
      输出内容: result.output,
      错误信息: result.error || '-',
      时间戳: result.timestamp
    }));

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `batch_command_results_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
    link.click();
    URL.revokeObjectURL(url);
    message.success('执行结果已导出');
  };

  // 状态图标映射
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <ClockCircleOutlined style={{ color: '#faad14' }} />;
      case 'running':
        return <Spin size="small" />;
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      case 'timeout':
        return <ExclamationCircleOutlined style={{ color: '#fa8c16' }} />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  // 状态标签映射
  const getStatusTag = (status: string) => {
    const statusMap = {
      pending: { color: 'default', text: '等待中' },
      running: { color: 'processing', text: '执行中' },
      success: { color: 'success', text: '成功' },
      error: { color: 'error', text: '失败' },
      timeout: { color: 'warning', text: '超时' }
    };
    const config = statusMap[status as keyof typeof statusMap] || statusMap.pending;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  return (
    <Modal
      title={
        <Space>
          <CodeOutlined />
          <span>批量终端管理</span>
          <Tag color="blue">{selectedServers.length} 台服务器</Tag>
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      width={1200}
      footer={null}
      destroyOnClose
    >
      <div style={{ maxHeight: '80vh', overflowY: 'auto' }}>
        {/* 命令输入区域 */}
        <Card 
          title="命令输入" 
          size="small" 
          style={{ marginBottom: 16 }}
          extra={
            <Space>
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={handleExecuteCommand}
                loading={isExecuting}
                disabled={!command.trim() || selectedServers.length === 0}
              >
                执行命令
              </Button>
              {isExecuting && (
                <Button
                  danger
                  icon={<StopOutlined />}
                  onClick={handleStopExecution}
                >
                  停止执行
                </Button>
              )}
              <Button
                icon={<DeleteOutlined />}
                onClick={handleClearResults}
                disabled={isExecuting}
              >
                清空结果
              </Button>
            </Space>
          }
        >
          <TextArea
            value={command}
            onChange={(e) => setCommand(e.target.value)}
            placeholder="请输入要在所有选中服务器上执行的命令，例如：ls -la, df -h, ps aux 等"
            rows={3}
            disabled={isExecuting}
          />
          <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
            <Text type="secondary">
              提示：命令将在所有选中的服务器上并行执行，请确保命令的安全性。支持大部分Linux/Unix命令。
            </Text>
          </div>
        </Card>

        {/* 执行统计 */}
        {results.length > 0 && (
          <Card title="执行统计" size="small" style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={4}>
                <Statistic title="总计" value={stats.total} />
              </Col>
              <Col span={4}>
                <Statistic 
                  title="等待中" 
                  value={stats.pending} 
                  valueStyle={{ color: '#faad14' }}
                />
              </Col>
              <Col span={4}>
                <Statistic 
                  title="执行中" 
                  value={stats.running} 
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={4}>
                <Statistic 
                  title="成功" 
                  value={stats.success} 
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={4}>
                <Statistic 
                  title="失败" 
                  value={stats.error} 
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
              <Col span={4}>
                <Statistic 
                  title="超时" 
                  value={stats.timeout} 
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Col>
            </Row>
            {stats.total > 0 && (
              <div style={{ marginTop: 16 }}>
                <Progress
                  percent={Math.round(((stats.success + stats.error + stats.timeout) / stats.total) * 100)}
                  status={isExecuting ? 'active' : 'normal'}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>
            )}
          </Card>
        )}

        {/* 执行结果 */}
        {results.length > 0 && (
          <Card 
            title="执行结果" 
            size="small"
            extra={
              <Space>
                <Button
                  icon={<DownloadOutlined />}
                  onClick={handleExportResults}
                  size="small"
                >
                  导出结果
                </Button>
              </Space>
            }
          >
            <Collapse>
              {results.map((result) => (
                <Panel
                  key={result.serverId}
                  header={
                    <Space>
                      {getStatusIcon(result.status)}
                      <Text strong>{result.serverName}</Text>
                      <Text type="secondary">({result.serverIp})</Text>
                      {getStatusTag(result.status)}
                      {result.executionTime && (
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          {result.executionTime}ms
                        </Text>
                      )}
                    </Space>
                  }
                >
                  {result.output && (
                    <div style={{ marginBottom: 12 }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                        <Text strong>输出内容:</Text>
                        <Button
                          size="small"
                          icon={<CopyOutlined />}
                          onClick={() => handleCopyOutput(result.output)}
                        >
                          复制
                        </Button>
                      </div>
                      <div
                        style={{
                          background: '#f6f8fa',
                          border: '1px solid #e1e4e8',
                          borderRadius: '6px',
                          padding: '12px',
                          fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                          fontSize: '12px',
                          whiteSpace: 'pre-wrap',
                          maxHeight: '300px',
                          overflowY: 'auto'
                        }}
                      >
                        {result.output}
                      </div>
                    </div>
                  )}
                  
                  {result.error && (
                    <Alert
                      message="执行错误"
                      description={result.error}
                      type="error"
                      showIcon
                      style={{ marginTop: 8 }}
                    />
                  )}
                  
                  {result.timestamp && (
                    <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                      执行时间: {new Date(result.timestamp).toLocaleString()}
                    </div>
                  )}
                </Panel>
              ))}
            </Collapse>
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default BatchTerminalManager;
