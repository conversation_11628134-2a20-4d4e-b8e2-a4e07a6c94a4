import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Switch,
  Space,
  Typography,
  Alert,
  Row,
  Col,
  App,
  But<PERSON>,
  Spin,
  Card
} from 'antd';
import { DesktopOutlined, ReloadOutlined, CheckCircleOutlined, ExclamationCircleOutlined, SettingOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface ServerFormProps {
  visible: boolean;
  mode: 'create' | 'edit';
  initialValues?: any;
  onSuccess: (data: any) => void;
  onCancel: () => void;
}

const ServerForm: React.FC<ServerFormProps> = ({
  visible,
  mode,
  initialValues,
  onSuccess,
  onCancel
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [serverInfo, setServerInfo] = useState<any>(null);
  const [lastRefreshTime, setLastRefreshTime] = useState<string | null>(null);
  const [sshConfigChanged, setSshConfigChanged] = useState(false);
  const [sshConfigs, setSshConfigs] = useState<any[]>([]);
  const [loadingSshConfigs, setLoadingSshConfigs] = useState(false);
  const [departments, setDepartments] = useState<string[]>([]);

  // 获取服务器配置信息
  const fetchServerInfo = async (serverData?: any) => {
    if (!serverData && mode === 'create') {
      // 创建模式下需要先有基本信息
      const values = await form.validateFields(['name', 'ipAddress', 'sshUsername', 'sshAuthType']);
      if (values.sshAuthType === 'password' && !values.sshPassword) {
        message.error('请先填写SSH密码');
        return;
      }
      if (values.sshAuthType === 'key' && !values.sshPrivateKey) {
        message.error('请先填写SSH私钥');
        return;
      }
      serverData = {
        ...values,
        sshPort: values.sshPort || 22,
        sshPassword: values.sshAuthType === 'password' ? values.sshPassword : undefined,
        sshPrivateKey: values.sshAuthType === 'key' ? values.sshPrivateKey : undefined,
        sshKeyPassphrase: values.sshAuthType === 'key' ? values.sshKeyPassphrase : undefined
      };
    }

    if (!serverData) {
      message.error('无法获取服务器信息，请检查配置');
      return;
    }

    setRefreshing(true);
    try {
      const response = await fetch('http://localhost:3001/api/v1/servers/detect-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(serverData),
      });

      const result = await response.json();

      if (result.success) {
        setServerInfo(result.data);
        setLastRefreshTime(new Date().toLocaleString());

        // 自动填充检测到的配置信息
        if (result.data.systemInfo) {
          const currentValues = form.getFieldsValue();
          form.setFieldsValue({
            ...currentValues,
            cpu: result.data.systemInfo.cpu || currentValues.cpu,
            memory: result.data.systemInfo.memory || currentValues.memory,
            storage: result.data.systemInfo.storage || currentValues.storage,
            os: result.data.systemInfo.os || currentValues.os,
          });
        }

        message.success('服务器配置信息获取成功！');
        setSshConfigChanged(false);
      } else {
        message.error(result.message || '获取服务器配置信息失败');
      }
    } catch (error) {
      console.error('获取服务器配置信息失败:', error);
      message.error('获取服务器配置信息失败，请检查网络连接');
    } finally {
      setRefreshing(false);
    }
  };

  // 手动刷新服务器信息
  const handleRefreshServerInfo = () => {
    if (mode === 'edit' && initialValues) {
      fetchServerInfo(initialValues);
    } else {
      fetchServerInfo();
    }
  };

  // 加载SSH配置列表
  const loadSshConfigs = async () => {
    setLoadingSshConfigs(true);
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/ssh-configs');
      const result = await response.json();
      if (result.success) {
        setSshConfigs(result.data.filter((config: any) => config.is_active));
      }
    } catch (error) {
      console.error('加载SSH配置失败:', error);
    } finally {
      setLoadingSshConfigs(false);
    }
  };

  // 加载部门列表
  const loadDepartments = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/departments');
      const result = await response.json();
      if (result.success) {
        setDepartments(result.data);
      }
    } catch (error) {
      console.error('加载部门列表失败:', error);
      // 使用默认部门列表
      setDepartments(['技术部', '运维部', '产品部', '市场部', '财务部', '人事部', '行政部']);
    }
  };

  // 选择SSH配置
  const handleSelectSshConfig = async (configId: number) => {
    if (!configId) {
      // 清空SSH配置
      form.setFieldsValue({
        sshUsername: '',
        sshAuthType: 'password',
        sshPassword: '',
        sshPrivateKey: '',
        sshKeyPassphrase: ''
      });
      setSshConfigChanged(true);
      return;
    }

    try {
      const response = await fetch(`http://localhost:3001/api/v1/settings/ssh-configs/${configId}`);
      const result = await response.json();
      if (result.success) {
        const config = result.data;
        form.setFieldsValue({
          sshUsername: config.username,
          sshAuthType: config.authType,
          sshPassword: config.authType === 'password' ? config.password : '',
          sshPrivateKey: config.authType === 'key' ? config.privateKey : '',
          sshKeyPassphrase: config.authType === 'key' ? config.keyPassphrase : ''
        });
        setSshConfigChanged(true);
        message.success('SSH配置已应用');
      }
    } catch (error) {
      console.error('获取SSH配置详情失败:', error);
      message.error('获取SSH配置详情失败');
    }
  };

  // 重置表单
  useEffect(() => {
    if (visible) {
      // 加载SSH配置列表和部门列表
      loadSshConfigs();
      loadDepartments();

      if (mode === 'edit' && initialValues) {
        form.setFieldsValue({
          ...initialValues,
          expireDate: initialValues.expireDate ? dayjs(initialValues.expireDate) : null,
          // 新增字段
          instanceId: initialValues.instanceId,
          type: initialValues.type || 'cloud',
          region: initialValues.region,
          uptime: initialValues.uptime,
          department: initialValues.department,
          // 展开规格配置
          cpu: initialValues.specifications?.cpu,
          memory: initialValues.specifications?.memory,
          storage: initialValues.specifications?.storage,
          bandwidth: initialValues.specifications?.bandwidth,
          os: initialValues.specifications?.os,
          // 展开告警阈值
          cpuThreshold: initialValues.alertThresholds?.cpuUsage,
          memoryThreshold: initialValues.alertThresholds?.memoryUsage,
          diskThreshold: initialValues.alertThresholds?.diskUsage,
          networkThreshold: initialValues.alertThresholds?.networkUsage,
          // SSH配置
          sshPort: initialValues.sshPort || 22,
          sshUsername: initialValues.sshUsername,
          sshAuthType: initialValues.sshAuthType || 'password',
          sshPassword: initialValues.sshPassword,
          sshPrivateKey: initialValues.sshPrivateKey,
          sshKeyPassphrase: initialValues.sshKeyPassphrase
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, mode, initialValues, form]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 转换数据格式
      const serverData = {
        ...values,
        expireDate: values.expireDate ? values.expireDate.format('YYYY-MM-DD') : null,
        // 新增字段
        instanceId: values.instanceId,
        type: values.type || 'cloud',
        region: values.region,
        uptime: values.uptime,
        specifications: {
          cpu: values.cpu,
          memory: values.memory,
          storage: values.storage,
          bandwidth: values.bandwidth,
          os: values.os
        },
        alertThresholds: {
          cpuUsage: values.cpuThreshold || 80,
          memoryUsage: values.memoryThreshold || 85,
          diskUsage: values.diskThreshold || 90,
          networkUsage: values.networkThreshold || 80
        },
        // SSH配置
        sshPort: values.sshPort || 22,
        sshUsername: values.sshUsername,
        sshAuthType: values.sshAuthType || 'password',
        sshPassword: values.sshAuthType === 'password' ? values.sshPassword : undefined,
        sshPrivateKey: values.sshAuthType === 'key' ? values.sshPrivateKey : undefined,
        sshKeyPassphrase: values.sshAuthType === 'key' ? values.sshKeyPassphrase : undefined
      };

      // 调用真实API
      const url = mode === 'create'
        ? 'http://localhost:3001/api/v1/servers'
        : `http://localhost:3001/api/v1/servers/${initialValues?.id}`;

      const method = mode === 'create' ? 'POST' : 'PUT';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(serverData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '操作失败');
      }

      const result = await response.json();

      message.success(mode === 'create' ? '服务器创建成功！' : '服务器更新成功！');

      // 保存成功后自动获取服务器配置信息
      if (result.data && (mode === 'create' || sshConfigChanged)) {
        setTimeout(() => {
          fetchServerInfo(result.data);
        }, 1000);
      }

      onSuccess(result.data || serverData);
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error(error.message || '操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 取消操作
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={
        <Space>
          <DesktopOutlined />
          <span>{mode === 'create' ? '新增服务器' : '编辑服务器'}</span>
        </Space>
      }
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={900}
      style={{ top: 20 }}
      okText={mode === 'create' ? '创建' : '保存'}
      cancelText="取消"
      destroyOnHidden
    >
      <Alert
        message={mode === 'create' ? '新增服务器' : '编辑服务器信息'}
        description="请填写服务器的基本信息和配置参数，带 * 的字段为必填项。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Form
        form={form}
        layout="vertical"
        initialValues={{
          status: 'active',
          monitoringEnabled: true,
          type: 'cloud',
          cpuThreshold: 80,
          memoryThreshold: 85,
          diskThreshold: 90,
          networkThreshold: 80,
          sshPort: 22,
          sshAuthType: 'password'
        }}
      >
        {/* 基本信息 */}
        <div style={{ marginBottom: 12 }}>
          <Text strong>基本信息</Text>
        </div>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="服务器名称"
              rules={[
                { required: true, message: '请输入服务器名称' },
                { min: 2, max: 50, message: '服务器名称长度应在2-50个字符之间' }
              ]}
            >
              <Input placeholder="例如：Web-Server-01" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="ipAddress"
              label="IP地址"
              rules={[
                { required: true, message: '请输入IP地址' },
                { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: 'IP地址格式不正确' }
              ]}
            >
              <Input placeholder="例如：*************" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="provider"
              label="服务商"
              rules={[{ required: true, message: '请选择服务商' }]}
            >
              <Select placeholder="请选择服务商">
                <Option value="阿里云">阿里云</Option>
                <Option value="腾讯云">腾讯云</Option>
                <Option value="华为云">华为云</Option>
                <Option value="百度云">百度云</Option>
                <Option value="AWS">AWS</Option>
                <Option value="Azure">Azure</Option>
                <Option value="自建">自建</Option>
                <Option value="其他">其他</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="location"
              label="机房位置"
              rules={[{ required: true, message: '请输入机房位置' }]}
            >
              <Input placeholder="例如：北京-阿里云" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="department"
              label="所属部门"
            >
              <Select
                placeholder="请选择或输入部门"
                allowClear
                showSearch
                mode="combobox"
                filterOption={(input, option) =>
                  (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {departments.map(dept => (
                  <Option key={dept} value={dept}>{dept}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="instanceId"
              label="实例ID"
            >
              <Input placeholder="例如：i-bp1234567890abcdef" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="type"
              label="服务器类型"
            >
              <Input placeholder="例如：云服务器、物理机、虚拟机、容器等" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="region"
              label="地区/国家"
            >
              <Input placeholder="例如：cn-beijing, us-west-1" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="uptime"
              label="启用时间(秒)"
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="例如：86400 (1天)"
                min={0}
                max={999999999}
                formatter={value => {
                  if (!value) return '';
                  const seconds = parseInt(value.toString());
                  const days = Math.floor(seconds / 86400);
                  const hours = Math.floor((seconds % 86400) / 3600);
                  return days > 0 ? `${value} (${days}天${hours}小时)` : `${value} (${hours}小时)`;
                }}
                parser={value => parseInt(value!.replace(/[^\d]/g, '')) || 0}
              />
            </Form.Item>
          </Col>
        </Row>

        {/* 配置信息 */}
        <div style={{ marginBottom: 12, marginTop: 16 }}>
          <Text strong>配置信息</Text>
        </div>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="cpu"
              label="CPU配置"
              initialValue="待检测"
            >
              <Input placeholder="例如：4核 Intel Xeon (可通过刷新配置自动获取)" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="memory"
              label="内存配置"
              initialValue="待检测"
            >
              <Input placeholder="例如：8GB DDR4 (可通过刷新配置自动获取)" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="storage"
              label="存储配置"
              initialValue="待检测"
            >
              <Input placeholder="例如：100GB SSD (可通过刷新配置自动获取)" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="bandwidth"
              label="带宽配置"
              initialValue="待检测"
            >
              <Input placeholder="例如：10Mbps (可通过刷新配置自动获取)" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="os"
          label="操作系统"
          initialValue="待检测"
        >
          <Input placeholder="例如：Ubuntu 20.04 LTS (可通过刷新配置自动获取)" />
        </Form.Item>

        {/* 费用和到期信息 */}
        <div style={{ marginBottom: 12, marginTop: 16 }}>
          <Text strong>费用和到期信息</Text>
        </div>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="expireDate"
              label="到期时间"
            >
              <DatePicker style={{ width: '100%' }} placeholder="选择到期时间" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="renewalFee"
              label="续费金额"
              rules={[
                {
                  validator: (_, value) => {
                    if (value === undefined || value === null || value === '') {
                      return Promise.resolve();
                    }
                    const numValue = typeof value === 'string' ? parseFloat(value) : value;
                    if (isNaN(numValue) || numValue < 0) {
                      return Promise.reject(new Error('续费金额不能为负数'));
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                placeholder="请输入续费金额"
                prefix="¥"
                precision={2}
                min={0}
                max={999999}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => parseFloat(value!.replace(/\$\s?|(,*)/g, '')) || 0}
              />
            </Form.Item>
          </Col>
        </Row>

        {/* 监控设置 */}
        <div style={{ marginBottom: 12, marginTop: 16 }}>
          <Text strong>监控设置</Text>
        </div>

        <Row gutter={16}>
          <Col span={6}>
            <Form.Item
              name="cpuThreshold"
              label="CPU告警阈值(%)"
            >
              <InputNumber
                style={{ width: '100%' }}
                min={1}
                max={100}
                placeholder="80"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="memoryThreshold"
              label="内存告警阈值(%)"
            >
              <InputNumber
                style={{ width: '100%' }}
                min={1}
                max={100}
                placeholder="85"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="diskThreshold"
              label="磁盘告警阈值(%)"
            >
              <InputNumber
                style={{ width: '100%' }}
                min={1}
                max={100}
                placeholder="90"
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="networkThreshold"
              label="网络告警阈值(%)"
            >
              <InputNumber
                style={{ width: '100%' }}
                min={1}
                max={100}
                placeholder="80"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="status"
              label="状态"
              initialValue="active"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <Select>
                <Option value="active">正常</Option>
                <Option value="inactive">停用</Option>
                <Option value="maintenance">维护中</Option>
                <Option value="expired">已过期</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="monitoringEnabled"
              label="启用监控"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>

        {/* SSH配置 */}
        <div style={{ marginBottom: 12, marginTop: 16 }}>
          <Space>
            <Text strong>SSH连接配置</Text>
            <Text type="secondary">
              (用于服务器监控和管理)
            </Text>
            {mode === 'edit' && initialValues && (
              <Button
                type="link"
                size="small"
                icon={<ReloadOutlined />}
                loading={refreshing}
                onClick={handleRefreshServerInfo}
              >
                刷新配置信息
              </Button>
            )}
          </Space>
        </div>

        {/* SSH配置选择器 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={24}>
            <Form.Item
              label={
                <Space>
                  <SettingOutlined />
                  <span>选择SSH配置</span>
                  <Text type="secondary">(可选择系统设置中保存的SSH配置)</Text>
                </Space>
              }
            >
              <Select
                placeholder="选择已保存的SSH配置或手动填写"
                allowClear
                loading={loadingSshConfigs}
                onChange={handleSelectSshConfig}
                style={{ width: '100%' }}
              >
                {sshConfigs.map(config => (
                  <Option key={config.id} value={config.id}>
                    <Space>
                      <Text strong>{config.name}</Text>
                      <Text type="secondary">({config.username}@{config.auth_type === 'password' ? '密码' : '密钥'})</Text>
                      {config.description && (
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          - {config.description}
                        </Text>
                      )}
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="sshPort"
              label="SSH端口"
              initialValue={22}
            >
              <InputNumber
                min={1}
                max={65535}
                placeholder="22"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="sshUsername"
              label="SSH用户名"
              rules={[{ required: true, message: '请输入SSH用户名' }]}
            >
              <Input
                placeholder="root"
                autoComplete="username"
                onChange={() => setSshConfigChanged(true)}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="sshAuthType"
              label="认证方式"
              initialValue="password"
              rules={[{ required: true, message: '请选择认证方式' }]}
            >
              <Select onChange={() => setSshConfigChanged(true)}>
                <Option value="password">密码认证</Option>
                <Option value="key">密钥认证</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
          prevValues.sshAuthType !== currentValues.sshAuthType
        }>
          {({ getFieldValue }) => {
            const authType = getFieldValue('sshAuthType');

            if (authType === 'password') {
              return (
                <Form.Item
                  name="sshPassword"
                  label="SSH密码"
                  rules={[{ required: true, message: '请输入SSH密码' }]}
                >
                  <Input.Password
                    placeholder="请输入SSH密码"
                    autoComplete="new-password"
                    onChange={() => setSshConfigChanged(true)}
                  />
                </Form.Item>
              );
            } else if (authType === 'key') {
              return (
                <>
                  <Form.Item
                    name="sshPrivateKey"
                    label="SSH私钥"
                    rules={[{ required: true, message: '请输入SSH私钥内容' }]}
                  >
                    <TextArea
                      rows={6}
                      placeholder="请粘贴SSH私钥内容，例如：&#10;-----BEGIN OPENSSH PRIVATE KEY-----&#10;b3BlbnNzaC1rZXktdjEAAAAABG5vbmUAAAAEbm9uZQAAAAAAAAABAAAA...&#10;-----END OPENSSH PRIVATE KEY-----"
                      style={{ fontFamily: 'monospace', fontSize: '12px' }}
                      onChange={() => setSshConfigChanged(true)}
                    />
                  </Form.Item>
                  <Form.Item
                    name="sshKeyPassphrase"
                    label="密钥密码"
                    extra="如果您的SSH密钥设置了密码保护，请在此输入"
                  >
                    <Input.Password
                      placeholder="如果密钥有密码保护，请输入密码"
                      autoComplete="new-password"
                      onChange={() => setSshConfigChanged(true)}
                    />
                  </Form.Item>
                </>
              );
            }
            return null;
          }}
        </Form.Item>

        <Form.Item
          name="notes"
          label="备注"
        >
          <TextArea
            rows={2}
            placeholder="请输入备注信息，如用途、重要说明等"
            maxLength={500}
            showCount
          />
        </Form.Item>

        {/* 服务器配置信息显示 */}
        {serverInfo && (
          <Card
            title={
              <Space>
                <CheckCircleOutlined style={{ color: '#52c41a' }} />
                <span>服务器配置信息</span>
                {lastRefreshTime && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    (更新时间: {lastRefreshTime})
                  </Text>
                )}
              </Space>
            }
            size="small"
            style={{ marginTop: 16 }}
          >
            <Row gutter={16}>
              {serverInfo.systemInfo?.cpu && (
                <Col span={12}>
                  <Text strong>CPU: </Text>
                  <Text>{serverInfo.systemInfo.cpu}</Text>
                </Col>
              )}
              {serverInfo.systemInfo?.memory && (
                <Col span={12}>
                  <Text strong>内存: </Text>
                  <Text>{serverInfo.systemInfo.memory}</Text>
                </Col>
              )}
              {serverInfo.systemInfo?.storage && (
                <Col span={12}>
                  <Text strong>存储: </Text>
                  <Text>{serverInfo.systemInfo.storage}</Text>
                </Col>
              )}
              {serverInfo.systemInfo?.os && (
                <Col span={12}>
                  <Text strong>操作系统: </Text>
                  <Text>{serverInfo.systemInfo.os}</Text>
                </Col>
              )}
            </Row>
            {serverInfo.loadInfo && (
              <Row gutter={16} style={{ marginTop: 8 }}>
                <Col span={6}>
                  <Text strong>CPU使用率: </Text>
                  <Text>{serverInfo.loadInfo.cpuUsage}%</Text>
                </Col>
                <Col span={6}>
                  <Text strong>内存使用率: </Text>
                  <Text>{serverInfo.loadInfo.memoryUsage}%</Text>
                </Col>
                <Col span={6}>
                  <Text strong>磁盘使用率: </Text>
                  <Text>{serverInfo.loadInfo.diskUsage}%</Text>
                </Col>
                <Col span={6}>
                  <Text strong>运行时间: </Text>
                  <Text>{Math.floor(serverInfo.loadInfo.uptime / 86400)}天</Text>
                </Col>
              </Row>
            )}
          </Card>
        )}

        {sshConfigChanged && (
          <Alert
            message="SSH配置已修改"
            description="保存后将自动获取最新的服务器配置信息"
            type="info"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </Form>
    </Modal>
  );
};

export default ServerForm;
