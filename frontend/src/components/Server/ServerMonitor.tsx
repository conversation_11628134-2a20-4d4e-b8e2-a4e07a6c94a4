import React, { useState, useEffect } from 'react';
import {
  Modal,
  Card,
  Row,
  Col,
  Progress,
  Statistic,
  Typography,
  Space,
  Button,
  Table,
  Tag,
  Alert,
  Spin,
  message
} from 'antd';
import {
  MonitorOutlined,
  <PERSON>boltOutlined,
  DatabaseOutlined,
  CloudOutlined,
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  LineChartOutlined
} from '@ant-design/icons';
import { Server } from '../../types';
import { request } from '../../services/api';

const { Title, Text } = Typography;

interface ServerMonitorProps {
  visible: boolean;
  server: Server | null;
  onClose: () => void;
}

interface MonitorData {
  timestamp: string;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkIn: number | string;
  networkOut: number | string;
}

const ServerMonitor: React.FC<ServerMonitorProps> = ({
  visible,
  server,
  onClose
}) => {
  const [loading, setLoading] = useState(false);
  const [monitorData, setMonitorData] = useState<MonitorData[]>([]);
  const [realTimeData, setRealTimeData] = useState<MonitorData | null>(null);
  const [performanceTestLoading, setPerformanceTestLoading] = useState(false);
  const [connectionTestLoading, setConnectionTestLoading] = useState(false);
  const [performanceResult, setPerformanceResult] = useState<any>(null);
  const [connectionResult, setConnectionResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [suggestions, setSuggestions] = useState<string[]>([]);

  // 获取监控数据
  const fetchMonitorData = async () => {
    if (!server) return;

    setLoading(true);
    try {
      // 使用统一的API服务，自动包含认证头
      const result = await request.get(`/servers/${server.id}/monitor`);

      if (result.success) {
        setMonitorData(result.data.historical);
        setRealTimeData(result.data.realTime);
        setError(null);
        setSuggestions([]);
      } else {
        setError(result.message);
        setSuggestions(result.suggestions || []);
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('获取监控数据失败:', error);
      // 不使用模拟数据，显示错误状态
      setMonitorData([]);
      setRealTimeData(null);
      setError('无法获取服务器监控数据，请检查服务器连接和SSH配置');
      setSuggestions([
        '检查服务器IP地址和SSH端口是否正确',
        '确认SSH用户名和密码/密钥是否正确',
        '检查服务器防火墙设置',
        '确认服务器SSH服务是否正常运行'
      ]);
      message.error('获取监控数据失败，请检查服务器连接');
    } finally {
      setLoading(false);
    }
  };

  // 性能测试
  const runPerformanceTest = async () => {
    if (!server) return;

    setPerformanceTestLoading(true);
    try {
      // 使用统一的API服务，自动包含认证头
      const result = await request.post(`/servers/${server.id}/performance-test`);

      if (result.success) {
        setPerformanceResult(result.data);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('性能测试失败:', error);
    } finally {
      setPerformanceTestLoading(false);
    }
  };

  // 连接测试
  const runConnectionTest = async () => {
    if (!server) return;

    setConnectionTestLoading(true);
    try {
      // 使用统一的API服务，自动包含认证头
      const result = await request.post(`/servers/${server.id}/connection-test`);

      if (result.success) {
        setConnectionResult(result.data);
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('连接测试失败:', error);
    } finally {
      setConnectionTestLoading(false);
    }
  };

  useEffect(() => {
    if (visible && server) {
      fetchMonitorData();
    }
  }, [visible, server]);

  if (!server) return null;

  // 获取负载状态
  const getLoadStatus = (usage: number, threshold: number) => {
    if (usage >= threshold) {
      return { status: 'exception', color: '#ff4d4f', level: '危险' };
    } else if (usage >= threshold * 0.8) {
      return { status: 'active', color: '#faad14', level: '警告' };
    } else {
      return { status: 'success', color: '#52c41a', level: '正常' };
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 180,
      render: (timestamp: string) => new Date(timestamp).toLocaleString()
    },
    {
      title: 'CPU使用率',
      dataIndex: 'cpuUsage',
      key: 'cpuUsage',
      width: 120,
      render: (usage: number) => {
        const status = getLoadStatus(usage, server.alertThresholds?.cpuUsage || 80);
        return (
          <Space>
            <Progress
              percent={usage}
              size="small"
              strokeColor={status.color}
              showInfo={false}
              style={{ width: 60 }}
            />
            <span style={{ color: status.color }}>{usage}%</span>
          </Space>
        );
      }
    },
    {
      title: '内存使用率',
      dataIndex: 'memoryUsage',
      key: 'memoryUsage',
      width: 120,
      render: (usage: number) => {
        const status = getLoadStatus(usage, server.alertThresholds?.memoryUsage || 85);
        return (
          <Space>
            <Progress
              percent={usage}
              size="small"
              strokeColor={status.color}
              showInfo={false}
              style={{ width: 60 }}
            />
            <span style={{ color: status.color }}>{usage}%</span>
          </Space>
        );
      }
    },
    {
      title: '磁盘使用率',
      dataIndex: 'diskUsage',
      key: 'diskUsage',
      width: 120,
      render: (usage: number) => {
        const status = getLoadStatus(usage, server.alertThresholds?.diskUsage || 90);
        return (
          <Space>
            <Progress
              percent={usage}
              size="small"
              strokeColor={status.color}
              showInfo={false}
              style={{ width: 60 }}
            />
            <span style={{ color: status.color }}>{usage}%</span>
          </Space>
        );
      }
    },
    {
      title: '网络流量',
      key: 'network',
      width: 120,
      render: (record: MonitorData) => {
        const networkIn = typeof record.networkIn === 'string' ? parseFloat(record.networkIn) : record.networkIn;
        const networkOut = typeof record.networkOut === 'string' ? parseFloat(record.networkOut) : record.networkOut;
        return (
          <div>
            <div style={{ fontSize: 12 }}>入: {networkIn.toFixed(1)} MB/s</div>
            <div style={{ fontSize: 12 }}>出: {networkOut.toFixed(1)} MB/s</div>
          </div>
        );
      }
    }
  ];

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <MonitorOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          服务器监控 - {server.name}
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="refresh" icon={<ReloadOutlined />} onClick={fetchMonitorData} loading={loading}>
          刷新数据
        </Button>,
        <Button key="performance" icon={<LineChartOutlined />} onClick={runPerformanceTest} loading={performanceTestLoading}>
          性能测试
        </Button>,
        <Button key="connection" icon={<CloudOutlined />} onClick={runConnectionTest} loading={connectionTestLoading}>
          连接测试
        </Button>,
        <Button key="close" type="primary" onClick={onClose}>
          关闭
        </Button>
      ]}
      width={1200}
      style={{ top: 20 }}
    >
      <Spin spinning={loading}>
        {/* SSH连接错误提示 */}
        {error && (
          <Alert
            message="服务器监控连接失败"
            description={
              <div>
                <p>{error}</p>
                {suggestions.length > 0 && (
                  <div>
                    <p style={{ marginTop: 8, marginBottom: 4, fontWeight: 'bold' }}>解决建议：</p>
                    <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                      {suggestions.map((suggestion, index) => (
                        <li key={index}>{suggestion}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            }
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
            action={
              <Button size="small" onClick={fetchMonitorData}>
                重试连接
              </Button>
            }
          />
        )}

        {/* 实时状态 */}
        {realTimeData && (
          <div style={{ marginBottom: 24 }}>
            <Title level={5} style={{ marginBottom: 16 }}>
              <LineChartOutlined style={{ marginRight: 8 }} />
              实时状态
            </Title>
            
            <Row gutter={16}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="CPU使用率"
                    value={realTimeData.cpuUsage}
                    suffix="%"
                    valueStyle={{ 
                      color: getLoadStatus(realTimeData.cpuUsage, server.alertThresholds?.cpuUsage || 80).color 
                    }}
                    prefix={<ThunderboltOutlined />}
                  />
                  <Progress
                    percent={realTimeData.cpuUsage}
                    strokeColor={getLoadStatus(realTimeData.cpuUsage, server.alertThresholds?.cpuUsage || 80).color}
                    size="small"
                    style={{ marginTop: 8 }}
                  />
                  <div style={{ marginTop: 4, fontSize: 12, color: '#666' }}>
                    阈值: {server.alertThresholds?.cpuUsage || 80}%
                  </div>
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="内存使用率"
                    value={realTimeData.memoryUsage}
                    suffix="%"
                    valueStyle={{ 
                      color: getLoadStatus(realTimeData.memoryUsage, server.alertThresholds?.memoryUsage || 85).color 
                    }}
                    prefix={<DatabaseOutlined />}
                  />
                  <Progress
                    percent={realTimeData.memoryUsage}
                    strokeColor={getLoadStatus(realTimeData.memoryUsage, server.alertThresholds?.memoryUsage || 85).color}
                    size="small"
                    style={{ marginTop: 8 }}
                  />
                  <div style={{ marginTop: 4, fontSize: 12, color: '#666' }}>
                    阈值: {server.alertThresholds?.memoryUsage || 85}%
                  </div>
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="磁盘使用率"
                    value={realTimeData.diskUsage}
                    suffix="%"
                    valueStyle={{ 
                      color: getLoadStatus(realTimeData.diskUsage, server.alertThresholds?.diskUsage || 90).color 
                    }}
                    prefix={<CloudOutlined />}
                  />
                  <Progress
                    percent={realTimeData.diskUsage}
                    strokeColor={getLoadStatus(realTimeData.diskUsage, server.alertThresholds?.diskUsage || 90).color}
                    size="small"
                    style={{ marginTop: 8 }}
                  />
                  <div style={{ marginTop: 4, fontSize: 12, color: '#666' }}>
                    阈值: {server.alertThresholds?.diskUsage || 90}%
                  </div>
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: 14, color: '#666', marginBottom: 8 }}>网络流量</div>
                    <div style={{ fontSize: 20, fontWeight: 600, color: '#1890ff' }}>
                      ↓ {(typeof realTimeData.networkIn === 'string' ? parseFloat(realTimeData.networkIn) : realTimeData.networkIn).toFixed(1)} MB/s
                    </div>
                    <div style={{ fontSize: 20, fontWeight: 600, color: '#52c41a' }}>
                      ↑ {(typeof realTimeData.networkOut === 'string' ? parseFloat(realTimeData.networkOut) : realTimeData.networkOut).toFixed(1)} MB/s
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>
          </div>
        )}

        {/* 告警状态 */}
        {realTimeData && (
          <div style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              {[
                { name: 'CPU', value: realTimeData.cpuUsage, threshold: server.alertThresholds?.cpuUsage || 80 },
                { name: '内存', value: realTimeData.memoryUsage, threshold: server.alertThresholds?.memoryUsage || 85 },
                { name: '磁盘', value: realTimeData.diskUsage, threshold: server.alertThresholds?.diskUsage || 90 }
              ].map(item => {
                const status = getLoadStatus(item.value, item.threshold);
                if (status.level !== '正常') {
                  return (
                    <Col span={8} key={item.name}>
                      <Alert
                        message={`${item.name}使用率${status.level}`}
                        description={`当前${item.value}%，阈值${item.threshold}%`}
                        type={status.level === '危险' ? 'error' : 'warning'}
                        showIcon
                        icon={status.level === '危险' ? <WarningOutlined /> : <CheckCircleOutlined />}
                      />
                    </Col>
                  );
                }
                return null;
              }).filter(Boolean)}
            </Row>
          </div>
        )}

        {/* 性能测试结果 */}
        {performanceResult && (
          <Card title="性能测试结果" style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="网络延迟"
                    value={performanceResult.ping?.avg}
                    suffix="ms"
                    prefix={<CloudOutlined />}
                  />
                  <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                    最小: {performanceResult.ping?.min}ms | 最大: {performanceResult.ping?.max}ms
                  </div>
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="下载带宽"
                    value={performanceResult.bandwidth?.download}
                    suffix="Mbps"
                    prefix={<ThunderboltOutlined />}
                  />
                  <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                    上传: {performanceResult.bandwidth?.upload}Mbps
                  </div>
                </Card>
              </Col>
              <Col span={8}>
                <Card size="small">
                  <Statistic
                    title="磁盘读取"
                    value={performanceResult.diskIO?.sequentialRead}
                    suffix="MB/s"
                    prefix={<DatabaseOutlined />}
                  />
                  <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                    写入: {performanceResult.diskIO?.sequentialWrite}MB/s
                  </div>
                </Card>
              </Col>
            </Row>
          </Card>
        )}

        {/* 连接测试结果 */}
        {connectionResult && (
          <Card title="连接测试结果" style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>
                    {connectionResult.details?.ping?.success ? (
                      <CheckCircleOutlined style={{ color: '#52c41a' }} />
                    ) : (
                      <WarningOutlined style={{ color: '#ff4d4f' }} />
                    )}
                    <span style={{ marginLeft: 8 }}>Ping测试</span>
                  </div>
                  <div style={{ fontSize: 12, color: '#666' }}>
                    {connectionResult.details?.ping?.time}ms
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>
                    {connectionResult.details?.ssh?.success ? (
                      <CheckCircleOutlined style={{ color: '#52c41a' }} />
                    ) : (
                      <WarningOutlined style={{ color: '#ff4d4f' }} />
                    )}
                    <span style={{ marginLeft: 8 }}>SSH连接</span>
                  </div>
                  <div style={{ fontSize: 12, color: '#666' }}>
                    {connectionResult.details?.ssh?.time}ms
                  </div>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>
                    {connectionResult.details?.http?.success ? (
                      <CheckCircleOutlined style={{ color: '#52c41a' }} />
                    ) : (
                      <WarningOutlined style={{ color: '#ff4d4f' }} />
                    )}
                    <span style={{ marginLeft: 8 }}>HTTP连接</span>
                  </div>
                  <div style={{ fontSize: 12, color: '#666' }}>
                    状态码: {connectionResult.details?.http?.statusCode}
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        )}

        {/* 历史数据 */}
        <div>
          <Title level={5} style={{ marginBottom: 16 }}>
            历史监控数据 (最近24小时)
          </Title>
          
          <Table
            columns={columns}
            dataSource={monitorData}
            rowKey="timestamp"
            size="small"
            scroll={{ y: 300 }}
            pagination={{
              pageSize: 10,
              showSizeChanger: false,
              showQuickJumper: true
            }}
          />
        </div>
      </Spin>
    </Modal>
  );
};

export default ServerMonitor;
