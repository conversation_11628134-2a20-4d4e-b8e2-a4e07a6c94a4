import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Select,
  Input,
  Button,
  Space,
  Alert,
  Typography,
  Divider,
  message,
  Popconfirm,
  Row,
  Col,
  InputNumber,
  Card
} from 'antd';
import {
  SettingOutlined,
  KeyOutlined,
  LockOutlined,
  CloudServerOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { Server } from '../../types';

const { Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface BatchSshConfigProps {
  selectedServers: Server[];
  onSuccess: () => void;
  onClearSelection: () => void;
  visible?: boolean;
  onCancel?: () => void;
}

const BatchSshConfig: React.FC<BatchSshConfigProps> = ({
  selectedServers,
  onSuccess,
  onClearSelection,
  visible = false,
  onCancel
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [operationType, setOperationType] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [sshConfigs, setSshConfigs] = useState<any[]>([]);
  const [loadingSshConfigs, setLoadingSshConfigs] = useState(false);
  const [form] = Form.useForm();

  // 当外部visible变化时，同步内部状态
  useEffect(() => {
    if (visible) {
      setOperationType(''); // 不设置默认类型，让用户选择
      setModalVisible(true);
      form.resetFields();
    }
  }, [visible, form]);

  // 操作类型配置
  const operationTypes = [
    {
      key: 'batchSshConfig',
      label: '手动配置SSH',
      icon: <SettingOutlined />,
      color: '#1890ff',
      description: '手动输入SSH连接信息并批量应用到选中服务器'
    },
    {
      key: 'batchSshFromPreset',
      label: '选择预设配置',
      icon: <KeyOutlined />,
      color: '#52c41a',
      description: '从服务器设置中选择已保存的SSH配置并批量应用'
    }
  ];

  // 加载SSH配置列表
  const loadSshConfigs = async () => {
    setLoadingSshConfigs(true);
    try {
      const response = await fetch('http://localhost:3001/api/v1/settings/ssh-configs');
      const result = await response.json();
      if (result.success) {
        setSshConfigs(result.data.filter((config: any) => config.is_active));
      }
    } catch (error) {
      console.error('加载SSH配置失败:', error);
    } finally {
      setLoadingSshConfigs(false);
    }
  };

  // 打开操作模态框
  const handleOpenModal = (type: string) => {
    setOperationType(type);
    setModalVisible(true);
    form.resetFields();
    
    if (type === 'batchSshFromPreset') {
      loadSshConfigs();
    }
  };

  // 应用预设SSH配置
  const handleSelectSshConfig = async (configId: number) => {
    if (!configId) {
      form.setFieldsValue({
        sshUsername: '',
        sshAuthType: 'password',
        sshPassword: '',
        sshPrivateKey: '',
        sshKeyPassphrase: ''
      });
      return;
    }

    try {
      const response = await fetch(`http://localhost:3001/api/v1/settings/ssh-configs/${configId}`);
      const result = await response.json();
      if (result.success) {
        const config = result.data;
        form.setFieldsValue({
          sshUsername: config.username,
          sshAuthType: config.authType,
          sshPassword: config.authType === 'password' ? config.password : '',
          sshPrivateKey: config.authType === 'key' ? config.privateKey : '',
          sshKeyPassphrase: config.authType === 'key' ? config.keyPassphrase : ''
        });
        message.success('SSH配置已应用到表单');
      }
    } catch (error) {
      console.error('获取SSH配置详情失败:', error);
      message.error('获取SSH配置详情失败');
    }
  };

  // 执行批量操作
  const handleBatchOperation = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const serverIds = selectedServers.map(s => s.id);
      
      // 构建SSH配置数据
      const sshConfigData = {
        sshPort: values.sshPort || 22,
        sshUsername: values.sshUsername,
        sshAuthType: values.sshAuthType || 'password',
        sshPassword: values.sshAuthType === 'password' ? values.sshPassword : undefined,
        sshPrivateKey: values.sshAuthType === 'key' ? values.sshPrivateKey : undefined,
        sshKeyPassphrase: values.sshAuthType === 'key' ? values.sshKeyPassphrase : undefined
      };

      // 调用批量更新API
      const response = await fetch('http://localhost:3001/api/v1/servers/batch-ssh-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serverIds,
          sshConfig: sshConfigData
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        message.success(`批量SSH配置成功，影响 ${serverIds.length} 台服务器`);
        setModalVisible(false);
        onCancel?.();
        onSuccess();
        onClearSelection();
      } else {
        message.error(result.message || '批量配置失败');
      }

    } catch (error) {
      console.error('批量SSH配置失败:', error);
      message.error('批量SSH配置失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取当前操作配置
  const currentOperation = operationTypes.find(op => op.key === operationType);

  return (
    <>

      {/* 批量SSH配置模态框 */}
      <Modal
        title={
          <Space>
            <KeyOutlined />
            批量SSH配置
          </Space>
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          onCancel?.();
        }}
        footer={[
          <Button key="cancel" onClick={() => {
            setModalVisible(false);
            onCancel?.();
          }}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={handleBatchOperation}
            disabled={!operationType}
          >
            确认配置
          </Button>
        ]}
        width={800}
      >
        {/* 操作类型选择 */}
        {!operationType && (
          <div style={{ marginBottom: 24 }}>
            <Alert
              message="请选择SSH配置方式"
              description="您可以手动输入SSH信息，或从服务器设置中选择已保存的配置"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            <Row gutter={16}>
              {operationTypes.map(operation => (
                <Col span={12} key={operation.key}>
                  <Card
                    hoverable
                    style={{
                      textAlign: 'center',
                      borderColor: '#d9d9d9',
                      cursor: 'pointer'
                    }}
                    onClick={() => handleOpenModal(operation.key)}
                  >
                    <div style={{ padding: '20px 0' }}>
                      <div style={{ fontSize: 32, color: operation.color, marginBottom: 12 }}>
                        {operation.icon}
                      </div>
                      <div style={{ fontSize: 16, fontWeight: 600, marginBottom: 8 }}>
                        {operation.label}
                      </div>
                      <div style={{ fontSize: 14, color: '#666' }}>
                        {operation.description}
                      </div>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </div>
        )}

        {/* 配置表单 */}
        {operationType && (
          <>
            <Alert
              message={currentOperation?.description}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

        <Alert
          message={`将对以下 ${selectedServers.length} 台服务器进行SSH配置`}
          description={
            <div style={{ marginTop: 8 }}>
              {selectedServers.map(server => (
                <div key={server.id} style={{ marginBottom: 4 }}>
                  <CloudServerOutlined style={{ marginRight: 8 }} />
                  <Text strong>{server.name}</Text>
                  <Text type="secondary" style={{ marginLeft: 8 }}>({server.ipAddress})</Text>
                </div>
              ))}
            </div>
          }
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            sshPort: 22,
            sshAuthType: 'password'
          }}
        >
          {/* 预设SSH配置选择 */}
          {operationType === 'batchSshFromPreset' && (
            <Form.Item
              label={
                <Space>
                  <SettingOutlined />
                  <span>选择SSH配置</span>
                  <Text type="secondary">(从系统设置中选择)</Text>
                </Space>
              }
            >
              <Select
                placeholder="选择已保存的SSH配置"
                allowClear
                loading={loadingSshConfigs}
                onChange={handleSelectSshConfig}
                style={{ width: '100%' }}
              >
                {sshConfigs.map(config => (
                  <Option key={config.id} value={config.id}>
                    <Space>
                      <Text strong>{config.name}</Text>
                      <Text type="secondary">({config.username}@{config.auth_type === 'password' ? '密码' : '密钥'})</Text>
                      {config.description && (
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          - {config.description}
                        </Text>
                      )}
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}

          {/* SSH配置表单 */}
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="sshPort"
                label="SSH端口"
                rules={[{ required: true, message: '请输入SSH端口' }]}
              >
                <InputNumber
                  min={1}
                  max={65535}
                  placeholder="22"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="sshUsername"
                label="SSH用户名"
                rules={[{ required: true, message: '请输入SSH用户名' }]}
              >
                <Input placeholder="root" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="sshAuthType"
                label="认证方式"
                rules={[{ required: true, message: '请选择认证方式' }]}
              >
                <Select>
                  <Option value="password">密码认证</Option>
                  <Option value="key">密钥认证</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item noStyle shouldUpdate={(prevValues, currentValues) =>
            prevValues.sshAuthType !== currentValues.sshAuthType
          }>
            {({ getFieldValue }) => {
              const authType = getFieldValue('sshAuthType');

              if (authType === 'password') {
                return (
                  <Form.Item
                    name="sshPassword"
                    label="SSH密码"
                    rules={[{ required: true, message: '请输入SSH密码' }]}
                  >
                    <Input.Password placeholder="请输入SSH密码" />
                  </Form.Item>
                );
              } else if (authType === 'key') {
                return (
                  <>
                    <Form.Item
                      name="sshPrivateKey"
                      label="SSH私钥"
                      rules={[{ required: true, message: '请输入SSH私钥内容' }]}
                    >
                      <TextArea
                        rows={6}
                        placeholder="请粘贴SSH私钥内容"
                        style={{ fontFamily: 'monospace', fontSize: '12px' }}
                      />
                    </Form.Item>
                    <Form.Item
                      name="sshKeyPassphrase"
                      label="密钥密码"
                      extra="如果您的SSH密钥设置了密码保护，请在此输入"
                    >
                      <Input.Password placeholder="如果密钥有密码保护，请输入密码" />
                    </Form.Item>
                  </>
                );
              }
              return null;
            }}
          </Form.Item>
        </Form>
        </>
        )}
      </Modal>
    </>
  );
};

export default BatchSshConfig;
