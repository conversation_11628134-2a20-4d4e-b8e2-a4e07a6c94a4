import React from 'react';
import {
  Modal,
  Descriptions,
  Progress,
  Tag,
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Space,
  Button
} from 'antd';
import {
  DesktopOutlined,
  MonitorOutlined,
  EditOutlined,
  DatabaseOutlined,
  CloudOutlined,
  ThunderboltOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { Server } from '../../types';

const { Title, Text } = Typography;

interface ServerDetailProps {
  visible: boolean;
  server: Server | null;
  onClose: () => void;
  onEdit: (server: Server) => void;
}

const ServerDetail: React.FC<ServerDetailProps> = ({
  visible,
  server,
  onClose,
  onEdit
}) => {
  if (!server) return null;

  // 状态配置
  const statusConfig = {
    active: { label: '正常', color: 'green', icon: <CheckCircleOutlined /> },
    inactive: { label: '停用', color: 'red', icon: <ExclamationCircleOutlined /> },
    maintenance: { label: '维护中', color: 'orange', icon: <SettingOutlined /> },
    expired: { label: '已过期', color: 'red', icon: <ExclamationCircleOutlined /> }
  };

  // 获取负载状态
  const getLoadStatus = (usage: number, threshold: number) => {
    if (usage >= threshold) {
      return { status: 'exception', color: '#ff4d4f' };
    } else if (usage >= threshold * 0.8) {
      return { status: 'active', color: '#faad14' };
    } else {
      return { status: 'success', color: '#52c41a' };
    }
  };

  // 格式化运行时间
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  };

  // 计算到期天数
  const getDaysUntilExpiry = (expireDate: string) => {
    const today = new Date();
    const expire = new Date(expireDate);
    const diffTime = expire.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const daysLeft = server.expireDate ? getDaysUntilExpiry(server.expireDate) : 0;
  const config = statusConfig[server.status as keyof typeof statusConfig];

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <DesktopOutlined style={{ marginRight: 8, color: '#1890ff' }} />
          服务器详情 - {server.name}
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
        <Button
          key="edit"
          type="primary"
          icon={<EditOutlined />}
          onClick={() => onEdit(server)}
        >
          编辑
        </Button>
      ]}
      width={1000}
      style={{ top: 20 }}
    >
      <div>
        {/* 基本信息 */}
        <Descriptions title="基本信息" bordered column={2} style={{ marginBottom: 24 }}>
          <Descriptions.Item label="服务器名称">{server.name}</Descriptions.Item>
          <Descriptions.Item label="IP地址">{server.ipAddress}</Descriptions.Item>
          <Descriptions.Item label="服务商">{server.provider}</Descriptions.Item>
          <Descriptions.Item label="机房位置">{server.location}</Descriptions.Item>
          <Descriptions.Item label="状态">
            <Tag color={config?.color} icon={config?.icon}>
              {config?.label}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="监控状态">
            <Tag color={server.monitoringEnabled ? 'green' : 'red'}>
              {server.monitoringEnabled ? '已启用' : '已禁用'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="到期时间">
            <span style={{
              color: daysLeft <= 30 ? '#ff4d4f' : '#52c41a',
              fontWeight: 500
            }}>
              {server.expireDate}
            </span>
            <div style={{ fontSize: 12, color: '#666' }}>
              {daysLeft > 0 ? `${daysLeft}天后到期` : '已过期'}
            </div>
          </Descriptions.Item>
          <Descriptions.Item label="续费金额">
            {server.renewalFee ? `¥${server.renewalFee.toLocaleString()}` : '-'}
          </Descriptions.Item>
        </Descriptions>

        {/* 配置信息 */}
        <Descriptions title="配置信息" bordered column={2} style={{ marginBottom: 24 }}>
          <Descriptions.Item label="CPU">{server.specifications.cpu}</Descriptions.Item>
          <Descriptions.Item label="内存">{server.specifications.memory}</Descriptions.Item>
          <Descriptions.Item label="存储">{server.specifications.storage}</Descriptions.Item>
          <Descriptions.Item label="带宽">{server.specifications.bandwidth}</Descriptions.Item>
          <Descriptions.Item label="操作系统" span={2}>
            {server.specifications.os}
          </Descriptions.Item>
        </Descriptions>

        {/* 实时监控 */}
        {server.loadInfo && (
          <div style={{ marginBottom: 24 }}>
            <Title level={5} style={{ marginBottom: 16 }}>
              <MonitorOutlined style={{ marginRight: 8 }} />
              实时监控
            </Title>
            
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="CPU使用率"
                    value={server.loadInfo.cpuUsage}
                    suffix="%"
                    valueStyle={{ 
                      color: getLoadStatus(server.loadInfo.cpuUsage, server.alertThresholds?.cpuUsage || 80).color 
                    }}
                    prefix={<ThunderboltOutlined />}
                  />
                  <Progress
                    percent={server.loadInfo.cpuUsage}
                    strokeColor={getLoadStatus(server.loadInfo.cpuUsage, server.alertThresholds?.cpuUsage || 80).color}
                    size="small"
                    style={{ marginTop: 8 }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="内存使用率"
                    value={server.loadInfo.memoryUsage}
                    suffix="%"
                    valueStyle={{ 
                      color: getLoadStatus(server.loadInfo.memoryUsage, server.alertThresholds?.memoryUsage || 85).color 
                    }}
                    prefix={<DatabaseOutlined />}
                  />
                  <Progress
                    percent={server.loadInfo.memoryUsage}
                    strokeColor={getLoadStatus(server.loadInfo.memoryUsage, server.alertThresholds?.memoryUsage || 85).color}
                    size="small"
                    style={{ marginTop: 8 }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="磁盘使用率"
                    value={server.loadInfo.diskUsage}
                    suffix="%"
                    valueStyle={{ 
                      color: getLoadStatus(server.loadInfo.diskUsage, server.alertThresholds?.diskUsage || 90).color 
                    }}
                    prefix={<CloudOutlined />}
                  />
                  <Progress
                    percent={server.loadInfo.diskUsage}
                    strokeColor={getLoadStatus(server.loadInfo.diskUsage, server.alertThresholds?.diskUsage || 90).color}
                    size="small"
                    style={{ marginTop: 8 }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="运行时间"
                    value={formatUptime(server.loadInfo.uptime)}
                    valueStyle={{ color: '#52c41a', fontSize: 14 }}
                    prefix={<CheckCircleOutlined />}
                  />
                </Card>
              </Col>
            </Row>

            <Descriptions bordered column={3}>
              <Descriptions.Item label="网络入流量">
                {server.loadInfo.networkIn} MB/s
              </Descriptions.Item>
              <Descriptions.Item label="网络出流量">
                {server.loadInfo.networkOut} MB/s
              </Descriptions.Item>
              <Descriptions.Item label="进程数">
                {server.loadInfo.processes}
              </Descriptions.Item>
              <Descriptions.Item label="负载均值">
                {server.loadInfo.loadAverage.join(', ')}
              </Descriptions.Item>
              <Descriptions.Item label="最后更新" span={2}>
                {new Date(server.loadInfo.lastUpdated).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}

        {/* 告警阈值 */}
        {server.alertThresholds && (
          <Descriptions title="告警阈值" bordered column={4} style={{ marginBottom: 24 }}>
            <Descriptions.Item label="CPU告警">
              {server.alertThresholds.cpuUsage}%
            </Descriptions.Item>
            <Descriptions.Item label="内存告警">
              {server.alertThresholds.memoryUsage}%
            </Descriptions.Item>
            <Descriptions.Item label="磁盘告警">
              {server.alertThresholds.diskUsage}%
            </Descriptions.Item>
            <Descriptions.Item label="网络告警">
              {server.alertThresholds.networkUsage}%
            </Descriptions.Item>
          </Descriptions>
        )}

        {/* 备注信息 */}
        {server.notes && (
          <Descriptions title="备注信息" bordered style={{ marginBottom: 24 }}>
            <Descriptions.Item label="备注" span={3}>
              <div style={{ whiteSpace: 'pre-wrap' }}>{server.notes}</div>
            </Descriptions.Item>
          </Descriptions>
        )}

        {/* 时间信息 */}
        <Descriptions title="时间信息" bordered column={2}>
          <Descriptions.Item label="创建时间">
            {server.createdAt}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {server.updatedAt}
          </Descriptions.Item>
        </Descriptions>
      </div>
    </Modal>
  );
};

export default ServerDetail;
