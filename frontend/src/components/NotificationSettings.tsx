import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Space,
  Alert,
  Divider,
  Row,
  Col,
  Select,
  Table,
  Tag,
  Modal,
  message,
  Tabs,
  Typography
} from 'antd';
import {
  BellOutlined,
  RobotOutlined,
  MailOutlined,
  PhoneOutlined,
  LinkOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ExperimentOutlined
} from '@ant-design/icons';

const { TabPane } = Tabs;
const { Title, Paragraph } = Typography;
const { Option } = Select;

interface NotificationConfig {
  id?: number;
  type: 'feishu' | 'email' | 'sms' | 'webhook';
  name: string;
  config: any;
  is_active: boolean;
  created_at?: string;
  updated_at?: string;
}

interface NotificationSettingsProps {
  onSave?: (configs: NotificationConfig[]) => void;
}

const NotificationSettings: React.FC<NotificationSettingsProps> = ({ onSave }) => {
  const [configs, setConfigs] = useState<NotificationConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingConfig, setEditingConfig] = useState<NotificationConfig | null>(null);
  const [form] = Form.useForm();

  // 通知类型配置
  const notificationTypes = [
    {
      key: 'feishu',
      name: '飞书机器人',
      icon: <RobotOutlined />,
      color: 'blue',
      fields: [
        { name: 'webhookUrl', label: 'Webhook URL', type: 'input', required: true },
        { name: 'botName', label: '机器人名称', type: 'input', defaultValue: '网站监控机器人' },
        { name: 'notificationThreshold', label: '通知阈值', type: 'number', defaultValue: 5 }
      ]
    },
    {
      key: 'email',
      name: '邮件通知',
      icon: <MailOutlined />,
      color: 'green',
      fields: [
        { name: 'host', label: 'SMTP服务器', type: 'input', required: true },
        { name: 'port', label: '端口', type: 'number', defaultValue: 587 },
        { name: 'secure', label: '启用SSL', type: 'switch', defaultValue: false },
        { name: 'user', label: '用户名', type: 'input', required: true },
        { name: 'password', label: '密码', type: 'password', required: true },
        { name: 'from', label: '发件人', type: 'input' },
        { name: 'to', label: '收件人', type: 'tags', required: true }
      ]
    },
    {
      key: 'sms',
      name: '短信通知',
      icon: <PhoneOutlined />,
      color: 'orange',
      fields: [
        { name: 'provider', label: '服务商', type: 'select', options: ['aliyun', 'tencent'], defaultValue: 'aliyun' },
        { name: 'accessKeyId', label: 'Access Key ID', type: 'input', required: true },
        { name: 'accessKeySecret', label: 'Access Key Secret', type: 'password', required: true },
        { name: 'signName', label: '签名', type: 'input', defaultValue: '网站监控' },
        { name: 'templateCode', label: '模板代码', type: 'input', required: true },
        { name: 'phoneNumbers', label: '手机号码', type: 'tags', required: true }
      ]
    },
    {
      key: 'webhook',
      name: 'Webhook',
      icon: <LinkOutlined />,
      color: 'purple',
      fields: [
        { name: 'url', label: 'Webhook URL', type: 'input', required: true },
        { name: 'method', label: '请求方法', type: 'select', options: ['POST', 'PUT'], defaultValue: 'POST' },
        { name: 'headers', label: '请求头', type: 'json', defaultValue: '{}' },
        { name: 'timeout', label: '超时时间(ms)', type: 'number', defaultValue: 10000 }
      ]
    }
  ];

  // 加载通知配置
  const loadConfigs = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:3001/api/v1/notifications/configs');
      const result = await response.json();
      if (result.success) {
        setConfigs(result.data);
      }
    } catch (error) {
      message.error('加载通知配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存通知配置
  const saveConfig = async (values: any) => {
    setLoading(true);
    try {
      const url = editingConfig
        ? `http://localhost:3001/api/v1/notifications/config/${editingConfig.id}`
        : 'http://localhost:3001/api/v1/notifications/config';

      const method = editingConfig ? 'PUT' : 'POST';

      // 构建符合后端期望的数据格式
      const { type, name, enabled, ...configFields } = values;
      const requestData = {
        type,
        name,
        enabled: enabled || false,
        config: configFields
      };

      console.log('发送的数据:', requestData); // 调试日志

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const result = await response.json();
      if (result.success) {
        message.success('通知配置保存成功');
        setModalVisible(false);
        setEditingConfig(null);
        form.resetFields();
        loadConfigs();
        onSave?.(configs);
      } else {
        message.error(result.message || '保存失败');
      }
    } catch (error) {
      message.error('保存通知配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除通知配置
  const deleteConfig = async (id: number) => {
    try {
      const response = await fetch(`http://localhost:3001/api/v1/notifications/config/${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      if (result.success) {
        message.success('通知配置删除成功');
        loadConfigs();
      } else {
        message.error(result.message || '删除失败');
      }
    } catch (error) {
      message.error('删除通知配置失败');
    }
  };

  // 测试通知
  const testNotification = async (config: NotificationConfig) => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/notifications/test-feishu', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          webhookUrl: config.config.webhookUrl,
          botName: config.config.botName || '网站监控机器人'
        }),
      });

      const result = await response.json();
      if (result.success) {
        message.success('测试通知发送成功');
      } else {
        message.error(result.message || '测试通知发送失败');
      }
    } catch (error) {
      message.error('测试通知失败');
    }
  };

  // 打开编辑模态框
  const openEditModal = (config?: NotificationConfig) => {
    setEditingConfig(config || null);
    if (config) {
      form.setFieldsValue({
        type: config.type,
        name: config.name,
        enabled: config.is_active,
        ...config.config
      });
    } else {
      form.resetFields();
    }
    setModalVisible(true);
  };

  useEffect(() => {
    loadConfigs();
  }, []);

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeConfig = notificationTypes.find(t => t.key === type);
        return (
          <Tag color={typeConfig?.color} icon={typeConfig?.icon}>
            {typeConfig?.name || type}
          </Tag>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active: boolean) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: NotificationConfig) => (
        <Space>
          <Button
            type="link"
            icon={<ExperimentOutlined />}
            onClick={() => testNotification(record)}
            disabled={!record.is_active}
          >
            测试
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => openEditModal(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => {
              Modal.confirm({
                title: '确认删除',
                content: '确定要删除这个通知配置吗？',
                onOk: () => deleteConfig(record.id!),
              });
            }}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Card>
        <div className="flex justify-between items-center mb-4">
          <div>
            <Title level={4}>通知配置管理</Title>
            <Paragraph type="secondary">
              配置和管理各种通知渠道，包括飞书、邮件、短信和Webhook
            </Paragraph>
          </div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => openEditModal()}
          >
            添加通知配置
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={configs}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>

      {/* 编辑/添加模态框 */}
      <Modal
        title={editingConfig ? '编辑通知配置' : '添加通知配置'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingConfig(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={saveConfig}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="通知类型"
                rules={[{ required: true, message: '请选择通知类型' }]}
              >
                <Select placeholder="选择通知类型">
                  {notificationTypes.map(type => (
                    <Option key={type.key} value={type.key}>
                      {type.icon} {type.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="name"
                label="配置名称"
                rules={[{ required: true, message: '请输入配置名称' }]}
              >
                <Input placeholder="输入配置名称" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="enabled"
            label="启用状态"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Divider>配置详情</Divider>

          {/* 动态配置字段将在这里渲染 */}
          <Form.Item dependencies={['type']}>
            {({ getFieldValue }) => {
              const selectedType = getFieldValue('type');
              const typeConfig = notificationTypes.find(t => t.key === selectedType);
              
              if (!typeConfig) return null;

              return (
                <div>
                  {typeConfig.fields.map(field => (
                    <Form.Item
                      key={field.name}
                      name={field.name}
                      label={field.label}
                      rules={field.required ? [{ required: true, message: `请输入${field.label}` }] : []}
                      initialValue={field.defaultValue}
                    >
                      {field.type === 'input' && <Input />}
                      {field.type === 'password' && <Input.Password />}
                      {field.type === 'number' && <InputNumber style={{ width: '100%' }} />}
                      {field.type === 'switch' && <Switch />}
                      {field.type === 'select' && (
                        <Select>
                          {field.options?.map(option => (
                            <Option key={option} value={option}>{option}</Option>
                          ))}
                        </Select>
                      )}
                      {field.type === 'tags' && <Select mode="tags" style={{ width: '100%' }} />}
                      {field.type === 'json' && <Input.TextArea rows={3} />}
                    </Form.Item>
                  ))}
                </div>
              );
            }}
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                保存配置
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default NotificationSettings;
