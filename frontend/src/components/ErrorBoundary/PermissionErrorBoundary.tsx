import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Result } from 'antd';
import { ReloadOutlined, HomeOutlined } from '@ant-design/icons';

interface PermissionErrorBoundaryProps {
  children: React.ReactNode;
}

interface PermissionErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

/**
 * 权限管理页面专用错误边界组件
 * 捕获权限相关的渲染错误并提供友好的错误界面
 */
class PermissionErrorBoundary extends React.Component<
  PermissionErrorBoundaryProps,
  PermissionErrorBoundaryState
> {
  constructor(props: PermissionErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<PermissionErrorBoundaryState> {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 记录错误信息
    console.error('权限管理页面错误:', error);
    console.error('错误详情:', errorInfo);
    
    this.setState({
      error,
      errorInfo
    });
  }

  handleReload = () => {
    // 重置错误状态并重新加载
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
    window.location.reload();
  };

  handleGoHome = () => {
    // 返回首页
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      const isDevelopment = process.env.NODE_ENV === 'development';
      
      return (
        <div style={{ padding: '24px', minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
          <Card>
            <Result
              status="error"
              title="权限管理页面加载失败"
              subTitle="抱歉，权限管理页面遇到了一个错误。请尝试刷新页面或联系管理员。"
              extra={[
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />} 
                  onClick={this.handleReload}
                  key="reload"
                >
                  刷新页面
                </Button>,
                <Button 
                  icon={<HomeOutlined />} 
                  onClick={this.handleGoHome}
                  key="home"
                >
                  返回首页
                </Button>
              ]}
            />
            
            {this.state.error && (
              <Alert
                message="错误详情"
                description={this.state.error.message}
                type="error"
                showIcon
                style={{ marginTop: '16px' }}
              />
            )}
            
            {isDevelopment && this.state.errorInfo && (
              <Alert
                message="开发调试信息"
                description={
                  <pre style={{ 
                    fontSize: '12px', 
                    maxHeight: '200px', 
                    overflow: 'auto',
                    backgroundColor: '#f8f8f8',
                    padding: '8px',
                    borderRadius: '4px'
                  }}>
                    {this.state.error?.stack}
                    {'\n\n组件堆栈:'}
                    {this.state.errorInfo.componentStack}
                  </pre>
                }
                type="warning"
                showIcon
                style={{ marginTop: '16px' }}
              />
            )}
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default PermissionErrorBoundary;
