import React, { useState } from 'react';
import {
  Button,
  Dropdown,
  Modal,
  Form,
  Select,
  Switch,
  Space,
  Typography,
  Alert,
  Spin
} from 'antd';
import {
  DownloadOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { ExportFormat, ExportParams, exportFormatOptions, exportUtils } from '@/services/export';
import { QueryParams } from '@/types';

const { Text } = Typography;
const { Option } = Select;

interface ExportButtonProps {
  // 导出类型
  exportType: 'users' | 'customers' | 'projects' | 'websites' | 'servers' | 'domains';
  // 当前查询参数
  queryParams: QueryParams;
  // 导出函数
  onExport: (params: ExportParams) => Promise<void>;
  // 按钮文本
  buttonText?: string;
  // 按钮类型
  buttonType?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
  // 是否显示下拉菜单
  showDropdown?: boolean;
  // 是否禁用
  disabled?: boolean;
  // 数据总数
  totalCount?: number;
}

const ExportButton: React.FC<ExportButtonProps> = ({
  exportType,
  queryParams,
  onExport,
  buttonText = '导出',
  buttonType = 'default',
  showDropdown = true,
  disabled = false,
  totalCount = 0
}) => {
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 快速导出（Excel格式）
  const handleQuickExport = async () => {
    setLoading(true);
    try {
      const params = exportUtils.buildExportParams(queryParams, 'excel');
      await onExport(params);
    } catch (error) {
      console.error('快速导出失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 显示高级导出对话框
  const showAdvancedExport = () => {
    form.setFieldsValue({
      format: 'excel',
      includeFilters: true
    });
    setModalVisible(true);
  };

  // 处理高级导出
  const handleAdvancedExport = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      const params: ExportParams = {
        format: values.format,
        ...(values.includeFilters ? queryParams : {})
      };
      
      // 验证参数
      const errors = exportUtils.validateExportParams(params);
      if (errors.length > 0) {
        Modal.error({
          title: '参数验证失败',
          content: errors.join('\n')
        });
        return;
      }
      
      await onExport(params);
      setModalVisible(false);
    } catch (error) {
      console.error('高级导出失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 下拉菜单项
  const dropdownItems = [
    {
      key: 'quick-excel',
      label: (
        <Space>
          <FileExcelOutlined style={{ color: '#52c41a' }} />
          <span>快速导出 (Excel)</span>
        </Space>
      ),
      onClick: handleQuickExport
    },
    {
      key: 'advanced',
      label: (
        <Space>
          <DownloadOutlined />
          <span>高级导出...</span>
        </Space>
      ),
      onClick: showAdvancedExport
    }
  ];

  // 获取导出类型的中文名称
  const getExportTypeName = (type: string): string => {
    const typeMap = {
      users: '用户',
      customers: '客户',
      projects: '项目',
      websites: '网站',
      servers: '服务器',
      domains: '域名'
    };
    return typeMap[type as keyof typeof typeMap] || type;
  };

  // 渲染按钮
  const renderButton = () => {
    if (showDropdown) {
      return (
        <Dropdown
          menu={{ items: dropdownItems }}
          placement="bottomRight"
          disabled={disabled}
        >
          <Button
            type={buttonType}
            icon={<DownloadOutlined />}
            loading={loading}
            disabled={disabled}
          >
            {buttonText}
          </Button>
        </Dropdown>
      );
    } else {
      return (
        <Button
          type={buttonType}
          icon={<DownloadOutlined />}
          loading={loading}
          disabled={disabled}
          onClick={handleQuickExport}
        >
          {buttonText}
        </Button>
      );
    }
  };

  return (
    <>
      {renderButton()}
      
      {/* 高级导出对话框 */}
      <Modal
        title={`导出${getExportTypeName(exportType)}数据`}
        open={modalVisible}
        onOk={handleAdvancedExport}
        onCancel={() => setModalVisible(false)}
        confirmLoading={loading}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            format: 'excel',
            includeFilters: true
          }}
        >
          <Alert
            message="导出说明"
            description={`当前筛选条件下共有 ${totalCount} 条数据可供导出`}
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Form.Item
            name="format"
            label="导出格式"
            rules={[{ required: true, message: '请选择导出格式' }]}
          >
            <Select placeholder="请选择导出格式">
              {exportFormatOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  <Space>
                    <span>{exportUtils.getFormatIcon(option.value as ExportFormat)}</span>
                    <span>{option.label}</span>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="includeFilters"
            label="导出范围"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="当前筛选结果"
              unCheckedChildren="全部数据"
            />
          </Form.Item>
          
          <Form.Item shouldUpdate>
            {({ getFieldValue }) => {
              const format = getFieldValue('format') as ExportFormat;
              const includeFilters = getFieldValue('includeFilters');
              
              return (
                <div style={{ padding: 12, background: '#f5f5f5', borderRadius: 6 }}>
                  <Text type="secondary">
                    {exportUtils.getFormatDescription(format)}
                  </Text>
                  <br />
                  <Text type="secondary">
                    将导出: {includeFilters ? '当前筛选结果' : '全部数据'}
                    {includeFilters && totalCount > 0 && ` (${totalCount} 条)`}
                  </Text>
                </div>
              );
            }}
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default ExportButton;
