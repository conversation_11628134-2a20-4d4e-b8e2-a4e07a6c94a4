import React, { useState } from 'react';
import {
  Modal,
  Form,
  Checkbox,
  Select,
  Space,
  Typography,
  Alert,
  Divider,
  Progress,
  List
} from 'antd';
import {
  FileExcelOutlined,
  UserOutlined,
  TeamOutlined,
  ProjectOutlined,
  GlobalOutlined,
  DatabaseOutlined,
  CloudOutlined
} from '@ant-design/icons';
import { ExportApi } from '@/services/export';
import { useAuthStore } from '@/store/auth';
import { permission } from '@/services/auth';

const { Text, Title } = Typography;
const { Option } = Select;

interface BatchExportModalProps {
  visible: boolean;
  onCancel: () => void;
}

interface ExportTask {
  key: string;
  name: string;
  icon: React.ReactNode;
  description: string;
  permission?: string[];
  status: 'pending' | 'running' | 'success' | 'error';
  progress: number;
  error?: string;
}

const BatchExportModal: React.FC<BatchExportModalProps> = ({
  visible,
  onCancel
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [exportTasks, setExportTasks] = useState<ExportTask[]>([]);
  const [currentTask, setCurrentTask] = useState<string | null>(null);
  const { user } = useAuthStore();

  // 可导出的数据类型
  const exportOptions: ExportTask[] = [
    {
      key: 'users',
      name: '用户数据',
      icon: <UserOutlined />,
      description: '包含用户基本信息、角色权限等',
      permission: ['admin', 'super_admin'],
      status: 'pending',
      progress: 0
    },
    {
      key: 'customers',
      name: '客户数据',
      icon: <TeamOutlined />,
      description: '包含客户信息、联系方式、状态等',
      status: 'pending',
      progress: 0
    },
    {
      key: 'projects',
      name: '项目数据',
      icon: <ProjectOutlined />,
      description: '包含项目信息、进度、合同等',
      status: 'pending',
      progress: 0
    },
    {
      key: 'websites',
      name: '网站数据',
      icon: <GlobalOutlined />,
      description: '包含网站信息、监控状态、SSL等',
      status: 'pending',
      progress: 0
    },
    {
      key: 'servers',
      name: '服务器数据',
      icon: <DatabaseOutlined />,
      description: '包含服务器信息、配置、状态等',
      permission: ['admin', 'super_admin'],
      status: 'pending',
      progress: 0
    },
    {
      key: 'domains',
      name: '域名数据',
      icon: <CloudOutlined />,
      description: '包含域名信息、到期时间、DNS等',
      status: 'pending',
      progress: 0
    }
  ];

  // 过滤有权限的选项
  const availableOptions = exportOptions.filter(option => {
    if (!option.permission) return true;
    return permission.hasRole(user, option.permission);
  });

  // 开始批量导出
  const handleExport = async () => {
    try {
      const values = await form.validateFields();
      const { selectedTypes, format } = values;
      
      if (!selectedTypes || selectedTypes.length === 0) {
        return;
      }

      setLoading(true);
      
      // 初始化任务状态
      const tasks = availableOptions
        .filter(option => selectedTypes.includes(option.key))
        .map(option => ({ ...option, status: 'pending' as const, progress: 0 }));
      
      setExportTasks(tasks);

      // 依次执行导出任务
      for (const task of tasks) {
        setCurrentTask(task.key);
        
        // 更新任务状态为运行中
        setExportTasks(prev => prev.map(t => 
          t.key === task.key 
            ? { ...t, status: 'running', progress: 0 }
            : t
        ));

        try {
          // 模拟进度更新
          const progressInterval = setInterval(() => {
            setExportTasks(prev => prev.map(t => 
              t.key === task.key && t.progress < 90
                ? { ...t, progress: t.progress + 10 }
                : t
            ));
          }, 200);

          // 执行导出
          await executeExportTask(task.key, format);
          
          clearInterval(progressInterval);
          
          // 更新任务状态为成功
          setExportTasks(prev => prev.map(t => 
            t.key === task.key 
              ? { ...t, status: 'success', progress: 100 }
              : t
          ));
          
        } catch (error) {
          // 更新任务状态为失败
          setExportTasks(prev => prev.map(t => 
            t.key === task.key 
              ? { 
                  ...t, 
                  status: 'error', 
                  progress: 0,
                  error: error instanceof Error ? error.message : '导出失败'
                }
              : t
          ));
        }
      }
      
      setCurrentTask(null);
      
    } catch (error) {
      console.error('批量导出失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 执行单个导出任务
  const executeExportTask = async (taskKey: string, format: string): Promise<void> => {
    const exportParams = { format };
    
    switch (taskKey) {
      case 'users':
        await ExportApi.exportUsers(exportParams);
        break;
      case 'customers':
        await ExportApi.exportCustomers(exportParams);
        break;
      case 'projects':
        await ExportApi.exportProjects(exportParams);
        break;
      case 'websites':
        await ExportApi.exportWebsites(exportParams);
        break;
      case 'servers':
        await ExportApi.exportServers(exportParams);
        break;
      case 'domains':
        await ExportApi.exportDomains(exportParams);
        break;
      default:
        throw new Error(`未知的导出类型: ${taskKey}`);
    }
  };

  // 获取任务状态颜色
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'running': return '#1890ff';
      case 'success': return '#52c41a';
      case 'error': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };

  // 获取任务状态文本
  const getStatusText = (status: string): string => {
    switch (status) {
      case 'pending': return '等待中';
      case 'running': return '导出中';
      case 'success': return '已完成';
      case 'error': return '失败';
      default: return '未知';
    }
  };

  // 重置状态
  const handleCancel = () => {
    if (!loading) {
      setExportTasks([]);
      setCurrentTask(null);
      form.resetFields();
      onCancel();
    }
  };

  return (
    <Modal
      title={
        <Space>
          <FileExcelOutlined />
          <span>批量导出数据</span>
        </Space>
      }
      open={visible}
      onOk={handleExport}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={600}
      okText="开始导出"
      cancelText="取消"
      maskClosable={!loading}
      closable={!loading}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          format: 'excel'
        }}
      >
        <Alert
          message="批量导出说明"
          description="选择需要导出的数据类型，系统将依次导出各类数据并自动下载到本地。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form.Item
          name="selectedTypes"
          label="选择导出数据"
          rules={[{ required: true, message: '请至少选择一种数据类型' }]}
        >
          <Checkbox.Group style={{ width: '100%' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {availableOptions.map(option => (
                <Checkbox key={option.key} value={option.key}>
                  <Space>
                    {option.icon}
                    <div>
                      <div style={{ fontWeight: 500 }}>{option.name}</div>
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {option.description}
                      </Text>
                    </div>
                  </Space>
                </Checkbox>
              ))}
            </Space>
          </Checkbox.Group>
        </Form.Item>

        <Form.Item
          name="format"
          label="导出格式"
          rules={[{ required: true, message: '请选择导出格式' }]}
        >
          <Select>
            <Option value="excel">Excel文件 (.xlsx)</Option>
            <Option value="csv">CSV文件 (.csv)</Option>
            <Option value="json">JSON文件 (.json)</Option>
          </Select>
        </Form.Item>

        {/* 导出进度 */}
        {exportTasks.length > 0 && (
          <>
            <Divider />
            <Title level={5}>导出进度</Title>
            <List
              size="small"
              dataSource={exportTasks}
              renderItem={(task) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={task.icon}
                    title={
                      <Space>
                        <span>{task.name}</span>
                        <Text 
                          type={task.status === 'error' ? 'danger' : 'secondary'}
                          style={{ fontSize: 12 }}
                        >
                          {getStatusText(task.status)}
                        </Text>
                      </Space>
                    }
                    description={
                      <div>
                        <Progress
                          percent={task.progress}
                          size="small"
                          status={task.status === 'error' ? 'exception' : 'active'}
                          strokeColor={getStatusColor(task.status)}
                        />
                        {task.error && (
                          <Text type="danger" style={{ fontSize: 12 }}>
                            {task.error}
                          </Text>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </>
        )}
      </Form>
    </Modal>
  );
};

export default BatchExportModal;
