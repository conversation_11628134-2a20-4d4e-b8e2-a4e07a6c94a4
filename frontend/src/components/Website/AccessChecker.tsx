import React, { useState } from 'react';
import {
  Button,
  Space,
  Tag,
  Tooltip,
  message,
  Modal,
  Progress,
  List,
  Typography,
  Alert
} from 'antd';
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  GlobalOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { WebsiteApi } from '../../services/website';
import { Website } from '../../types';

const { Text } = Typography;

interface AccessCheckerProps {
  website?: Website;
  websites?: Website[];
  mode?: 'single' | 'batch';
  onSuccess?: () => void;
}

interface CheckResult {
  id: number;
  domain: string;
  accessible: boolean;
  statusCode: number;
  responseTime: number;
  error?: string;
  loading?: boolean;
}

const AccessChecker: React.FC<AccessCheckerProps> = ({
  website,
  websites = [],
  mode = 'single',
  onSuccess
}) => {
  const [checking, setChecking] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [results, setResults] = useState<CheckResult[]>([]);

  // 单个网站检查
  const checkSingleWebsite = async () => {
    if (!website) return;

    setChecking(true);
    try {
      const response = await WebsiteApi.checkAccess(website.id);
      
      if (response.success) {
        const { accessible, statusCode, responseTime } = response.data;
        
        message.success(
          `检查完成：${accessible ? '可访问' : '不可访问'} (${statusCode}, ${responseTime}ms)`
        );
        
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error) {
      console.error('检查失败:', error);
      message.error('检查网站可访问性失败');
    } finally {
      setChecking(false);
    }
  };

  // 批量检查
  const checkBatchWebsites = async () => {
    if (websites.length === 0) return;

    setModalVisible(true);
    setChecking(true);
    
    // 初始化结果
    const initialResults: CheckResult[] = websites.map(w => ({
      id: w.id,
      domain: w.domain,
      accessible: false,
      statusCode: 0,
      responseTime: 0,
      loading: true
    }));
    setResults(initialResults);

    try {
      // 逐个检查网站（避免并发过多）
      for (let i = 0; i < websites.length; i++) {
        const website = websites[i];
        
        try {
          const response = await WebsiteApi.checkAccess(website.id);
          
          if (response.success) {
            const { accessible, statusCode, responseTime, error } = response.data;
            
            setResults(prev => prev.map(result => 
              result.id === website.id 
                ? { 
                    ...result, 
                    accessible, 
                    statusCode, 
                    responseTime,
                    error,
                    loading: false 
                  }
                : result
            ));
          }
        } catch (error) {
          setResults(prev => prev.map(result => 
            result.id === website.id 
              ? { 
                  ...result, 
                  accessible: false,
                  statusCode: 0,
                  responseTime: 0,
                  error: '检查失败',
                  loading: false 
                }
              : result
          ));
        }

        // 添加延迟避免请求过快
        if (i < websites.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('批量检查失败:', error);
      message.error('批量检查失败');
    } finally {
      setChecking(false);
    }
  };

  // 获取状态标签
  const getStatusTag = (result: CheckResult) => {
    if (result.loading) {
      return (
        <Tag icon={<LoadingOutlined />} color="processing">
          检查中...
        </Tag>
      );
    }

    if (result.accessible) {
      return (
        <Tag icon={<CheckCircleOutlined />} color="success">
          可访问 ({result.statusCode})
        </Tag>
      );
    } else {
      return (
        <Tag icon={<CloseCircleOutlined />} color="error">
          不可访问 ({result.statusCode || 'N/A'})
        </Tag>
      );
    }
  };

  // 计算进度
  const completedCount = results.filter(r => !r.loading).length;
  const progress = results.length > 0 ? (completedCount / results.length) * 100 : 0;
  const accessibleCount = results.filter(r => r.accessible).length;

  if (mode === 'single' && website) {
    return (
      <Tooltip title="检查网站是否可访问">
        <Button
          icon={<GlobalOutlined />}
          loading={checking}
          onClick={checkSingleWebsite}
          size="small"
        >
          检查访问
        </Button>
      </Tooltip>
    );
  }

  return (
    <>
      <Button
        icon={<GlobalOutlined />}
        loading={checking}
        onClick={checkBatchWebsites}
        disabled={websites.length === 0}
      >
        批量检查访问性 ({websites.length})
      </Button>

      <Modal
        title="批量检查网站可访问性"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button 
            key="close" 
            onClick={() => setModalVisible(false)}
            disabled={checking}
          >
            {checking ? '检查中...' : '关闭'}
          </Button>
        ]}
        width={700}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 进度信息 */}
          <Alert
            message={`检查进度：${completedCount}/${results.length}`}
            description={
              checking 
                ? `正在检查网站可访问性，请稍候...` 
                : `检查完成：${accessibleCount} 个可访问，${results.length - accessibleCount} 个不可访问`
            }
            type={checking ? 'info' : 'success'}
            showIcon
          />

          {/* 进度条 */}
          <Progress 
            percent={Math.round(progress)} 
            status={checking ? 'active' : 'success'}
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
          />

          {/* 检查结果列表 */}
          <List
            dataSource={results}
            renderItem={(result) => (
              <List.Item>
                <List.Item.Meta
                  avatar={<GlobalOutlined />}
                  title={
                    <Space>
                      <Text strong>{result.domain}</Text>
                      {getStatusTag(result)}
                    </Space>
                  }
                  description={
                    <Space>
                      {!result.loading && (
                        <>
                          <Text type="secondary">
                            <ClockCircleOutlined /> {result.responseTime}ms
                          </Text>
                          {result.error && (
                            <Text type="danger">{result.error}</Text>
                          )}
                        </>
                      )}
                    </Space>
                  }
                />
              </List.Item>
            )}
            style={{ maxHeight: 400, overflowY: 'auto' }}
          />
        </Space>
      </Modal>
    </>
  );
};

export default AccessChecker;
