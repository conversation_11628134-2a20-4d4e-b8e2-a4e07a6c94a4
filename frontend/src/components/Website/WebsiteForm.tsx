import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Switch,
  Space,
  Typography,
  Alert,
  Spin,
  App,
  Row,
  Col,
  Button,
  Divider
} from 'antd';
import { GlobalOutlined, DatabaseOutlined, FileOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { Website } from '../../types';
import { WebsiteApi } from '../../services/website';
import ServerSelect from '../Common/ServerSelect';
import AttachmentManager from './AttachmentManager';

const { Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface WebsiteFormProps {
  visible: boolean;
  mode: 'create' | 'edit';
  initialValues?: Website;
  onCancel: () => void;
  onSuccess: (data?: any) => void;
}

const WebsiteForm: React.FC<WebsiteFormProps> = ({
  visible,
  mode,
  initialValues,
  onCancel,
  onSuccess
}) => {
  const { message } = App.useApp();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [optionsLoading, setOptionsLoading] = useState(false);
  const [platforms, setPlatforms] = useState<any[]>([]);
  const [industries, setIndustries] = useState<string[]>([]);
  const [attachmentVisible, setAttachmentVisible] = useState(false);
  const [attachmentRefreshTrigger, setAttachmentRefreshTrigger] = useState(0);

  // 加载选项数据
  const loadOptions = async () => {
    setOptionsLoading(true);
    try {
      const [settingPlatformsRes, settingIndustriesRes, dbPlatformsRes] = await Promise.all([
        // 从设置API获取平台类型名称
        fetch('http://localhost:3001/api/v1/settings/platforms').then(res => res.json()),
        // 从设置API获取行业类型
        fetch('http://localhost:3001/api/v1/settings/industries').then(res => res.json()),
        // 从网站API获取平台数据（用于ID映射）
        WebsiteApi.getPlatforms()
      ]);

      // 处理平台类型数据
      if (settingPlatformsRes.success && dbPlatformsRes.data) {
        // 处理设置API的新数据格式
        let settingPlatformNames = settingPlatformsRes.data;
        if (settingPlatformNames.length > 0 && typeof settingPlatformNames[0] === 'object') {
          // 新格式：对象数组，只取活跃的平台名称
          settingPlatformNames = settingPlatformNames
            .filter((platform: any) => platform.isActive)
            .map((platform: any) => platform.name);
        }

        const dbPlatforms = dbPlatformsRes.data; // 来自数据库的平台对象数组

        // 创建一个映射，确保设置中的平台在数据库中存在，如果不存在则创建临时ID
        const mergedPlatforms = settingPlatformNames.map((platformName: string) => {
          const existingPlatform = dbPlatforms.find((p: any) => p.name === platformName);
          return existingPlatform || {
            id: `temp_${Date.now()}_${Math.random()}`,
            name: platformName,
            isFromSettings: true
          };
        });

        setPlatforms(mergedPlatforms);
      } else {
        // 如果设置API失败，回退到原有的平台API
        setPlatforms(dbPlatformsRes.data || []);
      }

      // 处理行业类型数据
      if (settingIndustriesRes.success) {
        setIndustries(settingIndustriesRes.data);
      } else {
        // 如果设置API失败，使用默认行业
        setIndustries(['电商', '企业官网', '博客', '论坛', '新闻资讯', '教育培训', '医疗健康', '金融服务']);
      }
    } catch (error) {
      console.error('加载选项失败:', error);
      message.error('加载选项数据失败');

      // 错误时使用默认数据
      try {
        const fallbackRes = await WebsiteApi.getPlatforms();
        setPlatforms(fallbackRes.data || []);
      } catch (e) {
        console.error('加载默认平台数据也失败:', e);
        setPlatforms([]);
      }
      setIndustries(['电商', '企业官网', '博客', '论坛', '新闻资讯', '教育培训', '医疗健康', '金融服务']);
    } finally {
      setOptionsLoading(false);
    }
  };

  // 初始化表单数据
  useEffect(() => {
    if (visible) {
      loadOptions();
      
      if (mode === 'edit' && initialValues) {
        form.setFieldsValue({
          siteName: initialValues.siteName || initialValues.domain, // 兼容旧数据
          siteUrl: initialValues.siteUrl,
          platformId: initialValues.platform?.platform_id || initialValues.platform?.id,
          serverId: initialValues.server?.id,
          industry: initialValues.industry,
          onlineDate: initialValues.onlineDate ? dayjs(initialValues.onlineDate) : null,
          expireDate: initialValues.expireDate ? dayjs(initialValues.expireDate) : null,
          projectAmount: initialValues.projectAmount,
          renewalFee: initialValues.renewalFee,
          notes: initialValues.notes,
          status: initialValues.status,
          hasOnboard: initialValues.hasOnboard || false
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          status: 'active'
        });
      }
    }
  }, [visible, mode, initialValues, form]);

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 格式化数据
      const formData = {
        siteName: values.siteName,
        siteUrl: values.siteUrl,
        platformId: values.platformId,
        serverId: values.serverId,
        industry: values.industry || null,
        status: values.status,
        onlineDate: values.onlineDate ? values.onlineDate.format('YYYY-MM-DD') : null,
        expireDate: values.expireDate ? values.expireDate.format('YYYY-MM-DD') : null,
        projectAmount: values.projectAmount || null,
        renewalFee: values.renewalFee || null,
        notes: values.notes || null,
        hasOnboard: values.hasOnboard || false
      };

      let result;
      if (mode === 'create') {
        result = await WebsiteApi.createWebsite(formData);
      } else {
        result = await WebsiteApi.updateWebsite(initialValues!.id, formData);
      }

      message.success(mode === 'create' ? '网站创建成功！' : '网站更新成功！');
      onSuccess(formData);
      form.resetFields();

    } catch (error) {
      console.error('提交失败:', error);
      message.error(mode === 'create' ? '网站创建失败，请重试' : '网站更新失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 取消操作
  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 处理附件变化
  const handleAttachmentChange = () => {
    setAttachmentRefreshTrigger(prev => prev + 1);
  };

  return (
    <Modal
      title={
        <Space>
          <GlobalOutlined />
          <span>{mode === 'create' ? '新建网站' : '编辑网站'}</span>
        </Space>
      }
      open={visible}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={900}
      style={{ top: 20 }}
      okText={mode === 'create' ? '创建' : '保存'}
      cancelText="取消"
      destroyOnHidden
    >
      <Spin spinning={optionsLoading}>
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            status: 'active',
            hasOnboard: false
          }}
        >
          {/* 基本信息 */}
          <div style={{ marginBottom: 12 }}>
            <Text strong>基本信息</Text>
          </div>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="siteName"
                label="站点名称"
                rules={[
                  { required: true, message: '请输入站点名称' },
                  { min: 2, max: 50, message: '站点名称长度应在2-50个字符之间' }
                ]}
              >
                <Input placeholder="例如：企业官网、电商平台" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="platformId"
                label="平台类型"
                rules={[{ required: true, message: '请选择平台类型' }]}
              >
                <Select placeholder="请选择平台类型">
                  {platforms.map(platform => (
                    <Option key={platform.platform_id || platform.id} value={platform.platform_id || platform.id}>
                      {platform.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="industry"
                label="所属行业"
              >
                <Select placeholder="请选择所属行业" allowClear>
                  {industries.map(industry => (
                    <Option key={industry} value={industry}>
                      {industry}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="siteUrl"
            label="网站URL"
            rules={[
              { required: true, message: '请输入网站URL' },
              { type: 'url', message: 'URL格式不正确' }
            ]}
          >
            <Input placeholder="例如：https://example.com" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="serverId"
                label="服务器"
              >
                <ServerSelect
                  placeholder="选择服务器"
                  allowClear
                  style={{ width: '100%' }}
                  onAddServer={() => {
                    // 这里可以添加跳转到服务器管理页面的逻辑
                    window.open('/servers', '_blank');
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select>
                  <Option value="active">正常</Option>
                  <Option value="inactive">停用</Option>
                  <Option value="suspended">暂停</Option>
                  <Option value="maintenance">维护</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="hasOnboard"
                label="Onboard"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="已安装"
                  unCheckedChildren="未安装"
                />
              </Form.Item>
            </Col>
          </Row>

          {/* 时间和金额信息 */}
          <div style={{ marginBottom: 12, marginTop: 16 }}>
            <Text strong>时间和金额信息</Text>
          </div>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="onlineDate"
                label="上线时间"
              >
                <DatePicker style={{ width: '100%' }} placeholder="选择上线时间" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="expireDate"
                label="到期时间"
              >
                <DatePicker style={{ width: '100%' }} placeholder="选择到期时间" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="projectAmount"
                label="项目金额"
                rules={[
                  {
                    validator: (_, value) => {
                      if (value === undefined || value === null || value === '') {
                        return Promise.resolve();
                      }
                      const numValue = typeof value === 'string' ? parseFloat(value) : value;
                      if (isNaN(numValue) || numValue < 0) {
                        return Promise.reject(new Error('项目金额不能为负数'));
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入项目金额"
                  prefix="¥"
                  precision={2}
                  min={0}
                  max={9999999}
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="renewalFee"
                label="续费金额"
                rules={[
                  {
                    validator: (_, value) => {
                      if (value === undefined || value === null || value === '') {
                        return Promise.resolve();
                      }
                      const numValue = typeof value === 'string' ? parseFloat(value) : value;
                      if (isNaN(numValue) || numValue < 0) {
                        return Promise.reject(new Error('续费金额不能为负数'));
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入续费金额"
                  prefix="¥"
                  precision={2}
                  min={0}
                  max={999999}
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value!.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea
              rows={2}
              placeholder="请输入备注信息，如特殊配置、重要说明等"
              maxLength={500}
              showCount
            />
          </Form.Item>

          {/* 附件管理 - 仅编辑模式显示 */}
          {mode === 'edit' && initialValues && (
            <>
              <Divider />
              <div style={{ marginBottom: 12 }}>
                <Text strong>附件管理</Text>
              </div>
              <div style={{ marginBottom: 16 }}>
                <Button
                  type="dashed"
                  icon={<FileOutlined />}
                  onClick={() => setAttachmentVisible(true)}
                  block
                >
                  管理附件 (支持图片、PDF、表格文件上传和预览)
                </Button>
              </div>
            </>
          )}
        </Form>
      </Spin>

      {/* 附件管理器 */}
      {mode === 'edit' && initialValues && (
        <AttachmentManager
          visible={attachmentVisible}
          websiteId={initialValues.id}
          onCancel={() => setAttachmentVisible(false)}
          onAttachmentChange={handleAttachmentChange}
        />
      )}
    </Modal>
  );
};

export default WebsiteForm;
