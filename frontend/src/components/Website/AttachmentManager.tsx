import React, { useState, useEffect } from 'react';
import {
  Modal,
  Upload,
  List,
  Button,
  Space,
  Typography,
  Tag,
  Popconfirm,
  Input,
  Select,
  message,
  Image,
  Tooltip,
  Progress,
  Card,
  Row,
  Col,
  Empty
} from 'antd';
import {
  UploadOutlined,
  DeleteOutlined,
  DownloadOutlined,
  EyeOutlined,
  EditOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FileOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { UploadFile, UploadProps } from 'antd/es/upload/interface';
import { WebsiteAttachmentApi } from '../../services/websiteAttachment';
import { request } from '../../services/api';

const { Text, Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface AttachmentManagerProps {
  visible: boolean;
  websiteId: number;
  onCancel: () => void;
  onAttachmentChange?: () => void; // 附件变化时的回调
}

interface Attachment {
  id: number;
  fileName: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
  mimeType: string;
  category: 'image' | 'pdf' | 'excel' | 'word' | 'other';
  description?: string;
  uploadedBy: number;
  uploadedByName: string;
  isPreviewAvailable: boolean;
  downloadCount: number;
  lastAccessed?: string;
  createdAt: string;
}

const AttachmentManager: React.FC<AttachmentManagerProps> = ({
  visible,
  websiteId,
  onCancel,
  onAttachmentChange
}) => {
  const [attachments, setAttachments] = useState<Attachment[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [editingAttachment, setEditingAttachment] = useState<Attachment | null>(null);
  const [editDescription, setEditDescription] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  // 获取附件列表
  const fetchAttachments = async () => {
    if (!websiteId) return;
    
    setLoading(true);
    try {
      const response = await WebsiteAttachmentApi.getAttachments(websiteId, {
        category: categoryFilter === 'all' ? undefined : categoryFilter
      });
      if (response.success) {
        setAttachments(response.data.attachments || []);
      }
    } catch (error) {
      message.error('获取附件列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && websiteId) {
      fetchAttachments();
    }
  }, [visible, websiteId, categoryFilter]);

  // 文件上传配置
  const uploadProps: UploadProps = {
    name: 'file',
    multiple: true,
    fileList,
    beforeUpload: (file) => {
      // 检查文件类型
      const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml',
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword'
      ];

      if (!allowedTypes.includes(file.type)) {
        message.error('不支持的文件类型！支持图片、PDF、Excel、Word文档');
        return false;
      }

      // 检查文件大小 (50MB)
      if (file.size > 50 * 1024 * 1024) {
        message.error('文件大小不能超过50MB');
        return false;
      }

      return false; // 阻止自动上传
    },
    onChange: (info) => {
      setFileList(info.fileList);
    },
    onRemove: (file) => {
      setFileList(prev => prev.filter(item => item.uid !== file.uid));
    }
  };

  // 手动上传文件
  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请选择要上传的文件');
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i];
        if (file.originFileObj) {
          await WebsiteAttachmentApi.uploadAttachment(websiteId, file.originFileObj, {
            description: `上传的${file.name}`
          });
          setUploadProgress(Math.round(((i + 1) / fileList.length) * 100));
        }
      }

      message.success('文件上传成功');
      setFileList([]);

      // 清除附件相关的缓存
      request.clearCache();

      fetchAttachments();

      // 通知父组件附件已变化
      onAttachmentChange?.();
    } catch (error) {
      message.error('文件上传失败');
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  // 删除附件
  const handleDelete = async (attachment: Attachment) => {
    try {
      await WebsiteAttachmentApi.deleteAttachment(websiteId, attachment.id);
      message.success('附件删除成功');

      // 清除附件相关的缓存
      request.clearCache();

      fetchAttachments();

      // 通知父组件附件已变化
      onAttachmentChange?.();
    } catch (error) {
      message.error('删除附件失败');
    }
  };

  // 下载附件
  const handleDownload = async (attachment: Attachment) => {
    try {
      const url = WebsiteAttachmentApi.getDownloadUrl(websiteId, attachment.id);
      const link = document.createElement('a');
      link.href = url;
      link.download = attachment.originalName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success('开始下载');
    } catch (error) {
      message.error('下载失败');
    }
  };

  // 预览附件
  const handlePreview = (attachment: Attachment) => {
    if (!attachment.isPreviewAvailable) {
      message.warning('该文件类型不支持预览');
      return;
    }

    const previewUrl = WebsiteAttachmentApi.getPreviewUrl(websiteId, attachment.id);
    setPreviewUrl(previewUrl);
    setPreviewTitle(attachment.originalName);
    setPreviewVisible(true);
  };

  // 编辑描述
  const handleEditDescription = (attachment: Attachment) => {
    setEditingAttachment(attachment);
    setEditDescription(attachment.description || '');
  };

  // 保存描述
  const handleSaveDescription = async () => {
    if (!editingAttachment) return;

    try {
      await WebsiteAttachmentApi.updateAttachment(websiteId, editingAttachment.id, {
        description: editDescription
      });
      message.success('描述更新成功');
      setEditingAttachment(null);

      // 清除附件相关的缓存
      request.clearCache();

      fetchAttachments();
    } catch (error) {
      message.error('更新描述失败');
    }
  };

  // 获取文件图标
  const getFileIcon = (category: string, mimeType: string) => {
    switch (category) {
      case 'image':
        return <FileImageOutlined style={{ color: '#52c41a' }} />;
      case 'pdf':
        return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
      case 'excel':
        return <FileExcelOutlined style={{ color: '#1890ff' }} />;
      case 'word':
        return <FileWordOutlined style={{ color: '#1890ff' }} />;
      default:
        return <FileOutlined style={{ color: '#8c8c8c' }} />;
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <>
      <Modal
        title={
          <Space>
            <FileOutlined />
            <span>附件管理</span>
          </Space>
        }
        open={visible}
        onCancel={onCancel}
        width={1000}
        footer={null}
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col>
              <Select
                value={categoryFilter}
                onChange={setCategoryFilter}
                style={{ width: 120 }}
              >
                <Option value="all">全部</Option>
                <Option value="image">图片</Option>
                <Option value="pdf">PDF</Option>
                <Option value="excel">表格</Option>
                <Option value="word">文档</Option>
                <Option value="other">其他</Option>
              </Select>
            </Col>
            <Col flex={1}>
              <Upload {...uploadProps}>
                <Button icon={<UploadOutlined />}>选择文件</Button>
              </Upload>
            </Col>
            <Col>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleUpload}
                loading={uploading}
                disabled={fileList.length === 0}
              >
                上传
              </Button>
            </Col>
          </Row>

          {uploading && (
            <Progress
              percent={uploadProgress}
              status="active"
              style={{ marginTop: 8 }}
            />
          )}
        </div>

        <List
          loading={loading}
          dataSource={attachments}
          locale={{
            emptyText: <Empty description="暂无附件" />
          }}
          renderItem={(attachment) => (
            <List.Item
              actions={[
                attachment.isPreviewAvailable && (
                  <Tooltip title="预览">
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      onClick={() => handlePreview(attachment)}
                    />
                  </Tooltip>
                ),
                <Tooltip title="下载">
                  <Button
                    type="text"
                    icon={<DownloadOutlined />}
                    onClick={() => handleDownload(attachment)}
                  />
                </Tooltip>,
                <Tooltip title="编辑描述">
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => handleEditDescription(attachment)}
                  />
                </Tooltip>,
                <Popconfirm
                  title="确定要删除这个附件吗？"
                  onConfirm={() => handleDelete(attachment)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Tooltip title="删除">
                    <Button
                      type="text"
                      danger
                      icon={<DeleteOutlined />}
                    />
                  </Tooltip>
                </Popconfirm>
              ].filter(Boolean)}
            >
              <List.Item.Meta
                avatar={getFileIcon(attachment.category, attachment.mimeType)}
                title={
                  <Space>
                    <Text strong>{attachment.originalName}</Text>
                    <Tag color={
                      attachment.category === 'image' ? 'green' :
                      attachment.category === 'pdf' ? 'red' :
                      attachment.category === 'excel' ? 'blue' :
                      attachment.category === 'word' ? 'blue' : 'default'
                    }>
                      {attachment.category.toUpperCase()}
                    </Tag>
                    {attachment.isPreviewAvailable && (
                      <Tag color="orange">可预览</Tag>
                    )}
                  </Space>
                }
                description={
                  <div>
                    <div>
                      <Text type="secondary">
                        大小: {formatFileSize(attachment.fileSize)} | 
                        上传者: {attachment.uploadedByName} | 
                        下载次数: {attachment.downloadCount}
                      </Text>
                    </div>
                    {attachment.description && (
                      <div style={{ marginTop: 4 }}>
                        <Text>{attachment.description}</Text>
                      </div>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Modal>

      {/* 预览模态框 */}
      <Modal
        title={previewTitle}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width="80%"
        style={{ top: 20 }}
      >
        {previewUrl && (
          <div style={{ textAlign: 'center' }}>
            {previewUrl.includes('.pdf') ? (
              <iframe
                src={previewUrl}
                style={{ width: '100%', height: '600px', border: 'none' }}
                title={previewTitle}
              />
            ) : (
              <Image
                src={previewUrl}
                alt={previewTitle}
                style={{ maxWidth: '100%', maxHeight: '600px' }}
              />
            )}
          </div>
        )}
      </Modal>

      {/* 编辑描述模态框 */}
      <Modal
        title="编辑附件描述"
        open={!!editingAttachment}
        onOk={handleSaveDescription}
        onCancel={() => setEditingAttachment(null)}
        okText="保存"
        cancelText="取消"
      >
        <TextArea
          value={editDescription}
          onChange={(e) => setEditDescription(e.target.value)}
          placeholder="请输入附件描述"
          rows={4}
          maxLength={500}
          showCount
        />
      </Modal>
    </>
  );
};

export default AttachmentManager;
