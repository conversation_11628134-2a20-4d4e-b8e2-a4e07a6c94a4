import React, { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Button,
  Space,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Tag,
  Tooltip,
  Typography
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  CopyOutlined,
  KeyOutlined,
  UserOutlined
} from '@ant-design/icons';
import { WebsiteCredential } from '../../types';
import { WebsiteApi } from '../../services/website';

const { Option } = Select;
const { Text } = Typography;

interface PasswordManagerProps {
  visible: boolean;
  onClose: () => void;
  websiteId: number;
  websiteName: string;
}

const PasswordManager: React.FC<PasswordManagerProps> = ({
  visible,
  onClose,
  websiteId,
  websiteName
}) => {
  const [credentials, setCredentials] = useState<WebsiteCredential[]>([]);
  const [loading, setLoading] = useState(false);
  const [formVisible, setFormVisible] = useState(false);
  const [editingCredential, setEditingCredential] = useState<WebsiteCredential | null>(null);
  const [visiblePasswords, setVisiblePasswords] = useState<Record<number, boolean>>({});
  const [form] = Form.useForm();

  // 账号类型配置
  const accountTypeConfig = {
    admin: { label: '管理员', color: 'red', icon: <UserOutlined /> },
    user: { label: '用户', color: 'blue', icon: <UserOutlined /> }
  };

  // 加载凭据数据
  const loadCredentials = async () => {
    setLoading(true);
    try {
      const response = await fetch(`http://localhost:3001/api/v1/websites/${websiteId}/credentials`);
      const result = await response.json();

      if (result.success) {
        setCredentials(result.data || []);
      } else {
        message.error(result.message || '加载密码信息失败');
      }
    } catch (error) {
      console.error('加载密码信息失败:', error);
      message.error('加载密码信息失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      loadCredentials();
    }
  }, [visible, websiteId]);

  // 处理新增
  const handleAdd = () => {
    setEditingCredential(null);
    form.resetFields();
    setFormVisible(true);
  };

  // 处理编辑
  const handleEdit = (credential: WebsiteCredential) => {
    setEditingCredential(credential);
    form.setFieldsValue(credential);
    setFormVisible(true);
  };

  // 处理删除
  const handleDelete = async (id: number) => {
    try {
      const response = await fetch(`http://localhost:3001/api/v1/websites/${websiteId}/credentials/${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        setCredentials(prev => prev.filter(c => c.id !== id));
        message.success('删除成功');
      } else {
        message.error(result.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 处理表单提交
  const handleFormSubmit = async (values: any) => {
    try {
      const credentialData = {
        accountType: values.accountType,
        username: values.username,
        password: values.password,
        description: values.description
      };

      if (editingCredential) {
        // 更新
        const response = await fetch(`http://localhost:3001/api/v1/websites/${websiteId}/credentials/${editingCredential.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(credentialData),
        });

        const result = await response.json();

        if (result.success) {
          setCredentials(prev => prev.map(c =>
            c.id === editingCredential.id ? result.data : c
          ));
          message.success('更新成功');
        } else {
          message.error(result.message || '更新失败');
          return;
        }
      } else {
        // 新增
        const response = await fetch(`http://localhost:3001/api/v1/websites/${websiteId}/credentials`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(credentialData),
        });

        const result = await response.json();

        if (result.success) {
          setCredentials(prev => [...prev, result.data]);
          message.success('添加成功');
        } else {
          message.error(result.message || '添加失败');
          return;
        }
      }

      setFormVisible(false);
      setEditingCredential(null);
      form.resetFields();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 切换密码可见性
  const togglePasswordVisibility = (id: number) => {
    setVisiblePasswords(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  // 复制到剪贴板
  const copyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success(`${type}已复制到剪贴板`);
    }).catch(() => {
      message.error('复制失败');
    });
  };

  // 生成随机密码
  const generatePassword = (length: number = 12) => {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  };

  // 设置生成的密码到表单
  const handleGeneratePassword = () => {
    const newPassword = generatePassword(16);
    form.setFieldsValue({ password: newPassword });
    message.success('密码已生成');
  };

  // 表格列定义
  const columns = [
    {
      title: '账号类型',
      dataIndex: 'accountType',
      key: 'accountType',
      width: 120,
      render: (type: string) => {
        const config = accountTypeConfig[type as keyof typeof accountTypeConfig];
        return (
          <Tag color={config?.color} icon={config?.icon}>
            {config?.label}
          </Tag>
        );
      }
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      width: 150,
      render: (username: string) => (
        <Text copyable={{ text: username }}>{username}</Text>
      )
    },
    {
      title: '密码',
      dataIndex: 'password',
      key: 'password',
      width: 200,
      render: (password: string, record: WebsiteCredential) => (
        <Space>
          <Text>
            {visiblePasswords[record.id] ? password : '••••••••'}
          </Text>
          <Button
            type="text"
            size="small"
            icon={visiblePasswords[record.id] ? <EyeInvisibleOutlined /> : <EyeOutlined />}
            onClick={() => togglePasswordVisibility(record.id)}
          />
          <Button
            type="text"
            size="small"
            icon={<CopyOutlined />}
            onClick={() => copyToClipboard(password, '密码')}
          />
        </Space>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right' as const,
      render: (_, record: WebsiteCredential) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个账号吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <>
      <Modal
        title={
          <Space>
            <KeyOutlined />
            {websiteName} - 密码管理
          </Space>
        }
        open={visible}
        onCancel={onClose}
        width={800}
        footer={null}
        destroyOnClose
      >
        <div className="mb-4">
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
            className="mb-4"
          >
            添加账号
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={credentials}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 600 }}
          size="small"
        />
      </Modal>

      {/* 添加/编辑表单 */}
      <Modal
        title={editingCredential ? '编辑账号' : '添加账号'}
        open={formVisible}
        onCancel={() => setFormVisible(false)}
        onOk={() => form.submit()}
        width={500}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
        >
          <Form.Item
            name="accountType"
            label="账号类型"
            rules={[{ required: true, message: '请选择账号类型' }]}
          >
            <Select placeholder="选择账号类型">
              {Object.entries(accountTypeConfig).map(([key, config]) => (
                <Option key={key} value={key}>
                  <Space>
                    {config.icon}
                    {config.label}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="输入用户名" />
          </Form.Item>

          <Form.Item
            name="password"
            label={
              <Space>
                密码
                <Button
                  type="link"
                  size="small"
                  onClick={handleGeneratePassword}
                  className="p-0 h-auto"
                >
                  生成密码
                </Button>
              </Space>
            }
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password placeholder="输入密码" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={2} placeholder="输入账号描述" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default PasswordManager;
