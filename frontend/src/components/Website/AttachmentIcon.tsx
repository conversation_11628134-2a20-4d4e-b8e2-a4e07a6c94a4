import React, { useState, useEffect, useRef } from 'react';
import { Tooltip, message, Modal, Button, Space } from 'antd';
import {
  FileImageOutlined,
  PaperClipOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  ReloadOutlined,
  DownloadOutlined,
  FileWordOutlined
} from '@ant-design/icons';
import { renderAsync } from 'docx-preview';

// 添加docx预览的样式
const docxStyles = `
  .docx-wrapper {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
  }
  .docx-wrapper p {
    margin: 0.5em 0;
  }
  .docx-wrapper h1, .docx-wrapper h2, .docx-wrapper h3, .docx-wrapper h4, .docx-wrapper h5, .docx-wrapper h6 {
    margin: 1em 0 0.5em 0;
    font-weight: bold;
  }
  .docx-wrapper table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
  }
  .docx-wrapper td, .docx-wrapper th {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }
  .docx-wrapper th {
    background-color: #f5f5f5;
    font-weight: bold;
  }
`;

// 注入样式到页面
if (typeof document !== 'undefined' && !document.getElementById('docx-preview-styles')) {
  const styleElement = document.createElement('style');
  styleElement.id = 'docx-preview-styles';
  styleElement.textContent = docxStyles;
  document.head.appendChild(styleElement);
}

interface AttachmentIconProps {
  websiteId: number;
}

const AttachmentIcon: React.FC<AttachmentIconProps> = ({ websiteId }) => {
  const [hasAttachment, setHasAttachment] = useState<boolean | null>(null);
  const [attachmentInfo, setAttachmentInfo] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);
  const iconRef = useRef<HTMLDivElement>(null);

  // 预览相关状态
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [previewType, setPreviewType] = useState<'image' | 'pdf' | 'docx'>('image');
  const docxContainerRef = useRef<HTMLDivElement>(null);

  // 图片操作状态
  const [imageScale, setImageScale] = useState(1);
  const [imageRotation, setImageRotation] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // Intersection Observer 用于延迟加载
  useEffect(() => {
    if (!iconRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin: '100px', // 提前100px开始加载
        threshold: 0.1
      }
    );

    observer.observe(iconRef.current);

    return () => {
      observer.disconnect();
    };
  }, []);

  // 只有当组件可见时才检查附件
  useEffect(() => {
    if (!isVisible || hasChecked || !websiteId) return;

    const checkAttachment = async () => {
      try {
        // 添加随机延迟，避免大量并发请求
        const delay = Math.random() * 3000; // 0-3秒随机延迟
        await new Promise(resolve => setTimeout(resolve, delay));

        const response = await fetch(`http://localhost:3001/api/v1/websites/${websiteId}/attachments?fast=true&limit=1`, {
          headers: {
            'Authorization': 'Bearer dev-token'
          },
          signal: AbortSignal.timeout(8000) // 8秒超时
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data.attachments && data.data.attachments.length > 0) {
            setHasAttachment(true);
            setAttachmentInfo(data.data.attachments[0]);
          } else {
            setHasAttachment(false);
          }
        } else {
          setHasAttachment(false);
        }
      } catch (error) {
        // 静默处理错误，不影响主要功能
        setHasAttachment(false);
      } finally {
        setHasChecked(true);
      }
    };

    checkAttachment();
  }, [isVisible, hasChecked, websiteId]);

  // 重置预览状态
  const resetPreviewState = () => {
    setImageScale(1);
    setImageRotation(0);
    setIsFullscreen(false);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
  };

  // 预览附件
  const handleClick = async () => {
    if (!hasAttachment || !attachmentInfo) return;

    if (attachmentInfo.isPreviewAvailable) {
      try {
        // 获取文件blob用于预览
        const response = await fetch(`http://localhost:3001/api/v1/websites/${websiteId}/attachments/${attachmentInfo.id}/preview`, {
          headers: {
            'Authorization': 'Bearer dev-token'
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const blob = await response.blob();

        setPreviewTitle(attachmentInfo.originalName);
        resetPreviewState();

        // 根据文件类型设置预览类型
        if (attachmentInfo.category === 'word') {
          setPreviewType('docx');
          setPreviewUrl(''); // docx不需要URL，直接渲染到容器
          setPreviewVisible(true);

          // 等待Modal渲染完成后再渲染docx
          setTimeout(async () => {
            if (docxContainerRef.current) {
              try {
                await renderAsync(blob, docxContainerRef.current, undefined, {
                  className: 'docx-wrapper',
                  inWrapper: true,
                  ignoreWidth: false,
                  ignoreHeight: false,
                  ignoreFonts: false,
                  breakPages: true,
                  ignoreLastRenderedPageBreak: true,
                  experimental: false,
                  trimXmlDeclaration: true,
                  useBase64URL: false,
                  useMathMLPolyfill: false,
                  showChanges: false,
                  debug: false
                });
              } catch (error) {
                console.error('渲染docx失败:', error);
                message.error('Word文档渲染失败');
              }
            }
          }, 100);
        } else {
          // 图片和PDF使用blob URL
          const blobUrl = URL.createObjectURL(blob);
          setPreviewUrl(blobUrl);
          setPreviewType(attachmentInfo.category === 'pdf' ? 'pdf' : 'image');
          setPreviewVisible(true);
        }
      } catch (error) {
        console.error('预览失败:', error);
        message.error('预览失败，请稍后重试');
      }
    } else {
      // 直接下载
      const downloadUrl = `http://localhost:3001/api/v1/websites/${websiteId}/attachments/${attachmentInfo.id}/download`;
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = attachmentInfo.originalName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success('开始下载');
    }
  };

  // 图片缩放控制
  const handleZoomIn = () => {
    setImageScale(prev => Math.min(prev + 0.2, 3));
  };

  const handleZoomOut = () => {
    setImageScale(prev => Math.max(prev - 0.2, 0.2));
  };

  const handleResetZoom = () => {
    setImageScale(1);
  };

  // 图片旋转控制
  const handleRotateLeft = () => {
    setImageRotation(prev => prev - 90);
  };

  const handleRotateRight = () => {
    setImageRotation(prev => prev + 90);
  };

  // 全屏控制
  const handleToggleFullscreen = () => {
    setIsFullscreen(prev => !prev);
  };

  // 图片拖拽控制
  const handleMouseDown = (e: React.MouseEvent) => {
    if (previewType === 'image') {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - imagePosition.x,
        y: e.clientY - imagePosition.y
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && previewType === 'image') {
      setImagePosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 重置图片位置
  const handleResetPosition = () => {
    setImagePosition({ x: 0, y: 0 });
  };

  // 关闭预览
  const handleClosePreview = () => {
    setPreviewVisible(false);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl); // 清理blob URL
    }
    setPreviewUrl('');

    // 清理docx容器内容
    if (docxContainerRef.current) {
      docxContainerRef.current.innerHTML = '';
    }
  };

  // 加载中或未开始检查
  if (hasAttachment === null) {
    return (
      <div ref={iconRef} style={{ color: '#ccc', fontSize: 12 }}>
        <PaperClipOutlined style={{ fontSize: 14 }} />
      </div>
    );
  }

  // 无附件
  if (hasAttachment === false) {
    return (
      <div style={{ color: '#999', fontSize: 12, textAlign: 'center' }}>
        <PaperClipOutlined style={{ fontSize: 14 }} />
        <div style={{ fontSize: 10 }}>无附件</div>
      </div>
    );
  }

  // 有附件
  return (
    <>
      <Tooltip title={`${attachmentInfo?.originalName || '附件'} - 点击${attachmentInfo?.isPreviewAvailable ? '预览' : '下载'}`}>
        <div
          onClick={handleClick}
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 24,
            height: 24,
            borderRadius: 4,
            border: '1px solid #d9d9d9',
            backgroundColor: attachmentInfo?.isPreviewAvailable ? '#f6ffed' : '#fafafa',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            position: 'relative'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.borderColor = '#1890ff';
            e.currentTarget.style.backgroundColor = attachmentInfo?.isPreviewAvailable ? '#f0f9ff' : '#f5f5f5';
            e.currentTarget.style.transform = 'scale(1.1)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.borderColor = '#d9d9d9';
            e.currentTarget.style.backgroundColor = attachmentInfo?.isPreviewAvailable ? '#f6ffed' : '#fafafa';
            e.currentTarget.style.transform = 'scale(1)';
          }}
        >
          {attachmentInfo?.category === 'word' ? (
            <FileWordOutlined style={{ fontSize: 14, color: '#722ed1' }} />
          ) : (
            <FileImageOutlined style={{ fontSize: 14, color: '#52c41a' }} />
          )}
          {attachmentInfo?.isPreviewAvailable && (
            <div
              style={{
                position: 'absolute',
                top: -2,
                right: -2,
                width: 6,
                height: 6,
                borderRadius: '50%',
                backgroundColor: '#52c41a',
                border: '1px solid white'
              }}
            />
          )}
        </div>
      </Tooltip>

      {/* 预览模态框 */}
      <Modal
        title={previewTitle}
        open={previewVisible}
        onCancel={handleClosePreview}
        footer={[
          <Button key="download" icon={<DownloadOutlined />} onClick={() => {
            const link = document.createElement('a');
            link.href = previewUrl;
            link.download = previewTitle;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            message.success('开始下载');
          }}>
            下载
          </Button>,
          <Button key="close" onClick={handleClosePreview}>
            关闭
          </Button>
        ]}
        width={isFullscreen ? '100vw' : '80%'}
        style={{
          top: isFullscreen ? 0 : 20,
          maxWidth: isFullscreen ? '100vw' : 'none',
          height: isFullscreen ? '100vh' : 'auto'
        }}
        styles={{
          body: {
            padding: 0,
            height: isFullscreen ? 'calc(100vh - 110px)' : '600px',
            overflow: 'hidden'
          }
        }}
        destroyOnHidden
      >
        {(previewUrl || previewType === 'docx') && (
          <div style={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
            {/* 工具栏 */}
            {previewType === 'image' && (
              <div style={{
                padding: '8px 16px',
                borderBottom: '1px solid #f0f0f0',
                backgroundColor: '#fafafa',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}>
                <Space size="small">
                  <Button type="text" size="small" icon={<ZoomOutOutlined />} onClick={handleZoomOut} title="缩小" />
                  <Button type="text" size="small" icon={<ZoomInOutlined />} onClick={handleZoomIn} title="放大" />
                  <Button type="text" size="small" icon={<ReloadOutlined />} onClick={handleResetZoom} title="重置缩放" />
                  <Button type="text" size="small" icon={<RotateLeftOutlined />} onClick={handleRotateLeft} title="向左旋转" />
                  <Button type="text" size="small" icon={<RotateRightOutlined />} onClick={handleRotateRight} title="向右旋转" />
                  <Button type="text" size="small" icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />} onClick={handleToggleFullscreen} title={isFullscreen ? "退出全屏" : "全屏"} />
                  <span style={{ marginLeft: 8, fontSize: 12, color: '#666' }}>
                    缩放: {Math.round(imageScale * 100)}% | 旋转: {imageRotation}°
                  </span>
                </Space>
              </div>
            )}

            {/* 预览内容区域 */}
            <div
              style={{
                flex: 1,
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                alignItems: previewType === 'docx' ? 'flex-start' : 'center',
                backgroundColor: '#f5f5f5',
                position: 'relative',
                overflow: previewType === 'docx' ? 'auto' : 'hidden',
                padding: previewType === 'docx' ? '20px' : '0'
              }}
              onMouseDown={previewType === 'image' ? handleMouseDown : undefined}
              onMouseMove={previewType === 'image' ? handleMouseMove : undefined}
              onMouseUp={previewType === 'image' ? handleMouseUp : undefined}
              onMouseLeave={previewType === 'image' ? handleMouseUp : undefined}
            >
              {previewType === 'pdf' ? (
                <iframe
                  src={previewUrl}
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                    backgroundColor: 'white'
                  }}
                  title={previewTitle}
                />
              ) : previewType === 'docx' ? (
                <div
                  ref={docxContainerRef}
                  style={{
                    width: '100%',
                    maxWidth: '800px',
                    backgroundColor: 'white',
                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                    borderRadius: '4px',
                    padding: '20px',
                    minHeight: '400px'
                  }}
                />
              ) : (
                <img
                  src={previewUrl}
                  alt={previewTitle}
                  style={{
                    maxWidth: isFullscreen ? '100%' : 'none',
                    maxHeight: isFullscreen ? '100%' : 'none',
                    transform: `scale(${imageScale}) rotate(${imageRotation}deg) translate(${imagePosition.x}px, ${imagePosition.y}px)`,
                    cursor: isDragging ? 'grabbing' : 'grab',
                    transition: isDragging ? 'none' : 'transform 0.2s ease',
                    userSelect: 'none',
                    pointerEvents: 'none'
                  }}
                  draggable={false}
                />
              )}
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default AttachmentIcon;
