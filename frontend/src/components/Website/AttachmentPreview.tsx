import React, { useState, useEffect } from 'react';
import {
  Button,
  Space,
  Tooltip,
  Modal,
  Image,
  Spin,
  Empty,
  Tag,
  message,
  Popover,
  List
} from 'antd';
import {
  FileImageOutlined,
  FilePdfOutlined,
  FileExcelOutlined,
  FileWordOutlined,
  FileOutlined,
  EyeOutlined,
  DownloadOutlined,
  PaperClipOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined
} from '@ant-design/icons';
import { WebsiteAttachmentApi, WebsiteAttachment } from '../../services/websiteAttachment';

interface AttachmentPreviewProps {
  websiteId: number;
  maxDisplay?: number; // 最多显示的附件数量
  showCount?: boolean; // 是否显示附件数量
  lazy?: boolean; // 延迟加载
  onlyShowIcon?: boolean; // 只显示图标，不加载实际数据
  refreshTrigger?: number; // 刷新触发器，变化时重新加载数据
}

const AttachmentPreview: React.FC<AttachmentPreviewProps> = ({
  websiteId,
  maxDisplay = 3,
  showCount = true,
  lazy = false,
  onlyShowIcon = false,
  refreshTrigger
}) => {
  const [attachments, setAttachments] = useState<WebsiteAttachment[]>([]);
  const [loading, setLoading] = useState(false);
  const [hasLoaded, setHasLoaded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [previewType, setPreviewType] = useState<'image' | 'pdf'>('image');
  const [imageScale, setImageScale] = useState(1);
  const [imageRotation, setImageRotation] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [imagePosition, setImagePosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // 获取附件列表
  const fetchAttachments = async (forceRefresh = false) => {
    if (!websiteId || (hasLoaded && !forceRefresh)) return;

    setLoading(true);
    try {
      // 如果强制刷新，使用非缓存API；否则使用缓存API
      const apiCall = forceRefresh
        ? WebsiteAttachmentApi.getAttachments(websiteId, {
            limit: onlyShowIcon ? 1 : 50
          })
        : WebsiteAttachmentApi.getAttachmentsCached(websiteId, {
            limit: onlyShowIcon ? 1 : 50,
            cacheTime: 10 * 60 * 1000,
            fast: onlyShowIcon
          });

      const response = await Promise.race([
        apiCall,
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('附件加载超时')), 3000)
        )
      ]);

      if (response.success) {
        const attachmentList = response.data.attachments || [];
        setAttachments(attachmentList);
        setHasLoaded(true);
      } else {
        setAttachments([]);
        setHasLoaded(true);
      }
    } catch (error) {
      setAttachments([]);
      setHasLoaded(true);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时立即加载数据
  useEffect(() => {
    if (websiteId) {
      fetchAttachments();
    }
  }, [websiteId]);

  // 监听刷新触发器
  useEffect(() => {
    if (refreshTrigger !== undefined) {
      // 强制刷新数据
      fetchAttachments(true);
    }
  }, [refreshTrigger]);

  // 鼠标悬停时加载数据（延迟加载模式）
  const handleMouseEnter = () => {
    setIsHovered(true);
    if (lazy && !hasLoaded) {
      fetchAttachments();
    }
  };

  // 获取文件图标
  const getFileIcon = (category: string, mimeType: string, isPreviewable: boolean = false, size: number = 16) => {
    const iconProps = { style: { fontSize: size } };

    switch (category) {
      case 'image':
        return <FileImageOutlined {...iconProps} style={{ color: '#52c41a', fontSize: size }} />;
      case 'pdf':
        return <FilePdfOutlined {...iconProps} style={{ color: '#ff4d4f', fontSize: size }} />;
      case 'excel':
        return <FileExcelOutlined {...iconProps} style={{ color: '#1890ff', fontSize: size }} />;
      case 'word':
        return <FileWordOutlined {...iconProps} style={{ color: '#722ed1', fontSize: size }} />;
      default:
        return <FileOutlined {...iconProps} style={{ color: '#8c8c8c', fontSize: size }} />;
    }
  };

  // 重置预览状态
  const resetPreviewState = () => {
    setImageScale(1);
    setImageRotation(0);
    setIsFullscreen(false);
    setImagePosition({ x: 0, y: 0 });
    setIsDragging(false);
  };

  // 预览附件
  const handlePreview = async (attachment: WebsiteAttachment) => {
    console.log('预览附件:', attachment);

    if (!attachment.isPreviewAvailable) {
      message.warning('该文件类型不支持预览');
      return;
    }

    try {
      // 通过API获取文件blob，这样可以携带认证头
      const response = await fetch(WebsiteAttachmentApi.getPreviewUrl(websiteId, attachment.id), {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || 'dev-token'}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const blob = await response.blob();
      const blobUrl = URL.createObjectURL(blob);

      console.log('预览URL:', blobUrl);

      setPreviewUrl(blobUrl);
      setPreviewTitle(attachment.originalName);
      setPreviewType(attachment.category === 'pdf' ? 'pdf' : 'image');
      resetPreviewState();
      setPreviewVisible(true);

      // 更新访问记录
      WebsiteAttachmentApi.updateAttachment(websiteId, attachment.id, {
        description: attachment.description
      }).catch((error) => {
        console.warn('更新访问记录失败:', error);
        // 忽略更新错误，不影响预览功能
      });
    } catch (error) {
      console.error('预览失败:', error);
      message.error('预览失败，请稍后重试');
    }
  };

  // 图片缩放控制
  const handleZoomIn = () => {
    setImageScale(prev => Math.min(prev + 0.2, 3));
  };

  const handleZoomOut = () => {
    setImageScale(prev => Math.max(prev - 0.2, 0.2));
  };

  const handleResetZoom = () => {
    setImageScale(1);
  };

  // 图片旋转控制
  const handleRotateLeft = () => {
    setImageRotation(prev => prev - 90);
  };

  const handleRotateRight = () => {
    setImageRotation(prev => prev + 90);
  };

  // 全屏控制
  const handleToggleFullscreen = () => {
    setIsFullscreen(prev => !prev);
  };

  // 图片拖拽控制
  const handleMouseDown = (e: React.MouseEvent) => {
    if (previewType === 'image') {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - imagePosition.x,
        y: e.clientY - imagePosition.y
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && previewType === 'image') {
      setImagePosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // 重置图片位置
  const handleResetPosition = () => {
    setImagePosition({ x: 0, y: 0 });
  };

  // 下载附件
  const handleDownload = async (attachment: WebsiteAttachment) => {
    try {
      // 通过API获取文件blob，这样可以携带认证头
      const response = await fetch(WebsiteAttachmentApi.getDownloadUrl(websiteId, attachment.id), {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || 'dev-token'}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = attachment.originalName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url); // 清理blob URL
      message.success('开始下载');
    } catch (error) {
      console.error('下载失败:', error);
      message.error('下载失败');
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  // 只显示图标模式 - 简化逻辑
  if (onlyShowIcon) {
    // 正在加载
    if (loading) {
      return <Spin size="small" />;
    }

    // 已加载完成
    if (hasLoaded) {
      // 没有附件
      if (attachments.length === 0) {
        return (
          <div style={{ color: '#999', fontSize: 12, textAlign: 'center' }}>
            <PaperClipOutlined style={{ fontSize: 14 }} />
            <div style={{ fontSize: 10 }}>无附件</div>
          </div>
        );
      }

      // 有附件 - 显示第一个附件的图标
      const attachment = attachments[0];
      return (
        <Tooltip title={`${attachment.originalName} - ${attachment.isPreviewAvailable ? '点击预览' : '点击下载'}`}>
          <div
            onClick={() => attachment.isPreviewAvailable ? handlePreview(attachment) : handleDownload(attachment)}
            style={{
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 24,
              height: 24,
              borderRadius: 4,
              border: '1px solid #d9d9d9',
              backgroundColor: attachment.isPreviewAvailable ? '#f6ffed' : '#fafafa',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              position: 'relative'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.borderColor = '#1890ff';
              e.currentTarget.style.backgroundColor = attachment.isPreviewAvailable ? '#f0f9ff' : '#f5f5f5';
              e.currentTarget.style.transform = 'scale(1.1)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.borderColor = '#d9d9d9';
              e.currentTarget.style.backgroundColor = attachment.isPreviewAvailable ? '#f6ffed' : '#fafafa';
              e.currentTarget.style.transform = 'scale(1)';
            }}
          >
            {getFileIcon(attachment.category, attachment.mimeType, attachment.isPreviewAvailable, 14)}
            {attachment.isPreviewAvailable && (
              <div
                style={{
                  position: 'absolute',
                  top: -2,
                  right: -2,
                  width: 6,
                  height: 6,
                  borderRadius: '50%',
                  backgroundColor: '#52c41a',
                  border: '1px solid white'
                }}
              />
            )}
          </div>
        </Tooltip>
      );
    }

    // 未开始加载 - 显示加载提示
    return (
      <div
        style={{
          display: 'inline-flex',
          alignItems: 'center',
          cursor: 'pointer',
          color: '#1890ff'
        }}
        onClick={() => fetchAttachments(true)}
        title={`点击加载附件 (websiteId: ${websiteId})`}
      >
        <PaperClipOutlined style={{ fontSize: 14 }} />
        <span style={{ marginLeft: 4, fontSize: 12 }}>...</span>
      </div>
    );
  }

  if (loading) {
    return <Spin size="small" />;
  }

  if (attachments.length === 0) {
    return (
      <div style={{ color: '#999', fontSize: 12 }}>
        <PaperClipOutlined /> 无附件
      </div>
    );
  }

  // 显示的附件（限制数量）
  const displayAttachments = attachments.slice(0, maxDisplay);
  const remainingCount = attachments.length - maxDisplay;



  return (
    <>
      <div>
        <Space size={4} wrap>
          {displayAttachments.map((attachment) => (
            <Tooltip
              key={attachment.id}
              title={`${attachment.originalName} (${formatFileSize(attachment.fileSize)})${attachment.isPreviewAvailable ? ' - 点击预览' : ' - 点击下载'}`}
            >
              <div
                onClick={() => attachment.isPreviewAvailable ? handlePreview(attachment) : handleDownload(attachment)}
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 32,
                  height: 32,
                  borderRadius: 6,
                  border: '1px solid #d9d9d9',
                  backgroundColor: attachment.isPreviewAvailable ? '#f6ffed' : '#fafafa',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  position: 'relative'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = '#1890ff';
                  e.currentTarget.style.backgroundColor = attachment.isPreviewAvailable ? '#f0f9ff' : '#f5f5f5';
                  e.currentTarget.style.transform = 'scale(1.05)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = '#d9d9d9';
                  e.currentTarget.style.backgroundColor = attachment.isPreviewAvailable ? '#f6ffed' : '#fafafa';
                  e.currentTarget.style.transform = 'scale(1)';
                }}
              >
                {getFileIcon(attachment.category, attachment.mimeType, attachment.isPreviewAvailable, 16)}
                {attachment.isPreviewAvailable && (
                  <div
                    style={{
                      position: 'absolute',
                      top: -2,
                      right: -2,
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      backgroundColor: '#52c41a',
                      border: '1px solid white'
                    }}
                  />
                )}
              </div>
            </Tooltip>
          ))}
          
          {remainingCount > 0 && (
            <Popover
              content={
                <div style={{ maxWidth: 300 }}>
                  <List
                    size="small"
                    dataSource={attachments}
                    renderItem={(attachment) => (
                      <List.Item
                        actions={[
                          attachment.isPreviewAvailable && (
                            <Tooltip title="预览">
                              <Button
                                type="text"
                                size="small"
                                icon={<EyeOutlined />}
                                onClick={() => handlePreview(attachment)}
                              />
                            </Tooltip>
                          ),
                          <Tooltip title="下载">
                            <Button
                              type="text"
                              size="small"
                              icon={<DownloadOutlined />}
                              onClick={() => handleDownload(attachment)}
                            />
                          </Tooltip>
                        ].filter(Boolean)}
                      >
                        <List.Item.Meta
                          avatar={getFileIcon(attachment.category, attachment.mimeType, attachment.isPreviewAvailable, 16)}
                          title={
                            <div style={{ fontSize: 12 }}>
                              {attachment.originalName.length > 20
                                ? attachment.originalName.substring(0, 20) + '...'
                                : attachment.originalName
                              }
                            </div>
                          }
                          description={
                            <div style={{ fontSize: 11, color: '#999' }}>
                              {formatFileSize(attachment.fileSize)}
                              {attachment.isPreviewAvailable && (
                                <Tag color="orange" style={{ marginLeft: 4, fontSize: 10 }}>
                                  可预览
                                </Tag>
                              )}
                            </div>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </div>
              }
              title={`所有附件 (${attachments.length})`}
              trigger="click"
              placement="bottomLeft"
            >
              <Button
                type="text"
                size="small"
                style={{
                  padding: '2px 4px',
                  height: 'auto',
                  fontSize: 11,
                  color: '#1890ff'
                }}
              >
                +{remainingCount}
              </Button>
            </Popover>
          )}
        </Space>
        
        {showCount && (
          <div style={{ fontSize: 11, color: '#999', marginTop: 2 }}>
            <PaperClipOutlined /> {attachments.length} 个附件
          </div>
        )}
      </div>

      {/* 预览模态框 */}
      <Modal
        title={
          <div style={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            maxWidth: 'calc(100% - 100px)' // 为关闭按钮留出足够空间
          }}>
            {previewTitle}
          </div>
        }
        open={previewVisible}
        onCancel={() => {
          setPreviewVisible(false);
          setIsFullscreen(false);
          // 清理blob URL，避免内存泄漏
          if (previewUrl && previewUrl.startsWith('blob:')) {
            URL.revokeObjectURL(previewUrl);
          }
          setPreviewUrl('');
        }}
        footer={null}
        width={isFullscreen ? "100%" : "80%"}
        style={{
          top: isFullscreen ? 0 : 20,
          maxWidth: isFullscreen ? "100%" : "none",
          height: isFullscreen ? "100vh" : "auto"
        }}
        styles={{
          body: {
            padding: isFullscreen ? 0 : 24,
            height: isFullscreen ? "calc(100vh - 55px)" : "auto",
            overflow: "auto"
          }
        }}
        destroyOnHidden
        centered={!isFullscreen}
      >
        {previewUrl && (
          <>
            {/* 图片工具栏 */}
            {previewType === 'image' && (
              <div style={{
                marginBottom: 16,
                textAlign: 'center',
                borderBottom: '1px solid #f0f0f0',
                paddingBottom: 12
              }}>
                <Space size="small" wrap>
                  <Tooltip title="放大">
                    <Button size="small" icon={<ZoomInOutlined />} onClick={handleZoomIn} />
                  </Tooltip>
                  <Tooltip title="缩小">
                    <Button size="small" icon={<ZoomOutOutlined />} onClick={handleZoomOut} />
                  </Tooltip>
                  <Tooltip title="重置缩放">
                    <Button size="small" onClick={handleResetZoom}>1:1</Button>
                  </Tooltip>
                  <Tooltip title="重置位置">
                    <Button size="small" onClick={handleResetPosition}>居中</Button>
                  </Tooltip>
                  <Tooltip title="向左旋转">
                    <Button size="small" icon={<RotateLeftOutlined />} onClick={handleRotateLeft} />
                  </Tooltip>
                  <Tooltip title="向右旋转">
                    <Button size="small" icon={<RotateRightOutlined />} onClick={handleRotateRight} />
                  </Tooltip>
                  <Tooltip title={isFullscreen ? "退出全屏" : "全屏"}>
                    <Button
                      size="small"
                      icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                      onClick={handleToggleFullscreen}
                    />
                  </Tooltip>
                </Space>
              </div>
            )}

            {/* 预览内容区域 */}
            <div
              style={{
                textAlign: 'center',
                height: isFullscreen ? "calc(100% - 60px)" : "auto",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                overflow: "hidden",
                position: "relative"
              }}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            >
            {previewType === 'pdf' ? (
              <iframe
                src={previewUrl}
                style={{
                  width: '100%',
                  height: isFullscreen ? '100%' : '600px',
                  border: 'none'
                }}
                title={previewTitle}
              />
            ) : (
              <img
                src={previewUrl}
                alt={previewTitle}
                style={{
                  maxWidth: isFullscreen ? 'none' : '100%',
                  maxHeight: isFullscreen ? 'none' : '600px',
                  transform: `translate(${imagePosition.x}px, ${imagePosition.y}px) scale(${imageScale}) rotate(${imageRotation}deg)`,
                  transition: isDragging ? 'none' : 'transform 0.3s ease',
                  cursor: isDragging ? 'grabbing' : 'grab',
                  userSelect: 'none'
                }}
                onMouseDown={handleMouseDown}
                onLoad={() => {
                  console.log('图片加载完成:', previewUrl);
                }}
                onError={(e) => {
                  console.error('图片加载失败:', previewUrl, e);
                  message.error('图片加载失败');
                }}
                draggable={false}
              />
            )}
            </div>
          </>
        )}
      </Modal>
    </>
  );
};

export default AttachmentPreview;
