import React from 'react';
import { Card, Button, Space, Tag, Descriptions, Alert } from 'antd';
import { 
  GlobalOutlined, 
  SettingOutlined, 
  ThunderboltOutlined,
  ExperimentOutlined,
  ApiOutlined,
  RightOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';

const WebsiteNavigation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const navigationItems = [
    {
      key: 'basic',
      title: '基础网站管理',
      description: '基本的网站管理功能，包含新建、编辑、查看网站信息',
      path: '/websites',
      icon: <GlobalOutlined className="text-blue-500" />,
      features: [
        '网站列表查看',
        '新建网站',
        '编辑网站信息',
        '基础搜索过滤',
        '批量操作'
      ],
      level: '基础',
      color: 'blue'
    },
    {
      key: 'enhanced',
      title: '增强网站管理',
      description: '增强版网站管理，提供更丰富的功能和更好的用户体验',
      path: '/websites/enhanced',
      icon: <ThunderboltOutlined className="text-green-500" />,
      features: [
        '美观的界面设计',
        '增强的数据展示',
        '访问状态检查',
        '性能指标显示',
        '快速操作按钮'
      ],
      level: '增强',
      color: 'green'
    },
    {
      key: 'advanced',
      title: '高级网站管理',
      description: '完整的企业级网站管理解决方案，包含SSL监控、安全扫描、性能分析等',
      path: '/websites/advanced',
      icon: <SettingOutlined className="text-purple-500" />,
      features: [
        'SSL证书监控',
        '安全扫描分析',
        '性能测试评估',
        '备份管理',
        '凭据管理',
        '监控设置',
        '详细报告'
      ],
      level: '高级',
      color: 'purple'
    },
    {
      key: 'test',
      title: '功能测试页面',
      description: '专门用于测试网站管理功能的页面',
      path: '/websites/test-form',
      icon: <ExperimentOutlined className="text-orange-500" />,
      features: [
        '表单功能测试',
        '新建网站测试',
        '字段验证测试',
        'API交互测试'
      ],
      level: '测试',
      color: 'orange'
    },
    {
      key: 'api-test',
      title: 'API测试工具',
      description: '直接测试后端API接口的工具页面',
      path: '/test-api',
      icon: <ApiOutlined className="text-red-500" />,
      features: [
        'API接口测试',
        '数据格式验证',
        '错误处理测试',
        '响应时间监控'
      ],
      level: '开发',
      color: 'red'
    }
  ];

  const currentPath = location.pathname;

  const handleNavigate = (path: string) => {
    navigate(path);
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">网站管理功能导航</h1>
        <p className="text-gray-600">
          选择适合您需求的网站管理功能。从基础管理到高级企业级功能，满足不同场景的需求。
        </p>
      </div>

      <Alert
        message="功能说明"
        description="基础管理适合日常使用，增强管理提供更好的体验，高级管理包含完整的企业级功能。测试页面用于验证功能正常性。"
        type="info"
        showIcon
        className="mb-6"
      />

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {navigationItems.map((item) => (
          <Card
            key={item.key}
            className={`hover:shadow-lg transition-shadow cursor-pointer ${
              currentPath === item.path ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => handleNavigate(item.path)}
          >
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center">
                {item.icon}
                <div className="ml-3">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {item.title}
                  </h3>
                  <Tag color={item.color} size="small">
                    {item.level}
                  </Tag>
                </div>
              </div>
              <RightOutlined className="text-gray-400" />
            </div>

            <p className="text-gray-600 mb-4 text-sm">
              {item.description}
            </p>

            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">主要功能：</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                {item.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <span className="w-1 h-1 bg-gray-400 rounded-full mr-2"></span>
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            <Button
              type={currentPath === item.path ? 'primary' : 'default'}
              block
              onClick={(e) => {
                e.stopPropagation();
                handleNavigate(item.path);
              }}
            >
              {currentPath === item.path ? '当前页面' : '进入管理'}
            </Button>
          </Card>
        ))}
      </div>

      <div className="mt-8">
        <Card title="功能对比" className="mb-6">
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 px-4">功能</th>
                  <th className="text-center py-2 px-4">基础管理</th>
                  <th className="text-center py-2 px-4">增强管理</th>
                  <th className="text-center py-2 px-4">高级管理</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="py-2 px-4">网站列表</td>
                  <td className="text-center py-2 px-4">✅</td>
                  <td className="text-center py-2 px-4">✅</td>
                  <td className="text-center py-2 px-4">✅</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4">新建/编辑网站</td>
                  <td className="text-center py-2 px-4">✅</td>
                  <td className="text-center py-2 px-4">✅</td>
                  <td className="text-center py-2 px-4">✅</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4">访问状态检查</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">✅</td>
                  <td className="text-center py-2 px-4">✅</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4">SSL证书监控</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">✅</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4">安全扫描</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">✅</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4">性能分析</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">✅</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4">备份管理</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">✅</td>
                </tr>
                <tr className="border-b">
                  <td className="py-2 px-4">凭据管理</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">✅</td>
                </tr>
                <tr>
                  <td className="py-2 px-4">详细报告</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">❌</td>
                  <td className="text-center py-2 px-4">✅</td>
                </tr>
              </tbody>
            </table>
          </div>
        </Card>

        <Card title="使用建议">
          <Descriptions column={1} bordered>
            <Descriptions.Item label="基础管理">
              适合个人用户或小型团队，满足基本的网站管理需求
            </Descriptions.Item>
            <Descriptions.Item label="增强管理">
              适合中小型企业，需要更好的用户体验和基础监控功能
            </Descriptions.Item>
            <Descriptions.Item label="高级管理">
              适合大型企业或专业运维团队，需要完整的监控、安全和管理功能
            </Descriptions.Item>
            <Descriptions.Item label="测试页面">
              适合开发人员测试功能或验证系统正常性
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </div>
    </div>
  );
};

export default WebsiteNavigation;
