import React, { useState } from 'react';
import {
  Modal,
  Form,
  Select,
  DatePicker,
  Button,
  Space,
  Alert,
  Typography,
  Divider,
  message,
  Popconfirm
} from 'antd';
import {
  DeleteOutlined,
  EditOutlined,
  DatabaseOutlined,
  CalendarOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { WebsiteApi } from '../../services/website';
import { Website } from '../../types';

const { Text } = Typography;
const { Option } = Select;

interface BatchOperationsProps {
  selectedWebsites: Website[];
  onSuccess: () => void;
  onClearSelection: () => void;
}

const BatchOperations: React.FC<BatchOperationsProps> = ({
  selectedWebsites,
  onSuccess,
  onClearSelection
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [operationType, setOperationType] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  // 操作类型配置
  const operationTypes = [
    {
      key: 'updateStatus',
      label: '批量更新状态',
      icon: <EditOutlined />,
      color: '#1890ff',
      description: '批量修改选中网站的状态'
    },
    {
      key: 'updateServer',
      label: '批量更换服务器',
      icon: <DatabaseOutlined />,
      color: '#52c41a',
      description: '批量修改选中网站的服务器'
    },
    {
      key: 'updateExpireDate',
      label: '批量更新到期时间',
      icon: <CalendarOutlined />,
      color: '#faad14',
      description: '批量修改选中网站的到期时间'
    },
    {
      key: 'delete',
      label: '批量删除',
      icon: <DeleteOutlined />,
      color: '#ff4d4f',
      description: '永久删除选中的网站（不可恢复）'
    }
  ];

  // 状态选项
  const statusOptions = [
    { value: 'active', label: '正常', color: '#52c41a' },
    { value: 'inactive', label: '停用', color: '#faad14' },
    { value: 'suspended', label: '暂停', color: '#ff4d4f' },
    { value: 'maintenance', label: '维护', color: '#1890ff' }
  ];

  // 服务器选项（模拟数据）
  const serverOptions = [
    { value: 1, label: 'Server-1 (*************)' },
    { value: 2, label: 'Server-2 (*************)' },
    { value: 3, label: 'Server-3 (*************)' }
  ];

  // 打开操作模态框
  const handleOpenModal = (type: string) => {
    setOperationType(type);
    setModalVisible(true);
    form.resetFields();
  };

  // 执行批量操作
  const handleBatchOperation = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const websiteIds = selectedWebsites.map(w => w.id);
      
      await WebsiteApi.batchUpdate({
        action: operationType,
        websiteIds,
        data: values
      });

      message.success(`批量操作成功，影响 ${websiteIds.length} 个网站`);
      setModalVisible(false);
      onSuccess();
      onClearSelection();

    } catch (error) {
      console.error('批量操作失败:', error);
      message.error('批量操作失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 渲染表单内容
  const renderFormContent = () => {
    switch (operationType) {
      case 'updateStatus':
        return (
          <Form.Item
            name="status"
            label="新状态"
            rules={[{ required: true, message: '请选择新状态' }]}
          >
            <Select placeholder="请选择状态">
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  <Space>
                    <span style={{ 
                      display: 'inline-block',
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      backgroundColor: option.color
                    }} />
                    {option.label}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'updateServer':
        return (
          <Form.Item
            name="serverId"
            label="目标服务器"
            rules={[{ required: true, message: '请选择目标服务器' }]}
          >
            <Select placeholder="请选择服务器">
              {serverOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      case 'updateExpireDate':
        return (
          <Form.Item
            name="expireDate"
            label="新到期时间"
            rules={[{ required: true, message: '请选择新到期时间' }]}
          >
            <DatePicker 
              style={{ width: '100%' }}
              placeholder="请选择到期时间"
              disabledDate={(current) => current && current.valueOf() < Date.now()}
            />
          </Form.Item>
        );

      case 'delete':
        return (
          <Alert
            message="危险操作"
            description="删除操作不可恢复，请确认要删除选中的网站。"
            type="error"
            showIcon
          />
        );

      default:
        return null;
    }
  };

  // 获取当前操作配置
  const currentOperation = operationTypes.find(op => op.key === operationType);

  if (selectedWebsites.length === 0) {
    return null;
  }

  return (
    <>
      {/* 批量操作工具栏 */}
      <div style={{
        position: 'fixed',
        bottom: 24,
        left: '50%',
        transform: 'translateX(-50%)',
        background: 'white',
        padding: '16px 24px',
        borderRadius: 8,
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        border: '1px solid #d9d9d9',
        zIndex: 1000
      }}>
        <Space size="large">
          <div>
            <Text strong>已选择 {selectedWebsites.length} 个网站</Text>
            <div style={{ fontSize: 12, color: '#8c8c8c' }}>
              {selectedWebsites.slice(0, 3).map(w => w.domain).join(', ')}
              {selectedWebsites.length > 3 && ` 等${selectedWebsites.length}个`}
            </div>
          </div>

          <Divider type="vertical" style={{ height: 40 }} />

          <Space>
            {operationTypes.map(operation => (
              <Button
                key={operation.key}
                icon={operation.icon}
                style={{ 
                  color: operation.color,
                  borderColor: operation.color
                }}
                onClick={() => handleOpenModal(operation.key)}
              >
                {operation.label}
              </Button>
            ))}
            
            <Button onClick={onClearSelection}>
              取消选择
            </Button>
          </Space>
        </Space>
      </div>

      {/* 批量操作模态框 */}
      <Modal
        title={
          <Space>
            {currentOperation?.icon}
            {currentOperation?.label}
          </Space>
        }
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
          operationType === 'delete' ? (
            <Popconfirm
              key="confirm"
              title="确认删除"
              description={`确定要删除选中的 ${selectedWebsites.length} 个网站吗？此操作不可恢复。`}
              onConfirm={handleBatchOperation}
              okText="确认删除"
              cancelText="取消"
              okType="danger"
            >
              <Button type="primary" danger loading={loading}>
                确认删除
              </Button>
            </Popconfirm>
          ) : (
            <Button
              key="submit"
              type="primary"
              loading={loading}
              onClick={handleBatchOperation}
            >
              确认操作
            </Button>
          )
        ]}
      >
        <Alert
          message={currentOperation?.description}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <div style={{ marginBottom: 16 }}>
          <Text strong>影响的网站：</Text>
          <div style={{ 
            maxHeight: 120, 
            overflowY: 'auto',
            background: '#fafafa',
            padding: 8,
            borderRadius: 4,
            marginTop: 8
          }}>
            {selectedWebsites.map(website => (
              <div key={website.id} style={{ padding: '4px 0' }}>
                <Text>{website.domain}</Text>
                <Text type="secondary" style={{ marginLeft: 8, fontSize: 12 }}>
                  ({website.platform?.name})
                </Text>
              </div>
            ))}
          </div>
        </div>

        <Form form={form} layout="vertical">
          {renderFormContent()}
        </Form>
      </Modal>
    </>
  );
};

export default BatchOperations;
