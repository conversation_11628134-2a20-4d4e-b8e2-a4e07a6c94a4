import React, { useState } from 'react';
import {
  Modal,
  Upload,
  Button,
  Steps,
  Table,
  Alert,
  Progress,
  Space,
  Typography,
  Tag,
  App
} from 'antd';
import {
  UploadOutlined,
  FileExcelOutlined,
  CheckCircleOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import * as XLSX from 'xlsx';
import { WebsiteApi } from '../../services/website';

const { Title, Text } = Typography;
const { Step } = Steps;
const { Dragger } = Upload;

interface WebsiteImportProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

interface ImportData {
  id?: number;
  siteId?: number;
  siteName: string;
  siteUrl: string;
  platformId: number;
  serverName?: string;
  serverIp?: string;
  onlineDate?: string;
  expireDate?: string;
  projectAmount?: number;
  renewalFee?: number;
  hasOnboard?: boolean;
  notes?: string;
  status?: string;
}

interface ValidationResult {
  valid: boolean;
  errors: string[];
  data: ImportData;
}

const WebsiteImport: React.FC<WebsiteImportProps> = ({
  visible,
  onCancel,
  onSuccess
}) => {
  const { message: messageApi } = App.useApp();
  const [currentStep, setCurrentStep] = useState(0);
  const [fileList, setFileList] = useState<any[]>([]);

  const [validationResults, setValidationResults] = useState<ValidationResult[]>([]);
  const [importing, setImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importResults, setImportResults] = useState<{
    success: number;
    failed: number;
    errors: string[];
    warnings: string[];
  }>({ success: 0, failed: 0, errors: [], warnings: [] });
  const [platforms, setPlatforms] = useState<any[]>([]);
  const [servers, setServers] = useState<any[]>([]);

  // 获取平台和服务器数据
  const fetchOptions = async () => {
    try {
      const [platformsRes, serversRes] = await Promise.all([
        fetch('http://localhost:3001/api/v1/platforms'), // 使用新的平台API，包含platform_id字段
        fetch('http://localhost:3001/api/v1/websites/options/servers')
      ]);

      const platformsData = await platformsRes.json();
      const serversData = await serversRes.json();

      if (platformsData.success) {
        setPlatforms(platformsData.data);
      }
      if (serversData.success) {
        setServers(serversData.data);
      }
    } catch (error) {
      console.error('获取选项数据失败:', error);
    }
  };

  // Excel日期转换函数
  const convertExcelDate = (excelDate: any): string | undefined => {
    if (!excelDate) return undefined;

    // 如果已经是字符串格式的日期，直接返回
    if (typeof excelDate === 'string') {
      // 检查是否是有效的日期格式
      const date = new Date(excelDate);
      if (!isNaN(date.getTime())) {
        return excelDate;
      }
      return undefined;
    }

    // 如果是数字，转换Excel日期序列号
    if (typeof excelDate === 'number') {
      // Excel日期从1900年1月1日开始计算，但Excel错误地认为1900年是闰年
      // 所以需要减去1天来修正
      const excelEpoch = new Date(1900, 0, 1);
      const days = excelDate - 1; // 减去1天修正Excel的闰年错误
      const date = new Date(excelEpoch.getTime() + days * 24 * 60 * 60 * 1000);

      if (!isNaN(date.getTime())) {
        return date.toISOString().split('T')[0]; // 返回YYYY-MM-DD格式
      }
    }

    return undefined;
  };

  // 重置状态
  const resetState = () => {
    setCurrentStep(0);
    setFileList([]);
    setValidationResults([]);
    setImporting(false);
    setImportProgress(0);
    setImportResults({ success: 0, failed: 0, errors: [], warnings: [] });
  };

  // 处理文件上传
  const handleFileUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

        // 解析数据
        const headers = jsonData[0] as string[];
        const rows = jsonData.slice(1) as any[][];

        const parsedData: ImportData[] = rows
          .filter(row => row.some(cell => cell !== undefined && cell !== ''))
          .map((row) => {
            const rowData: any = {};
            headers.forEach((header, index) => {
              rowData[header] = row[index];
            });

            // 解析平台类型ID - 尝试多种可能的列名
            let platformIdRaw = null;
            const possibleHeaders = [
              '平台类型ID',
              '平台ID',
              headers.find(h => h && h.includes && h.includes('平台') && h.includes('ID'))
            ].filter(Boolean);

            for (const header of possibleHeaders) {
              if (header && rowData[header] !== undefined && rowData[header] !== '') {
                platformIdRaw = rowData[header];
                break;
              }
            }

            const platformId = parseInt(platformIdRaw);

            return {
              id: parseInt(rowData['ID'] || rowData['网站ID']) || undefined,
              siteId: parseInt(rowData['站点ID'] || rowData['站点唯一ID']) || undefined,
              siteName: rowData['站点名称'] || rowData['网站名称'] || '',
              siteUrl: rowData['站点URL'] || rowData['网站地址'] || '',
              platformId: isNaN(platformId) ? 0 : platformId,
              serverName: rowData['服务器'] || rowData['服务器名称'] || '',
              serverIp: rowData['服务器IP'] || rowData['IP地址'] || rowData['IP'] || '',
              onlineDate: convertExcelDate(rowData['上线时间'] || rowData['上线日期']),
              expireDate: convertExcelDate(rowData['到期时间'] || rowData['到期日期']),
              projectAmount: parseFloat(rowData['项目金额']) || undefined,
              renewalFee: parseFloat(rowData['续费金额']) || undefined,
              hasOnboard: rowData['Onboard'] === '已安装' || rowData['Onboard'] === true || rowData['Onboard'] === 'true' || false,
              notes: rowData['备注'] || rowData['说明'] || '',
              status: rowData['状态'] || 'active'
            };
          });

        validateData(parsedData);
        setCurrentStep(1);
      } catch (error) {
        messageApi.error('文件解析失败，请检查文件格式');
        console.error('文件解析错误:', error);
      }
    };
    reader.readAsArrayBuffer(file);
    return false; // 阻止自动上传
  };

  // 验证数据
  const validateData = (data: ImportData[]) => {
    const results: ValidationResult[] = data.map((item) => {
      const errors: string[] = [];

      if (!item.siteName) {
        errors.push('站点名称不能为空');
      }
      if (!item.siteUrl) {
        errors.push('站点URL不能为空');
      } else if (!/^https?:\/\/.+/.test(item.siteUrl)) {
        errors.push('站点URL格式不正确');
      }
      if (!item.platformId || item.platformId === 0) {
        errors.push('平台类型ID不能为空');
      }

      // 验证平台ID是否存在
      if (item.platformId && item.platformId !== 0 && platforms.length > 0) {
        const platform = platforms.find(p => p.platform_id === item.platformId);
        if (!platform) {
          // 提供更详细的错误信息，包括可用的平台ID列表
          const availablePlatforms = platforms.map(p => `${p.platform_id}(${p.name})`).join('、');
          console.error(`平台ID匹配失败: "${item.platformId}" 不在可用平台中:`, platforms.map(p => `${p.platform_id}(${p.name})`));
          errors.push(`平台类型ID"${item.platformId}"不存在。可用的平台ID：${availablePlatforms}`);
        }
      }

      // 验证服务器是否存在（如果提供了服务器名称）
      // 注意：如果服务器不存在，不报错，而是在导入时忽略并留空服务器值

      return {
        valid: errors.length === 0,
        errors,
        data: item
      };
    });

    setValidationResults(results);
  };

  // 执行导入
  const handleImport = async () => {
    const validData = validationResults.filter(result => result.valid);
    if (validData.length === 0) {
      messageApi.error('没有有效的数据可以导入');
      return;
    }

    setImporting(true);
    setCurrentStep(2);
    
    let successCount = 0;
    let failedCount = 0;
    const errors: string[] = [];
    const warnings: string[] = [];

    for (let i = 0; i < validData.length; i++) {
      try {
        const item = validData[i].data;

        // 验证平台ID
        const platform = platforms.find(p => p.platform_id === item.platformId);
        if (!platform) {
          const availablePlatforms = platforms.map(p => `${p.platform_id}(${p.name})`).join('、');
          console.error(`导入失败 - 平台ID匹配失败: "${item.platformId}" 不在可用平台中:`, platforms.map(p => `${p.platform_id}(${p.name})`));
          throw new Error(`平台类型ID"${item.platformId}"不存在。可用的平台ID：${availablePlatforms}`);
        }
        const platformId = item.platformId;

        // 查找服务器ID
        let serverId = null;
        let serverWarning = '';
        if (item.serverName) {
          const server = servers.find(s => s.name === item.serverName);
          if (server) {
            serverId = server.id;
          } else {
            serverWarning = `第${i + 1}行：服务器"${item.serverName}"不存在，已忽略并留空服务器字段`;
          }
        }

        // 转换数据格式
        const domain = item.siteUrl.replace(/^https?:\/\/(www\.)?/, '').replace(/\/.*$/, '');
        const websiteData = {
          siteName: item.siteName,
          domain: domain,
          siteUrl: item.siteUrl,
          platformId: platformId,
          serverId: serverId,
          onlineDate: item.onlineDate || undefined,
          expireDate: item.expireDate || undefined,
          projectAmount: item.projectAmount || undefined,
          renewalFee: item.renewalFee || undefined,
          hasOnboard: item.hasOnboard || false,
          notes: item.notes || undefined,
          status: (item.status as 'active' | 'inactive' | 'suspended' | 'expired') || 'active',
          // 如果提供了站点ID，则包含在数据中，否则让后端自动分配
          ...(item.siteId ? { siteId: item.siteId } : {})
        };



        // 根据是否有ID决定创建还是更新
        if (item.id) {
          await WebsiteApi.updateWebsite(item.id, websiteData);
        } else {
          await WebsiteApi.createWebsite(websiteData);
        }
        successCount++;

        // 如果有服务器警告，添加到警告列表中
        if (serverWarning) {
          warnings.push(serverWarning);
        }
      } catch (error) {
        failedCount++;
        console.error(`第${i + 1}行导入失败:`, error);

        // 提取更详细的错误信息
        let errorMessage = `第${i + 1}行导入失败: `;
        if (error instanceof Error) {
          errorMessage += error.message;
        } else if (typeof error === 'object' && error !== null) {
          errorMessage += JSON.stringify(error);
        } else {
          errorMessage += String(error);
        }

        errors.push(errorMessage);
      }

      setImportProgress(Math.round(((i + 1) / validData.length) * 100));
    }

    setImportResults({
      success: successCount,
      failed: failedCount,
      errors,
      warnings
    });
    
    setImporting(false);
    
    if (successCount > 0) {
      messageApi.success(`成功导入 ${successCount} 条记录`);
      onSuccess();
    }
  };

  // 下载模板
  const downloadTemplate = () => {
    // 创建平台ID说明
    const platformInfo = platforms.length > 0
      ? platforms.map(p => `${p.platform_id}=${p.name}`).join(', ')
      : '1=WordPress, 2=未知平台, 3=Shopify, 8=易营宝, 9=多谷, 10=巨旺, 11=弗米乐, 12=领动, 13=test';

    const templateData = [
      ['ID', '站点ID', '站点名称', '站点URL', `平台类型ID(${platformInfo})`, '服务器', '上线时间', '到期时间', '项目金额', '续费金额', 'Onboard', '备注', '状态'],
      ['', '1001', '企业官网', 'https://example.com', '1', 'Server-1', '2024-01-01', '2024-12-31', 15000, 3600, '已安装', '企业主站', 'active'],
      ['', '1002', '营销网站', 'https://marketing.example.com', '2', 'Server-2', '2024-02-01', '2024-12-31', 28000, 4800, '未安装', '营销推广网站', 'active'],
      ['', '1003', '电商平台', 'https://shop.example.com', '3', 'Server-3', '2024-03-01', '2024-12-31', 35000, 5200, '已安装', '电商网站', 'active']
    ];

    const ws = XLSX.utils.aoa_to_sheet(templateData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '网站导入模板');
    XLSX.writeFile(wb, '网站导入模板.xlsx');
  };

  const uploadProps = {
    name: 'file',
    multiple: false,
    accept: '.xlsx,.xls',
    fileList,
    beforeUpload: handleFileUpload,
    onRemove: () => {
      setFileList([]);
      setValidationResults([]);
      setCurrentStep(0);
    },
    onChange: (info: any) => {
      setFileList(info.fileList.slice(-1));
    }
  };

  const validCount = validationResults.filter(r => r.valid).length;
  const invalidCount = validationResults.filter(r => !r.valid).length;

  // 组件挂载时获取选项数据
  React.useEffect(() => {
    if (visible) {
      fetchOptions();
    }
  }, [visible]);

  return (
    <Modal
      title="批量导入网站"
      open={visible}
      onCancel={() => {
        resetState();
        onCancel();
      }}
      footer={null}
      width={800}
      style={{ top: 20 }}
    >
      <Steps current={currentStep} style={{ marginBottom: 24 }}>
        <Step title="上传文件" icon={<UploadOutlined />} />
        <Step title="数据验证" icon={<CheckCircleOutlined />} />
        <Step title="执行导入" icon={<FileExcelOutlined />} />
      </Steps>

      {currentStep === 0 && (
        <div>
          <Alert
            message="导入说明"
            description={
              <div>
                <p>1. 请使用Excel文件(.xlsx或.xls格式)</p>
                <p>2. 第一行必须是列标题</p>
                <p>3. 必填字段：站点名称、站点URL、平台类型ID</p>
                <p>4. ID字段：留空表示新建，填写ID表示更新现有网站</p>
                <p>5. 站点名称可以重复，系统通过ID进行唯一识别</p>
                <p>6. 支持的列标题：ID、站点ID、站点名称、站点URL、平台类型ID、服务器、上线时间、到期时间、项目金额、续费金额、Onboard、备注、状态</p>
                <p>7. 站点ID字段：自定义的站点唯一标识，留空时系统自动分配</p>
                <p>8. 平台类型ID字段：填写数字ID，对应关系请查看模板表头或下载模板查看</p>
                <p>9. 服务器字段：填写服务器名称，如果服务器不存在则自动忽略并留空</p>
                <p>10. 日期字段：支持Excel日期格式和文本日期格式（如2024-01-01），系统会自动转换</p>
                <p>11. Onboard字段：填写"已安装"或"未安装"，留空默认为"未安装"</p>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <div style={{ textAlign: 'center', marginBottom: 16 }}>
            <Button
              icon={<DownloadOutlined />}
              onClick={downloadTemplate}
              type="dashed"
            >
              下载导入模板
            </Button>
          </div>

          <Dragger {...uploadProps}>
            <p className="ant-upload-drag-icon">
              <FileExcelOutlined style={{ fontSize: 48, color: '#1890ff' }} />
            </p>
            <p className="ant-upload-text">点击或拖拽Excel文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持.xlsx和.xls格式的Excel文件
            </p>
          </Dragger>
        </div>
      )}

      {currentStep === 1 && (
        <div>
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Tag color="green">有效数据: {validCount}</Tag>
              <Tag color="red">无效数据: {invalidCount}</Tag>
            </Space>
          </div>

          <Table
            dataSource={validationResults.map((result, index) => {
              const platform = platforms.find(p => p.platform_id === result.data.platformId);
              return {
                key: index,
                index: index + 1,
                id: result.data.id,
                siteId: result.data.siteId,
                siteName: result.data.siteName,
                siteUrl: result.data.siteUrl,
                platformId: result.data.platformId,
                platformName: platform ? `${platform.platform_id}(${platform.name})` : `${result.data.platformId}(未知)`,
                serverName: result.data.serverName,
                valid: result.valid,
                errors: result.errors
              };
            })}
            columns={[
              { title: '序号', dataIndex: 'index', width: 50 },
              { title: 'ID', dataIndex: 'id', width: 60, render: (id: number) => id || '新建' },
              { title: '站点ID', dataIndex: 'siteId', width: 70, render: (siteId: number) => siteId || '自动分配' },
              { title: '站点名称', dataIndex: 'siteName', width: 100 },
              { title: '站点URL', dataIndex: 'siteUrl', width: 150 },
              { title: '平台类型', dataIndex: 'platformName', width: 120 },
              { title: '服务器', dataIndex: 'serverName', width: 80 },
              {
                title: '状态',
                dataIndex: 'valid',
                width: 80,
                render: (valid: boolean) => (
                  <Tag color={valid ? 'green' : 'red'}>
                    {valid ? '有效' : '无效'}
                  </Tag>
                )
              },
              {
                title: '错误信息',
                dataIndex: 'errors',
                render: (errors: string[]) => (
                  <div>
                    {errors.map((error, index) => (
                      <div key={index} style={{ color: '#ff4d4f', fontSize: 12 }}>
                        {error}
                      </div>
                    ))}
                  </div>
                )
              }
            ]}
            pagination={false}
            scroll={{ y: 300 }}
            size="small"
          />

          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Space>
              <Button onClick={() => setCurrentStep(0)}>
                重新上传
              </Button>
              <Button
                type="primary"
                onClick={handleImport}
                disabled={validCount === 0}
              >
                开始导入 ({validCount} 条)
              </Button>
            </Space>
          </div>
        </div>
      )}

      {currentStep === 2 && (
        <div>
          {importing ? (
            <div style={{ textAlign: 'center' }}>
              <Title level={4}>正在导入数据...</Title>
              <Progress percent={importProgress} />
              <Text type="secondary">请稍候，正在处理数据</Text>
            </div>
          ) : (
            <div>
              <Alert
                message="导入完成"
                description={
                  <div>
                    <p>成功导入: {importResults.success} 条</p>
                    <p>导入失败: {importResults.failed} 条</p>
                    {importResults.warnings.length > 0 && (
                      <div style={{ marginTop: 8 }}>
                        <Text strong>警告信息:</Text>
                        {importResults.warnings.map((warning, index) => (
                          <div key={index} style={{ color: '#faad14', fontSize: 12 }}>
                            {warning}
                          </div>
                        ))}
                      </div>
                    )}
                    {importResults.errors.length > 0 && (
                      <div style={{ marginTop: 8 }}>
                        <Text strong>错误详情:</Text>
                        {importResults.errors.map((error, index) => (
                          <div key={index} style={{ color: '#ff4d4f', fontSize: 12 }}>
                            {error}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                }
                type={importResults.failed === 0 ? 'success' : 'warning'}
                showIcon
              />

              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Button
                  type="primary"
                  onClick={() => {
                    resetState();
                    onCancel();
                  }}
                >
                  完成
                </Button>
              </div>
            </div>
          )}
        </div>
      )}
    </Modal>
  );
};

export default WebsiteImport;
