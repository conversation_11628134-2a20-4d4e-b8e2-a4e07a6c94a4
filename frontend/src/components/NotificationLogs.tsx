import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Tag,
  Button,
  Space,
  DatePicker,
  Select,
  Input,
  Row,
  Col,
  Statistic,
  Progress,
  Typography,
  Modal,
  message,
  Tooltip
} from 'antd';
import {
  ReloadOutlined,
  SearchOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  BellOutlined
} from '@ant-design/icons';
import { motion } from 'framer-motion';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Title, Paragraph } = Typography;

interface NotificationLog {
  id: number;
  website_id: number;
  site_name: string;
  url: string;
  notification_type: 'feishu' | 'email' | 'sms' | 'webhook';
  trigger_reason: 'consecutive_failures' | 'ssl_expiry' | 'domain_expiry' | 'manual';
  message: string;
  status: 'pending' | 'sent' | 'failed';
  response_data?: any;
  error_message?: string;
  sent_at?: string;
  created_at: string;
}

interface NotificationStats {
  total: number;
  sent: number;
  failed: number;
  pending: number;
  successRate: number;
  typeStats: Array<{
    notification_type: string;
    total: number;
    sent: number;
    failed: number;
  }>;
  dailyStats: Array<{
    date: string;
    total: number;
    sent: number;
    failed: number;
  }>;
}

const NotificationLogs: React.FC = () => {
  const [logs, setLogs] = useState<NotificationLog[]>([]);
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [filters, setFilters] = useState({
    type: '',
    status: '',
    website_id: '',
    dateRange: null as any
  });
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedLog, setSelectedLog] = useState<NotificationLog | null>(null);

  // 加载通知日志
  const loadLogs = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pageSize.toString(),
        ...(filters.type && { type: filters.type }),
        ...(filters.status && { status: filters.status }),
        ...(filters.website_id && { website_id: filters.website_id }),
        ...(filters.dateRange && {
          start_date: filters.dateRange[0].format('YYYY-MM-DD'),
          end_date: filters.dateRange[1].format('YYYY-MM-DD')
        })
      });

      const response = await fetch(`http://localhost:3001/api/v1/notifications/logs?${params}`);
      const result = await response.json();
      
      if (result.success) {
        setLogs(result.data.logs);
        setPagination({
          current: result.data.pagination.page,
          pageSize: result.data.pagination.limit,
          total: result.data.pagination.total
        });
      }
    } catch (error) {
      message.error('加载通知日志失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/v1/notifications/stats');
      const result = await response.json();
      
      if (result.success) {
        setStats(result.data);
      }
    } catch (error) {
      message.error('加载统计数据失败');
    }
  };

  // 查看详情
  const viewDetail = (log: NotificationLog) => {
    setSelectedLog(log);
    setDetailModalVisible(true);
  };

  // 重新发送通知
  const resendNotification = async (log: NotificationLog) => {
    try {
      const response = await fetch(`/api/v1/websites/${log.website_id}/send-notification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: log.notification_type,
          reason: 'manual'
        }),
      });

      const result = await response.json();
      if (result.success) {
        message.success('通知重新发送成功');
        loadLogs(pagination.current, pagination.pageSize);
      } else {
        message.error(result.message || '重新发送失败');
      }
    } catch (error) {
      message.error('重新发送通知失败');
    }
  };

  useEffect(() => {
    loadLogs();
    loadStats();
  }, []);

  useEffect(() => {
    loadLogs(1, pagination.pageSize);
  }, [filters]);

  // 状态标签配置
  const statusConfig = {
    pending: { color: 'orange', icon: <ClockCircleOutlined />, text: '待发送' },
    sent: { color: 'green', icon: <CheckCircleOutlined />, text: '已发送' },
    failed: { color: 'red', icon: <CloseCircleOutlined />, text: '发送失败' }
  };

  // 通知类型配置
  const typeConfig = {
    feishu: { color: 'blue', text: '飞书' },
    email: { color: 'green', text: '邮件' },
    sms: { color: 'orange', text: '短信' },
    webhook: { color: 'purple', text: 'Webhook' }
  };

  // 触发原因配置
  const reasonConfig = {
    consecutive_failures: '连续失败',
    ssl_expiry: 'SSL到期',
    domain_expiry: '域名到期',
    manual: '手动发送'
  };

  // 表格列定义
  const columns = [
    {
      title: '网站',
      dataIndex: 'site_name',
      key: 'site_name',
      width: 150,
      render: (name: string, record: NotificationLog) => (
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-xs text-gray-500">{record.url}</div>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'notification_type',
      key: 'notification_type',
      width: 80,
      render: (type: string) => {
        const config = typeConfig[type as keyof typeof typeConfig];
        return <Tag color={config?.color}>{config?.text || type}</Tag>;
      }
    },
    {
      title: '触发原因',
      dataIndex: 'trigger_reason',
      key: 'trigger_reason',
      width: 100,
      render: (reason: string) => reasonConfig[reason as keyof typeof reasonConfig] || reason
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const config = statusConfig[status as keyof typeof statusConfig];
        return (
          <Tag color={config?.color} icon={config?.icon}>
            {config?.text || status}
          </Tag>
        );
      }
    },
    {
      title: '发送时间',
      dataIndex: 'sent_at',
      key: 'sent_at',
      width: 150,
      render: (time: string) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record: NotificationLog) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => viewDetail(record)}
            />
          </Tooltip>
          {record.status === 'failed' && (
            <Tooltip title="重新发送">
              <Button
                type="link"
                size="small"
                icon={<BellOutlined />}
                onClick={() => resendNotification(record)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      {stats && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Row gutter={16}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总通知数"
                  value={stats.total}
                  prefix={<BellOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="成功发送"
                  value={stats.sent}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="发送失败"
                  value={stats.failed}
                  prefix={<CloseCircleOutlined />}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <div>
                  <div className="text-sm text-gray-500 mb-2">成功率</div>
                  <Progress
                    percent={stats.successRate}
                    size="small"
                    status={stats.successRate >= 90 ? 'success' : stats.successRate >= 70 ? 'normal' : 'exception'}
                  />
                </div>
              </Card>
            </Col>
          </Row>
        </motion.div>
      )}

      {/* 筛选器 */}
      <Card>
        <Row gutter={16} align="middle">
          <Col span={4}>
            <Select
              placeholder="通知类型"
              allowClear
              style={{ width: '100%' }}
              value={filters.type}
              onChange={(value) => setFilters({ ...filters, type: value || '' })}
            >
              <Option value="feishu">飞书</Option>
              <Option value="email">邮件</Option>
              <Option value="sms">短信</Option>
              <Option value="webhook">Webhook</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Select
              placeholder="发送状态"
              allowClear
              style={{ width: '100%' }}
              value={filters.status}
              onChange={(value) => setFilters({ ...filters, status: value || '' })}
            >
              <Option value="pending">待发送</Option>
              <Option value="sent">已发送</Option>
              <Option value="failed">发送失败</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Input
              placeholder="网站ID"
              allowClear
              value={filters.website_id}
              onChange={(e) => setFilters({ ...filters, website_id: e.target.value })}
            />
          </Col>
          <Col span={6}>
            <RangePicker
              style={{ width: '100%' }}
              value={filters.dateRange}
              onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
            />
          </Col>
          <Col span={6}>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={() => loadLogs(1, pagination.pageSize)}
              >
                搜索
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  setFilters({ type: '', status: '', website_id: '', dateRange: null });
                  loadLogs(1, pagination.pageSize);
                  loadStats();
                }}
              >
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 通知日志表格 */}
      <Card>
        <div className="mb-4">
          <Title level={4}>通知历史记录</Title>
          <Paragraph type="secondary">
            查看系统发送的所有通知记录，包括飞书、邮件、短信等通知
          </Paragraph>
        </div>

        <Table
          columns={columns}
          dataSource={logs}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              loadLogs(page, pageSize);
            }
          }}
          scroll={{ x: 1000 }}
        />
      </Card>

      {/* 详情模态框 */}
      <Modal
        title="通知详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedLog && (
          <div className="space-y-4">
            <div>
              <strong>网站信息：</strong>
              <div className="ml-4">
                <div>名称：{selectedLog.site_name}</div>
                <div>URL：{selectedLog.url}</div>
              </div>
            </div>
            
            <div>
              <strong>通知信息：</strong>
              <div className="ml-4">
                <div>类型：{typeConfig[selectedLog.notification_type]?.text}</div>
                <div>触发原因：{reasonConfig[selectedLog.trigger_reason as keyof typeof reasonConfig]}</div>
                <div>状态：{statusConfig[selectedLog.status]?.text}</div>
              </div>
            </div>

            <div>
              <strong>消息内容：</strong>
              <div className="ml-4 p-3 bg-gray-50 rounded border">
                <pre className="whitespace-pre-wrap">{selectedLog.message}</pre>
              </div>
            </div>

            {selectedLog.error_message && (
              <div>
                <strong>错误信息：</strong>
                <div className="ml-4 p-3 bg-red-50 rounded border text-red-600">
                  {selectedLog.error_message}
                </div>
              </div>
            )}

            {selectedLog.response_data && (
              <div>
                <strong>响应数据：</strong>
                <div className="ml-4 p-3 bg-gray-50 rounded border">
                  <pre className="whitespace-pre-wrap">
                    {JSON.stringify(selectedLog.response_data, null, 2)}
                  </pre>
                </div>
              </div>
            )}

            <div>
              <strong>时间信息：</strong>
              <div className="ml-4">
                <div>创建时间：{new Date(selectedLog.created_at).toLocaleString()}</div>
                {selectedLog.sent_at && (
                  <div>发送时间：{new Date(selectedLog.sent_at).toLocaleString()}</div>
                )}
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default NotificationLogs;
