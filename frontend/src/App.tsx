import React from 'react';
import { RouterProvider } from 'react-router-dom';
import { ConfigProvider, App as AntdApp, theme } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import router from './router';
import { useAppStore } from './store/app';
import MessageProvider from './components/Common/MessageProvider';
import { PermissionProvider } from './contexts/PermissionContext';

// 设置dayjs中文
dayjs.locale('zh-cn');

const App: React.FC = () => {
  const { theme: appTheme, primaryColor } = useAppStore();

  return (
    <ConfigProvider
      locale={zhCN}
      theme={{
        algorithm: appTheme === 'dark' ? theme.darkAlgorithm : theme.defaultAlgorithm,
        token: {
          colorPrimary: primaryColor,
        },
        components: {
          Layout: {
            siderBg: appTheme === 'dark' ? '#001529' : '#ffffff',
            headerBg: appTheme === 'dark' ? '#001529' : '#ffffff',
          },
        },
      }}
    >
      <AntdApp>
        <MessageProvider>
          <PermissionProvider>
            <RouterProvider router={router} />
          </PermissionProvider>
        </MessageProvider>
      </AntdApp>
    </ConfigProvider>
  );
};

export default App;
