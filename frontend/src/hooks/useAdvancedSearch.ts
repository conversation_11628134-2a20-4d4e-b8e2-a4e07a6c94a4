import { useState, useMemo, useCallback } from 'react';

export interface SearchOptions {
  multiKeyword?: boolean;      // 是否支持多关键词搜索
  keywordLogic?: 'AND' | 'OR'; // 多关键词逻辑
  caseSensitive?: boolean;     // 是否区分大小写
  debounceMs?: number;         // 防抖延迟
  highlightMatch?: boolean;    // 是否高亮匹配
}

export interface SearchField {
  key: string;                 // 字段路径，支持嵌套如 'user.profile.name'
  weight?: number;             // 搜索权重（暂未实现）
  exact?: boolean;             // 是否精确匹配
}

export interface SearchResult<T> {
  data: T[];
  total: number;
  filtered: number;
  searchText: string;
  matchRate: number;
  keywords: string[];
  suggestions: string[];
}

/**
 * 高级搜索Hook
 * @param originalData 原始数据
 * @param searchFields 搜索字段配置
 * @param options 搜索选项
 */
export function useAdvancedSearch<T = any>(
  originalData: T[],
  searchFields: (string | SearchField)[],
  options: SearchOptions = {}
) {
  const [searchText, setSearchText] = useState('');
  const [debouncedSearchText, setDebouncedSearchText] = useState('');

  const {
    multiKeyword = true,
    keywordLogic = 'AND',
    caseSensitive = false,
    debounceMs = 300,
    highlightMatch = false
  } = options;

  // 防抖处理
  const debounceTimer = useMemo(() => {
    let timer: NodeJS.Timeout;
    return (callback: () => void, delay: number) => {
      clearTimeout(timer);
      timer = setTimeout(callback, delay);
    };
  }, []);

  // 获取嵌套对象的值
  const getNestedValue = useCallback((obj: any, path: string): any => {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }, []);

  // 检测是否包含中文字符
  const containsChinese = useMemo(() => {
    return /[\u4e00-\u9fa5]/.test(debouncedSearchText);
  }, [debouncedSearchText]);

  // 中文分词函数
  const segmentChinese = useCallback((text: string): string[] => {
    if (!text || typeof text !== 'string') return [];

    // 简单的中文分词逻辑
    const cleanText = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, ' ');
    const segments: string[] = [];
    let currentSegment = '';

    for (let i = 0; i < cleanText.length; i++) {
      const char = cleanText[i];

      if (char === ' ') {
        if (currentSegment.trim()) {
          segments.push(currentSegment.trim());
          currentSegment = '';
        }
        continue;
      }

      if (/[\u4e00-\u9fa5]/.test(char)) {
        currentSegment += char;
        if (currentSegment.length >= 2) {
          segments.push(currentSegment);
          currentSegment = char; // 保留最后一个字符作为下一个词的开始
        }
      } else if (/[a-zA-Z0-9]/.test(char)) {
        currentSegment += char;
      }
    }

    if (currentSegment.trim()) {
      segments.push(currentSegment.trim());
    }

    return segments.filter(segment => segment.length > 0);
  }, []);

  // 地区词汇映射（简化版）
  const getRegionVariants = useCallback((term: string): string[] => {
    const regionMapping: Record<string, string[]> = {
      '深圳': ['深圳市', '深'],
      '广州': ['广州市', '穗'],
      '上海': ['上海市', '沪'],
      '北京': ['北京市', '京'],
      '杭州': ['杭州市'],
      '南京': ['南京市', '宁'],
      '武汉': ['武汉市'],
      '成都': ['成都市', '蓉']
    };

    const variants = [term];

    // 检查是否为地区词汇
    Object.entries(regionMapping).forEach(([key, values]) => {
      if (key === term) {
        variants.push(...values);
      } else if (values.includes(term)) {
        variants.push(key);
        variants.push(...values.filter(v => v !== term));
      }
    });

    return [...new Set(variants)];
  }, []);

  // 过滤数据 - 精准搜索：优先精确匹配，然后AND逻辑
  const filteredData = useMemo(() => {
    if (!debouncedSearchText.trim() || !Array.isArray(originalData)) {
      return originalData;
    }

    const originalText = debouncedSearchText.trim();
    const searchValue = caseSensitive ? originalText : originalText.toLowerCase();

    // 如果包含中文，使用分词搜索
    const keywords = containsChinese && multiKeyword
      ? segmentChinese(searchValue)
      : multiKeyword
        ? searchValue.split(/\s+/).filter(keyword => keyword.length > 0)
        : [searchValue];

    return originalData.filter(item => {
      // 策略1: 优先尝试完整文本精确匹配
      const exactMatch = searchFields.some(fieldConfig => {
        const field = typeof fieldConfig === 'string' ? fieldConfig : fieldConfig.key;
        const value = getNestedValue(item, field);
        if (value == null) return false;

        const stringValue = caseSensitive ? String(value) : String(value).toLowerCase();
        return stringValue.includes(searchValue);
      });

      if (exactMatch) {
        return true;
      }

      // 策略2: 如果没有精确匹配且有多个关键词，使用AND逻辑
      if (keywords.length > 1) {
        return keywords.every(keyword => {
          // 对地区词汇进行有限扩展
          const searchTerms = containsChinese ? getRegionVariants(keyword) : [keyword];

          return searchTerms.some(term => {
            return searchFields.some(fieldConfig => {
              const field = typeof fieldConfig === 'string' ? fieldConfig : fieldConfig.key;
              const exact = typeof fieldConfig === 'object' ? fieldConfig.exact : false;

              const value = getNestedValue(item, field);
              if (value == null) return false;

              const stringValue = caseSensitive ? String(value) : String(value).toLowerCase();
              const searchTerm = caseSensitive ? term : term.toLowerCase();

              return exact ? stringValue === searchTerm : stringValue.includes(searchTerm);
            });
          });
        });
      }

      // 策略3: 单个关键词，使用地区扩展
      if (keywords.length === 1) {
        const searchTerms = containsChinese ? getRegionVariants(keywords[0]) : keywords;

        return searchTerms.some(term => {
          return searchFields.some(fieldConfig => {
            const field = typeof fieldConfig === 'string' ? fieldConfig : fieldConfig.key;
            const exact = typeof fieldConfig === 'object' ? fieldConfig.exact : false;

            const value = getNestedValue(item, field);
            if (value == null) return false;

            const stringValue = caseSensitive ? String(value) : String(value).toLowerCase();
            const searchTerm = caseSensitive ? term : term.toLowerCase();

            return exact ? stringValue === searchTerm : stringValue.includes(searchTerm);
          });
        });
      }

      return false;
    });
  }, [originalData, debouncedSearchText, searchFields, multiKeyword, caseSensitive, getNestedValue, containsChinese, segmentChinese, getRegionVariants]);

  // 搜索建议
  const suggestions = useMemo(() => {
    if (!searchText.trim() || !Array.isArray(originalData)) return [];

    const suggestionSet = new Set<string>();
    const searchValue = searchText.toLowerCase();

    originalData.forEach(item => {
      searchFields.forEach(fieldConfig => {
        const field = typeof fieldConfig === 'string' ? fieldConfig : fieldConfig.key;
        const value = getNestedValue(item, field);
        if (value) {
          const stringValue = String(value);
          const lowerValue = stringValue.toLowerCase();
          if (lowerValue.includes(searchValue) && lowerValue !== searchValue) {
            suggestionSet.add(stringValue);
          }
        }
      });
    });

    return Array.from(suggestionSet)
      .sort((a, b) => {
        const aStartsWith = a.toLowerCase().startsWith(searchValue);
        const bStartsWith = b.toLowerCase().startsWith(searchValue);
        if (aStartsWith && !bStartsWith) return -1;
        if (!aStartsWith && bStartsWith) return 1;
        return a.length - b.length;
      })
      .slice(0, 5);
  }, [originalData, searchText, searchFields, getNestedValue]);

  // 搜索结果统计
  const searchResult: SearchResult<T> = useMemo(() => {
    const keywords = debouncedSearchText 
      ? debouncedSearchText.trim().split(/\s+/).filter(k => k.length > 0)
      : [];

    return {
      data: filteredData,
      total: originalData.length,
      filtered: filteredData.length,
      searchText: debouncedSearchText,
      matchRate: originalData.length > 0 
        ? Number((filteredData.length / originalData.length * 100).toFixed(1))
        : 0,
      keywords,
      suggestions
    };
  }, [filteredData, originalData, debouncedSearchText, suggestions]);

  // 高亮文本
  const highlightText = useCallback((text: string, className = 'search-highlight'): string => {
    if (!text || !debouncedSearchText || !highlightMatch) return text;

    const searchValue = caseSensitive 
      ? debouncedSearchText.trim() 
      : debouncedSearchText.trim().toLowerCase();
    
    const keywords = multiKeyword 
      ? searchValue.split(/\s+/).filter(keyword => keyword.length > 0)
      : [searchValue];

    let result = text;
    
    keywords.forEach(keyword => {
      const flags = caseSensitive ? 'g' : 'gi';
      const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`(${escapedKeyword})`, flags);
      result = result.replace(regex, `<span class="${className}">$1</span>`);
    });

    return result;
  }, [debouncedSearchText, caseSensitive, multiKeyword, highlightMatch]);

  // 更新搜索文本
  const updateSearchText = useCallback((text: string) => {
    setSearchText(text);
    debounceTimer(() => {
      setDebouncedSearchText(text);
    }, debounceMs);
  }, [debounceTimer, debounceMs]);

  // 清除搜索
  const clearSearch = useCallback(() => {
    setSearchText('');
    setDebouncedSearchText('');
  }, []);

  // 应用建议
  const applySuggestion = useCallback((suggestion: string) => {
    setSearchText(suggestion);
    setDebouncedSearchText(suggestion);
  }, []);

  return {
    searchText,
    updateSearchText,
    clearSearch,
    searchResult,
    highlightText,
    applySuggestion,
    isSearching: searchText !== debouncedSearchText
  };
}

export default useAdvancedSearch;
