/**
 * 权限相关Hook集合
 * 功能：
 * 1. 提供各种权限检查Hook
 * 2. 实现权限数据管理Hook
 * 3. 提供权限状态监听Hook
 * 4. 支持权限变更回调
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { usePermissions } from '../contexts/PermissionContext';
import { permissionApi } from '../services/permission';

/**
 * 基础权限检查Hook
 */
export const usePermissionCheck = (
  requiredPermissions: string | string[], 
  options: {
    requireAll?: boolean;
    fallback?: boolean;
    onPermissionChange?: (hasPermission: boolean) => void;
  } = {}
) => {
  const { requireAll = false, fallback = false, onPermissionChange } = options;
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermissions();
  
  const checkResult = useMemo(() => {
    if (loading) return fallback;
    
    if (typeof requiredPermissions === 'string') {
      return hasPermission(requiredPermissions);
    }
    
    if (Array.isArray(requiredPermissions)) {
      return requireAll 
        ? hasAllPermissions(requiredPermissions)
        : hasAnyPermission(requiredPermissions);
    }
    
    return fallback;
  }, [requiredPermissions, requireAll, hasPermission, hasAnyPermission, hasAllPermissions, loading, fallback]);
  
  // 权限变更回调
  useEffect(() => {
    if (onPermissionChange && !loading) {
      onPermissionChange(checkResult);
    }
  }, [checkResult, loading, onPermissionChange]);
  
  return {
    hasPermission: checkResult,
    loading,
    requiredPermissions: Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]
  };
};

/**
 * 角色检查Hook
 */
export const useRoleCheck = (
  requiredRoles: string | string[],
  options: {
    fallback?: boolean;
    onRoleChange?: (hasRole: boolean) => void;
  } = {}
) => {
  const { fallback = false, onRoleChange } = options;
  const { hasRole, roles, loading } = usePermissions();
  
  const checkResult = useMemo(() => {
    if (loading) return fallback;
    
    if (typeof requiredRoles === 'string') {
      return hasRole(requiredRoles);
    }
    
    if (Array.isArray(requiredRoles)) {
      return requiredRoles.some(role => hasRole(role));
    }
    
    return fallback;
  }, [requiredRoles, hasRole, loading, fallback]);
  
  // 角色变更回调
  useEffect(() => {
    if (onRoleChange && !loading) {
      onRoleChange(checkResult);
    }
  }, [checkResult, loading, onRoleChange]);
  
  return {
    hasRole: checkResult,
    currentRoles: roles,
    loading,
    requiredRoles: Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles]
  };
};

/**
 * 管理员权限检查Hook
 */
export const useAdminCheck = () => {
  return useRoleCheck(['admin', 'super_admin']);
};

/**
 * 超级管理员权限检查Hook
 */
export const useSuperAdminCheck = () => {
  return useRoleCheck('super_admin');
};

/**
 * 权限列表管理Hook
 */
export const usePermissionList = () => {
  const [allPermissions, setAllPermissions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const fetchPermissions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await permissionApi.getAllPermissions();
      
      if (response.success && response.data) {
        setAllPermissions(response.data.permissions || []);
      } else {
        throw new Error(response.error?.message || '获取权限列表失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取权限列表失败';
      setError(errorMessage);
      console.error('获取权限列表失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);
  
  useEffect(() => {
    fetchPermissions();
  }, [fetchPermissions]);
  
  return {
    permissions: allPermissions,
    loading,
    error,
    refresh: fetchPermissions
  };
};

/**
 * 用户权限管理Hook
 */
export const useUserPermissionManagement = (userId?: number) => {
  const [userPermissions, setUserPermissions] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const fetchUserPermissions = useCallback(async (targetUserId: number) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await permissionApi.getUserPermissions(targetUserId);
      
      if (response.success && response.data) {
        setUserPermissions(response.data);
      } else {
        throw new Error(response.error?.message || '获取用户权限失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取用户权限失败';
      setError(errorMessage);
      console.error('获取用户权限失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);
  
  const updateUserPermissions = useCallback(async (
    targetUserId: number, 
    permissions: { role?: string; customPermissions?: Array<{ code: string; granted: boolean }> }
  ) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await permissionApi.updateUserPermissions(targetUserId, permissions);
      
      if (response.success) {
        // 重新获取用户权限
        await fetchUserPermissions(targetUserId);
        return response.data;
      } else {
        throw new Error(response.error?.message || '更新用户权限失败');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新用户权限失败';
      setError(errorMessage);
      console.error('更新用户权限失败:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [fetchUserPermissions]);
  
  useEffect(() => {
    if (userId) {
      fetchUserPermissions(userId);
    }
  }, [userId, fetchUserPermissions]);
  
  return {
    userPermissions,
    loading,
    error,
    fetchUserPermissions,
    updateUserPermissions
  };
};

/**
 * 权限变更监听Hook
 */
export const usePermissionChangeListener = (
  callback: (permissions: string[], roles: string[]) => void,
  dependencies: any[] = []
) => {
  const { permissions, roles } = usePermissions();
  
  useEffect(() => {
    callback(permissions, roles);
  }, [permissions, roles, callback, ...dependencies]);
};

/**
 * 条件权限Hook - 根据条件动态检查权限
 */
export const useConditionalPermission = (
  condition: boolean,
  permissionsWhenTrue: string | string[],
  permissionsWhenFalse?: string | string[]
) => {
  const trueCheck = usePermissionCheck(permissionsWhenTrue);
  const falseCheck = usePermissionCheck(permissionsWhenFalse || []);
  
  return condition ? trueCheck : falseCheck;
};

/**
 * 权限组合Hook - 支持复杂的权限逻辑
 */
export const usePermissionCombination = (
  combinations: Array<{
    permissions: string | string[];
    operator: 'AND' | 'OR';
    weight?: number;
  }>
) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading } = usePermissions();
  
  const result = useMemo(() => {
    if (loading) return false;
    
    return combinations.every(combo => {
      const { permissions, operator } = combo;
      
      if (typeof permissions === 'string') {
        return hasPermission(permissions);
      }
      
      if (Array.isArray(permissions)) {
        return operator === 'AND' 
          ? hasAllPermissions(permissions)
          : hasAnyPermission(permissions);
      }
      
      return false;
    });
  }, [combinations, hasPermission, hasAnyPermission, hasAllPermissions, loading]);
  
  return {
    hasPermission: result,
    loading
  };
};

/**
 * 权限缓存Hook - 缓存权限检查结果
 */
export const usePermissionCache = () => {
  const [cache, setCache] = useState<Map<string, boolean>>(new Map());
  const { permissions, roles } = usePermissions();
  
  const checkPermissionCached = useCallback((permission: string): boolean => {
    const cacheKey = `${permissions.join(',')}_${roles.join(',')}_${permission}`;
    
    if (cache.has(cacheKey)) {
      return cache.get(cacheKey)!;
    }
    
    // 超级管理员拥有所有权限
    if (roles.includes('super_admin')) {
      cache.set(cacheKey, true);
      return true;
    }
    
    const result = permissions.includes(permission);
    cache.set(cacheKey, result);
    
    return result;
  }, [permissions, roles, cache]);
  
  const clearCache = useCallback(() => {
    setCache(new Map());
  }, []);
  
  // 权限变更时清除缓存
  useEffect(() => {
    clearCache();
  }, [permissions, roles, clearCache]);
  
  return {
    checkPermission: checkPermissionCached,
    clearCache,
    cacheSize: cache.size
  };
};

/**
 * 权限调试Hook - 开发环境下的权限调试工具
 */
export const usePermissionDebug = () => {
  const { permissions, roles, userInfo, loading, error, lastUpdated } = usePermissions();
  
  const debugInfo = useMemo(() => ({
    permissions,
    roles,
    userInfo,
    loading,
    error,
    lastUpdated,
    permissionCount: permissions.length,
    roleCount: roles.length
  }), [permissions, roles, userInfo, loading, error, lastUpdated]);
  
  const logPermissions = useCallback(() => {
    console.group('🔐 权限调试信息');
    console.log('用户信息:', userInfo);
    console.log('角色:', roles);
    console.log('权限列表:', permissions);
    console.log('权限数量:', permissions.length);
    console.log('加载状态:', loading);
    console.log('错误信息:', error);
    console.log('最后更新:', lastUpdated);
    console.groupEnd();
  }, [userInfo, roles, permissions, loading, error, lastUpdated]);
  
  return {
    debugInfo,
    logPermissions
  };
};

// 所有Hook已在上方单独导出，无需重复导出