/**
 * 权限上下文
 * 功能：
 * 1. 提供全局权限状态管理
 * 2. 实现权限数据自动刷新机制
 * 3. 提供权限检查方法
 * 4. 支持权限变更实时通知
 */

import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import { permissionApi } from '../services/permission';

// 权限上下文类型定义
interface PermissionContextType {
  // 权限数据
  permissions: string[];
  roles: string[];
  userInfo: UserInfo | null;
  
  // 权限检查方法
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  canAccessPage: (permission: string) => boolean;
  
  // 数据管理方法
  refreshPermissions: () => Promise<void>;
  clearPermissions: () => void;
  
  // 状态
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

// 用户信息类型
interface UserInfo {
  id: number;
  username: string;
  role: string;
  email?: string;
  status: string;
}

// 权限数据类型
interface PermissionData {
  userId: number;
  username: string;
  role: string;
  status: string;
  rolePermissions: string[];
  customPermissions: Array<{
    permission_code: string;
    granted: boolean;
  }>;
  effectivePermissions: string[];
  deniedPermissions: string[];
  lastUpdated: string;
}

// 创建权限上下文
const PermissionContext = createContext<PermissionContextType | null>(null);

// 权限提供者组件属性
interface PermissionProviderProps {
  children: ReactNode;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

/**
 * 权限提供者组件
 */
export const PermissionProvider: React.FC<PermissionProviderProps> = ({
  children,
  autoRefresh = true,
  refreshInterval = 300000 // 5分钟
}) => {
  // 状态管理
  const [permissions, setPermissions] = useState<string[]>([]);
  const [roles, setRoles] = useState<string[]>([]);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  /**
   * 从本地存储加载权限数据
   */
  const loadFromStorage = useCallback(() => {
    try {
      const storedData = localStorage.getItem('userPermissions');
      if (storedData) {
        const data: PermissionData = JSON.parse(storedData);
        
        // 检查数据是否过期（超过1小时）
        const dataAge = Date.now() - new Date(data.lastUpdated).getTime();
        if (dataAge < 3600000) { // 1小时
          setPermissions(data.effectivePermissions || []);
          setRoles([data.role]);
          setUserInfo({
            id: data.userId,
            username: data.username,
            role: data.role,
            status: data.status
          });
          setLastUpdated(new Date(data.lastUpdated));
          setLoading(false);
          return true;
        }
      }
    } catch (error) {
      console.warn('加载本地权限数据失败:', error);
    }
    return false;
  }, []);

  /**
   * 保存权限数据到本地存储
   */
  const saveToStorage = useCallback((data: PermissionData) => {
    try {
      localStorage.setItem('userPermissions', JSON.stringify(data));
    } catch (error) {
      console.warn('保存权限数据到本地存储失败:', error);
    }
  }, []);

  /**
   * 从服务器获取权限数据
   */
  const fetchPermissions = useCallback(async () => {
    try {
      setError(null);

      // 记录开始时间，确保最小加载时间为800ms，让用户能看到加载动画
      const startTime = Date.now();

      const response = await permissionApi.getCurrentUserPermissions();
      
      if (response.success && response.data) {
        const data = response.data;

        // 兼容不同的API响应格式
        const permissions = data.effectivePermissions || data.permissions || [];

        // 计算已经过去的时间
        const elapsedTime = Date.now() - startTime;
        const minLoadingTime = 800; // 最小加载时间800ms

        // 如果加载时间不足800ms，等待剩余时间
        if (elapsedTime < minLoadingTime) {
          await new Promise(resolve => setTimeout(resolve, minLoadingTime - elapsedTime));
        }

        setPermissions(permissions);
        setRoles([data.role]);
        setUserInfo({
          id: data.userId,
          username: data.username,
          role: data.role,
          status: data.status
        });
        setLastUpdated(new Date());

        // 保存到本地存储时，统一使用effectivePermissions字段
        const storageData = {
          ...data,
          effectivePermissions: permissions
        };
        saveToStorage(storageData);

        console.log('权限数据更新成功:', {
          permissions: permissions.length,
          role: data.role
        });
      } else {
        throw new Error(response.error?.message || '获取权限数据失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取权限数据失败';
      setError(errorMessage);
      console.error('获取权限数据失败:', error);
      
      // 如果是认证错误，清除本地数据
      if (errorMessage.includes('认证') || errorMessage.includes('登录')) {
        clearPermissions();
      }
    } finally {
      setLoading(false);
    }
  }, [saveToStorage]);

  /**
   * 刷新权限数据
   */
  const refreshPermissions = useCallback(async () => {
    setLoading(true);
    await fetchPermissions();
  }, [fetchPermissions]);

  /**
   * 清除权限数据
   */
  const clearPermissions = useCallback(() => {
    setPermissions([]);
    setRoles([]);
    setUserInfo(null);
    setError(null);
    setLastUpdated(null);
    setLoading(false);
    
    // 清除本地存储
    localStorage.removeItem('userPermissions');
    
    console.log('权限数据已清除');
  }, []);

  /**
   * 检查是否拥有特定权限
   */
  const hasPermission = useCallback((permission: string): boolean => {
    if (!permission) return false;
    
    // 超级管理员拥有所有权限
    if (roles.includes('super_admin')) {
      return true;
    }
    
    return permissions.includes(permission);
  }, [permissions, roles]);

  /**
   * 检查是否拥有特定角色
   */
  const hasRole = useCallback((role: string): boolean => {
    if (!role) return false;
    return roles.includes(role);
  }, [roles]);

  /**
   * 检查是否拥有任意一个权限
   */
  const hasAnyPermission = useCallback((permissionList: string[]): boolean => {
    if (!permissionList || permissionList.length === 0) return false;
    
    // 超级管理员拥有所有权限
    if (roles.includes('super_admin')) {
      return true;
    }
    
    return permissionList.some(permission => permissions.includes(permission));
  }, [permissions, roles]);

  /**
   * 检查是否拥有所有权限
   */
  const hasAllPermissions = useCallback((permissionList: string[]): boolean => {
    if (!permissionList || permissionList.length === 0) return true;
    
    // 超级管理员拥有所有权限
    if (roles.includes('super_admin')) {
      return true;
    }
    
    return permissionList.every(permission => permissions.includes(permission));
  }, [permissions, roles]);

  /**
   * 检查是否可以访问页面（页面权限检查）
   */
  const canAccessPage = useCallback((permission: string): boolean => {
    if (!permission) return true;

    // 超级管理员可以访问所有页面
    if (roles.includes('super_admin')) {
      return true;
    }

    return permissions.includes(permission);
  }, [permissions, roles]);

  // 监听认证状态变化
  useEffect(() => {
    let authCheckInterval: NodeJS.Timeout;

    const checkAuthStatus = () => {
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');
      const isAuthenticated = !!(token && user);

      if (isAuthenticated && permissions.length === 0 && !loading) {
        // 用户已认证但没有权限数据，且不在加载中，获取权限数据
        console.log('检测到用户已认证但缺少权限数据，开始获取权限');
        fetchPermissions();
      } else if (!isAuthenticated && permissions.length > 0) {
        // 用户未认证但仍有权限数据，清除权限数据
        console.log('检测到用户未认证，清除权限数据');
        clearPermissions();
      }
    };

    // 立即检查一次
    checkAuthStatus();

    // 每秒检查一次认证状态（仅在必要时）
    authCheckInterval = setInterval(checkAuthStatus, 1000);

    return () => {
      if (authCheckInterval) {
        clearInterval(authCheckInterval);
      }
    };
  }, [permissions.length, loading, fetchPermissions, clearPermissions]);

  // 组件初始化
  useEffect(() => {
    // 检查用户是否已认证
    const token = localStorage.getItem('token');
    const user = localStorage.getItem('user');

    // 只有在用户已认证时才获取权限数据
    if (!token || !user) {
      console.log('用户未认证，跳过权限数据获取');
      setLoading(false);
      return;
    }

    // 首先尝试从本地存储加载
    const loadedFromStorage = loadFromStorage();

    // 如果没有本地数据或数据过期，从服务器获取
    if (!loadedFromStorage) {
      fetchPermissions();
    }
  }, [loadFromStorage, fetchPermissions]);

  // 自动刷新权限数据
  useEffect(() => {
    if (!autoRefresh || refreshInterval <= 0) return;

    const interval = setInterval(() => {
      // 检查用户是否已认证
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');

      // 只有在用户已认证且页面可见时才刷新
      if (token && user && document.visibilityState === 'visible') {
        fetchPermissions();
      }
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval, fetchPermissions]);

  // 监听页面可见性变化，页面重新可见时刷新权限
  useEffect(() => {
    const handleVisibilityChange = () => {
      // 检查用户是否已认证
      const token = localStorage.getItem('token');
      const user = localStorage.getItem('user');

      if (token && user && document.visibilityState === 'visible' && lastUpdated) {
        // 如果超过5分钟没有更新，则刷新
        const timeSinceUpdate = Date.now() - lastUpdated.getTime();
        if (timeSinceUpdate > 300000) {
          fetchPermissions();
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [lastUpdated, fetchPermissions]);

  // 监听存储变化（多标签页同步和登录状态变化）
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'userPermissions') {
        if (event.newValue) {
          // 其他标签页更新了权限数据
          loadFromStorage();
        } else {
          // 其他标签页清除了权限数据
          clearPermissions();
        }
      } else if (event.key === 'token' || event.key === 'user') {
        // 监听登录状态变化
        const token = localStorage.getItem('token');
        const user = localStorage.getItem('user');

        if (token && user && event.newValue) {
          // 用户刚刚登录，刷新权限数据
          console.log('检测到用户登录，刷新权限数据');
          fetchPermissions();
        } else if (!token || !user) {
          // 用户登出，清除权限数据
          console.log('检测到用户登出，清除权限数据');
          clearPermissions();
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [loadFromStorage, clearPermissions, fetchPermissions]);

  // 上下文值
  const contextValue: PermissionContextType = {
    permissions,
    roles,
    userInfo,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAllPermissions,
    canAccessPage,
    refreshPermissions,
    clearPermissions,
    loading,
    error,
    lastUpdated
  };

  return (
    <PermissionContext.Provider value={contextValue}>
      {children}
    </PermissionContext.Provider>
  );
};

/**
 * 使用权限上下文的Hook
 */
export const usePermissions = (): PermissionContextType => {
  const context = useContext(PermissionContext);
  
  if (!context) {
    throw new Error('usePermissions must be used within a PermissionProvider');
  }
  
  return context;
};

/**
 * 权限检查Hook
 * 基于React最佳实践优化：
 * 1. 使用useMemo避免不必要的重新计算
 * 2. 支持条件渲染的多种模式
 * 3. 提供权限过滤功能
 */
export const usePermissionCheck = (requiredPermissions: string | string[], requireAll = false) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, loading, roles } = usePermissions();

  const checkResult = React.useMemo(() => {
    if (loading) return false;

    // 超级管理员绕过检查（Gate::before模式）
    if (roles.includes('super_admin')) {
      return true;
    }

    if (typeof requiredPermissions === 'string') {
      return hasPermission(requiredPermissions);
    }

    if (Array.isArray(requiredPermissions)) {
      return requireAll
        ? hasAllPermissions(requiredPermissions)
        : hasAnyPermission(requiredPermissions);
    }

    return false;
  }, [requiredPermissions, requireAll, hasPermission, hasAnyPermission, hasAllPermissions, loading, roles]);

  return {
    hasPermission: checkResult,
    loading,
    // 条件渲染辅助函数
    renderIf: (component: React.ReactNode, fallback: React.ReactNode = null) =>
      checkResult ? component : fallback,
    // 权限过滤函数
    filterData: (data: any, attributes: string[] = ['*']) => {
      if (!checkResult) return null;
      if (attributes.includes('*')) return data;

      const filtered: any = {};
      attributes.forEach(attr => {
        if (!attr.startsWith('!') && data[attr] !== undefined) {
          filtered[attr] = data[attr];
        }
      });
      return filtered;
    }
  };
};

/**
 * 增强的权限检查Hook
 * 支持更复杂的权限逻辑和条件渲染
 */
export const useAdvancedPermissionCheck = (config: {
  permissions?: string | string[];
  roles?: string | string[];
  requireAll?: boolean;
  logic?: 'AND' | 'OR';
  attributes?: string[];
}) => {
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    loading,
    roles: userRoles
  } = usePermissions();

  const {
    permissions = [],
    roles = [],
    requireAll = false,
    logic = 'OR',
    attributes = ['*']
  } = config;

  const checkResult = React.useMemo(() => {
    if (loading) return { granted: false, loading: true };

    // 超级管理员绕过检查
    if (userRoles.includes('super_admin')) {
      return {
        granted: true,
        loading: false,
        attributes: ['*'],
        reason: '超级管理员权限'
      };
    }

    const permissionsArray = Array.isArray(permissions) ? permissions : [permissions].filter(Boolean);
    const rolesArray = Array.isArray(roles) ? roles : [roles].filter(Boolean);

    const hasPermissions = permissionsArray.length === 0 ||
      (requireAll ? hasAllPermissions(permissionsArray) : hasAnyPermission(permissionsArray));
    const hasRoles = rolesArray.length === 0 ||
      rolesArray.some(role => hasRole(role));

    let granted = false;
    if (logic === 'AND') {
      granted = hasPermissions && hasRoles;
    } else {
      granted = hasPermissions || hasRoles;
    }

    return {
      granted,
      loading: false,
      attributes,
      reason: granted ? '权限验证通过' : '权限不足'
    };
  }, [permissions, roles, requireAll, logic, hasPermission, hasAnyPermission, hasAllPermissions, hasRole, loading, userRoles, attributes]);

  return {
    ...checkResult,
    // 条件渲染组件
    ConditionalRender: ({ children, fallback = null }: { children: React.ReactNode; fallback?: React.ReactNode }) =>
      checkResult.granted ? <>{children}</> : <>{fallback}</>,
    // 权限过滤函数
    filter: (data: any) => {
      if (!checkResult.granted) return null;
      if (checkResult.attributes.includes('*')) {
        // 处理拒绝属性
        const deniedAttrs = checkResult.attributes.filter(attr => attr.startsWith('!'));
        if (deniedAttrs.length === 0) return data;

        const filtered = { ...data };
        deniedAttrs.forEach(attr => {
          const key = attr.substring(1);
          delete filtered[key];
        });
        return filtered;
      }

      const filtered: any = {};
      checkResult.attributes.forEach(attr => {
        if (!attr.startsWith('!') && data[attr] !== undefined) {
          filtered[attr] = data[attr];
        }
      });
      return filtered;
    }
  };
};

/**
 * 角色检查Hook
 */
export const useRoleCheck = (requiredRoles: string | string[]) => {
  const { hasRole, roles, loading } = usePermissions();
  
  const checkResult = React.useMemo(() => {
    if (loading) return false;
    
    if (typeof requiredRoles === 'string') {
      return hasRole(requiredRoles);
    }
    
    if (Array.isArray(requiredRoles)) {
      return requiredRoles.some(role => hasRole(role));
    }
    
    return false;
  }, [requiredRoles, hasRole, loading]);
  
  return {
    hasRole: checkResult,
    currentRoles: roles,
    loading
  };
};

export default PermissionContext;