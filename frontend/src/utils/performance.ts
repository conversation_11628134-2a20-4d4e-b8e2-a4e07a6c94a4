// 前端性能优化工具

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// 图片懒加载
export const lazyLoadImage = (img: HTMLImageElement, src: string): void => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const image = entry.target as HTMLImageElement;
        image.src = src;
        image.classList.remove('lazy');
        observer.unobserve(image);
      }
    });
  });
  
  observer.observe(img);
};

// 虚拟滚动配置
export interface VirtualScrollConfig {
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}

export const calculateVirtualScrollItems = (
  scrollTop: number,
  totalItems: number,
  config: VirtualScrollConfig
) => {
  const { itemHeight, containerHeight, overscan = 5 } = config;
  
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    totalItems - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );
  
  return {
    startIndex,
    endIndex,
    visibleItems: endIndex - startIndex + 1,
    offsetY: startIndex * itemHeight
  };
};

// 内存使用监控
export const monitorMemoryUsage = (): void => {
  if ('memory' in performance) {
    const memory = (performance as any).memory;
    console.log('内存使用情况:', {
      used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)} MB`,
      total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)} MB`,
      limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)} MB`
    });
  }
};

// 性能标记
export const performanceMark = {
  start: (name: string): void => {
    performance.mark(`${name}-start`);
  },
  
  end: (name: string): number => {
    try {
      performance.mark(`${name}-end`);

      // 检查start标记是否存在
      const startMarks = performance.getEntriesByName(`${name}-start`);
      if (startMarks.length === 0) {
        console.warn(`Performance mark '${name}-start' does not exist`);
        return 0;
      }

      performance.measure(name, `${name}-start`, `${name}-end`);

      const measure = performance.getEntriesByName(name)[0];
      const duration = measure ? measure.duration : 0;

      // 清理标记
      performance.clearMarks(`${name}-start`);
      performance.clearMarks(`${name}-end`);
      performance.clearMeasures(name);

      return duration;
    } catch (error) {
      console.warn(`Performance measurement failed for '${name}':`, error);
      return 0;
    }
  }
};

// 组件渲染性能监控
export const withPerformanceMonitor = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName: string
) => {
  return React.memo((props: P) => {
    React.useEffect(() => {
      performanceMark.start(`${componentName}-render`);
      
      return () => {
        const duration = performanceMark.end(`${componentName}-render`);
        if (duration > 16) { // 超过一帧的时间
          console.warn(`组件 ${componentName} 渲染耗时: ${duration.toFixed(2)}ms`);
        }
      };
    });
    
    return React.createElement(WrappedComponent, props);
  });
};

// 缓存管理
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  set(key: string, data: any, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
  
  get<T = any>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  delete(key: string): boolean {
    return this.cache.delete(key);
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  size(): number {
    return this.cache.size;
  }
  
  // 清理过期缓存
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

export const cacheManager = new CacheManager();

// 定期清理过期缓存
setInterval(() => {
  cacheManager.cleanup();
}, 60000); // 每分钟清理一次

// 请求去重
class RequestDeduplicator {
  private pendingRequests = new Map<string, Promise<any>>();
  
  async deduplicate<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    if (this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)!;
    }
    
    const promise = requestFn().finally(() => {
      this.pendingRequests.delete(key);
    });
    
    this.pendingRequests.set(key, promise);
    return promise;
  }
}

export const requestDeduplicator = new RequestDeduplicator();

// 批量请求管理
class BatchRequestManager {
  private batches = new Map<string, {
    requests: Array<{ resolve: Function; reject: Function; params: any }>;
    timer: NodeJS.Timeout;
  }>();
  
  batch<T>(
    key: string,
    params: any,
    batchFn: (paramsList: any[]) => Promise<T[]>,
    delay: number = 10
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      if (!this.batches.has(key)) {
        this.batches.set(key, {
          requests: [],
          timer: setTimeout(() => this.executeBatch(key, batchFn), delay)
        });
      }
      
      const batch = this.batches.get(key)!;
      batch.requests.push({ resolve, reject, params });
    });
  }
  
  private async executeBatch<T>(key: string, batchFn: (paramsList: any[]) => Promise<T[]>): Promise<void> {
    const batch = this.batches.get(key);
    if (!batch) return;
    
    this.batches.delete(key);
    
    try {
      const paramsList = batch.requests.map(req => req.params);
      const results = await batchFn(paramsList);
      
      batch.requests.forEach((req, index) => {
        req.resolve(results[index]);
      });
    } catch (error) {
      batch.requests.forEach(req => {
        req.reject(error);
      });
    }
  }
}

export const batchRequestManager = new BatchRequestManager();

// 资源预加载
export const preloadResource = (url: string, type: 'script' | 'style' | 'image' = 'script'): Promise<void> => {
  return new Promise((resolve, reject) => {
    let element: HTMLElement;
    
    switch (type) {
      case 'script':
        element = document.createElement('script');
        (element as HTMLScriptElement).src = url;
        break;
      case 'style':
        element = document.createElement('link');
        (element as HTMLLinkElement).rel = 'stylesheet';
        (element as HTMLLinkElement).href = url;
        break;
      case 'image':
        element = document.createElement('img');
        (element as HTMLImageElement).src = url;
        break;
    }
    
    element.onload = () => resolve();
    element.onerror = () => reject(new Error(`Failed to load ${type}: ${url}`));
    
    if (type !== 'image') {
      document.head.appendChild(element);
    }
  });
};

// 组件预加载
export const preloadComponent = (importFn: () => Promise<any>): void => {
  // 在空闲时间预加载组件
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      importFn().catch(() => {
        // 忽略预加载失败
      });
    });
  } else {
    setTimeout(() => {
      importFn().catch(() => {
        // 忽略预加载失败
      });
    }, 100);
  }
};

// 性能监控报告
export const getPerformanceReport = () => {
  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  const paint = performance.getEntriesByType('paint');
  
  return {
    // 页面加载时间
    pageLoad: navigation.loadEventEnd - navigation.loadEventStart,
    // DNS查询时间
    dns: navigation.domainLookupEnd - navigation.domainLookupStart,
    // TCP连接时间
    tcp: navigation.connectEnd - navigation.connectStart,
    // 请求响应时间
    request: navigation.responseEnd - navigation.requestStart,
    // DOM解析时间
    domParse: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
    // 首次绘制时间
    firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
    // 首次内容绘制时间
    firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
    // 内存使用情况
    memory: 'memory' in performance ? {
      used: (performance as any).memory.usedJSHeapSize,
      total: (performance as any).memory.totalJSHeapSize,
      limit: (performance as any).memory.jsHeapSizeLimit
    } : null
  };
};
