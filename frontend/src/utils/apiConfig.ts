/**
 * 统一的API配置工具
 * 解决API基础URL配置不一致的问题
 */

// 获取API基础URL
export const getApiBaseUrl = (): string => {
  const envUrl = import.meta.env.VITE_API_URL;
  const defaultUrl = 'http://localhost:3001';
  
  // 在开发环境中，优先使用环境变量，否则使用默认值
  const baseUrl = envUrl || defaultUrl;
  
  console.log(`🌐 API基础URL: ${baseUrl} (来源: ${envUrl ? '环境变量' : '默认值'})`);
  return baseUrl;
};

// 获取完整的API URL
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  const fullUrl = `${baseUrl}/api/v1${cleanEndpoint}`;
  
  console.log(`🔗 构建API URL: ${fullUrl}`);
  return fullUrl;
};

// 获取默认的请求头
export const getDefaultHeaders = (): Record<string, string> => {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  const token = localStorage.getItem('token');
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  
  return headers;
};

// 统一的API调用函数
export const apiCall = async (endpoint: string, options: RequestInit = {}): Promise<Response> => {
  const url = getApiUrl(endpoint);
  const defaultHeaders = getDefaultHeaders();
  
  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };
  
  console.log(`🚀 API调用: ${config.method || 'GET'} ${url}`);
  
  try {
    const response = await fetch(url, config);
    
    if (!response.ok) {
      console.error(`❌ API调用失败: ${response.status} ${response.statusText}`);
    } else {
      console.log(`✅ API调用成功: ${response.status}`);
    }
    
    return response;
  } catch (error) {
    console.error(`💥 API调用异常:`, error);
    throw error;
  }
};

// 便捷的HTTP方法
export const apiGet = (endpoint: string, options?: RequestInit) => 
  apiCall(endpoint, { ...options, method: 'GET' });

export const apiPost = (endpoint: string, data?: any, options?: RequestInit) => 
  apiCall(endpoint, { 
    ...options, 
    method: 'POST', 
    body: data ? JSON.stringify(data) : undefined 
  });

export const apiPut = (endpoint: string, data?: any, options?: RequestInit) => 
  apiCall(endpoint, { 
    ...options, 
    method: 'PUT', 
    body: data ? JSON.stringify(data) : undefined 
  });

export const apiDelete = (endpoint: string, options?: RequestInit) => 
  apiCall(endpoint, { ...options, method: 'DELETE' });

// 检查API配置
export const checkApiConfig = (): void => {
  const baseUrl = getApiBaseUrl();
  const envUrl = import.meta.env.VITE_API_URL;
  
  console.group('🔧 API配置检查');
  console.log('环境变量 VITE_API_URL:', envUrl || '未设置');
  console.log('实际使用的基础URL:', baseUrl);
  console.log('是否使用代理:', !envUrl ? '是（开发模式）' : '否');
  console.groupEnd();
};

// 在开发环境中自动检查配置
if (import.meta.env.DEV) {
  checkApiConfig();
}
