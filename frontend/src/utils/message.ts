import { App } from 'antd';

let messageApi: any = null;

// 获取message实例
export const getMessageApi = () => {
  if (!messageApi) {
    // 如果还没有获取到message实例，使用静态方法作为fallback
    const { message } = App.useApp?.() || {};
    messageApi = message;
  }
  return messageApi;
};

// 设置message实例
export const setMessageApi = (api: any) => {
  messageApi = api;
};

// 导出message方法
export const message = {
  success: (content: string) => {
    const api = getMessageApi();
    if (api) {
      api.success(content);
    } else {
      console.log('✅', content);
    }
  },
  error: (content: string) => {
    const api = getMessageApi();
    if (api) {
      api.error(content);
    } else {
      console.error('❌', content);
    }
  },
  warning: (content: string) => {
    const api = getMessageApi();
    if (api) {
      api.warning(content);
    } else {
      console.warn('⚠️', content);
    }
  },
  info: (content: string) => {
    const api = getMessageApi();
    if (api) {
      api.info(content);
    } else {
      console.info('ℹ️', content);
    }
  },
  loading: (content: string) => {
    const api = getMessageApi();
    if (api) {
      return api.loading(content);
    } else {
      console.log('⏳', content);
      return () => {}; // 返回空的销毁函数
    }
  },
};
