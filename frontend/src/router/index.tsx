import { createBrowserRouter, Navigate } from 'react-router-dom';
import { Suspense, lazy } from 'react';
import { Spin } from 'antd';

// 布局组件
import MainLayout from '../components/Layout/MainLayout';
import AuthLayout from '../components/Layout/AuthLayout';

// 路由守卫
import ProtectedRoute from '../components/Route/ProtectedRoute';
import PublicRoute from '../components/Route/PublicRoute';
import RootRedirect from '../components/Route/RootRedirect';
import PermissionGuard from '../components/Permission/PermissionGuard';

// 加载组件
const PageLoading = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh'
  }}>
    <Spin size="large" />
  </div>
);

// 懒加载页面组件
const Login = lazy(() => import('../pages/Auth/Login'));
const Dashboard = lazy(() => import('../pages/Dashboard'));
const UserList = lazy(() => import('../pages/User/UserList'));
const UserDetail = lazy(() => import('../pages/User/UserDetail'));

const ProjectList = lazy(() => import('../pages/Project/ProjectList'));
const ProjectDetail = lazy(() => import('../pages/Project/ProjectDetail'));
const WebsiteList = lazy(() => import('../pages/Website/WebsiteList'));
const SimpleEnhancedWebsiteList = lazy(() => import('../pages/Website/SimpleEnhancedWebsiteList'));
const AdvancedWebsiteManagement = lazy(() => import('../pages/Website/AdvancedWebsiteManagement'));
const TestWebsiteForm = lazy(() => import('../pages/Website/TestWebsiteForm'));
const WebsiteNavigation = lazy(() => import('../components/Website/WebsiteNavigation'));
const SimpleTest = lazy(() => import('../pages/Test/SimpleTest'));
const PermissionTest = lazy(() => import('../pages/Test/PermissionTest'));
const WebsiteDetail = lazy(() => import('../pages/Website/WebsiteDetail'));
const DomainStatusTest = lazy(() => import('../pages/Website/DomainStatusTest'));

const SimpleServerManagement = lazy(() => import('../pages/Server/SimpleServerManagement'));
const EnhancedDashboard = lazy(() => import('../pages/Dashboard/EnhancedDashboard'));
const KnowledgeBase = lazy(() => import('../pages/Knowledge/KnowledgeBase'));
const ServerList = lazy(() => import('../pages/Server/ServerList'));
const ServerDetail = lazy(() => import('../pages/Server/ServerDetail'));
const DomainList = lazy(() => import('../pages/Domain/DomainList'));
const DomainDetail = lazy(() => import('../pages/Domain/DomainDetail'));
const MonitorDashboard = lazy(() => import('../pages/Monitor/Dashboard'));
const Settings = lazy(() => import('../pages/Settings/SimpleSettings'));
const Profile = lazy(() => import('../pages/Profile'));
const NotFound = lazy(() => import('../pages/Error/NotFound'));

// 路由配置
export const router = createBrowserRouter([
  {
    path: '/',
    element: <RootRedirect />,
  },
  {
    path: '/login',
    element: (
      <PublicRoute>
        <AuthLayout>
          <Suspense fallback={<PageLoading />}>
            <Login />
          </Suspense>
        </AuthLayout>
      </PublicRoute>
    ),
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        path: 'dashboard',
        element: (
          <Suspense fallback={<PageLoading />}>
            <Dashboard />
          </Suspense>
        ),
      },
      {
        path: 'dashboard/enhanced',
        element: (
          <Suspense fallback={<PageLoading />}>
            <EnhancedDashboard />
          </Suspense>
        ),
      },
      {
        path: 'users',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<PageLoading />}>
                <UserList />
              </Suspense>
            ),
          },
          {
            path: ':id',
            element: (
              <Suspense fallback={<PageLoading />}>
                <UserDetail />
              </Suspense>
            ),
          },
        ],
      },

      {
        path: 'projects',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<PageLoading />}>
                <ProjectList />
              </Suspense>
            ),
          },
          {
            path: ':id',
            element: (
              <Suspense fallback={<PageLoading />}>
                <ProjectDetail />
              </Suspense>
            ),
          },
        ],
      },
      {
        path: 'websites',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<PageLoading />}>
                <WebsiteList />
              </Suspense>
            ),
          },
          {
            path: 'navigation',
            element: (
              <Suspense fallback={<PageLoading />}>
                <WebsiteNavigation />
              </Suspense>
            ),
          },
          {
            path: 'enhanced',
            element: (
              <Suspense fallback={<PageLoading />}>
                <SimpleEnhancedWebsiteList />
              </Suspense>
            ),
          },
          {
            path: 'advanced',
            element: (
              <Suspense fallback={<PageLoading />}>
                <AdvancedWebsiteManagement />
              </Suspense>
            ),
          },
          {
            path: 'test-form',
            element: (
              <Suspense fallback={<PageLoading />}>
                <TestWebsiteForm />
              </Suspense>
            ),
          },
          {
            path: 'domain-test',
            element: (
              <Suspense fallback={<PageLoading />}>
                <DomainStatusTest />
              </Suspense>
            ),
          },
          {
            path: ':id',
            element: (
              <Suspense fallback={<PageLoading />}>
                <WebsiteDetail />
              </Suspense>
            ),
          },
        ],
      },
      {
        path: 'servers',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<PageLoading />}>
                <SimpleServerManagement />
              </Suspense>
            ),
          },
          {
            path: 'list',
            element: (
              <Suspense fallback={<PageLoading />}>
                <ServerList />
              </Suspense>
            ),
          },
          {
            path: ':id',
            element: (
              <Suspense fallback={<PageLoading />}>
                <ServerDetail />
              </Suspense>
            ),
          },
        ],
      },
      {
        path: 'domains',
        children: [
          {
            index: true,
            element: (
              <Suspense fallback={<PageLoading />}>
                <DomainList />
              </Suspense>
            ),
          },
          {
            path: ':id',
            element: (
              <Suspense fallback={<PageLoading />}>
                <DomainDetail />
              </Suspense>
            ),
          },
        ],
      },
      {
        path: 'monitor',
        element: (
          <Suspense fallback={<PageLoading />}>
            <MonitorDashboard />
          </Suspense>
        ),
      },
      {
        path: 'settings',
        element: (
          <Suspense fallback={<PageLoading />}>
            <Settings />
          </Suspense>
        ),
      },
      {
        path: 'profile',
        element: (
          <Suspense fallback={<PageLoading />}>
            <Profile />
          </Suspense>
        ),
      },



      {
        path: 'knowledge',
        element: (
          <Suspense fallback={<PageLoading />}>
            <KnowledgeBase />
          </Suspense>
        ),
      },


      {
        path: 'test',
        element: (
          <Suspense fallback={<PageLoading />}>
            <SimpleTest />
          </Suspense>
        ),
      },
      {
        path: 'permission-test',
        element: (
          <Suspense fallback={<PageLoading />}>
            <PermissionTest />
          </Suspense>
        ),
      },
    ],
  },
  {
    path: '*',
    element: (
      <Suspense fallback={<PageLoading />}>
        <NotFound />
      </Suspense>
    ),
  },
]);

export default router;
