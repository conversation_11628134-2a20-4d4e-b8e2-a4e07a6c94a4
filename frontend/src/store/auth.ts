import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginRequest } from '../types';
import { authManager, storage } from '../services/auth';
import { enhancedAuthManager } from '../services/enhancedAuth';

interface AuthState {
  // 状态
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // 操作
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: storage.getUser(),
      isAuthenticated: storage.isAuthenticated(),
      isLoading: false,
      error: null,

      // 登录
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null });
        
        try {
          const user = await enhancedAuthManager.login(credentials);
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          });
        } catch (error: any) {
          set({ 
            isLoading: false, 
            error: error.message || '登录失败',
            isAuthenticated: false,
            user: null
          });
          throw error;
        }
      },

      // 登出
      logout: async () => {
        set({ isLoading: true });
        
        try {
          await enhancedAuthManager.logout();
          set({ 
            user: null, 
            isAuthenticated: false, 
            isLoading: false,
            error: null 
          });
        } catch (error: any) {
          console.error('登出失败:', error);
          // 即使登出请求失败，也要清除本地状态
          set({ 
            user: null, 
            isAuthenticated: false, 
            isLoading: false,
            error: null 
          });
        }
      },

      // 刷新用户信息
      refreshUser: async () => {
        if (!get().isAuthenticated) return;
        
        set({ isLoading: true });
        
        try {
          const user = await authManager.refreshUserInfo();
          if (user) {
            set({ user, isLoading: false });
          } else {
            // 刷新失败，可能token已过期
            set({ 
              user: null, 
              isAuthenticated: false, 
              isLoading: false 
            });
          }
        } catch (error: any) {
          console.error('刷新用户信息失败:', error);
          set({ 
            user: null, 
            isAuthenticated: false, 
            isLoading: false,
            error: error.message || '刷新用户信息失败'
          });
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// 监听认证状态变化 - 暂时注释掉避免状态冲突
// authManager.addListener((user) => {
//   useAuthStore.setState({
//     user,
//     isAuthenticated: !!user,
//   });
// });
