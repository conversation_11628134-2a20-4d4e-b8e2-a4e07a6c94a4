import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AppState {
  // 主题设置
  theme: 'light' | 'dark';
  primaryColor: string;
  
  // 布局设置
  collapsed: boolean;
  sidebarWidth: number;
  
  // 语言设置
  locale: 'zh-CN' | 'en-US';
  
  // 页面设置
  pageSize: number;
  
  // 通知设置
  notifications: boolean;
  soundEnabled: boolean;
  
  // 操作
  setTheme: (theme: 'light' | 'dark') => void;
  setPrimaryColor: (color: string) => void;
  setCollapsed: (collapsed: boolean) => void;
  setSidebarWidth: (width: number) => void;
  setLocale: (locale: 'zh-CN' | 'en-US') => void;
  setPageSize: (size: number) => void;
  setNotifications: (enabled: boolean) => void;
  setSoundEnabled: (enabled: boolean) => void;
  resetSettings: () => void;
}

const defaultSettings = {
  theme: 'light' as const,
  primaryColor: '#1890ff',
  collapsed: false,
  sidebarWidth: 256,
  locale: 'zh-CN' as const,
  pageSize: 10,
  notifications: true,
  soundEnabled: true,
};

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      ...defaultSettings,

      setTheme: (theme) => set({ theme }),
      
      setPrimaryColor: (primaryColor) => set({ primaryColor }),
      
      setCollapsed: (collapsed) => set({ collapsed }),
      
      setSidebarWidth: (sidebarWidth) => set({ sidebarWidth }),
      
      setLocale: (locale) => set({ locale }),
      
      setPageSize: (pageSize) => set({ pageSize }),
      
      setNotifications: (notifications) => set({ notifications }),
      
      setSoundEnabled: (soundEnabled) => set({ soundEnabled }),
      
      resetSettings: () => set(defaultSettings),
    }),
    {
      name: 'app-settings',
    }
  )
);

// 全局状态管理
interface GlobalState {
  // 加载状态
  loading: boolean;
  
  // 错误状态
  error: string | null;
  
  // 当前页面
  currentPage: string;
  
  // 面包屑
  breadcrumbs: Array<{ title: string; path?: string }>;
  
  // 操作
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentPage: (page: string) => void;
  setBreadcrumbs: (breadcrumbs: Array<{ title: string; path?: string }>) => void;
  clearError: () => void;
}

export const useGlobalStore = create<GlobalState>((set) => ({
  loading: false,
  error: null,
  currentPage: '',
  breadcrumbs: [],

  setLoading: (loading) => set({ loading }),
  
  setError: (error) => set({ error }),
  
  setCurrentPage: (currentPage) => set({ currentPage }),
  
  setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs }),
  
  clearError: () => set({ error: null }),
}));
