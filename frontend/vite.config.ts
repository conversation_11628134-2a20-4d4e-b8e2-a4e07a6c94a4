import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    strictPort: true,
    host: true,
    proxy: {
      '/api': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false,
        timeout: 30000, // 增加代理超时时间
      },
    },
    // 开发服务器性能优化
    hmr: {
      overlay: false, // 减少错误覆盖层
    },
    fs: {
      strict: false, // 允许访问工作区外的文件
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    // 构建性能优化
    target: 'es2015',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // 生产环境移除console
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        // 更细粒度的代码分割
        manualChunks: {
          vendor: ['react', 'react-dom'],
          antd: ['antd', '@ant-design/icons'],
          router: ['react-router-dom'],
          utils: ['axios', 'dayjs', 'lodash-es'],
          charts: ['echarts', 'recharts'], // 图表库单独分割
        },
        // 优化chunk文件名
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: '[ext]/[name]-[hash].[ext]',
      },
    },
    // 增加chunk大小限制
    chunkSizeWarningLimit: 1000,
  },
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'antd',
      '@ant-design/icons',
      'axios',
      'dayjs',
      'lodash-es'
    ],
    // 预构建优化
    force: false,
  },
  // 缓存配置
  cacheDir: 'node_modules/.vite',
})
