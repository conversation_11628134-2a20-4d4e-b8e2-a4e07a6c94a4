{"name": "sitemanager-frontend", "version": "1.0.0", "description": "WordPress站点管理后台系统 - 前端应用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@ant-design/charts": "^2.3.0", "@ant-design/icons": "^5.2.6", "@ant-design/pro-components": "^2.6.48", "@hookform/resolvers": "^3.3.2", "@types/xlsx": "^0.0.35", "antd": "^5.12.8", "axios": "^1.6.2", "dayjs": "^1.11.13", "docx-preview": "^0.3.5", "file-saver": "^2.0.5", "framer-motion": "^12.18.1", "js-file-download": "^0.4.12", "lodash-es": "^4.17.21", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-query": "^3.39.3", "react-router-dom": "^6.20.1", "react-virtualized": "^9.22.5", "recharts": "^2.8.0", "xlsx": "^0.18.5", "yup": "^1.4.0", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/file-saver": "^2.0.7", "@types/lodash-es": "^4.17.12", "@types/react": "^18.2.43", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^18.2.17", "@types/react-virtualized": "^9.21.29", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/ui": "^1.0.4", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "jsdom": "^23.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}