version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: sitemanager-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root123}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-sitemanager}
      MYSQL_USER: ${MYSQL_USER:-sitemanager}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-sitemanager123}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
      - ./mysql/conf.d:/etc/mysql/conf.d:ro
    ports:
      - "3306:3306"
    networks:
      - sitemanager-network
    healthcheck:
      test: ["<PERSON><PERSON>", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10
    command: --default-authentication-plugin=mysql_native_password

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: sitemanager-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - sitemanager-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 10s
      retries: 5

  # 后端 API 服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: sitemanager-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      PORT: 3001
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: ${MYSQL_DATABASE:-sitemanager}
      DB_USER: ${MYSQL_USER:-sitemanager}
      DB_PASSWORD: ${MYSQL_PASSWORD:-sitemanager123}
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET}
      ENCRYPTION_KEY: ${ENCRYPTION_KEY}
    volumes:
      - uploads_data:/app/backend/uploads
      - logs_data:/app/backend/logs
      - ./ssl-checker:/app/ssl-checker
    ports:
      - "3001:3001"
    networks:
      - sitemanager-network
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      timeout: 10s
      retries: 5
      start_period: 30s

  # 前端应用 (Nginx)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: sitemanager-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - logs_data:/var/log/nginx
    networks:
      - sitemanager-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      timeout: 10s
      retries: 5

  # 监控服务 (可选)
  monitoring:
    build:
      context: ./coolmonitor
      dockerfile: Dockerfile
    container_name: sitemanager-monitoring
    restart: unless-stopped
    environment:
      DATABASE_URL: "mysql://${MYSQL_USER:-sitemanager}:${MYSQL_PASSWORD:-sitemanager123}@mysql:3306/${MYSQL_DATABASE:-sitemanager}"
      NEXTAUTH_SECRET: ${NEXTAUTH_SECRET}
      NEXTAUTH_URL: ${NEXTAUTH_URL:-http://localhost:3333}
    ports:
      - "3333:3333"
    networks:
      - sitemanager-network
    depends_on:
      mysql:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3333/api/health"]
      timeout: 10s
      retries: 5

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  uploads_data:
    driver: local
  logs_data:
    driver: local

networks:
  sitemanager-network:
    driver: bridge
