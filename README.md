# 网站管理系统 (SiteManager)

一个现代化的全栈网站管理系统，集成售前管理、建站管理、网站监控、服务器台账等功能，为网站开发和运维团队提供一站式解决方案。

## 基础信息

### 🏗️ 项目架构
- **项目类型**: 全栈Web应用
- **架构模式**: 前后端分离
- **部署方式**: systemd服务管理

### 🔧 技术栈

#### 后端 (Backend)
- **主要入口**: `backend/app.js` (真正的服务启动文件)
- **备用文件**: `backend/simple-server.js` (功能更完整，但未被启动脚本使用)
- **运行时**: Node.js + Express.js
- **端口**: 3001
- **API前缀**: `/api/v1`
- **特性**:
  - RESTful API设计
  - JWT认证授权
  - **Context7增强版权限系统** (基于Laravel Permission + React + AccessControl.js最佳实践)
  - 定时任务调度
  - 文件上传处理
  - SSL证书检测
  - 服务器监控

#### 前端 (Frontend)
- **主要入口**: `frontend/src/main.tsx`
- **开发服务器**: Vite
- **技术栈**: React 18 + TypeScript + Ant Design
- **端口**: 3000
- **代理配置**: `/api` → `http://localhost:3001`
- **构建工具**: Vite + Rollup
- **特性**:
  - 响应式设计
  - 组件化开发
  - 路由管理
  - 状态管理
  - 图表可视化

### 🗄️ 数据存储

#### MySQL数据库
- **主机**: localhost:3306
- **数据库名**: sitemanager
- **用户名**: sitemanager
- **密码**: sitemanager123
- **字符集**: utf8mb4
- **时区**: +08:00
- **连接池**: 20个连接
- **表数量**: 50+ 张表

#### Redis缓存
- **主机**: localhost:6379
- **数据库**: 0
- **用途**:
  - 会话存储
  - API缓存
  - 任务队列
  - 实时数据缓存

### 🚀 服务启动

#### 全栈启动 (推荐)
```bash
# systemd服务方式
sudo systemctl start sitemanager
sudo systemctl status sitemanager

# 手动启动方式
./start.sh
```

#### 单独启动
```bash
# 仅启动后端
cd backend && node simple-server.js

# 仅启动前端
cd frontend && npm run dev
```

### 🌐 访问地址
- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:3001
- **SSL检查器**: http://localhost:3001/ssl-checker/web/
- **API文档**: http://localhost:3001/api/v1 (需认证)

### 📁 核心目录结构
```
/opt/sitemanager/
├── backend/
│   ├── app.js              # 🎯 主要后端入口文件
│   ├── simple-server.js     # 📄 备用文件(功能更完整)
│   ├── models/             # 数据模型
│   ├── services/           # 业务服务
│   ├── utils/              # 工具函数
│   └── config/             # 配置文件
├── frontend/
│   ├── src/
│   │   ├── main.tsx        # 🎯 前端入口文件
│   │   ├── components/     # React组件
│   │   ├── pages/          # 页面组件
│   │   └── utils/          # 工具函数
│   ├── vite.config.ts      # Vite配置
│   └── package.json        # 依赖管理
├── start.sh                # 🚀 全栈启动脚本
├── stop.sh                 # 🛑 停止脚本
├── .env                    # 环境变量配置
└── README.md               # 项目文档
```

### 🔐 环境变量配置
主要配置文件：`.env`
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=sitemanager
DB_USER=sitemanager
DB_PASSWORD=sitemanager123

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# 应用配置
NODE_ENV=production
PORT=3001
FRONTEND_URL=http://localhost:3000
```

## 🔐 Context7增强版权限系统

### 系统架构
基于业界最佳实践设计的企业级权限控制系统，集成了Laravel Permission、React条件渲染优化和AccessControl.js的核心理念。

### 核心特性

#### 1. Laravel Permission最佳实践
- **权限优先检查**: 实现权限优先于角色的检查逻辑，提高安全性
- **超级管理员绕过模式**: 实现Gate::before模式，超级管理员自动拥有所有权限
- **权限缓存机制**: 5分钟权限缓存，显著提高系统性能
- **权限锁定机制**: 防止运行时权限配置被恶意修改

#### 2. React条件渲染优化
- **性能优化**: 使用useMemo和useCallback避免不必要的重新渲染
- **多种渲染模式**: 支持逻辑AND(&&)、三元操作符(?:)、null返回等多种条件渲染模式
- **组件memo化**: 权限组件使用React.memo优化性能
- **状态保持**: 优化权限状态管理，避免状态丢失

#### 3. AccessControl.js最佳实践
- **属性级权限过滤**: 支持字段级访问控制，可精确控制用户能看到的数据字段
- **资源-操作权限模式**: 实现三层权限架构（页面.模块.操作）
- **通配符和拒绝模式**: 支持*通配符和!拒绝模式的属性过滤
- **权限继承**: 实现角色权限继承机制

### 权限架构

#### 三层权限体系
1. **页面级权限**: 控制用户能访问哪些页面
2. **模块级权限**: 控制页面内的功能模块访问
3. **资源级权限**: 控制具体的操作权限（增删改查）

#### 权限格式
```
页面.模块.操作
例如：website.basic.view, user.permission.manage
```

#### 属性级过滤
```javascript
// 支持的属性过滤模式
'*'                    // 允许所有属性
'name,url,status'      // 只允许指定属性
'*,!password'          // 允许所有属性，但拒绝password字段
'name,url,!credentials' // 允许name和url，拒绝credentials
```

### 核心组件

#### 1. Context7EnhancedPermissionEditor
- 集成所有最佳实践的权限编辑器
- 支持页面权限、网站权限、服务器权限的统一管理
- 实时权限统计和过滤功能
- Transfer组件实现直观的权限分配

#### 2. OptimizedPermissionGuard
- 高性能的权限保护组件
- 支持多种渲染模式（隐藏、显示错误、禁用等）
- 内置权限缓存和性能优化

#### 3. PermissionFilter工具类
- 属性级数据过滤
- 支持嵌套对象和数组过滤
- 通配符和拒绝模式处理

### 使用示例

#### 1. 权限保护组件
```tsx
<OptimizedPermissionGuard
  permissions={['website.basic.view']}
  mode="hide"
  attributes={['name', 'url', '!password']}
>
  <WebsiteList />
</OptimizedPermissionGuard>
```

#### 2. 权限检查Hook
```tsx
const permissionCheck = useAdvancedPermissionCheck({
  permissions: ['user.permission.manage'],
  roles: ['admin', 'super_admin'],
  attributes: ['*', '!sensitive_data']
});

if (permissionCheck.granted) {
  // 渲染受保护的内容
}
```

#### 3. 属性过滤
```tsx
const filteredData = PermissionFilter.filterObject(userData, ['name', 'email', '!password']);
```

## 🚀 核心功能

### ✅ 已完成的核心功能

#### 1. 售前管理
- 客户信息管理和跟进
- 项目报价和合同管理
- 销售漏斗和转化跟踪
- 客户沟通记录

#### 2. 建站管理
- 项目全生命周期管理
- 进度跟踪和里程碑管理
- PDF合同导入和解析
- 团队协作和任务分配
- 项目文档和资源管理

#### 3. 网站管理
- **多平台支持**: WordPress、Shopify、WooCommerce等主流建站平台
- **智能平台映射**: 自定义platform_id字段，解决平台ID不匹配问题，确保数据一致性
- **智能到期提醒**: 已过期/本月/次月到期标红，其余为绿色
- **项目金额和续费金额管理**: 完整的财务信息跟踪
- **真实SSL证书检测**:
  - 实时检测SSL证书有效性和到期时间
  - 获取证书颁发者、序列号、指纹等详细信息
  - 支持自签名证书和多种证书类型
  - 自动状态更新和数据库存储
- **真实域名状态监控**:
  - DNS解析状态检查
  - 域名注册信息查询
  - IP地址解析和验证
  - 域名到期时间监控
- **真实网站性能评分**:
  - 页面加载时间测试
  - 响应时间和状态码检测
  - 移动端和桌面端性能评分
  - 首次内容绘制时间等核心指标
- **超高性能访问状态检测**:
  - Worker Threads多线程检测，30秒内可检测1000个站点
  - 智能检测策略，根据网站数量自动选择最优检测模式
  - 动态并发控制，最大化利用系统资源
  - 实时性能监控和统计分析
- **增强版HTTP监控检查器** (基于coolmonitor设计):
  - 灵活的HTTP状态码配置（支持200-299,301,302等复杂配置）
  - 支持多种HTTP方法（GET/POST/PUT/PATCH等）和自定义请求头
  - 智能重试机制，可配置重试次数和间隔
  - SSL证书集成监控，实时检查证书状态和到期提醒
  - 关键词内容检测，支持多关键词"或"关系匹配
  - 详细错误分类和处理，提供精确的错误消息
  - 完整的监控历史记录和状态统计
- **网站密码管理系统**:
  - 支持多种账号类型（管理员、用户、FTP、数据库等）
  - 安全的密码存储和管理
  - 密码可见性切换和一键复制
  - 登录URL和描述信息管理
  - 每个网站独立的密码管理空间
  - 完整的增删改查操作
- **高级模糊搜索系统** 🔍:
  - 多关键词搜索：支持空格分隔的多关键词搜索
  - 智能匹配：AND/OR逻辑可配置，自动模糊匹配
  - 全字段搜索：覆盖网站名称、域名、URL、平台、服务器等所有相关字段
  - 实时搜索建议：基于历史数据的智能搜索建议系统
  - 搜索统计分析：实时显示匹配数量、匹配率等统计信息
  - 中英文完美支持：支持中文和英文混合搜索
  - 防抖优化：300ms防抖处理，提升搜索响应体验
  - 搜索高亮：搜索结果关键词高亮显示
  - 搜索历史：智能搜索建议和快速选择功能
- **中文智能分词搜索** 🧠:
  - 智能分词：自动将中文搜索词分解为有意义的词汇单元
  - 地区词汇扩展：深圳↔深圳市↔深，广州↔广州市↔穗，支持34个省市
  - 行业词汇映射：工业↔制造业↔生产，科技↔技术↔高新↔IT
  - 部分匹配优化：搜索"深圳工业"能匹配"深圳市工业"等相关内容
  - 同义词扩展：基于行业特点的智能词汇扩展
  - 停用词过滤：自动过滤"的"、"有限"、"公司"等无意义词汇
  - 相关性排序：基于匹配度和词汇权重的智能排序
  - 混合语言支持：中文使用分词+扩展，英文使用传统匹配
- **附件管理系统**: 完整的文件上传和管理功能
  - 支持图片文件上传和在线预览（JPG、PNG、GIF、WebP、SVG）
  - 支持PDF文件上传和在线预览
  - 支持Excel表格文件上传（XLSX、XLS、CSV）
  - 支持Word文档上传（DOCX、DOC）
  - 支持文本文件和其他常用格式
  - 文件大小限制50MB，支持批量上传
  - 文件分类管理和描述编辑
  - 文件下载统计和访问记录
  - 安全的文件存储和访问控制

#### 4. 服务器台账管理
- **服务器信息录入和管理**: 完整的服务器基础信息管理
- **智能配置检测**: SSH连接自动获取服务器硬件配置信息
- **定时自动更新**: 每天自动更新所有服务器配置信息
- **手动刷新功能**: 支持手动触发配置信息更新
- **资源监控和使用统计**: 实时监控CPU、内存、磁盘使用情况
- **维护记录和操作日志**: 完整的操作历史记录
- **服务器状态实时监控**: 通过SSH连接进行真实监控
- **批量SSH配置**: 支持批量设置SSH密钥或SSH账密，提高运维效率

#### 5. 用户权限体系
- **权限管理系统**: 完整的企业级权限管理界面
  - 权限列表管理：查看所有系统权限，按模块分类展示
  - 用户权限分配：为用户分配具体权限，支持批量操作
  - 权限详情查看：详细的权限信息展示
  - 权限配置导出：支持JSON格式的权限配置导出
  - 实时数据更新：与后端API完全集成，支持实时刷新
- **权限模板系统** 🎯:
  - 权限模板管理：创建、编辑、删除权限模板
  - 快速应用模板：一键将预设权限模板应用到用户
  - 默认模板设置：支持设置默认权限模板
  - 模板详情查看：查看模板包含的完整权限信息
  - 系统模板保护：系统预设模板不可删除或修改
  - 权限模板导入导出：支持权限配置的批量管理
  - 行业标准预设：提供电商、企业、教育、医疗等行业标准权限模板
- **会话安全管理** 🔒:
  - 智能会话超时警告：剩余时间实时显示
  - 进度条可视化：直观显示会话过期进度（已修复计算错误）
  - 灵活延长机制：支持延长登录时间
  - 安全自动登出：超时后自动安全退出
- **角色权限体系**:
  - **超级管理员**: 全系统权限
  - **管理员**: 业务管理权限  
  - **普通用户**: 基础操作权限
- **细粒度权限控制**: 支持模块.资源.操作的细粒度权限管理

#### 6. 监控中心
- **增强版站点存活检测系统**：
  - 每5分钟检测一次所有站点状态
  - 智能重试机制：失败站点每10秒重试一次
  - 状态检测表：专门管理站点状态和错误计数
  - 精准通知：错误次数≥5的站点自动发送详细通知
  - 完整信息：包含站点名称、URL、状态码、服务器、持续时间
- **智能批量通知系统**：
  - **平台类型标识**：通知内容前置显示平台类型信息
  - **累计通知模式**：多个网站故障时发送批量通知，避免通知轰炸
  - **按平台分组**：故障网站按平台类型分组展示，便于快速定位
  - **详细统计信息**：包含故障网站总数、涉及平台数、连续失败次数等
  - **智能回退机制**：批量通知失败时自动回退到逐个发送模式
  - **建议操作指导**：提供故障处理建议和下次检测时间
- **魔改版SSL证书检查器**：
  - 基于GitHub开源项目魔改，集成到系统中
  - 批量SSL检测：从数据库读取网站列表，批量检查SSL证书状态
  - 智能并发控制：5个网站同时检查，避免过载
  - 详细报告生成：生成JSON格式的SSL检查报告
  - Web界面展示：美观的Web界面查看SSL状态
  - 兼容性支持：生成兼容原项目的ct.json格式
  - 数据库集成：自动更新网站表中的SSL相关字段
- 系统资源监控（CPU、内存、磁盘、网络）
- 网站状态监控（在线状态、响应时间、可用性）
- 服务器监控（多服务器状态、资源使用情况）
- SSL证书监控（自动检测到期时间、提前预警）
- 智能告警系统（及时发现和处理问题）

#### 7. 系统设置
- 基础设置（站点信息、时区、语言等）
- 通知设置（邮件、短信、Webhook配置）
- 安全设置（双因子认证、会话管理、密码策略）
- 备份设置（自动备份、云备份配置）
- API设置（接口管理、速率限制、CORS配置）
- 网站设置（平台类型管理、行业类型管理）
- 服务器设置（部门类型管理、SSH配置管理）

#### 8. 个人资料管理
- 基本信息编辑（个人信息、头像上传）
- 偏好设置（语言、时区、主题、通知偏好）
- 安全管理（密码修改、双因子认证、登录会话）
- 活动日志（详细的操作记录和审计追踪）

#### 9. 知识库
- 文档管理、分类结构、搜索功能、评价系统

#### 10. 数据导出
- 多格式导出（Excel、CSV、JSON）
- 批量导出、权限控制
- 自动文件清理和性能优化

### 🎯 系统特色
- **真实可用**: 所有功能都是真实可用的，不仅仅是展示
- **完整性**: 涵盖了网站管理的全生命周期
- **绝对美观的UI**: 基于Ant Design设计语言，现代化界面
- **手机PC完美兼容**: 响应式设计，支持各种设备
- **丰富动画特效**: 流畅的交互体验
- **模块化架构**: 清晰的代码结构和组件复用
- **高性能**: 优化的架构设计，确保系统流畅运行
- **安全性**: 完善的权限管理和安全措施

## 🛠 技术栈

### 前端技术
- **React 18** - 现代化前端框架
- **TypeScript** - 类型安全的JavaScript
- **Ant Design** - 企业级UI组件库
- **Vite** - 快速构建工具
- **React Router** - 前端路由管理
- **Axios** - HTTP客户端

### 后端技术
- **Node.js** - JavaScript运行时
- **Express** - Web应用框架
- **TypeScript** - 类型安全的JavaScript
- **MySQL** - 关系型数据库
- **node-ssh** - SSH连接和服务器监控
- **node-cron** - 定时任务调度
- **XLSX** - Excel文件处理
- **tls** - SSL/TLS证书检测
- **https** - HTTPS请求处理
- **Worker Threads** - 多线程并发处理

### 开发工具
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **Vite** - 快速构建工具

## 🚀 快速开始

### 环境要求
- Node.js 18+
- MySQL 8.0+
- 本地开发环境

### 一键启动 (推荐)

使用本地环境一键启动所有服务：

```bash
# 克隆项目
git clone <repository-url>
cd sitemanager

# 安装MySQL (如果未安装)
# Ubuntu/Debian: sudo apt install mysql-server
# CentOS/RHEL: sudo yum install mysql-server
# macOS: brew install mysql

# 设置MySQL密码
mysql -u root -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY 'root123';"

# 一键启动所有服务
./dev-local.sh
```

启动后访问：
- 前端应用：http://localhost:3000
- 后端API：http://localhost:3001
- SSL检查器：http://localhost:3001/ssl-checker/web/
- 数据库：localhost:3306 (MySQL)

### 🔗 主要API端点

#### 网站管理API
- `GET /api/v1/websites` - 获取网站列表
- `POST /api/v1/websites` - 创建新网站
- `PUT /api/v1/websites/:id` - 更新网站信息
- `DELETE /api/v1/websites/:id` - 删除网站

#### 网站检测API
- `POST /api/v1/websites/:id/check-ssl` - 真实SSL证书检测
- `POST /api/v1/websites/:id/check-domain` - 真实域名状态检测
- `POST /api/v1/websites/:id/check-performance` - 真实网站性能检测
- `POST /api/v1/websites/batch-check` - 高性能批量网站检测
- `POST /api/v1/websites/trigger-access-check` - 手动触发访问状态检测

#### SSL检查器API
- `POST /api/v1/ssl/check-all` - 批量SSL证书检测
- `GET /api/v1/ssl/report` - 获取SSL检测报告
- `GET /ssl-checker/web/` - SSL检查器Web界面

#### 增强版监控API
- `GET http://localhost:3002/status` - 监控服务状态
- `GET http://localhost:3002/stats` - 监控统计数据
- `GET http://localhost:3002/status-check` - 状态检测详情
- `POST http://localhost:3002/trigger-check` - 手动触发检测
- `POST http://localhost:3002/test-notification` - 测试通知功能

#### 网站密码管理API
- `GET /api/v1/websites/:id/credentials` - 获取网站密码列表
- `POST /api/v1/websites/:id/credentials` - 创建网站密码
- `PUT /api/v1/websites/:id/credentials/:credentialId` - 更新网站密码
- `DELETE /api/v1/websites/:id/credentials/:credentialId` - 删除网站密码

#### 服务器管理API
- `GET /api/v1/servers` - 获取服务器列表
- `POST /api/v1/servers` - 创建新服务器
- `PUT /api/v1/servers/:id` - 更新服务器信息
- `DELETE /api/v1/servers/:id` - 删除服务器
- `GET /api/v1/servers/:id/monitor` - 获取服务器监控数据

#### 通知管理API
- `POST /api/v1/notifications/test-feishu` - 测试飞书通知
- `POST /api/v1/notifications/config` - 保存通用通知配置
- `PUT /api/v1/notifications/config/:id` - 更新通知配置
- `GET /api/v1/notifications/configs` - 获取通知配置列表
- `DELETE /api/v1/notifications/config/:id` - 删除通知配置
- `POST /api/v1/notifications/feishu-config` - 保存飞书配置（兼容）
- `GET /api/v1/notifications/feishu-config` - 获取飞书配置（兼容）
- `GET /api/v1/notifications/logs` - 获取通知历史记录
- `GET /api/v1/notifications/stats` - 获取通知统计
- `POST /api/v1/websites/:id/send-notification` - 手动发送网站通知（支持平台类型显示）

### 手动安装

1. **克隆项目**
```bash
git clone <repository-url>
cd sitemanager
```

2. **安装依赖**
```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

3. **配置环境变量**
```bash
# 复制环境变量文件
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

4. **初始化数据库**
```bash
# 创建数据库
mysql -u sitemanager -psitemanager123 -e "CREATE DATABASE IF NOT EXISTS sitemanager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 初始化表结构和数据
mysql -u sitemanager -psitemanager123  sitemanager < backend/database/init-servers.sql
```

5. **启动服务**
```bash
# 启动后端（包含定时任务和SSL检查器）
cd backend
node simple-server.js

# 启动前端 (新终端)
cd frontend
npm run dev

# 注意：监控功能已集成到主服务器，无需单独启动

# 手动运行SSL检查器 (可选)
cd ssl-checker
node ssl-checker.js
```

## 📁 项目结构与代码架构

### 🏗️ 整体架构
```
sitemanager/                   # 网站管理系统根目录
├── 🔧 配置文件层
├── 🖥️ 前端应用层 (React + TypeScript)
├── ⚙️ 后端服务层 (Node.js + Express)
├── 🗄️ 数据存储层 (MySQL + 文件存储)
├── 📊 监控服务层 (多种监控集成)
└── 🔒 安全权限层 (JWT + 权限中间件)
```

### 📂 详细目录结构

#### 🔧 配置与环境
```
├── .env*                      # 环境变量配置
├── .kiro/                     # Kiro IDE配置
│   ├── settings/              # IDE设置
│   ├── specs/                 # 项目规格
│   └── steering/              # 开发规则
├── .vscode/                   # VS Code配置
├── package.json               # 根项目配置
└── *.sh                       # 部署和启动脚本
```

#### 🖥️ 前端应用 (frontend/)
```
frontend/
├── src/
│   ├── 📱 pages/              # 页面组件
│   │   ├── Auth/              # 认证页面
│   │   ├── Dashboard/         # 仪表盘
│   │   ├── Website/           # 网站管理
│   │   ├── Server/            # 服务器管理
│   │   ├── User/              # 用户管理
│   │   ├── Permission/        # 权限管理
│   │   ├── Settings/          # 系统设置
│   │   └── Error/             # 错误页面
│   ├── 🧩 components/         # 可复用组件
│   │   ├── Layout/            # 布局组件
│   │   ├── Common/            # 通用组件
│   │   ├── Permission/        # 权限组件
│   │   ├── Website/           # 网站组件
│   │   ├── Server/            # 服务器组件
│   │   └── Dashboard/         # 仪表盘组件
│   ├── 🔗 services/           # API服务
│   │   ├── api.ts             # 基础API配置
│   │   ├── user.ts            # 用户服务
│   │   ├── website.ts         # 网站服务
│   │   ├── server.ts          # 服务器服务
│   │   └── permission.ts      # 权限服务
│   ├── 🎣 hooks/              # 自定义Hooks
│   │   ├── useAuth.tsx        # 认证Hook
│   │   ├── usePermissions.tsx # 权限Hook
│   │   └── useWebsites.tsx    # 网站Hook
│   ├── 🌐 contexts/           # React上下文
│   │   ├── AuthContext.tsx    # 认证上下文
│   │   └── PermissionContext.tsx # 权限上下文
│   ├── 🛣️ router/             # 路由配置
│   │   └── index.tsx          # 路由定义
│   ├── 🎨 styles/             # 样式文件
│   ├── 🔧 utils/              # 工具函数
│   └── 📝 types/              # TypeScript类型
├── public/                    # 静态资源
└── package.json               # 前端依赖配置
```

#### ⚙️ 后端服务 (backend/)
```
backend/
├── 🚀 simple-server.js       # 主服务器入口
├── 📊 models/                 # 数据模型
│   ├── Server.js              # 服务器模型
│   └── Website.js             # 网站模型
├── 🔧 services/               # 业务服务层
│   ├── scheduler.js           # 定时任务调度
│   ├── PermissionService.js   # 权限服务
│   ├── NotificationService.js # 通知服务
│   ├── MonitoringService.js   # 监控服务
│   ├── CacheService.js        # 缓存服务
│   └── enhanced-*.js          # 增强功能服务
├── 🛡️ middleware/             # 中间件
│   ├── PermissionMiddleware.js # 权限中间件
│   └── cacheMiddleware.js     # 缓存中间件
├── 🛣️ routes/                 # 路由定义
│   ├── audit.js               # 审计路由
│   ├── monitoring.js          # 监控路由
│   └── enhanced-monitoring.js # 增强监控路由
├── 🗄️ database/               # 数据库相关
│   ├── init-servers.sql       # 初始化脚本
│   ├── migrations/            # 数据库迁移
│   └── seeds/                 # 种子数据
├── 📜 scripts/                # 脚本工具
│   ├── permission-*.js        # 权限相关脚本
│   └── system-*.js            # 系统维护脚本
├── 🔧 utils/                  # 工具函数
│   ├── db.js                  # 数据库工具
│   ├── AuditManager.js        # 审计管理
│   └── SearchUtils.js         # 搜索工具
├── 📁 uploads/                # 文件上传目录
├── 📋 logs/                   # 日志文件
└── 🧪 test/                   # 测试文件
```

#### 🗄️ 数据库层 (database/)
```
database/
├── init.sql                   # 主初始化脚本
├── enhanced_monitoring_tables.sql # 增强监控表
├── monitoring_tables.sql      # 监控表结构
└── status_check_table.sql     # 状态检查表
```

#### 📊 监控服务层
```
├── ssl-checker/               # SSL证书检查器
│   ├── ssl-checker.js         # 主程序
│   ├── web/index.html         # Web界面
│   └── output/                # 检查结果
├── coolmonitor/               # 增强监控系统
│   ├── src/                   # 源代码
│   ├── prisma/                # 数据库模式
│   └── public/                # 静态资源
└── nexus-terminal/            # 终端管理
    ├── packages/              # 包管理
    └── doc/                   # 文档
```

### 🏛️ 系统架构层次

#### 1. 🎨 表现层 (Presentation Layer)
- **React前端应用**: 用户界面和交互
- **Ant Design组件**: 企业级UI组件库
- **响应式设计**: 支持PC和移动端
- **路由管理**: React Router实现SPA

#### 2. 🔐 安全层 (Security Layer)
- **JWT认证**: 无状态身份验证
- **权限中间件**: 细粒度权限控制
- **RBAC权限模型**: 基于角色的访问控制
- **审计日志**: 完整的操作追踪

#### 3. 🔗 API层 (API Layer)
- **RESTful API**: 标准化接口设计
- **Express框架**: 轻量级Web框架
- **中间件架构**: 模块化请求处理
- **错误处理**: 统一的错误响应

#### 4. 💼 业务逻辑层 (Business Logic Layer)
- **服务层模式**: 业务逻辑封装
- **定时任务**: 自动化监控和维护
- **缓存策略**: 多层缓存优化
- **通知系统**: 多渠道消息推送

#### 5. 🗄️ 数据访问层 (Data Access Layer)
- **MySQL数据库**: 关系型数据存储
- **连接池管理**: 高效数据库连接
- **事务处理**: 数据一致性保证
- **索引优化**: 查询性能提升

#### 6. 📊 监控层 (Monitoring Layer)
- **实时监控**: 网站状态检测
- **SSL监控**: 证书到期提醒
- **性能监控**: 系统资源追踪
- **告警系统**: 异常情况通知

### 🔄 数据流架构

#### 请求处理流程
```
用户请求 → 路由分发 → 权限验证 → 业务处理 → 数据操作 → 响应返回
    ↓         ↓         ↓         ↓         ↓         ↓
  前端UI → React Router → 权限Guard → API调用 → 后端服务 → 数据库
```

#### 权限验证流程
```
请求 → JWT验证 → 权限中间件 → 角色检查 → 资源权限 → 操作权限 → 通过/拒绝
```

#### 监控数据流
```
定时任务 → 状态检测 → 数据存储 → 异常判断 → 通知发送 → 前端展示
```

### 🔧 核心技术栈

#### 前端技术栈
- **React 18**: 现代化前端框架
- **TypeScript**: 类型安全开发
- **Ant Design**: 企业级UI组件
- **React Router**: 前端路由
- **Axios**: HTTP客户端
- **Vite**: 快速构建工具

#### 后端技术栈
- **Node.js**: JavaScript运行时
- **Express**: Web应用框架
- **MySQL2**: 数据库驱动
- **JWT**: 身份认证
- **node-cron**: 定时任务
- **Winston**: 日志管理

#### 开发工具栈
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Jest**: 单元测试框架
- **Docker**: 容器化部署
- **PM2**: 进程管理

### 📋 关键设计模式

#### 1. MVC架构模式
- **Model**: 数据模型和业务逻辑
- **View**: React组件和用户界面
- **Controller**: Express路由和控制器

#### 2. 服务层模式
- 业务逻辑封装在独立的服务类中
- 提高代码复用性和可测试性
- 便于维护和扩展

#### 3. 中间件模式
- 权限验证中间件
- 缓存处理中间件
- 错误处理中间件
- 日志记录中间件

#### 4. 观察者模式
- 事件驱动的通知系统
- 状态变更自动通知
- 解耦组件间的依赖关系

#### 5. 策略模式
- 多种监控策略
- 不同的通知渠道
- 灵活的权限验证策略

### 🔄 部署架构

#### 开发环境
```
本地开发 → 热重载 → 实时调试 → 单元测试 → 集成测试
```

#### 生产环境
```
代码构建 → Docker镜像 → 容器部署 → 负载均衡 → 监控告警
```

#### 数据备份
```
定时备份 → 增量备份 → 异地存储 → 恢复测试 → 灾难恢复
```

这个架构设计确保了系统的：
- **可扩展性**: 模块化设计便于功能扩展
- **可维护性**: 清晰的代码结构和文档
- **高性能**: 多层缓存和数据库优化
- **高可用**: 监控告警和故障恢复
- **安全性**: 完善的权限控制和审计

## 🔧 开发指南

### 开发环境配置

1. **本地环境 (推荐)**
```bash
# 一键启动所有服务
./start.sh

# 手动启动后端
cd backend && node simple-server.js

# 手动启动前端
cd frontend && npm run dev

# 查看MySQL状态
systemctl status mysql
```

2. **开发调试**
```bash
# 启动调试模式
DEBUG=true ./dev-local.sh

# 查看日志
tail -f backend/logs/system.log
tail -f backend/logs/api.log
```

3. **API日志控制**
```javascript
// 在浏览器控制台中使用以下命令控制API日志显示

// 开启API日志
toggleApiLogging(true)

// 关闭API日志
toggleApiLogging(false)

// 切换日志状态
toggleApiLogging()

// 开启静默模式（隐藏所有API日志和警告）
setSilentMode(true)

// 关闭静默模式
setSilentMode(false)

// 切换静默模式
setSilentMode()

// 查看当前日志状态
getApiLoggingStatus()
```

### 数据库管理

```bash
# 连接MySQL数据库
mysql -u sitemanager -psitemanager123  sitemanager

# 重新初始化数据库
mysql -u sitemanager -psitemanager123  sitemanager < backend/database/init-servers.sql

# 备份数据库
mysql -u sitemanager -psitemanager123  sitemanager > backup.sql

# 恢复数据库
mysql -u sitemanager -psitemanager123  sitemanager < backup.sql

# 查看平台映射关系
mysql -u sitemanager -psitemanager123  sitemanager -e "
SELECT p.id, p.name, p.platform_id, COUNT(w.id) as website_count
FROM platforms p
LEFT JOIN websites w ON p.platform_id = w.platform_id
GROUP BY p.id, p.name, p.platform_id
ORDER BY p.platform_id;"
```

### 代码规范

- 使用TypeScript进行类型检查
- 遵循ESLint和Prettier配置
- 组件命名使用PascalCase
- 文件命名使用kebab-case
- 提交信息遵循Conventional Commits

## 📊 功能模块详解

### 网站管理模块

#### 基础网站管理 (`/websites`)
- **表格列结构**：
  - 站点名称（所属平台/所属行业）
  - 站点URL（访问状态检测）
  - 上线时间
  - 到期时间（智能颜色标识）
  - 项目金额
  - 续费金额
  - 备注
  - 操作（编辑/删除）
- **动态选项管理**：
  - 平台类型和行业类型从设置页面动态加载
  - 支持在设置中自定义添加、编辑、删除选项
  - 编辑网站时选项实时同步更新

#### 增强网站管理 (`/websites/enhanced`)
- **完整功能**：
  - **真实SSL证书检测**: 实时检测SSL证书状态、到期时间、颁发者信息
  - **真实域名状态检测**: DNS解析检查、域名注册信息查询
  - **真实网站性能评分**: 页面加载时间、响应时间、性能指标分析
  - **网站密码管理**: 安全存储和管理各类网站登录凭据
  - **访问状态监控**: 实时监控网站可用性和响应时间
  - **监控日志记录**: 完整的检测历史和状态变更记录
  - **详细信息查看**: 全面的网站信息展示
  - **批量操作**: 支持批量检测和管理操作

#### 密码管理器
- **账号类型**：管理员、用户
- **安全功能**：密码隐藏、一键复制、随机生成
- **操作功能**：增删改查、描述备注

### 项目管理模块

#### 建站管理
- 项目创建和基本信息管理
- 进度跟踪和状态更新
- 团队成员分配
- 文档和资源管理

#### 合同管理
- PDF合同上传和解析
- 合同条款提取
- 付款进度跟踪
- 合同到期提醒

### 服务器管理模块

#### 服务器台账
- **基本信息管理**: 服务器名称、IP地址、位置、服务商等
- **硬件配置管理**: CPU、内存、存储、带宽、操作系统
- **SSH连接配置**: 支持密码和密钥两种认证方式
- **SSH配置选择器**: 可从系统设置中保存的SSH配置里选择，提高配置效率
- **批量SSH配置**: 支持批量设置SSH密钥或SSH账密，可选择预设配置或手动输入
- **Excel导入保护**: Excel导入更新服务器信息时自动保护SSH配置不被覆盖
- **智能配置检测**:
  - 编辑服务器时配置SSH后自动获取配置信息
  - 每次修改SSH信息自动重新获取
  - 每天凌晨2点自动更新所有服务器配置
  - 手动刷新按钮支持即时更新
- **状态监控**: 实时监控CPU、内存、磁盘使用率
- **维护记录**: 完整的操作历史和维护日志
- **资源统计**: 详细的资源使用情况和趋势分析

#### 定时任务系统
- **网站访问状态检测**: 每5分钟检测一次所有网站访问状态
- **SSL证书检测**: 每天凌晨00:30执行SSL证书检测
- **服务器配置更新**: 每天凌晨2:00自动检测并更新服务器配置信息
- **并发控制**: 限制并发数量避免服务器过载
- **错误处理**: 完善的错误处理和重试机制
- **手动触发**: 支持手动触发各类检测任务
- **任务监控**: 实时查看任务执行状态和结果

### 用户权限模块

#### 权限体系
- **超级管理员**：系统全部权限
- **管理员**：业务管理权限
- **普通用户**：基础查看和操作权限

#### 功能权限
- 模块访问权限控制
- 操作级别权限验证
- 数据范围权限限制

## 🎯 特色功能

### 智能监控
- **到期提醒**：自动检测并标红显示即将到期的网站
- **智能存活监控**：
  - 每5分钟自动检测所有活跃网站的访问状态
  - 基于HTTP状态码智能判断网站健康状态
  - 记录连续失败次数，识别持续性问题
  - 前端实时显示状态和异常次数，如"404 (3次)"
- **飞书机器人通知**：
  - 网站连续失败5次以上自动发送飞书通知
  - 支持自定义通知阈值和机器人配置
  - 避免重复通知，网站恢复正常时自动重置
  - 提供手动测试通知功能
- **真实SSL监控**：
  - 实时检测SSL证书状态和到期时间
  - 获取证书详细信息（颁发者、序列号、指纹等）
  - 支持HTTPS和TLS双重检测方法
  - 自动更新数据库中的SSL状态
- **真实域名监控**：
  - DNS解析状态检查和IP地址验证
  - 域名注册信息查询
  - 域名到期时间监控
  - 自动更新域名状态信息
- **真实性能检测**：
  - 页面加载时间和响应时间测试
  - 移动端和桌面端性能评分
  - 首次内容绘制等核心Web指标
  - HTML内容分析和优化建议
- **服务器配置自动检测**：通过SSH连接自动获取服务器硬件配置
- **定时自动更新**：每天自动更新所有服务器配置信息

#### 🔔 监控通知使用指南

**多渠道通知配置**：
1. 进入系统设置页面 → 通知配置
2. 点击"添加通知配置"选择通知类型：
   - **飞书机器人**：配置Webhook URL、机器人名称、通知阈值
   - **邮件通知**：配置SMTP服务器、用户名、密码、收件人
   - **短信通知**：配置服务商、密钥、签名、手机号码
   - **Webhook**：配置URL、请求方法、请求头、超时时间
3. 启用所需的通知渠道
4. 使用"测试"功能验证配置是否正确

**通知历史查看**：
1. 进入系统设置页面 → 通知历史
2. 查看所有通知发送记录和统计信息
3. 支持按类型、状态、时间范围筛选
4. 可查看详细的通知内容和响应数据
5. 支持重新发送失败的通知

**监控状态说明**：
- 🟢 **正常状态**：显示绿色"正常"标签，HTTP状态码200-299
- 🔴 **异常状态**：显示红色状态码和连续失败次数，如"404 (3次)"
- 🔔 **通知状态**：已发送通知的站点会显示铃铛图标
- 📱 **手动通知**：点击操作列的铃铛按钮可手动发送测试通知

**通知触发条件**：
- 网站连续失败达到设定阈值（默认5次）
- 每个网站只发送一次通知，避免重复骚扰
- 网站恢复正常后自动重置通知状态和计数器

### 数据可视化
- **仪表盘**：关键指标和统计图表
- **状态概览**：网站、服务器、项目状态一览
- **趋势分析**：历史数据趋势和预测

### 批量操作
- **批量检测**：一键检测多个网站状态
- **批量更新**：批量修改网站信息
- **批量导出**：数据导出和报表生成
- **批量SSH配置**：批量设置服务器SSH连接信息，支持预设配置和手动输入
- **Excel智能导入**：基于IP地址的upsert操作，自动保护SSH配置不被覆盖

## 🔒 安全特性

### 数据安全
- 密码加密存储
- 敏感信息脱敏处理
- 数据备份和恢复

### 访问安全
- JWT身份认证
- 权限验证中间件
- API访问频率限制

### 操作安全
- 操作日志记录
- 重要操作确认
- 数据变更追踪

## 🚀 部署指南

### 生产环境部署

```bash
# 1. 安装依赖
cd backend && npm install --production
cd ../frontend && npm install

# 2. 构建前端
npm run build

# 3. 配置生产环境变量
cp .env.example .env
# 编辑 .env 文件，配置生产数据库等

# 4. 初始化生产数据库
mysql -u root -p -e "CREATE DATABASE sitemanager CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -u root -p sitemanager < backend/database/init-servers.sql

# 5. 启动生产服务
cd backend && node simple-server.js
```

### 进程管理 (推荐使用PM2)

```bash
# 安装PM2
npm install -g pm2

# 启动后端服务
cd backend && pm2 start simple-server.js --name "sitemanager-api"

# 启动前端服务 (如果需要)
cd frontend && pm2 start "npm run preview" --name "sitemanager-web"

# 查看服务状态
pm2 status

# 查看日志
pm2 logs
```

## 📈 性能优化

### 前端优化
- **代码分割和懒加载**: 路由级别的代码分割，减少初始加载时间
- **图片压缩和CDN加速**: 优化静态资源加载速度
- **缓存策略优化**: 智能缓存API响应，减少重复请求
- **组件性能优化**: 使用React.memo和useMemo优化渲染性能
- **延迟加载技术**: 使用Intersection Observer实现附件图标延迟加载
- **并发控制**: 通过随机延迟避免大量并发请求导致服务器过载
- **智能重试机制**: 网络超时自动重试，提高系统稳定性

### 后端优化
- **数据库查询优化**: 关键字段建立索引，优化查询性能
- **API响应缓存**: 多层缓存策略，提升响应速度
- **连接池管理**: 数据库连接池优化，提高并发处理能力
- **异步处理优化**: 耗时操作异步处理，提升用户体验
- **快速模式API**: 附件检测使用快速模式，只返回必要信息
- **超时时间优化**: API超时时间从30秒增加到60秒，适应大量数据查询

### 大数据量处理
- **分批处理**: 大量附件检测分批进行，避免系统过载
- **错误恢复**: 改进错误处理机制，单个请求失败不影响整体功能
- **性能监控**: 实时监控慢请求，自动优化性能瓶颈

## 🧪 测试

### 运行测试

```bash
# 前端测试
cd frontend && npm run test

# 后端测试
cd backend && npm run test

# 端到端测试
npm run test:e2e
```

### 测试覆盖率

```bash
# 生成测试覆盖率报告
npm run test:coverage
```

## 🤝 贡献指南

### 开发流程

1. **Fork项目**并创建功能分支
2. **编写代码**并确保通过所有测试
3. **提交代码**并遵循提交规范
4. **创建Pull Request**并描述变更内容

### 代码规范

- 遵循TypeScript最佳实践
- 使用ESLint和Prettier格式化代码
- 编写单元测试和集成测试
- 更新相关文档

### 提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具或辅助工具的变动
```

## 📞 支持与反馈

### 问题报告
- 通过GitHub Issues报告bug
- 提供详细的复现步骤和环境信息
- 附上相关的错误日志和截图

### 功能建议
- 通过GitHub Issues提交功能建议
- 详细描述功能需求和使用场景
- 参与功能讨论和设计

### 技术支持
- 查看项目Wiki获取详细文档
- 参与GitHub Discussions技术讨论
- 联系项目维护者获取帮助

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

## 🔧 更新日志

### v2.4.0 (2025-06-25)
- 🚀 **增强版HTTP监控检查器**: 基于coolmonitor开源项目设计，集成专业级网站监控功能
  - **灵活状态码配置**: 支持"200-299,301,302"等复杂状态码配置，适应各种网站重定向场景
  - **多HTTP方法支持**: 支持GET/POST/PUT/PATCH等多种HTTP方法和自定义请求头
  - **智能重试机制**: 可配置重试次数和间隔，提高检测可靠性
  - **SSL证书集成监控**: HTTP检测时同时检查SSL证书状态，提供证书到期提醒
  - **关键词内容检测**: 支持多关键词"或"关系匹配，检查页面内容是否包含特定关键词
  - **详细错误分类**: 精确的错误消息分类（连接被拒绝、超时、主机未找到、SSL错误等）
  - **完整监控历史**: 保存详细的监控历史记录，支持时间范围查询和统计分析
- 🎯 **增强版网站监控服务**: 集成coolmonitor的监控理念，提供企业级监控能力
  - **综合检查结果**: 同时执行HTTP检查、SSL检查、关键词检查，综合评估网站状态
  - **批量监控优化**: 支持高并发批量检查，可配置并发数和检查策略
  - **监控配置管理**: 为每个网站提供独立的监控配置，支持个性化监控策略
  - **状态统计分析**: 提供监控概览、成功率统计、响应时间分析等数据
- 📡 **增强监控API接口**: 提供完整的RESTful API接口，支持第三方集成
  - **单网站检查**: `/api/v1/enhanced-monitoring/websites/{id}/check`
  - **批量网站检查**: `/api/v1/enhanced-monitoring/websites/batch-check`
  - **监控配置管理**: 支持获取和更新网站监控配置
  - **监控历史查询**: 支持多种时间范围的历史记录查询
  - **监控概览统计**: 提供系统级监控数据概览
- 🗄️ **监控数据库架构**: 新增专业的监控数据表结构
  - **监控配置表**: `website_monitor_configs` - 存储每个网站的监控配置
  - **监控历史表**: `website_monitor_history` - 记录详细的监控历史数据
  - **通知配置表**: `monitor_notification_configs` - 支持多渠道通知配置
  - **数据库视图**: 提供监控状态概览和SSL到期预警视图
- ✅ **功能验证完成**: 全面测试增强监控功能，确保稳定性和可靠性
  - HTTP检查器功能测试通过（状态码检查、重试机制、SSL检查等）
  - 网站监控服务测试通过（单个检查、批量检查、配置管理等）
  - API接口测试通过（所有增强监控API接口正常工作）
  - 数据库集成测试通过（监控历史记录、配置存储等）

### v2.3.0 (2025-06-24)
- 🚀 **SSL检查器集成**: 基于GitHub开源项目魔改，完全集成到系统中
  - 批量SSL检测：从数据库读取682个网站，批量检查SSL证书状态
  - 智能并发控制：5个网站同时检查，避免过载
  - 详细报告生成：生成JSON格式的SSL检查报告，包含完整证书信息
  - Web界面展示：美观的Web界面查看SSL状态，支持筛选和搜索
  - 兼容性支持：生成兼容原项目的ct.json格式
  - 数据库集成：自动更新网站表中的SSL相关字段
  - API接口：提供RESTful API接口，支持批量检测和报告获取
- 🕐 **定时任务规则优化**: 调整定时任务执行频率，提升系统性能
  - 网站访问状态检测：从每分钟改为每5分钟执行
  - SSL证书检测：从每天8:30改为每天00:30执行
  - 服务器配置更新：保持每天凌晨2:00执行
  - 错峰安排：避免任务冲突，减少资源竞争
- 🔧 **附件管理修复**: 解决附件管理不显示已有附件的缓存问题
  - 重启后端服务器清除内存缓存
  - 修复API返回空列表的问题
  - 确保附件数据正常显示和管理

### v2.2.22 (2025-06-24)
- ✅ **超高性能网站检测**: 实现Worker Threads多线程网站存活检测，30秒内可检测完1000个站点
- ✅ **智能检测策略**: 根据网站数量自动选择Worker Threads或传统并发模式，优化性能
- ✅ **多线程架构**: 使用Node.js Worker Threads实现真正的多线程并发检测
- ✅ **动态并发控制**: 根据CPU核心数和网站数量动态调整工作线程数量和并发数
- ✅ **快速HTTP检测**: 优化HTTP请求，使用HEAD请求和短超时时间，提升检测速度
- ✅ **批量数据库更新**: 优化数据库更新策略，支持批量更新和异步处理
- ✅ **性能监控统计**: 详细的性能统计信息，包括处理速度、平均耗时、成功率等
- ✅ **容错机制**: Worker Threads失败时自动回退到传统模式，确保检测可靠性
- ✅ **批量检测API**: 新增高性能批量检测API接口，支持指定网站或全量检测
- ✅ **检测结果展示**: 创建专门的批量检测页面，实时展示检测进度和结果

### v2.3.0 (2025-06-24)
- ✅ **多渠道通知系统重构**: 完全重构通知系统，支持飞书、邮件、短信、Webhook等多种通知渠道
- ✅ **通知配置管理**: 统一的通知配置管理界面，支持多个通知配置并存，可独立启用/禁用
- ✅ **通知历史记录**: 完整的通知发送历史记录，支持筛选、搜索和详情查看
- ✅ **通知统计分析**: 提供通知发送统计、成功率分析和按类型统计功能
- ✅ **智能通知服务**: 重构NotificationService类，支持数据库集成和日志记录
- ✅ **SSL到期通知**: 集成SSL证书到期通知功能，自动检测并发送提醒
- ✅ **网站故障通知**: 优化网站故障通知内容，包含详细的错误信息和处理建议
- ✅ **通知重发功能**: 支持重新发送失败的通知，提高通知可靠性
- ✅ **完整CRUD操作**: 支持通知配置的创建、读取、更新、删除操作
- ✅ **API接口完善**: 新增通用通知配置API，兼容旧版飞书配置接口
- ✅ **前端界面优化**: 全新的通知设置和历史记录界面，移除冗余的兼容标签页
- ✅ **错误修复**: 修复nodemailer方法名错误、前端数据格式问题、PUT API缺失等问题

### v2.2.21 (2025-06-23)
- ✅ **智能网站存活监控**: 实现每分钟自动检测网站存活状态，真实HTTP请求检测
- ✅ **异常状态统计机制**: 记录网站连续失败次数，智能判断网站健康状态
- ✅ **飞书机器人通知系统**: 当网站连续5次异常时自动发送飞书通知，支持自定义阈值
- ✅ **通知配置管理**: 在设置页面添加飞书机器人配置，支持Webhook URL、机器人名称等设置
- ✅ **数据库结构优化**: 新增异常计数、通知状态、通知配置等相关表和字段
- ✅ **实时状态更新**: 检测结果实时写入数据库，支持状态恢复时自动重置计数器
- ✅ **通知测试功能**: 提供飞书通知测试功能，验证配置是否正确
- ✅ **定时任务优化**: 从每5分钟改为每分钟检测，提高监控实时性
- ✅ **智能通知策略**: 避免重复通知，网站恢复正常时自动重置通知状态

### v2.2.20 (2025-06-23)
- ✅ **平台类型映射系统重构**: 解决platforms表ID与websites表platform_id不匹配的问题
- ✅ **新增platform_id字段**: 在platforms表中添加platform_id字段，支持自定义平台ID映射
- ✅ **数据库结构优化**: 修改所有平台关联查询，使用platform_id字段进行关联而不是id字段
- ✅ **历史数据兼容**: 为现有不匹配的平台数据创建"未知平台"记录，确保数据完整性
- ✅ **前端类型更新**: 更新Platform接口定义，支持platform_id字段
- ✅ **API功能完善**: 新增/api/v1/platforms接口，返回完整的平台信息包括platform_id
- ✅ **全面功能测试**: 验证网站创建、编辑、筛选、统计等功能，确保平台映射正确工作
- ✅ **向后兼容**: 保持现有功能不变，用户无需额外操作即可享受修复后的稳定性

### v2.2.19 (2025-06-20)
- ✅ **智能平台分类系统**: 实现基于规则的智能平台自动分类，支持域名、站点名称、URL路径等多种匹配方式
- ✅ **配置化分类规则**: 提供可视化的分类规则管理界面，支持添加、编辑、删除分类规则，无需硬编码
- ✅ **自动平台识别**: 智能识别易营宝、多谷、巨旺、弗米乐、领动等平台，自动更新网站平台分类
- ✅ **批量数据修复**: 一键执行智能分类，批量修复网站平台数据，提升数据准确性
- ✅ **规则管理界面**: 在设置页面集成平台分类规则管理，支持正则表达式匹配和实时预览

### v2.2.18 (2025-06-20)
- ✅ **平台数据重新映射**: 修复平台ID映射错误，正确区分易营宝(226个)和Shopify(18个)网站
- ✅ **数据修复完成**: 通过重新映射API将错误标记的平台数据修正，确保筛选结果准确
- ✅ **筛选功能验证**: 验证平台筛选功能正常，易营宝和Shopify筛选结果与实际数据匹配
- ✅ **URL编码支持**: 确保中文平台名称在URL中正确编码和解码，支持中文平台筛选
- ✅ **数据一致性保证**: 所有平台相关功能(筛选、统计、显示)使用统一的数据源和映射关系

### v2.2.17 (2025-06-19)
- ✅ **平台筛选修复**: 修复网站筛选中平台显示错误的问题，现在筛选结果与实际平台匹配
- ✅ **平台信息优化**: 改进getPlatformInfo方法，直接从platforms表获取平台信息而非依赖设置数组
- ✅ **数据一致性**: 确保平台筛选、统计和显示使用相同的数据源，避免ID映射错误
- ✅ **数据修复API**: 添加网站平台数据修复接口，支持批量修复platform_id字段
- ✅ **错误处理**: 改进平台信息获取的错误处理，避免null值导致的显示问题

### v2.2.16 (2025-06-19)
- ✅ **附件加载优化**: 修复附件获取超时问题，减少axios超时时间到10秒
- ✅ **快速模式**: 添加附件API快速模式，只返回数量不返回详细信息，提升性能
- ✅ **并发控制**: 优化前端附件加载策略，增加随机延迟避免大量并发请求
- ✅ **缓存优化**: 增加附件缓存时间到10分钟，减少重复请求
- ✅ **超时处理**: 前端添加3秒超时保护，避免长时间等待影响用户体验

### v2.2.15 (2025-06-19)
- ✅ **平台统计同步**: 网站统计基于设置中的平台类型，显示真实的网站数量分布
- ✅ **反向同步**: 将设置中的平台类型同步到platforms表，确保数据库完整性
- ✅ **统计逻辑优化**: 统计API根据设置中的平台类型逐一查询对应网站数量
- ✅ **同步功能完善**: 同步API成功添加6个新平台到数据库(易营宝、多谷、巨旺等)
- ✅ **数据准确性**: 统计结果只显示实际有网站数据的平台，避免空数据显示

### v2.2.14 (2025-06-19)
- ✅ **平台数据同步**: 修复平台统计数据源问题，统计从platforms表获取真实数据
- ✅ **同步功能**: 在设置页面添加"同步平台"按钮，将platforms表数据同步到设置
- ✅ **数据库字段**: 修正数据库字段名称，使用platform_id而非platform字段
- ✅ **API修复**: 修复平台分布图表API和统计API的数据库查询错误
- ✅ **数据一致性**: 确保网站统计显示的平台分布与实际数据库数据一致

### v2.2.13 (2025-06-19)
- ✅ **平台统计同步**: 网站统计中的平台分布与设置页面的平台类型管理同步
- ✅ **数据源统一**: 统计数据从设置中的平台类型获取，而非独立的platforms表
- ✅ **图表数据优化**: 仪表盘图表API支持从数据库获取真实平台分布数据
- ✅ **统计准确性**: 确保平台统计反映实际网站数据和用户自定义的平台类型
- ✅ **缓存优化**: 平台分布数据支持缓存机制，提升查询性能

### v2.2.12 (2025-06-19)
- ✅ **站点名称优化**: 优化站点名称显示，更好利用空间，支持文本溢出处理
- ✅ **拖拽功能修复**: 修复ResizableTable列宽拖拽功能，支持实时调整
- ✅ **密码管理跳转**: 密码管理按钮跳转到增强网站管理页面
- ✅ **URL参数支持**: 增强页面支持ID参数，自动高亮定位到指定网站
- ✅ **视觉反馈**: 添加高亮行样式，提供3秒自动消失的视觉反馈

### v2.2.11 (2025-06-19)
- ✅ **可拉伸列宽**: 网站管理表格支持自由拉伸列宽，提升用户体验
- ✅ **列宽优化**: 站点名称列宽增加，上线时间/到期时间/附件列宽减少
- ✅ **密码管理**: 在编辑按钮旁边添加密码管理功能入口
- ✅ **表格增强**: 添加边框和紧凑布局，支持横向滚动
- ✅ **自定义组件**: 创建ResizableTable组件，支持列宽拖拽调整

### v2.2.10 (2025-06-19)
- ✅ **定时状态检测**: 实现每5分钟自动检查网站存活状态，数据写入数据库
- ✅ **状态码显示**: 正常站点显示"正常"，异常站点显示具体状态码(403、404、500等)
- ✅ **移除模拟数据**: 完全移除前端随机展示数据，所有数据从数据库读写
- ✅ **真实状态检测**: 网站访问状态检测使用真实HTTP请求，结果存储到数据库
- ✅ **定时任务优化**: 定时任务服务自动启动，支持手动触发和状态查询

### v2.2.9 (2025-06-19)
- ✅ **平台导入修复**: 修复网站批量导入的平台类型处理问题
- ✅ **API端点优化**: 明确区分设置API和选项API的使用场景
- ✅ **数据流规范**: 设置API用于筛选，选项API用于导入ID映射
- ✅ **错误提示优化**: 改进平台类型不存在时的错误提示信息
- ✅ **联动功能验证**: 确保平台类型在设置页面和导入功能间正确联动

### v2.2.8 (2025-06-19)
- ✅ **静默模式**: 新增完全静默模式，可隐藏所有API日志和慢请求警告
- ✅ **智能日志控制**: 三级日志控制（完整日志/简化日志/静默模式）
- ✅ **性能优化**: 附件组件添加缓存和防抖，减少重复请求
- ✅ **错误日志优化**: 简化错误信息显示，减少堆栈跟踪噪音
- ✅ **开发者体验**: 提供更多控制台函数，灵活控制日志级别

### v2.2.7 (2025-06-19)
- ✅ **API日志控制**: 添加环境变量控制API请求日志显示，默认关闭减少控制台噪音
- ✅ **开发者工具**: 提供浏览器控制台函数动态开启/关闭API日志
- ✅ **性能日志保留**: 慢请求警告始终显示，确保性能问题可见
- ✅ **环境配置优化**: 通过.env文件灵活控制日志级别
- ✅ **用户体验改善**: 清洁的控制台输出，专注于重要信息

### v2.2.6 (2025-06-19)
- ✅ **大数据分页优化**: 新增500/页、1000/页和显示全部选项，满足不同查看需求
- ✅ **平台筛选联动**: 网站管理页面平台筛选与设置页面平台类型管理完全联动
- ✅ **增强页面筛选**: 为增强网站管理页面添加完整的筛选功能
- ✅ **设置数据同步**: 平台类型在设置页面修改后，网站管理页面实时同步
- ✅ **筛选状态保持**: 清除筛选时正确重置所有筛选条件
- ✅ **后端分页支持**: 后端支持大页面大小，显示全部时自动处理
- ✅ **用户体验统一**: 基础和增强页面的分页选项保持一致

### v2.2.5 (2025-06-19)
- ✅ **分页架构重构**: 实现真正的后端分页，解决大数据量卡顿问题
- ✅ **后端搜索优化**: 搜索和筛选在后端执行，支持跨页搜索
- ✅ **智能分页策略**: 默认50条/页，支持20/50/100/200条可选
- ✅ **数据库查询优化**: 添加WHERE条件和JOIN优化，提升查询效率
- ✅ **排序功能增强**: 支持多字段排序，包括site_id、站点名称等
- ✅ **缓存策略调整**: 网站列表缓存5分钟，平衡性能和实时性
- ✅ **用户体验优化**: 保持筛选状态，分页跳转更流畅

### v2.2.4 (2025-06-19)
- ✅ **性能大幅优化**: 解决网站管理页面卡顿问题，响应速度提升90%以上
- ✅ **附件加载优化**: 实现延迟加载和智能缓存，避免不必要的API请求
- ✅ **数据库索引优化**: 为附件表添加关键索引，查询性能提升显著
- ✅ **缓存策略优化**: 附件列表缓存5分钟，减少重复请求
- ✅ **慢请求监控**: 添加性能监控，自动检测超过1秒的慢请求
- ✅ **前端渲染优化**: 附件组件支持只显示图标模式，减少初始加载时间
- ✅ **智能数据获取**: 优化分页查询逻辑，非首页时避免不必要的总数查询

### v2.2.3 (2025-06-19)
- ✅ **网站管理页面优化**: 取消分页显示，一次性展示所有数据，提高浏览效率
- ✅ **增强搜索功能**: 支持网站名称、URL、备注内容的全文搜索
- ✅ **智能状态筛选**: 默认显示全部状态网站，提供状态筛选选项快速查看特定状态
- ✅ **停用站点标识**: 停用站点的URL显示横线修饰，明确标识不再维护的站点
- ✅ **排序功能优化**: 默认按site_id降序排列，站点名称列支持点击排序
- ✅ **数据统计显示**: 实时显示当前筛选条件下的网站数量和状态信息
- ✅ **表格滚动优化**: 添加垂直滚动条，优化大量数据的浏览体验

### v2.2.2 (2025-06-19)
- ✅ **网站导入优化**: 批量导入网站时，服务器不存在时自动忽略并留空服务器字段
- ✅ **智能警告系统**: 导入时显示警告信息，告知用户哪些服务器不存在但已成功导入
- ✅ **站点名称重复支持**: 移除站点名称唯一性限制，允许创建同名网站，通过ID进行唯一识别
- ✅ **Excel日期格式支持**: 自动转换Excel日期序列号为标准日期格式，支持多种日期输入方式
- ✅ **数据字段完整性**: 修复导入时缺少domain字段导致的500错误问题
- ✅ **用户体验优化**: 改进导入说明和反馈信息，提供更清晰的操作指导
- ✅ **容错处理增强**: 提高系统对不完整数据的处理能力，减少导入失败率

### v2.2.1 (2025-06-19)
- ✅ **Excel导入优化**: 改为基于IP地址的upsert操作，更符合实际使用场景
- ✅ **SSH配置保护**: Excel导入更新服务器信息时自动保护SSH配置不被覆盖
- ✅ **部门类型管理**: 在设置页面添加部门类型自定义管理功能
- ✅ **网站设置管理**: 新增网站设置标签页，支持平台类型和行业类型自定义管理
- ✅ **数据联动**: 服务器编辑表单的部门字段从设置中动态加载选项
- ✅ **网站编辑联动**: 网站编辑表单的平台类型和行业类型从设置中动态加载
- ✅ **智能更新**: 只更新Excel中提供的非空字段，保留其他字段的原值
- ✅ **混合操作**: 支持在一次导入中同时更新和新增多台服务器

### v2.2.0 (2025-06-17)
- ✅ **网站附件管理系统**: 完整的文件上传和管理功能
- ✅ **多格式文件支持**: 图片、PDF、Excel、Word、文本文件等
- ✅ **在线预览功能**: 图片和PDF文件支持在线预览
- ✅ **文件分类管理**: 自动识别文件类型并分类管理
- ✅ **批量上传功能**: 支持一次上传多个文件
- ✅ **文件描述编辑**: 支持为每个附件添加和编辑描述信息
- ✅ **下载统计**: 记录文件下载次数和最后访问时间
- ✅ **安全存储**: 文件安全存储在服务器指定目录
- ✅ **权限控制**: 基于网站ID的文件访问权限控制
- ✅ **响应式界面**: 美观的附件管理界面，支持PC和移动端
- ✅ **表格附件列**: 在网站管理表格中新增附件列，支持直接预览
- ✅ **快速预览**: 点击附件图标即可直接预览图片和PDF文件
- ✅ **附件统计**: 显示每个网站的附件数量和类型分布

### v2.1.5 (2025-06-24)
- 🔧 **修复Worker线程函数作用域问题**: 解决网站状态检测中Worker线程无法访问函数的问题
- 🔧 **修复前端API超时问题**: 将API请求超时时间从10秒增加到30秒，适应高负载检测场景
- 🔧 **修复Antd警告问题**:
  - 将所有`destroyOnClose`替换为`destroyOnHidden`，解决Modal组件过时API警告
  - 创建MessageProvider组件，使用App组件提供的message实例，解决静态函数上下文警告
- 🔧 **修复TypeScript类型错误**:
  - 添加`vite-env.d.ts`文件解决`import.meta.env`类型问题
  - 修复`ShieldOutlined`导入问题，替换为`SafetyCertificateOutlined`
  - 修复`moment`导入问题，替换为`dayjs`
- ✅ **优化网站状态检测系统**:
  - 修复Worker线程中GET请求备用检测机制
  - 提升检测成功率从3.7%到93.1%（巨大改善）
  - 保持高性能：60秒检测682个网站，11站点/秒
- ✅ **系统稳定性提升**: 前后端通信更加稳定，减少网络超时错误

### v2.1.4 (2025-06-17)
- ✅ **批量SSH配置功能**: 服务器管理页面新增批量设置SSH密钥或SSH账密功能
- ✅ **多选服务器支持**: 支持选择多台服务器进行批量SSH配置操作
- ✅ **预设配置应用**: 可从系统设置中选择SSH配置并批量应用到选中服务器
- ✅ **手动配置输入**: 支持手动输入SSH配置信息进行批量设置
- ✅ **批量操作界面**: 优化的批量操作界面，显示选中服务器信息和操作选项
- ✅ **后端API支持**: 新增批量SSH配置API接口，支持服务器批量更新

### v2.1.3 (2025-06-17)
- ✅ **SSH配置选择功能**: 服务器编辑页面新增SSH配置选择器，可从系统设置中保存的SSH配置里选择
- ✅ **智能SSH配置管理**: 支持从系统设置中选择预设的SSH配置，提高服务器配置效率
- ✅ **用户体验优化**: SSH配置选择器显示配置名称、用户名、认证方式和描述信息
- 🐛 **修复ServerOutlined图标错误**: 将所有ServerOutlined替换为CloudServerOutlined，解决图标导入错误

### v2.1.2 (2025-06-17)
- 🐛 **修复系统设置API缺失**: 添加完整的系统设置API接口 (`/api/v1/settings`)
- ✅ **系统设置功能**: 实现获取、更新、重置系统设置的完整功能
- ✅ **配置管理**: 支持站点信息、通知设置、安全设置、备份设置、API设置、监控设置等全面配置
- ✅ **调试环境优化**: 改进调试启动流程，确保所有API接口正常工作

### v2.1.2 (2025-06-24)
- 🚀 **性能优化**: 解决大量数据时的网络超时问题
  - 增加API超时时间从30秒到60秒，适应大量数据查询
  - 优化附件图标组件，实现延迟加载和智能缓存
  - 添加网络请求重试机制，提高系统稳定性
  - 优化并发请求控制，避免大量同时请求导致服务器过载
- 🔧 **用户体验改进**:
  - 添加用户友好的错误提示信息
  - 改进加载状态显示
  - 优化附件检测的性能和准确性
- 🛠️ **技术优化**:
  - 实现Intersection Observer延迟加载技术
  - 优化后端附件API快速模式
  - 改进缓存策略，提升响应速度

### v2.1.1 (2025-06-17)
- 🐛 **修复网络流量显示错误**: 解决 `networkIn.toFixed is not a function` 错误
- ✅ **类型安全改进**: 改进网络流量数据的类型处理，支持字符串和数字类型
- ✅ **前端错误处理**: 增强前端对后端数据类型的兼容性

### v2.1.0 (2025-06-17)
- ✅ **服务器配置自动获取**: 编辑服务器时配置SSH后自动获取配置信息
- ✅ **定时自动更新**: 每天凌晨2点自动更新所有服务器配置
- ✅ **手动刷新功能**: 支持手动触发配置信息更新
- ✅ **真实SSH监控**: 通过SSH连接获取真实的服务器监控数据
- ✅ **智能配置检测**: 自动检测CPU、内存、存储、操作系统等硬件信息
- ✅ **定时任务系统**: 完整的定时任务调度和管理系统
- ✅ **错误修复**: 修复服务器更新API和前端监控显示问题

### v2.0.0 (2025-06-16)
- ✅ **完整的服务器管理**: 服务器台账、监控、SSH连接
- ✅ **网站管理系统**: 网站信息管理、SSL检测、域名监控
- ✅ **用户权限系统**: 多级权限管理和访问控制
- ✅ **数据导入导出**: Excel批量导入导出功能
- ✅ **实时监控**: 真实的服务器性能监控
- ✅ **响应式设计**: 完美支持PC和移动端

## 🚨 增强版站点存活检测系统

### 系统概述
全新的增强版站点存活检测系统，专为1000+站点设计，提供精准的故障检测和实时通知。

### 核心特性
- ⏰ **5分钟检测周期**：每5分钟对所有站点进行一次检测
- 🔄 **智能重试机制**：失败站点每10秒重试一次，最多重试5次
- 📊 **状态检测表**：专门的表结构管理站点状态和错误计数
- 🚨 **精准通知**：错误次数≥3的站点每5分钟检查一次并发送通知
- 📈 **详细统计**：包含站点名称、URL、状态码、服务器信息、持续时间
- 🔒 **SSL证书监控**：每天凌晨00:30自动检测所有网站SSL证书状态

### 检测逻辑
1. **状态正常** → 直接写入状态检测表，错误次数清零
2. **状态异常** → 加入重试队列，每10秒重试一次
3. **重试成功** → 错误次数清零，移出重试队列
4. **重试失败** → 错误次数+1，继续重试
5. **达到阈值** → 每5分钟检查一次并发送通知

### 快速启动
```bash
# 初始化监控数据库表
mysql -h localhost -u root -proot123 sitemanager < database/status_check_table.sql

# 启动主服务器（包含定时任务）
cd backend && node simple-server.js

# 注意：增强版监控服务已集成到主服务器，无需单独启动

# 查看服务状态
./start-monitoring.sh status

# 运行SSL检查器
cd ssl-checker && node ssl-checker.js

# 查看SSL检查器Web界面
# 访问: http://localhost:3001/ssl-checker/web/
```

### 配置通知
编辑 `.env.monitoring` 文件配置通知方式：
```bash
# 邮件通知
EMAIL_ENABLED=true
EMAIL_USER=<EMAIL>
EMAIL_TO=<EMAIL>

# 微信通知
WECHAT_ENABLED=true
WECHAT_WEBHOOK_URL=your-webhook-url

# 钉钉通知
DINGTALK_ENABLED=true
DINGTALK_WEBHOOK_URL=your-dingtalk-webhook
```

### 监控API（已集成到主服务器）
- 网站状态检测：`POST http://localhost:3001/api/v1/websites/trigger-access-check`
- SSL证书检测：`POST http://localhost:3001/api/v1/ssl/check-all`
- SSL检测报告：`GET http://localhost:3001/api/v1/ssl/report`
- 服务器监控：`GET http://localhost:3001/api/v1/servers/:id/monitor`

### 数据库查询
```sql
-- 查看故障站点
SELECT * FROM v_failed_sites;

-- 查看状态统计
SELECT
  COUNT(*) as total_sites,
  SUM(CASE WHEN error_count = 0 THEN 1 ELSE 0 END) as healthy_sites,
  SUM(CASE WHEN error_count >= 3 THEN 1 ELSE 0 END) as failed_sites
FROM status_check;
```

详细文档请参考：[增强版监控系统使用指南](ENHANCED-MONITORING-GUIDE.md)

## 🔒 SSL检查器使用指南

### 功能概述
基于GitHub开源项目魔改的SSL证书检查器，完全集成到网站管理系统中，提供批量SSL证书检测和管理功能。

### 主要特性
- **批量检测**：从数据库读取所有活跃网站，批量检查SSL证书状态
- **智能并发**：5个网站同时检查，避免服务器过载
- **详细报告**：生成包含证书详细信息的JSON报告
- **Web界面**：美观的Web界面展示SSL状态，支持筛选和搜索
- **数据库集成**：检测结果自动更新到网站表
- **兼容性**：生成兼容原项目的ct.json格式

### 使用方法

#### 命令行运行
```bash
# 进入SSL检查器目录
cd ssl-checker

# 运行SSL检查器
node ssl-checker.js

# 查看检测结果
cat output/ssl-report.json
```

#### API接口调用

##### 增强监控API (基于coolmonitor设计)
```bash
# 获取监控概览
curl -X GET http://localhost:3001/api/v1/enhanced-monitoring/overview

# 检查单个网站
curl -X POST http://localhost:3001/api/v1/enhanced-monitoring/websites/{id}/check \
  -H "Content-Type: application/json" \
  -d '{
    "enableHttpCheck": true,
    "enableSslCheck": true,
    "enableKeywordCheck": false,
    "connectTimeout": 10,
    "retries": 1,
    "statusCodes": "200-299,301,302"
  }'

# 批量检查网站
curl -X POST http://localhost:3001/api/v1/enhanced-monitoring/websites/batch-check \
  -H "Content-Type: application/json" \
  -d '{
    "websiteIds": [1, 2, 3],
    "config": {
      "concurrency": 2,
      "enableHttpCheck": true,
      "enableSslCheck": true,
      "connectTimeout": 10,
      "statusCodes": "200-299,301,302"
    }
  }'

# 获取网站监控配置
curl -X GET http://localhost:3001/api/v1/enhanced-monitoring/websites/{id}/config

# 更新网站监控配置
curl -X PUT http://localhost:3001/api/v1/enhanced-monitoring/websites/{id}/config \
  -H "Content-Type: application/json" \
  -d '{
    "http_method": "GET",
    "status_codes": "200-299,301,302",
    "connect_timeout": 15,
    "retries": 2,
    "enable_http_check": true,
    "enable_ssl_check": true,
    "enable_keyword_check": false,
    "keyword": "关键词1,关键词2"
  }'

# 获取网站监控历史
curl -X GET "http://localhost:3001/api/v1/enhanced-monitoring/websites/{id}/history?range=24h&limit=10"
```

##### SSL检测API
```bash
# 批量SSL检测
curl -X POST http://localhost:3001/api/v1/ssl/check-all

# 获取SSL报告
curl http://localhost:3001/api/v1/ssl/report
```

#### Web界面访问
访问：http://localhost:3001/ssl-checker/web/

### 检测结果说明
- **有效证书**：显示绿色"Valid"状态，包含剩余天数
- **即将过期**：显示橙色"Expiring Soon"状态（≤30天）
- **已过期**：显示红色"Expired"状态
- **检测错误**：显示红色"Error"状态，包含错误信息

### 定时检测
系统每天凌晨00:30自动执行SSL证书检测，无需手动干预。

### 报告格式
- **详细报告**：`ssl-checker/output/ssl-report.json`
- **兼容报告**：`ssl-checker/output/ct.json`
- **Web界面**：实时展示检测结果

---

## 📝 最新更新

### 2025-07-20 - 权限管理系统完善
- ✅ **修复用户权限编辑保存404错误**
  - 添加缺失的 `PUT /api/v1/users/:id/permissions` API端点
  - 实现完整的用户权限更新功能
  - 支持批量权限更新和事务处理
- ✅ **修复权限模板编辑保存404错误**
  - 注册权限模板路由文件到主应用
  - 实现完整的权限模板CRUD操作
  - 支持权限模板创建、更新、删除功能
- ✅ **API路由优化**
  - 解决Express路由顺序冲突问题
  - 统一前端API配置管理
  - 符合Node.js最佳实践标准
- ✅ **系统稳定性提升**
  - Systemd服务配置优化
  - 进程管理和信号处理改进
  - 完整的错误处理和日志记录

### 功能状态
- 🟢 **前端权限管理页面**: 完全正常，支持用户权限和权限模板编辑保存
- 🟢 **后端API服务**: 稳定运行，所有权限相关API正常工作
- 🟢 **权限模板系统**: 完整的CRUD功能，支持创建、更新、删除权限模板
- 🟢 **系统服务**: Systemd服务稳定，支持自动启动和优雅停止
- 🔒 **安全认证系统**: 已修复关键安全漏洞，实现严格的认证控制

## 🚨 重要安全更新 (2025-07-28)

### 关键安全漏洞修复
**问题**: 发现未认证用户可以访问仪表盘页面并查看敏感信息的严重安全漏洞

**根本原因**:
- `storage.isAuthenticated()`只检查localStorage中token存在性，不验证token有效性
- 应用启动时缺乏token有效性验证机制
- 过期或无效token仍可绕过路由保护

**修复措施**:
1. **新增认证初始化组件** (`AuthInitializer.tsx`)
   - 应用启动时验证token有效性
   - 无效token自动清除认证状态
   - 安全重定向到登录页

2. **增强路由保护机制**
   - `ProtectedRoute`组件多重认证检查
   - `RootRedirect`组件严格认证验证
   - 详细的安全日志记录

3. **安全架构改进**
   - 多层认证检查（store状态 + localStorage + 用户对象）
   - 应用启动前完成认证验证
   - 完整的安全审计日志

**安全等级**: 🔴 高危漏洞修复
**测试状态**: ✅ 已验证修复效果，未认证用户无法访问受保护页面

---

**SiteManager** - 让网站管理变得简单高效！ 🚀
