#!/bin/bash

# SiteManager部署验证脚本
# 用于验证系统部署是否成功

echo "🔍 SiteManager部署验证开始..."
echo "=================================="

# 检查服务状态
echo "1. 检查systemd服务状态..."
if systemctl is-active --quiet sitemanager; then
    echo "✅ sitemanager服务正在运行"
else
    echo "❌ sitemanager服务未运行"
    exit 1
fi

if systemctl is-enabled --quiet sitemanager; then
    echo "✅ sitemanager服务已启用自动启动"
else
    echo "❌ sitemanager服务未启用自动启动"
fi

# 检查依赖服务
echo ""
echo "2. 检查依赖服务..."
if systemctl is-active --quiet mysql; then
    echo "✅ MySQL服务正在运行"
else
    echo "❌ MySQL服务未运行"
fi

if systemctl is-active --quiet redis-server; then
    echo "✅ Redis服务正在运行"
else
    echo "❌ Redis服务未运行"
fi

# 检查端口监听
echo ""
echo "3. 检查端口监听..."
if netstat -tlnp | grep -q ":3001.*LISTEN"; then
    echo "✅ 后端端口3001正在监听"
else
    echo "❌ 后端端口3001未监听"
fi

if netstat -tlnp | grep -q ":3000.*LISTEN"; then
    echo "✅ 前端端口3000正在监听"
else
    echo "❌ 前端端口3000未监听"
fi

# 检查数据库连接
echo ""
echo "4. 检查数据库连接..."
if mysql -u sitemanager -psitemanager123 sitemanager -e "SELECT 1" &>/dev/null; then
    echo "✅ 数据库连接正常"
else
    echo "❌ 数据库连接失败"
fi

# 检查API响应
echo ""
echo "5. 检查服务响应..."
if curl -s http://localhost:3001/api/v1/websites | grep -q "需要认证令牌"; then
    echo "✅ 后端API服务正常响应"
else
    echo "❌ 后端API服务无响应"
fi

if curl -s http://localhost:3000 | grep -q "html"; then
    echo "✅ 前端服务正常响应"
else
    echo "❌ 前端服务无响应"
fi

# 检查日志
echo ""
echo "6. 检查最近日志..."
echo "最近5条日志："
journalctl -u sitemanager -n 5 --no-pager | tail -5

echo ""
echo "=================================="
echo "🎉 SiteManager部署验证完成！"
echo ""
echo "服务管理命令："
echo "  查看状态: sudo systemctl status sitemanager"
echo "  查看日志: sudo journalctl -u sitemanager -f"
echo "  重启服务: sudo systemctl restart sitemanager"
echo ""
echo "访问地址："
echo "  前端应用: http://localhost:3000"
echo "  后端API: http://localhost:3001"
echo "  SSL检查器: http://localhost:3001/ssl-checker/web/"
