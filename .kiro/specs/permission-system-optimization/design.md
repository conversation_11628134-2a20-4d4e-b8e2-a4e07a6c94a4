# 权限系统优化设计文档

## 概述

本设计文档基于需求分析，详细描述了权限系统优化的技术架构、组件设计、数据模型和实现方案。目标是构建一个统一、安全、高性能的权限管理体系。

## 架构设计

### 整体架构

```mermaid
graph TB
    subgraph "前端层"
        A[React组件] --> B[权限Hook]
        B --> C[权限上下文]
        C --> D[权限API服务]
    end
    
    subgraph "后端层"
        E[Express路由] --> F[权限中间件]
        F --> G[权限服务]
        G --> H[权限缓存]
        G --> I[数据库层]
    end
    
    subgraph "数据层"
        I --> J[用户表]
        I --> K[权限表]
        I --> L[角色权限表]
        I --> M[用户自定义权限表]
        I --> N[审计日志表]
    end
    
    D --> E
    F --> O[审计服务]
    O --> N
```

### 核心设计原则

1. **统一性**：所有权限检查通过统一的中间件和服务
2. **细粒度**：支持模块级、功能级、操作级权限控制
3. **高性能**：多层缓存机制，权限验证响应时间<50ms
4. **安全性**：完整的审计日志和权限变更追踪
5. **可扩展性**：支持动态权限配置和角色模板

## 组件设计

### 1. 统一权限中间件

#### 设计目标
- 为所有API接口提供统一的权限验证
- 支持细粒度权限检查
- 提供标准化的错误响应
- 记录详细的审计日志

#### 核心组件

**PermissionMiddleware.js**
```javascript
class PermissionMiddleware {
  // 主要权限检查中间件
  static requirePermission(requiredPermissions) {
    return async (req, res, next) => {
      // 权限验证逻辑
    }
  }
  
  // 角色权限检查
  static requireRole(requiredRoles) {
    return async (req, res, next) => {
      // 角色验证逻辑
    }
  }
  
  // 资源所有者检查
  static requireOwnership(resourceType) {
    return async (req, res, next) => {
      // 资源所有权验证逻辑
    }
  }
}
```

**权限验证流程**
```mermaid
sequenceDiagram
    participant Client
    participant Middleware
    participant PermissionService
    participant Cache
    participant Database
    participant AuditService
    
    Client->>Middleware: API请求
    Middleware->>PermissionService: 获取用户权限
    PermissionService->>Cache: 查询权限缓存
    
    alt 缓存命中
        Cache-->>PermissionService: 返回权限数据
    else 缓存未命中
        PermissionService->>Database: 查询权限数据
        Database-->>PermissionService: 返回权限数据
        PermissionService->>Cache: 更新缓存
    end
    
    PermissionService-->>Middleware: 权限验证结果
    
    alt 权限验证通过
        Middleware->>AuditService: 记录访问日志
        Middleware-->>Client: 继续处理请求
    else 权限验证失败
        Middleware->>AuditService: 记录拒绝访问日志
        Middleware-->>Client: 返回403错误
    end
```

### 2. 权限服务层

#### PermissionService.js
```javascript
class PermissionService {
  // 获取用户完整权限
  async getUserPermissions(userId) {
    // 合并角色权限和自定义权限
  }
  
  // 检查用户是否拥有特定权限
  async hasPermission(userId, permissionCode) {
    // 权限检查逻辑
  }
  
  // 检查用户是否拥有特定角色
  async hasRole(userId, role) {
    // 角色检查逻辑
  }
  
  // 更新用户权限
  async updateUserPermissions(userId, permissions) {
    // 权限更新逻辑
  }
  
  // 权限继承计算
  async calculateEffectivePermissions(userId) {
    // 计算用户的有效权限（角色权限 + 自定义权限）
  }
}
```

#### RoleService.js
```javascript
class RoleService {
  // 获取角色权限
  async getRolePermissions(role) {
    // 角色权限查询
  }
  
  // 更新角色权限
  async updateRolePermissions(role, permissions) {
    // 角色权限更新
  }
  
  // 获取角色模板
  async getRoleTemplates() {
    // 角色模板查询
  }
}
```

### 3. 权限缓存系统

#### CacheService.js
```javascript
class PermissionCacheService {
  constructor() {
    this.redis = new Redis(config.redis);
    this.localCache = new Map();
    this.cacheTimeout = 300; // 5分钟
  }
  
  // 获取用户权限缓存
  async getUserPermissions(userId) {
    // 多层缓存策略：本地缓存 -> Redis -> 数据库
  }
  
  // 更新权限缓存
  async updateUserPermissions(userId, permissions) {
    // 同时更新本地缓存和Redis
  }
  
  // 清除用户权限缓存
  async clearUserPermissions(userId) {
    // 清除所有层级的缓存
  }
  
  // 批量清除权限缓存
  async clearRolePermissions(role) {
    // 清除该角色所有用户的权限缓存
  }
}
```

#### 缓存策略
```mermaid
graph LR
    A[权限请求] --> B{本地缓存}
    B -->|命中| C[返回权限数据]
    B -->|未命中| D{Redis缓存}
    D -->|命中| E[更新本地缓存]
    E --> C
    D -->|未命中| F[数据库查询]
    F --> G[更新Redis缓存]
    G --> E
```

### 4. 前端权限控制

#### 权限上下文 (PermissionContext.tsx)
```typescript
interface PermissionContextType {
  permissions: string[];
  roles: string[];
  hasPermission: (permission: string) => boolean;
  hasRole: (role: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  refreshPermissions: () => Promise<void>;
}

const PermissionContext = createContext<PermissionContextType | null>(null);
```

#### 权限Hook (usePermissions.ts)
```typescript
export const usePermissions = () => {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error('usePermissions must be used within PermissionProvider');
  }
  return context;
};

export const usePermissionCheck = (requiredPermissions: string[]) => {
  const { hasAllPermissions } = usePermissions();
  return hasAllPermissions(requiredPermissions);
};
```

#### 权限组件 (PermissionGuard.tsx)
```typescript
interface PermissionGuardProps {
  permissions?: string[];
  roles?: string[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  children: React.ReactNode;
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permissions = [],
  roles = [],
  requireAll = true,
  fallback = null,
  children
}) => {
  const { hasPermission, hasRole } = usePermissions();
  
  // 权限检查逻辑
  const hasRequiredPermissions = requireAll 
    ? permissions.every(p => hasPermission(p))
    : permissions.some(p => hasPermission(p));
    
  const hasRequiredRoles = requireAll
    ? roles.every(r => hasRole(r))
    : roles.some(r => hasRole(r));
  
  if (!hasRequiredPermissions || !hasRequiredRoles) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
};
```

### 5. 审计日志系统

#### AuditService.js
```javascript
class AuditService {
  // 记录权限访问日志
  async logPermissionAccess(userId, resource, action, result) {
    const logEntry = {
      userId,
      resource,
      action,
      result, // 'granted' | 'denied'
      timestamp: new Date(),
      ip: req.ip,
      userAgent: req.get('User-Agent')
    };
    
    await this.writeAuditLog(logEntry);
  }
  
  // 记录权限变更日志
  async logPermissionChange(adminId, targetUserId, changes) {
    const logEntry = {
      type: 'permission_change',
      adminId,
      targetUserId,
      changes,
      timestamp: new Date()
    };
    
    await this.writeAuditLog(logEntry);
  }
  
  // 查询审计日志
  async getAuditLogs(filters) {
    // 支持按用户、时间、操作类型等筛选
  }
}
```

## 数据模型

### 数据库表结构

#### 权限表 (permissions)
```sql
CREATE TABLE permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL COMMENT '权限名称',
  code VARCHAR(100) NOT NULL UNIQUE COMMENT '权限代码',
  description TEXT COMMENT '权限描述',
  module VARCHAR(50) NOT NULL COMMENT '所属模块',
  resource VARCHAR(100) COMMENT '资源类型',
  action VARCHAR(50) COMMENT '操作类型',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_module (module),
  INDEX idx_code (code)
);
```

#### 角色权限表 (role_permissions)
```sql
CREATE TABLE role_permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  role ENUM('super_admin', 'admin', 'user') NOT NULL,
  permission_code VARCHAR(100) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (permission_code) REFERENCES permissions(code) ON DELETE CASCADE,
  UNIQUE KEY uk_role_permission (role, permission_code),
  INDEX idx_role (role)
);
```

#### 用户自定义权限表 (user_custom_permissions)
```sql
CREATE TABLE user_custom_permissions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_id INT NOT NULL,
  permission_code VARCHAR(100) NOT NULL,
  granted BOOLEAN DEFAULT TRUE COMMENT '是否授予权限',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_by INT COMMENT '授权人ID',
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_code) REFERENCES permissions(code) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  UNIQUE KEY uk_user_permission (user_id, permission_code),
  INDEX idx_user_id (user_id)
);
```

#### 审计日志表 (audit_logs)
```sql
CREATE TABLE audit_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id INT COMMENT '操作用户ID',
  action VARCHAR(100) NOT NULL COMMENT '操作类型',
  resource VARCHAR(100) COMMENT '资源类型',
  resource_id VARCHAR(100) COMMENT '资源ID',
  result ENUM('granted', 'denied', 'success', 'failure') NOT NULL,
  details JSON COMMENT '详细信息',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_action (action),
  INDEX idx_created_at (created_at),
  INDEX idx_result (result)
);
```

### 权限数据结构

#### 权限代码规范
```
模块.资源.操作
例如：
- user.list.view      // 查看用户列表
- user.profile.edit   // 编辑用户资料
- site.website.create // 创建网站
- site.website.delete // 删除网站
- server.monitor.view // 查看服务器监控
- system.settings.edit // 编辑系统设置
```

#### 权限继承规则
```mermaid
graph TD
    A[用户最终权限] --> B[角色权限]
    A --> C[用户自定义权限]
    
    B --> D[super_admin权限]
    B --> E[admin权限]
    B --> F[user权限]
    
    C --> G[额外授予权限]
    C --> H[撤销权限]
    
    style A fill:#e1f5fe
    style G fill:#c8e6c9
    style H fill:#ffcdd2
```

## 接口设计

### 权限管理API

#### 获取用户权限
```http
GET /api/v1/permissions/user/:userId
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "userId": 123,
    "role": "admin",
    "rolePermissions": ["user.list", "site.create"],
    "customPermissions": ["system.backup"],
    "effectivePermissions": ["user.list", "site.create", "system.backup"],
    "deniedPermissions": []
  }
}
```

#### 更新用户权限
```http
PUT /api/v1/permissions/user/:userId
Authorization: Bearer <token>
Content-Type: application/json

{
  "role": "admin",
  "customPermissions": [
    {"code": "system.backup", "granted": true},
    {"code": "user.delete", "granted": false}
  ]
}

Response:
{
  "success": true,
  "message": "用户权限更新成功",
  "data": {
    "updatedPermissions": ["system.backup"],
    "revokedPermissions": ["user.delete"]
  }
}
```

#### 权限检查API
```http
POST /api/v1/permissions/check
Authorization: Bearer <token>
Content-Type: application/json

{
  "userId": 123,
  "permissions": ["user.create", "site.edit"]
}

Response:
{
  "success": true,
  "data": {
    "hasAllPermissions": false,
    "permissionResults": {
      "user.create": true,
      "site.edit": false
    }
  }
}
```

### 审计日志API

#### 获取审计日志
```http
GET /api/v1/audit/logs?page=1&limit=50&userId=123&action=permission_check
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "logs": [
      {
        "id": 1001,
        "userId": 123,
        "action": "permission_check",
        "resource": "user.create",
        "result": "granted",
        "ipAddress": "*************",
        "createdAt": "2025-07-16T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 150,
      "totalPages": 3
    }
  }
}
```

## 错误处理

### 标准错误响应

#### 权限不足错误
```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_PERMISSIONS",
    "message": "权限不足，无法执行此操作",
    "details": {
      "required": ["user.create"],
      "missing": ["user.create"],
      "userPermissions": ["user.list", "user.view"]
    }
  },
  "timestamp": "2025-07-16T10:30:00Z"
}
```

#### 角色权限错误
```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_ROLE",
    "message": "用户角色权限不足",
    "details": {
      "required": ["admin"],
      "current": "user"
    }
  },
  "timestamp": "2025-07-16T10:30:00Z"
}
```

## 测试策略

### 单元测试

#### 权限中间件测试
```javascript
describe('PermissionMiddleware', () => {
  test('应该允许拥有权限的用户访问', async () => {
    // 测试权限验证通过的情况
  });
  
  test('应该拒绝没有权限的用户访问', async () => {
    // 测试权限验证失败的情况
  });
  
  test('应该记录权限检查日志', async () => {
    // 测试审计日志记录
  });
});
```

#### 权限服务测试
```javascript
describe('PermissionService', () => {
  test('应该正确计算用户有效权限', async () => {
    // 测试权限继承和合并逻辑
  });
  
  test('应该正确处理权限冲突', async () => {
    // 测试权限冲突解决
  });
});
```

### 集成测试

#### API权限测试
```javascript
describe('API权限集成测试', () => {
  test('用户管理API权限控制', async () => {
    // 测试用户管理相关API的权限控制
  });
  
  test('网站管理API权限控制', async () => {
    // 测试网站管理相关API的权限控制
  });
});
```

### 性能测试

#### 权限验证性能测试
```javascript
describe('权限验证性能测试', () => {
  test('权限验证响应时间应小于50ms', async () => {
    // 测试权限验证性能
  });
  
  test('缓存命中率应大于95%', async () => {
    // 测试缓存性能
  });
});
```

## 部署和运维

### 配置管理

#### 权限配置文件
```json
{
  "permissions": {
    "cache": {
      "ttl": 300,
      "maxSize": 10000
    },
    "audit": {
      "enabled": true,
      "retention": 90
    },
    "security": {
      "requireSecondAuth": ["user.delete", "system.backup"],
      "ipWhitelist": ["***********/24"]
    }
  }
}
```

### 监控指标

#### 关键性能指标
- 权限验证平均响应时间
- 权限缓存命中率
- 权限验证成功率
- 审计日志写入速度
- 权限相关错误率

#### 告警规则
- 权限验证响应时间 > 100ms
- 权限缓存命中率 < 90%
- 权限验证失败率 > 5%
- 审计日志写入失败

### 数据迁移

#### 现有数据兼容
```javascript
class PermissionMigration {
  // 迁移现有用户角色到新权限系统
  async migrateUserRoles() {
    // 迁移逻辑
  }
  
  // 创建默认权限配置
  async createDefaultPermissions() {
    // 默认权限创建
  }
  
  // 验证迁移结果
  async validateMigration() {
    // 迁移验证
  }
}
```

## 安全考虑

### 权限提升防护
- 普通用户不能修改自己的权限
- 管理员不能提升自己为超级管理员
- 权限变更需要二次确认

### 会话安全
- 权限变更后立即刷新用户会话
- 敏感操作需要重新验证身份
- 权限缓存定期刷新

### 审计安全
- 审计日志不可篡改
- 关键操作必须记录审计日志
- 审计日志定期备份和归档

## 扩展性设计

### 插件化权限
- 支持自定义权限模块
- 权限规则可配置化
- 支持外部权限系统集成

### 多租户支持
- 租户级权限隔离
- 跨租户权限管理
- 租户权限模板

### API版本兼容
- 权限API向后兼容
- 渐进式权限升级
- 权限配置版本管理