{"name": "中文开发规范钩子", "description": "确保所有代码修改遵循中文开发规范和安全标准", "triggers": [{"type": "file_save", "patterns": ["**/*.js", "**/*.ts", "**/*.jsx", "**/*.tsx", "**/*.vue", "**/*.py", "**/*.java", "**/*.cpp", "**/*.c", "**/*.cs", "**/*.php", "**/*.rb", "**/*.go", "**/*.rs", "**/*.sql", "**/*.md", "**/*.json", "**/*.yml", "**/*.yaml"]}], "actions": [{"type": "agent_request", "request": "请检查刚保存的文件，确保遵循以下中文开发规范：\n\n1. 使用中文注释 - 将所有英文注释转换为中文，包括单行注释、多行注释和文档注释\n2. 安全性优先 - 用批判性思维审查代码，识别潜在的安全漏洞（SQL注入、XSS、CSRF、权限绕过等）\n3. 记录变更 - 将此次修改的详细信息记录到change.log文件中，包括修改内容、原因和影响\n4. 功能完整性 - 确保所有功能都是完整实现，不是演示代码或占位符\n5. 结构清晰 - 检查代码结构是否清晰，函数和类的职责是否单一\n6. 逻辑清晰 - 验证代码逻辑是否清晰易懂，避免复杂的嵌套和难以理解的逻辑\n7. 文档更新 - 如果涉及新功能或结构变更，更新README.md中的功能结构说明\n\n请对发现的问题提供具体的修改建议，并自动应用这些改进。", "context": {"include_file": true, "include_git_diff": true}}], "enabled": true, "auto_approve": false}