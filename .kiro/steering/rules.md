<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:   
-------------------------------------------------------------------------------------> 

作为WordPress站点管理系统开发的AI助手，请严格遵循以下开发规范和工作流程：

## 语言和注释规范
1. **中文优先原则**：所有代码、注释、文档、变量名、函数名都必须使用中文。发现任何英文注释时，必须立即修改为中文注释。

## 安全和质量标准
2. **安全优先原则**：在处理任何问题时，必须运用批判性思维，将安全性作为首要考虑因素。包括但不限于：数据验证、SQL注入防护、XSS防护、权限检查、文件上传安全等。

3. **完整实现原则**：严禁创建演示性质、占位符性质或不完整的功能。每个设计的功能都必须完整实现，除非明确得到用户许可可以简化或跳过。

## 文档和记录规范
4. **变更记录原则**：每次代码修改、功能添加、问题修复都必须在`change.log`文件中详细记录，包括：修改时间、修改内容、修改原因、影响范围。

5. **结构文档原则**：所有功能结构、代码架构、模块关系都必须记录在`README.md`中。在进行任何修改前，必须先查阅README.md了解当前项目结构。

## 开发和调试流程
6. **日志优先调试**：在修复问题和排错前，必须先查看对应模块的日志文件，通过日志分析问题根源，然后再进行修复。禁止盲目修改代码。

7. **任务状态管理**：完成的任务必须及时标记，可以在README.md的任务列表中添加完成标记（✅），或在change.log中记录完成状态。

## 代码质量和结构规范
8. **代码清晰原则**：所有功能实现和代码结构都必须保持清晰易懂，包括：
   - 函数职责单一
   - 变量命名有意义
   - 代码逻辑层次分明
   - 适当的代码注释

## 文件管理规范
9. **测试文件管理**：
   - 禁止在项目根目录或各模块目录下创建单独的测试文件
   - 所有测试文件必须统一放在专门的测试目录中（如`/tests/`）
   - 临时调试文件使用完毕后必须立即删除
   - 优先使用日志系统进行调试，而非创建测试文件

10. **目录结构原则**：
    - 保持清晰简洁的目录结构
    - 及时清理无用文件和冗余代码
    - 每个文件都应有明确的用途和位置
    - 避免重复功能的文件

## 工作流程要求
在执行任何开发任务时，请按以下顺序进行：
1. 查阅README.md了解项目当前状态
2. 检查相关模块的日志文件
3. 分析问题或需求
4. 设计安全可靠的解决方案
5. 完整实现功能（不允许半成品）
6. 更新change.log记录变更
7. 更新README.md（如有结构变化）
8. 清理临时文件
9. 标记任务完成状态

请确保每个步骤都严格执行，不得跳过或简化任何环节。

## 前后端协作规范
11. **API接口规范**：
    - 所有API接口必须遵循RESTful设计原则
    - 接口响应格式必须统一：`{ success: boolean, data: any, message: string, error?: string }`
    - 接口路径命名使用中文拼音或英文，但注释必须为中文
    - 必须进行输入参数验证和错误处理
    - 敏感操作必须进行权限验证

12. **数据库操作规范**：
    - 所有数据库查询必须使用参数化查询防止SQL注入
    - 数据库连接必须正确关闭，避免连接泄露
    - 敏感数据必须加密存储
    - 数据库操作必须有适当的错误处理和日志记录

## 前端开发规范
13. **React组件规范**：
    - 组件名称必须使用PascalCase命名
    - 组件内部状态管理优先使用useState和useEffect
    - 复杂状态管理使用useReducer或状态管理库
    - 组件必须进行PropTypes或TypeScript类型检查
    - 组件必须有适当的错误边界处理

14. **用户体验规范**：
    - 所有异步操作必须有加载状态提示
    - 表单提交必须有防重复提交机制
    - 错误信息必须用户友好且具体
    - 重要操作必须有确认提示
    - 会话超时必须有明确的用户提示

## 性能和监控规范
15. **性能优化原则**：
    - 数据库查询必须优化，避免N+1查询问题
    - 前端组件必须进行适当的性能优化（memo, useMemo, useCallback）
    - 大数据量操作必须进行分页处理
    - 静态资源必须进行压缩和缓存优化

16. **监控和日志规范**：
    - 关键业务操作必须记录详细日志
    - 错误日志必须包含足够的上下文信息
    - 性能关键点必须有监控指标
    - 日志文件必须定期清理，避免磁盘空间不足

## 部署和运维规范
17. **环境配置原则**：
    - 开发、测试、生产环境必须严格分离
    - 敏感配置信息必须使用环境变量
    - 配置文件必须有完整的示例文件
    - 数据库连接信息必须加密存储

18. **备份和恢复规范**：
    - 数据库必须定期自动备份
    - 重要配置文件必须版本控制
    - 系统更新前必须创建完整备份
    - 必须定期测试备份恢复流程

## 权限和安全规范
19. **权限控制原则**：
    - 实施最小权限原则
    - 权限检查必须在前后端同时进行
    - 敏感操作必须记录操作日志
    - 权限变更必须有审批流程

20. **数据安全规范**：
    - 用户密码必须使用强加密算法
    - 敏感数据传输必须使用HTTPS
    - 文件上传必须进行类型和大小限制
    - 用户输入必须进行XSS防护

## 代码审查和质量控制
21. **代码审查要求**：
    - 所有代码变更必须经过审查
    - 审查重点关注安全性、性能、可维护性
    - 必须检查是否遵循编码规范
    - 必须验证功能完整性和测试覆盖

22. **版本控制规范**：
    - 提交信息必须清晰描述变更内容
    - 重大功能变更必须创建分支
    - 生产发布必须打标签
    - 必须定期清理无用分支

## 应急响应规范
23. **故障处理流程**：
    - 发现问题立即记录和上报
    - 优先恢复服务，再分析根因
    - 必须记录完整的故障处理过程
    - 故障解决后必须进行复盘总结

24. **数据恢复规范**：
    - 数据丢失必须立即停止相关操作
    - 使用最近的可用备份进行恢复
    - 恢复过程必须详细记录
    - 恢复后必须验证数据完整性

---

## 特殊注意事项

### 针对当前项目的特定规范：
- **权限管理系统**：权限模板功能必须支持批量操作和快速应用
- **监控系统**：SSL证书检查和服务器监控必须7x24小时运行
- **会话管理**：用户会话超时必须有渐进式提醒机制
- **数据导入导出**：Excel文件处理必须支持大文件和错误恢复

### 开发优先级：
1. 安全性 > 功能完整性 > 性能优化 > 用户体验
2. 数据一致性 > 系统可用性 > 响应速度
3. 错误处理 > 功能扩展 > 界面美化

### 禁止事项：
- 禁止在生产环境直接调试
- 禁止硬编码敏感信息
- 禁止跳过权限验证
- 禁止忽略错误处理
- 禁止创建未完成的功能分支

---

**最后提醒**：以上所有规范都是强制性的，不得以任何理由跳过或简化。如有特殊情况需要例外处理，必须详细记录原因和风险评估。
#
# 问题诊断和修复规范
25. **系统问题诊断流程**：
    - 遇到API错误时，必须先检查路由配置和中间件设置
    - 数据库连接问题必须检查环境变量和连接池配置
    - 权限相关错误必须验证JWT令牌和权限中间件
    - 服务启动失败必须检查端口占用和依赖服务状态
    - 所有错误修复必须验证完整的功能链路

26. **服务依赖管理**：
    - MySQL服务必须在应用启动前确保可用
    - 权限服务初始化必须在路由注册前完成
    - 缓存服务故障不能影响核心业务功能
    - 监控服务异常必须有降级处理机制
    - 第三方服务调用必须有超时和重试机制

## 环境和配置管理规范
27. **数据库连接管理**：
    - 数据库密码配置必须通过环境变量管理
    - 连接池大小必须根据并发需求合理配置
    - 数据库连接超时必须设置合理的重试机制
    - 生产环境数据库访问必须使用专用账户
    - 数据库连接失败必须有详细的错误日志

28. **服务启动和健康检查**：
    - 应用启动必须验证所有关键依赖服务
    - 健康检查接口必须包含数据库连接状态
    - 服务启动失败必须提供明确的错误信息
    - 关键服务异常必须触发告警机制
    - 服务重启必须保证数据一致性

## API设计和实现规范
29. **权限API设计原则**：
    - 用户权限API必须包含完整的权限检查中间件
    - 权限模板API必须支持CRUD操作和批量应用
    - 敏感权限操作必须记录详细的审计日志
    - 权限验证失败必须返回具体的错误信息
    - 权限缓存失效必须有自动刷新机制

30. **错误处理和响应规范**：
    - 404错误必须检查路由配置和中间件顺序
    - 500错误必须记录完整的错误堆栈信息
    - 权限错误必须区分认证失败和授权失败
    - 数据库错误必须隐藏敏感的连接信息
    - 客户端错误必须提供用户友好的错误提示

## 开发调试和测试规范
31. **本地开发环境**：
    - 开发环境必须与生产环境保持配置一致性
    - 本地数据库必须使用与生产相同的字符集和排序规则
    - 环境变量配置必须有完整的示例文件
    - 开发工具配置必须团队共享和版本控制
    - 本地服务启动必须有一键启动脚本

32. **功能测试和验证**：
    - 权限功能修复后必须测试完整的用户流程
    - API修改后必须验证前后端数据交互
    - 数据库结构变更必须验证数据迁移脚本
    - 缓存机制变更必须测试缓存失效和刷新
    - 监控功能变更必须验证告警和通知机制

## 生产环境运维规范
33. **服务监控和告警**：
    - 关键API响应时间必须设置监控阈值
    - 数据库连接数和慢查询必须实时监控
    - 权限验证失败次数必须设置告警规则
    - 系统资源使用率必须定期检查和优化
    - 业务指标异常必须及时通知相关人员

34. **日志管理和分析**：
    - 应用日志必须按级别和模块分类存储
    - 错误日志必须包含请求ID和用户上下文
    - 性能日志必须记录关键操作的执行时间
    - 安全日志必须记录所有权限相关操作
    - 日志文件必须定期轮转和归档处理

## 团队协作和知识管理
35. **问题解决和知识共享**：
    - 重要问题解决方案必须记录在知识库
    - 常见错误和解决方法必须制作故障手册
    - 系统架构变更必须更新技术文档
    - 新功能开发必须编写使用说明
    - 团队成员必须定期进行技术分享

36. **代码质量和规范检查**：
    - 提交代码前必须通过ESLint和Prettier检查
    - 关键功能必须编写单元测试和集成测试
    - 代码审查必须关注安全漏洞和性能问题
    - 技术债务必须定期评估和处理
    - 代码重构必须保证向后兼容性

---

## 当前项目特定问题和解决方案

### 已识别的关键问题：
1. **用户权限API缺少权限检查中间件** - 已修复
2. **数据库连接配置问题** - 需要检查环境变量
3. **权限缓存服务方法缺失** - 需要完善PermissionCacheService
4. **监控服务数据库访问权限** - 需要配置专用数据库用户

### 紧急修复清单：
- [ ] 验证数据库连接配置和权限
- [ ] 完善权限缓存服务的统计方法
- [ ] 修复权限模板API的500错误
- [ ] 优化监控服务的错误处理
- [ ] 更新环境变量配置文档

### 长期优化目标：
- [ ] 实现完整的权限管理系统测试覆盖
- [ ] 建立自动化的服务健康检查机制
- [ ] 完善系统监控和告警体系
- [ ] 优化数据库查询性能和连接管理
- [ ] 建立完整的故障恢复和应急响应流程

---

## 规范执行检查清单

### 每次开发任务完成后必须检查：
- [ ] 是否遵循了中文优先原则
- [ ] 是否进行了完整的安全性检查
- [ ] 是否更新了change.log记录
- [ ] 是否验证了完整的功能流程
- [ ] 是否清理了临时文件和调试代码
- [ ] 是否更新了相关文档
- [ ] 是否进行了错误处理测试
- [ ] 是否验证了权限控制机制

### 代码提交前必须确认：
- [ ] 所有API接口都有适当的权限检查
- [ ] 数据库操作都使用了参数化查询
- [ ] 错误处理覆盖了所有异常情况
- [ ] 日志记录包含了足够的上下文信息
- [ ] 配置信息没有硬编码敏感数据
- [ ] 功能测试通过了完整的用户场景
- [ ] 性能测试满足了响应时间要求
- [ ] 安全测试通过了权限和数据验证

---

**重要提醒**：这些规范是基于实际项目经验总结的最佳实践，必须严格遵循。任何偏离规范的行为都可能导致系统安全风险或功能故障。如遇特殊情况需要例外处理，必须经过技术负责人审批并详细记录风险评估。## 实战问
题解决和经验总结
37. **环境变量加载问题**：
    - Node.js应用必须在入口文件顶部加载dotenv配置
    - 使用`require('dotenv').config()`确保环境变量正确加载
    - 数据库配置必须优先使用环境变量，提供合理的默认值
    - 生产环境禁止使用硬编码的数据库连接信息
    - 环境变量配置错误必须在应用启动时立即检测并报错

38. **JWT令牌和认证问题**：
    - JWT令牌格式必须严格验证，包括header、payload、signature
    - 令牌过期时间必须合理设置，避免过长或过短
    - 认证失败必须返回具体的错误类型（令牌无效、过期、格式错误）
    - 权限检查中间件必须在所有需要认证的路由前正确配置
    - 测试API时必须使用有效的JWT令牌，避免使用过期或格式错误的令牌

## 数据库设计和优化规范
39. **数据库表结构设计**：
    - 权限相关表必须有完整的索引设计
    - 用户表和权限表的关联必须使用外键约束
    - 时间戳字段必须使用TIMESTAMP类型并设置默认值
    - JSON字段必须有适当的验证和解析处理
    - 表名和字段名必须使用统一的命名规范

40. **数据库查询优化**：
    - 复杂查询必须使用EXPLAIN分析执行计划
    - 避免在WHERE子句中使用函数，影响索引使用
    - 大表查询必须使用LIMIT限制返回结果数量
    - 关联查询必须检查JOIN条件的索引覆盖
    - 定期分析慢查询日志并优化性能瓶颈

## 缓存策略和性能优化
41. **缓存设计原则**：
    - 权限信息必须有合理的缓存策略，平衡性能和实时性
    - 缓存键名必须有统一的命名规范，便于管理和清理
    - 缓存过期时间必须根据数据更新频率合理设置
    - 缓存失效必须有主动清理和被动过期两种机制
    - 缓存统计信息必须定期收集和分析

42. **内存管理和资源控制**：
    - 应用内存使用必须设置合理的上限
    - 定时任务必须有资源使用限制，避免影响主业务
    - 大文件处理必须使用流式处理，避免内存溢出
    - 数据库连接池必须根据并发需求合理配置
    - 系统资源监控必须包含内存、CPU、磁盘IO等指标

## 错误处理和日志记录最佳实践
43. **错误分类和处理**：
    - 业务错误和系统错误必须明确区分
    - 用户输入错误必须返回友好的提示信息
    - 系统内部错误必须记录详细日志但不暴露给用户
    - 第三方服务错误必须有降级处理机制
    - 错误恢复必须有自动重试和手动干预两种方式

44. **日志记录标准化**：
    - 日志级别必须正确使用（ERROR、WARN、INFO、DEBUG）
    - 关键业务操作必须记录操作用户、时间、结果
    - 性能敏感操作必须记录执行时间
    - 错误日志必须包含错误堆栈和上下文信息
    - 日志格式必须结构化，便于自动化分析

## 安全防护和漏洞预防
45. **输入验证和数据清理**：
    - 所有用户输入必须进行严格的类型和格式验证
    - SQL查询必须使用参数化查询，禁止字符串拼接
    - 文件上传必须验证文件类型、大小、内容
    - XSS防护必须在输出时进行HTML转义
    - CSRF防护必须使用token验证机制

46. **权限控制和访问管理**：
    - 最小权限原则必须严格执行
    - 权限检查必须在业务逻辑执行前完成
    - 敏感操作必须有二次确认机制
    - 权限变更必须有完整的审计日志
    - 系统管理员权限必须有特殊的保护机制

## 测试策略和质量保证
47. **自动化测试覆盖**：
    - 核心业务逻辑必须有单元测试覆盖
    - API接口必须有集成测试验证
    - 权限控制必须有专门的安全测试
    - 数据库操作必须有事务一致性测试
    - 性能关键点必须有压力测试验证

48. **手动测试和验收标准**：
    - 新功能上线前必须进行完整的功能测试
    - 用户界面必须在多种设备和浏览器上测试
    - 错误处理必须测试各种异常场景
    - 数据备份和恢复必须定期测试
    - 安全漏洞必须定期进行渗透测试

## 持续集成和部署规范
49. **代码质量检查**：
    - 代码提交前必须通过静态代码分析
    - 代码覆盖率必须达到最低要求标准
    - 安全漏洞扫描必须集成到CI/CD流程
    - 依赖包安全检查必须定期执行
    - 代码重复度检查必须控制在合理范围

50. **部署和发布管理**：
    - 生产部署必须有回滚机制
    - 数据库变更必须有迁移脚本和回滚脚本
    - 配置变更必须有版本控制和审批流程
    - 服务启动必须有健康检查和依赖验证
    - 发布过程必须有详细的操作日志

---

## 成功案例和经验分享

### 本次权限管理问题修复总结：
1. **问题识别**：通过控制台错误日志快速定位API 404和500错误
2. **根因分析**：发现环境变量未加载导致数据库连接使用默认配置
3. **系统性修复**：
   - 添加dotenv配置加载
   - 修复数据库连接配置
   - 完善权限检查中间件
   - 修复缓存服务方法缺失
4. **验证测试**：通过API测试验证修复效果
5. **文档更新**：及时更新change.log和开发规范

### 关键成功因素：
- **系统性思维**：不仅修复表面问题，还解决了根本原因
- **完整测试**：修复后进行了完整的功能验证
- **文档记录**：详细记录了问题和解决方案
- **规范完善**：基于实际问题完善了开发规范

### 经验教训：
- **环境配置**：环境变量加载是基础设施，必须在应用启动时优先处理
- **错误诊断**：日志分析是快速定位问题的关键工具
- **系统依赖**：数据库、缓存等基础服务的配置错误会影响整个应用
- **测试验证**：修复后的验证测试必须覆盖完整的用户场景

---

## 未来改进方向

### 技术架构优化：
- [ ] 实现微服务架构，提高系统可扩展性
- [ ] 引入消息队列，优化异步处理能力
- [ ] 实现分布式缓存，提高系统性能
- [ ] 引入容器化部署，简化运维管理
- [ ] 实现自动化监控和告警系统

### 开发流程改进：
- [ ] 建立完整的CI/CD流水线
- [ ] 实现自动化测试和代码质量检查
- [ ] 建立代码审查和安全扫描机制
- [ ] 完善文档管理和知识库建设
- [ ] 建立团队技术分享和培训体系

### 业务功能扩展：
- [ ] 完善权限管理系统的高级功能
- [ ] 实现多租户和数据隔离
- [ ] 添加审计日志和合规性报告
- [ ] 实现智能监控和预警系统
- [ ] 建立完整的数据分析和报表系统

---

**持续改进承诺**：我们将持续根据实际开发中遇到的问题和挑战，不断完善和更新这些开发规范，确保它们始终反映最佳实践和实际需求。每个团队成员都有责任贡献自己的经验和建议，共同提升整个团队的技术水平和开发效率。