# 站点监控服务配置文件

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root123
DB_NAME=sitemanager

# 监控API服务器端口
MONITOR_PORT=3002

# 邮件通知配置
EMAIL_ENABLED=false
EMAIL_HOST=smtp.qq.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>,<EMAIL>

# 企业微信通知配置
WECHAT_ENABLED=false
WECHAT_WEBHOOK_URL=https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your-key
WECHAT_MENTIONED=@all

# 钉钉通知配置
DINGTALK_ENABLED=false
DINGTALK_WEBHOOK_URL=https://oapi.dingtalk.com/robot/send?access_token=your-token
DINGTALK_SECRET=your-secret
DINGTALK_AT_MOBILES=13800138000,13900139000
DINGTALK_AT_ALL=false

# Webhook通知配置
WEBHOOK_ENABLED=false
WEBHOOK_URLS=https://your-webhook-url.com/notify

# 监控参数配置
CHECK_INTERVAL=300000
BATCH_SIZE=50
CONCURRENCY=10
TIMEOUT=15000
FAILURE_THRESHOLD=3
RECOVERY_THRESHOLD=2
RETRY_ATTEMPTS=2
RETRY_DELAY=3000

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/monitoring.log
