@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 主题色 */
  --primary: #6366F1;
  --secondary: #8B5CF6;
  
  /* 浅色模式背景 */
  --light-bg: #F9FAFB;
  --light-card: #FFFFFF;
  --light-nav: #F3F4F6;
  --light-input: #F9FAFB;
  
  /* 浅色模式文字 */
  --light-text-primary: #111827;
  --light-text-secondary: #374151;
  --light-text-tertiary: #6B7280;
  
  /* 点缀色 */
  --success: #10B981;
  --warning: #F59E0B;
  --error: #EF4444;
  
  /* 暗色模式背景 */
  --dark-bg: #121827;
  --dark-card: #1E293B;
  --dark-nav: #2D3748;
  --dark-input: #2D3748;
  
  /* 暗色模式文字 */
  --dark-text-primary: #F1F5F9;
  --dark-text-secondary: #CBD5E1;
  --dark-text-tertiary: #94A3B8;
}

body {
  background-color: var(--light-bg);
  color: var(--light-text-primary);
}

.dark body {
  background-color: var(--dark-bg);
  color: var(--dark-text-primary);
}

/* Tailwind样式替代 */
.bg-primary\/5 { background-color: rgba(99, 102, 241, 0.05); }
.bg-primary\/10 { background-color: rgba(99, 102, 241, 0.1); }
.bg-primary\/20 { background-color: rgba(99, 102, 241, 0.2); }
.bg-primary\/30 { background-color: rgba(99, 102, 241, 0.3); }
.text-primary { color: var(--primary); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.border-primary\/10 { border-color: rgba(99, 102, 241, 0.1); }
.border-primary\/15 { border-color: rgba(99, 102, 241, 0.15); }
.border-primary\/20 { border-color: rgba(99, 102, 241, 0.2); }
.border-primary\/25 { border-color: rgba(99, 102, 241, 0.25); }
.border-primary\/30 { border-color: rgba(99, 102, 241, 0.3); }

/* 组件样式 */
.hover-card {
  transition: all 0.2s ease;
}

.hover-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.glass-effect {
  backdrop-filter: blur(10px);
}

.dark .glass-effect {
  background-color: rgba(30, 41, 59, 0.85);
  border-color: rgba(99, 102, 241, 0.1);
}

.light .glass-effect {
  background-color: rgba(243, 244, 246, 0.85);
  border-color: rgba(99, 102, 241, 0.15);
}

.rounded-button {
  border-radius: 0.5rem;
}

.shadow-glow-sm {
  box-shadow: 0 0 10px rgba(99, 102, 241, 0.3);
}

/* 表格样式修复 */
table.border-collapse {
  border-collapse: collapse;
}

table.border-collapse tbody tr {
  border-bottom: 1px solid rgba(99, 102, 241, 0.05);
}

.dark table.border-collapse tbody tr {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

table.border-collapse tbody tr:last-child {
  border-bottom: none;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: var(--light-bg);
}

.dark ::-webkit-scrollbar-track {
  background-color: var(--dark-bg);
}

::-webkit-scrollbar-thumb {
  background-color: var(--light-nav);
  border-radius: 9999px;
}

.dark ::-webkit-scrollbar-thumb {
  background-color: var(--dark-nav);
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(99, 102, 241, 0.3);
}

/* 开关按钮样式 */
input[type="checkbox"] + .toggle-label .toggle-dot {
  transform: translateX(0);
}

input[type="checkbox"]:checked + .toggle-label {
  background-color: rgba(99, 102, 241, 0.5);
}

input[type="checkbox"]:checked + .toggle-label .toggle-dot {
  transform: translateX(100%);
  background-color: #6366F1;
}

input[type="checkbox"]:focus + .toggle-label {
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.25);
}

.form-checkbox {
  appearance: none;
  padding: 0;
  print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #6366F1;
  background-color: rgba(99, 102, 241, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 0.25rem;
}

.form-checkbox:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

.form-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.25);
  border-color: #6366F1;
}

/* 动画 */
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
