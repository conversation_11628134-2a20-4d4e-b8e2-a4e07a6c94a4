{"name": "coolmonitor", "version": "0.1.0", "private": true, "scripts": {"dev": "npx prisma generate && next dev --turbopack ", "build": "npx prisma generate && next build --no-lint", "start": "next start", "lint": "next lint", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@types/nodemailer": "^6.4.17", "axios": "^1.8.4", "croner": "^9.0.0", "echarts": "^5.6.0", "mysql2": "^3.14.0", "next": "15.3.1", "nodemailer": "^6.10.1", "pg": "^8.15.1", "ping": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "redis": "^4.7.0", "ssl-checker": "^2.0.10", "undici": "^6.21.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@fortawesome/fontawesome-free": "^6.7.2", "@prisma/client": "^6.6.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/pg": "^8.11.13", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.1.2", "autoprefixer": "^10.4.21", "bcryptjs": "^3.0.2", "eslint": "^9", "eslint-config-next": "15.3.1", "jsdom": "^26.1.0", "next-auth": "^4.24.11", "postcss": "^8.5.3", "prisma": "^6.6.0", "tailwindcss": "^3.4.1", "typescript": "^5", "vitest": "^3.1.2"}}